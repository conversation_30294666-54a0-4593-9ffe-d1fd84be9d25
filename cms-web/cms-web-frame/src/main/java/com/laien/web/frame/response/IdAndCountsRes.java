package com.laien.web.frame.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * note: id和count
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value="id和count", description="")
public class IdAndCountsRes {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "counts")
    private Integer counts;

}
