package com.laien.web.frame.json.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.laien.web.frame.json.IJsonService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Component
public class JsonServiceImpl implements IJsonService {

    @Resource
    private ObjectMapper objectMapper;

    private Map<Class, TypeReference> typeReferenceMap = Maps.newHashMap();

    @Override
    public String toJsonString(Object object) throws JsonProcessingException {
        return objectMapper.writeValueAsString(object);
    }

    @Override
    public String toJsonArrayString(Collection object) {
        if (CollectionUtils.isEmpty(object)) {
            return "[]";
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            return "[]";
        }
    }

    @Override
    public <T> T parseToObject(String json, Class<T> tClass) throws JsonProcessingException {
        return objectMapper.readValue(json, tClass);
    }

    @Override
    public JsonNode parseToNode(String json) throws JsonProcessingException {
        return objectMapper.readTree(json);
    }

    @Override
    public <T> List<T> parseToObjectArray(String json, Class<T> tClass) {
        if (StringUtils.isBlank(json)) {
            return Lists.newArrayListWithCapacity(0);
        }
        try {
            TypeReference<List<T>> typeReference = typeReferenceMap.get(tClass);
            if (typeReference == null) {
                typeReference = new TypeReference<List<T>>() {
                };
                typeReferenceMap.put(tClass, typeReference);
            }
            return objectMapper.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            return Lists.newArrayListWithCapacity(0);
        }
    }
}
