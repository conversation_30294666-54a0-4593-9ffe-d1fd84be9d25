package com.laien.web.frame.http.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.http.HttpAuthorization;
import com.laien.web.frame.http.IHttpServiceRespProcess;
import com.laien.web.frame.http.service.IHttpService;
import com.laien.web.frame.utils.MockMultipartFile;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class HttpServiceImpl implements IHttpService {

    public static final String MULTIPARFILENAME = "file";
    @Resource
    private OkHttpClient okHttpClient;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * get相关
     *
     * @param url
     * @param respProcess
     * @throws Exception
     */
    @Override
    public void doGet(String url, IHttpServiceRespProcess respProcess) throws Exception {
        doGetByAuth(url, null, respProcess);
    }

    @Override
    public void doGetByAuth(String url, String token, IHttpServiceRespProcess respProcess) throws Exception {
        if (StringUtils.isNotBlank(token)) {
            doGetByAuth(url, HttpAuthorization.DEFAULT, token, respProcess);
        } else {
            doGetByHeaders(url, new HashMap<>(), respProcess);
        }
    }

    @Override
    public void doGetByAuth(String url, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception {
        Map<String, String> header = Maps.newHashMap();
        if (httpAuthorization != null) {
            header.put(httpAuthorization.getHeaderName(), httpAuthorization.getHeaderValuePrefix() + GlobalConstant.SPACE_STRING + token);
            doGetByHeaders(url, header, respProcess);
        }
    }

    @Override
    public void doGetByHeaders(String url, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception {
        log.info("get Url: " + url);
        Request.Builder requestBuilder = new Request.Builder().url(url);
        if (!CollectionUtils.isEmpty(headers)) {
            addHeaders(headers, requestBuilder);
        }
        Request request = requestBuilder.build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            respProcess.processResp(response);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public void doPostFormData(String url, Map<String, String> formData, IHttpServiceRespProcess respProcess) throws Exception {
        doPostFormData(url, formData, GlobalConstant.EMPTY_STRING, respProcess);
    }

    @Override
    public void doPostFormData(String url, Map<String, String> formData, String token, IHttpServiceRespProcess respProcess) throws Exception {
        if (StringUtils.isNotBlank(token)) {
            doPostFormData(url, formData, HttpAuthorization.DEFAULT, token, respProcess);
        } else {
            doPostFormData(url, formData, null, null, respProcess);
        }
    }

    @Override
    public void doPostFormData(String url, Map<String, String> formData, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception {
        Map<String, String> header = Maps.newHashMap();
        if (httpAuthorization != null) {
            header.put(httpAuthorization.getHeaderName(), httpAuthorization.getHeaderValuePrefix() + GlobalConstant.SPACE_STRING + token);
            doPostFormData(url, formData, header, respProcess);
        }
    }

    @Override
    public void doPostFormData(String url, Map<String, String> formData, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception {
        log.info("post-formBody Url: " + url);
        FormBody.Builder bodyBuilder = new FormBody.Builder();
        if (formData != null && formData.size() > 0) {
            for (Map.Entry<String, String> entry : formData.entrySet()) {
                bodyBuilder.add(entry.getKey(), entry.getValue());
            }
        }
        FormBody body = bodyBuilder.build();
        Request.Builder requestBuilder = new Request.Builder();
        addHeaders(headers, requestBuilder);
        Request request = requestBuilder.post(body).url(url).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            respProcess.processResp(response);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public void doPostRaw(String url, Object data, IHttpServiceRespProcess respProcess) throws Exception {
        doPostRaw(url, data, GlobalConstant.EMPTY_STRING, respProcess);
    }

    @Override
    public void doPostRaw(String url, Object data, String token, IHttpServiceRespProcess respProcess) throws Exception {
        if (StringUtils.isNotBlank(token)) {
            doPostRaw(url, data, HttpAuthorization.DEFAULT, token, respProcess);
        } else {
            doPostRaw(url, data, new HashMap<>(), respProcess);
        }
    }

    @Override
    public void doPostRaw(String url, Object data, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception {
        Map<String, String> header = Maps.newHashMap();
        if (httpAuthorization != null) {
            header.put(httpAuthorization.getHeaderName(), httpAuthorization.getHeaderValuePrefix() + GlobalConstant.SPACE_STRING + token);
            doPostRaw(url, data, header, respProcess);
        }
    }

    @Override
    public void doPostRaw(String url, Object data, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception {
        log.info("post-raw Url: " + url);
        MediaType type = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE);
        RequestBody requestBody = null;
        if (data instanceof String) {
            requestBody = RequestBody.create(type, (String) data);
        } else {
            requestBody = RequestBody.create(type, objectMapper.writeValueAsString(data));
        }
        Request.Builder requestBuilder = new Request.Builder();
        if (!CollectionUtils.isEmpty(headers)) {
            addHeaders(headers, requestBuilder);
        }
        Request request = requestBuilder.post(requestBody).url(url).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            respProcess.processResp(response);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public void doPutRaw(String url, Object data, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception {
        log.info("put-raw Url: " + url);
        MediaType type = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE);
        RequestBody requestBody = null;
        if (data instanceof String) {
            requestBody = RequestBody.create(type, (String) data);
        } else {
            requestBody = RequestBody.create(type, objectMapper.writeValueAsString(data));
        }
        Request.Builder requestBuilder = new Request.Builder();
        addHeaders(headers, requestBuilder);
        Request request = requestBuilder.put(requestBody).url(url).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            respProcess.processResp(response);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public void doPostMultipartBody(String url, Map<String, String> formData, Map<String, String> fileNameContentMap, String token, IHttpServiceRespProcess respProcess) throws Exception {
        if (StringUtils.isNotBlank(token)) {
            doPostMultipartBody(url, formData, fileNameContentMap, HttpAuthorization.DEFAULT, token, respProcess);
        } else {
            doPostMultipartBody(url, formData, fileNameContentMap, null, null, respProcess);
        }
    }

    @Override
    public void doPostMultipartBody(String url, Map<String, String> formData, Map<String, String> fileNameContentMap, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception {
        Map<String, String> header = Maps.newHashMap();
        if (httpAuthorization != null) {
            header.put(httpAuthorization.getHeaderName(), httpAuthorization.getHeaderValuePrefix() + GlobalConstant.SPACE_STRING + token);
            doPostMultipartBody(url, formData, fileNameContentMap, header, respProcess);
        }
    }

    @Override
    public void doPostMultipartBody(String url, Map<String, String> formData, Map<String, String> fileNameContentMap, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception {
        log.info("post-multipart Url: " + url);
        MediaType mediaType = MediaType.parse(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE);
        MultipartBody.Builder bodyBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        if (formData != null && formData.size() > 0) {
            for (Map.Entry<String, String> entry : formData.entrySet()) {
                bodyBuilder.addFormDataPart(entry.getKey(), entry.getValue());
            }
        }
        if (fileNameContentMap != null && fileNameContentMap.size() > 0) {
            for (Map.Entry<String, String> entry : fileNameContentMap.entrySet()) {
                bodyBuilder.addFormDataPart(MULTIPARFILENAME, entry.getKey(), RequestBody.create(mediaType, entry.getValue().getBytes("UTF-8")));
            }
        }
        MultipartBody requestBody = bodyBuilder.build();
        Request.Builder requestBuilder = new Request.Builder();
        addHeaders(headers, requestBuilder);
        Request request = requestBuilder.post(requestBody).url(url).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            respProcess.processResp(response);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public void doDelete(String url, String token, IHttpServiceRespProcess respProcess) throws Exception {
        if (StringUtils.isNotBlank(token)) {
            doDelete(url, HttpAuthorization.DEFAULT, token, respProcess);
        } else {
            doDelete(url, null, null, respProcess);
        }
    }

    @Override
    public void doDelete(String url, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception {
        Map<String, String> header = Maps.newHashMap();
        if (httpAuthorization != null) {
            header.put(httpAuthorization.getHeaderName(), httpAuthorization.getHeaderValuePrefix() + GlobalConstant.SPACE_STRING + token);
            doDelete(url, header, respProcess);
        }
    }

    @Override
    public void doDelete(String url, Map<String, String> headers, IHttpServiceRespProcess respProcess) throws Exception {
        log.info("delete Url: " + url);
        Request.Builder requestBuilder = new Request.Builder();
        addHeaders(headers, requestBuilder);
        Request request = requestBuilder.delete().url(url).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            respProcess.processResp(response);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public void doDeleteRaw(String url, Object data, HttpAuthorization httpAuthorization, String token, IHttpServiceRespProcess respProcess) throws Exception {
        log.info("post-raw Url: " + url);
        Map<String, String> header = Maps.newHashMap();
        if (httpAuthorization != null) {
            header.put(httpAuthorization.getHeaderName(), httpAuthorization.getHeaderValuePrefix() + GlobalConstant.SPACE_STRING + token);
        }
        MediaType type = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE);
        RequestBody requestBody = null;
        if (data instanceof String) {
            requestBody = RequestBody.create(type, (String) data);
        } else {
            requestBody = RequestBody.create(type, objectMapper.writeValueAsString(data));
        }
        Request.Builder requestBuilder = new Request.Builder();
        addHeaders(header, requestBuilder);
        Request request = requestBuilder.delete(requestBody).url(url).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            respProcess.processResp(response);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public File downloadFile(String url, String diskDir) {
        Request request = new Request.Builder().header("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36)").url(url).build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            byte[] fileBytes = response.body().bytes();
            String orgfileName = StringUtils.substringAfterLast(StringUtils.substringBefore(url, "?"), "/");
            String fileName = UUID.randomUUID().toString();
            if (orgfileName.contains(".")) {
                String suffix = StringUtils.substringAfterLast(orgfileName, ".");
                fileName = fileName + "." + suffix;
            }
            MockMultipartFile file = new MockMultipartFile(fileName, fileBytes);
            File saveFile = new File(diskDir + File.separator + fileName);
            file.transferTo(saveFile);
            return saveFile;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void addHeaders(Map<String, String> headers, Request.Builder requestBuilder) {
        if (headers != null && headers.size() > 0) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }
    }
}
