package com.laien.web.frame.async.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class ThreadPoolConfig {


    public static final String OTHER_TASK_THREAD_POOL = "otherTaskThreadPool";

    public static final String DEFERRED_TASK_THREAD_POOL = "deferredTaskThreadPool";


    @Bean(OTHER_TASK_THREAD_POOL)
    public Executor otherTaskThreadPool() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        threadPoolTaskExecutor.setCorePoolSize(8);
        // 设置最大线程数
        threadPoolTaskExecutor.setMaxPoolSize(8);
        // 设置工作队列大小
//        threadPoolTaskExecutor.setQueueCapacity(2000);
        // 设置线程名称前缀
//        threadPoolTaskExecutor.setThreadNamePrefix("threadPoolTaskExecutor-->");
        // 设置拒绝策略.当工作队列已满,线程数为最大线程数的时候,接收新任务抛出RejectedExecutionException异常
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 初始化线程池
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean(DEFERRED_TASK_THREAD_POOL)
    public Executor deferredTaskThreadPool() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        threadPoolTaskExecutor.setCorePoolSize(8);
        // 设置最大线程数
        threadPoolTaskExecutor.setMaxPoolSize(20);
        // 设置工作队列大小
//        threadPoolTaskExecutor.setQueueCapacity(2000);
        // 设置线程名称前缀
//        threadPoolTaskExecutor.setThreadNamePrefix("threadPoolTaskExecutor-->");
        // 设置拒绝策略.当工作队列已满,线程数为最大线程数的时候,接收新任务抛出RejectedExecutionException异常
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 初始化线程池
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }
}