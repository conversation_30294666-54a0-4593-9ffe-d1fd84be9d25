package com.laien.web.frame.json;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Collection;
import java.util.List;

public interface IJsonService {

    String toJsonString(Object object) throws JsonProcessingException;

    String toJsonArrayString(Collection object);

    <T> T parseToObject(String json, Class<T> tClass) throws JsonProcessingException;

    JsonNode parseToNode(String json) throws JsonProcessingException;

    public <T> List<T> parseToObjectArray(String json, Class<T> tClass);
}
