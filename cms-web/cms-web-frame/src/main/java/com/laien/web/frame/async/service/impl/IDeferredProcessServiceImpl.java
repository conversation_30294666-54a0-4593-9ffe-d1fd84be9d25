package com.laien.web.frame.async.service.impl;

import com.laien.web.frame.async.IDeferredAsyncProcess;
import com.laien.web.frame.async.service.IDeferredProcessService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

import static com.laien.web.frame.async.config.ThreadPoolConfig.DEFERRED_TASK_THREAD_POOL;


@Service
public class IDeferredProcessServiceImpl implements IDeferredProcessService {


    @Async(DEFERRED_TASK_THREAD_POOL)
    @Override
    public void processSomethings(<PERSON><PERSON><PERSON><PERSON><PERSON> deferredResult, IDeferredAsyncProcess process) {
        Object result = null;
        try {
            result = process.process();
            deferredResult.setResult(result);
        } catch (Exception e) {
            e.printStackTrace();
            deferredResult.setErrorResult(e);
        }
    }
}
