package com.laien.web.frame.constant;

/**
 * 全局变量
 *
 * <AUTHOR>
 */
public interface GlobalConstant {

    /**
     * 正向，1，yes
     */
    int YES = 1;
    /**
     * 负向，0，no
     */
    int NO = 0;

    /**
     * value -1
     */
    int MINUS_ONE = -1;
    /**
     * value 0
     */
    int ZERO = 0;

    /**
     *  1秒钟对应的毫秒数
     */
    int MILLIS_PER_SECOND = 1000;

    double DOUBLE_ZERO = 0d;
    /**
     * value 1
     */
    int ONE = 1;

    /**
     * value 2
     */
    int TWO = 2;
    /**
     * value 3
     */
    int THREE = 3;
    /**
     * value 4
     */
    int FOUR = 4;
    /**
     * value 5
     */
    int FIVE = 5;
    /**
     * value 6
     */
    int SIX = 6;
    /**
     * value 7
     */
    int SEVEN = 7;
    /**
     * value 8
     */
    int EIGHT = 8;
    /**
     * value 9
     */
    int NINE = 9;
    /**
     * value 10
     */
    int TEN = 10;

    int TWENTY = 20;

    int TWENTY_ONE = 21;

    int ELEVEN = 11;

    int TWELVE = 12;

    int THIRTEEN = 13;

    int FOURTEEN = 14;

    /**
     * 默认语言
     */
    String DEFAULT_LANGUAGE = "en";

    String SECOND_MILL = "1000.0";

    int NEXT_VIDEO_SPLIT = 3000;

    int FIFTY = 50;

    /**
     * value 100
     */
    int HUNDRED = 100;

    int TWO_HUNDRED = 200;


    int FIVE_HUNDRED = 500;

    /**
     * value 1000
     */
    int THOUSAND = 1000;

    int TWO_THOUSAND = 2000;

    /**
     * 空字符""
     */
    String EMPTY_STRING = "";

    /**
     * 空格字符串
     */
    String SPACE_STRING = " ";

    /**
     * 英文逗号
     */
    String COMMA = ",";

    /**  英文中划线 */
    String ENGLISH_DASH = "-";

    /**
     * 鉴权的请求头名称
     */
    String AUTH_HEADER_NAME = "token";

    /**
     * 语言头
     */
    String HEADER_LANGUAGE_NAME = "lang";

    /**
     * 设备
     */
    String HEADER_DEVICE_NAME = "device";

    /**
     * 项目id
     */
    String HEADER_PROJECTID_NAME = "projId";

    /**
     * 服务传递用户信息
     */
    String PASS_USER_HEADER_NAME = "currentLoginUser";

    /**
     * 服务传递用户信息
     */
    String HEADER_APPVERSION = "version";

    /**
     * appcode
     */
    String HEADER_APPCODE = "appCode";

    /**
     * 显示状态 0 隐藏 1显示
     */
    int SHOW_HIDDEN = 0;

    int SHOW_VISIBLE = 1;

    /**
     * 未知用户
     */
    String UNKNOWN_USER = "(Unknown User)";

    /**
     * 数据状态 0 草稿 1 启用 2禁用
     */

    int STATUS_DRAFT = 0;

    int STATUS_ENABLE = 1;

    int STATUS_DISABLE = 2;

    int STATUS_NOT_READY = 3;


    /**
     * 账号类型 1普通用户 0管理员
     */
    int USER_TYPE_NORMAL = 1;

    int USER_TYPE_ADMIN = 0;

    /**
     * 系统账号
     */
    String USER_SYSTEM = "system";


    /**
     * 预发布版本号
     */
    int VERSION_PRE_RELEASE = -1;
}
