package com.laien.web.frame.utils;

import com.laien.web.frame.exception.BizException;
import org.slf4j.helpers.MessageFormatter;

import java.util.function.BooleanSupplier;

/**
 * <p>
 *     异常工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public class BizExceptionUtil {

    /**
     * 根据条件抛出指定异常
     *
     * @param condition 判断条件的布尔函数
     * @throws BizException 如果条件为 true，则抛出指定异常
     */
    public static void throwIf(BooleanSupplier condition,String message) throws BizException {
        throwIf(condition.getAsBoolean(),message);
    }

    /**
     * 根据条件抛出指定异常
     *
     * @param condition 判断条件
     * @throws BizException 如果条件为 true，则抛出指定异常
     */
    public static void throwIf(boolean condition, String message, Object... args) throws BizException {
        if (condition) {
            String formattedMessage = MessageFormatter.arrayFormat(message, args).getMessage();
            throw new BizException(formattedMessage);
        }
    }
}
