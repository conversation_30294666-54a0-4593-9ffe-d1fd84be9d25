package com.laien.web.frame.async.service.impl;

import com.laien.web.frame.async.IAsyncCallback;
import com.laien.web.frame.async.IAsyncProcess;
import com.laien.web.frame.async.service.IAsyncService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import static com.laien.web.frame.async.config.ThreadPoolConfig.OTHER_TASK_THREAD_POOL;


@Service
public class AsyncServiceImpl implements IAsyncService {

    @Override
    @Async(OTHER_TASK_THREAD_POOL)
    public void doSomethings(IAsyncProcess process) throws Exception {
        doSomethings(process,null);
    }

    @Override
    @Async(OTHER_TASK_THREAD_POOL)
    public void doSomethings(IAsyncProcess process, IAsyncCallback callback) throws Exception {
        try {
            process.process();
            if (callback != null) {
                callback.completed(null);
            }
        }catch (Throwable e){
            if (callback != null) {
                callback.completed(e);
            }
        }finally {

        }
    }

}
