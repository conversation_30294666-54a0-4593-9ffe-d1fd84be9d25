package com.laien.web.frame.async.service.impl;

import com.laien.web.frame.async.service.IDeferredProcessService;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;
import com.laien.web.frame.async.IDeferredAsyncProcess;
import com.laien.web.frame.async.service.IDeferredAsyncService;
import javax.annotation.Resource;

@Service
public class IDeferredAsyncServiceImpl implements IDeferredAsyncService {

    @Resource
    private IDeferredProcessService deferredProcessService;

    @Override
    public DeferredResult doSomethings(IDeferredAsyncProcess process) throws Exception {
        DeferredResult deferredResult = new DeferredResult();
        deferredProcessService.processSomethings(deferredResult, process);
        return deferredResult;
    }

}
