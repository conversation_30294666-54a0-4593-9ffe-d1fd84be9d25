<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramMapper">

    <select id="page" resultType="com.laien.web.biz.proj.oog200.entity.ProjYogaProgram">

        SELECT distinct program.* FROM proj_yoga_program program
        LEFT JOIN proj_yoga_program_type_relation relation on program.id = relation.proj_yoga_program_id
        WHERE program.del_flag = 0
        <if test="status != null">
            AND program.`status` = #{status}
        </if>
        <if test="name != null and name != ''">
            AND program.name LIKE CONCAT('%', #{name},'%')
        </if>
        <if test="difficulty != null">
            AND program.difficulty = #{difficulty}
        </if>
        <if test="programPositionType != null">
            AND program.program_position_types LIKE CONCAT('%', #{programPositionType},'%')
        </if>
        <if test="programCategoryId != null">
            AND relation.proj_yoga_program_type_id = #{programCategoryId} AND relation.del_flag = 0
        </if>
        order by program.id desc
    </select>
</mapper>