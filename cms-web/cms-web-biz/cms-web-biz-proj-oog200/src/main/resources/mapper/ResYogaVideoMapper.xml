<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ResYogaVideoMapper">

    <select id="selectYogaVideoPage" resultType="com.laien.web.biz.proj.oog200.response.ResYogaVideoPageVO">
        SELECT
            yv.id,
            yv.`name`,
            yv.event_name,
            yv.image_url,
            yv.type,
            yv.difficulty,
            yv.position,
            yv.focus,
            yv.special_limit,
            yv.pose_time,
            yv.calorie,
            yv.`status`
        FROM
            res_yoga_video AS yv
            <if test="myPage.linkId != null">
            INNER JOIN res_yoga_video_connection AS yvc ON yv.id = yvc.res_yoga_video_next_id
            INNER JOIN res_transition AS t ON t.id = yvc.res_transition_id
            </if>
        WHERE
            yv.del_flag = 0
            <if test="myPage.linkId != null">
                AND yvc.del_flag = 0
                AND yvc.res_yoga_video_id = #{myPage.linkId}
                AND t.`status` = 1
                AND t.del_flag = 0
            </if>
            <if test="myPage.name != null and myPage.name != ''">
                AND yv.name like CONCAT('%', #{myPage.name},'%')
            </if>
            <if test="myPage.status != null">
                AND yv.`status` = #{myPage.status}
            </if>
            <if test="myPage.difficulty != null and myPage.difficulty != ''">
                AND yv.difficulty = #{myPage.difficulty}
            </if>
            <if test="myPage.position != null and myPage.position != ''">
                AND yv.position = #{myPage.position}
            </if>
            <if test="myPage.poseTime != null">
                AND yv.pose_time = #{myPage.poseTime}
            </if>
            <if test="myPage.yogaVideoIds != null">
                <foreach collection="myPage.yogaVideoIds" item="id" open="AND yv.id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="myPage.typeArr != null and myPage.typeArr.length > 0">
                <foreach collection="myPage.typeArr" item="t" open="and (" separator="or" close=")">
                    FIND_IN_SET(#{t}, yv.`type`)
                </foreach>
            </if>
            <if test="myPage.focusArr != null and myPage.focusArr.length > 0">
                <foreach collection="myPage.focusArr" item="t" open="and (" separator="or" close=")">
                    FIND_IN_SET(#{t}, yv.focus)
                </foreach>
            </if>
            <if test="myPage.specialLimitArr != null and myPage.specialLimitArr.length > 0">
                <foreach collection="myPage.specialLimitArr" item="t" open="and (" separator="or" close=")">
                    FIND_IN_SET(#{t}, yv.special_limit)
                </foreach>
            </if>
            <choose>
                <when test="myPage.requestPage != null and myPage.requestPage == 'video_add'">
                    ORDER BY yv.`name`, yv.id DESC
                </when>
                <otherwise>
                    ORDER BY yv.id DESC
                </otherwise>
            </choose>
    </select>

</mapper>