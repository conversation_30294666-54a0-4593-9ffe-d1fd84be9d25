<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjYogaRegularWorkoutVideoRelationMapper">

    <select id="selectYogaRegularWorkoutVideos" resultType="com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutVideoDetailVO">
        SELECT
            v.id,
            v.`name`,
            v.event_name,
            v.image_url,
            v.type,
            v.difficulty,
            v.position,
            v.focus,
            v.special_limit,
            v.pose_time,
            v.calorie,
            v.`status`
        FROM
            proj_yoga_regular_workout_video_relation wvr
            INNER JOIN res_yoga_video v ON wvr.res_yoga_video_id = v.id
        WHERE
            wvr.del_flag = 0
          AND wvr.proj_yoga_regular_workout_id = #{workoutId}
        ORDER BY
            wvr.id
    </select>

</mapper>