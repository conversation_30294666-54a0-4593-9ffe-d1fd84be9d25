<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjCollectionClassRelationMapper">

    <select id="selectClassesByCollectionId" resultType="com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoVO">
        SELECT
            c.id,
            c.`name`,
            c.event_name,
            c.image_png,
            c.image_gif,
            c.video_url,
            c.duration,
            c.difficulty,
            c.calorie,
            c.new_start_time,
            c.new_end_time,
            c.subscription,
            c.`status`,
            c.type
        FROM
            proj_collection_class_relation cc
                INNER JOIN res_video_class c ON c.id = cc.video_class_id
        WHERE
            cc.del_flag = 0
          AND c.del_flag = 0
          AND cc.collection_class_id = #{collectionClassId}
    </select>
    <select id="selectCollectionClassStatusCount" resultType="com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoStatusCountVO">
        SELECT
            cc.collection_class_id AS id,
            c.`status`,
            count(*) count
        FROM
            proj_collection_class_relation cc
                INNER JOIN res_video_class c ON c.id = cc.video_class_id
        WHERE
            cc.del_flag = 0
          AND c.del_flag = 0
        GROUP BY
            cc.collection_class_id,
            c.`status`
    </select>


</mapper>