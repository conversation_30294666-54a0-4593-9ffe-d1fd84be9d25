<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjChairYogaVideoMapper">

    <select id="listByIds" resultType="com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo">
        select *
        from proj_chair_yoga_video
        where 1 = 1
        <foreach collection="videoIds" item="item" open=" and id in (" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>