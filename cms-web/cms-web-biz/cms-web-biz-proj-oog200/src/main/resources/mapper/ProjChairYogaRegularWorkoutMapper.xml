<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjChairYogaRegularWorkoutMapper">

    <select id="page" resultType="java.lang.Integer">
        SELECT
            w.id
        FROM
            proj_chair_yoga_regular_workout w
            LEFT JOIN proj_yoga_regular_workout_category_relation wcr ON wcr.workout_id = w.id AND wcr.del_flag = 0
        WHERE
            w.del_flag = 0
            AND w.proj_id = #{projId}
            <if test="pageReq.status != null">
                AND w.`status` = #{pageReq.status}
            </if>
            <if test="pageReq.updateStatus != null">
                AND w.update_status = #{pageReq.updateStatus}
            </if>
            <if test="pageReq.difficulty != null and pageReq.difficulty != ''">
                AND w.difficulty = #{pageReq.difficulty}
            </if>
            <if test="pageReq.name != null and pageReq.name != ''">
                AND w.`name` LIKE CONCAT('%', #{pageReq.name},'%')
            </if>
            <if test="pageReq.yogaDataSource != null">
                AND FIND_IN_SET(#{pageReq.yogaDataSource}, w.data_sources)
            </if>
            <if test="pageReq.categoryId != null">
                AND (
                    (wcr.workout_type = #{workoutType}
                    AND wcr.proj_yoga_regular_category_id = #{pageReq.categoryId})
                )
            </if>
        GROUP BY w.id
        ORDER BY w.id DESC
    </select>
</mapper>