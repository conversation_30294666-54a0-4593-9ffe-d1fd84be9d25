<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseWorkoutMapper">

    <select id="findByPoseGroupId"
            resultType="com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutListVO">
        SELECT
            pw.id,
            pw.name,
            pw.cover_img_url,
            pw.difficulty,
            pw.calorie,
            pw.duration,
            pw.status,
            pw.position
        FROM
            proj_yoga_pose_group_workout_relation gwr
                JOIN proj_yoga_pose_workout pw ON pw.id = gwr.proj_yoga_pose_workout_id
        WHERE gwr.proj_yoga_pose_group_id = #{poseGroupId}
          AND gwr.del_flag = 0
        ORDER BY gwr.id
    </select>

    <select id="findCount" resultType="com.laien.web.biz.proj.oog200.bo.CountBO">
        SELECT
            pg.id,
            COUNT(*) count
        FROM
            proj_yoga_pose_workout pw
            JOIN proj_yoga_pose_group_workout_relation pypgwr ON pw.id = pypgwr.proj_yoga_pose_workout_id
            JOIN proj_yoga_pose_group pg ON pypgwr.proj_yoga_pose_group_id = pg.id
        WHERE
            pg.id IN
        <foreach collection="poseGroupIdSet" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
            AND pw.del_flag = 0
            AND pypgwr.del_flag = 0
        <if test="status != null">
            AND pw.status = #{status}
        </if>
        GROUP BY pg.id
    </select>
</mapper>