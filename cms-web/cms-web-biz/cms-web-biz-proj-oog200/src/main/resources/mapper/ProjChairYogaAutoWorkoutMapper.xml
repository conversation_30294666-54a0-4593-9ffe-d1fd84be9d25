<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjChairYogaAutoWorkoutMapper">

    <select id="listWorkoutCountByTemplate" resultType="com.laien.web.biz.proj.oog200.bo.CountBO">
        select proj_yoga_auto_workout_template_id as id, count(1) as count
        from proj_chair_yoga_auto_workout
        where del_flag = 0
        <foreach collection="templateIds" item="item" open=" and proj_yoga_auto_workout_template_id in (" separator="," close=")">
            #{item}
        </foreach>
        group by proj_yoga_auto_workout_template_id
    </select>

</mapper>