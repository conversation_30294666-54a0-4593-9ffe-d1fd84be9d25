<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjWallPilatesAutoWorkoutMapper">

    <select id="findCount" resultType="com.laien.web.biz.proj.oog200.bo.CountBO">
        SELECT
            proj_yoga_auto_workout_template_id AS id,
            COUNT(*) AS count
        FROM
            proj_wall_pilates_auto_workout
        WHERE
            <foreach collection="yogaAutoWorkoutTemplateIdList" item="id" open="proj_yoga_auto_workout_template_id in (" close=")" separator=",">
                #{id}
            </foreach>
          AND del_flag = 0
        GROUP BY
            proj_yoga_auto_workout_template_id
    </select>
</mapper>