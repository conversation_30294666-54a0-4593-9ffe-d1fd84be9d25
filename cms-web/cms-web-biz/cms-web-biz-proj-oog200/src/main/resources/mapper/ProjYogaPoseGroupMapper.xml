<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseGroupMapper">




    <select id="findCount" resultType="com.laien.web.biz.proj.oog200.bo.CountBO">
        SELECT
            pl.id,
            count(*) count
        FROM
            proj_yoga_pose_group pg
                JOIN proj_yoga_pose_level_group_relation plgr ON plgr.proj_yoga_pose_group_id = pg.id
                JOIN proj_yoga_pose_level pl ON pl.id = plgr.proj_yoga_pose_level_id
        WHERE
            pl.id IN
        <foreach collection="poseLevelIdSet" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
          AND plgr.del_flag = 0
          AND pg.del_flag = 0
        <if test="status != null">
          AND pg.status = #{status}
        </if>
        GROUP BY pl.id
    </select>
    <select id="findByPoseLevelId" resultType="com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupListVO">
        SELECT pg.id,
               pg.name,
               pg.event_name,
               pg.description,
               pg.type,
               pg.group_img_light_url,
               pg.group_img_dark_url,
               pg.status
        FROM proj_yoga_pose_group pg
                 JOIN proj_yoga_pose_level_group_relation plgr ON plgr.proj_yoga_pose_group_id = pg.id
        WHERE plgr.proj_yoga_pose_level_id = #{poseLevelId}
          AND plgr.del_flag = 0
          AND pg.del_flag = 0
        ORDER BY plgr.id
    </select>
</mapper>