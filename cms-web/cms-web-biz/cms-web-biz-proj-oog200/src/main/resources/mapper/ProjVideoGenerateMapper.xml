<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog200.mapper.ProjVideoGenerateMapper">

    <select id="selectCountByTemplateIds" resultType="com.laien.web.frame.response.IdAndCountsRes">
        SELECT
            count(*) counts,
            template_id id
        FROM
            proj_video_generate
        WHERE
            del_flag = 0
        <foreach collection="list" item="item" open="and template_id in (" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
            template_id
    </select>

    <select id="selectGenerateVideoListById" resultType="com.laien.web.biz.proj.oog200.response.ProjVideoGenerateVideoSliceVO">
        SELECT
            v.id,
            v.video_name,
            v.video_code,
            v.video_type,
            v.calorie,
            v.focus,
            v.difficulty,
            v.data_version
        FROM
            proj_video_generate_relation vgr
            INNER JOIN res_video_slice v ON vgr.video_id = v.id
        WHERE
            vgr.del_flag = 0
            AND vgr.generate_id = #{id}
    </select>

</mapper>