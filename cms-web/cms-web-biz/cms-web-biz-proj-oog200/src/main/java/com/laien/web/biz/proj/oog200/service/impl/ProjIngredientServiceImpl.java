package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjIngredient;
import com.laien.web.biz.proj.oog200.mapper.ProjIngredientMapper;
import com.laien.web.biz.proj.oog200.request.ProjIngredientReq;
import com.laien.web.biz.proj.oog200.response.ProjIngredientVO;
import com.laien.web.biz.proj.oog200.service.IProjIngredientService;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * ingredient 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Service
public class ProjIngredientServiceImpl extends ServiceImpl<ProjIngredientMapper, ProjIngredient> implements IProjIngredientService {

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private IProjInfoService projInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<ProjIngredientReq> ingredientReqList, Integer dishId, Integer projId) {
        LambdaUpdateWrapper<ProjIngredient> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjIngredient::getProjDishId, dishId);
        baseMapper.delete(wrapper);
        if(CollUtil.isEmpty(ingredientReqList)){
            return;
        }
        List<ProjIngredient> ingredientList = new ArrayList<>(ingredientReqList.size());
        for (ProjIngredientReq req : ingredientReqList) {
            ProjIngredient ingredient = new ProjIngredient();
            BeanUtils.copyProperties(req, ingredient);
            ingredient.setProjId(projId)
                    .setProjDishId(dishId);
            ingredientList.add(ingredient);
        }
        saveBatch(ingredientList);

        projLmsI18nService.handleI18n(ingredientList, projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBatch(List<Integer> dishIdList) {
        if(CollUtil.isEmpty(dishIdList)){
            return;
        }
        LambdaUpdateWrapper<ProjIngredient> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjIngredient::getProjDishId, dishIdList);
        baseMapper.delete(wrapper);
    }

    @Override
    public List<ProjIngredientVO> query(Integer dishId) {
        if(null == dishId){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjIngredient> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjIngredient::getProjDishId, dishId)
                .orderByAsc(BaseModel::getId);
        List<ProjIngredient> ingredientList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(ingredientList)){
            return new ArrayList<>();
        }
        List<ProjIngredientVO> ingredientListVO = new ArrayList<>(ingredientList.size());
        for (ProjIngredient ingredient : ingredientList) {
            ProjIngredientVO ingredientVO = new ProjIngredientVO();
            BeanUtils.copyProperties(ingredient, ingredientVO);
            ingredientListVO.add(ingredientVO);
        }
        return ingredientListVO;
    }
}
