package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkout;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/25 10:46
 */
public interface ProjChairYogaAutoWorkoutMapper extends BaseMapper<ProjChairYogaAutoWorkout> {

    List<CountBO> listWorkoutCountByTemplate(@Param("templateIds") Collection<Integer> templateIds);
}
