package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog200.enums.MusicTypeEnum;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音乐表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaMusic对象", description="音乐表")
public class ProjYogaMusicPageReq extends PageReq {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Music Id")
    private Integer id;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "音乐类型")
    private MusicTypeEnum musicType;

    @JsonIgnore
    private Integer projId;

}
