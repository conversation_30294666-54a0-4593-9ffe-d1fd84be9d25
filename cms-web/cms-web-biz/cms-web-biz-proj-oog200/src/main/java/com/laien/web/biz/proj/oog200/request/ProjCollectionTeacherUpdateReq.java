package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjCollectionTeacherUpdateReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/28
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjCollectionTeacherUpdateReq", description="ProjCollectionTeacherUpdateReq")
public class ProjCollectionTeacherUpdateReq extends ProjCollectionTeacherAddReq {

    @ApiModelProperty(value = "id")
    private Integer id;
}