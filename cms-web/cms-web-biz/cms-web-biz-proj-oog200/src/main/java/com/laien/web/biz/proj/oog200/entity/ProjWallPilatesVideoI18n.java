package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.entity.BaseUserAssignIdModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@ApiModel(value="ProjWallPilatesVideo多语言", description="ProjWallPilatesVideo多语言")
public class ProjWallPilatesVideoI18n extends BaseUserAssignIdModel {

    @ApiModelProperty(value = "名称多语言翻译")
    private String name;

    @ApiModelProperty(value = "名称翻译音频")
    private String nameFemale;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameFemaleDuration;


}
