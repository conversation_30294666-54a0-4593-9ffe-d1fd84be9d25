package com.laien.web.biz.proj.oog200.response;

import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjWallPilatesVideoListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "workoutId")
    private Integer workoutId;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "English Voice Name")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String[] targetArr;

    @ApiModelProperty(value = "target,多个用英文逗号分隔，Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String position;

    @ApiModelProperty(value = "动作类型 ： Warm Up、Main 、Cool Down")
    private String type;

    @ApiModelProperty(value = "当前动作方向 Left、Right、Central")
    private String direction;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "duration")
    private Integer duration;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "frontTaskStatus")
    private TaskResourceSectionStatusEnums frontTaskStatus;

    @ApiModelProperty(value = "sideTaskStatus")
    private TaskResourceSectionStatusEnums sideTaskStatus;

    @ApiModelProperty(value = "projWallPilatesVideoResourceId")
    private Integer projWallPilatesVideoResourceId;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;
}
