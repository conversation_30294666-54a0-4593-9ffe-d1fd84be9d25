package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yoga video next 下一个视频过渡
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video next 下一个视频过渡", description = "yoga video next 下一个视频过渡")
public class ResYogaVideoNextTransitionAddReq {

    @ApiModelProperty(value = "下一个视频过渡id")
    private Integer id;

}
