package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjChairYogaRegularWorkoutVideoRelationMapper;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaRegularWorkoutVideoRelationService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/11/4 17:45
 */
@Slf4j
@Service
public class ProjChairYogaRegularWorkoutVideoRelationServiceImpl extends ServiceImpl<ProjChairYogaRegularWorkoutVideoRelationMapper, ProjChairYogaRegularWorkoutVideoRelation> implements IProjChairYogaRegularWorkoutVideoRelationService {

    @Resource
    ProjChairYogaRegularWorkoutVideoRelationMapper relationMapper;

    @Override
    public void deleteByWorkoutIds(Collection<Integer> workoutIds) {

        LambdaUpdateWrapper<ProjChairYogaRegularWorkoutVideoRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjChairYogaRegularWorkoutVideoRelation::getDelFlag, GlobalConstant.YES);
        wrapper.set(ProjChairYogaRegularWorkoutVideoRelation::getUpdateTime, LocalDateTime.now());

        wrapper.set(ProjChairYogaRegularWorkoutVideoRelation::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.in(ProjChairYogaRegularWorkoutVideoRelation::getProjChairYogaRegularWorkoutId, workoutIds);
        this.update(new ProjChairYogaRegularWorkoutVideoRelation(), wrapper);
    }

    @Override
    public List<ProjChairYogaVideoPageVO> listRelationByWorkoutId(Integer workoutId) {

        LambdaQueryWrapper<ProjChairYogaAutoWorkoutVideoRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjChairYogaAutoWorkoutVideoRelation::getProjChairYogaAutoWorkoutId, workoutId);

        List<ProjChairYogaVideoPageVO> videoList = relationMapper.listRelationAndVideo(workoutId);
        return videoList;
    }

}
