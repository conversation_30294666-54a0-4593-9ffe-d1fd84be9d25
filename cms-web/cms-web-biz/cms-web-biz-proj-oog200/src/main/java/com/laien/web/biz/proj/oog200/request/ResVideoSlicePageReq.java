package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video slice 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video slice 分页", description = "video slice 分页")
public class ResVideoSlicePageReq extends PageReq {

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频类型")
    private String videoType;

    @ApiModelProperty(value = "状态")
    private Integer status;

}
