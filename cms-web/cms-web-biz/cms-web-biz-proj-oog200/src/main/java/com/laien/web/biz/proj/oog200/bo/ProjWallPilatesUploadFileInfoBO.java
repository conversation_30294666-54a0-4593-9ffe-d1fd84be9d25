package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.TsTextMergeBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="Wall pilates auto workout")
public class ProjWallPilatesUploadFileInfoBO {


    private Integer duration;
    private BigDecimal calorie;
    private TsTextMergeBO tsTextMergeBO;
    private List<TsMergeBO> tsMerge2532BOList;
    private List<AudioJsonBO> shortAudioList;
    private List<AudioJsonBO> longAudioList;

}
