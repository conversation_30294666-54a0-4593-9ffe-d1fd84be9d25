package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * AutoWorkoutBasicInfoImportReq
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="AutoWorkoutBasicInfoImportReq对象", description="AutoWorkoutBasicInfoImportReq")
public class YogaQuoteImportReq {

    @NotEmpty(message = "content cannot be empty", groups = Group1.class)
    @Length(message = "The content cannot exceed 511 characters", min = 1, max = 511, groups = Group2.class)
    @ExcelProperty(value = "content", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名字")
    private String content;


}
