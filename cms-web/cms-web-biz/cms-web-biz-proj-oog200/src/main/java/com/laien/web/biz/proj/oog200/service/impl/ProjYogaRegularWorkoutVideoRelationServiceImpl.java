package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaRegularWorkoutVideoRelationMapper;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutVideoDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaRegularWorkoutVideoRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * oog200 regular workout 关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class ProjYogaRegularWorkoutVideoRelationServiceImpl extends ServiceImpl<ProjYogaRegularWorkoutVideoRelationMapper, ProjYogaRegularWorkoutVideoRelation> implements IProjYogaRegularWorkoutVideoRelationService {


    @Override
    public List<ProjYogaRegularWorkoutVideoDetailVO> selectYogaRegularWorkoutVideos(Integer workoutId) {
        return this.baseMapper.selectYogaRegularWorkoutVideos(workoutId);
    }

}
