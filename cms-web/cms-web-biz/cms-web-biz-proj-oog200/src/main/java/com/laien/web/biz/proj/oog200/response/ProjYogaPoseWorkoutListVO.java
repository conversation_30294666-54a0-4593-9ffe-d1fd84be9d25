package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * proj yoga pose workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseWorkout对象", description="proj yoga pose workout")
public class ProjYogaPoseWorkoutListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "workout 封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "难度Newbie, Beginner, Intermediate, Advanced")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "Standing，Seated，Supine，Prone，Arm & Leg Support")
    private String position;

    @ApiModelProperty(value = "取值Flexibility、Balance、Strength、Relaxation")
    private String focus;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

}
