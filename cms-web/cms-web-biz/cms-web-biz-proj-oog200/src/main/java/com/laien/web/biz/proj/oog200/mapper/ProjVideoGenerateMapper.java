package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjVideoGenerate;
import com.laien.web.biz.proj.oog200.response.ProjVideoGenerateVideoSliceVO;
import com.laien.web.frame.response.IdAndCountsRes;

import java.util.List;

/**
 * <p>
 * video generate Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
public interface ProjVideoGenerateMapper extends BaseMapper<ProjVideoGenerate> {

    /**
     * 查询video 生成数量
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<IdAndCountsRes> selectCountByTemplateIds(List<Integer> templateIdList);

    /**
     * 查询视频生成的ts 视频里列表
     * @return list
     */
    List<ProjVideoGenerateVideoSliceVO> selectGenerateVideoListById(Integer id);

}
