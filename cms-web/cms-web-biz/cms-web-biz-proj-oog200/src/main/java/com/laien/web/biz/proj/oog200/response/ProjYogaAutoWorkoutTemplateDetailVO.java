package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value = "ProjYogaAutoWorkoutTemplateDetailVO", description = "ProjYogaAutoWorkoutTemplateDetailVO")
public class ProjYogaAutoWorkoutTemplateDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "多语言列表")
    private List<String> languageArr;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "最小时长 单位分钟")
    private Integer minTime;

    @ApiModelProperty(value = "最大时长 单位分钟")
    private Integer maxTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "template类型：Classic Yoga、Wall Pilates、Chair Yoga")
    private String type;

    @ApiModelProperty(value = "warm up个数")
    private Integer warmUpCount;

    @ApiModelProperty(value = "cool down个数")
    private Integer coolDownCount;

}
