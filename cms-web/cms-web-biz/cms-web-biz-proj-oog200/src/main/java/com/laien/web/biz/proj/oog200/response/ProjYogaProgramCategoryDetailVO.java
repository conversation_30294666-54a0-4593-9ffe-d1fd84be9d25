package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:58
 */
@Data
public class ProjYogaProgramCategoryDetailVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "课程类型")
    private List<YogaProgramTypeEnum> programType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "关联的program列表")
    List<ProjYogaProgramPageVO> programList;
}
