package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author:  hhl
 * Date:  2024/7/31 12:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjYogaPoseVideoAudioDetailVO {

    @JsonIgnore
    @ApiModelProperty(value = "关联的proj yoga pose video id")
    private Integer projYogaPoseVideoId;

    @ApiModelProperty(value = "用于标识在pose的第几轮播放中使用")
    private Integer roundIndex;

    @ApiModelProperty(value = "pose video 第一阶段 解说音频")
    private String firstGuidanceAudioUrl;
    @ApiModelProperty(value = "pose video 第一阶段 解说音频时长")
    private Integer firstGuidanceAudioDuration;
    @ApiModelProperty(value = "用于表示 pose video 第一阶段音频是否启用，True表示启用，False禁用。")
    private Boolean firstAudioEnable = Boolean.TRUE;

    @ApiModelProperty(value = "pose video 第二阶段 解说音频")
    private String secondGuidanceAudioUrl;
    @ApiModelProperty(value = "pose video 第二阶段 解说音频时长")
    private Integer secondGuidanceAudioDuration;
    @ApiModelProperty(value = "用于表示 pose video 第一阶段音频是否启用，True表示启用，False禁用。")
    private Boolean secondAudioEnable = Boolean.TRUE;

    @ApiModelProperty(value = "pose video 第三阶段 解说音频")
    private String thirdGuidanceAudioUrl;
    @ApiModelProperty(value = "pose video 第三阶段 解说音频时长")
    private Integer thirdGuidanceAudioDuration;
    @ApiModelProperty(value = "用于表示 pose video 第三阶段音频是否启用，True表示启用，False禁用。")
    private Boolean thirdAudioEnable = Boolean.TRUE;

    @ApiModelProperty(value = "pose video 第四阶段 解说音频")
    private String fourthGuidanceAudioUrl;
    @ApiModelProperty(value = "pose video 第四阶段 解说音频时长")
    private Integer fourthGuidanceAudioDuration;
    @ApiModelProperty(value = "用于表示 pose video 第四阶段音频是否启用，True表示启用，False禁用。")
    private Boolean fourthAudioEnable = Boolean.TRUE;

    @ApiModelProperty(value = "pose video 第五阶段 解说音频")
    private String fifthGuidanceAudioUrl;
    @ApiModelProperty(value = "pose video 第五阶段 解说音频时长")
    private Integer fifthGuidanceAudioDuration;
    @ApiModelProperty(value = "用于表示 pose video 第五阶段音频是否启用，True表示启用，False禁用。")
    private Boolean fifthAudioEnable = Boolean.TRUE;

    @ApiModelProperty(value = "pose video 第六阶段 解说音频")
    private String sixthGuidanceAudioUrl;
    @ApiModelProperty(value = "pose video 第六阶段 解说音频时长")
    private Integer sixthGuidanceAudioDuration;
    @ApiModelProperty(value = "用于表示 pose video 第六阶段音频是否启用，True表示启用，False禁用。")
    private Boolean sixthAudioEnable = Boolean.TRUE;

}
