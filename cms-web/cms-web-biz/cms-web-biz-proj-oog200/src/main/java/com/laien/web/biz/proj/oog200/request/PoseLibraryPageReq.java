package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "pose分页请求", description = "pose分页请求")
public class PoseLibraryPageReq extends PageReq {

    @ApiModelProperty(value = "pose名称")
    private String poseName;

    @ApiModelProperty(value = "启用状态")
    private Integer status;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "position")
    private String position;

    @ApiModelProperty(value = "focusList")
    private List<String> focusList;

    @ApiModelProperty(value = "按名称排序 1按名称升序 2按名称降序")
    private Integer orderByPoseName;
}
