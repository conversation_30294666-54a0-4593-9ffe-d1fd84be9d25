package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目poseLibrary
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjPoseLibrary对象", description = "项目poseLibrary")
public class ProjPoseLibrary extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "pose library id")
    private Integer poseLibraryId;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "删除标识 0 未删除，1已删除")
    private Integer delFlag;

}
