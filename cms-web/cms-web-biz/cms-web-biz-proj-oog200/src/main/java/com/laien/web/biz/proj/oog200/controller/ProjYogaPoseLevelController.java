package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseLevel;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseLevelDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseLevelListVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseLevelService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj yoga pose grouping 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Api(tags = "项目管理:Pose Diff_Level Group")
@RestController
@RequestMapping("/proj/yogaPoseLevel")
public class ProjYogaPoseLevelController extends ResponseController {

    @Resource
    private IProjYogaPoseLevelService projYogaPoseLevelService;


    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaPoseLevelListVO>> page(ProjYogaPoseLevelPageReq pageReq) {
        PageRes<ProjYogaPoseLevelListVO> pageRes = projYogaPoseLevelService.page(pageReq, RequestContextUtils.getProjectId());
        return succ(pageRes);
    }


    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaPoseLevelUpdateReq poseLevelReq) {
        projYogaPoseLevelService.update(poseLevelReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaPoseLevelAddReq poseLevelReq) {
        projYogaPoseLevelService.save(poseLevelReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaPoseLevelDetailVO> detail(@PathVariable Integer id) {
        ProjYogaPoseLevelDetailVO detailVO = projYogaPoseLevelService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseLevelService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseLevelService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseLevelService.deleteByIdList(idList);
        return succ();
    }

}
