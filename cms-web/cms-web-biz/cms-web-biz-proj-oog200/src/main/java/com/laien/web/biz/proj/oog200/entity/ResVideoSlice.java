package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * video slice
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResVideoSlice对象", description="video slice")
public class ResVideoSlice extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频类型")
    private String videoType;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "focus")
    private String focus;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "机位1视频地址")
    private String video1Url;

    @ApiModelProperty(value = "机位1视频开始机位")
    private String video1StartStand;

    @ApiModelProperty(value = "机位1视频结束机位")
    private String video1EndStand;

    @ApiModelProperty(value = "机位1视频时长")
    private Integer video1Duration;

    @ApiModelProperty(value = "机位1视频地址")
    private String video2Url;

    @ApiModelProperty(value = "机位1视频开始机位")
    private String video2StartStand;

    @ApiModelProperty(value = "机位1视频结束机位")
    private String video2EndStand;

    @ApiModelProperty(value = "机位1视频时长")
    private Integer video2Duration;

    @ApiModelProperty(value = "视频标题字幕")
    private String titleSubtitleUrl;

    @ApiModelProperty(value = "视频详细指导字幕url")
    private String guidanceDefaultUrl;

    @ApiModelProperty(value = "视频详细指导音频url")
    private String guidanceDefaultAudioUrl;

    @ApiModelProperty(value = "视频简略指导字幕url")
    private String guidanceLeastUrl;

    @ApiModelProperty(value = "视频简略指导音频url")
    private String guidanceLeastAudioUrl;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "数据版本")
    private Integer dataVersion;


}
