package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj yoga pose grouping workout relation
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseGroupWorkoutRelation对象", description="proj yoga pose grouping workout relation")
public class ProjYogaPoseGroupWorkoutRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_yoga_pose_group_id")
    private Integer projYogaPoseGroupId;

    @ApiModelProperty(value = "proj_yoga_pose_workout_id")
    private Integer projYogaPoseWorkoutId;


}
