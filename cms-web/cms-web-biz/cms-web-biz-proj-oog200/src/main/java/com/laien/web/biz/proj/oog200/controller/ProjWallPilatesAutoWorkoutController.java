package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesAutoWorkout;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesAutoWorkoutListVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesAutoWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj yoga pose workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Api(tags = "项目管理:wall pilates auto workout")
@RestController
@RequestMapping("/proj/wallPilatesAutoWorkout")
public class ProjWallPilatesAutoWorkoutController extends ResponseController {

    @Resource
    private IProjWallPilatesAutoWorkoutService projWallPilatesAutoWorkoutService;

    @ApiOperation(value = "分页列表")
    @GetMapping( "/page")
    public ResponseResult<PageRes<ProjWallPilatesAutoWorkoutListVO>> page(ProjWallPilatesAutoWorkoutPageReq pageReq) {
        PageRes<ProjWallPilatesAutoWorkoutListVO> pageRes = projWallPilatesAutoWorkoutService.page(pageReq,RequestContextUtils.getProjectId());
        return succ(pageRes);
    }

    @ApiOperation(value = "分页-workout-video列表")
    @GetMapping("/page/{id}/video")
    public ResponseResult<PageRes<ProjWallPilatesVideoListVO>> pageWorkout(PageReq pageReq, @PathVariable Integer id) {
        return succ(projWallPilatesAutoWorkoutService.pageVideo(pageReq, id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjWallPilatesAutoWorkoutAddReq workoutReq) {
        projWallPilatesAutoWorkoutService.add(workoutReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjWallPilatesAutoWorkoutUpdateReq workoutUpdateReq) {
        projWallPilatesAutoWorkoutService.update(workoutUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjWallPilatesAutoWorkoutDetailVO> detail(@PathVariable Integer id) {
        ProjWallPilatesAutoWorkoutDetailVO detailVO = projWallPilatesAutoWorkoutService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWallPilatesAutoWorkoutService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWallPilatesAutoWorkoutService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWallPilatesAutoWorkoutService.deleteByIdList(idList);
        return succ();
    }


    @ApiOperation(value = "批量更新workout 音视频资源", notes = "异步更新")
    @PostMapping("/updateFileBatch")
    public ResponseResult<Void> updateFileBatch(@RequestBody IdListReq idListReq) {
        projWallPilatesAutoWorkoutService.updateFileBatch(idListReq.getIdList(), RequestContextUtils.getProjectId());
        return succ();
    }



}
