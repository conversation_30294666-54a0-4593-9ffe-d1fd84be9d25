package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ResPoseLibrary;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * pose表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
public interface ResPoseLibraryMapper extends BaseMapper<ResPoseLibrary> {

    @Select("select * from res_pose_library where id in (${idsStr}) ")
    List<ResPoseLibrary> selectListByIds(@Param("idsStr") String idsStr);

    @Select("select * from res_pose_library where id in (${idsStr}) order by ${orderByName} ${orderBy} limit ${offset} ,${limit}")
    List<ResPoseLibrary> selectOrderListByIds(@Param("idsStr") String idsStr, @Param("orderByName") String orderByName, @Param("orderBy") String orderBy, @Param("offset") Integer offset, @Param("limit") Integer limit);

}
