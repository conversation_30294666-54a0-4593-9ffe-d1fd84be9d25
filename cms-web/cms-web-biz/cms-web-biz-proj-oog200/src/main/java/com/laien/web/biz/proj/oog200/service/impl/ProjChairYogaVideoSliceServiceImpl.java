package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideoSlice;
import com.laien.web.biz.proj.oog200.mapper.ProjChairYogaVideoSliceMapper;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaVideoSliceService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/9/24 11:42
 */

@Slf4j
@Service
public class ProjChairYogaVideoSliceServiceImpl extends ServiceImpl<ProjChairYogaVideoSliceMapper, ProjChairYogaVideoSlice> implements IProjChairYogaVideoSliceService {

    @Resource
    ProjChairYogaVideoSliceMapper sliceMapper;

    @Override
    public void deleteByChairYodaVideoId(Integer chairYogaVideoId) {

        if (Objects.isNull(chairYogaVideoId)) {
            return;
        }

        LambdaUpdateWrapper<ProjChairYogaVideoSlice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjChairYogaVideoSlice::getProjChairYogaVideoId, chairYogaVideoId);
        updateWrapper.eq(ProjChairYogaVideoSlice::getDelFlag, GlobalConstant.NO);
        remove(updateWrapper);
    }

    @Override
    public List<ProjChairYogaVideoSliceDetailVO> listByChairYogaVideoId(Collection<Integer> chairYogaVideoIds) {

        LambdaQueryWrapper<ProjChairYogaVideoSlice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjChairYogaVideoSlice::getProjChairYogaVideoId, chairYogaVideoIds);
        queryWrapper.eq(ProjChairYogaVideoSlice::getDelFlag, GlobalConstant.NO);

        List<ProjChairYogaVideoSlice> sliceList = list(queryWrapper);
        if (CollectionUtils.isEmpty(sliceList)) {
            return Collections.emptyList();
        }

        return sliceList.stream().map(this::convert2DetailVO).collect(Collectors.toList());
    }

    @Override
    public void videoDurationCount(Collection<Integer> chairYogaVideoIds, Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap) {

        LambdaQueryWrapper<ProjChairYogaVideoSlice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjChairYogaVideoSlice::getProjChairYogaVideoId, chairYogaVideoIds);
        queryWrapper.eq(ProjChairYogaVideoSlice::getDelFlag, GlobalConstant.NO);

        List<ProjChairYogaVideoSlice> sliceList = list(queryWrapper);
        if (CollectionUtils.isEmpty(sliceList)) {
            return;
        }

        Map<Integer, List<ProjChairYogaVideoSlice>> videoSLiceMap = sliceList.stream().collect(
                Collectors.groupingBy(ProjChairYogaVideoSlice::getProjChairYogaVideoId));

        AtomicBoolean startFromFront = new AtomicBoolean(true);
        AtomicBoolean startFromSide = new AtomicBoolean(true);
        videoSLiceMap.entrySet().forEach(entry -> {
            computeDuration(entry, startFromFront, frontDurationMap);
            computeDuration(entry, startFromSide, sideDurationMap);
        });
    }

    private void computeDuration(Map.Entry<Integer, List<ProjChairYogaVideoSlice>> entry, AtomicBoolean start, Map<Integer, Integer> durationMap) {
        Integer duration = entry.getValue().stream().sorted(Comparator.comparing(ProjChairYogaVideoSlice::getSliceIndex)).mapToInt(slice -> {
            if (start.get()) {
                start.set(false);
                return slice.getFrontVideoDuration();
            } else {
                start.set(true);
                return slice.getSideVideoDuration();
            }
        }).sum();
        durationMap.put(entry.getKey(), duration);
    }

    private ProjChairYogaVideoSliceDetailVO convert2DetailVO(ProjChairYogaVideoSlice slice) {

        ProjChairYogaVideoSliceDetailVO detailVO = new ProjChairYogaVideoSliceDetailVO();
        BeanUtils.copyProperties(slice, detailVO);
        return detailVO;
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveBatch(List<ProjChairYogaVideoSlice> sliceList) {

        if (CollectionUtils.isEmpty(sliceList)) {
            return;
        }

        super.saveBatch(sliceList);
    }
}
