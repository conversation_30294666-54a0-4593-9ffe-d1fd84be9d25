package com.laien.web.biz.proj.oog200.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.biz.proj.oog200.entity.ProjAutoWorkoutBasicInfo;
import com.laien.web.biz.proj.oog200.request.AutoWorkoutBasicInfoImportReq;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * proj_image
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Data
@Accessors(chain = true)
@TableName("proj_auto_workout_basic_info")
@ApiModel(value="ProjAutoWorkoutBasicInfo对象", description="proj_image")
public class ProjAutoWorkoutBasicInfoBO {

    private ProjAutoWorkoutBasicInfo projAutoWorkoutBasicInfo;
    private AutoWorkoutBasicInfoImportReq importReq;
    private List<Integer> workoutIdList;

}
