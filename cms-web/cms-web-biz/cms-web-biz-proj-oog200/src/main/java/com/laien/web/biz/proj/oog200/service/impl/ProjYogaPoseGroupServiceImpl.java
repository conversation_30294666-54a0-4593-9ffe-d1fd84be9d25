package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseGroup;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseGroupWorkoutRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseGroupMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupListReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupListVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutListVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseGroupService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseGroupWorkoutRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseWorkoutService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj yoga pose grouping 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
public class ProjYogaPoseGroupServiceImpl extends ServiceImpl<ProjYogaPoseGroupMapper, ProjYogaPoseGroup>
        implements IProjYogaPoseGroupService {

    @Resource
    private IProjYogaPoseGroupWorkoutRelationService projYogaPoseGroupWorkoutRelationService;
    @Resource
    private IProjYogaPoseWorkoutService projYogaPoseWorkoutService;
    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Override
    public void save(ProjYogaPoseGroupAddReq poseGroupReq, Integer projId) {
        check(poseGroupReq, null);
        ProjYogaPoseGroup poseGroup = new ProjYogaPoseGroup();
        BeanUtils.copyProperties(poseGroupReq, poseGroup);
        poseGroup.setProjId(projId);
        save(poseGroup);
        List<Integer> poseWorkoutIdList = poseGroupReq.getYogaPoseWorkoutIdList();
        saveGroupWorkoutRelation(poseWorkoutIdList, poseGroup);
        lmsI18nService.handleI18n(Collections.singletonList(poseGroup), projId);
    }

    @Override
    public void update(ProjYogaPoseGroupUpdateReq poseGroupReq, Integer projId) {
        Integer id = poseGroupReq.getId();
        ProjYogaPoseGroup yogaPoseGroup = baseMapper.selectById(id);
        if(null == yogaPoseGroup){
            throw new BizException("yogaPoseGroup not found");
        }
        check(poseGroupReq, id);
        ProjYogaPoseGroup poseGroup = new ProjYogaPoseGroup();
        BeanUtils.copyProperties(poseGroupReq, poseGroup);
        poseGroup.setProjId(projId);
        updateById(poseGroup);
        projYogaPoseGroupWorkoutRelationService.deleteByYogaPoseGroupId(poseGroup.getId());
        saveGroupWorkoutRelation(poseGroupReq.getYogaPoseWorkoutIdList(), poseGroup);
        lmsI18nService.handleI18n(Collections.singletonList(poseGroup), projId);
    }

    @Override
    public ProjYogaPoseGroupDetailVO findDetailById(Integer id) {
        ProjYogaPoseGroup yogaPoseGroup = baseMapper.selectById(id);
        if (null == yogaPoseGroup) {
            return null;
        }
        ProjYogaPoseGroupDetailVO detailVO = new ProjYogaPoseGroupDetailVO();
        BeanUtils.copyProperties(yogaPoseGroup, detailVO);
        List<ProjYogaPoseWorkoutListVO> workoutList = projYogaPoseWorkoutService.findByPoseGroupId(id);
        detailVO.setYogaPoseWorkoutList(workoutList);
        return detailVO;
    }

    @Override
    public List<ProjYogaPoseGroupListVO> list(ProjYogaPoseGroupListReq listReq, Integer projId) {
        Integer status = listReq.getStatus();
        String name = listReq.getName();
        String type = listReq.getType();
        LambdaQueryWrapper<ProjYogaPoseGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseGroup::getProjId, projId)
                .like(StringUtils.isNotBlank(name), ProjYogaPoseGroup::getName, name)
                .eq(null != status, ProjYogaPoseGroup::getStatus, status)
                .eq(StringUtils.isNotBlank(type), ProjYogaPoseGroup::getType, type)
                .orderByAsc(ProjYogaPoseGroup::getSort)
                .orderByDesc(BaseModel::getCreateTime);
        List<ProjYogaPoseGroup> groupList = list(wrapper);
        if (CollUtil.isEmpty(groupList)) {
            return null;
        }
        Set<Integer> groupIdSet = groupList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjYogaPoseGroupListVO> poseGroupList = new ArrayList<>(groupList.size());
        List<CountBO> workoutCountBO = projYogaPoseWorkoutService.findCount(null, groupIdSet);
        List<CountBO> workoutEnabledCountBO = projYogaPoseWorkoutService.findCount(GlobalConstant.STATUS_ENABLE, groupIdSet);
        Map<Integer, List<CountBO>> workoutCountGroup = workoutCountBO.stream().collect(Collectors.groupingBy(CountBO::getId));
        Map<Integer, List<CountBO>> workoutEnabledCountGroup = workoutEnabledCountBO.stream().collect(Collectors.groupingBy(CountBO::getId));
        for (ProjYogaPoseGroup group : groupList) {
            ProjYogaPoseGroupListVO yogaPoseGroup = new ProjYogaPoseGroupListVO();
            BeanUtils.copyProperties(group, yogaPoseGroup);
            Integer id = group.getId();
            List<CountBO> countList = workoutCountGroup.get(id);
            if (CollUtil.isNotEmpty(countList)) {
                Integer workoutCount = countList.get(GlobalConstant.ZERO).getCount();
                yogaPoseGroup.setWorkoutNumber(workoutCount);
            }
            List<CountBO> enabledCountList = workoutEnabledCountGroup.get(id);
            if (CollUtil.isNotEmpty(enabledCountList)) {
                Integer workoutEnabledCount = enabledCountList.get(GlobalConstant.ZERO).getCount();
                yogaPoseGroup.setWorkoutEnabledNumber(workoutEnabledCount);
            }
            poseGroupList.add(yogaPoseGroup);
        }
        return poseGroupList;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)){
            return;
        }
        LambdaUpdateWrapper<ProjYogaPoseGroup> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseGroup::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseGroup::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaPoseGroup::getId, idList);
        update(new ProjYogaPoseGroup(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjYogaPoseGroup> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseGroup::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaPoseGroup::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseGroup::getId, idList);
        this.update(new ProjYogaPoseGroup(), wrapper);
    }

    @Override
    @Transactional
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<ProjYogaPoseGroup> groupList = new ArrayList<>(idList.size());
        for (int i = GlobalConstant.ZERO; i < idList.size(); i++) {
            ProjYogaPoseGroup group = new ProjYogaPoseGroup();
            group.setSort(i)
                    .setId(idList.get(i));
            groupList.add(group);
        }
        updateBatchById(groupList);
    }

    @Override
    public List<CountBO> findCount(Integer status, Set<Integer> poseLevelIdSet) {
        return baseMapper.findCount(status, poseLevelIdSet);
    }

    @Override
    public List<ProjYogaPoseGroupListVO> findByPoseLevelId(Integer poseLevelId){
        return baseMapper.findByPoseLevelId(poseLevelId);
    }

    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjYogaPoseGroup> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseGroup::getDelFlag, GlobalConstant.YES)
                .eq(ProjYogaPoseGroup::getStatus, GlobalConstant.STATUS_DRAFT)
                .in(ProjYogaPoseGroup::getId, idList);
        this.update(new ProjYogaPoseGroup(), wrapper);
    }

    private void saveGroupWorkoutRelation(List<Integer> poseWorkoutIdList, ProjYogaPoseGroup poseGroup) {
        if(CollUtil.isEmpty(poseWorkoutIdList)){
            return;
        }
        List<ProjYogaPoseGroupWorkoutRelation> groupWorkoutRelationList = new ArrayList<>(poseWorkoutIdList.size());
        Integer id = poseGroup.getId();
        for (Integer workoutId : poseWorkoutIdList) {
            ProjYogaPoseGroupWorkoutRelation groupWorkoutRelation = new ProjYogaPoseGroupWorkoutRelation();
            groupWorkoutRelation.setProjYogaPoseGroupId(id);
            groupWorkoutRelation.setProjYogaPoseWorkoutId(workoutId);
            groupWorkoutRelationList.add(groupWorkoutRelation);
        }
        projYogaPoseGroupWorkoutRelationService.saveBatch(groupWorkoutRelationList);
    }

    private void check(ProjYogaPoseGroupAddReq yogaPoseGroupAddReq, Integer id) {
        LambdaQueryWrapper<ProjYogaPoseGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseGroup::getName, yogaPoseGroupAddReq.getName())
                .ne(null != id, BaseModel::getId, id);
        if (baseMapper.selectCount(wrapper) > 0) {
            throw new BizException("name already exists");
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseGroup::getEventName, yogaPoseGroupAddReq.getEventName())
                .ne(null != id, BaseModel::getId, id);
        if (baseMapper.selectCount(wrapper) > 0) {
            throw new BizException("eventName already exists");
        }
    }
}
