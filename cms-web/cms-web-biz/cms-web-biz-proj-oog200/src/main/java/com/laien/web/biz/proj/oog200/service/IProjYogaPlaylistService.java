package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPlaylist;
import com.laien.web.biz.proj.oog200.request.ProjYogaPlaylistAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPlaylistUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistPageVO;
import com.laien.web.frame.request.IdListReq;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/19 15:07
 */
public interface IProjYogaPlaylistService extends IService<ProjYogaPlaylist> {

    /**
     * 查询项目下所有playlist
     *
     * @return
     */
    List<ProjYogaPlaylistPageVO> pageQuery(Integer projId);

    ProjYogaPlaylistDetailVO getDetailById(Integer id);

    void insert(ProjYogaPlaylistAddReq playlistAddReq);

    void sort(IdListReq idListReq);

    void update(ProjYogaPlaylistUpdateReq playlistUpdateReq);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);
}
