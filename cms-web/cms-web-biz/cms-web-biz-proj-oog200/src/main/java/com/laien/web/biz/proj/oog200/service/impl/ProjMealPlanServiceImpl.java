package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjMealPlan;
import com.laien.web.biz.proj.oog200.entity.ProjMealPlanRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjMealPlanMapper;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjDishListVO;
import com.laien.web.biz.proj.oog200.response.ProjMealPlanDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjMealPlanListVO;
import com.laien.web.biz.proj.oog200.service.IProjDishService;
import com.laien.web.biz.proj.oog200.service.IProjMealPlanRelationService;
import com.laien.web.biz.proj.oog200.service.IProjMealPlanService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/31 17:01
 */
@Slf4j
@Service
public class ProjMealPlanServiceImpl extends ServiceImpl<ProjMealPlanMapper, ProjMealPlan> implements IProjMealPlanService {

    @Resource
    IProjMealPlanRelationService planRelationService;

    @Resource
    IProjDishService dishService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjMealPlanListVO> selectMealPlanList(ProjMealPlanListReq listReq) {

        LambdaQueryWrapper<ProjMealPlan> queryWrapper = wrapQueryWrapper(listReq);
        List<ProjMealPlan> mealPlanList = list(queryWrapper);
        if (CollectionUtils.isEmpty(mealPlanList)) {
            return Collections.emptyList();
        }

        return mealPlanList.stream().map(mealPlan -> convert2ListVO(mealPlan)).collect(Collectors.toList());
    }

    private ProjMealPlanListVO convert2ListVO(ProjMealPlan mealPlan) {

        ProjMealPlanListVO listVO = new ProjMealPlanListVO();
        BeanUtils.copyProperties(mealPlan, listVO);
        return listVO;
    }

    private LambdaQueryWrapper<ProjMealPlan> wrapQueryWrapper(ProjMealPlanListReq pageReq) {

        LambdaQueryWrapper<ProjMealPlan> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjMealPlan::getName, pageReq.getName());
        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjMealPlan::getStatus, pageReq.getStatus());
        queryWrapper.orderByAsc(ProjMealPlan::getSorted);
        queryWrapper.orderByDesc(ProjMealPlan::getId);
        return queryWrapper;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveMealPlan(ProjMealPlanAddReq addReq) {

        checkMealPlan(addReq, null);
        ProjMealPlan mealPlan = new ProjMealPlan();
        BeanUtils.copyProperties(addReq, mealPlan);

        mealPlan.setStatus(GlobalConstant.STATUS_DRAFT);
        mealPlan.setProjId(RequestContextUtils.getProjectId());
        setDays(addReq, mealPlan);
        save(mealPlan);

        projLmsI18nService.handleI18n(Collections.singletonList(mealPlan), RequestContextUtils.getProjectId());

        if (CollectionUtils.isEmpty(addReq.getDailyDishList())) {
            return;
        }
        List<ProjMealPlanRelation> relationList = addReq.getDailyDishList().stream().map(dailyDish -> wrapPlanRelation(mealPlan.getId(), mealPlan.getProjId(), dailyDish)).collect(Collectors.toList());
        planRelationService.saveBatch(relationList);
    }

    private void setDays(ProjMealPlanAddReq addReq, ProjMealPlan mealPlan) {

        if (CollectionUtils.isEmpty(addReq.getDailyDishList())) {
            mealPlan.setDays(GlobalConstant.ZERO);
        }

        Map<Integer, List<DailyDishDetailVO>> dailyMap = addReq.getDailyDishList().stream().collect(Collectors.groupingBy(DailyDishDetailVO::getDay));
        mealPlan.setDays(dailyMap.size());
    }

    private void checkMealPlan(ProjMealPlanAddReq mealPlanAddReq, Integer id) {

        LambdaQueryWrapper<ProjMealPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjMealPlan::getName, mealPlanAddReq.getName())
                .eq(ProjMealPlan::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjMealPlan::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Meal plan name exists.");
        }

        LambdaQueryWrapper<ProjMealPlan> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjMealPlan::getEventName, mealPlanAddReq.getEventName())
                .eq(ProjMealPlan::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjMealPlan::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Meal plan event name exists.");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateMealPlan(ProjMealPlanUpdateReq updateReq) {

        ProjMealPlan mealPlan = this.getById(updateReq.getId());
        if (Objects.isNull(mealPlan)) {
            throw new BizException("Can't find mealPlan.");
        }

        checkMealPlan(updateReq, updateReq.getId());
        BeanUtils.copyProperties(updateReq, mealPlan);
        setDays(updateReq, mealPlan);
        updateById(mealPlan);

        projLmsI18nService.handleI18n(Collections.singletonList(mealPlan), RequestContextUtils.getProjectId());
        updateNewTime(updateReq);
        handlePlanRelation(updateReq);
    }

    private void handlePlanRelation(ProjMealPlanUpdateReq updateReq) {

        planRelationService.deleteByMealPlanId(updateReq.getId());
        if (CollectionUtils.isEmpty(updateReq.getDailyDishList())) {
            return;
        }

        List<ProjMealPlanRelation> relationList = updateReq.getDailyDishList().stream().map(dailyDish -> wrapPlanRelation(updateReq.getId(), RequestContextUtils.getProjectId(), dailyDish)).collect(Collectors.toList());
        planRelationService.saveBatch(relationList);
    }

    private ProjMealPlanRelation wrapPlanRelation(Integer mealPlanId, Integer projId, DailyDishDetailVO dailyDish) {

        ProjMealPlanRelation relation = new ProjMealPlanRelation();
        relation.setProjMealPlanId(mealPlanId);
        relation.setProjId(projId);
        relation.setDay(dailyDish.getDay());
        relation.setProjDishId(dailyDish.getId());
        relation.setDishType(dailyDish.getDishType());
        return relation;
    }

    private void updateNewTime(ProjMealPlanUpdateReq updateReq) {

        LambdaUpdateWrapper<ProjMealPlan> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjMealPlan::getNewStartTime, updateReq.getNewStartTime());
        updateWrapper.set(ProjMealPlan::getNewEndTime, updateReq.getNewEndTime());
        updateWrapper.eq(ProjMealPlan::getId, updateReq.getId());
        this.update(updateWrapper);
    }

    @Override
    public ProjMealPlanDetailVO getDetailById(Integer mealPlanId) {

        ProjMealPlan mealPlan = getById(mealPlanId);
        if (Objects.isNull(mealPlan)) {
            return null;
        }

        ProjMealPlanDetailVO detailVO = new ProjMealPlanDetailVO();
        BeanUtils.copyProperties(mealPlan, detailVO);

        List<DailyDishDetailVO> detailVOList = getDailyDishList(mealPlanId);
        detailVO.setDailyDishList(detailVOList);
        return detailVO;
    }

    public List<DailyDishDetailVO> getDailyDishList(Integer mealPlanId) {

        List<ProjMealPlanRelation> mealPlanRelationList = planRelationService.listByMealPlanId(mealPlanId);
        if(CollectionUtils.isEmpty(mealPlanRelationList)) {
            return Collections.emptyList();
        }

        List<Integer> dishIds = mealPlanRelationList.stream().map(ProjMealPlanRelation::getProjDishId).collect(Collectors.toList());
        ProjDishListReq dishListReq = new ProjDishListReq();
        dishListReq.setDishIds(dishIds);

        List<ProjDishListVO> dishList = dishService.list(dishListReq, RequestContextUtils.getProjectId());
        if (CollectionUtils.isEmpty(dishList)) {
            return Collections.emptyList();
        }

        Map<Integer, ProjDishListVO> dishIdMap = dishList.stream().collect(Collectors.toMap(ProjDishListVO::getId, Function.identity(), (k1, k2) -> k1));
        return mealPlanRelationList.stream().map(relation -> wrapDailyDishDetail(relation, dishIdMap.get(relation.getProjDishId()))).collect(Collectors.toList());
    }

    private DailyDishDetailVO wrapDailyDishDetail(ProjMealPlanRelation planRelation, ProjDishListVO dishList) {

        DailyDishDetailVO detailVO = new DailyDishDetailVO();
        BeanUtils.copyProperties(dishList, detailVO);
        detailVO.setDay(planRelation.getDay());
        detailVO.setDishType(planRelation.getDishType());
        return detailVO;
    }

    @Override
    public void sort(IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        List<ProjMealPlan> dishList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjMealPlan dishCollection = new ProjMealPlan();
            dishCollection.setSorted(i).setId(idList.get(i));
            dishList.add(dishCollection);
        }
        updateBatchById(dishList);
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjMealPlan> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjMealPlan::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjMealPlan::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjMealPlan::getId, idList);
        this.update(new ProjMealPlan(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjMealPlan> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjMealPlan::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjMealPlan::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjMealPlan::getId, idList);
        this.update(new ProjMealPlan(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjMealPlan> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjMealPlan::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjMealPlan::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjMealPlan::getId, idList);
        this.update(new ProjMealPlan(), wrapper);
    }
}
