package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "pose分页请求", description = "pose分页请求")
public class ProjPoseLibraryPageReq extends PageReq {

    @ApiModelProperty(value = "按名称排序 1按名称升序 2按名称降序")
    private Integer orderByPoseName;
}
