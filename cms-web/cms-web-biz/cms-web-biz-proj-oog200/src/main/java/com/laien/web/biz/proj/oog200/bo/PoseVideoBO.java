package com.laien.web.biz.proj.oog200.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2024/8/7 17:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoseVideoBO {

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "视频")
    private String videoUrl;

    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    @ApiModelProperty(value = "m3u8Text2k对应的m3u8内容")
    private String m3u8Text2k;

    @ApiModelProperty(value = "m3u8Text1080p对应的m3u8内容")
    private String m3u8Text1080p;

    @ApiModelProperty(value = "m3u8Text720p对应的m3u8内容")
    private String m3u8Text720p;

    @ApiModelProperty(value = "m3u8Text480p对应的m3u8内容")
    private String m3u8Text480p;

    @ApiModelProperty(value = "m3u8Text360p对应的m3u8内容")
    private String m3u8Text360p;

}
