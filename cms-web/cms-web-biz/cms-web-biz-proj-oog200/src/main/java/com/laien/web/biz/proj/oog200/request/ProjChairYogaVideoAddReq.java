package com.laien.web.biz.proj.oog200.request;

import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/23 15:38
 */
@Data
public class ProjChairYogaVideoAddReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "动作类型，单选，示例值 Warmup、Main、Cooldown")
    private String type;

    @ApiModelProperty(value = "锻炼部位，多选，示例值 Upper Body 、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "锻炼姿势，单选，示例值 Seated、Standing")
    private String position;

    @ApiModelProperty(value = "动作朝向，0 -> Central、1 -> Left、2 -> Right")
    private String direction;

    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "对应的right video id, 只有Left机位的才有right video id")
    private Integer rightVideoId;

    @ApiModelProperty(value = "chair yoga video 切片")
    private List<ProjChairYogaVideoSliceDetailVO> chairYogaVideoList;

}
