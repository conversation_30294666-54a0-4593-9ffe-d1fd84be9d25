package com.laien.web.biz.proj.oog200.bo;

import cn.hutool.extra.spring.SpringUtil;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import com.laien.web.biz.proj.oog200.service.IProjSoundService;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.exception.BizException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.math.BigDecimal;

/**
 * note: video200 生成系统音配置
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@ApiModel(value = "video200 生成系统音配置", description = "video200 生成系统音配置")
public class Video200SysSoundBO {

    @ApiModelProperty(value = "first")
    private String first;

    @ApiModelProperty(value = "next")
    private String next;

    @ApiModelProperty(value = "last")
    private String last;

    private String chairYogaFirst = "sys_Chair First Up_200_v7.2.0";

    private String chairYogaNext = "sys_Chair The Next Is_200_v7.2.0";

    private String chairYogaLast = "sys_Chair Last One_200_v7.2.0";

    private String ready2GoPoseAudio;
    /**  空音频数据，用于替换无法翻译的语音 */
    private String emptyAudio;

    @ApiModelProperty(notes = "时间单位为毫秒")
    private Integer videoMaskDelay = 200;

    @ApiModelProperty(value = "当音视频同步播放时，音频延迟的时间，如100ms", notes = "时间单位为毫秒")
    private Integer poseAudioSplitOne = 100;

    @ApiModelProperty(value = "倒推时间间隔")
    private Integer poseAudioSplitTwo = 4000;

    @ApiModelProperty(value = "第一个蒙层静默持续时间", notes = "时间单位为毫秒")
    private Integer firstVideoMaskDuration = 3000;
    @ApiModelProperty(value = "第一个蒙层音频播放完成后的缓冲时间")
    private Integer firstVideoMaskBuffer = 2000;

    @ApiModelProperty(value = "第二个蒙层静默持续时间", notes = "时间单位为毫秒")
    private Integer secondVideoMaskDuration = 2000;
    @ApiModelProperty(value = "第二个蒙层音频播放完成后的缓冲时间")
    private Integer secondVideoMaskBuffer = 1000;

    @ApiModelProperty(value = "第三个蒙层静默持续时间", notes = "时间单位为毫秒")
    private Integer thirdVideoMaskDuration = 2000;
    @ApiModelProperty(value = "第三个蒙层音频播放完成后的缓冲时间")
    private Integer thirdVideoMaskBuffer = 1000;

    @ApiModelProperty(notes = "时间单位为毫秒")
    private Integer splitMiddle = 500;
    private Integer splitEnd = 1000;

    private YogaAudioJsonBO getSysSoundByName(String soundName) {

        IProjSoundService soundService = SpringUtil.getBean(IProjSoundService.class);
        FileService fileService = SpringUtil.getBean(FileService.class);
        ProjSound sound = soundService.getBySoundName(soundName);
        if (sound == null) {
            throw new BizException("System sound '" + soundName + "' not find!");
        }

        String soundUrl = sound.getUrl();
        if (StringUtils.isBlank(soundUrl)) {
            throw new BizException("System sound '" + soundName + "' not set!");
        }

        Integer duration = sound.getDuration();
        return new YogaAudioJsonBO(soundName, fileService.getAbsoluteR2Url(soundUrl), soundName, new BigDecimal(duration), sound.getId(), sound.getNeedTranslation());
    }

    public YogaAudioJsonBO getAudio(String audioName) {
        return getSysSoundByName(audioName);
    }

    public YogaAudioJsonBO getFirstAudio() {
        return getSysSoundByName(first);
    }

    public YogaAudioJsonBO getNextAudio() {
        return getSysSoundByName(next);
    }

    public YogaAudioJsonBO getLastAudio() {
        return getSysSoundByName(last);
    }

}
