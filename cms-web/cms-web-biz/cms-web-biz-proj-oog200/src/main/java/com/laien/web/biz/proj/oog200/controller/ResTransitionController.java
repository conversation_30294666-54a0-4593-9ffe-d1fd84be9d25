package com.laien.web.biz.proj.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.oog200.entity.ResTransition;
import com.laien.web.biz.proj.oog200.request.ResTransitionAddReq;
import com.laien.web.biz.proj.oog200.request.ResTransitionPageReq;
import com.laien.web.biz.proj.oog200.request.ResTransitionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResTransitionDetailVO;
import com.laien.web.biz.proj.oog200.response.ResTransitionDownloadVO;
import com.laien.web.biz.proj.oog200.response.ResTransitionPageVO;
import com.laien.web.biz.proj.oog200.service.IResTransitionService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.RES_TRANSITION_FRONT;


/**
 * <p>
 * 过渡 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Api(tags = "资源管理: transition")
@RestController
@RequestMapping("/res/transition")
public class ResTransitionController extends ResponseController {

    @Resource
    private IResTransitionService resTransitionService;
    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResTransitionPageVO>> page(ResTransitionPageReq pageReq) {
        PageRes<ResTransitionPageVO> pageRes = resTransitionService.selectTransitionPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResTransitionAddReq TransitionAddReq) {
        resTransitionService.saveTransition(TransitionAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResTransitionUpdateReq TransitionUpdateReq) {
        resTransitionService.updateTransition(TransitionUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResTransitionDetailVO> detail(@PathVariable Integer id) {
        ResTransitionDetailVO detailVO = resTransitionService.getTransitionDetail(id);
        return succ(detailVO);
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(resTransitionService.importScriptByExcel(excel.getInputStream()));
    }

    @ApiOperation(value = "下载excel")
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        List<ResTransitionDownloadVO> transitionList = resTransitionService.downloadList();
        if(CollUtil.isEmpty(transitionList)){
            return;
        }
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''transition.xlsx";
        String sheetName = "transition";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ResTransitionDownloadVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(transitionList);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        //校验切片任务是否成功
        List<TaskResourceSection> taskList = taskResourceSectionService.query(RES_TRANSITION_FRONT.getTableName(), RES_TRANSITION_FRONT.getEntityFieldName(), idList);
        if (CollUtil.isEmpty(taskList)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }
        List<Integer> completedIdList = TaskResourceSectionUtil.getCompletedIdList(taskList);
        if (CollUtil.isEmpty(completedIdList)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }
        List<Integer> failedIds = idList.stream()
                .filter(id -> !completedIdList.contains(id))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(failedIds)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + failedIds);
        }
        resTransitionService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        Set<Integer> failedIdSet = resTransitionService.updateDisableByIds(idList);
        if (!failedIdSet.isEmpty()) {
            String ids = StrUtil.join(",", failedIdSet);
            return fail("The following data(id: " + ids + ") has been used");
        }
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resTransitionService.deleteByIds(idList);
        return succ();
    }

}
