/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Sets;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesAutoWorkoutBO;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesSysSoundBO;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesVideoBO;
import com.laien.web.biz.proj.oog200.bo.YogaVideoI18nBO;
import com.laien.web.biz.proj.oog200.config.WallPilatesConfig;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import com.laien.web.biz.proj.oog200.i18n.BaseSoundI18n;
import com.laien.web.biz.proj.oog200.i18n.BaseVideoI18n;
import com.laien.web.biz.proj.oog200.i18n.I18nAudioUtil;
import com.laien.web.biz.proj.oog200.service.IProjSoundService;
import com.laien.web.biz.resource.entity.ResSound;
import com.laien.web.biz.resource.entity.i18n.ResSoundI18n;
import com.laien.web.biz.resource.service.IResSoundService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.web.biz.proj.oog200.constant.WallPilatesConstant.WALL_PILATES_WORKOUT_JSON;
import static com.laien.web.biz.proj.oog200.constant.WallPilatesConstant.WALL_PILATES_WORKOUT_M3U8;

/**
 * <p>生成workout文件 多语言 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Slf4j
@Component
public class ProjWallPilatesWorkoutFileHandler {

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    private WallPilatesConfig wallPilatesConfig;

    @Resource
    private IResSoundService resSoundService;

    @Resource
    private FileService fileService;

    @Resource
    private I18nAudioUtil i18nAudioUtil;

    @Resource
    private IProjSoundService projSoundService;

    public List<ProjWallPilatesAutoWorkoutBO> generateFileWithMultiLanguage(List<ProjWallPilatesAutoWorkoutBO> workoutBoList, Integer projId) {

        // 加载项目语言
        List<String> projectLanguages = projInfoService.getLanguagesById(projId);
        List<String> excludeEnLanguages = projectLanguages.stream().filter(language -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)).collect(Collectors.toList());

        // 验证视频是否翻译完成
        List<ProjWallPilatesVideoBO> videoBOList = workoutBoList.stream().flatMap(w -> w.getVideoList().stream()).collect(Collectors.toList());
        List<BaseVideoI18n> videoI18nList = videoBOList.stream().map(video -> new BaseVideoI18n(video.getCoreVoiceConfigI18nId(), video.getId(), video.getName(), "")).collect(Collectors.toList());
        Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap = i18nAudioUtil.checkAndConvert2I18nMap(videoI18nList, excludeEnLanguages, true, false);

        // 加载音频对应的多语言版本数据
        Map<String, ProjWallPilatesSysSoundBO> allLanguageSysSoundMap = getAllLanguageSysSoundMap(projectLanguages);
        // 生成并上传
        generateAudioJsonAndUploadAndSetUrl(workoutBoList, excludeEnLanguages, videoI18nMap, allLanguageSysSoundMap);
        return workoutBoList;
    }

    private void generateAudioJsonAndUploadAndSetUrl(List<ProjWallPilatesAutoWorkoutBO> workoutBoList, List<String> excludeEnLanguages, Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap, Map<String, ProjWallPilatesSysSoundBO> allLanguageSysSoundMap) {

        // 上传任务数量
        int uploadTaskCount = workoutBoList.size() * 4 + workoutBoList.size() * excludeEnLanguages.size();
        // 创建一个任务并行上传执行器，用于管理批量上传任务
        try (UploadTaskExecutor uploadTaskExecutor = new UploadTaskExecutor(uploadTaskCount)) {
            generateAndUpload(workoutBoList, excludeEnLanguages, videoI18nMap, allLanguageSysSoundMap, uploadTaskExecutor);
            // 等待任务执行完成，文件上传线程的执行结果也可以在主线程读取到了
            uploadTaskExecutor.join();
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("wall pilates yoga workout file failed", e);
            throw new BizException("wall pilates yoga workout file failed");
        }
    }

    private void generateAndUpload(List<ProjWallPilatesAutoWorkoutBO> workoutBoList, List<String> excludeEnLanguages, Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap, Map<String, ProjWallPilatesSysSoundBO> allLanguageSysSoundMap, UploadTaskExecutor uploadTaskExecutor) {
        // 逐个 生成音频文件并上传
        workoutBoList.forEach(workoutBO -> {
            // 总时长和总卡路里
            int totalDuration = workoutBO.getVideoList().stream().mapToInt(video -> video.getFrontVideoDuration() * 3 + video.getSideVideoDuration()).sum();
            BigDecimal totalCalorie = workoutBO.getVideoList().stream().map(ProjWallPilatesVideoBO::getCalorie).reduce(BigDecimal.ZERO, BigDecimal::add);
            workoutBO.setDuration(totalDuration).setCalorie(totalCalorie);
            // 多语言音频设置默认值
            workoutBO.setMultiLanguageAudioLongJson(new ConcurrentHashMap<>());
            workoutBO.setMultiLanguageAudioShortJson(new ConcurrentHashMap<>());
            // 生成其他语言音频文件
            excludeEnLanguages.forEach(language -> {
                // 生成指定语言的音频文件
                List<ProjWallPilatesVideoBO> videoBOList = getProjWallPilatesVideoBOSWithLanguage(workoutBO, language, videoI18nMap);
                Pair<List<AudioJsonBO>, List<AudioJsonBO>> audioJsonList = createAudioJsonList(videoBOList, allLanguageSysSoundMap.get(language));
                // 上传音频并设置url
                uploadTaskExecutor.execute(() -> {
                    String audioShortJsonUrl = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonList.getLeft()), WALL_PILATES_WORKOUT_JSON).getFileRelativeUrl();
                    workoutBO.getMultiLanguageAudioShortJson().put(language, audioShortJsonUrl);
                    // 由于无法翻译guidance，这里将long json 设置为short json
                    workoutBO.getMultiLanguageAudioLongJson().put(language, audioShortJsonUrl);
                });
            });
            // 生成默认语言的音频文件
            Pair<List<AudioJsonBO>, List<AudioJsonBO>> audioJsonList = createAudioJsonList(workoutBO.getVideoList(), allLanguageSysSoundMap.get(GlobalConstant.DEFAULT_LANGUAGE));
            uploadTaskExecutor.execute(() -> {
                String audioShortJsonUrl = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonList.getLeft()), WALL_PILATES_WORKOUT_JSON).getFileRelativeUrl();
                workoutBO.setAudioShortJson(audioShortJsonUrl);
                // 将默认语言数据保存到多语言数据中去
                workoutBO.getMultiLanguageAudioShortJson().put(GlobalConstant.DEFAULT_LANGUAGE, audioShortJsonUrl);

            });
            uploadTaskExecutor.execute(() -> {
                String audioLongJsonUrl = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonList.getRight()), WALL_PILATES_WORKOUT_JSON).getFileRelativeUrl();
                workoutBO.setAudioLongJson(audioLongJsonUrl);
                // 将默认语言数据保存到多语言数据中去
                workoutBO.getMultiLanguageAudioLongJson().put(GlobalConstant.DEFAULT_LANGUAGE, audioLongJsonUrl);
            });
            // 视频文件生成
            Pair<TsTextMergeBO, List<TsMergeBO>> m3U8File = createM3U8File(workoutBO.getVideoList());
            uploadTaskExecutor.execute(() -> {
                String video2532Url = fileService.uploadMergeTSForM3U8R2(m3U8File.getRight(), WALL_PILATES_WORKOUT_M3U8).getFileRelativeUrl();
                workoutBO.setVideo2532Url(video2532Url);
            });
            uploadTaskExecutor.execute(() -> {
                String videoM3U8Url = fileService.uploadMergeTsTextForM3u8(m3U8File.getLeft(), WALL_PILATES_WORKOUT_M3U8).getFileRelativeUrl();
                workoutBO.setVideoM3u8Url(videoM3U8Url);
            });

        });
    }

    private static List<ProjWallPilatesVideoBO> getProjWallPilatesVideoBOSWithLanguage(ProjWallPilatesAutoWorkoutBO workoutBO, String language, Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap) {
        // 获取该workout下视频数据的对应语言版本
        return workoutBO.getVideoList().stream().map(video -> {
            ProjWallPilatesVideoBO bean = BeanUtil.toBean(video, ProjWallPilatesVideoBO.class);
            YogaVideoI18nBO videoI18n = videoI18nMap.get(video.getId()).get(language);
            // 只翻译name字段
            bean.setName(videoI18n.getNameScript());
            bean.setNameAudioUrl(videoI18n.getNameScriptFemale());
            bean.setNameAudioDuration(videoI18n.getNameScriptFemaleDuration());
            return bean;
        }).collect(Collectors.toList());
    }
    private Pair<TsTextMergeBO, List<TsMergeBO>> createM3U8File(List<ProjWallPilatesVideoBO> videoBOList) {
        TsTextMergeBO tsTextMergeBO = new TsTextMergeBO();
        List<TsMergeBO> tsMerge2532BO = new ArrayList<>();
        videoBOList.forEach(video -> {
            tsTextMergeBO.addM3u8Text(video.getFrontM3u8Text2k(), video.getFrontM3u8Text1080p(), video.getFrontM3u8Text720p(), video.getFrontM3u8Text480p(), video.getFrontM3u8Text360p());
            tsTextMergeBO.addM3u8Text(video.getFrontM3u8Text2k(), video.getFrontM3u8Text1080p(), video.getFrontM3u8Text720p(), video.getFrontM3u8Text480p(), video.getFrontM3u8Text360p());
            tsTextMergeBO.addM3u8Text(video.getSideM3u8Text2k(), video.getSideM3u8Text1080p(), video.getSideM3u8Text720p(), video.getSideM3u8Text480p(), video.getSideM3u8Text360p());
            tsTextMergeBO.addM3u8Text(video.getFrontM3u8Text2k(), video.getFrontM3u8Text1080p(), video.getFrontM3u8Text720p(), video.getFrontM3u8Text480p(), video.getFrontM3u8Text360p());

            tsMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(video.getFrontVideoUrl()), video.getFrontVideoDuration()));
            tsMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(video.getFrontVideoUrl()), video.getFrontVideoDuration()));
            tsMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(video.getSideVideoUrl()), video.getSideVideoDuration()));
            tsMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(video.getFrontVideoUrl()), video.getFrontVideoDuration()));
        });

        return Pair.of(tsTextMergeBO, tsMerge2532BO);
    }


    private Map<String, ProjWallPilatesSysSoundBO> getAllLanguageSysSoundMap(List<String> languageList) {

        Set<String> soundNames = Sets.newHashSet(wallPilatesConfig.getFirst(), wallPilatesConfig.getNext(), wallPilatesConfig.getLast(), wallPilatesConfig.getStart(), wallPilatesConfig.getRest());
        List<ProjSound> soundList = projSoundService.listBySoundNames(soundNames);
        Map<String, ProjSound> soundNameMap = soundList.stream().collect(Collectors.toMap(ProjSound::getSoundName, Function.identity(), (k1, k2) -> k1));

        for (String soundName : soundNames) {
            if (!soundNameMap.containsKey(soundName)) {
                throw new BizException("System sound '" + soundName + "' not find!");
            }
            ProjSound sound = soundNameMap.get(soundName);
            if (org.springframework.util.StringUtils.isEmpty(sound.getUrl())) {
                throw new BizException("System sound '" + soundName + "' sound url is null!");
            }
        }

        List<BaseSoundI18n> soundI18nList = soundList.stream().map(BaseSoundI18n::new).collect(Collectors.toList());
        Map<String, Map<String, AudioJsonBO>> soundI18nMap = i18nAudioUtil.convertSound2I18nMap(soundI18nList, languageList);
        Map<String, ProjWallPilatesSysSoundBO> sysSoundMap = new HashMap<>();

        for (String language : languageList) {
            ProjWallPilatesSysSoundBO wrapper = new ProjWallPilatesSysSoundBO();
            sysSoundMap.put(language, wrapper);
            wrapper.setFirstAudio(soundI18nMap.get(wallPilatesConfig.getFirst()).get(language))
                    .setNextAudio(soundI18nMap.get(wallPilatesConfig.getNext()).get(language))
                    .setLastAudio(soundI18nMap.get(wallPilatesConfig.getLast()).get(language))
                    .setStartAudio(soundI18nMap.get(wallPilatesConfig.getStart()).get(language))
                    .setRestAudio(soundI18nMap.get(wallPilatesConfig.getRest()).get(language));
        }

        return sysSoundMap;
    }

    private ProjWallPilatesSysSoundBO getProjWallPilatesSysSoundBOWithLanguage(String language, Map<Integer, Map<String, ResSoundI18n>> soundI18nMap, Map<String, Integer> soundNameIdMap,ResSound startAudio) {

        ProjWallPilatesSysSoundBO sysSoundBO = new ProjWallPilatesSysSoundBO();

        ResSoundI18n first = soundI18nMap.get(soundNameIdMap.get(wallPilatesConfig.getFirst())).get(language);
        ResSoundI18n next = soundI18nMap.get(soundNameIdMap.get(wallPilatesConfig.getNext())).get(language);
        ResSoundI18n last = soundI18nMap.get(soundNameIdMap.get(wallPilatesConfig.getLast())).get(language);
        ResSoundI18n rest = soundI18nMap.get(soundNameIdMap.get(wallPilatesConfig.getRest())).get(language);

        sysSoundBO.setFirstAudio(covert2AudioJsonBO(first, wallPilatesConfig.getFirst()));
        sysSoundBO.setNextAudio(covert2AudioJsonBO(next, wallPilatesConfig.getNext()));
        sysSoundBO.setLastAudio(covert2AudioJsonBO(last, wallPilatesConfig.getLast()));
        sysSoundBO.setRestAudio(covert2AudioJsonBO(rest, wallPilatesConfig.getRest()));

        // start 使用默认语言音频，不使用翻译的
        AudioJsonBO start = covert2AudioJsonBO(startAudio);
        sysSoundBO.setStartAudio(start);

        return sysSoundBO;

    }

    private AudioJsonBO covert2AudioJsonBO(ResSound resSound) {
        String soundUrl = resSound.getFemaleUrl();
        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        String url = fileService.getAbsoluteR2Url(soundUrl);
        return new AudioJsonBO(resSound.getSoundName(), url, soundName, NumberUtil.toBigDecimal(resSound.getFemaleDuration()));
    }

    private AudioJsonBO covert2AudioJsonBO(ResSoundI18n resSoundI18n, String soundName) {
        String soundUrl = resSoundI18n.getSoundScriptFemale();
        return new AudioJsonBO(soundName, fileService.getAbsoluteR2Url(soundUrl), FireBaseUrlSubUtils.getFileName(soundUrl), NumberUtil.toBigDecimal(resSoundI18n.getSoundScriptFemaleDuration()));
    }

    private ProjWallPilatesSysSoundBO getDefaultLanguageProjWallPilatesSysSoundBO(List<ResSound> sysSounds) {
        // 系统音获取并验证是否完整(系统音不区分语言)
        Set<String> soundNames = Sets.newHashSet(wallPilatesConfig.getFirst(), wallPilatesConfig.getNext(), wallPilatesConfig.getLast(), wallPilatesConfig.getStart(), wallPilatesConfig.getRest());
        Map<String, ResSound> nameSoundMap = sysSounds.stream().collect(Collectors.toMap(ResSound::getSoundName, Function.identity(), (v1, v2) -> v1));
        // 数据校验
        soundNames.forEach(name -> {
            if (!nameSoundMap.containsKey(name)) {
                throw new BizException("System sound '" + name + "' not find!");
            }
            if (StringUtils.isBlank(nameSoundMap.get(name).getFemaleUrl())) {
                throw new BizException("System sound '" + name + "' not set!");
            }
        });
        ProjWallPilatesSysSoundBO soundBO = new ProjWallPilatesSysSoundBO();
        soundBO.setFirstAudio(createAudioJsonBO(nameSoundMap, wallPilatesConfig.getFirst()));
        soundBO.setNextAudio(createAudioJsonBO(nameSoundMap, wallPilatesConfig.getNext()));
        soundBO.setLastAudio(createAudioJsonBO(nameSoundMap, wallPilatesConfig.getLast()));
        soundBO.setStartAudio(createAudioJsonBO(nameSoundMap, wallPilatesConfig.getStart()));
        soundBO.setRestAudio(createAudioJsonBO(nameSoundMap, wallPilatesConfig.getRest()));

        return soundBO;
    }

    private AudioJsonBO createAudioJsonBO(Map<String, ResSound> nameSoundMap, String sysAudioName) {

        ResSound resSound = nameSoundMap.get(sysAudioName);
        String soundUrl = resSound.getFemaleUrl();
        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        String url = fileService.getAbsoluteR2Url(soundUrl);
        return new AudioJsonBO(sysAudioName, url, soundName, NumberUtil.toBigDecimal(resSound.getFemaleDuration()));
    }

    /**
     * <p>给定video list 和 sys audio 生成 long audio 和 short audio</p>
     *
     * @param videoList  视频列表
     * @param sysSoundBO 给定语言系统音
     * @return org.apache.commons.lang3.tuple.Pair<java.util.List < com.laien.web.common.file.bo.AudioJsonBO>,java.util.List<com.laien.web.common.file.bo.AudioJsonBO>>
     * <AUTHOR>
     * @date 2025/3/31 12:00
     */
    private Pair<List<AudioJsonBO>, List<AudioJsonBO>> createAudioJsonList(List<ProjWallPilatesVideoBO> videoList, ProjWallPilatesSysSoundBO sysSoundBO) {
        List<AudioJsonBO> shortAudioJsonList = new ArrayList<>();
        List<AudioJsonBO> longAudioJsonList = new ArrayList<>();

        // 上一个音频结束时间 单位毫秒
        int lastAudioEndTime = 0;
        for (int i = 0; i < videoList.size(); i++) {

            ProjWallPilatesVideoBO video = videoList.get(i);
            // 组装audio json
            int firstSysSoundPlayTime = lastAudioEndTime + GlobalConstant.MILLIS_PER_SECOND;
            // 第一部分，视频开始时播放 first/next/last 系统音
            AudioJsonBO firstAudioToAdd = createFirstAudioToAdd(sysSoundBO, firstSysSoundPlayTime, i, videoList.size());
            shortAudioJsonList.add(firstAudioToAdd);
            longAudioJsonList.add(firstAudioToAdd);
            // 第二部分 name audio first audio 播放时间向后后500ms播放
            AudioJsonBO nameAudioJsonBO = createNameAudioJsonBO(sysSoundBO, video, firstSysSoundPlayTime);
            shortAudioJsonList.add(nameAudioJsonBO);
            longAudioJsonList.add(nameAudioJsonBO);
            // 第三部分 start audio star audio 播放结束时间为当前动作第一个正机位片段结束时间，即开始播放时间为第一个正机位播放结束时间减去guidance audio时长
            int startAudioPlayTime = lastAudioEndTime + video.getFrontVideoDuration() - sysSoundBO.getStartAudio().getTime().intValue();
            AudioJsonBO startAudioJsonBO = createAudioJsonBOWithPlayTime(sysSoundBO.getStartAudio(), startAudioPlayTime);
            shortAudioJsonList.add(startAudioJsonBO);
            longAudioJsonList.add(startAudioJsonBO);
            // 第四部分 guidance audio 播放开始时间为当前动作第一个正机位片段结束时间之后2000ms,只有longAudioJsonList需要添加
            AudioJsonBO guidanceAudioJsonBO = createGuidanceAudioJsonBO(lastAudioEndTime, video);
            longAudioJsonList.add(guidanceAudioJsonBO);
            // 第五部分 rest audio 播放结束时间为 当前动作播放结束时间前 1000ms
            int restAudioPlayTime = lastAudioEndTime + video.getFrontVideoDuration() * GlobalConstant.THREE + video.getSideVideoDuration() - sysSoundBO.getRestAudio().getTime().intValue() - GlobalConstant.MILLIS_PER_SECOND;
            AudioJsonBO restAudioJsonBO = createAudioJsonBOWithPlayTime(sysSoundBO.getRestAudio(), restAudioPlayTime);
            shortAudioJsonList.add(restAudioJsonBO);
            longAudioJsonList.add(restAudioJsonBO);
            // 更新上个视频结束时间
            lastAudioEndTime = lastAudioEndTime + video.getFrontVideoDuration() * GlobalConstant.THREE + video.getSideVideoDuration();

        }
        return Pair.of(shortAudioJsonList, longAudioJsonList);
    }

    private AudioJsonBO createFirstAudioToAdd(ProjWallPilatesSysSoundBO sysSoundBO, int playTime, int index, int size) {
        AudioJsonBO audioJsonBO;
        if (index == GlobalConstant.ZERO) {
            audioJsonBO = createAudioJsonBOWithPlayTime(sysSoundBO.getFirstAudio(), playTime);
        } else if (index == size - 1) {
            audioJsonBO = createAudioJsonBOWithPlayTime(sysSoundBO.getLastAudio(), playTime);
        } else {
            audioJsonBO = createAudioJsonBOWithPlayTime(sysSoundBO.getNextAudio(), playTime);
        }
        return audioJsonBO;
    }

    private AudioJsonBO createGuidanceAudioJsonBO(int lastAudioEndTime, ProjWallPilatesVideoBO video) {
        int guidanceAudioPlayTime = lastAudioEndTime + video.getFrontVideoDuration() + 2 * GlobalConstant.MILLIS_PER_SECOND;
        String guidanceNameId = "guidanceAudio-" + video.getId();
        String guidanceAudioUrl = fileService.getAbsoluteR2Url(video.getGuidanceAudioUrl());
        String guidanceAudioName = FireBaseUrlSubUtils.getFileName(guidanceAudioUrl);
        AudioJsonBO guidanceAudio = new AudioJsonBO(guidanceNameId, guidanceAudioUrl, guidanceAudioName, NumberUtil.toBigDecimal(video.getGuidanceAudioDuration()));
        return createAudioJsonBOWithPlayTime(guidanceAudio, guidanceAudioPlayTime);
    }

    private AudioJsonBO createNameAudioJsonBO(ProjWallPilatesSysSoundBO sysSoundBO, ProjWallPilatesVideoBO video, int firstSysSoundPlayTime) {
        String nameId = "nameAudio-" + video.getId();
        String nameAudioUrl = fileService.getAbsoluteR2Url(video.getNameAudioUrl());
        String nameAudioName = FireBaseUrlSubUtils.getFileName(nameAudioUrl);
        AudioJsonBO nameAudio = new AudioJsonBO(nameId, nameAudioUrl, nameAudioName, NumberUtil.toBigDecimal(video.getNameAudioDuration()));
        int nameSoundPlayTime = firstSysSoundPlayTime + sysSoundBO.getFirstAudio().getTime().intValue() + 500;
        return createAudioJsonBOWithPlayTime(nameAudio, nameSoundPlayTime);
    }

    /**
     * <p>按指定播放开始播放时间创建audio json</p>
     *
     * @param sysAudioJson 待添加系统音
     * @param playTime     开始播放时间 单位毫秒
     * @return com.laien.web.common.file.bo.AudioJsonBO
     * <AUTHOR>
     * @date 2025/3/26 13:47
     */
    private AudioJsonBO createAudioJsonBOWithPlayTime(AudioJsonBO sysAudioJson, Integer playTime) {

        return new AudioJsonBO(sysAudioJson.getId()
                , sysAudioJson.getUrl()
                , sysAudioJson.getName()
                // 播放时间转为秒
                , new BigDecimal(playTime).divide(new BigDecimal("1000.0"), 1, RoundingMode.HALF_UP));
    }


}