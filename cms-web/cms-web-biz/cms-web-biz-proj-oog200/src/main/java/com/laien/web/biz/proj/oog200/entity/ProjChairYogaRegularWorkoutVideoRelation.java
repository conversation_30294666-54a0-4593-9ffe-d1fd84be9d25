package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author:  hhl
 * Date:  2024/9/27 18:14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjChairYogaRegularWorkoutVideoRelation extends BaseModel {

    @ApiModelProperty(value = "workout id")
    private Integer projChairYogaRegularWorkoutId;

    @ApiModelProperty(value = "video id")
    private Integer projChairYogaVideoId;

    @ApiModelProperty(value = "真实的视频播放时长")
    private Integer videoDuration;

}
