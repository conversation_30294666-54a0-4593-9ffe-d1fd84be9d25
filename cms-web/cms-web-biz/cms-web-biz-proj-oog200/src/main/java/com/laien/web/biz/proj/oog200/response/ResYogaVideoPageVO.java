package com.laien.web.biz.proj.oog200.response;

import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * note: yoga video分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video分页", description = "yoga video分页")
public class ResYogaVideoPageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "English Voice Name")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "动作类型 数组 Start、Main、End、CoolDown")
    private String type;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "动作体位 Steated、Standing、Prone、Supine、Arm & Leg Support")
    private String position;

    @ApiModelProperty(value = "瑜伽派别 数组 Strength、Balancing、Relaxation、Flexbility")
    private String focus;

    @ApiModelProperty(value = "特殊人群不可使用的 数组 Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String specialLimit;

    @ApiModelProperty(value = "动作时长 单位毫秒")
    private Integer poseTime;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "关联的pose video信息")
    private ProjBaseDetailVO relationPoseVideo;

    @ApiModelProperty(value = "pose video id")
    private Integer relationPoseVideoId;

    @ApiModelProperty(value = "正机位切片任务状态")
    private TaskResourceSectionStatusEnums frontTaskStatus;

    @ApiModelProperty(value = "侧机位切片任务状态")
    private TaskResourceSectionStatusEnums sideTaskStatus;
}
