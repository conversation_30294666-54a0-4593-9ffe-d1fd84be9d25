package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * note: Video Generate Video 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Video Generate Video 分页", description = "Video Generate Video 分页")
public class ProjVideoGenerateVideoSliceVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频类型")
    private String videoType;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "focus")
    private String focus;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "数据版本")
    private Integer dataVersion;

}
