package com.laien.web.biz.proj.oog200.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import com.laien.web.biz.proj.oog200.request.ProjSoundAddReq;
import com.laien.web.biz.proj.oog200.request.ProjSoundUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjSoundDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjSoundPageVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSoundMapStruct {

    List<ProjSoundPageVO> toPageList(List<ProjSound> entities);

    /**
     * 添加 request 转 entity
     * @param req
     * @return
     */
    ProjSound toEntity(ProjSoundAddReq req);

    /**
     * 修改 request 转 entity
     * @param req
     * @return
     */
    ProjSound toEntity(ProjSoundUpdateReq req);

    /**
     * entity 转详情
     * @param entity
     * @return
     */
    ProjSoundDetailVO toDetailVO(ProjSound entity);
}