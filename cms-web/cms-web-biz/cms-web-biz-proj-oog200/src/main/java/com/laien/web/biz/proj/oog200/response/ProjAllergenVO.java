package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * allergen
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjAllergen对象", description="allergen")
public class ProjAllergenVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "过敏源名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
