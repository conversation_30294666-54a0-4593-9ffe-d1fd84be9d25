package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjTemplateRule;
import com.laien.web.biz.proj.oog200.mapper.ProjTemplateRuleMapper;
import com.laien.web.biz.proj.oog200.service.IProjTemplateRuleService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * template rule 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Service
public class ProjTemplateRuleServiceImpl extends ServiceImpl<ProjTemplateRuleMapper, ProjTemplateRule> implements IProjTemplateRuleService {

}
