package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.core.compression.constant.TaskConstant;
import com.laien.web.biz.proj.oog200.entity.ProjTemplateTask;
import com.laien.web.biz.proj.oog200.mapper.ProjTemplateTaskMapper;
import com.laien.web.biz.proj.oog200.service.IProjTemplateTaskService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * template task 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Service
public class ProjTemplateTaskServiceImpl extends ServiceImpl<ProjTemplateTaskMapper, ProjTemplateTask> implements IProjTemplateTaskService {

    @Override
    public ProjTemplateTask saveStartTemplateTask(Integer templateId, Integer generateCount, Integer cleanUp) {
        ProjTemplateTask templateTask = new ProjTemplateTask();
        templateTask.setTemplateId(templateId);
        templateTask.setGenerateCount(generateCount);
        templateTask.setCleanUp(cleanUp);
        templateTask.setTaskStatus(TaskConstant.TASK_STATUS_PROCESS);
        this.save(templateTask);
        return templateTask;
    }

    @Override
    public void updateTaskStatusById(Integer id, Integer taskStatus) {
        ProjTemplateTask templateTask = new ProjTemplateTask();
        templateTask.setId(id);
        templateTask.setTaskStatus(taskStatus);
        this.updateById(templateTask);
    }

    @Override
    public List<ProjTemplateTask> selectLastTask(List<Integer> templateIdList) {
        return this.baseMapper.selectLastTask(templateIdList);
    }

}
