package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2024/9/24 17:29
 */
@Data
public class ProjChairYogaAutoWorkout extends BaseModel {

    @ApiModelProperty(value = "生成模版id")
    private Integer projYogaAutoWorkoutTemplateId;

    @ApiModelProperty(value = "生成任务id")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "锻炼部位，示例值 Upper Body 、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "难度，可选值有 Newbie,Beginner,Intermediate,Advanced")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "video的 2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称")
    private String audioShortJson;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
