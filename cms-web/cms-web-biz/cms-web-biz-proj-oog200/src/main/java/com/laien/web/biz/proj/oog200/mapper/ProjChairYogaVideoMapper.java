package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/23 17:32
 */
public interface ProjChairYogaVideoMapper extends BaseMapper<ProjChairYogaVideo> {

    @Select("select distinct chair.* \n" +
            "from proj_chair_yoga_video chair \n" +
            "inner join proj_chair_yoga_video_slice slice on chair.id = slice.proj_chair_yoga_video_id \n" +
            "where chair.status = 1 and chair.del_flag =0 and slice.del_flag = 0 \n")
    List<ProjChairYogaVideo> listValid4Generate();

    List<ProjChairYogaVideo> listByIds(@Param("videoIds") Collection<Integer> videoIds);
}
