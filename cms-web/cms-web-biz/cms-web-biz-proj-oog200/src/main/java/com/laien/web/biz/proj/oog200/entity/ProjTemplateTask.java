package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * template task
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjTemplateTask对象", description="template task")
public class ProjTemplateTask extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "template id")
    private Integer templateId;

    @ApiModelProperty(value = "生成数量")
    private Integer generateCount;

    @ApiModelProperty(value = "是否需要清理已生成的video 0 否，1是")
    private Integer cleanUp;

    @ApiModelProperty(value = "任务状态 0待处理 1处理中 2成功 3失败")
    private Integer taskStatus;


}
