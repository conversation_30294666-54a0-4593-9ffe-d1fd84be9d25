package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjDishStepTip;
import com.laien.web.biz.proj.oog200.response.ProjDishStepTipVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Dish step tip 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface IProjDishStepTipService extends IService<ProjDishStepTip> {

    void delete(Collection<Integer> dishIdCollection);

    List<ProjDishStepTipVO> query(Integer dishId);

}
