package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: template 列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template rule 列表", description = "template rule 列表")
public class ProjTemplateRuleVO {

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频type")
    private String videoType;

}
