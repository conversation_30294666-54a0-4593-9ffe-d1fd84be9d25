package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseGroup对象", description="proj yoga pose grouping")
public class ProjYogaPoseGroupListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "eventName")
    private String eventName;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "分组用途，例如today pose、training path")
    private String type;

    @ApiModelProperty(value = "group 彩色封面")
    private String groupImgLightUrl;

    @ApiModelProperty(value = "group 黑色封面")
    private String groupImgDarkUrl;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "workoutNumber")
    private Integer workoutNumber;

    @ApiModelProperty(value = "workoutEnabledNumber")
    private Integer workoutEnabledNumber;

}
