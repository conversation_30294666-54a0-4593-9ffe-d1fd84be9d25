package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.laien.common.oog200.enums.GoalEnum;
import com.laien.common.oog200.enums.ResUpdateStatusEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog200.bo.*;
import com.laien.web.biz.proj.oog200.config.Oog200BizConfig;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.web.biz.proj.oog200.i18n.BaseSoundI18n;
import com.laien.web.biz.proj.oog200.i18n.BaseVideoI18n;
import com.laien.web.biz.proj.oog200.i18n.I18nAudioUtil;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaAutoWorkoutMapper;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjClassicYogaAutoWorkoutDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempWorkoutPageVO;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.biz.resource.entity.i18n.ResSoundI18n;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.async.IAsyncProcess;
import com.laien.web.frame.async.service.IAsyncService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.validation.Group;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * oog200 workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Slf4j
@Service
public class ProjYogaAutoWorkoutServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutMapper, ProjYogaAutoWorkout> implements IProjYogaAutoWorkoutService {

    @Resource
    private IResYogaVideoService resYogaVideoService;

    @Resource
    private IResYogaVideoConnectionService resYogaVideoConnectionService;

    @Resource
    private IProjYogaAutoWorkoutVideoRelationService projYogaAutoWorkoutVideoRelationService;

    @Resource
    private IProjYogaAutoWorkoutTemplateService projYogaAutoWorkoutTemplateService;

    @Resource
    private IProjYogaAutoWorkoutTaskService projYogaAutoWorkoutTaskService;

    @Resource
    private IResTransitionService resTransitionService;

    @Resource
    private FileService fileService;

    @Resource
    private Oog200BizConfig oog200BizConfig;

    @Resource
    private IAsyncService asyncService;

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    private IProjYogaAutoWorkoutAudioI18nService projYogaAutoWorkoutAudioI18nService;

    @Resource
    private IProjYogaAutoWorkoutService autoWorkoutService;

    @Resource
    private IProjAutoWorkoutBasicInfoWorkoutRelationService basicInfoWorkoutRelationService;

    @Resource
    private Validator validator;

    @Resource
    private I18nAudioUtil i18nAudioUtil;

    @Resource
    private IProjSoundService projSoundService;

    private static final int NUMBER_ONE = 1;
    public static final String SECOND_MILL = "1000.0";

    @Override
    public Map<Integer, Integer> listWorkoutNum(Collection<Integer> templateIds) {
        Map<Integer, Integer> result = MapUtil.newHashMap();
        Optional.ofNullable(templateIds).filter(CollUtil::isNotEmpty).ifPresent(ids -> {
            QueryWrapper<ProjYogaAutoWorkout> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("proj_yoga_auto_workout_template_id as templateId,count(id) as workoutNum");
            LambdaQueryWrapper<ProjYogaAutoWorkout> lambda = queryWrapper.lambda();
            lambda.in(ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIds);
            lambda.groupBy(ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId);
            List<Map<String, Object>> maps = listMaps(queryWrapper);
            maps.stream().forEach(m -> {
                result.put(Integer.parseInt(m.get("templateId") + ""), Integer.parseInt(m.get("workoutNum") + ""));
            });
        });
        return result;
    }

    @Override
    public PageRes<ProjYogaAutoWorkoutTempWorkoutPageVO> page(ProjYogaAutoWorkoutTempWorkoutPageReq pageReq, Integer templateId) {
        Integer templateStatus = pageReq.getTemplateStatus();
        GoalEnum goal = pageReq.getGoal();
        Set<Integer> templateIdSet = null;
        if (null == templateId) {
            List<ProjYogaAutoWorkoutTemplate> templateList = projYogaAutoWorkoutTemplateService.find(templateStatus);
            templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        }
        LambdaQueryWrapper<ProjYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(templateIdSet), ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIdSet);
        queryWrapper.eq(null != templateId, ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateId);
        queryWrapper.eq(pageReq.getId() != null, ProjYogaAutoWorkout::getId, pageReq.getId());
        queryWrapper.eq(pageReq.getProjYogaAutoWorkoutTaskId() != null, ProjYogaAutoWorkout::getProjYogaAutoWorkoutTaskId, pageReq.getProjYogaAutoWorkoutTaskId());
        queryWrapper.eq(StrUtil.isNotBlank(pageReq.getDifficulty()), ProjYogaAutoWorkout::getDifficulty, pageReq.getDifficulty());
        queryWrapper.eq(pageReq.getStatus() != null, ProjYogaAutoWorkout::getStatus, pageReq.getStatus());
        queryWrapper.eq(Objects.nonNull(pageReq.getUpdateStatus()), ProjYogaAutoWorkout::getUpdateStatus, pageReq.getUpdateStatus());
        queryWrapper.eq(null != goal, ProjYogaAutoWorkout::getGoal, goal);

        if (pageReq.getTime() != null) {
            switch (pageReq.getTime()) {
                case GlobalConstant.ONE:
                    queryWrapper.lt(ProjYogaAutoWorkout::getDuration, 1000 * 60 * 10);
                    break;
                case GlobalConstant.TWO:
                    queryWrapper.between(ProjYogaAutoWorkout::getDuration, 1000 * 60 * 10, 1000 * 60 * 20);
                    break;
                case GlobalConstant.THREE:
                    queryWrapper.gt(ProjYogaAutoWorkout::getDuration, 1000 * 60 * 20);
                    break;
            }
        }
        queryWrapper.orderByDesc(ProjYogaAutoWorkout::getId);
        Page<ProjYogaAutoWorkout> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        page(page, queryWrapper);
        List<ProjYogaAutoWorkoutTempWorkoutPageVO> resultRecords = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(page.getRecords())) {
            for (ProjYogaAutoWorkout record : page.getRecords()) {
                ProjYogaAutoWorkoutTempWorkoutPageVO pageVO = new ProjYogaAutoWorkoutTempWorkoutPageVO();
                BeanUtils.copyProperties(record, pageVO);

                Integer updateStatus = pageVO.getUpdateStatus();
                pageVO.setDisplayUpdateStatus(ResUpdateStatusEnum.SingletonHolder.getStatusMap().get(updateStatus));
                resultRecords.add(pageVO);
            }
            //查询任务的理想时长
            Set<Integer> taskIds = resultRecords.stream().map(ProjYogaAutoWorkoutTempWorkoutPageVO::getProjYogaAutoWorkoutTaskId).collect(Collectors.toSet());
            Collection<ProjYogaAutoWorkoutTask> projYogaAutoWorkoutTasks = projYogaAutoWorkoutTaskService.listByIds(taskIds);
            Map<Integer, ProjYogaAutoWorkoutTask> taskMaps = projYogaAutoWorkoutTasks.stream().collect(Collectors.toMap(ProjYogaAutoWorkoutTask::getId, t -> t));
            for (ProjYogaAutoWorkoutTempWorkoutPageVO resultRecord : resultRecords) {
                Optional.ofNullable(taskMaps.get(resultRecord.getProjYogaAutoWorkoutTaskId())).ifPresent(task -> {
                    resultRecord.setExpectTime(task.getExpectTime() * 60 * 1000);
                    resultRecord.setCreateUser(task.getCreateUser());
                    resultRecord.setGenerateNum(task.getGenerateNum());
                    resultRecord.setCleanUp(task.getCleanUp());
                });
            }
        }
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), resultRecords);
    }

    @Override
    public PageRes<ProjYogaAutoWorkoutTempVideoPageVO> pageVideo(PageReq pageReq, Integer workoutId) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutVideoRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAutoWorkoutVideoRelation::getProjYogaAutoWorkoutId, workoutId);
        queryWrapper.orderByAsc(ProjYogaAutoWorkoutVideoRelation::getId);
        Page<ProjYogaAutoWorkoutVideoRelation> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        projYogaAutoWorkoutVideoRelationService.page(page, queryWrapper);
        List<ProjYogaAutoWorkoutTempVideoPageVO> resultRecords = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(page.getRecords())) {
            Set<Integer> videoIds = page.getRecords().stream().map(ProjYogaAutoWorkoutVideoRelation::getResYogaVideoId).collect(Collectors.toSet());
            Map<Integer, ResYogaVideo> videoMapById = resYogaVideoService.listAllByIds(videoIds).stream().collect(Collectors.toMap(ResYogaVideo::getId, t -> t));
            for (ProjYogaAutoWorkoutVideoRelation record : page.getRecords()) {
                ResYogaVideo resYogaVideo = videoMapById.get(record.getResYogaVideoId());
                ProjYogaAutoWorkoutTempVideoPageVO pageVO = new ProjYogaAutoWorkoutTempVideoPageVO();
                BeanUtils.copyProperties(resYogaVideo, pageVO);
                resultRecords.add(pageVO);
            }
        }
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), resultRecords);
    }

    @Override
    public Collection<ProjYogaAutoWorkout> listByWorkoutIds(Collection<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return Collections.emptyList();
        }

        return listByIds(workoutIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByTemplate(Collection<Integer> templateIds) {
        LambdaQueryWrapper<ProjYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ProjYogaAutoWorkout::getId);
        queryWrapper.in(ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIds);
        Optional.ofNullable(list(queryWrapper)).filter(CollUtil::isNotEmpty).ifPresent(workouts -> {
            Set<Integer> workoutIds = workouts.stream().map(ProjYogaAutoWorkout::getId).collect(Collectors.toSet());
            deleteByIds(workoutIds);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(Collection<Integer> workoutIds) {
        if (CollUtil.isNotEmpty(workoutIds)) {
            LambdaQueryWrapper<ProjYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(ProjYogaAutoWorkout::getId);
            queryWrapper.in(ProjYogaAutoWorkout::getId, workoutIds);
            queryWrapper.ne(ProjYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
            Set<Integer> workoutSet = list(queryWrapper).stream().map(ProjYogaAutoWorkout::getId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(workoutSet)) {

                List<ProjAutoWorkoutBasicInfoWorkoutRelation> workoutRelations = basicInfoWorkoutRelationService.listByPlanTypeAndWorkoutIds(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA, workoutIds);
                if (!CollectionUtils.isEmpty(workoutRelations)) {
                    List<String> errorMessages = workoutRelations.stream().map(this::convertWorkoutBasicInfo2String).collect(Collectors.toList());
                    throw new BizException(String.format("This workout cannot be deleted because it is used in the following images: \n%s", String.join("\n", errorMessages)));
                }

                removeByIds(workoutSet);
                projYogaAutoWorkoutVideoRelationService.removeByWorkout(workoutSet);
            }
        }
    }

    private String convertWorkoutBasicInfo2String(ProjAutoWorkoutBasicInfoWorkoutRelation workoutRelation) {

        return String.format("%s(%d)", workoutRelation.getProjAutoWorkoutBasicInfoName(), workoutRelation.getProjAutoWorkoutBasicInfoId());
    }

    @Override
    public void updateEnableByIds(Collection<Integer> workoutIds) {
        if (CollUtil.isNotEmpty(workoutIds)) {
            LambdaUpdateWrapper<ProjYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(ProjYogaAutoWorkout::getId, workoutIds);
            updateWrapper.set(ProjYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
            update(updateWrapper);
        }
    }

    @Override
    public void updateDisableByIds(Collection<Integer> workoutIds) {
        if (CollUtil.isNotEmpty(workoutIds)) {

            List<ProjAutoWorkoutBasicInfoWorkoutRelation> workoutRelations = basicInfoWorkoutRelationService.listByPlanTypeAndWorkoutIds(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA, workoutIds);
            if (!CollectionUtils.isEmpty(workoutRelations)) {
                List<String> errorMessages = workoutRelations.stream().map(this::convertWorkoutBasicInfo2String).collect(Collectors.toList());
                throw new BizException(String.format("This workout cannot be disabled because it is used in the following images: \n%s", String.join("\n", errorMessages)));
            }

            LambdaUpdateWrapper<ProjYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(ProjYogaAutoWorkout::getId, workoutIds);
            updateWrapper.set(ProjYogaAutoWorkout::getStatus, GlobalConstant.STATUS_DISABLE);
            update(updateWrapper);
        }
    }

    @Override
    public ProjYogaAutoWorkoutDetailVO detail(Integer workoutId) {
        AtomicReference<ProjYogaAutoWorkoutDetailVO> result = new AtomicReference<>();
        Optional.ofNullable(getById(workoutId)).ifPresent(workout -> {
            result.set(new ProjYogaAutoWorkoutDetailVO());
            result.get().setId(workout.getId());
            result.get().setDifficulty(workout.getDifficulty());
            PageReq pageReq = new PageReq();
            pageReq.setPageNum(1);
            pageReq.setPageSize(Integer.MAX_VALUE);
            PageRes<ProjYogaAutoWorkoutTempVideoPageVO> pageRes = projYogaAutoWorkoutTemplateService.pageVideo(pageReq, workout.getId());
            result.get().setVideoList(pageRes.getList());
            result.get().setStatus(workout.getStatus());
            result.get().setProjYogaAutoTemplateId(workout.getProjYogaAutoWorkoutTemplateId());
            result.get().setDuration(workout.getDuration());
            result.get().setGoal(workout.getGoal());
        });
        return result.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjYogaAutoWorkoutUpdateReq projYogaAutoWorkoutUpdateReq, Integer projId) {
        Integer workoutId = projYogaAutoWorkoutUpdateReq.getId();
        Optional.ofNullable(getById(workoutId)).ifPresent(workout -> {
            Collection<ResYogaVideo> allVideos = resYogaVideoService.listByIds(Sets.newHashSet(projYogaAutoWorkoutUpdateReq.getVideoIdList()));
            if (CollUtil.isNotEmpty(allVideos)) {
                check(projYogaAutoWorkoutUpdateReq, allVideos);
            }
            GenerateInfo generateInfo = getGenerateInfo(projYogaAutoWorkoutUpdateReq.getVideoIdList(), projId);
            //删除旧的video关系表数据
            projYogaAutoWorkoutVideoRelationService.removeByWorkout(CollUtil.newHashSet(projYogaAutoWorkoutUpdateReq.getId()));
            //更新内容
            Map<String, String> longJsonUrlMap = generateInfo.getLongJsonUrlMap();
            Map<String, String> shortJsonUrlMap = generateInfo.getShortJsonUrlMap();
            LambdaUpdateWrapper<ProjYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProjYogaAutoWorkout::getId, projYogaAutoWorkoutUpdateReq.getId());
            updateWrapper.set(ProjYogaAutoWorkout::getDifficulty, projYogaAutoWorkoutUpdateReq.getDifficulty());
            updateWrapper.set(ProjYogaAutoWorkout::getVideoM3u8Url, generateInfo.getVideoM3u8Url());
            updateWrapper.set(ProjYogaAutoWorkout::getVideo2532Url, generateInfo.getVideo2532Url());
            updateWrapper.set(ProjYogaAutoWorkout::getCalorie, generateInfo.getCalorie());
            updateWrapper.set(ProjYogaAutoWorkout::getAudioLongJson, longJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE));
            updateWrapper.set(ProjYogaAutoWorkout::getUpdateStatus, ResUpdateStatusEnum.SUCCESS.getStatus());
            updateWrapper.set(ProjYogaAutoWorkout::getAudioShortJson, shortJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE));
            updateWrapper.set(ProjYogaAutoWorkout::getDuration, generateInfo.getDuration());
            updateWrapper.set(ProjYogaAutoWorkout::getGoal, projYogaAutoWorkoutUpdateReq.getGoal());
            updateWrapper.set(ProjYogaAutoWorkout::getResYogaStartVideoId, projYogaAutoWorkoutUpdateReq.getVideoIdList().get(0));
            updateWrapper.set(!StringUtils.isEmpty(projYogaAutoWorkoutUpdateReq.getUpdateUser()), ProjYogaAutoWorkout::getUpdateUser, projYogaAutoWorkoutUpdateReq.getUpdateUser());
            update(new ProjYogaAutoWorkout(), updateWrapper);
            saveAudioI18n(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA,workoutId, longJsonUrlMap, shortJsonUrlMap, projId);
            //关联表
            List<ProjYogaAutoWorkoutVideoRelation> relationList = CollUtil.newArrayList();
            for (WorkoutVideoRalation relation : generateInfo.getRelations()) {
                ProjYogaAutoWorkoutVideoRelation relation1 = new ProjYogaAutoWorkoutVideoRelation();
                relation1.setResYogaVideoId(relation.getResYogaVideoId());
                relation1.setResYogaVideoConnectionId(relation.getResYogaVideoConnectionId());
                relation1.setProjYogaAutoWorkoutId(workoutId);
                relation1.setRealVideoDuration(relation.getRealVideoDuration());
                relation1.setRealTransitionDuration(relation.getRealTransitionDuration());
                relationList.add(relation1);
            }
            projYogaAutoWorkoutVideoRelationService.saveBatch(relationList);
        });

    }

    @Override
    public List<ProjClassicYogaAutoWorkoutDownloadVO> downloadList() {
        List<ProjYogaAutoWorkoutTemplate> templateList = projYogaAutoWorkoutTemplateService.find(null);
        Set<Integer> templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(templateIdSet)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjYogaAutoWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIdSet);
        List<ProjYogaAutoWorkout> workoutList = list(wrapper);
        if (CollUtil.isEmpty(workoutList)) {
            return new ArrayList<>();
        }
        List<ProjClassicYogaAutoWorkoutDownloadVO> workoutDownloadList = new ArrayList<>(workoutList.size());
        for (ProjYogaAutoWorkout workout : workoutList) {
            ProjClassicYogaAutoWorkoutDownloadVO downloadVO = new ProjClassicYogaAutoWorkoutDownloadVO();
            BeanUtils.copyProperties(workout, downloadVO);
            GoalEnum goal = workout.getGoal();
            if (null != goal) {
                downloadVO.setGoal(goal.getNewName());
            }
            workoutDownloadList.add(downloadVO);
        }
        return workoutDownloadList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importByExcel(InputStream inputStream, Integer projId) {
        final String errorTemplate = "id: %s,error message: %s";
        List<ProjClassicYogaAutoWorkoutImportReq> workoutReqList = CollUtil.newArrayList();
        EasyExcel.read(inputStream, ProjClassicYogaAutoWorkoutImportReq.class, new AnalysisEventListener<ProjClassicYogaAutoWorkoutImportReq>() {
            @Override
            public void invoke(ProjClassicYogaAutoWorkoutImportReq row, AnalysisContext analysisContext) {
                workoutReqList.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        if (CollUtil.isEmpty(workoutReqList)) {
            return null;
        }

        List<String> failMessage = CollUtil.newArrayList();
        List<ProjYogaAutoWorkout> workoutList = new ArrayList<>(workoutReqList.size());
        // goal为null的workoutId
        Set<Integer> workoutIdSet = new HashSet<>();
        for (ProjClassicYogaAutoWorkoutImportReq importReq : workoutReqList) {
            Set<ConstraintViolation<ProjClassicYogaAutoWorkoutImportReq>> violationSet = validator.validate(importReq, Group.class);
            if (CollUtil.isNotEmpty(violationSet)) {
                violationSet.stream().findFirst().ifPresent(violation -> failMessage.add(String.format(errorTemplate, importReq.getId(), violation.getMessage())));
                continue;
            }
            GoalEnum goalEnum = GoalEnum.getByNewName(importReq.getGoal());
            Integer id = importReq.getId();
            if (null == goalEnum) {
                workoutIdSet.add(id);
                continue;
            }
            ProjYogaAutoWorkout autoWorkout = new ProjYogaAutoWorkout();
            autoWorkout
                    .setGoal(goalEnum)
                    .setId(id);
            workoutList.add(autoWorkout);
        }

        if (CollUtil.isNotEmpty(failMessage)) {
            return failMessage;
        }
        if (CollUtil.isNotEmpty(workoutList)) {
            updateBatchById(workoutList);
        }
        if (CollUtil.isNotEmpty(workoutIdSet)) {
            LambdaUpdateWrapper<ProjYogaAutoWorkout> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(BaseModel::getId, workoutIdSet)
                    .set(ProjYogaAutoWorkout::getGoal, null);
            baseMapper.update(new ProjYogaAutoWorkout(), wrapper);
        }
        return failMessage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ProjYogaAutoWorkoutAddReq projYogaAutoWorkoutReq, Integer projId) {
        Collection<ResYogaVideo> allVideos = resYogaVideoService.listByIds(Sets.newHashSet(projYogaAutoWorkoutReq.getVideoIdList()));
        if (CollUtil.isNotEmpty(allVideos)) {
            check(projYogaAutoWorkoutReq, allVideos);
        }
        GenerateInfo generateInfo = getGenerateInfo(projYogaAutoWorkoutReq.getVideoIdList(), projId);
        Map<String, String> longJsonUrlMap = generateInfo.getLongJsonUrlMap();
        Map<String, String> shortJsonUrlMap = generateInfo.getShortJsonUrlMap();
        ProjYogaAutoWorkout workout = new ProjYogaAutoWorkout();
        workout.setProjId(projId)
                .setStatus(GlobalConstant.STATUS_DRAFT)
                .setDifficulty(projYogaAutoWorkoutReq.getDifficulty())
                .setVideoM3u8Url(generateInfo.getVideoM3u8Url())
                .setVideo2532Url(generateInfo.getVideo2532Url())
                .setGoal(projYogaAutoWorkoutReq.getGoal())
                .setCalorie(generateInfo.getCalorie())
                .setProjYogaAutoWorkoutTemplateId(projYogaAutoWorkoutReq.getProjYogaAutoTemplateId())
                .setAudioLongJson(longJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE))
                .setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus())
                .setAudioShortJson(shortJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE))
                .setDuration(generateInfo.getDuration())
                .setResYogaStartVideoId(projYogaAutoWorkoutReq.getVideoIdList().get(GlobalConstant.ZERO));

        save(workout);
        Integer workoutId = workout.getId();
        saveAudioI18n(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA,workoutId, longJsonUrlMap, shortJsonUrlMap, projId);
        // 关联表
        List<ProjYogaAutoWorkoutVideoRelation> relationList = CollUtil.newArrayList();
        for (WorkoutVideoRalation relation : generateInfo.getRelations()) {
            ProjYogaAutoWorkoutVideoRelation workoutVideoRelation = new ProjYogaAutoWorkoutVideoRelation();
            workoutVideoRelation.setResYogaVideoId(relation.getResYogaVideoId());
            workoutVideoRelation.setResYogaVideoConnectionId(relation.getResYogaVideoConnectionId());
            workoutVideoRelation.setProjYogaAutoWorkoutId(workoutId);
            workoutVideoRelation.setRealVideoDuration(relation.getRealVideoDuration());
            workoutVideoRelation.setRealTransitionDuration(relation.getRealTransitionDuration());
            relationList.add(workoutVideoRelation);
        }
        projYogaAutoWorkoutVideoRelationService.saveBatch(relationList);

    }

    @Override
    public void batchUpdate(ProjYogaAutoWorkoutBatchUpdateReq batchUpdateReq, Integer projId) {

        if (Objects.isNull(batchUpdateReq) || CollectionUtils.isEmpty(batchUpdateReq.getWorkoutIds())) {
            return;
        }

        Collection<ProjYogaAutoWorkout> workoutList = listByIds(batchUpdateReq.getWorkoutIds());
        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        // 更新这一批次数据的状态
        workoutList.forEach(workout -> workout.setUpdateStatus(ResUpdateStatusEnum.UPDATE.getStatus()));
        updateBatchById(workoutList);

        // 异步更新workout
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        asyncUpdateRes(workoutList, userName, projId);
    }

    private void saveAudioI18n(YogaAutoWorkoutTemplateEnum yogaAutoWorkoutTemplate,Integer workoutId, Map<String, String> longJsonUrlMap, Map<String, String> shortJsonUrlMap, Integer projId) {
        projYogaAutoWorkoutAudioI18nService.delete(workoutId, yogaAutoWorkoutTemplate);
        Map<String, ProjYogaAutoWorkoutAudioI18n> workoutAudioI18nMap = new HashMap<>();
        longJsonUrlMap.forEach((language, longJsonUrl) -> {
            ProjYogaAutoWorkoutAudioI18n audioI18n = workoutAudioI18nMap.getOrDefault(language, new ProjYogaAutoWorkoutAudioI18n());
            audioI18n.setWorkoutId(workoutId)
                    .setProjId(projId)
                    .setLanguage(language)
                    .setWorkoutType(yogaAutoWorkoutTemplate)
                    .setAudioLongJsonUrl(longJsonUrl)
                    .setAudioShortJsonUrl(shortJsonUrlMap.get(language));
            workoutAudioI18nMap.put(language, audioI18n);
        });
        projYogaAutoWorkoutAudioI18nService.saveBatch(workoutAudioI18nMap.values());
    }


    private static void check(ProjYogaAutoWorkoutAddReq projYogaAutoWorkoutReq, Collection<ResYogaVideo> allVideos) {
        List<ResYogaVideo> sortVideos = CollUtil.newArrayList();
        Map<Integer, ResYogaVideo> videoMap = allVideos.stream().collect(Collectors.toMap(ResYogaVideo::getId, t -> t));
        for (Integer videoId : projYogaAutoWorkoutReq.getVideoIdList()) {
            ResYogaVideo resYogaVideo = videoMap.get(videoId);
            if (resYogaVideo == null) {
                throw new BizException("No video with id " + videoId + " was found");
            }
            sortVideos.add(resYogaVideo);
        }
        final String LEFT = "(LEFT)";
        final String RIGHT = "(RIGHT)";
        Set<Integer> leftVideoIds = sortVideos.stream().filter(s -> s.getName().trim().toUpperCase().endsWith(LEFT)).map(s -> s.getId()).collect(Collectors.toSet());
        Set<Integer> rightVideoIds = sortVideos.stream().filter(s -> s.getName().trim().toUpperCase().endsWith(RIGHT)).map(s -> s.getId()).collect(Collectors.toSet());
        //校验left right 对应关系
        List<Integer> leftIndexs = CollUtil.newArrayList();
        List<Integer> rightIndexs = CollUtil.newArrayList();
        for (Integer id : projYogaAutoWorkoutReq.getVideoIdList()) {
            if (leftVideoIds.contains(id)) {
                leftIndexs.add(id);
            }
            if (rightVideoIds.contains(id)) {
                rightIndexs.add(id);
            }
        }
        if (leftIndexs.size() != rightIndexs.size()) {
            throw new BizException("left right match error");
        }
        for (int i = 0; i < leftIndexs.size(); i++) {
            Integer leftVideoId = leftIndexs.get(i);
            Integer rightVideoId = rightIndexs.get(i);
            ResYogaVideo leftVideo = videoMap.get(leftVideoId);
            ResYogaVideo rightVideo = videoMap.get(rightVideoId);
            if (leftVideo != null && rightVideo != null) {
                if (!StrUtil.subBefore(leftVideo.getName(), "(", true).equals(StrUtil.subBefore(rightVideo.getName(), "(", true))) {
                    throw new BizException("left right match error," + leftVideo.getName() + "," + rightVideo.getName());
                }
            } else {
                throw new BizException("No video with id " + leftVideoId + "," + rightVideoId + " was found");
            }
        }
    }


    private void asyncUpdateRes(Collection<ProjYogaAutoWorkout> workoutList, String operationUser, Integer projId) {

        try {
            asyncService.doSomethings(new IAsyncProcess() {
                @Override
                public void process() {
                    workoutList.forEach(workout -> {
                        try{
                            log.warn("Start to update res for yoga auto workout, workout id is {}.", workout.getId());
                            updateRes4Workout(workout.getId(), operationUser, projId);
                            log.warn("Finished to update res for yoga auto workout, workout id is {}.", workout.getId());
                        } catch (Exception ex) {
                            updateResStatus2Failed(workout.getId(), operationUser);
                            log.error("async update yoga auto workout failed, workout id is {}.", workout.getId());
                            log.warn(ex.getMessage(), ex);
                        }
                    });
                }
            });
        } catch (Exception exception) {
            log.warn(exception.getMessage(), exception);
        }
    }

    private boolean updateResStatus2Failed(Integer workoutId, String operationUser) {

        LambdaUpdateWrapper<ProjYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(ProjYogaAutoWorkout::getId, workoutId);
        updateWrapper.set(ProjYogaAutoWorkout::getUpdateUser, operationUser);
        updateWrapper.set(ProjYogaAutoWorkout::getUpdateStatus, ResUpdateStatusEnum.FAIL.getStatus());
        return update(new ProjYogaAutoWorkout(), updateWrapper);
    }

    private void updateRes4Workout(Integer workoutId, String operationUser, Integer projId) {

        ProjYogaAutoWorkoutDetailVO detailVO = detail(workoutId);
        ProjYogaAutoWorkoutUpdateReq updateReq = wrapUpdateReq(detailVO);
        updateReq.setUpdateUser(operationUser);
        autoWorkoutService.update(updateReq, projId);
    }

    private ProjYogaAutoWorkoutUpdateReq wrapUpdateReq(ProjYogaAutoWorkoutDetailVO detailVO) {

        ProjYogaAutoWorkoutUpdateReq updateReq = new ProjYogaAutoWorkoutUpdateReq();
        updateReq.setId(detailVO.getId());
        updateReq.setDifficulty(detailVO.getDifficulty());

        if (!CollectionUtils.isEmpty(detailVO.getVideoList())) {
            List<Integer> videoIdList = detailVO.getVideoList().stream().map(ProjYogaAutoWorkoutTempVideoPageVO::getId).collect(Collectors.toList());
            updateReq.setVideoIdList(videoIdList);
        }

        return updateReq;
    }

    @Override
    public BuildGenrateObj buildByGenerate(Integer projId, Integer projYogaAutoWorkoutTemplateId, Integer projYogaAutoWorkoutTaskId, List<ResYogaVideo> videos, Collection<ResYogaVideoConnection> connections, Collection<ResTransition> resTransitionList, String difficulty) {
        ProjYogaAutoWorkout projYogaAutoWorkout = new ProjYogaAutoWorkout();
        List<String> languageList = projInfoService.getLanguagesById(projId);
        Map<String, ClassicYogaAudioJsonWrapperBO> audioJsonWrapperMap = getSoundI18nMap(languageList);
        if(languageList.size() != audioJsonWrapperMap.size()){
            throw new BizException("there is an unfinished translation");
        }

        GenerateInfo generateInfo = getGenerateInfo(videos, connections, resTransitionList, audioJsonWrapperMap);
        Map<String, String> longJsonUrlMap = generateInfo.getLongJsonUrlMap();
        Map<String, String> shortJsonUrlMap = generateInfo.getShortJsonUrlMap();
        projYogaAutoWorkout
                .setProjId(projId)
                .setProjYogaAutoWorkoutTemplateId(projYogaAutoWorkoutTemplateId)
                .setProjYogaAutoWorkoutTaskId(projYogaAutoWorkoutTaskId)
                .setDifficulty(difficulty)
                .setCalorie(generateInfo.getCalorie())
                .setDuration(generateInfo.getDuration())
                .setStatus(GlobalConstant.STATUS_DRAFT)
                .setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus())
                .setVideoM3u8Url(generateInfo.getVideoM3u8Url())
                .setVideo2532Url(generateInfo.getVideo2532Url())
                .setAudioLongJson(longJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE))
                .setAudioShortJson(shortJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE));
        //保存关系
        List<WorkoutVideoRalation> relations = generateInfo.getRelations();
        List<ProjYogaAutoWorkoutVideoRelation> batchRelations = CollUtil.newArrayList();
        relations.stream().forEach(r -> {
            ProjYogaAutoWorkoutVideoRelation relation = new ProjYogaAutoWorkoutVideoRelation();
            BeanUtils.copyProperties(r, relation);
            batchRelations.add(relation);
        });
        return new BuildGenrateObj(projYogaAutoWorkout, batchRelations);
    }

    private Map<String, ClassicYogaAudioJsonWrapperBO> getSoundI18nMap(List<String> languageList) {

        Video200SysSoundBO oog200 = oog200BizConfig.getOog200();
        Set<String> soundNames = Sets.newHashSet(oog200.getFirst(), oog200.getNext(), oog200.getLast());
        List<ProjSound> soundList = projSoundService.listBySoundNames(soundNames);
        Map<String, ProjSound> soundNameMap = soundList.stream().collect(Collectors.toMap(ProjSound::getSoundName, Function.identity(), (k1, k2) -> k1));

        for (String soundName : soundNames) {
            if (!soundNameMap.containsKey(soundName)) {
                throw new BizException("System sound '" + soundName + "' not find!");
            }
            ProjSound sound = soundNameMap.get(soundName);
            if (StringUtils.isEmpty(sound.getUrl())) {
                throw new BizException("System sound '" + soundName + "' sound url is null!");
            }
        }

        List<BaseSoundI18n> soundI18nList = soundList.stream().map(BaseSoundI18n::new).collect(Collectors.toList());
        Map<String, Map<String, AudioJsonBO>> soundI18nMap = i18nAudioUtil.convertSound2I18nMap(soundI18nList, languageList);
        Map<String, ClassicYogaAudioJsonWrapperBO> audioJsonWrapperMap = new HashMap<>();

        for (String language : languageList) {
            ClassicYogaAudioJsonWrapperBO wrapper = new ClassicYogaAudioJsonWrapperBO();
            audioJsonWrapperMap.put(language, wrapper);
            wrapper.setFirstAudio(soundI18nMap.get(oog200.getFirst()).get(language))
                    .setNextAudio(soundI18nMap.get(oog200.getNext()).get(language))
                    .setLastAudio(soundI18nMap.get(oog200.getLast()).get(language));
        }
        return audioJsonWrapperMap;
    }

    private YogaAudioJsonBO getAudioJsonI18n(String language, Map<Integer, Map<String, ResSoundI18n>> soundI18nMap, YogaAudioJsonBO audioJson) {
        if (BooleanUtil.isFalse(audioJson.getNeedTranslation())) {
            return audioJson;
        }
        Integer soundId = audioJson.getSoundId();
        Map<String, ResSoundI18n> i18nMap = soundI18nMap.get(soundId);
        if (CollUtil.isEmpty(i18nMap)) {
            throw new BizException("res sound i18n not found");
        }
        ResSoundI18n resSoundI18n = i18nMap.get(language);
        if (null == resSoundI18n) {
            throw new BizException("res sound i18n not found");
        }
        String soundUrl = resSoundI18n.getSoundScriptFemale();
        if (StringUtils.isEmpty(soundUrl)) {
            throw new BizException(String.format("resSoundId:%s not found i18n audio", soundId));
        }
        YogaAudioJsonBO audioJsonBO = new YogaAudioJsonBO();
        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        audioJsonBO.setSoundId(soundId);
        audioJsonBO.setName(soundName);
        audioJsonBO.setUrl(fileService.getAbsoluteR2Url(soundUrl));
        audioJsonBO.setTime(new BigDecimal(resSoundI18n.getSoundScriptFemaleDuration()));
        audioJsonBO.setId(audioJson.getId());
        return audioJsonBO;
    }

    @Override
    public GenerateInfo getGenerateInfo(List<ResYogaVideo> videos, Collection<ResYogaVideoConnection> connections, Collection<ResTransition> resTransitionList, Map<String, ClassicYogaAudioJsonWrapperBO> audioJsonWrapperMap) {
        Set<String> languageSet = audioJsonWrapperMap.keySet();

        // video i18n
        List<BaseVideoI18n> videoI18nList = videos.stream().map(video ->
                new BaseVideoI18n(video.getCoreVoiceConfigI18nId(), video.getId(), video.getName(), video.getGuidanceScript())).collect(Collectors.toList());
        Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap = i18nAudioUtil.checkAndConvert2I18nMap(videoI18nList, languageSet, true, false);

//        Set<Integer> transitionIdSet = resTransitionList.stream().map(BaseModel::getId).collect(Collectors.toSet());
//        Map<Integer, Map<String, YogaTransition18nBO>> transitionI18nMap = middleI18nDataService.getI18nDataGroupList(ResTransition.class, YogaTransition18nBO.class, transitionIdSet);

        // transition 暂未进行翻译
        List<BaseVideoI18n> transitionI18nList = resTransitionList.stream().map(video ->
                new BaseVideoI18n(video.getCoreVoiceConfigI18nId(), video.getId(), "", video.getGuidanceScript())).collect(Collectors.toList());
        Map<Integer, Map<String, YogaVideoI18nBO>> transitionI18nMap = i18nAudioUtil.checkAndConvert2I18nMap(transitionI18nList, languageSet, false, false);


        GenerateInfo generateInfo = new GenerateInfo();
        Table<Integer, Integer, ResYogaVideoConnection> connectionTable = HashBasedTable.create();
        connections.stream().forEach(c -> connectionTable.put(c.getResYogaVideoId(), c.getResYogaVideoNextId(), c));
        Map<Integer, ResTransition> transitionMapById = resTransitionList.stream().collect(Collectors.toMap(ResTransition::getId, t -> t));
        //开始进行组合,默认最后两个是end和colldown,不进行transition拼接
        int tsTextMerge = 0;
        TsTextMergeBO tsTextMergeBO = new TsTextMergeBO();
        List<TsMergeBO> tsTextMerge2532BO = CollUtil.newArrayList();
        BigDecimal calorie = new BigDecimal(0d);
        List<WorkoutVideoRalation> relations = CollUtil.newArrayList();

        Map<String, List<AudioJsonBO>> audioLongJsonI18nMap = new HashMap<>();
        Map<String, List<AudioJsonBO>> audioShortJsonI18nMap = new HashMap<>();

        Integer duration = 0;
        for (int i = 0; i < videos.size(); i++) {
            ResYogaVideo resYogaVideo = videos.get(i);
            Integer videoId = resYogaVideo.getId();
            WorkoutVideoRalation workoutVideoRalation = new WorkoutVideoRalation();
            int oneVideoRealDuration = 0;
            int oneVideoTransitionDuration = 0;
            long videoLoopCount = Math.round(new BigDecimal(resYogaVideo.getPoseTime()).divide(new BigDecimal(resYogaVideo.getFrontVideoDuration()), 2, BigDecimal.ROUND_HALF_UP).doubleValue());
            Map<String, YogaVideoI18nBO> videoLanguageMap = videoI18nMap.get(videoId);
            for (String language : languageSet) {
                ClassicYogaAudioJsonWrapperBO wrapperBO = audioJsonWrapperMap.get(language);
                AudioJsonBO firstAudio = wrapperBO.getFirstAudio();
                List<AudioJsonBO> audioShortJsonList = audioShortJsonI18nMap.getOrDefault(language, new ArrayList<>());
                List<AudioJsonBO> audioLongJsonList = audioLongJsonI18nMap.getOrDefault(language, new ArrayList<>());
                YogaVideoI18nBO yogaVideoI18nBO = getYogaVideoI18nBO(language, resYogaVideo, videoLanguageMap);
                String nameAudioUrl = yogaVideoI18nBO.getNameScriptFemale();

                AudioJsonBO nameAudio = new AudioJsonBO(resYogaVideo.getId().toString(), fileService.getAbsoluteR2Url(nameAudioUrl), FireBaseUrlSubUtils.getFileName(nameAudioUrl), new BigDecimal(yogaVideoI18nBO.getNameScriptFemaleDuration()));
                if(GlobalConstant.DEFAULT_LANGUAGE.equals(language)){
                    String guidanceAudioUrl = yogaVideoI18nBO.getGuidanceScriptFemale();
                    AudioJsonBO guidanceAudio = new AudioJsonBO(resYogaVideo.getId() + "guidance", fileService.getAbsoluteR2Url(guidanceAudioUrl), FireBaseUrlSubUtils.getFileName(guidanceAudioUrl), new BigDecimal(yogaVideoI18nBO.getGuidanceScriptFemaleDuration()));
                    addSysAudioJso(audioLongJsonList, guidanceAudio, duration + 100);
                }
                // 第一个视频，first+name
                if (0 == i) {
                    addSysAudioJso(audioShortJsonList, firstAudio, duration + 100);
                    addSysAudioJso(audioShortJsonList, nameAudio, duration + 100 + firstAudio.getTime().intValue());
                }
                audioShortJsonI18nMap.put(language, audioShortJsonList);
                audioLongJsonI18nMap.put(language, audioLongJsonList);
            }

            //组装视频
            for (long l = 0; l < videoLoopCount; l++) {
                if (tsTextMerge % 2 == 0) {
                    //处理 dynamic 清晰度
                    tsTextMergeBO.addM3u8Text(resYogaVideo.getFrontM3u8Text2k(), resYogaVideo.getFrontM3u8Text1080p(), resYogaVideo.getFrontM3u8Text720p(), resYogaVideo.getFrontM3u8Text480p(), resYogaVideo.getFrontM3u8Text360p());
                    //处理 2532 清晰度
//                    tsTextMerge2532BO.addM3u8Text(resYogaVideo.getFrontM3u8Text2532());
                    tsTextMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(resYogaVideo.getFrontVideoUrl()), resYogaVideo.getFrontVideoDuration()));
                    duration += resYogaVideo.getFrontVideoDuration();
                    oneVideoRealDuration += resYogaVideo.getFrontVideoDuration();
                    tsTextMerge++;
                } else {
                    //处理 dynamic 清晰度
                    tsTextMergeBO.addM3u8Text(resYogaVideo.getSideM3u8Text2k(), resYogaVideo.getSideM3u8Text1080p(), resYogaVideo.getSideM3u8Text720p(), resYogaVideo.getSideM3u8Text480p(), resYogaVideo.getSideM3u8Text360p());
                    //处理 2532 清晰度
//                    tsTextMerge2532BO.addM3u8Text(resYogaVideo.getSideM3u8Text2532());
                    tsTextMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(resYogaVideo.getSideVideoUrl()), resYogaVideo.getSideVideoDuration()));
                    duration += resYogaVideo.getSideVideoDuration();
                    oneVideoRealDuration += resYogaVideo.getSideVideoDuration();
                    tsTextMerge++;
                }
            }

            addNextUpAndNextNameAudio4Guidance(audioLongJsonI18nMap, videos, i, audioJsonWrapperMap, duration, languageSet, videoI18nMap);

            // 只有一个video 就first+name；至少有两个video,才会有next last,transition
            if (videos.size() >= 2) {
                // 当前是倒数第二个，他的系统音用last,其他时候用next
                for (String language : languageSet) {
                    ClassicYogaAudioJsonWrapperBO wrapperBO = audioJsonWrapperMap.get(language);
                    List<AudioJsonBO> audioShortJsonList = audioShortJsonI18nMap.getOrDefault(language, new ArrayList<>());
                    if (i == videos.size() - 2) {
                        addSysAudioJso(audioShortJsonList, wrapperBO.getLastAudio(), duration - 3000);
                    } else if (i < videos.size() - 2) {
                        addSysAudioJso(audioShortJsonList, wrapperBO.getNextAudio(), duration - 3000);
                    }
                    audioShortJsonI18nMap.put(language, audioShortJsonList);
                }


                if (i < videos.size() - 1) {
                    ResYogaVideo nextVideo = videos.get(i + 1);
                    Integer nextVideoId = nextVideo.getId();
                    ResYogaVideoConnection connection = connectionTable.get(videoId, nextVideoId);
                    if (connection == null) {
                        throw new BizException("There is no flow from " + resYogaVideo.getName() + " to " + nextVideo.getName());
                    }
                    ResTransition resTransition = transitionMapById.get(connection.getResTransitionId());
                    Integer transitionId = resTransition.getId();
                    Map<String, YogaVideoI18nBO> transitionLanguageMap = transitionI18nMap.get(transitionId);

                    for (String language : languageSet) {
                        List<AudioJsonBO> audioShortJsonList = audioShortJsonI18nMap.getOrDefault(language, new ArrayList<>());
                        YogaVideoI18nBO yogaVideoI18nBO = getYogaVideoI18nBO(language, nextVideo, videoI18nMap.get(nextVideoId));
                        String nextNameAudioUrl = yogaVideoI18nBO.getNameScriptFemale();
                        AudioJsonBO nextNameAudio = new AudioJsonBO(nextVideo.getId() + "next audio name", fileService.getAbsoluteR2Url(nextNameAudioUrl), FireBaseUrlSubUtils.getFileName(nextNameAudioUrl), new BigDecimal(yogaVideoI18nBO.getNameScriptFemaleDuration()));
                        addSysAudioJso(audioShortJsonList, nextNameAudio, duration + 100);
                        audioShortJsonI18nMap.put(language, audioShortJsonList);


                        if (GlobalConstant.DEFAULT_LANGUAGE.equals(language)) {
                            YogaVideoI18nBO transition18nBO = getYogaVideoI18nBO4ResTransition(language, resTransition, transitionLanguageMap);
                            String transitionGuidanceAudioUrl = transition18nBO.getGuidanceScriptFemale();
                            AudioJsonBO transitionGuidanceAudio = new AudioJsonBO(transitionId + "transition guidance", fileService.getAbsoluteR2Url(transitionGuidanceAudioUrl), FireBaseUrlSubUtils.getFileName(transitionGuidanceAudioUrl), new BigDecimal(transition18nBO.getGuidanceScriptFemaleDuration()));
                            List<AudioJsonBO> audioLongJsonList = audioLongJsonI18nMap.getOrDefault(language, new ArrayList<>());
                            addSysAudioJso(audioLongJsonList, transitionGuidanceAudio, duration + 100);
                            audioLongJsonI18nMap.put(language, audioLongJsonList);
                        }
                    }


                    if (tsTextMerge % 2 == 0) {
                        //处理 dynamic 清晰度
                        tsTextMergeBO.addM3u8Text(resTransition.getFrontM3u8Text2k(), resTransition.getFrontM3u8Text1080p(), resTransition.getFrontM3u8Text720p(), resTransition.getFrontM3u8Text480p(), resTransition.getFrontM3u8Text360p());
                        //处理 2532 清晰度
                        tsTextMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(resTransition.getFrontVideoUrl()), resTransition.getFrontVideoDuration()));
                        duration += resTransition.getFrontVideoDuration();
                        oneVideoTransitionDuration += resTransition.getFrontVideoDuration();
                        tsTextMerge++;
                    } else {
                        //处理 dynamic 清晰度
                        tsTextMergeBO.addM3u8Text(resTransition.getSideM3u8Text2k(), resTransition.getSideM3u8Text1080p(), resTransition.getSideM3u8Text720p(), resTransition.getSideM3u8Text480p(), resTransition.getSideM3u8Text360p());
                        //处理 2532 清晰度
                        tsTextMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(resTransition.getSideVideoUrl()), resTransition.getSideVideoDuration()));
                        duration += resTransition.getSideVideoDuration();
                        oneVideoTransitionDuration += resTransition.getSideVideoDuration();
                        tsTextMerge++;
                    }
                }

            }

            calorie = calorie.add(resYogaVideo.getCalorie());

            //relation关系
            //不是最后一个video
            if (i < videos.size() - 1) {
                ResYogaVideo nextVideo = videos.get(i + 1);
                Integer nextVideoId = nextVideo.getId();
                ResYogaVideoConnection connection = connectionTable.get(videoId, nextVideoId);
                workoutVideoRalation.setResYogaVideoConnectionId(connection.getId());
                workoutVideoRalation.setResYogaVideoId(videoId);
                workoutVideoRalation.setRealVideoDuration(oneVideoRealDuration);
                workoutVideoRalation.setRealTransitionDuration(oneVideoTransitionDuration);
                relations.add(workoutVideoRalation);
            } else {
                // 处理workout最后一个video
                workoutVideoRalation.setResYogaVideoConnectionId(null);
                workoutVideoRalation.setResYogaVideoId(videoId);
                workoutVideoRalation.setRealVideoDuration(oneVideoRealDuration);
                workoutVideoRalation.setRealTransitionDuration(oneVideoTransitionDuration);
                relations.add(workoutVideoRalation);
            }
        }
        Map<String, String> longJsonUrlMap = new HashMap<>();
        Map<String, String> shortJsonUrlMap = new HashMap<>();
        audioLongJsonI18nMap.forEach((language,longJson) -> {
            UploadFileInfoRes audioJsonLongInfo = fileService.uploadJsonR2(JacksonUtil.toJsonString(longJson), "project-yoga-workout-json");
            longJsonUrlMap.put(language, audioJsonLongInfo.getFileRelativeUrl());
        });
        audioShortJsonI18nMap.forEach((language, shortJson) -> {
            UploadFileInfoRes audioJsonShortInfo = fileService.uploadJsonR2(JacksonUtil.toJsonString(shortJson), "project-yoga-workout-json");
            shortJsonUrlMap.put(language, audioJsonShortInfo.getFileRelativeUrl());
        });
        //生成m3u8
        //生成m3u8
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(tsTextMergeBO, "project-yoga-workout-m3u8");
        String video2532Url = null;
        if (CollectionUtil.isNotEmpty(tsTextMerge2532BO)) {
            UploadFileInfoRes video2532R2Info = fileService.uploadMergeTSForM3U8R2(tsTextMerge2532BO, "project-yoga-workout-m3u8");
//            UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8WithoutNested(tsTextMerge2532BO, "project-yoga-workout-m3u8");
            video2532Url = video2532R2Info.getFileRelativeUrl();
        }
        generateInfo.setCalorie(calorie);
        generateInfo.setDuration(duration);
        generateInfo.setRelations(relations);
        generateInfo.setVideoM3u8Url(videoR2Info.getFileRelativeUrl());
        generateInfo.setVideo2532Url(video2532Url);
//        generateInfo.setVideoM3u8Url("");
//        generateInfo.setAudioLongJson("");
//        generateInfo.setAudioShortJson("");
        generateInfo.setLongJsonUrlMap(longJsonUrlMap);
        generateInfo.setShortJsonUrlMap(shortJsonUrlMap);
        return generateInfo;
    }

    private static YogaTransition18nBO getYogaTransition18nBO(String language, Integer transitionId, ResTransition resTransition, Map<String, YogaTransition18nBO> transitionMap) {
        YogaTransition18nBO transition18nBO;
        if (GlobalConstant.DEFAULT_LANGUAGE.equals(language)) {
            transition18nBO = new YogaTransition18nBO();
            transition18nBO
                    .setId(transitionId)
                    .setGuidanceScriptFemale(resTransition.getGuidanceAudioUrl())
                    .setGuidanceScriptFemaleDuration(resTransition.getGuidanceAudioDuration());
        } else {
            transition18nBO = transitionMap.get(language);
        }
        if(null == transition18nBO){
            throw new BizException(String.format("transition not translation completed, language: %s, id: %s", language, transitionId));
        }
        // 这个版本暂时不翻译guidance，故注释
        // if(StrUtil.isBlank(transition18nBO.getGuidanceScriptFemale()) || null == transition18nBO.getGuidanceScriptFemaleDuration()){
        //     throw new BizException(String.format("transition guidance script not translation completed, language: %s, id: %s", language, transitionId));
        // }
        return transition18nBO;
    }

    private static YogaVideoI18nBO getYogaVideoI18nBO(String language, ResYogaVideo resYogaVideo, Map<String, YogaVideoI18nBO> videoLanguageMap) {

        Integer videoId = resYogaVideo.getId();
        YogaVideoI18nBO yogaVideoI18nBO;
        if (GlobalConstant.DEFAULT_LANGUAGE.equals(language)) {
            yogaVideoI18nBO = new YogaVideoI18nBO();
            yogaVideoI18nBO
                    .setId(videoId)
                    .setNameScriptFemale(resYogaVideo.getNameAudioUrl())
                    .setNameScriptFemaleDuration(resYogaVideo.getNameAudioDuration())
                    .setGuidanceScriptFemale(resYogaVideo.getGuidanceAudioUrl())
                    .setGuidanceScriptFemaleDuration(resYogaVideo.getGuidanceAudioDuration());
        } else {
            yogaVideoI18nBO = videoLanguageMap.get(language);
        }
        if(null == yogaVideoI18nBO){
            throw new BizException(String.format("video not translation completed, language: %s, id: %s", language, videoId));
        }
        // 这个版本暂时不翻译guidance，故注释
        // if (StrUtil.isBlank(yogaVideoI18nBO.getGuidanceScriptFemale()) || null == yogaVideoI18nBO.getGuidanceScriptFemaleDuration()) {
        //     throw new BizException(String.format("video guidance script not translation completed, language: %s, id: %s", language, videoId));
        // }
        if(StrUtil.isBlank(yogaVideoI18nBO.getNameScriptFemale()) || null == yogaVideoI18nBO.getNameScriptFemaleDuration()){
            throw new BizException(String.format("video name script not translation completed, language: %s, id: %s", language, videoId));
        }
        return yogaVideoI18nBO;
    }

    private static YogaVideoI18nBO getYogaVideoI18nBO4ResTransition(String language, ResTransition resTransition, Map<String, YogaVideoI18nBO> videoLanguageMap) {

        Integer videoId = resTransition.getId();
        YogaVideoI18nBO yogaVideoI18nBO;
        if (GlobalConstant.DEFAULT_LANGUAGE.equals(language)) {
            yogaVideoI18nBO = new YogaVideoI18nBO();
            yogaVideoI18nBO
                    .setId(videoId)
                    .setGuidanceScriptFemale(resTransition.getGuidanceAudioUrl())
                    .setGuidanceScriptFemaleDuration(resTransition.getGuidanceAudioDuration());
        } else {
            yogaVideoI18nBO = videoLanguageMap.get(language);
        }
        if(null == yogaVideoI18nBO){
            throw new BizException(String.format("video not translation completed, language: %s, id: %s", language, videoId));
        }
        // 这个版本暂时不翻译guidance，故注释
        // if (StrUtil.isBlank(yogaVideoI18nBO.getGuidanceScriptFemale()) || null == yogaVideoI18nBO.getGuidanceScriptFemaleDuration()) {
        //     throw new BizException(String.format("video guidance script not translation completed, language: %s, id: %s", language, videoId));
        // }
        return yogaVideoI18nBO;
    }



    private void addNextUpAndNextNameAudio4Guidance(Map<String, List<AudioJsonBO>> audioLongJsonI18nMap, List<ResYogaVideo> yogaVideoList, int index,
                                                    Map<String, ClassicYogaAudioJsonWrapperBO> audioJsonWrapperMap, Integer duration, Set<String> languageSet, Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap) {


        if (CollectionUtils.isEmpty(yogaVideoList) || yogaVideoList.size() == index + NUMBER_ONE) {
            return;
        }

        // next name audio
        ResYogaVideo nextVideo = yogaVideoList.get(index + NUMBER_ONE);
        for (String language : languageSet) {
            List<AudioJsonBO> audioLongJsonList = audioLongJsonI18nMap.getOrDefault(language, new ArrayList<>());
            YogaVideoI18nBO yogaVideoI18nBO = getYogaVideoI18nBO(language, nextVideo, videoI18nMap.get(nextVideo.getId()));
            String nameAudioUrl = yogaVideoI18nBO.getNameScriptFemale();
            AudioJsonBO nextNameAudio = new AudioJsonBO(nextVideo.getId() + nextVideo.getName(), fileService.getAbsoluteR2Url(nameAudioUrl), FireBaseUrlSubUtils.getFileName(nameAudioUrl), new BigDecimal(yogaVideoI18nBO.getNameScriptFemaleDuration()));

            ClassicYogaAudioJsonWrapperBO wrapperBO = audioJsonWrapperMap.get(language);
            AudioJsonBO nextAudio = wrapperBO.getNextAudio();
            Video200SysSoundBO soundFor200 = oog200BizConfig.getOog200();
            // for next_up audio playtime: next up + period + next pose + period
            int nextUpPlayTime = duration - nextAudio.getTime().intValue() - soundFor200.getSplitMiddle() - nextNameAudio.getTime().intValue() - soundFor200.getSplitEnd();
            addSysAudioJsoV2(audioLongJsonList, nextAudio, nextUpPlayTime);

            // for next_name audio playtime: next pose + period
            int nextPosePlayTime = duration - nextNameAudio.getTime().intValue() - soundFor200.getSplitEnd();
            addSysAudioJsoV2(audioLongJsonList, nextNameAudio, nextPosePlayTime);
            audioLongJsonI18nMap.put(language, audioLongJsonList);
        }

    }

    @Override
    public GenerateInfo getGenerateInfo(List<Integer> yogaVideoIdList2, Integer projId) {
        List<Integer> yogaVideoIdList = CollUtil.newArrayList(yogaVideoIdList2);
        AtomicReference<GenerateInfo> result = new AtomicReference<>();
        Optional.ofNullable(resYogaVideoService.listByIds(Sets.newHashSet(yogaVideoIdList))).filter(CollUtil::isNotEmpty).ifPresent(videos -> {
            //默认最后两个节点为end和cooldown
            Optional.ofNullable(resYogaVideoConnectionService.listByVideoIds(yogaVideoIdList.subList(0, yogaVideoIdList.size()))).filter(CollUtil::isNotEmpty).ifPresent(connections -> {
                Optional.ofNullable((resTransitionService.listByIds(connections.stream().map(ResYogaVideoConnection::getResTransitionId).collect(Collectors.toSet())))).filter(CollUtil::isNotEmpty).ifPresent(transitions -> {
                    List<ResYogaVideo> sortVideos = CollUtil.newArrayList();
                    Map<Integer, ResYogaVideo> videoMaps = videos.stream().collect(Collectors.toMap(ResYogaVideo::getId, t -> t));
                    for (Integer id : yogaVideoIdList2) {
                        sortVideos.add(videoMaps.get(id));
                    }
                    List<String> languageList = projInfoService.getLanguagesById(projId);
                    Map<String, ClassicYogaAudioJsonWrapperBO> audioJsonWrapperMap = getSoundI18nMap(languageList);
                    if(languageList.size() != audioJsonWrapperMap.size()){
                        throw new BizException("there is an unfinished translation");
                    }
                    result.set(getGenerateInfo(sortVideos, connections, transitions, audioJsonWrapperMap));
                });
            });
        });
        return result.get();
    }

    @Data
    @AllArgsConstructor
    public static class BuildGenrateObj {
        //workout
        private ProjYogaAutoWorkout projYogaAutoWorkout;
        //workout的关联数据表数据
        private List<ProjYogaAutoWorkoutVideoRelation> relations;
    }

    @Data
    public static class GenerateInfo {
        private String videoM3u8Url;
        private String video2532Url;
        Map<String, String> longJsonUrlMap;
        Map<String, String> shortJsonUrlMap;
        private BigDecimal calorie;
        private Integer duration;
        private List<WorkoutVideoRalation> relations;
    }

    @Data
    public static class WorkoutVideoRalation {
        private Integer resYogaVideoId;
        private Integer resYogaVideoConnectionId;
        private Integer realVideoDuration;
        private Integer realTransitionDuration;
    }

    private void addSysAudioJso(List<AudioJsonBO> audioList, AudioJsonBO sysAudioJson, int playTime) {
        AudioJsonBO audioJsonBO = new AudioJsonBO(sysAudioJson.getId(), sysAudioJson.getUrl(), sysAudioJson.getName(), new BigDecimal(playTime).divide(new BigDecimal("1000.0"), 1, RoundingMode.HALF_UP));
        audioList.add(audioJsonBO);
    }

    /**
     * 向下取整
     */
    private void addSysAudioJsoV2(List<AudioJsonBO> audioList, AudioJsonBO sysAudioJson, int playTime) {
        AudioJsonBO audioJsonBO = new AudioJsonBO(sysAudioJson.getId(), sysAudioJson.getUrl(), sysAudioJson.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), NUMBER_ONE, RoundingMode.DOWN));
        audioList.add(audioJsonBO);
    }


}
