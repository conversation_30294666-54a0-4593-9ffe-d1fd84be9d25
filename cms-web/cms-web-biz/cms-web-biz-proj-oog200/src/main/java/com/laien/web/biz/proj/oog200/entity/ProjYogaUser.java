package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: hhl
 * @date: 2025/6/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_user")
@ApiModel(value="ProjYogaUser对象", description="yoga user")
public class ProjYogaUser extends BaseModel {

    @ApiModelProperty(value = "第三方支付平台Id")
    private String adaptyId;

    @ApiModelProperty(value = "个人邀请码，保证唯一")
    private String inviteCode;

}
