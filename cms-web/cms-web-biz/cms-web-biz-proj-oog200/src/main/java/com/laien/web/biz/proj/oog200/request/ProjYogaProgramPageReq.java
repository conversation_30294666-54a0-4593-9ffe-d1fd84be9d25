package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/12/17 20:05
 */
@Data
public class ProjYogaProgramPageReq extends PageReq {

    private String name;

    private Integer status;

    private Integer programCategoryId;

    @ApiModelProperty(value = "单选")
    private YogaProgramTypeEnum programPositionType;

    private DifficultyEnum difficulty;

}
