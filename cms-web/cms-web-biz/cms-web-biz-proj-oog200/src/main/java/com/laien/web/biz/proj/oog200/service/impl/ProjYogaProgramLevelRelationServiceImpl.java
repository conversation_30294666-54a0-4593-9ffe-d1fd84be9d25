package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaRegularWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesRegularWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevelRelation;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkout;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramLevelRelationMapper;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutPageVO;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaRegularWorkoutService;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesRegularWorkoutService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramLevelRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaRegularWorkoutService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:48
 */
@Slf4j
@Service
public class ProjYogaProgramLevelRelationServiceImpl extends ServiceImpl<ProjYogaProgramLevelRelationMapper, ProjYogaProgramLevelRelation> implements IProjYogaProgramLevelRelationService {

    @Resource
    private IProjChairYogaRegularWorkoutService chairYogaRegularWorkoutService;

    @Resource
    private IProjYogaRegularWorkoutService yogaRegularWorkoutService;

    @Resource
    private IProjWallPilatesRegularWorkoutService wallPilatesRegularWorkoutService;

    @Override
    public List<ProjYogaRegularWorkoutPageVO> listWorkoutByProgramLevelIds(Collection<Integer> programLevelIds) {

        if (CollectionUtils.isEmpty(programLevelIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaProgramLevelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaProgramLevelRelation::getProjYogaProgramLevelId, programLevelIds);

        List<ProjYogaProgramLevelRelation> relationList = list(queryWrapper);
        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }

        List<ProjYogaRegularWorkoutPageVO> workoutResultList = new ArrayList<>(relationList.size());
        Map<YogaAutoWorkoutTemplateEnum, List<ProjYogaProgramLevelRelation>> groupByVideoType = relationList.stream().collect(Collectors.groupingBy(ProjYogaProgramLevelRelation::getVideoType));
        groupByVideoType.entrySet().forEach(entry -> {

            List<Integer> workoutIds = entry.getValue().stream().map(ProjYogaProgramLevelRelation::getProjYogaRegularWorkoutId).collect(Collectors.toList());
            if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA)) {

                Collection<ProjYogaRegularWorkout> classicWorkouts = yogaRegularWorkoutService.listByIds(workoutIds);
                if (CollectionUtils.isEmpty(classicWorkouts)) {
                    return;
                }

                List<ProjYogaRegularWorkoutPageVO> pageVOList = classicWorkouts.stream().map(classicWorkout -> {
                    ProjYogaRegularWorkoutPageVO pageVO = new ProjYogaRegularWorkoutPageVO();
                    BeanUtils.copyProperties(classicWorkout, pageVO);
                    return pageVO;
                }).collect(Collectors.toList());
                workoutResultList.addAll(pageVOList);
            }

            if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.WALL_PILATES)) {

                Collection<ProjWallPilatesRegularWorkout> wallPilatesWorkouts = wallPilatesRegularWorkoutService.listByIds(workoutIds);
                if (CollectionUtils.isEmpty(wallPilatesWorkouts)) {
                    return;
                }

                List<ProjYogaRegularWorkoutPageVO> pageVOList = wallPilatesWorkouts.stream().map(wallPilatesWorkout -> {
                    ProjYogaRegularWorkoutPageVO pageVO = new ProjYogaRegularWorkoutPageVO();
                    BeanUtils.copyProperties(wallPilatesWorkout, pageVO);
                    return pageVO;
                }).collect(Collectors.toList());
                workoutResultList.addAll(pageVOList);
            }

            if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA)) {

                Collection<ProjChairYogaRegularWorkout> chairYogaWorkouts = chairYogaRegularWorkoutService.listByIds(workoutIds);
                if (CollectionUtils.isEmpty(chairYogaWorkouts)) {
                    return;
                }

                List<ProjYogaRegularWorkoutPageVO> pageVOList = chairYogaWorkouts.stream().map(chairYogaWorkout -> {
                    ProjYogaRegularWorkoutPageVO pageVO = new ProjYogaRegularWorkoutPageVO();
                    BeanUtils.copyProperties(chairYogaWorkout, pageVO);
                    return pageVO;
                }).collect(Collectors.toList());
                workoutResultList.addAll(pageVOList);
            }
        });

        Table<Integer, YogaAutoWorkoutTemplateEnum, ProjYogaRegularWorkoutPageVO> uniqueWorkoutMap = HashBasedTable.create();
        workoutResultList.forEach(workout -> uniqueWorkoutMap.put(workout.getId(), workout.getVideoType(), workout));
        return relationList.stream().filter(relation -> uniqueWorkoutMap.contains(relation.getProjYogaRegularWorkoutId(), relation.getVideoType()))
                .map(relation -> uniqueWorkoutMap.get(relation.getProjYogaRegularWorkoutId(), relation.getVideoType())).collect(Collectors.toList());
    }

    @Override
    public void deleteByProgramLevelId(Integer programLevelId) {

        LambdaUpdateWrapper<ProjYogaProgramLevelRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaProgramLevelRelation::getProjYogaProgramLevelId, programLevelId);
        updateWrapper.set(ProjYogaProgramLevelRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjYogaProgramLevelRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjYogaProgramLevelRelation> listByProgramLevelIds(Collection<Integer> programLevelIds) {

        if (CollectionUtils.isEmpty(programLevelIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaProgramLevelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaProgramLevelRelation::getProjYogaProgramLevelId, programLevelIds);
        return list(queryWrapper);
    }

}
