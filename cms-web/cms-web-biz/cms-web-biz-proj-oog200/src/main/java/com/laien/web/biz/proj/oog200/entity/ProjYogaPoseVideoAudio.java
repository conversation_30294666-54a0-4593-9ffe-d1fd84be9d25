package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2024/7/31 10:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseVideo对象", description="proj yoga pose video")
public class ProjYogaPoseVideoAudio extends BaseModel {

    @ApiModelProperty(value = "关联的proj yoga pose video id")
    private Integer projYogaPoseVideoId;

    @ApiModelProperty(value = "用于标识在pose的第几轮播放中使用")
    private Integer roundIndex;

    @ApiModelProperty(value = "项目Id")
    private Integer projId;

    @ApiModelProperty(value = "pose video 第一阶段 解说音频")
    private String firstGuidanceAudioUrl;

    @ApiModelProperty(value = "pose video 第一阶段 解说音频时长")
    private Integer firstGuidanceAudioDuration;

    @ApiModelProperty(value = "pose video 第二阶段 解说音频")
    private String secondGuidanceAudioUrl;

    @ApiModelProperty(value = "pose video 第二阶段 解说音频时长")
    private Integer secondGuidanceAudioDuration;

    @ApiModelProperty(value = "pose video 第三阶段 解说音频")
    private String thirdGuidanceAudioUrl;

    @ApiModelProperty(value = "pose video 第三阶段 解说音频时长")
    private Integer thirdGuidanceAudioDuration;

    @ApiModelProperty(value = "pose video 第四阶段 解说音频")
    private String fourthGuidanceAudioUrl;

    @ApiModelProperty(value = "pose video 第四阶段 解说音频时长")
    private Integer fourthGuidanceAudioDuration;

    @ApiModelProperty(value = "pose video 第五阶段 解说音频")
    private String fifthGuidanceAudioUrl;

    @ApiModelProperty(value = "pose video 第五阶段 解说音频时长")
    private Integer fifthGuidanceAudioDuration;

    @ApiModelProperty(value = "pose video 第六阶段 解说音频")
    private String sixthGuidanceAudioUrl;

    @ApiModelProperty(value = "pose video 第六阶段 解说音频时长")
    private Integer sixthGuidanceAudioDuration;

}
