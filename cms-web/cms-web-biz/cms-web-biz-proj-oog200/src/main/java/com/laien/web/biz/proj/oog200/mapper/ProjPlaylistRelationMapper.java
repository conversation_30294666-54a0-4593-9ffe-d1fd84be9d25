package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPlaylistRelation;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistRelationVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/19 15:06
 */
public interface ProjPlaylistRelationMapper extends BaseMapper<ProjYogaPlaylistRelation> {


    @Select(value = "select relation.proj_yoga_playlist_id, relation.proj_yoga_music_id, relation.proj_id, relation.display_name, relation.subscription, relation.short_link, music.music_name, music.music_type \n" +
            "from proj_yoga_playlist_relation relation \n" +
            "inner join proj_yoga_music music on relation.proj_yoga_music_id = music.id \n" +
            "where relation.proj_yoga_playlist_id = #{playlistId} and relation.del_flag = 0")
    List<ProjYogaPlaylistRelationVO> listByPlaylistId(Integer playlistId);

}
