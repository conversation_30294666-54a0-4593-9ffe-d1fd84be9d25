/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.handler;

import com.google.common.collect.Lists;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

/**
 * <p>workout生成时常常需要批量上传一些文件，并发上传控制逻辑是通用的这里提供一个任务包装器 </p>
 * <p>非线程安全</>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Slf4j
public class UploadTaskExecutor implements AutoCloseable {

    /**
     * 默认上传并发数
     */
    public static final int DEFAULT_UPLOAD_THREAD_COUNT = 4;

    /**
     * 上传任务总数量
     */
    private final int uploadTaskCount;

    /**
     * 是否所有任务都上传成功
     */
    private final AtomicBoolean uploadSuccess = new AtomicBoolean(true);

    /**
     * 用于并发执行上传任务的线程池
     */
    private final ExecutorService executor;

    /**
     * 文件上传子任务
     */
    private final List<CompletableFuture<Void>> uploadTaskList;

    /**
     * 包装上传任务
     */
    private final Function<Runnable, Runnable> uploadTaskWrapper;

    public UploadTaskExecutor(int uploadTaskCount) {

        this.uploadTaskCount = uploadTaskCount;
        this.uploadTaskList = Lists.newArrayListWithCapacity(uploadTaskCount);
        this.executor = new ThreadPoolExecutor(DEFAULT_UPLOAD_THREAD_COUNT, DEFAULT_UPLOAD_THREAD_COUNT, GlobalConstant.ZERO, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(this.uploadTaskCount), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

        this.uploadTaskWrapper = (upload) -> () -> {
            // 其他上传任务执行失败，则放弃当前上传任务
            if (!uploadSuccess.get()) {
                log.warn("other upload task failed, skip current upload task");
                return;
            }
            long startTime = System.currentTimeMillis();
            try {
                upload.run();
            } catch (Exception e) {
                // 当前上传任务失败，通过线程间共享变量通知其他线程放弃后续上传任务
                uploadSuccess.set(false);
                log.warn("workout file upload failed", e);
            }
            log.info("workout file upload cost time: {}ms", System.currentTimeMillis() - startTime);
        };
    }

    public void execute(Runnable upload) {
        uploadTaskList.add(CompletableFuture.runAsync(uploadTaskWrapper.apply(upload), executor));
    }

    public boolean hasError() {
        return !uploadSuccess.get();
    }

    public void join() {
        try {
            CompletableFuture
                    .allOf(uploadTaskList.toArray(new CompletableFuture[0]))
                    .get(uploadTaskCount, TimeUnit.MINUTES);

            if (!uploadSuccess.get()) {
                throw new BizException("wall pilates upload m3u8 and audio json failed");
            }

        } catch (TimeoutException e) {
            throw new BizException("waiting wall pilates upload m3u8 and audio json overtime");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("wall pilates executor interrupted exception");
        } catch (ExecutionException e) {
            throw new BizException("wall pilates executor execution exception");
        } finally {
            executor.shutdown();
        }
    }

    @Override
    public void close() throws Exception {
        if (!this.executor.isShutdown()) {
            this.executor.shutdownNow();
        }
    }
}
