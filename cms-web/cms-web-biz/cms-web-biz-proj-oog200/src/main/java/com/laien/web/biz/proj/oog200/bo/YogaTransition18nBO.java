package com.laien.web.biz.proj.oog200.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
@Accessors(chain = true)
public class YogaTransition18nBO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "guidance文本")
    private String guidanceScript;

    @ApiModelProperty(value = "音频女地址")
    private String guidanceScriptFemale;

    @ApiModelProperty(value = "音频女时长")
    private Integer guidanceScriptFemaleDuration;

}
