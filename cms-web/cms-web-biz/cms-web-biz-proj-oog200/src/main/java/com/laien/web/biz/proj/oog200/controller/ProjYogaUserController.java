package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.oog200.entity.ProjYogaUser;
import com.laien.web.biz.proj.oog200.request.ProjYogaUserPageReq;
import com.laien.web.biz.proj.oog200.service.IProjYogaUserService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Api(tags = "项目管理:Yoga User")
@RestController
@RequestMapping("/proj/yogaUser")
public class ProjYogaUserController extends ResponseController {

    @Resource
    private IProjYogaUserService yogaUserService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaUser>> page(ProjYogaUserPageReq pageReq) {

        PageRes<ProjYogaUser> pageRes = yogaUserService.pageQuery(pageReq);
        return succ(pageRes);
    }

}
