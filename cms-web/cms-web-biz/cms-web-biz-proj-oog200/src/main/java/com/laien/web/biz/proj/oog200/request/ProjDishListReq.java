package com.laien.web.biz.proj.oog200.request;


import com.laien.common.oog200.enums.DishStyleEnum;
import com.laien.common.oog200.enums.DishTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * note: dish列表筛选条件
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "dish列表筛选条件", description = "dish列表筛选条件")
public class ProjDishListReq {

    @ApiModelProperty(value = "dish Id 集合")
    private Collection<Integer> dishIds;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "类型")
    private DishTypeEnum type;

    @ApiModelProperty(value = "类型")
    private DishStyleEnum style;

}
