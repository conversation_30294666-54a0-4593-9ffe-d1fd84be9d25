package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramTypeRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramTypeRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramTypeRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/23 16:44
 */
@Slf4j
@Service
public class ProjYogaProgramTypeRelationServiceImpl extends ServiceImpl<ProjYogaProgramTypeRelationMapper, ProjYogaProgramTypeRelation> implements IProjYogaProgramTypeRelationService {


    @Override
    public void deleteByProgramId(Integer programId) {

        LambdaUpdateWrapper<ProjYogaProgramTypeRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaProgramTypeRelation::getProjYogaProgramId, programId);
        updateWrapper.set(ProjYogaProgramTypeRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjYogaProgramTypeRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjYogaProgramTypeRelation> listByProgramIds(Collection<Integer> programIds) {

        if (CollectionUtils.isEmpty(programIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaProgramTypeRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaProgramTypeRelation::getProjYogaProgramId, programIds);
        return list(queryWrapper);
    }
}
