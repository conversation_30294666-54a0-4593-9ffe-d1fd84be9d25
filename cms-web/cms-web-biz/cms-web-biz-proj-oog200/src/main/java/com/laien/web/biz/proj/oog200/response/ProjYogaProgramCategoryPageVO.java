package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:58
 */
@Data
public class ProjYogaProgramCategoryPageVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutNum = 0;

    @ApiModelProperty(value = "program 数量")
    private Integer programNum = 0;

    @ApiModelProperty(value = "program level 数量")
    private Integer programLevelNum = 0;

    @ApiModelProperty(value = "课程类型")
    private List<YogaProgramTypeEnum> programType;

}
