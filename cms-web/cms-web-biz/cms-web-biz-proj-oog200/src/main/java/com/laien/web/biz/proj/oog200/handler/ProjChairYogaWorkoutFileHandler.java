/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.common.oog200.enums.PositionEnum;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog200.bo.ChairYogaWorkoutAudioBO;
import com.laien.web.biz.proj.oog200.bo.ChairYogaWorkoutVideoBO;
import com.laien.web.biz.proj.oog200.bo.ProjChairYogaAutoWorkoutBO;
import com.laien.web.biz.proj.oog200.bo.YogaVideoI18nBO;
import com.laien.web.biz.proj.oog200.config.Oog200BizConfig;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import com.laien.web.biz.proj.oog200.i18n.BaseSoundI18n;
import com.laien.web.biz.proj.oog200.i18n.BaseVideoI18n;
import com.laien.web.biz.proj.oog200.i18n.I18nAudioUtil;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjSoundService;
import com.laien.web.biz.resource.entity.ResSound;
import com.laien.web.biz.resource.entity.i18n.ResSoundI18n;
import com.laien.web.biz.resource.service.IResSoundService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.laien.web.frame.constant.GlobalConstant.SECOND_MILL;

/**
 * <p>生成chair yoga workout 文件</p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Slf4j
@Component
public class ProjChairYogaWorkoutFileHandler {

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    private I18nAudioUtil i18nAudioUtil;

    @Resource
    private IResSoundService resSoundService;

    @Resource
    private FileService fileService;

    @Resource
    private Oog200BizConfig oog200BizConfig;

    @Resource
    private IProjSoundService projSoundService;

    private static final String AUDIO_DIR_KEY = "project-chair-yoga-workout-json";
    private static final String M3U8_DIR_KEY = "project-chair-yoga-workout-m3u8";

    public List<ProjChairYogaAutoWorkoutBO> generateChairYogaWorkoutFile(
            List<ProjChairYogaAutoWorkoutBO> workoutBoList
            , Integer projId
            , ChairYogaWorkoutVideoBO videoBO
            , Map<Integer, ProjChairYogaVideo> videoMap
            , Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap) {

        // 加载项目语言
        List<String> projectLanguages = projInfoService.getLanguagesById(projId);
        // 加载音视频资源 包含多语言部分
        List<String> excludeEnLanguages = projectLanguages.stream().filter(language -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)).collect(Collectors.toList());

        // 验证视频是否翻译完成
        List<BaseVideoI18n> videoI18nList = videoMap.values().stream().map(video -> new BaseVideoI18n(video.getCoreVoiceConfigI18nId(), video.getId(), video.getName(), "")).collect(Collectors.toList());
        Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap = i18nAudioUtil.checkAndConvert2I18nMap(videoI18nList, excludeEnLanguages, true, false);

        // 加载音频对应多语言数据
        Map<String, ChairYogaWorkoutAudioBO> allLanguageSysSoundMap = getAllLanguageSysSoundMap(projectLanguages);
        // 生成并上传
        generateFileAndUpload(videoBO, videoMap, videoSliceMap, workoutBoList, excludeEnLanguages, videoI18nMap, allLanguageSysSoundMap);
        return workoutBoList;
    }

    private void generateFileAndUpload(ChairYogaWorkoutVideoBO videoBO, Map<Integer, ProjChairYogaVideo> videoMap,
                                       Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap,
                                       List<ProjChairYogaAutoWorkoutBO> workoutBoList, List<String> excludeEnLanguages,
                                       Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap,
                                       Map<String, ChairYogaWorkoutAudioBO> allLanguageSysSoundMap) {

        // 上传任务数量
        int uploadTaskCount = workoutBoList.size() * 4 + workoutBoList.size() * excludeEnLanguages.size();
        // 创建一个任务并行上传执行器，用于管理批量上传任务
        try (UploadTaskExecutor uploadTaskExecutor = new UploadTaskExecutor(uploadTaskCount)) {
            generateAndUpload(videoBO, videoMap, videoSliceMap, workoutBoList, excludeEnLanguages, videoI18nMap, allLanguageSysSoundMap, uploadTaskExecutor);
            // 等待任务执行完成，文件上传线程的执行结果也可以在主线程读取到了
            uploadTaskExecutor.join();
        } catch (BizException e) {
            throw e;
        }catch (Exception e){
            log.error("generate chair yoga workout file failed", e);
            throw new BizException("generate chair yoga workout file failed");
        }

    }

    private void generateAndUpload(ChairYogaWorkoutVideoBO videoBO, Map<Integer, ProjChairYogaVideo> videoMap, Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap, List<ProjChairYogaAutoWorkoutBO> workoutBoList, List<String> excludeEnLanguages, Map<Integer, Map<String, YogaVideoI18nBO>> videoI18nMap, Map<String, ChairYogaWorkoutAudioBO> allLanguageSysSoundMap, UploadTaskExecutor uploadTaskExecutor) {
        workoutBoList.forEach(workoutBO -> {
            // 多语言音频设置默认值
            workoutBO.setMultiLanguageAudioLongJson(new ConcurrentHashMap<>());
            workoutBO.setMultiLanguageAudioShortJson(new ConcurrentHashMap<>());
            // 总时长和总卡路里
            BigDecimal totalCalorie = workoutBO.getVideoIdList().stream().map(videoId -> videoMap.get(videoId).getCalorie()).reduce(BigDecimal.ZERO, BigDecimal::add);
            workoutBO.setCalorie(totalCalorie);
            // 计算时长&视频文件生成
            Pair<Pair<List<TsMergeBO>, TsTextMergeBO>, Pair<Integer, Map<Integer, AtomicInteger>>> videoFileAndDuration = generateWorkoutM3u8File(workoutBO.getVideoIdList(), videoBO, videoSliceMap, videoMap);
            // workout时长
            workoutBO.setDuration(videoFileAndDuration.getRight().getLeft());
            // 各个视频的真实时长
            workoutBO.setVideoRealDuration(videoFileAndDuration.getRight().getRight());
            // 上传视频文件
            uploadTaskExecutor.execute(() -> {
                String video2532Url = fileService.uploadMergeTSForM3U8R2(videoFileAndDuration.getLeft().getLeft(), M3U8_DIR_KEY).getFileRelativeUrl();
                workoutBO.setVideo2532Url(video2532Url);
            });
            uploadTaskExecutor.execute(() -> {
                String videoM3U8Url = fileService.uploadMergeTsTextForM3u8(videoFileAndDuration.getLeft().getRight(), M3U8_DIR_KEY).getFileRelativeUrl();
                workoutBO.setVideoM3u8Url(videoM3U8Url);
            });
            // 生成默认语言音频文件
            List<ProjChairYogaVideo> defaultLanguageVideoList = workoutBO.getVideoIdList().stream().map(videoMap::get).collect(Collectors.toList());
            Pair<List<AudioJsonBO>, List<AudioJsonBO>> audioJsonList = createGuidanceAndShortAudioJsonBO(defaultLanguageVideoList, allLanguageSysSoundMap.get(GlobalConstant.DEFAULT_LANGUAGE), videoBO, videoSliceMap);
            uploadTaskExecutor.execute(() -> {
                String audioShortJsonUrl = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonList.getLeft()), AUDIO_DIR_KEY).getFileRelativeUrl();
                workoutBO.setAudioShortJson(audioShortJsonUrl);
                // 将默认语言数据保存到多语言数据中去
                workoutBO.getMultiLanguageAudioShortJson().put(GlobalConstant.DEFAULT_LANGUAGE, audioShortJsonUrl);
            });
            uploadTaskExecutor.execute(() -> {
                String audioLongJsonUrl = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonList.getRight()), AUDIO_DIR_KEY).getFileRelativeUrl();
                workoutBO.setAudioLongJson(audioLongJsonUrl);
                // 将默认语言数据保存到多语言数据中去
                workoutBO.getMultiLanguageAudioLongJson().put(GlobalConstant.DEFAULT_LANGUAGE, audioLongJsonUrl);
            });
            // 再生成其他语言数据
            excludeEnLanguages.forEach(language -> {
                // 多语言版本视频配置
                List<ProjChairYogaVideo> videoList = workoutBO.getVideoIdList().stream().map(videoMap::get).map(video -> {
                    YogaVideoI18nBO projChairYogaVideoI18n = videoI18nMap.get(video.getId()).get(language);
                    ProjChairYogaVideo bean = BeanUtil.toBean(video, ProjChairYogaVideo.class);
                    // 只翻译name字段
                    bean.setName(projChairYogaVideoI18n.getNameScript());
                    bean.setNameAudioUrl(projChairYogaVideoI18n.getNameScriptFemale());
                    bean.setNameAudioDuration(projChairYogaVideoI18n.getNameScriptFemaleDuration());
                    return bean;
                }).collect(Collectors.toList());
                // 生成音频文件
                Pair<List<AudioJsonBO>, List<AudioJsonBO>> audioJsonListWithLanguage = createGuidanceAndShortAudioJsonBO(videoList, allLanguageSysSoundMap.get(language), videoBO, videoSliceMap);
                uploadTaskExecutor.execute(() -> {
                    String audioShortJsonUrl = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonListWithLanguage.getLeft()), AUDIO_DIR_KEY).getFileRelativeUrl();
                    workoutBO.getMultiLanguageAudioShortJson().put(language, audioShortJsonUrl);
                    // 由于无法翻译guidance，这里将long json 设置为short json
                    workoutBO.getMultiLanguageAudioLongJson().put(language, audioShortJsonUrl);
                });
            });

        });
    }

    private Pair<List<AudioJsonBO>, List<AudioJsonBO>> createGuidanceAndShortAudioJsonBO(List<ProjChairYogaVideo> videoList, ChairYogaWorkoutAudioBO audioBO, ChairYogaWorkoutVideoBO videoBO, Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap) {
        List<AudioJsonBO> longAudioList = Lists.newArrayList();
        List<AudioJsonBO> shortAudioList = Lists.newArrayList();
        int lastVideoEndTime = GlobalConstant.ZERO;
        AtomicBoolean sliceIsFront = new AtomicBoolean(true);
        for (int i = 0; i < videoList.size(); i++) {

            ProjChairYogaVideo currentVideo = videoList.get(i);
            ProjChairYogaVideoSliceDetailVO easyStart = Objects.equals(currentVideo.getPosition(), PositionEnum.STANDING.getName()) ? videoBO.getEasyStart4Standing() : videoBO.getEasyStart4Seated();
            // 当前视频的 easy start 片段时间计算首先需要计算当前视频的easy start 片段是正面还是侧面
            int easyStarDuration;
            if (sliceIsFront.getAndSet(!sliceIsFront.get())) {
                easyStarDuration = easyStart.getFrontVideoDuration();
            } else {
                easyStarDuration = easyStart.getSideVideoDuration();
            }
            // 添加长音频和短音频
            List<AudioJsonBO> currentVideoGuidanceAudioList = createGuidanceAudioList(i == GlobalConstant.ZERO, i == videoList.size() - 1, currentVideo, audioBO, lastVideoEndTime, easyStarDuration);
            longAudioList.addAll(currentVideoGuidanceAudioList);
            List<AudioJsonBO> currentVideoShortAudioList = createShortAudioList(i == GlobalConstant.ZERO, i == videoList.size() - 1, currentVideo, audioBO, lastVideoEndTime);
            shortAudioList.addAll(currentVideoShortAudioList);
            // 计算当前视频结束时间，easyStarDuration + video all slice end time
            lastVideoEndTime += videoSliceMap.get(currentVideo.getId()).stream()
                    .mapToInt(slice -> sliceIsFront.getAndSet(!sliceIsFront.get()) ? slice.getFrontVideoDuration() : slice.getSideVideoDuration())
                    .sum();
            lastVideoEndTime += easyStarDuration;
        }
        return Pair.of(shortAudioList, longAudioList);
    }

    private List<AudioJsonBO> createShortAudioList(boolean isFirst, boolean isLast, ProjChairYogaVideo currentVideo, ChairYogaWorkoutAudioBO audioBO, int currentVideoStartTime) {

        List<AudioJsonBO> shortAudioList = Lists.newArrayList();
        // first audio
        int playTime = currentVideoStartTime + GlobalConstant.TWO_HUNDRED;
        if (isFirst) {
            AudioJsonBO firstAudio = audioBO.getFirstAudio();
            AudioJsonBO audioJsonBO = new AudioJsonBO(firstAudio.getId(), firstAudio.getUrl(), firstAudio.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP));
            shortAudioList.add(audioJsonBO);
            playTime += firstAudio.getTime().intValue();
        }

        // last audio or next
        if (!isFirst) {
            AudioJsonBO playAudio = isLast ? audioBO.getLastAudio() : audioBO.getNextAudio();
            AudioJsonBO audioJsonBO = new AudioJsonBO(playAudio.getId(), playAudio.getUrl(), playAudio.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP));
            shortAudioList.add(audioJsonBO);
            playTime += playAudio.getTime().intValue();
        }

        // first or last 结束后500ms 播放 name audio
        playTime += GlobalConstant.FIVE_HUNDRED;
        AudioJsonBO nameAudio = new AudioJsonBO(FireBaseUrlSubUtils.getFileName(currentVideo.getNameAudioUrl()), fileService.getAbsoluteR2Url(currentVideo.getNameAudioUrl()), FireBaseUrlSubUtils.getFileName(currentVideo.getNameAudioUrl()), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP));
        shortAudioList.add(nameAudio);
        return shortAudioList;
    }

    private List<AudioJsonBO> createGuidanceAudioList(boolean isFirst, boolean isLast, ProjChairYogaVideo currentVideo, ChairYogaWorkoutAudioBO audioBO, int currentVideoStartTime, int easyStartDuration) {

        List<AudioJsonBO> guidanceAudioList = Lists.newArrayList();
        // first audio
        int playTime = currentVideoStartTime + GlobalConstant.TWO_HUNDRED;
        if (isFirst) {
            AudioJsonBO firstAudio = audioBO.getFirstAudio();
            AudioJsonBO audioJsonBO = new AudioJsonBO(firstAudio.getId(), firstAudio.getUrl(), firstAudio.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP));
            guidanceAudioList.add(audioJsonBO);
            playTime += firstAudio.getTime().intValue();
        }

        // last audio
        if (!isFirst && isLast) {
            AudioJsonBO lastAudio = audioBO.getLastAudio();
            AudioJsonBO audioJsonBO = new AudioJsonBO(lastAudio.getId(), lastAudio.getUrl(), lastAudio.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP));
            guidanceAudioList.add(audioJsonBO);
            playTime += lastAudio.getTime().intValue();
        }

        // first or last 结束后500ms 播放 name audio
        playTime += GlobalConstant.FIVE_HUNDRED;
        AudioJsonBO nameAudio = new AudioJsonBO(FireBaseUrlSubUtils.getFileName(currentVideo.getNameAudioUrl()), fileService.getAbsoluteR2Url(currentVideo.getNameAudioUrl()), FireBaseUrlSubUtils.getFileName(currentVideo.getNameAudioUrl()), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP));
        guidanceAudioList.add(nameAudio);
        // easy start slice 播放结束之后20ms 播放 guidance audio
        playTime = currentVideoStartTime + easyStartDuration + GlobalConstant.TWO_HUNDRED;
        AudioJsonBO guidanceAudio = new AudioJsonBO(FireBaseUrlSubUtils.getFileName(currentVideo.getGuidanceAudioUrl()), fileService.getAbsoluteR2Url(currentVideo.getGuidanceAudioUrl()), FireBaseUrlSubUtils.getFileName(currentVideo.getGuidanceAudioUrl()), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP));
        guidanceAudioList.add(guidanceAudio);

        return guidanceAudioList;
    }

    private Pair<Pair<List<TsMergeBO>, TsTextMergeBO>, Pair<Integer, Map<Integer, AtomicInteger>>> generateWorkoutM3u8File(List<Integer> videoIdList, ChairYogaWorkoutVideoBO videoBO, Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap, Map<Integer, ProjChairYogaVideo> videoMap) {

        AtomicBoolean isFront = new AtomicBoolean(true);
        AtomicInteger workoutDuration = new AtomicInteger();
        List<TsMergeBO> tsTextMerge2532BO = new ArrayList<>(videoIdList.size());
        TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
        Map<Integer, AtomicInteger> videoRealDuration = new ConcurrentHashMap<>();

        // 先生成 easy start slice + current video all slice的视频片段序列
        videoIdList.stream().flatMap(videoId -> {
            ProjChairYogaVideo currentVideo = videoMap.get(videoId);
            ProjChairYogaVideoSliceDetailVO easyStart = Objects.equals(currentVideo.getPosition(), PositionEnum.STANDING.getName()) ? videoBO.getEasyStart4Standing() : videoBO.getEasyStart4Seated();
            // 拷贝easyStart，将其视频id设置为当前的视频ID
            ProjChairYogaVideoSliceDetailVO copyEasyStart = BeanUtil.toBean(easyStart, ProjChairYogaVideoSliceDetailVO.class);
            copyEasyStart.setProjChairYogaVideoId(currentVideo.getId());
            Stream<ProjChairYogaVideoSliceDetailVO> stream = videoSliceMap.get(videoId).stream().sorted(Comparator.comparing(ProjChairYogaVideoSliceDetailVO::getSliceIndex));
            return Stream.concat(Stream.of(copyEasyStart), stream);
        }).forEach(slice -> {
            // 先设置默认值
            videoRealDuration.putIfAbsent(slice.getProjChairYogaVideoId(), new AtomicInteger(GlobalConstant.ZERO));
            if (isFront.getAndSet(!isFront.get())) {
                // 2532 ts文件
                tsTextMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(slice.getFrontVideoUrl()), slice.getFrontVideoDuration()));
                // 动态 m3u8文件
                tsTextMergeDynamicBO.addM3u8Text(slice.getFrontM3u8Text2k(), slice.getFrontM3u8Text1080p(), slice.getFrontM3u8Text720p(), slice.getFrontM3u8Text480p(), slice.getFrontM3u8Text360p());
                // workout duration
                workoutDuration.addAndGet(slice.getFrontVideoDuration());
                // video real duration
                videoRealDuration.get(slice.getProjChairYogaVideoId()).addAndGet(slice.getFrontVideoDuration());
            } else {
                // 2532 ts文件
                tsTextMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(slice.getSideVideoUrl()), slice.getSideVideoDuration()));
                // 动态 m3u8文件
                tsTextMergeDynamicBO.addM3u8Text(slice.getSideM3u8Text2k(), slice.getSideM3u8Text1080p(), slice.getSideM3u8Text720p(), slice.getSideM3u8Text480p(), slice.getSideM3u8Text360p());
                // workout duration
                workoutDuration.addAndGet(slice.getSideVideoDuration());
                // video real duration
                videoRealDuration.get(slice.getProjChairYogaVideoId()).addAndGet(slice.getSideVideoDuration());
            }
        });

        Pair<List<TsMergeBO>, TsTextMergeBO> left = Pair.of(tsTextMerge2532BO, tsTextMergeDynamicBO);
        Pair<Integer, Map<Integer, AtomicInteger>> right = Pair.of(workoutDuration.get(), videoRealDuration);

        return Pair.of(left, right);
    }

    private Map<String, ChairYogaWorkoutAudioBO> getAllLanguageSysSoundMap(List<String> languageList) {

        Set<String> soundNames = Sets.newHashSet(oog200BizConfig.getOog200().getChairYogaFirst(), oog200BizConfig.getOog200().getChairYogaNext(), oog200BizConfig.getOog200().getChairYogaLast());
        List<ProjSound> soundList = projSoundService.listBySoundNames(soundNames);
        Map<String, ProjSound> soundNameMap = soundList.stream().collect(Collectors.toMap(ProjSound::getSoundName, Function.identity(), (k1, k2) -> k1));

        for (String soundName : soundNames) {
            if (!soundNameMap.containsKey(soundName)) {
                throw new BizException("System sound '" + soundName + "' not find!");
            }
            ProjSound sound = soundNameMap.get(soundName);
            if (org.springframework.util.StringUtils.isEmpty(sound.getUrl())) {
                throw new BizException("System sound '" + soundName + "' sound url is null!");
            }
        }

        List<BaseSoundI18n> soundI18nList = soundList.stream().map(BaseSoundI18n::new).collect(Collectors.toList());
        Map<String, Map<String, AudioJsonBO>> soundI18nMap = i18nAudioUtil.convertSound2I18nMap(soundI18nList, languageList);

        Map<String, ChairYogaWorkoutAudioBO> sysSoundMap = new HashMap<>();
        for (String language : languageList) {
            ChairYogaWorkoutAudioBO wrapper = new ChairYogaWorkoutAudioBO();
            sysSoundMap.put(language, wrapper);
            wrapper.setFirstAudio(soundI18nMap.get(oog200BizConfig.getOog200().getChairYogaFirst()).get(language))
                    .setNextAudio(soundI18nMap.get(oog200BizConfig.getOog200().getChairYogaNext()).get(language))
                    .setLastAudio(soundI18nMap.get(oog200BizConfig.getOog200().getChairYogaLast()).get(language));
        }
        return sysSoundMap;
    }

    private ChairYogaWorkoutAudioBO getProjChairYogaSysSoundBOWithLanguage(String language, Map<Integer, Map<String, ResSoundI18n>> soundI18nMap, Map<String, Integer> soundNameIdMap) {

        ChairYogaWorkoutAudioBO sysSoundBO = new ChairYogaWorkoutAudioBO();

        ResSoundI18n first = soundI18nMap.get(soundNameIdMap.get(oog200BizConfig.getOog200().getChairYogaFirst())).get(language);
        ResSoundI18n next = soundI18nMap.get(soundNameIdMap.get(oog200BizConfig.getOog200().getChairYogaNext())).get(language);
        ResSoundI18n last = soundI18nMap.get(soundNameIdMap.get(oog200BizConfig.getOog200().getChairYogaLast())).get(language);


        sysSoundBO.setFirstAudio(covert2AudioJsonBO(first, oog200BizConfig.getOog200().getChairYogaFirst()));
        sysSoundBO.setNextAudio(covert2AudioJsonBO(next, oog200BizConfig.getOog200().getChairYogaNext()));
        sysSoundBO.setLastAudio(covert2AudioJsonBO(last, oog200BizConfig.getOog200().getChairYogaLast()));

        return sysSoundBO;
    }

    private AudioJsonBO covert2AudioJsonBO(ResSoundI18n resSoundI18n, String soundName) {
        String soundUrl = resSoundI18n.getSoundScriptFemale();
        return new AudioJsonBO(soundName, fileService.getAbsoluteR2Url(soundUrl), FireBaseUrlSubUtils.getFileName(soundUrl), NumberUtil.toBigDecimal(resSoundI18n.getSoundScriptFemaleDuration()));
    }

    private ChairYogaWorkoutAudioBO getDefaultLanguageProjChairYogaSysSoundBO(List<ResSound> sysSounds) {

        // 系统音获取并验证是否完整(系统音不区分语言)
        Set<String> soundNames = Sets.newHashSet(oog200BizConfig.getOog200().getChairYogaFirst(), oog200BizConfig.getOog200().getChairYogaNext(), oog200BizConfig.getOog200().getChairYogaLast());
        Map<String, ResSound> nameSoundMap = sysSounds.stream().collect(Collectors.toMap(ResSound::getSoundName, Function.identity(), (v1, v2) -> v1));
        // 数据校验
        soundNames.forEach(name -> {
            if (!nameSoundMap.containsKey(name)) {
                throw new BizException("System sound '" + name + "' not find!");
            }
            if (StringUtils.isBlank(nameSoundMap.get(name).getFemaleUrl())) {
                throw new BizException("System sound '" + name + "' not set!");
            }
        });
        ChairYogaWorkoutAudioBO soundBO = new ChairYogaWorkoutAudioBO();
        soundBO.setFirstAudio(createAudioJsonBO(nameSoundMap, oog200BizConfig.getOog200().getChairYogaFirst()));
        soundBO.setNextAudio(createAudioJsonBO(nameSoundMap, oog200BizConfig.getOog200().getChairYogaNext()));
        soundBO.setLastAudio(createAudioJsonBO(nameSoundMap, oog200BizConfig.getOog200().getChairYogaLast()));

        return soundBO;
    }

    private AudioJsonBO createAudioJsonBO(Map<String, ResSound> nameSoundMap, String sysAudioName) {
        ResSound resSound = nameSoundMap.get(sysAudioName);
        String soundUrl = resSound.getFemaleUrl();
        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        String url = fileService.getAbsoluteR2Url(soundUrl);
        return new AudioJsonBO(sysAudioName, url, soundName, NumberUtil.toBigDecimal(resSound.getFemaleDuration()));
    }

    private Map<Integer, ResSound> getProjChairYogaSysSoundMap() {
        Set<String> soundNames = Sets.newHashSet(oog200BizConfig.getOog200().getChairYogaFirst(), oog200BizConfig.getOog200().getChairYogaNext(), oog200BizConfig.getOog200().getChairYogaLast());
        LambdaQueryWrapper<ResSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ResSound::getSoundName, soundNames);
        return resSoundService.list(queryWrapper).stream().collect(Collectors.toMap(BaseModel::getId, Function.identity(), (v1, v2) -> v1));

    }


}