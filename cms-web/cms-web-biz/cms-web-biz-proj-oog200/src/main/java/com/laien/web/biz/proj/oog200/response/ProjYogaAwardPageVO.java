package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: hhl
 * @date: 2025/6/10
 */
@Data
public class ProjYogaAwardPageVO extends BaseModel {

    @ApiModelProperty(value = "产品Id")
    private String productId;

    @ApiModelProperty(value = "兑换链接")
    private String awardLink;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "兑换截止时间")
    private LocalDateTime expiredTime;

    @ApiModelProperty(value = "持续时间，以月为单位")
    private Integer duration;

    @ApiModelProperty(value = "0表示未使用，1表示已使用")
    private Integer useFlag;

}
