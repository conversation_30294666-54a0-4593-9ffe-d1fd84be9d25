package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseLevel;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseLevelGroupRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseLevelMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseLevelUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupListVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseLevelDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseLevelListVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseGroupService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseLevelGroupRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseLevelService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * proj yoga pose grouping 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
public class ProjYogaPoseLevelServiceImpl extends ServiceImpl<ProjYogaPoseLevelMapper, ProjYogaPoseLevel> implements IProjYogaPoseLevelService {

    @Resource
    private IProjYogaPoseLevelGroupRelationService projYogaPoseLevelGroupRelationService;

    @Resource
    private IProjYogaPoseGroupService projYogaPoseGroupService;

    @Override
    @Transactional
    public void save(ProjYogaPoseLevelAddReq yogaPoseLevelAddReq, Integer projId) {
        check(yogaPoseLevelAddReq, null);
        ProjYogaPoseLevel poseLevel = new ProjYogaPoseLevel();
        BeanUtils.copyProperties(yogaPoseLevelAddReq, poseLevel);
        poseLevel.setProjId(projId);
        save(poseLevel);
        List<Integer> yogaPoseGroupIds = yogaPoseLevelAddReq.getProjYogaPoseGroupIds();
        saveLevelGroupRelation(yogaPoseGroupIds, poseLevel);

    }


    @Transactional
    @Override
    public void update(ProjYogaPoseLevelUpdateReq yogaPoseLevelReq, Integer projId) {
        Integer id = yogaPoseLevelReq.getId();
        ProjYogaPoseLevel yogaPoseLevel = baseMapper.selectById(id);
        if(null == yogaPoseLevel){
            throw new BizException("yogaPoseLevel not found");
        }
        check(yogaPoseLevelReq, id);
        ProjYogaPoseLevel poseLevel = new ProjYogaPoseLevel();
        BeanUtils.copyProperties(yogaPoseLevelReq, poseLevel);
        poseLevel.setProjId(projId);
        updateById(poseLevel);
        projYogaPoseLevelGroupRelationService.deleteByProjYogaPoseLevelId(poseLevel.getId());
        saveLevelGroupRelation(yogaPoseLevelReq.getProjYogaPoseGroupIds(), poseLevel);
    }


    @Override
    public ProjYogaPoseLevelDetailVO findDetailById(Integer id) {
        ProjYogaPoseLevel yogaPoseLevel = baseMapper.selectById(id);
        if (null == yogaPoseLevel) {
            return null;
        }
        ProjYogaPoseLevelDetailVO detailVO = new ProjYogaPoseLevelDetailVO();
        BeanUtils.copyProperties(yogaPoseLevel, detailVO);
        List<ProjYogaPoseGroupListVO> poseGroupList = projYogaPoseGroupService.findByPoseLevelId(id);
        detailVO.setProjYogaPoseGroupList(poseGroupList);
        return detailVO;
    }

    @Transactional
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)){
            return;
        }
        LambdaUpdateWrapper<ProjYogaPoseLevel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseLevel::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseLevel::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaPoseLevel::getId, idList);
        update(new ProjYogaPoseLevel(), wrapper);
    }

    @Transactional
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjYogaPoseLevel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseLevel::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaPoseLevel::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseLevel::getId, idList);
        this.update(new ProjYogaPoseLevel(), wrapper);
    }

    @Override
    public PageRes<ProjYogaPoseLevelListVO> page(ProjYogaPoseLevelPageReq pageReq, Integer projId) {
        Integer status = pageReq.getStatus();
        String name = pageReq.getName();
        String difficulty = pageReq.getDifficulty();
        LambdaQueryWrapper<ProjYogaPoseLevel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseLevel::getProjId, projId)
                .like(StringUtils.isNotBlank(name), ProjYogaPoseLevel::getName, name)
                .eq(null != status, ProjYogaPoseLevel::getStatus, status)
                .eq(StringUtils.isNotBlank(difficulty), ProjYogaPoseLevel::getDifficulty, difficulty)
                .orderByDesc(BaseModel::getCreateTime);
        Page<ProjYogaPoseLevel> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaPoseLevel> workoutPage = page(page, wrapper);
        PageRes<ProjYogaPoseLevelListVO> pageRes = PageConverter.convert(workoutPage, ProjYogaPoseLevelListVO.class);
        List<ProjYogaPoseLevelListVO> poseLevelList = pageRes.getList();
        if (CollUtil.isEmpty(poseLevelList)) {
            return pageRes;
        }
        Set<Integer> poseLevelIdSet = poseLevelList.stream().map(ProjYogaPoseLevelListVO::getId).collect(Collectors.toSet());
        List<CountBO> poseGroupCountList = projYogaPoseGroupService.findCount(null, poseLevelIdSet);
        List<CountBO> poseGroupEnabledCountList = projYogaPoseGroupService.findCount(GlobalConstant.STATUS_ENABLE, poseLevelIdSet);
        Map<Integer, List<CountBO>> poseGroupCountGroup = poseGroupCountList.stream().collect(Collectors.groupingBy(CountBO::getId));
        Map<Integer, List<CountBO>> poseGroupEnabledCountGroup = poseGroupEnabledCountList.stream().collect(Collectors.groupingBy(CountBO::getId));
        for (ProjYogaPoseLevelListVO poseLevel : poseLevelList) {
            Integer id = poseLevel.getId();
            List<CountBO> groupCountList = poseGroupCountGroup.get(id);
            if (CollUtil.isNotEmpty(groupCountList)) {
                CountBO poseGroupCount = groupCountList.get(GlobalConstant.ZERO);
                poseLevel.setPoseGroupCount(poseGroupCount.getCount());
            }
            List<CountBO> groupEnabledCountList = poseGroupEnabledCountGroup.get(id);
            if (CollUtil.isNotEmpty(groupEnabledCountList)) {
                CountBO poseGroupEnabledCount = groupEnabledCountList.get(GlobalConstant.ZERO);
                poseLevel.setPoseEnabledGroupCount(poseGroupEnabledCount.getCount());
            }
        }
        return pageRes;
    }

    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjYogaPoseLevel> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseLevel::getDelFlag, GlobalConstant.YES)
                .eq(ProjYogaPoseLevel::getStatus, GlobalConstant.STATUS_DRAFT)
                .in(ProjYogaPoseLevel::getId, idList);
        this.update(new ProjYogaPoseLevel(), wrapper);
    }

    private void check(ProjYogaPoseLevelAddReq yogaPoseLevelAddReq, Integer id) {
        LambdaQueryWrapper<ProjYogaPoseLevel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseLevel::getName, yogaPoseLevelAddReq.getName())
                .ne(null != id, BaseModel::getId, id);
        if (baseMapper.selectCount(wrapper) > 0) {
            throw new BizException("name already exists");
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseLevel::getEventName, yogaPoseLevelAddReq.getEventName())
                .ne(null != id, BaseModel::getId, id);
        if (baseMapper.selectCount(wrapper) > 0) {
            throw new BizException("eventName already exists");
        }
    }

    private void saveLevelGroupRelation(List<Integer> yogaPoseGroupIds, ProjYogaPoseLevel poseLevel) {
        if(CollUtil.isEmpty(yogaPoseGroupIds)){
            return;
        }
        List<ProjYogaPoseLevelGroupRelation> levelGroupRelationList = new ArrayList<>(yogaPoseGroupIds.size());
        Integer id = poseLevel.getId();
        for (Integer groupId : yogaPoseGroupIds) {
            ProjYogaPoseLevelGroupRelation levelGroupRelation = new ProjYogaPoseLevelGroupRelation();
            levelGroupRelation.setProjYogaPoseLevelId(id);
            levelGroupRelation.setProjYogaPoseGroupId(groupId);
            levelGroupRelationList.add(levelGroupRelation);
        }
        projYogaPoseLevelGroupRelationService.saveBatch(levelGroupRelationList);
    }
}
