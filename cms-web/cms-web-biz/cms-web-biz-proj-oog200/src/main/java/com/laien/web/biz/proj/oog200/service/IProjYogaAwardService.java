package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAward;
import com.laien.web.biz.proj.oog200.request.ProjYogaAwardPageReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaAwardPageVO;
import com.laien.web.frame.response.PageRes;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/9
 */
public interface IProjYogaAwardService extends IService<ProjYogaAward> {

    PageRes<ProjYogaAwardPageVO> pageQuery(ProjYogaAwardPageReq awardPageReq);

    void awardManage();

    List<String> batchImport(MultipartFile file);

    void delete(Collection<Integer> ids);
}
