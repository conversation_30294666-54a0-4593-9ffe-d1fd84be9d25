package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoAddReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjWallPilatesVideoBO extends ProjWallPilatesVideoAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    private List<String> targetList;

    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String position;

    @ApiModelProperty(value = "动作类型 ： Warm Up、Main 、Cool Down")
    private String type;

    @ApiModelProperty(value = "当前动作方向 Left、Right、Central")
    private String direction;

    @ApiModelProperty(value = "关联左右动作id")
    private Integer leftRightId;

    @ApiModelProperty(value = "关联左右动作")
    private ProjWallPilatesVideoBO leftRightVideo;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "front  2532")
    private String frontM3u8Text2532;

    @ApiModelProperty(value = "front 2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @ApiModelProperty(value = "front 1080对应的m3u8内容")
    private String frontM3u8Text1080p;

    @ApiModelProperty(value = "front 720对应的m3u8内容")
    private String frontM3u8Text720p;

    @ApiModelProperty(value = "front 480对应的m3u8内容")
    private String frontM3u8Text480p;

    @ApiModelProperty(value = "front 360对应的m3u8内容")
    private String frontM3u8Text360p;

    @ApiModelProperty(value = "side  2532")
    private String sideM3u8Text2532;

    @ApiModelProperty(value = "side 2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @ApiModelProperty(value = "side 1080对应的m3u8内容")
    private String sideM3u8Text1080p;

    @ApiModelProperty(value = "side 720对应的m3u8内容")
    private String sideM3u8Text720p;

    @ApiModelProperty(value = "side 480对应的m3u8内容")
    private String sideM3u8Text480p;

    @ApiModelProperty(value = "side 360对应的m3u8内容")
    private String sideM3u8Text360p;
}
