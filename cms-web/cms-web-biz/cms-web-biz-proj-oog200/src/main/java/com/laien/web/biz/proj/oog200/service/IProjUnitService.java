package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjUnit;
import com.laien.web.biz.proj.oog200.response.ProjUnitVO;

import java.util.List;

/**
 * <p>
 * unit 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface IProjUnitService extends IService<ProjUnit> {

    List<ProjUnitVO> query(Integer projId);
}
