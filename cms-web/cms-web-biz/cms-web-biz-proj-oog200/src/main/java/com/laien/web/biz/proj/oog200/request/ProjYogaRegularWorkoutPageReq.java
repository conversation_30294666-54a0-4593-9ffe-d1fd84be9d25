package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.YogaDataSourceEnum;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: YogaRegularWorkout分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "YogaRegularWorkout分页", description = "YogaRegularWorkout分页")
public class ProjYogaRegularWorkoutPageReq extends PageReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "yogaDataSource")
    private YogaDataSourceEnum yogaDataSource;

    @ApiModelProperty(value = "categoryId")
    private Integer categoryId;

}
