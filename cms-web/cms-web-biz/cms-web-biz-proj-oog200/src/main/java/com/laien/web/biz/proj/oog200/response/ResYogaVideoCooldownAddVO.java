package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yoga video cooldown
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video cooldown", description = "yoga video cooldown")
public class ResYogaVideoCooldownAddVO {

    @ApiModelProperty(value = "下一个视频id")
    private Integer id;

    @ApiModelProperty(value = "下一个视频名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

}
