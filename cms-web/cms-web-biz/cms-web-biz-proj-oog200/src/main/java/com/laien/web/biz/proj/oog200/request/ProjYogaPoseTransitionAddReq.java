package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/8/1 11:23
 */
@Data
@ApiModel(value = "proj yoga pose transition新增")
public class ProjYogaPoseTransitionAddReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "图片")
    private String imageUrl;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

}
