package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramCategory;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramCategoryUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramCategoryDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramCategoryPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramCategoryService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:13
 */
@Api(tags = "项目管理: yogaProgramCategory")
@RestController
@RequestMapping("/proj/yogaProgramCategory")
public class ProjYogaProgramCategoryController extends ResponseController {

    @Resource
    private IProjYogaProgramCategoryService programCategoryService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaProgramCategoryPageVO>> page(ProjYogaProgramCategoryPageReq pageReq) {
        PageRes<ProjYogaProgramCategoryPageVO> pageRes = programCategoryService.selectProgramPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaProgramCategoryAddReq addReq) {
        programCategoryService.saveProgram(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaProgramCategoryUpdateReq updateReq) {
        programCategoryService.updateProgram(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaProgramCategoryDetailVO> detail(@PathVariable Integer id) {
        ProjYogaProgramCategoryDetailVO detailVO = programCategoryService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programCategoryService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programCategoryService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programCategoryService.deleteByIds(idList);
        return succ();
    }

}
