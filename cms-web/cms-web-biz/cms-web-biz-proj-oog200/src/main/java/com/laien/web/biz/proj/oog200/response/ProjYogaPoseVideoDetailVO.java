package com.laien.web.biz.proj.oog200.response;

import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoAudioDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/7/31 14:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ProjYogaPoseVideoDetailVO")
public class ProjYogaPoseVideoDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "English Voice Name")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "动作类型 单选 Begin、Main")
    private String poseType;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "动作朝向，0 -> Central、1 -> Left、2 -> Right")
    private String poseDirection;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "rightVideo 信息, 只有Left机位的才有")
    private ProjBaseDetailVO rightVideoDetail;

    @ApiModelProperty(value = "yoga pose next video 信息")
    private List<ProjYogaPoseVideoConnectionDetailVO> nextVideoList;

    @ApiModelProperty(value = "音频列表，播放三轮、每一轮中含有六个音频")
    private List<ProjYogaPoseVideoAudioDetailVO> audioRoundList;
}
