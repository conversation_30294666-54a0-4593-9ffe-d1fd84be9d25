package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjChairYogaAutoWorkoutVideoRelationMapper;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaAutoWorkoutVideoRelationService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/29 11:44
 */
@Slf4j
@Service
public class ProjChairYogaAutoWorkoutVideoRelationServiceImpl extends ServiceImpl<ProjChairYogaAutoWorkoutVideoRelationMapper, ProjChairYogaAutoWorkoutVideoRelation> implements IProjChairYogaAutoWorkoutVideoRelationService {

    @Resource
    ProjChairYogaAutoWorkoutVideoRelationMapper relationMapper;

    @Override
    public void deleteByChairYogaAutoWorkoutIds(Collection<Integer> workoutIds) {

        LambdaUpdateWrapper<ProjChairYogaAutoWorkoutVideoRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjChairYogaAutoWorkoutVideoRelation::getDelFlag, GlobalConstant.YES);
        wrapper.set(ProjChairYogaAutoWorkoutVideoRelation::getUpdateTime, LocalDateTime.now());

        wrapper.set(ProjChairYogaAutoWorkoutVideoRelation::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.in(ProjChairYogaAutoWorkoutVideoRelation::getProjChairYogaAutoWorkoutId, workoutIds);
        this.update(new ProjChairYogaAutoWorkoutVideoRelation(), wrapper);
    }

    @Override
    public List<ProjChairYogaVideoPageVO> listRelationByWorkoutId(Integer workoutId) {

        LambdaQueryWrapper<ProjChairYogaAutoWorkoutVideoRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjChairYogaAutoWorkoutVideoRelation::getProjChairYogaAutoWorkoutId, workoutId);

        List<ProjChairYogaVideoPageVO> videoList = relationMapper.listRelationAndVideo(workoutId);
        return videoList;
    }

    @Override
    public IPage<ProjChairYogaAutoWorkoutVideoRelation> page(ProjChairYogaAutoWorkoutPageReq pageReq) {

        LambdaQueryWrapper<ProjChairYogaAutoWorkoutVideoRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjChairYogaAutoWorkoutVideoRelation::getProjChairYogaAutoWorkoutId, pageReq.getId());

        Page<ProjChairYogaAutoWorkoutVideoRelation> queryPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjChairYogaAutoWorkoutVideoRelation> resultPage = this.page(queryPage, queryWrapper);
        return resultPage;
    }

}
