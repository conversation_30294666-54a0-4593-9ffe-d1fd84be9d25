package com.laien.web.biz.proj.oog200.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.common.oog200.enums.PoseDirectionEnum;
import com.laien.common.oog200.enums.TargetEnum;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesVideoBO;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesVideo;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesVideoResource;
import com.laien.web.biz.proj.oog200.mapper.ProjWallPilatesVideoMapper;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoImportReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoLeftRightDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesVideoResourceService;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesVideoService;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.web.biz.proj.oog200.constant.WallPilatesConstant.WALL_PILATES_VIDEO_M3U8;
import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_WALL_PILATES_VIDEO_RESOURCE_FRONT;
import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_WALL_PILATES_VIDEO_RESOURCE_SIDE;

/**
 * <p>
 * wall pilates video资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Service
public class ProjWallPilatesVideoServiceImpl extends ServiceImpl<ProjWallPilatesVideoMapper, ProjWallPilatesVideo> implements IProjWallPilatesVideoService {

    @Resource
    private IProjWallPilatesVideoResourceService projWallPilatesVideoResourceService;

    @Resource
    private FileService fileService;

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @Resource
    private Validator validator;

    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Resource
    private ICoreVoiceConfigI18nService i18nConfigService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjWallPilatesVideoAddReq videoReq, Integer projId) {
        check(videoReq, null);
        ProjWallPilatesVideo video = new ProjWallPilatesVideo();
        BeanUtils.copyProperties(videoReq, video);
        video.setProjId(projId);
        video.setStatus(GlobalConstant.STATUS_DRAFT);
        video.setVideo2532Url(videoFrontFor2532M3u8(videoReq.getFrontVideoUrl(), videoReq.getFrontVideoDuration()));
        video.setTarget(MyStringUtil.getJoinWithComma(videoReq.getTargetArr()));
        save(video);
        projWallPilatesVideoResourceService.save(
                video.getId(),
                videoReq.getFrontVideoUrl(), videoReq.getFrontVideoDuration(),
                videoReq.getSideVideoUrl(), videoReq.getSideVideoDuration()
        );
        lmsI18nService.handleI18n(Collections.singletonList(video), projId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        List<ProjWallPilatesVideoResource> resourceList = projWallPilatesVideoResourceService.findByWallPilatesVideoIdList(idList);
        if(CollUtil.isEmpty(resourceList)){
            return idList;
        }
        Map<Integer, ProjWallPilatesVideoResource> videoIdKeyMap = resourceList.stream()
                .collect(Collectors.toMap(ProjWallPilatesVideoResource::getProjWallPilatesVideoId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );

        List<Integer> resourceIdList = resourceList.stream().map(BaseModel::getId).collect(Collectors.toList());
        List<TaskResourceSection> taskList = taskResourceSectionService.query(PROJ_WALL_PILATES_VIDEO_RESOURCE_FRONT.getTableName(),PROJ_WALL_PILATES_VIDEO_RESOURCE_FRONT.getEntityFieldName(), resourceIdList);
        if (CollUtil.isEmpty(taskList)) {
            return idList;
        }
        List<Integer> resourceCompletedIdList = TaskResourceSectionUtil.getCompletedIdList(taskList);
        if (CollUtil.isEmpty(resourceCompletedIdList)) {
            return idList;
        }
        // key：resourceId,value: videoId
        Map<Integer, Integer> resourceIdKeyMap = new HashMap<>();
        for (ProjWallPilatesVideoResource resource : resourceList) {
            resourceIdKeyMap.put(resource.getId(), resource.getProjWallPilatesVideoId());
        }
        List<Integer> videoCompletedIdList = new ArrayList<>(resourceCompletedIdList.size());
        for (Integer resourceId : resourceCompletedIdList) {
            Integer videoId = resourceIdKeyMap.get(resourceId);
            if(null != videoId){
                videoCompletedIdList.add(videoId);
            }
        }
        LambdaQueryWrapper<ProjWallPilatesVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ProjWallPilatesVideo::getId, ProjWallPilatesVideo::getDirection, ProjWallPilatesVideo::getLeftRightId)
                .in(BaseModel::getId, videoCompletedIdList)
                .in(ProjWallPilatesVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        List<ProjWallPilatesVideo> videoList = baseMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(videoList)){
            return null;
        }

        List<ProjWallPilatesVideo> videoUpdateList = new ArrayList<>(videoList.size());
        // video的正侧机位数据库按照1对多设计，目前只能选一个，所以直接用resource的videoUrl
        videoList.forEach(item -> {
            item.setStatus(GlobalConstant.STATUS_ENABLE);
            ProjWallPilatesVideoResource resource = videoIdKeyMap.get(item.getId());
            item.setVideoUrl(resource.getVideoUrl());
            String direction = item.getDirection();
            if (!PoseDirectionEnum.LEFT.getPoseDirection().equals(direction) || null != item.getLeftRightId()) {
                videoUpdateList.add(item);
            }
        });
        List<Integer> updateIdList = videoUpdateList.stream().map(BaseModel::getId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(videoUpdateList)){
            updateBatchById(videoUpdateList);
        }
        idList.removeAll(updateIdList);
        return idList;
    }

    @Override
    public ProjWallPilatesVideoDetailVO findDetailById(Integer id) {
        ProjWallPilatesVideo video = baseMapper.selectById(id);
        if (null == video) {
            return null;
        }
        ProjWallPilatesVideoDetailVO detailVO = new ProjWallPilatesVideoDetailVO();
        BeanUtils.copyProperties(video, detailVO);
        Integer leftRightId = video.getLeftRightId();
        if (PoseDirectionEnum.LEFT.getPoseDirection().equals(video.getDirection()) && null != leftRightId) {
            ProjWallPilatesVideo leftRightVideo = baseMapper.selectById(leftRightId);
            if(null != leftRightVideo){
                ProjWallPilatesVideoLeftRightDetailVO leftRightDetailVO = new ProjWallPilatesVideoLeftRightDetailVO();
                BeanUtils.copyProperties(leftRightVideo, leftRightDetailVO);
                detailVO.setLeftRightDetail(leftRightDetailVO);
            }
        }
        List<ProjWallPilatesVideoResource> resourceList = projWallPilatesVideoResourceService.findByWallPilatesVideoIdList(Collections.singletonList(id));
        if (CollUtil.isNotEmpty(resourceList)) {
            //目前只支持设置一个ProjWallPilatesVideoResource所以取第0个
            ProjWallPilatesVideoResource resource = resourceList.get(GlobalConstant.ZERO);
            detailVO.setFrontVideoUrl(resource.getFrontVideoUrl())
                    .setFrontVideoDuration(resource.getFrontVideoDuration())
                    .setSideVideoUrl(resource.getSideVideoUrl())
                    .setSideVideoDuration(resource.getSideVideoDuration())
                    .setTargetArr(MyStringUtil.getSplitWithComa(video.getTarget()));
        }

        fillI18nConfigInfoForDetail(detailVO);
        return detailVO;
    }

    private void fillI18nConfigInfoForDetail(ProjWallPilatesVideoDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjWallPilatesVideoUpdateReq videoReq, Integer projId) {
        Integer id = videoReq.getId();
        ProjWallPilatesVideo video = baseMapper.selectById(id);
        if (null == video) {
            throw new BizException("video not found");
        }
        ProjWallPilatesVideo updateVideo = new ProjWallPilatesVideo();
        check(videoReq, id);
        BeanUtils.copyProperties(videoReq, updateVideo);
        updateVideo.setProjId(projId);
        updateVideo.setVideo2532Url(videoFrontFor2532M3u8(videoReq.getFrontVideoUrl(), videoReq.getFrontVideoDuration()));
        updateVideo.setTarget(MyStringUtil.getJoinWithComma(videoReq.getTargetArr()));
        updateById(updateVideo);
        projWallPilatesVideoResourceService.save(
                video.getId(),
                videoReq.getFrontVideoUrl(), videoReq.getFrontVideoDuration(),
                videoReq.getSideVideoUrl(), videoReq.getSideVideoDuration()
        );
        if(!PoseDirectionEnum.LEFT.getPoseDirection().equals(updateVideo.getDirection()) && null != video.getLeftRightId()){
            LambdaUpdateWrapper<ProjWallPilatesVideo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BaseModel::getId, video.getId()).set(ProjWallPilatesVideo::getLeftRightId, null);
            baseMapper.update(null, updateWrapper);
        }
        lmsI18nService.handleI18n(Collections.singletonList(updateVideo), projId);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWallPilatesVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesVideo::getStatus, GlobalConstant.STATUS_DISABLE)
        .eq(ProjWallPilatesVideo::getStatus, GlobalConstant.STATUS_ENABLE)
        .in(ProjWallPilatesVideo::getId, idList);
        this.update(new ProjWallPilatesVideo(), wrapper);
    }

    @Override
    public List<ProjWallPilatesVideoListVO> findByWallPilatesAutoWorkoutId(Integer wallPilatesAutoWorkoutId) {
        List<ProjWallPilatesVideoListVO> videoList = baseMapper.find(Collections.singleton(wallPilatesAutoWorkoutId));
        handleProjWallPilatesVideoListVO(videoList);
        return videoList;
    }

    @Override
    public List<ProjWallPilatesVideoListVO> findByWallPilatesAutoWorkoutIdSet(Set<Integer> wallPilatesAutoWorkoutIdSet) {
        List<ProjWallPilatesVideoListVO> videoList = baseMapper.find(wallPilatesAutoWorkoutIdSet);
        handleProjWallPilatesVideoListVO(videoList);
        return videoList;
    }

    @Override
    public List<ProjWallPilatesVideoListVO> findByWallPilatesRegularWorkoutIdSet(Set<Integer> wallPilatesRegularWorkoutIdSet) {
        List<ProjWallPilatesVideoListVO> videoList = baseMapper.findByWallPilatesRegularWorkoutIdSet(wallPilatesRegularWorkoutIdSet);
        handleProjWallPilatesVideoListVO(videoList);
        return videoList;
    }

    @Override
    public List<ProjWallPilatesVideoBO> queryList() {
        LambdaQueryWrapper<ProjWallPilatesVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWallPilatesVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjWallPilatesVideo> videoList = baseMapper.selectList(wrapper);
        List<ProjWallPilatesVideoBO> videoBOList = new ArrayList<>(videoList.size());
        if (CollUtil.isEmpty(videoList)) {
            return videoBOList;
        }
        List<Integer> videoIdList = videoList.stream().map(BaseModel::getId).collect(Collectors.toList());
        List<ProjWallPilatesVideoResource> resourceList = projWallPilatesVideoResourceService.findByWallPilatesVideoIdList(videoIdList);
        Map<Integer, ProjWallPilatesVideoResource> resourceMap = resourceList.stream().collect(
                Collectors.toMap(ProjWallPilatesVideoResource::getProjWallPilatesVideoId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        videoList.forEach(video -> {
            ProjWallPilatesVideoBO videoBO = new ProjWallPilatesVideoBO();
            BeanUtils.copyProperties(video, videoBO);
            ProjWallPilatesVideoResource resource = resourceMap.get(video.getId());
            videoBO.setFrontVideoUrl(resource.getFrontVideoUrl())
                    .setFrontVideoDuration(resource.getFrontVideoDuration())
                    .setFrontM3u8Text2532(resource.getFrontM3u8Text2532())
                    .setFrontM3u8Text2k(resource.getFrontM3u8Text2k())
                    .setFrontM3u8Text1080p(resource.getFrontM3u8Text1080p())
                    .setFrontM3u8Text720p(resource.getFrontM3u8Text720p())
                    .setFrontM3u8Text480p(resource.getFrontM3u8Text480p())
                    .setFrontM3u8Text360p(resource.getFrontM3u8Text360p())
                    .setSideVideoUrl(resource.getSideVideoUrl())
                    .setSideVideoDuration(resource.getSideVideoDuration())
                    .setSideM3u8Text2532(resource.getSideM3u8Text2532())
                    .setSideM3u8Text2k(resource.getSideM3u8Text2k())
                    .setSideM3u8Text1080p(resource.getSideM3u8Text1080p())
                    .setSideM3u8Text720p(resource.getSideM3u8Text720p())
                    .setSideM3u8Text480p(resource.getSideM3u8Text480p())
                    .setSideM3u8Text360p(resource.getSideM3u8Text360p());
            videoBOList.add(videoBO);
        });
        Map<Integer, ProjWallPilatesVideoBO> videoBoMap = videoBOList.stream().collect(
                Collectors.toMap(ProjWallPilatesVideoBO::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        videoBOList.forEach(video -> {
            if (PoseDirectionEnum.LEFT.getPoseDirection().equals(video.getDirection())) {
                video.setLeftRightVideo(videoBoMap.get(video.getLeftRightId()));
            }
        });
        return videoBOList;
    }

    @Override
    public PageRes<ProjWallPilatesVideoListVO> findPage(Integer wallPilatesAutoWorkoutId, PageReq pageReq) {
        IPage<ProjWallPilatesVideo> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        page = baseMapper.findPage(page, wallPilatesAutoWorkoutId);
        PageRes<ProjWallPilatesVideoListVO> pageVO = PageConverter.convert(page, ProjWallPilatesVideoListVO.class);
        List<ProjWallPilatesVideoListVO> videoList = pageVO.getList();
        if (CollUtil.isEmpty(videoList)) {
            return pageVO;
        }
        handleProjWallPilatesVideoListVO(videoList);
        return pageVO;
    }

    @Override
    public List<ProjWallPilatesVideoBO> queryList(List<Integer> videoIdList) {
        if (CollUtil.isEmpty(videoIdList)) {
            return new ArrayList<>();
        }
        List<ProjWallPilatesVideoBO> videoList = baseMapper.queryByIdList(videoIdList);

        List<ProjWallPilatesVideoBO> finalVideoList = new ArrayList<>(videoList.size());
        List<ProjWallPilatesVideoResource> resourceList = projWallPilatesVideoResourceService.findByWallPilatesVideoIdList(videoIdList);
        Map<Integer, ProjWallPilatesVideoResource> resourceMap = resourceList.stream().collect(
                Collectors.toMap(ProjWallPilatesVideoResource::getProjWallPilatesVideoId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        Map<Integer, ProjWallPilatesVideoBO> videoBoMap = videoList.stream().collect(
                Collectors.toMap(ProjWallPilatesVideoBO::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        for (Integer id : videoIdList) {
            ProjWallPilatesVideoBO video = videoBoMap.get(id);
            finalVideoList.add(video);
            if (PoseDirectionEnum.LEFT.getPoseDirection().equals(video.getDirection())) {
                video.setLeftRightVideo(videoBoMap.get(video.getLeftRightId()));
            }

            ProjWallPilatesVideoResource resource = resourceMap.get(video.getId());
            video.setFrontVideoUrl(resource.getFrontVideoUrl())
                    .setFrontVideoDuration(resource.getFrontVideoDuration())
                    .setFrontM3u8Text2532(resource.getFrontM3u8Text2532())
                    .setFrontM3u8Text2k(resource.getFrontM3u8Text2k())
                    .setFrontM3u8Text1080p(resource.getFrontM3u8Text1080p())
                    .setFrontM3u8Text720p(resource.getFrontM3u8Text720p())
                    .setFrontM3u8Text480p(resource.getFrontM3u8Text480p())
                    .setFrontM3u8Text360p(resource.getFrontM3u8Text360p())
                    .setSideVideoUrl(resource.getSideVideoUrl())
                    .setSideVideoDuration(resource.getSideVideoDuration())
                    .setSideM3u8Text2532(resource.getSideM3u8Text2532())
                    .setSideM3u8Text2k(resource.getSideM3u8Text2k())
                    .setSideM3u8Text1080p(resource.getSideM3u8Text1080p())
                    .setSideM3u8Text720p(resource.getSideM3u8Text720p())
                    .setSideM3u8Text480p(resource.getSideM3u8Text480p())
                    .setSideM3u8Text360p(resource.getSideM3u8Text360p());
        }
        return finalVideoList;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importByExcel(InputStream inputStream, Integer projId) {
        String colon = ":";
        String nameExistsError = " name already exists";
        String eventNameExistsError = " event name already exists";
        String targetExistsError = "body target illegal";
        List<ProjWallPilatesVideoImportReq> videoListReq = CollUtil.newArrayList();
        Set<String> allTargetSet = Arrays.stream(TargetEnum.values()).map(TargetEnum::getName).collect(Collectors.toSet());
        EasyExcel.read(inputStream, ProjWallPilatesVideoImportReq.class, new AnalysisEventListener<ProjWallPilatesVideoImportReq>() {
            @Override
            public void invoke(ProjWallPilatesVideoImportReq row, AnalysisContext analysisContext) {
                videoListReq.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        if (CollUtil.isEmpty(videoListReq)) {
            return null;
        }
        Set<String> nameSet = videoListReq.stream().map(ProjWallPilatesVideoImportReq::getName).collect(Collectors.toSet());
        if (nameSet.size() < videoListReq.size()) {
            throw new BizException("wall pilates video import excel has repeat name, do not update any data");
        }

        Set<String> eventNameSet = videoListReq.stream().map(ProjWallPilatesVideoImportReq::getEventName).collect(Collectors.toSet());
        if (eventNameSet.size() < videoListReq.size()) {
            throw new BizException("wall pilates video import excel has repeat event name, do not update any data");
        }

        List<String> failMessage = CollUtil.newArrayList();
        List<ProjWallPilatesVideo> videoList = list();

        Map<String, ProjWallPilatesVideo> videoNameMap = videoList.stream().collect(
                Collectors.toMap(ProjWallPilatesVideo::getName,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        Map<String, ProjWallPilatesVideo> videoEventNameMap = videoList.stream().collect(
                Collectors.toMap(ProjWallPilatesVideo::getEventName,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );

        Set<String> i18nConfigNameSet = videoListReq.stream().map(ProjWallPilatesVideoImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
        Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));

        List<ProjWallPilatesVideo> addVideoList = new ArrayList<>(videoListReq.size());
        List<ProjWallPilatesVideoResource> addVideoResourceList = new ArrayList<>(videoListReq.size());
        for (ProjWallPilatesVideoImportReq videoReq : videoListReq) {
            String name = videoReq.getName();
            Set<ConstraintViolation<ProjWallPilatesVideoImportReq>> violationSet = validator.validate(videoReq, Group.class);
            if (CollUtil.isNotEmpty(violationSet)) {
                violationSet.stream().findFirst().ifPresent(violation -> failMessage.add(name + colon + violation.getMessage()));
                continue;
            }

            if (!i18nConfigNameIdMap.containsKey(videoReq.getCoreVoiceConfigI18nName())) {
                failMessage.add(videoReq.getName() + ": English Voice Name Not Found in TTS config");
                continue;
            } else {
                videoReq.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(videoReq.getCoreVoiceConfigI18nName()));
            }

            ProjWallPilatesVideo wallPilatesVideo = videoNameMap.get(name);
            if (null != wallPilatesVideo) {
                failMessage.add(name + colon + nameExistsError);
                continue;
            }
            wallPilatesVideo = videoEventNameMap.get(name);
            if (null != wallPilatesVideo) {
                failMessage.add(name + colon + eventNameExistsError);
                continue;
            }
            Set<String> targetSet = new HashSet<>();
            Arrays.stream(MyStringUtil.getSplitWithComa(videoReq.getTarget())).forEach(target -> {
                targetSet.add(target.trim());
            });
            videoReq.setTarget(CollUtil.join(targetSet, GlobalConstant.COMMA));
            if(CollUtil.intersection(targetSet, allTargetSet).size() != targetSet.size()){
                failMessage.add(name + colon + targetExistsError);
                continue;
            }

            ProjWallPilatesVideo video = new ProjWallPilatesVideo();
            BeanUtils.copyProperties(videoReq, video);
            video.setStatus(GlobalConstant.STATUS_DRAFT);
            video.setEventName(name);
            video.setProjId(projId);
            video.setVideo2532Url(videoFrontFor2532M3u8(videoReq.getFrontVideoUrl(), videoReq.getFrontVideoDuration()));
            addVideoList.add(video);
        }
        if(CollUtil.isEmpty(addVideoList)){
            return failMessage;
        }
        saveBatch(addVideoList);

        Map<String, ProjWallPilatesVideoImportReq> videoReqMap = videoListReq.stream()
                .collect(Collectors.toMap(ProjWallPilatesVideoImportReq::getName,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );
        for (ProjWallPilatesVideo video : addVideoList) {
            ProjWallPilatesVideoImportReq videoReq = videoReqMap.get(video.getName());
            ProjWallPilatesVideoResource resource = new ProjWallPilatesVideoResource();
            resource.setProjWallPilatesVideoId(video.getId())
                    .setFrontVideoUrl(videoReq.getFrontVideoUrl())
                    .setFrontVideoDuration(videoReq.getFrontVideoDuration())
                    .setSideVideoDuration(videoReq.getSideVideoDuration())
                    .setSideVideoUrl(videoReq.getSideVideoUrl());
            addVideoResourceList.add(resource);
        }
        projWallPilatesVideoResourceService.saveBatch(addVideoResourceList);

        lmsI18nService.handleI18n(addVideoList, projId);
        return failMessage;
    }

    @Override
    public PageRes<ProjWallPilatesVideoListVO> page(ProjWallPilatesVideoPageReq pageReq, Integer projId) {
        String target = pageReq.getTarget();
        String type = pageReq.getType();
        String direction = pageReq.getDirection();
        Integer status = pageReq.getStatus();
        String position = pageReq.getPosition();
        String name = pageReq.getName();
        LambdaQueryWrapper<ProjWallPilatesVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWallPilatesVideo::getProjId, projId)
                .like(StringUtils.isNotBlank(name), ProjWallPilatesVideo::getName, name)
                .like(StringUtils.isNotBlank(target), ProjWallPilatesVideo::getTarget, target)
                .eq(StringUtils.isNotBlank(position), ProjWallPilatesVideo::getPosition, position)
                .eq(null != status, ProjWallPilatesVideo::getStatus, status)
                .eq(StringUtils.isNotBlank(type), ProjWallPilatesVideo::getType, type)
                .eq(StringUtils.isNotBlank(direction), ProjWallPilatesVideo::getDirection, direction);
        if (StringUtils.isNotBlank(target)) {
            wrapper.last("order by LENGTH(target) asc, id desc");
        } else {
            wrapper.orderByDesc(BaseModel::getId);
        }
        Page<ProjWallPilatesVideo> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjWallPilatesVideo> videoPage = page(page, wrapper);
        PageRes<ProjWallPilatesVideoListVO> pageVO = PageConverter.convert(videoPage, ProjWallPilatesVideoListVO.class);
        List<ProjWallPilatesVideoListVO> videoList = pageVO.getList();
        if(CollUtil.isEmpty(videoList)){
            return pageVO;
        }
        handleProjWallPilatesVideoListVO(videoList);
        return pageVO;
    }

    private void handleProjWallPilatesVideoListVO(List<ProjWallPilatesVideoListVO> videoList) {
        List<Integer> videoIdList = videoList.stream().map(ProjWallPilatesVideoListVO::getId).collect(Collectors.toList());

        List<ProjWallPilatesVideoResource> resourceList = projWallPilatesVideoResourceService.findByWallPilatesVideoIdList(videoIdList);
        Map<Integer, ProjWallPilatesVideoResource> videoResourceMap = resourceList.stream()
                .collect(Collectors.toMap(ProjWallPilatesVideoResource::getProjWallPilatesVideoId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );
        for (ProjWallPilatesVideoListVO video : videoList) {
            video.setTargetArr(MyStringUtil.getSplitWithComa(video.getTarget()));
            ProjWallPilatesVideoResource resource = videoResourceMap.get(video.getId());
            if(null != resource) {
                Integer frontVideoDuration = resource.getFrontVideoDuration();
                Integer sideVideoDuration = resource.getSideVideoDuration();
                Integer duration = frontVideoDuration * 3 + sideVideoDuration;
                video.setProjWallPilatesVideoResourceId(resource.getId())
                        .setFrontVideoDuration(frontVideoDuration)
                        .setSideVideoDuration(resource.getSideVideoDuration())
                        .setDuration(duration);
            }
        }
        injectionTaskStatus(videoList);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjWallPilatesVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesVideo::getDelFlag, GlobalConstant.YES)
                .in(ProjWallPilatesVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY)
                .in(ProjWallPilatesVideo::getId, idList);
        this.update(new ProjWallPilatesVideo(), wrapper);
    }

    private String videoFrontFor2532M3u8(String frontVideoUrl, Integer frontDuration) {
        List<TsMergeBO> videoList = new ArrayList<>(GlobalConstant.ONE);
        videoList.add(new TsMergeBO(fileService.getAbsoluteR2Url(frontVideoUrl), frontDuration));
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTSForM3U8R2(videoList, WALL_PILATES_VIDEO_M3U8);
        return videoR2Info.getFileRelativeUrl();
    }

    private void injectionTaskStatus(List<ProjWallPilatesVideoListVO> videoList) {
        if (CollUtil.isEmpty(videoList)) {
            return;
        }
        Set<Integer> resourceIdSet = videoList.stream().map(ProjWallPilatesVideoListVO::getProjWallPilatesVideoResourceId).collect(Collectors.toSet());
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(PROJ_WALL_PILATES_VIDEO_RESOURCE_SIDE.getTableName(),PROJ_WALL_PILATES_VIDEO_RESOURCE_SIDE.getEntityFieldName(), resourceIdSet);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(PROJ_WALL_PILATES_VIDEO_RESOURCE_FRONT.getTableName(),PROJ_WALL_PILATES_VIDEO_RESOURCE_FRONT.getEntityFieldName(), resourceIdSet);
        if (CollUtil.isEmpty(sideStatusList) && CollUtil.isEmpty(frontStatusList)) {
            return;
        }
        sideStatusList = null == sideStatusList ? new ArrayList<>() : sideStatusList;
        frontStatusList = null == frontStatusList ? new ArrayList<>() : frontStatusList;

        Map<Integer, List<TaskResourceSection>> sideMap = sideStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        Map<Integer, List<TaskResourceSection>> frontMap = frontStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        for (ProjWallPilatesVideoListVO video : videoList) {
            Integer resourceId = video.getProjWallPilatesVideoResourceId();
            List<TaskResourceSection> sideList = sideMap.get(resourceId);
            if (CollUtil.isNotEmpty(sideList)) {
                TaskResourceSection sideTask = sideList.get(0);
                video.setSideTaskStatus(sideTask.getStatus());
            }

            List<TaskResourceSection> frontList = frontMap.get(resourceId);
            if (CollUtil.isNotEmpty(frontList)) {
                TaskResourceSection frontTask = frontList.get(0);
                video.setFrontTaskStatus(frontTask.getStatus());
            }
        }

    }

    private void check(ProjWallPilatesVideoAddReq videoReq, Integer id) {
        LambdaQueryWrapper<ProjWallPilatesVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWallPilatesVideo::getName, videoReq.getName())
                .ne(null != id, BaseModel::getId, id);
        List<ProjWallPilatesVideo> videoList = baseMapper.selectList(wrapper);
        Set<String> nameSet = videoList.stream().map(ProjWallPilatesVideo::getName).collect(Collectors.toSet());
        if (nameSet.contains(videoReq.getName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWallPilatesVideo::getEventName, videoReq.getEventName())
                .ne(null != id, BaseModel::getId, id);
        videoList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= videoList.stream().map(ProjWallPilatesVideo::getName).collect(Collectors.toSet());
        if (eventNameSet.contains(videoReq.getEventName())) {
            String error = "eventName already exists";
            throw new BizException(error);
        }
    }
}
