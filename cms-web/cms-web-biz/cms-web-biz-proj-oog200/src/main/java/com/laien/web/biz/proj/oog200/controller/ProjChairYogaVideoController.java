package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaVideoService;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaVideoSliceService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_CHAIR_YOGA_VIDEO_FRONT;

/**
 * Author:  hhl
 * Date:  2024/9/23 15:08
 */
@Api(tags = "项目管理: Chair Yoga Video")
@RestController
@RequestMapping("proj/chairYogaVideo")
public class ProjChairYogaVideoController extends ResponseController {

    @Resource
    IProjChairYogaVideoService chairYogaVideoService;

    @Resource
    IProjChairYogaVideoSliceService videoSliceService;

    @Resource
    ITaskResourceSectionService taskResourceSectionService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjChairYogaVideoPageVO>> page(ProjChairYogaVideoPageReq pageReq) {

        PageRes<ProjChairYogaVideoPageVO> pageRes = chairYogaVideoService.selectChairYogaVideoPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjChairYogaVideoAddReq videoAddReq) {

        chairYogaVideoService.saveChairYogaVideo(videoAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjChairYogaVideoUpdateReq videoUpdateReq) {

        chairYogaVideoService.updateChairYogaVideo(videoUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjChairYogaVideoDetailVO> detail(@PathVariable Integer id) {

        ProjChairYogaVideoDetailVO videoDetailVO = chairYogaVideoService.getDetailById(id);
        return succ(videoDetailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }

        List<Integer> idList = idListReq.getIdList().stream().distinct().collect(Collectors.toList());
        List<ProjChairYogaVideoSliceDetailVO> sliceDetailVOList = videoSliceService.listByChairYogaVideoId(idList);
        if (CollectionUtils.isEmpty(sliceDetailVOList)) {
            return fail("Missing slice for chair yoga video.");
        }

        Set<Integer> chairYogaVideoIds = sliceDetailVOList.stream().map(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId).collect(Collectors.toSet());
        if (!Objects.equals(idList.size(), chairYogaVideoIds.size())) {
            return fail("Missing slice for chair yoga video.");
        }

        List<Integer> sliceIdList = sliceDetailVOList.stream().map(ProjChairYogaVideoSliceDetailVO::getId).distinct().collect(Collectors.toList());
        List<TaskResourceSection> taskSectionList = taskResourceSectionService.query(PROJ_CHAIR_YOGA_VIDEO_FRONT.getTableName(), PROJ_CHAIR_YOGA_VIDEO_FRONT.getEntityFieldName(), sliceIdList);
        if (CollectionUtils.isEmpty(taskSectionList) || taskSectionList.size() / sliceIdList.size() != GlobalConstant.TWO) {
            return fail("It cannot be enabled because video slice processing is not complete.");
        }

        Set<Integer> unCompletedIdList = TaskResourceSectionUtil.listNotCompletedId(taskSectionList);
        if (!CollectionUtils.isEmpty(unCompletedIdList)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }

        chairYogaVideoService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }
        chairYogaVideoService.updateDisableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }
        chairYogaVideoService.deleteByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量导入Chair Yoga Video 信息")
    @PostMapping("/v1/importByExcel")
    public ResponseResult<List<String>> importPoseVideo(@RequestParam("file") MultipartFile excel) {

        return succ(chairYogaVideoService.importChairYogaVideo(excel));
    }

    @ApiOperation(value = "批量导入Chair Yoga Video Slice 信息")
    @PostMapping("/v2/importByExcel")
    public ResponseResult<List<String>> importPoseVideoConnection(@RequestParam("file") MultipartFile excel) {

        return succ(chairYogaVideoService.importChairYogaVideoSlice(excel));
    }

}
