package com.laien.web.biz.proj.oog200.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="Wall pilates auto workout")
public class ProjWallPilatesWorkoutUpdateFileBO {


    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "videoIdList")
    List<Integer> videoIdList;

}
