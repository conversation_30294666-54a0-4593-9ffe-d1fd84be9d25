package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog200.enums.MusicTypeEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音乐表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaMusic对象", description="音乐表")
public class ProjYogaMusicDetailVO extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "音频")
    private String audio;

    @ApiModelProperty(value = "音频总时长,毫秒")
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型（关联字典表）")
    private MusicTypeEnum musicType;

    @ApiModelProperty(value = "讲述者，用于Meditation类型")
    private String instructor;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "Music状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目ID")
    private Integer projId;

}
