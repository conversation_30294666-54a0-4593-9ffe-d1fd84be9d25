package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.oog200.entity.ProjYogaAward;
import com.laien.web.biz.proj.oog200.request.ProjYogaAwardPageReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaAwardPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaAwardService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: hhl
 * @date: 2025/6/10
 */
@Api(tags = "项目管理:Yoga Award")
@RestController
@RequestMapping("/proj/yogaAward")
public class ProjYogaAwardController extends ResponseController {

    @Resource
    private IProjYogaAwardService yogaAwardService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaAwardPageVO>> page(ProjYogaAwardPageReq pageReq) {

        PageRes<ProjYogaAwardPageVO> pageRes = yogaAwardService.pageQuery(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }

        yogaAwardService.delete(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量导入Yoga Award信息")
    @PostMapping("/v1/importByExcel")
    public ResponseResult<List<String>> importAward(@RequestParam("file") MultipartFile excel) {

        return succ(yogaAwardService.batchImport(excel));
    }

}
