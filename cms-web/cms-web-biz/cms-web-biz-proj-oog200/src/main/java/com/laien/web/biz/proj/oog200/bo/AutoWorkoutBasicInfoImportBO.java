package com.laien.web.biz.proj.oog200.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * AutoWorkoutBasicInfoImportBO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="AutoWorkoutBasicInfoImportBO", description="AutoWorkoutBasicInfoImportBO")
public class AutoWorkoutBasicInfoImportBO {

    private Integer id;

    @ExcelProperty(value = "Image Name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "planType")
    private String planTypeValue;

    @ApiModelProperty(value = "图片用途，Upper Body,Abs & Core,Lower Body,Upper Body+Abs & Core,Abs & Core+Lower Body,Fullbody,Learn Yoga Basics,Mindfulness,Weight Loss,Improve Flexibility")
    private String pointValue;

    @ApiModelProperty(value = "Auto Workout ID,多个用英文逗号分隔")
    private String workoutId;

    @ApiModelProperty(value = "Cover Image 封面图")
    private String coverImageMale;

    @ApiModelProperty(value = "Detail Image 详情图")
    private String detailImage;

    @ApiModelProperty(value = "Complete Image")
    private String completeImage;

}
