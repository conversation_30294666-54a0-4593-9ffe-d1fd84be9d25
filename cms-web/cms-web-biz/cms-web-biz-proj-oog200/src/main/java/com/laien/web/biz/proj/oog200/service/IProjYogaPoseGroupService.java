package com.laien.web.biz.proj.oog200.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseGroup;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupListReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj yoga pose grouping 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface IProjYogaPoseGroupService extends IService<ProjYogaPoseGroup> {

    void save(ProjYogaPoseGroupAddReq poseGroupReq, Integer projId);

    void update(ProjYogaPoseGroupUpdateReq poseGroupReq, Integer projId);

    ProjYogaPoseGroupDetailVO findDetailById(Integer id);

    List<ProjYogaPoseGroupListVO> list(ProjYogaPoseGroupListReq listReq, Integer projId);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void sort(IdListReq idListReq);

    List<CountBO> findCount(Integer status, Set<Integer> poseLevelIdSet);

    List<ProjYogaPoseGroupListVO> findByPoseLevelId(Integer poseLevelId);

    void deleteByIdList(List<Integer> idList);
}
