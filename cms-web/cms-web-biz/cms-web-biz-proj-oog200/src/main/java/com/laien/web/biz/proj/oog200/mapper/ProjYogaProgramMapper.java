package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgram;
import com.laien.web.biz.proj.oog200.handler.YogaProgramTypeHandler;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:54
 */
public interface ProjYogaProgramMapper extends BaseMapper<ProjYogaProgram> {

    IPage<ProjYogaProgram> page(@Param("page") Page<ProjYogaProgram> page, Integer status, String name, Integer difficulty, Integer programCategoryId, String programPositionType);

    @Select("<script>" +
            "SELECT * FROM proj_yoga_program " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    @Results({
            @Result(column = "program_position_types", property = "programPositionTypes", typeHandler = YogaProgramTypeHandler.class)
    })
    List<ProjYogaProgram> selectByIds(@Param("ids") List<Integer> ids);

    @Select("SELECT * FROM proj_yoga_program WHERE id = #{id} and del_flag = 0 ")
    @Results({
            @Result(column = "program_position_types", property = "programPositionTypes", typeHandler = YogaProgramTypeHandler.class)
    })
    ProjYogaProgram getEntityById(@Param("id") Integer id);

}
