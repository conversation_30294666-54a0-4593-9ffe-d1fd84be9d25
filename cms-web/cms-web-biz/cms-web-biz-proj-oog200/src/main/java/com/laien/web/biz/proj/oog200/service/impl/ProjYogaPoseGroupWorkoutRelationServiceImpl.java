package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseGroupWorkoutRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseGroupWorkoutRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseGroupWorkoutRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.stereotype.Service;

/**
 * <p>
 * proj yoga pose grouping workout relation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
public class ProjYogaPoseGroupWorkoutRelationServiceImpl
        extends ServiceImpl<ProjYogaPoseGroupWorkoutRelationMapper, ProjYogaPoseGroupWorkoutRelation>
        implements IProjYogaPoseGroupWorkoutRelationService {
    @Override
    public void deleteByYogaPoseGroupId(Integer yogaPoseGroupId) {
        LambdaUpdateWrapper<ProjYogaPoseGroupWorkoutRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjYogaPoseGroupWorkoutRelation::getProjYogaPoseGroupId, yogaPoseGroupId)
                .set(BaseModel::getDelFlag, GlobalConstant.YES);
        baseMapper.update(new ProjYogaPoseGroupWorkoutRelation(), wrapper);
    }
}
