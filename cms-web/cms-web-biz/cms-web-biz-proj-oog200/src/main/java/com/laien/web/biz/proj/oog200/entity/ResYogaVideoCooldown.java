package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * yoga video cooldown 链路
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResYogaVideoCooldown对象", description="yoga video cooldown 链路")
public class ResYogaVideoCooldown extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "group id uuid")
    private Integer groupId;

    @ApiModelProperty(value = "当前链路所属的video id")
    private Integer resYogaVideoLinkId;

    @ApiModelProperty(value = "当前链路video id")
    private Integer resYogaVideoId;

    @ApiModelProperty(value = "下一个yoga video id")
    private Integer resYogaVideoNextId;

    @ApiModelProperty(value = "过渡视频id")
    private Integer resTransitionId;


}
