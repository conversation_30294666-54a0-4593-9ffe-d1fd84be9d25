package com.laien.web.biz.proj.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.web.biz.proj.oog200.entity.ProjDish;
import com.laien.web.biz.proj.oog200.request.ProjDishAddReq;
import com.laien.web.biz.proj.oog200.request.ProjDishListReq;
import com.laien.web.biz.proj.oog200.request.ProjDishUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjDishDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjDishListVO;
import com.laien.web.biz.proj.oog200.service.IProjDishService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Dish 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */

@Api(tags = "项目管理:dish")
@RestController
@RequestMapping("/proj/dish")
public class ProjDishController extends ResponseController {

    @Resource
    private IProjDishService projDishService;

    @ApiOperation(value = "列表")
    @GetMapping( "/list")
    public ResponseResult<List<ProjDishListVO>> list(ProjDishListReq listReq) {
        return succ(projDishService.list(listReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjDishAddReq dishReq) {
        projDishService.save(dishReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjDishUpdateReq dishReq) {
        projDishService.update(dishReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjDishDetailVO> detail(@PathVariable Integer id) {
        ProjDishDetailVO detailVO = projDishService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        List<Integer> failedIdList = projDishService.updateEnableByIds(idList);
        if (CollUtil.isNotEmpty(failedIdList)) {
            String ids = StrUtil.join(GlobalConstant.COMMA, failedIdList);
            return fail("The following data(id: " + ids +") has an incorrect value or status");
        }
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projDishService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projDishService.deleteByIdList(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        projDishService.sort(idListReq);
        return succ();
    }

}
