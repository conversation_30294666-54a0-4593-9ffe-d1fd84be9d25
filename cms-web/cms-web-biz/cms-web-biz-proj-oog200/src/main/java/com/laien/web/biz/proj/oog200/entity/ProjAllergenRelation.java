package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * allergen和业务表的关系
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_allergen_relation")
@ApiModel(value="ProjAllergenRelation对象", description="allergen和业务表的关系")
public class ProjAllergenRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "业务表数据id")
    private Integer dataId;

    @ApiModelProperty(value = "proj_allergen表数据id")
    private Integer projAllergenId;

    @ApiModelProperty(value = "业务类型")
    private AllergenRelationBusinessEnum businessType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
