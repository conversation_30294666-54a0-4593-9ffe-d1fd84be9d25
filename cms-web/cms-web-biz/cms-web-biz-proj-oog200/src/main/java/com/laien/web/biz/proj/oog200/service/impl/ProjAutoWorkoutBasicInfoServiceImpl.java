package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.bo.ProjAutoWorkoutBasicInfoBO;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.common.oog200.enums.AutoWorkoutBasicInfoPointEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjAutoWorkoutBasicInfoMapper;
import com.laien.web.biz.proj.oog200.request.AutoWorkoutBasicInfoImportReq;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoListReq;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoUpdateReq;
import com.laien.web.biz.proj.oog200.response.AutoWorkoutBasicInfoDownloadVO;
import com.laien.web.biz.proj.oog200.response.AutoWorkoutListVO;
import com.laien.web.biz.proj.oog200.response.ProjAutoWorkoutBasicInfoDetailVO;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.validation.Group;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_image 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Service
public class ProjAutoWorkoutBasicInfoServiceImpl extends ServiceImpl<ProjAutoWorkoutBasicInfoMapper, ProjAutoWorkoutBasicInfo> implements IProjAutoWorkoutBasicInfoService {

    @Resource
    private IProjAutoWorkoutBasicInfoWorkoutRelationService projAutoWorkoutBasicInfoWorkoutRelationService;

    @Resource
    private Validator validator;
    @Resource
    private IProjWallPilatesAutoWorkoutService wallPilatesAutoWorkoutService;
    @Resource
    private IProjChairYogaAutoWorkoutService projChairYogaAutoWorkoutService;
    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;
    @Resource
    private IProjLmsI18nService i18nService;

    @Transactional
    @Override
    public void update(ProjAutoWorkoutBasicInfoUpdateReq infoReq, Integer projId) {
        Integer id = infoReq.getId();
        ProjAutoWorkoutBasicInfo info = baseMapper.selectById(id);
        if (null == info) {
            throw new BizException("data not found");
        }
        ProjAutoWorkoutBasicInfo updateInfo = new ProjAutoWorkoutBasicInfo();
        check(infoReq, id);
        BeanUtils.copyProperties(infoReq, updateInfo);
        updateInfo.setProjId(projId);
        updateById(updateInfo);

        updateNullable(infoReq);
        List<Integer> workoutIdList = infoReq.getAutoWorkoutIdList();
        saveRelation(workoutIdList, info);
        i18nService.handleI18n(Collections.singletonList(updateInfo), projId);
    }

    private void updateNullable(ProjAutoWorkoutBasicInfoUpdateReq infoReq) {

        if (Objects.nonNull(infoReq.getDifficulty()) && Objects.nonNull(infoReq.getPoint())) {
            return;
        }

        LambdaUpdateWrapper<ProjAutoWorkoutBasicInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Objects.isNull(infoReq.getDifficulty()), ProjAutoWorkoutBasicInfo::getDifficulty, null);
        updateWrapper.set(Objects.isNull(infoReq.getPoint()), ProjAutoWorkoutBasicInfo::getPoint, null);
        updateWrapper.eq(ProjAutoWorkoutBasicInfo::getId, infoReq.getId());
        update(updateWrapper);
    }

    @Override
    public ProjAutoWorkoutBasicInfoDetailVO findDetailById(Integer id) {
        ProjAutoWorkoutBasicInfo info = baseMapper.selectById(id);
        ProjAutoWorkoutBasicInfoDetailVO detailVO = new ProjAutoWorkoutBasicInfoDetailVO();
        BeanUtils.copyProperties(info, detailVO);
        Set<Integer> idSet = new HashSet<>();
        idSet.add(id);
        List<ProjAutoWorkoutBasicInfoWorkoutRelation> relationList = projAutoWorkoutBasicInfoWorkoutRelationService.query(idSet);
        List<Integer> workoutIdList = relationList.stream().map(ProjAutoWorkoutBasicInfoWorkoutRelation::getAutoWorkoutId).collect(Collectors.toList());
        List<AutoWorkoutListVO> autoWorkoutList = new ArrayList<>(workoutIdList.size());
        if (info.getPlanType() == YogaAutoWorkoutTemplateEnum.CHAIR_YOGA) {
            Collection<ProjChairYogaAutoWorkout> chairYogaAutoWorkoutList = projChairYogaAutoWorkoutService.listByWorkoutIds(workoutIdList);
            for (ProjChairYogaAutoWorkout workout : chairYogaAutoWorkoutList) {
                AutoWorkoutListVO workoutListVO = new AutoWorkoutListVO();
                BeanUtils.copyProperties(workout, workoutListVO);
                autoWorkoutList.add(workoutListVO);
            }

        } else if (info.getPlanType() == YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA || info.getPlanType() == YogaAutoWorkoutTemplateEnum.SOMATIC_YOGA || info.getPlanType() == YogaAutoWorkoutTemplateEnum.LAZY_YOGA) {
            Collection<ProjYogaAutoWorkout> yogaAutoWorkoutList = projYogaAutoWorkoutService.listByWorkoutIds(workoutIdList);
            for (ProjYogaAutoWorkout workout : yogaAutoWorkoutList) {
                AutoWorkoutListVO workoutListVO = new AutoWorkoutListVO();
                BeanUtils.copyProperties(workout, workoutListVO);
                autoWorkoutList.add(workoutListVO);
            }

        } else if (info.getPlanType() == YogaAutoWorkoutTemplateEnum.WALL_PILATES) {
            List<ProjWallPilatesAutoWorkout> wallPilatesAutoWorkoutList = wallPilatesAutoWorkoutService.query(workoutIdList);
            for (ProjWallPilatesAutoWorkout workout : wallPilatesAutoWorkoutList) {
                AutoWorkoutListVO workoutListVO = new AutoWorkoutListVO();
                BeanUtils.copyProperties(workout, workoutListVO);
                autoWorkoutList.add(workoutListVO);
            }
        }
        detailVO.setAutoWorkoutIdList(workoutIdList);
        detailVO.setAutoWorkoutList(autoWorkoutList);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjAutoWorkoutBasicInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjAutoWorkoutBasicInfo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjAutoWorkoutBasicInfo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjAutoWorkoutBasicInfo::getId, idList);
        update(new ProjAutoWorkoutBasicInfo(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjAutoWorkoutBasicInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjAutoWorkoutBasicInfo::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjAutoWorkoutBasicInfo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjAutoWorkoutBasicInfo::getId, idList);
        this.update(new ProjAutoWorkoutBasicInfo(), wrapper);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjAutoWorkoutBasicInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjAutoWorkoutBasicInfo::getDelFlag, GlobalConstant.YES).in(ProjAutoWorkoutBasicInfo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY).in(ProjAutoWorkoutBasicInfo::getId, idList);
        projAutoWorkoutBasicInfoWorkoutRelationService.deleteByProjAutoWorkoutBasicInfoIdList(idList);
        this.update(new ProjAutoWorkoutBasicInfo(), wrapper);
    }

    @Override
    @Transactional
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        CollectionUtils.isEmpty(idList);
        ArrayList<ProjAutoWorkoutBasicInfo> infoList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjAutoWorkoutBasicInfo info = new ProjAutoWorkoutBasicInfo();
            info.setSorted(i)
                    .setId(idList.get(i));
            infoList.add(info);
        }
        updateBatchById(infoList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importByExcel(InputStream excelInputStream, Integer projId) {
        List<AutoWorkoutBasicInfoImportReq> basicInfoImportList = CollUtil.newArrayList();
        EasyExcel.read(excelInputStream, AutoWorkoutBasicInfoImportReq.class, new AnalysisEventListener<AutoWorkoutBasicInfoImportReq>() {
            @Override
            public void invoke(AutoWorkoutBasicInfoImportReq row, AnalysisContext analysisContext) {
                basicInfoImportList.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        if (CollUtil.isEmpty(basicInfoImportList)) {
            return null;
        }
        List<String> failMessage = new ArrayList<>();
        List<ProjAutoWorkoutBasicInfoBO> saveOrUpdateBasicInfoList = checkField(projId, basicInfoImportList, failMessage);
        if (CollUtil.isNotEmpty(failMessage)) {
            return failMessage;
        }
        failMessage = checkFieldLogic(saveOrUpdateBasicInfoList);
        if (CollUtil.isNotEmpty(failMessage)) {
            return failMessage;
        }
        List<ProjAutoWorkoutBasicInfo> infoList = new ArrayList<>();
        for (ProjAutoWorkoutBasicInfoBO infoBO : saveOrUpdateBasicInfoList) {
            infoList.add(infoBO.getProjAutoWorkoutBasicInfo());
        }
        saveOrUpdateBatch(infoList);
        i18nService.handleI18n(infoList, projId);

        //将point为null的拿出来单独更新
        Set<Integer> updateInfoIdSet = infoList.stream().filter(info -> null == info.getPoint()).map(BaseModel::getId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(updateInfoIdSet)) {
            LambdaUpdateWrapper<ProjAutoWorkoutBasicInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(BaseModel::getId, updateInfoIdSet).set(ProjAutoWorkoutBasicInfo::getPoint, null);
            baseMapper.update(new ProjAutoWorkoutBasicInfo(), updateWrapper);
        }
        for (ProjAutoWorkoutBasicInfoBO infoBO : saveOrUpdateBasicInfoList) {
            saveRelation(infoBO.getWorkoutIdList(),infoBO.getProjAutoWorkoutBasicInfo());
        }
        return failMessage;
    }

    @Override
    public List<AutoWorkoutBasicInfoDownloadVO> downloadList() {
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ProjAutoWorkoutBasicInfo::getSorted);
        List<ProjAutoWorkoutBasicInfo> basicInfoList = baseMapper.selectList(wrapper);
        List<AutoWorkoutBasicInfoDownloadVO> downloadList = new ArrayList<>(basicInfoList.size());
        Set<Integer> infoIdSet = basicInfoList.stream().map(ProjAutoWorkoutBasicInfo::getId).collect(Collectors.toSet());
        Map<Integer, List<Integer>> workoutIdMap = workoutIdMap(projAutoWorkoutBasicInfoWorkoutRelationService.query(infoIdSet));
        for (ProjAutoWorkoutBasicInfo info : basicInfoList) {
            AutoWorkoutBasicInfoDownloadVO downloadVO = new AutoWorkoutBasicInfoDownloadVO();
            downloadVO.setId(info.getId());
            downloadVO.setName(info.getName());
            downloadVO.setDetailImage(info.getDetailImage());
            downloadVO.setCompleteImage(info.getCompleteImage());
            downloadVO.setCoverImageMale(info.getCoverImageMale());
            AutoWorkoutBasicInfoPointEnum point = info.getPoint();
            if (null != point) {
                downloadVO.setPointValue(point.getValue());
            }
            downloadVO.setCoverImage(info.getCoverImage());
            downloadVO.setPlanTypeValue(info.getPlanType().getName());

            DifficultyEnum difficultyEnum = info.getDifficulty();
            if (null != difficultyEnum) {
                downloadVO.setDifficultyValue(difficultyEnum.getName());
            }

            List<Integer> workoutIdList = workoutIdMap.get(info.getId());
            downloadVO.setWorkoutId(CollUtil.join(workoutIdList, GlobalConstant.COMMA));
            downloadList.add(downloadVO);
        }
        return downloadList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjAutoWorkoutBasicInfoAddReq infoReq, Integer projId) {
        check(infoReq, null);
        ProjAutoWorkoutBasicInfo info = new ProjAutoWorkoutBasicInfo();
        BeanUtils.copyProperties(infoReq, info);
        info.setProjId(projId);
        int defaultSorted = -1;
        info.setStatus(GlobalConstant.STATUS_DRAFT).setSorted(defaultSorted);
        save(info);
        List<Integer> workoutIdList = infoReq.getAutoWorkoutIdList();
        saveRelation(workoutIdList, info);
        i18nService.handleI18n(Collections.singletonList(info), projId);
    }


    @Override
    public List<ProjAutoWorkoutBasicInfoDetailVO> list(ProjAutoWorkoutBasicInfoListReq listReq, Integer projId) {
        Integer autoWorkoutId = listReq.getAutoWorkoutId();
        Integer id = listReq.getId();
        String name = listReq.getName();
        AutoWorkoutBasicInfoPointEnum autoWorkoutBasicInfoPointEnum = listReq.getPoint();
        YogaAutoWorkoutTemplateEnum planTypeEnum = listReq.getPlanType();
        DifficultyEnum difficultyEnum = listReq.getDifficulty();
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(null != id, BaseModel::getId, id)
                .like(StringUtils.isNotBlank(name), ProjAutoWorkoutBasicInfo::getName, name)
                .eq(null != autoWorkoutBasicInfoPointEnum, ProjAutoWorkoutBasicInfo::getPoint, autoWorkoutBasicInfoPointEnum)
                .eq(null != planTypeEnum, ProjAutoWorkoutBasicInfo::getPlanType, planTypeEnum)
                .eq(null != difficultyEnum, ProjAutoWorkoutBasicInfo::getDifficulty, difficultyEnum)
                .orderByAsc(ProjAutoWorkoutBasicInfo::getSorted);
        List<ProjAutoWorkoutBasicInfo> basicInfoList = baseMapper.selectList(wrapper);
        Set<Integer> infoIdList = basicInfoList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        //key: id，value: workoutId
        Map<Integer, List<Integer>> relationMap = workoutIdMap(projAutoWorkoutBasicInfoWorkoutRelationService.query(infoIdList));
        List<ProjAutoWorkoutBasicInfoDetailVO> detailList = new ArrayList<>();
        for (ProjAutoWorkoutBasicInfo basicInfo : basicInfoList) {
            List<Integer> workoutIdList = relationMap.get(basicInfo.getId());
            if(null != autoWorkoutId && CollUtil.isNotEmpty(workoutIdList) && !workoutIdList.contains(autoWorkoutId)){
                continue;
            }
            ProjAutoWorkoutBasicInfoDetailVO detailVO = new ProjAutoWorkoutBasicInfoDetailVO();
            BeanUtils.copyProperties(basicInfo, detailVO);
            detailVO.setAutoWorkoutIdList(workoutIdList);
            detailList.add(detailVO);
        }
        return detailList;
    }

    private Map<Integer, List<Integer>> workoutIdMap(List<ProjAutoWorkoutBasicInfoWorkoutRelation> projAutoWorkoutBasicInfoWorkoutRelationService) {
        List<ProjAutoWorkoutBasicInfoWorkoutRelation> relationList = projAutoWorkoutBasicInfoWorkoutRelationService;
        Map<Integer, List<Integer>> relationMap = new HashMap<>();

        for (ProjAutoWorkoutBasicInfoWorkoutRelation relation : relationList) {
            Integer projAutoWorkoutBasicInfoId = relation.getProjAutoWorkoutBasicInfoId();
            List<Integer> workoutIdList = relationMap.getOrDefault(projAutoWorkoutBasicInfoId, new ArrayList<>());
            workoutIdList.add(relation.getAutoWorkoutId());
            relationMap.put(projAutoWorkoutBasicInfoId, workoutIdList);
        }
        return relationMap;
    }

    private void check(ProjAutoWorkoutBasicInfoAddReq infoReq, Integer id) {
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjAutoWorkoutBasicInfo::getName, infoReq.getName())
                .eq(ProjAutoWorkoutBasicInfo::getPlanType, infoReq.getPlanType())
                .eq(ProjAutoWorkoutBasicInfo::getPoint,infoReq.getPoint())
                .ne(null != id, BaseModel::getId, id);
        List<ProjAutoWorkoutBasicInfo> videoList = baseMapper.selectList(wrapper);
        Set<String> nameSet = videoList.stream().map(ProjAutoWorkoutBasicInfo::getName).collect(Collectors.toSet());
        if (nameSet.contains(infoReq.getName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
    }

    private void saveRelation(List<Integer> workoutIdList, ProjAutoWorkoutBasicInfo info) {
        projAutoWorkoutBasicInfoWorkoutRelationService.deleteByProjAutoWorkoutBasicInfoId(info.getId());
        if(CollUtil.isEmpty(workoutIdList)){
            return;
        }
        List<ProjAutoWorkoutBasicInfoWorkoutRelation> relationList = new ArrayList<>(workoutIdList.size());
        for (Integer workoutId : workoutIdList) {
            ProjAutoWorkoutBasicInfoWorkoutRelation relation = new ProjAutoWorkoutBasicInfoWorkoutRelation();
            relation.setAutoWorkoutId(workoutId)
                    .setProjAutoWorkoutBasicInfoId(info.getId())
                    .setProjAutoWorkoutBasicInfoName(info.getName())
                    .setPlanType(info.getPlanType());
            relationList.add(relation);
        }
        projAutoWorkoutBasicInfoWorkoutRelationService.saveBatch(relationList);
    }

    /**
     * 校验字段取值是否符合逻辑，如名字重复、字段间的逻辑关系
     */
    private List<String> checkFieldLogic(List<ProjAutoWorkoutBasicInfoBO> basicInfoBOList) {
        List<String> nameRepeatErrorList = new ArrayList<>();
        List<String> workoutIllegalErrorList = new ArrayList<>();
        List<String> planTypePointUnMatechedErrorList = new ArrayList<>();
        List<String> failMessage = new ArrayList<>();
        List<String> difficultyUnMatechedErrorList = new ArrayList<>();
        List<ProjAutoWorkoutBasicInfo> basicInfoList = list();
        Map<Integer, ProjAutoWorkoutBasicInfo> basicInfoMap = basicInfoList.stream()
                .collect(Collectors.toMap(ProjAutoWorkoutBasicInfo::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );

        List<ProjAutoWorkoutBasicInfo> allInfoList = new ArrayList<>(basicInfoList);
        //key：planType,value：workoutId
        HashMap<YogaAutoWorkoutTemplateEnum, List<Integer>> planTypeMap = new HashMap<>();

        for (ProjAutoWorkoutBasicInfoBO infoBO : basicInfoBOList) {
            ProjAutoWorkoutBasicInfo info = new ProjAutoWorkoutBasicInfo();
            AutoWorkoutBasicInfoImportReq infoReq = infoBO.getImportReq();
            BeanUtils.copyProperties(infoReq, info);
            YogaAutoWorkoutTemplateEnum planType = YogaAutoWorkoutTemplateEnum.getByName(infoReq.getPlanTypeValue());
            AutoWorkoutBasicInfoPointEnum point = AutoWorkoutBasicInfoPointEnum.getByName(infoReq.getPointValue());
            DifficultyEnum difficultyEnum = DifficultyEnum.getByName(infoReq.getDifficultyValue());
            info.setPlanType(planType).setPoint(point).setDifficulty(difficultyEnum);
            if (null == infoReq.getId()) {
                allInfoList.add(info);
            } else {
                basicInfoMap.put(info.getId(), info);
            }
            String workoutIdArrString = infoReq.getWorkoutId();
            if (StringUtils.isNotBlank(workoutIdArrString)) {
                for (String workoutIdString : Arrays.stream(workoutIdArrString.replace(GlobalConstant.SPACE_STRING, GlobalConstant.EMPTY_STRING).split(GlobalConstant.COMMA)).collect(Collectors.toList())) {
                    Integer workoutId = NumberUtil.parseInt(workoutIdString, null);
                    if (null == workoutId) {
                        String msg = "name:" + infoReq.getName() + ",Auto Workout ID: " + workoutIdArrString + " illegal";
                        failMessage.add(msg);
                        break;
                    }
                    List<Integer> workoutIdList = planTypeMap.getOrDefault(planType, new ArrayList<>());
                    workoutIdList.add(workoutId);
                    planTypeMap.put(planType, workoutIdList);
                }
            }
            String pointErrorTemplate = "point not can be null, %s(%s)";
            String difficultyErrorTemplate = "difficulty value is invalid, %s(%s)";
            if (planType == YogaAutoWorkoutTemplateEnum.LAZY_YOGA || planType == YogaAutoWorkoutTemplateEnum.SOMATIC_YOGA) {
                // 这两种类型的point只能为null，所以手动设置为null
                point = null;
            } else if (planType == YogaAutoWorkoutTemplateEnum.WALL_PILATES) {
                if (null == point) {
                    failMessage.add(String.format(pointErrorTemplate, info.getName(), planType.getName()));
                } else if (point.getCode() > AutoWorkoutBasicInfoPointEnum.FULL_BODY.getCode()) {
                    String msg = getUniqueKey4BasicInfo(info);
                    planTypePointUnMatechedErrorList.add(msg);
                }
            } else if (planType == YogaAutoWorkoutTemplateEnum.CHAIR_YOGA) {
                if (Objects.nonNull(point) && point.getCode() > AutoWorkoutBasicInfoPointEnum.FULL_BODY.getCode()) {
                    String msg = getUniqueKey4BasicInfo(info);
                    planTypePointUnMatechedErrorList.add(msg);
                }
            } else {
                assert planType != null;
                if (null == point) {
                    failMessage.add(String.format(pointErrorTemplate, info.getName(), planType.getName()));
                } else if (point.getCode() <= AutoWorkoutBasicInfoPointEnum.FULL_BODY.getCode()) {
                    String msg = getUniqueKey4BasicInfo(info);
                    planTypePointUnMatechedErrorList.add(msg);
                }

                // YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA 类型必须配置Difficulty
                info.setDifficulty(difficultyEnum);
                if (Objects.isNull(difficultyEnum)) {
                    difficultyUnMatechedErrorList.add(String.format(difficultyErrorTemplate, info.getName(), infoReq.getDifficultyValue()));
                }
            }
            infoBO.getProjAutoWorkoutBasicInfo().setPoint(point);
        }
        checkWorkout(planTypeMap, workoutIllegalErrorList);
        Map<String, List<ProjAutoWorkoutBasicInfo>> infoMap = new HashMap<>();
        allInfoList.addAll(basicInfoMap.values());

        for (ProjAutoWorkoutBasicInfo info : allInfoList) {
            String key = getUniqueKey4BasicInfo(info);
            List<ProjAutoWorkoutBasicInfo> projAutoWorkoutBasicInfoList = infoMap.getOrDefault(key, new ArrayList<>());
            projAutoWorkoutBasicInfoList.add(info);
            infoMap.put(key, projAutoWorkoutBasicInfoList);
        }

        for (List<ProjAutoWorkoutBasicInfo> infoList : infoMap.values()) {
            Set<Integer> idList = infoList.stream().map(BaseModel::getId).collect(Collectors.toSet());
            if (idList.size() > GlobalConstant.ONE) {
                ProjAutoWorkoutBasicInfo info = infoList.get(GlobalConstant.ZERO);
                String msg = info.getName() + "(" + info.getPlanType().getName() + "-" + info.getPoint() +")";
                nameRepeatErrorList.add(msg);
            }
        }
        List<String> errorList = new ArrayList<>();
        if(CollUtil.isNotEmpty(workoutIllegalErrorList)){
            errorList.add("Import failed because the following Auto Workouts are not enabled or deleted:");
            errorList.addAll(workoutIllegalErrorList);
        }
        if(CollUtil.isNotEmpty(nameRepeatErrorList)){
            errorList.add("Import failed because the following Image Name is not unique:");
            errorList.addAll(nameRepeatErrorList);
        }
        if(CollUtil.isNotEmpty(planTypePointUnMatechedErrorList)){
            errorList.add("Import failed because the Point and Plan Type of the following images do not match:");
            errorList.addAll(planTypePointUnMatechedErrorList);
        }
        if (CollUtil.isNotEmpty(difficultyUnMatechedErrorList)) {
            errorList.add("Import failed because the Difficulty of the following images do not match:");
            errorList.addAll(difficultyUnMatechedErrorList);
        }
        if(CollUtil.isNotEmpty(failMessage)){
            errorList.add("Import failed because the Point and Plan Type of the following images do not match:");
            errorList.addAll(failMessage);
        }
        return errorList;

    }

    private String getUniqueKey4BasicInfo(ProjAutoWorkoutBasicInfo basicInfo) {

        YogaAutoWorkoutTemplateEnum planType = basicInfo.getPlanType();
        assert planType != null;
        AutoWorkoutBasicInfoPointEnum point = basicInfo.getPoint();
        DifficultyEnum difficulty = basicInfo.getDifficulty();

        StringBuilder uniqueKey = new StringBuilder();
        uniqueKey.append(planType.getName()).append(GlobalConstant.ENGLISH_DASH);
        uniqueKey.append(point).append(GlobalConstant.ENGLISH_DASH);
        uniqueKey.append(basicInfo.getName()).append(GlobalConstant.ENGLISH_DASH);
        uniqueKey.append(difficulty).append(GlobalConstant.ENGLISH_DASH);
        return uniqueKey.toString();
    }

    private void checkWorkout(HashMap<YogaAutoWorkoutTemplateEnum, List<Integer>> planTypeMap, List<String> workoutIllegalErrorList) {
        List<Integer> workoutIdList = planTypeMap.getOrDefault(YogaAutoWorkoutTemplateEnum.WALL_PILATES, new ArrayList<>());
        List<ProjWallPilatesAutoWorkout> workoutList = wallPilatesAutoWorkoutService.query(workoutIdList);
        Map<Integer, ProjWallPilatesAutoWorkout> workoutMap = workoutList.stream()
                .collect(Collectors.toMap(ProjWallPilatesAutoWorkout::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );
        for (Integer workoutId : workoutIdList) {
            ProjWallPilatesAutoWorkout autoWorkout = workoutMap.get(workoutId);
            if (null == autoWorkout || GlobalConstant.STATUS_ENABLE != autoWorkout.getStatus()) {
                String msg = "Auto Workout ID " + workoutId + "(" + YogaAutoWorkoutTemplateEnum.WALL_PILATES.getName() + ");";
                workoutIllegalErrorList.add(msg);
            }
        }

        workoutIdList = planTypeMap.getOrDefault(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, new ArrayList<>());
        Collection<ProjChairYogaAutoWorkout> chairYogaAutoWorkoutList = projChairYogaAutoWorkoutService.listByWorkoutIds(workoutIdList);
        Map<Integer, ProjChairYogaAutoWorkout> chairWorkoutMap = chairYogaAutoWorkoutList.stream()
                .collect(Collectors.toMap(ProjChairYogaAutoWorkout::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );
        for (Integer workoutId : workoutIdList) {
            ProjChairYogaAutoWorkout autoWorkout = chairWorkoutMap.get(workoutId);
            if (null == autoWorkout || GlobalConstant.STATUS_ENABLE != autoWorkout.getStatus()) {
                String msg = "Auto Workout ID " + workoutId + "(" + YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getName() + ");";
                workoutIllegalErrorList.add(msg);
            }
        }

        //LAZY_YOGA、SOMATIC_YOGA当成CLASSIC_YOGA处理
        workoutIdList = planTypeMap.getOrDefault(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA, new ArrayList<>());
        workoutIdList.addAll(planTypeMap.getOrDefault(YogaAutoWorkoutTemplateEnum.LAZY_YOGA, new ArrayList<>()));
        workoutIdList.addAll(planTypeMap.getOrDefault(YogaAutoWorkoutTemplateEnum.SOMATIC_YOGA, new ArrayList<>()));
        Collection<ProjYogaAutoWorkout> classicWorkoutList = projYogaAutoWorkoutService.listByWorkoutIds(workoutIdList);
        Map<Integer, ProjYogaAutoWorkout> classicWorkoutMap = classicWorkoutList.stream()
                .collect(Collectors.toMap(ProjYogaAutoWorkout::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );
        for (Integer workoutId : workoutIdList) {
            ProjYogaAutoWorkout autoWorkout = classicWorkoutMap.get(workoutId);
            if (null == autoWorkout || GlobalConstant.STATUS_ENABLE != autoWorkout.getStatus()) {
                String msg = "Auto Workout ID " + workoutId + "(" + YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getName() + ");";
                workoutIllegalErrorList.add(msg);
            }
        }
    }

    /**
     * 字段校验基础校验
     */
    private List<ProjAutoWorkoutBasicInfoBO> checkField(Integer projId, List<AutoWorkoutBasicInfoImportReq> basicInfoImportList, List<String> failMessage) {
        String colon = ":";
        List<ProjAutoWorkoutBasicInfoBO> saveOrUpdateBasicInfoList = new ArrayList<>(basicInfoImportList.size());
        for (int i = 0; i < basicInfoImportList.size(); i++) {
            AutoWorkoutBasicInfoImportReq infoReq = basicInfoImportList.get(i);
            String name = infoReq.getName();
            Set<ConstraintViolation<AutoWorkoutBasicInfoImportReq>> violationSet = validator.validate(infoReq, Group.class);
            if (CollUtil.isNotEmpty(violationSet)) {
                violationSet.stream().findFirst().ifPresent(violation -> failMessage.add(name + colon + violation.getMessage()));
                continue;
            }
            ProjAutoWorkoutBasicInfo info = new ProjAutoWorkoutBasicInfo();
            BeanUtils.copyProperties(infoReq, info);
            YogaAutoWorkoutTemplateEnum planType = YogaAutoWorkoutTemplateEnum.getByName(infoReq.getPlanTypeValue());
            AutoWorkoutBasicInfoPointEnum point = AutoWorkoutBasicInfoPointEnum.getByName(infoReq.getPointValue());
            DifficultyEnum difficultyEnum = DifficultyEnum.getByName(infoReq.getDifficultyValue());
            info.setPlanType(planType)
                    .setPoint(point).setDifficulty(difficultyEnum);
            info.setProjId(projId).setSorted(i);
            if (null == infoReq.getId()) {
                info.setStatus(GlobalConstant.STATUS_DRAFT);
            }

            ProjAutoWorkoutBasicInfoBO infoBO = new ProjAutoWorkoutBasicInfoBO();
            String workoutIdArrString = infoReq.getWorkoutId();
            List<Integer> workoutIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(workoutIdArrString)) {
                for (String workoutIdString : Arrays.stream(workoutIdArrString.replace(GlobalConstant.SPACE_STRING, GlobalConstant.EMPTY_STRING).split(GlobalConstant.COMMA)).collect(Collectors.toList())) {
                    if (!NumberUtil.isInteger(workoutIdString)) {
                        String msg = "name:" + infoReq.getName() + ",Auto Workout ID: " + workoutIdArrString + " illegal";
                        failMessage.add(msg);
                        break;
                    }
                    Integer workoutId = NumberUtil.parseInt(workoutIdString, null);
                    workoutIdList.add(workoutId);
                }
            }
            infoBO.setProjAutoWorkoutBasicInfo(info).setWorkoutIdList(workoutIdList).setImportReq(infoReq);
            saveOrUpdateBasicInfoList.add(infoBO);
        }
        return saveOrUpdateBasicInfoList;
    }
}
