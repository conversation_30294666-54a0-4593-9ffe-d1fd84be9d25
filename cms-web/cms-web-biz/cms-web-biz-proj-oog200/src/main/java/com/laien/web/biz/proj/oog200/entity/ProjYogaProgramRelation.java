package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaProgramRelation对象", description="proj yoga program relation")
public class ProjYogaProgramRelation extends BaseModel {

    private Integer projYogaProgramId;

    private Integer projYogaProgramLevelId;

    private Integer projId;
}
