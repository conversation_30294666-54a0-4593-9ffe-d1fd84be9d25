package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjWallPilatesVideoPageReq extends PageReq {


    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "动作类型 ： Warm Up、Main 、Cool Down")
    private String type;

    @ApiModelProperty(value = "当前动作方向 Left、Right、Central")
    private String direction;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String position;

}
