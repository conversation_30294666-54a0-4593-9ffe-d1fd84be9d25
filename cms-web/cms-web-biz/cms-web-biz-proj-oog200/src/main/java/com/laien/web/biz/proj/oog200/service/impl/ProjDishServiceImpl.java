package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import com.laien.common.oog200.enums.DishStyleEnum;
import com.laien.common.oog200.enums.DishTypeEnum;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjAllergenRelation;
import com.laien.web.biz.proj.oog200.entity.ProjDish;
import com.laien.web.biz.proj.oog200.mapper.ProjDishMapper;
import com.laien.web.biz.proj.oog200.request.ProjDishAddReq;
import com.laien.web.biz.proj.oog200.request.ProjDishListReq;
import com.laien.web.biz.proj.oog200.request.ProjDishUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjDishDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjDishListVO;
import com.laien.web.biz.proj.oog200.response.ProjDishStepVO;
import com.laien.web.biz.proj.oog200.response.ProjIngredientVO;
import com.laien.web.biz.proj.oog200.service.IProjAllergenRelationService;
import com.laien.web.biz.proj.oog200.service.IProjDishService;
import com.laien.web.biz.proj.oog200.service.IProjDishStepService;
import com.laien.web.biz.proj.oog200.service.IProjIngredientService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_DISH;

/**
 * <p>
 * Dish 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Service
public class ProjDishServiceImpl extends ServiceImpl<ProjDishMapper, ProjDish> implements IProjDishService {

    @Resource
    private IProjIngredientService projIngredientService;
    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @Resource
    private IProjAllergenRelationService projAllergenRelationService;

    @Resource
    private IProjDishStepService projDishStepService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private IProjInfoService projInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjDishAddReq dishReq, Integer projId) {
        check(null, dishReq, projId);
        ProjDish dish = toProjDish(dishReq, projId);
        save(dish);
        Integer id = dish.getId();
        projAllergenRelationService.saveBatch(dishReq.getAllergenIdList(), projId, id, AllergenRelationBusinessEnum.DISH);
        projIngredientService.saveBatch(dishReq.getIngredientList(), id, projId);
        projDishStepService.saveBatch(id, dishReq.getDishStepList(), projId);
        projLmsI18nService.handleI18n(Collections.singletonList(dish), projInfoService.getById(RequestContextUtils.getProjectId()));
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjDishUpdateReq dishReq, Integer projId) {
        check(dishReq.getId(), dishReq, projId);
        String resourceVideoUrl = dishReq.getResourceVideoUrl();

        String styleCodes = DishStyleEnum.getCodeString(dishReq.getStyleList());
        String typeCodes = DishTypeEnum.getCodeString(dishReq.getTypeList());
        Integer id = dishReq.getId();

        LambdaUpdateWrapper<ProjDish> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BaseModel::getId, id)
                .set(ProjDish::getName, dishReq.getName())
                .set(ProjDish::getEventName, dishReq.getEventName())
                .set(ProjDish::getCoverImgUrl, dishReq.getCoverImgUrl())
                .set(ProjDish::getDetailImgUrl, dishReq.getDetailImgUrl())
                .set(ProjDish::getTypes, typeCodes)
                .set(ProjDish::getStyles, styleCodes)
                .set(ProjDish::getPrepareTime, dishReq.getPrepareTime())
                .set(ProjDish::getCalorie, dishReq.getCalorie())
                .set(ProjDish::getCarb, dishReq.getCarb())
                .set(ProjDish::getProtein, dishReq.getProtein())
                .set(ProjDish::getFat, dishReq.getFat())
                .set(ProjDish::getServing, dishReq.getServing());

        ProjDish dish = new ProjDish();
        if (StrUtil.isBlank(resourceVideoUrl)) {
            wrapper.set(ProjDish::getVideo2532Url, null)
                    .set(ProjDish::getResourceVideoUrl, null)
                    .set(ProjDish::getVideoUrl, null)
                    .set(ProjDish::getDuration, GlobalConstant.ZERO);
        } else {
            dish.setId(id);
            dish.setResourceVideoUrl(resourceVideoUrl);
        }

        baseMapper.update(dish, wrapper);
        projAllergenRelationService.saveBatch(dishReq.getAllergenIdList(), projId, id, AllergenRelationBusinessEnum.DISH);
        projIngredientService.saveBatch(dishReq.getIngredientList(), id, projId);
        projDishStepService.saveBatch(id, dishReq.getDishStepList(), projId);
        projLmsI18nService.handleI18n(Collections.singletonList(getById(id)), projInfoService.getById(RequestContextUtils.getProjectId()));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjDish> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjDish::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjDish::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjDish::getId, idList);
        this.update(new ProjDish(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {
        LambdaUpdateWrapper<ProjDish> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjDish::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjDish::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjDish::getId, idList);
        this.update(new ProjDish(), wrapper);
        projIngredientService.deleteBatch(idList);
        projDishStepService.deleteBatch(idList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<ProjDish> dishList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjDish info = new ProjDish();
            info.setSorted(i)
                    .setId(idList.get(i));
            dishList.add(info);
        }
        updateBatchById(dishList);
    }

    @Override
    public List<ProjDishListVO> list(ProjDishListReq listReq, Integer projId) {
        String name = listReq.getName();
        DishTypeEnum type = listReq.getType();
        Integer typeCode = null;
        if (null != type) {
            typeCode = type.getCode();
        }
        DishStyleEnum style = listReq.getStyle();
        Integer styleCode = null;
        if(null != style){
            styleCode = style.getCode();
        }
        Integer status = listReq.getStatus();
        LambdaQueryWrapper<ProjDish> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(name), ProjDish::getName, name)
                .in(CollectionUtils.isNotEmpty(listReq.getDishIds()), ProjDish::getId, listReq.getDishIds())
                .like(null != typeCode, ProjDish::getTypes, typeCode)
                .like(null != styleCode, ProjDish::getStyles, styleCode)
                .eq(null != status, ProjDish::getStatus, status)
                .orderByAsc(ProjDish::getSorted)
                .orderByDesc(BaseModel::getId);
        List<ProjDish> dishList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(dishList)) {
            return new ArrayList<>();
        }
        List<ProjDishListVO> dishListVO = new ArrayList<>(dishList.size());
        for (ProjDish dish : dishList) {
            ProjDishListVO dishVO = new ProjDishListVO();
            BeanUtils.copyProperties(dish, dishVO);
            dishVO.setTypeList(DishTypeEnum.getByCodesString(dish.getTypes()));
            dishVO.setStyleList(DishStyleEnum.getByCodesString(dish.getStyles()));
            dishListVO.add(dishVO);
        }
        injectionTaskStatus(dishListVO);
        return dishListVO;
    }

    @Override
    public ProjDishDetailVO findDetailById(Integer id) {
        ProjDish dish = baseMapper.selectById(id);
        ProjDishDetailVO detailVO = new ProjDishDetailVO();
        BeanUtils.copyProperties(dish, detailVO);
        detailVO.setStyleList(DishStyleEnum.getByCodesString(dish.getStyles()));
        detailVO.setTypeList(DishTypeEnum.getByCodesString(dish.getTypes()));
        List<ProjDishStepVO> stepVOList = projDishStepService.query(id);
        detailVO.setDishStepList(stepVOList);
        List<ProjIngredientVO> ingredientVOList = projIngredientService.query(id);
        detailVO.setIngredientList(ingredientVOList);
        List<ProjAllergenRelation> relationList = projAllergenRelationService.query(id, AllergenRelationBusinessEnum.DISH);
        if(CollUtil.isNotEmpty(relationList)){
            List<Integer> allergenIdList = relationList.stream().map(ProjAllergenRelation::getProjAllergenId).collect(Collectors.toList());
            detailVO.setAllergenIdList(allergenIdList);
        }
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        List<Integer> completedIdList = new ArrayList<>();
        LambdaQueryWrapper<ProjDish> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseModel::getId, idList);
        List<Integer> resourceUrlEmptyIdList = baseMapper.selectList(wrapper).stream()
                .filter(item -> StrUtil.isBlank(item.getResourceVideoUrl()))
                .map(BaseModel::getId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(resourceUrlEmptyIdList)) {
            completedIdList.addAll(resourceUrlEmptyIdList);
        }
        List<TaskResourceSection> taskList = taskResourceSectionService.query(PROJ_DISH.getTableName(), PROJ_DISH.getEntityFieldName(), idList);
        List<Integer> taskCompletedIdList = taskList.stream()
                .filter(item -> TaskResourceSectionStatusEnums.COMPLETED == item.getStatus())
                .map(TaskResourceSection::getTableId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskCompletedIdList)) {
            completedIdList.addAll(taskCompletedIdList);
        }
        if (CollUtil.isEmpty(completedIdList)) {
            return idList;
        }

        LambdaUpdateWrapper<ProjDish> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjDish::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjDish::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjDish::getId, completedIdList);
        this.update(new ProjDish(), updateWrapper);
        idList.removeAll(completedIdList);
        return idList;
    }

    private static ProjDish toProjDish(ProjDishAddReq dishReq, Integer projId) {
        ProjDish dish = new ProjDish();
        dish.setProjId(projId);
        BeanUtils.copyProperties(dishReq, dish);
        String styleCodes = DishStyleEnum.getCodeString(dishReq.getStyleList());
        dish.setStyles(styleCodes);
        String typeCodes = DishTypeEnum.getCodeString(dishReq.getTypeList());
        dish.setTypes(typeCodes);
        return dish;
    }

    private void check(Integer id, ProjDishAddReq req, Integer projId) {
        LambdaQueryWrapper<ProjDish> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjDish::getName, req.getName())
                .eq(ProjDish::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjDish> dishList = baseMapper.selectList(wrapper);
        Set<String> nameSet = dishList.stream().map(ProjDish::getName).collect(Collectors.toSet());
        if (nameSet.contains(req.getName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjDish::getEventName, req.getEventName())
                .eq(ProjDish::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        dishList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= dishList.stream().map(ProjDish::getName).collect(Collectors.toSet());
        if (eventNameSet.contains(req.getEventName())) {
            String error = "eventName already exists";
            throw new BizException(error);
        }
    }


    private void injectionTaskStatus(List<ProjDishListVO> dishList) {
        if (CollUtil.isEmpty(dishList)) {
            return;
        }
        Set<Integer> idSet = dishList.stream().map(ProjDishListVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> statusList = taskResourceSectionService.find(PROJ_DISH.getTableName(), PROJ_DISH.getEntityFieldName(), idSet);

        Map<Integer, TaskResourceSection> taskMap = statusList.stream().collect(Collectors.toMap(TaskResourceSection::getTableId, item -> item));
        for (ProjDishListVO dish : dishList) {
            if (StrUtil.isBlank(dish.getResourceVideoUrl())) {
                dish.setResourceVideoTaskStatus(TaskResourceSectionStatusEnums.COMPLETED);
                continue;
            }
            TaskResourceSection task = taskMap.get(dish.getId());
            if (null != task) {
                dish.setResourceVideoTaskStatus(task.getStatus());
            }

        }

    }
}
