package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.YogaRegularCategoryTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * yoga名言警句
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaRegularCategoryVO对象", description="ProjYogaRegularCategoryVO")
public class ProjYogaRegularCategoryVO {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "yoga regular category类型")
    private YogaRegularCategoryTypeEnum type;

    @ApiModelProperty(value = "yoga regular category类型")
    private String typeName;

}
