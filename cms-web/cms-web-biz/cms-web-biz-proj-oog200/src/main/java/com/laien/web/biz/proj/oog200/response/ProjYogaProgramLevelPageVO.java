package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/12/17 16:45
 */
@Data
public class ProjYogaProgramLevelPageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutNum = 0;

}
