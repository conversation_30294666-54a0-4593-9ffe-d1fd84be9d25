package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: hhl
 * @date: 2025/6/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_user_award")
@ApiModel(value="ProjYogaUserAward对象", description="yoga user award")
public class ProjYogaUserAward extends BaseModel {

    private Integer userId;

    private Integer awardId;

}
