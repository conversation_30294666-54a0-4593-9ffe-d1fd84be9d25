package com.laien.web.biz.proj.oog200.controller;

import cn.hutool.core.collection.CollUtil;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseTransition;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseTransitionDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseTransitionPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseTransitionService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.PROJ_YOGA_POSE_TRANSITION_FRONT;


/**
 * Author:  hhl
 * Date:  2024/8/1 11:20
 */
@Api(tags = "项目管理: Pose Library Transition")
@RestController
@RequestMapping("/proj/yogaPoseTransition")
public class ProjYogaPoseTransitionController extends ResponseController {

    @Resource
    private IProjYogaPoseTransitionService poseTransitionService;

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaPoseTransitionPageVO>> page(ProjYogaPoseTransitionPageReq pageReq) {
        PageRes<ProjYogaPoseTransitionPageVO> pageRes = poseTransitionService.selectPoseTransitionPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaPoseTransitionAddReq addReq) {
        poseTransitionService.savePoseTransition(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaPoseTransitionUpdateReq updateReq) {
        poseTransitionService.updatePoseTransition(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaPoseTransitionDetailVO> detail(@PathVariable Integer id) {
        ProjYogaPoseTransitionDetailVO detailVO = poseTransitionService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        //校验切片任务是否成功
        idList = idList.stream().distinct().collect(Collectors.toList());
        List<TaskResourceSection> taskList = taskResourceSectionService.query(PROJ_YOGA_POSE_TRANSITION_FRONT.getTableName(), PROJ_YOGA_POSE_TRANSITION_FRONT.getEntityFieldName(), idList);
        if (CollUtil.isEmpty(taskList) || idList.size() != taskList.size()) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }

        Set<Integer> completedIdSet = TaskResourceSectionUtil.listNotCompletedId(taskList);
        if (!CollUtil.isEmpty(completedIdSet)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }

        poseTransitionService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        poseTransitionService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        poseTransitionService.deleteByIds(idList);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入Pose Transition")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {

        return ResponseResult.succ(poseTransitionService.importPoseTransition(excel));
    }


}
