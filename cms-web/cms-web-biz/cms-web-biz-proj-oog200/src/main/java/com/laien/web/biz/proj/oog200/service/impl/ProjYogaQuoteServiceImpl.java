package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaQuote;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaQuoteMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuoteAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuotePageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuoteUpdateReq;
import com.laien.web.biz.proj.oog200.request.YogaQuoteImportReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaQuoteDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaQuoteService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * yoga名言警句 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
public class ProjYogaQuoteServiceImpl extends ServiceImpl<ProjYogaQuoteMapper, ProjYogaQuote> implements IProjYogaQuoteService {

    @Resource
    private Validator validator;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public void save(ProjYogaQuoteAddReq req, Integer projId) {
        ProjYogaQuote yogaQuote = new ProjYogaQuote();
        BeanUtils.copyProperties(req, yogaQuote);
        yogaQuote.setProjId(projId);
        save(yogaQuote);
        projLmsI18nService.handleI18n(Collections.singletonList(yogaQuote), yogaQuote.getProjId());
    }

    @Override
    public void update(ProjYogaQuoteUpdateReq req, Integer projId) {
        ProjYogaQuote yogaQuote = new ProjYogaQuote();
        BeanUtils.copyProperties(req, yogaQuote);
        yogaQuote.setProjId(projId);
        updateById(yogaQuote);
        projLmsI18nService.handleI18n(Collections.singletonList(yogaQuote), yogaQuote.getProjId());
    }

    @Override
    public ProjYogaQuoteDetailVO findDetailById(Integer id) {
        if (null == id) {
            return null;
        }
        ProjYogaQuote yogaQuote = baseMapper.selectById(id);
        ProjYogaQuoteDetailVO detailVO = new ProjYogaQuoteDetailVO();
        BeanUtils.copyProperties(yogaQuote, detailVO);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjYogaQuote> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaQuote::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaQuote::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaQuote::getId, idList);
        update(new ProjYogaQuote(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaQuote> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaQuote::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaQuote::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaQuote::getId, idList);
        this.update(new ProjYogaQuote(), wrapper);
    }

    @Override
    public PageRes<ProjYogaQuoteDetailVO> page(ProjYogaQuotePageReq pageReq, Integer projId) {
        Integer id = pageReq.getId();
        Integer status = pageReq.getStatus();
        LambdaQueryWrapper<ProjYogaQuote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(null != id, BaseModel::getId, id)
                .eq(null != status, ProjYogaQuote::getStatus, status)
                .orderByDesc(BaseModel::getId);
        Page<ProjYogaQuote> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        page(page, wrapper);
        return PageConverter.convert(page, ProjYogaQuoteDetailVO.class);
    }

    @Transactional
    @Override
    public List<String> importByExcel(InputStream inputStream, Integer projId) {
        List<YogaQuoteImportReq> videoListReq = CollUtil.newArrayList();
        EasyExcel.read(inputStream, YogaQuoteImportReq.class, new AnalysisEventListener<YogaQuoteImportReq>() {
            @Override
            public void invoke(YogaQuoteImportReq row, AnalysisContext analysisContext) {
                videoListReq.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        if (CollUtil.isEmpty(videoListReq)) {
            return null;
        }
        List<ProjYogaQuote> addVideoList = new ArrayList<>(videoListReq.size());
        List<String> failMessage = CollUtil.newArrayList();
        for (YogaQuoteImportReq quoteReq : videoListReq) {
            Set<ConstraintViolation<YogaQuoteImportReq>> violationSet = validator.validate(quoteReq, Group.class);
            if (CollUtil.isNotEmpty(violationSet)) {
                violationSet.stream().findFirst().ifPresent(violation -> failMessage.add(violation.getMessage()));
                continue;
            }

            ProjYogaQuote yogaQuote = new ProjYogaQuote();
            BeanUtils.copyProperties(quoteReq, yogaQuote);
            yogaQuote.setStatus(GlobalConstant.STATUS_DRAFT);
            yogaQuote.setProjId(projId);
            addVideoList.add(yogaQuote);
        }
        if (CollUtil.isEmpty(addVideoList)) {
            return failMessage;
        }
        saveBatch(addVideoList);

        projLmsI18nService.handleI18n(addVideoList, projId);
        return failMessage;
    }

    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjYogaQuote> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaQuote::getDelFlag, GlobalConstant.YES)
                .in(ProjYogaQuote::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY)
                .in(ProjYogaQuote::getId, idList);
        this.update(new ProjYogaQuote(), wrapper);
    }
}
