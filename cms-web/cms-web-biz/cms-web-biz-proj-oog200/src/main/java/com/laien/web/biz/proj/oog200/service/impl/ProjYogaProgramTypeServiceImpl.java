package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramType;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramTypeMapper;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramTypeVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/23 16:32
 */
@Slf4j
@Service
public class ProjYogaProgramTypeServiceImpl extends ServiceImpl<ProjYogaProgramTypeMapper, ProjYogaProgramType> implements IProjYogaProgramTypeService {

    @Override
    public List<ProjYogaProgramTypeVO> listVOByProjId(Integer projId) {

        if (Objects.isNull(projId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaProgramType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaProgramType::getProjId, projId);
        List<ProjYogaProgramType> programTypes = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(programTypes)) {
            return Collections.emptyList();
        }

        return programTypes.stream().map(this::convert2VO).collect(Collectors.toList());
    }

    private ProjYogaProgramTypeVO convert2VO(ProjYogaProgramType projYogaProgramType) {

        ProjYogaProgramTypeVO typeVO = new ProjYogaProgramTypeVO();
        BeanUtils.copyProperties(projYogaProgramType, typeVO);
        return typeVO;
    }

    @Override
    public List<ProjYogaProgramTypeVO> listVOByIds(Collection<Integer> ids) {


        return Collections.emptyList();
    }
}
