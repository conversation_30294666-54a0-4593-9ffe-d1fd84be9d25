package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ResVideoSlice;
import com.laien.web.biz.proj.oog200.entity.ResVideoSliceI18n;
import com.laien.web.biz.proj.oog200.mapper.ResVideoSliceMapper;
import com.laien.web.biz.proj.oog200.request.ResVideoSliceAddReq;
import com.laien.web.biz.proj.oog200.request.ResVideoSliceI18nReq;
import com.laien.web.biz.proj.oog200.request.ResVideoSlicePageReq;
import com.laien.web.biz.proj.oog200.request.ResVideoSliceUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.response.ResVideoSliceI18nVO;
import com.laien.web.biz.proj.oog200.response.ResVideoSlicePageVO;
import com.laien.web.biz.proj.oog200.service.IResVideoSliceI18nService;
import com.laien.web.biz.proj.oog200.service.IResVideoSliceService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * video slice 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
public class ResVideoSliceServiceImpl extends ServiceImpl<ResVideoSliceMapper, ResVideoSlice> implements IResVideoSliceService {

    private static final String DEFAULT_LANGUAGE = "en";

    @Resource
    private IResVideoSliceI18nService resVideoSliceI18nService;

    @Override
    public PageRes<ResVideoSlicePageVO> selectVideoSlicePage(ResVideoSlicePageReq pageReq, Integer dataVersion) {
        LambdaQueryWrapper<ResVideoSlice> queryWrapper = new LambdaQueryWrapper<>();
        String videoName = pageReq.getVideoName();
        queryWrapper.like(StringUtils.isNotBlank(videoName), ResVideoSlice::getVideoName, videoName);
        String videoType = pageReq.getVideoType();
        queryWrapper.eq(StringUtils.isNotBlank(videoType), ResVideoSlice::getVideoType, videoType);
        String videoCode = pageReq.getVideoCode();
        queryWrapper.eq(StringUtils.isNotBlank(videoCode), ResVideoSlice::getVideoCode, videoCode);
        Integer status = pageReq.getStatus();
        queryWrapper.eq(Objects.nonNull(status), ResVideoSlice::getStatus, status);
        queryWrapper.eq(ResVideoSlice::getDataVersion, dataVersion);
        queryWrapper.orderByDesc(ResVideoSlice::getId);

        // 查询
        Page<ResVideoSlice> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.page(page, queryWrapper);
        List<ResVideoSlice> list = page.getRecords();
        List<ResVideoSlicePageVO> copyList = new ArrayList<>(list.size());
        for (ResVideoSlice videoSlice : list) {
            ResVideoSlicePageVO pageVO = new ResVideoSlicePageVO();
            BeanUtils.copyProperties(videoSlice, pageVO);
            copyList.add(pageVO);
        }

        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), copyList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVideoSlice(ResVideoSliceAddReq videoSliceAddReq) {

        // 保证主数据
        ResVideoSlice videoSlice = new ResVideoSlice();
        BeanUtils.copyProperties(videoSliceAddReq, videoSlice);

        // 默认语言设置到主数据展示
        List<ResVideoSliceI18nReq> i18nList = videoSliceAddReq.getI18nList();
        for (ResVideoSliceI18nReq videoSliceI18nReq : i18nList) {
            if (Objects.equals(videoSliceI18nReq.getLanguage(), DEFAULT_LANGUAGE)) {
                videoSlice.setTitleSubtitleUrl(videoSliceI18nReq.getTitleSubtitleUrl());
                videoSlice.setGuidanceDefaultUrl(videoSliceI18nReq.getGuidanceDefaultUrl());
                videoSlice.setGuidanceDefaultAudioUrl(videoSliceI18nReq.getGuidanceDefaultAudioUrl());
                videoSlice.setGuidanceLeastUrl(videoSliceI18nReq.getGuidanceLeastUrl());
                videoSlice.setGuidanceLeastAudioUrl(videoSliceI18nReq.getGuidanceLeastAudioUrl());
                break;
            }
        }
        videoSlice.setDataVersion(GlobalConstant.THREE);
        videoSlice.setFocus(MyStringUtil.getJoinWithComma(videoSliceAddReq.getFocusArr()));
        this.save(videoSlice);

        // 保存多语言
        Integer id = videoSlice.getId();
        List<ResVideoSliceI18n> i18nSaveList = new ArrayList<>(i18nList.size());
        for (ResVideoSliceI18nReq videoSliceI18nReq : i18nList) {
            ResVideoSliceI18n videoSliceI18n = new ResVideoSliceI18n();
            BeanUtils.copyProperties(videoSliceI18nReq, videoSliceI18n);
            videoSliceI18n.setId(id);
            i18nSaveList.add(videoSliceI18n);
        }
        resVideoSliceI18nService.saveBatch(i18nSaveList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateVideoSlice(ResVideoSliceUpdateReq videoSliceUpdateReq) {
        Integer id = videoSliceUpdateReq.getId();
        ResVideoSlice videoSliceFind = this.getById(id);
        if (Objects.isNull(videoSliceFind)) {
            throw new BizException("Data not found");
        }

        if (Objects.equals(videoSliceUpdateReq.getStatus(), GlobalConstant.STATUS_DRAFT)
                && !Objects.equals(videoSliceFind.getStatus(), GlobalConstant.STATUS_DRAFT)) {
            // 保存为草稿，原来的状态不是草稿，不能修改
            videoSliceUpdateReq.setStatus(videoSliceFind.getStatus());
        } else if(Objects.equals(videoSliceUpdateReq.getStatus(), GlobalConstant.STATUS_ENABLE)
                && Objects.equals(videoSliceFind.getStatus(), GlobalConstant.STATUS_DRAFT)) {
            // 保存，可以将草稿状态的启用
        } else {
            // 其他情况不处理状态
            videoSliceUpdateReq.setStatus(null);
        }

        ResVideoSlice videoSlice = new ResVideoSlice();
        BeanUtils.copyProperties(videoSliceUpdateReq, videoSlice);

        // 默认语言设置到主数据展示
        List<ResVideoSliceI18nReq> i18nList = videoSliceUpdateReq.getI18nList();
        List<ResVideoSliceI18n> i18nSaveList = new ArrayList<>();
        for (ResVideoSliceI18nReq videoSliceI18nReq : i18nList) {
            // 默认语言设置到主数据展示
            if (Objects.equals(videoSliceI18nReq.getLanguage(), DEFAULT_LANGUAGE)) {
                videoSlice.setTitleSubtitleUrl(videoSliceI18nReq.getTitleSubtitleUrl());
                videoSlice.setGuidanceDefaultUrl(videoSliceI18nReq.getGuidanceDefaultUrl());
                videoSlice.setGuidanceDefaultAudioUrl(videoSliceI18nReq.getGuidanceDefaultAudioUrl());
                videoSlice.setGuidanceLeastUrl(videoSliceI18nReq.getGuidanceLeastUrl());
                videoSlice.setGuidanceLeastAudioUrl(videoSliceI18nReq.getGuidanceLeastAudioUrl());
            }

            ResVideoSliceI18n videoSliceI18nSave = new ResVideoSliceI18n();
            BeanUtils.copyProperties(videoSliceI18nReq, videoSliceI18nSave);
            videoSliceI18nSave.setId(id);
            i18nSaveList.add(videoSliceI18nSave);
        }

        videoSlice.setFocus(MyStringUtil.getJoinWithComma(videoSliceUpdateReq.getFocusArr()));
        this.updateById(videoSlice);

        // 物理删除之前的多语言
        resVideoSliceI18nService.deleteByIdReal(id);
        // 保存新的多语言
        resVideoSliceI18nService.saveBatch(i18nSaveList);
    }

    /**
     * 验证名称是否重复
     *
     * @param videoName  videoName
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectNameExists(String videoName, Integer excludeId) {
        LambdaQueryWrapper<ResVideoSlice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideoSlice::getVideoName, videoName);
        queryWrapper.ne(Objects.nonNull(excludeId), ResVideoSlice::getId, excludeId);
        int counts = this.count(queryWrapper);
        return counts > GlobalConstant.ZERO;
    }

    @Override
    public ResVideoSliceDetailVO getVideoSliceDetail(Integer id) {
        ResVideoSlice videoSliceFind = this.getById(id);
        if (Objects.isNull(videoSliceFind)) {
            throw new BizException("Data not found");
        }
        ResVideoSliceDetailVO detailVO = new ResVideoSliceDetailVO();
        BeanUtils.copyProperties(videoSliceFind, detailVO);
        detailVO.setFocusArr(MyStringUtil.getSplitWithComa(videoSliceFind.getFocus()));

        List<ResVideoSliceI18nVO> i18nList = resVideoSliceI18nService.selectById(id);
        detailVO.setI18nList(i18nList);

        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResVideoSlice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideoSlice::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.eq(ResVideoSlice::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ResVideoSlice::getId, idList);
        this.update(new ResVideoSlice(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResVideoSlice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideoSlice::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ResVideoSlice::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResVideoSlice::getId, idList);
        this.update(new ResVideoSlice(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResVideoSlice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideoSlice::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ResVideoSlice::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ResVideoSlice::getId, idList);
        this.update(new ResVideoSlice(), wrapper);
    }

}
