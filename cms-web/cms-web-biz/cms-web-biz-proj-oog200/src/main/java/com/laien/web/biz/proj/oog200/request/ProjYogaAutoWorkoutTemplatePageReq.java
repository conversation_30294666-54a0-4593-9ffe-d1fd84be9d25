package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjYogaAutoWorkoutTemplatePageReq", description = "ProjYogaAutoWorkoutTemplatePageReq")
public class ProjYogaAutoWorkoutTemplatePageReq extends PageReq {

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "template类型：Classic Yoga、Wall Pilates、Chair Yoga")
    private String type;
}
