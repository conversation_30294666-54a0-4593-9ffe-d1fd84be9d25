package com.laien.web.biz.proj.oog200.response;

import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2024/7/31 14:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjYogaPoseVideoPageVO extends ProjOperationDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "English Voice Name")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "动作类型 单选 Begin、Main")
    private String poseType;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "动作朝向，0 -> Central、1 -> Left、2 -> Right")
    private String poseDirection;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "正机位切片任务状态")
    private TaskResourceSectionStatusEnums frontTaskStatus;

    @ApiModelProperty(value = "侧机位切片任务状态")
    private TaskResourceSectionStatusEnums sideTaskStatus;
}
