package com.laien.web.biz.proj.oog200.service.impl;

import com.laien.web.biz.proj.oog200.entity.ProjPoseLibrary;
import com.laien.web.biz.proj.oog200.mapper.ProjPoseLibraryMapper;
import com.laien.web.biz.proj.oog200.service.IProjPoseLibraryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 项目poseLibrary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Service
public class ProjPoseLibraryServiceImpl extends ServiceImpl<ProjPoseLibraryMapper, ProjPoseLibrary> implements IProjPoseLibraryService {

}
