package com.laien.web.biz.proj.oog200.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog200.enums.AutoWorkoutBasicInfoPointEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_image
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Data
@Accessors(chain = true)
@TableName("proj_auto_workout_basic_info")
@ApiModel(value="ProjAutoWorkoutBasicInfo对象", description="proj_image")
public class ProjAutoWorkoutBasicInfoListReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "图片用途")
    private AutoWorkoutBasicInfoPointEnum point;

    @ApiModelProperty(value = "plan类型")
    private YogaAutoWorkoutTemplateEnum planType;

    @ApiModelProperty(value = "difficulty类型")
    private DifficultyEnum difficulty;

    @ApiModelProperty(value = "autoWorkoutId")
    private Integer autoWorkoutId;


}
