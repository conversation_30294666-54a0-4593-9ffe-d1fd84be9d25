package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.laien.web.biz.proj.oog200.entity.ResPoseLibrary;
import com.laien.web.biz.proj.oog200.mapper.ResPoseLibraryMapper;
import com.laien.web.biz.proj.oog200.service.IResPoseLibraryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * pose表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Service
public class ResPoseLibraryServiceImpl extends ServiceImpl<ResPoseLibraryMapper, ResPoseLibrary> implements IResPoseLibraryService {

    @Override
    public List<ResPoseLibrary> listByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayListWithCapacity(0);
        }
        return getBaseMapper().selectListByIds(Joiner.on(",").join(ids));
    }

    @Override
    public List<ResPoseLibrary> listByIds(List<Integer> ids, Integer pageNum, Integer pageSize, String orderByName, String orderBy) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayListWithCapacity(0);
        }
        return getBaseMapper().selectOrderListByIds(Joiner.on(",").join(ids), orderByName, orderBy, (pageNum - 1) * pageSize, pageSize);
    }
}
