package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.GoalEnum;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjYogaAutoWorkoutTempWorkoutPageReq", description = "ProjYogaAutoWorkoutTempWorkoutPageReq")
public class ProjYogaAutoWorkoutTempWorkoutPageReq extends PageReq {

    @ApiModelProperty(value = "workout id")
    private Integer id;

    @ApiModelProperty(value = " 1-小于10 ，2-10-20，3-大于20")
    private Integer time;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "workout启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "template启用状态 0草稿 1启用 2停用")
    private Integer templateStatus;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "生成任务id")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "生成模板id")
    private Integer projYogaAutoWorkoutTemplateId;

    @ApiModelProperty(value = "goal")
    private GoalEnum goal;
}
