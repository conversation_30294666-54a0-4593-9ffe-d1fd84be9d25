package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.web.biz.proj.oog200.mapper.ResYogaVideoMapper;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.*;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.RES_YOGA_VIDEO_FRONT;
import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.RES_YOGA_VIDEO_SIDE;


/**
 * <p>
 * 瑜伽视频 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Slf4j
@Service
public class ResYogaVideoServiceImpl extends ServiceImpl<ResYogaVideoMapper, ResYogaVideo> implements IResYogaVideoService {

    @Resource
    private IResYogaVideoConnectionService resYogaVideoConnectionService;
    @Resource
    private IResYogaVideoCooldownService resYogaVideoCooldownService;
    @Resource
    private IResTransitionService resTransitionService;
    @Resource
    private Validator validator;
    @Resource
    private ITaskResourceSectionService taskResourceSectionService;
    @Resource
    private IProjClassicYogaVideoPoseRelationService poseRelationService;
    @Resource
    private IProjYogaPoseVideoService poseVideoService;
    @Resource
    private IProjLmsI18nService i18nService;
    @Resource
    private IProjInfoService projInfoService;
    @Resource
    private ICoreVoiceConfigI18nService i18nConfigService;

    private static final String CORPSE = "Corpse";

    @Override
    public PageRes<ResYogaVideoPageVO> selectYogaVideoPage(ResYogaVideoPageReq pageReq) {
        Page<ResYogaVideoPageVO> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        selectByRelationPoseVideo(pageReq);
        this.baseMapper.selectYogaVideoPage(page, pageReq);
        PageRes<ResYogaVideoPageVO> result = PageConverter.convert(page);
        this.injectionTaskStatus(result.getList());
        setPoseVideoInfo(result.getList());
        return result;
    }

    private void selectByRelationPoseVideo(ResYogaVideoPageReq pageReq) {

        if (Objects.isNull(pageReq) || Objects.isNull(pageReq.getRelationPoseVideoId())) {
            return;
        }

        List<ProjClassicYogaVideoPoseRelation> poseRelationList = poseRelationService.listRelationByPoseVideoIds(Lists.newArrayList(pageReq.getRelationPoseVideoId()));
        if (CollectionUtils.isEmpty(poseRelationList)) {
            return;
        }

        List<Integer> yogaVideoIds = poseRelationList.stream().map(ProjClassicYogaVideoPoseRelation::getResYogaVideoId).collect(Collectors.toList());
        pageReq.setYogaVideoIds(yogaVideoIds);
    }

    private void setPoseVideoInfo(List<ResYogaVideoPageVO> pageVOList) {

        if (CollectionUtils.isEmpty(pageVOList)) {
            return;
        }

        List<Integer> yogaVideoIds = pageVOList.stream().map(ResYogaVideoPageVO::getId).collect(Collectors.toList());
        List<ProjClassicYogaVideoPoseRelation> poseRelationList = poseRelationService.listRelationByYogaVideoIds(yogaVideoIds);
        if (CollectionUtils.isEmpty(poseRelationList)) {
            return;
        }

        List<Integer> poseVideoIds = poseRelationList.stream().map(relation -> relation.getProjYogaPoseVideoId()).collect(Collectors.toList());
        Collection<ProjYogaPoseVideo> poseVideoList = poseVideoService.listByIds(poseVideoIds);
        if (CollectionUtils.isEmpty(poseVideoList)) {
            return;
        }

        Map<Integer, Integer> yogaAndPoseIdMap = poseRelationList.stream().collect(Collectors.toMap(ProjClassicYogaVideoPoseRelation::getResYogaVideoId, ProjClassicYogaVideoPoseRelation::getProjYogaPoseVideoId, (k1, k2) -> k1));
        Map<Integer, ProjBaseDetailVO> poseVideoIdMap = poseVideoList.stream().collect(Collectors.toMap(poseVideo -> poseVideo.getId(), poseVideo -> convert2BaseDetail(poseVideo)));

        pageVOList.forEach(yogaVideoVO -> {
            if (yogaAndPoseIdMap.containsKey(yogaVideoVO.getId()) && poseVideoIdMap.containsKey(yogaAndPoseIdMap.get(yogaVideoVO.getId()))) {
                yogaVideoVO.setRelationPoseVideo(poseVideoIdMap.get(yogaAndPoseIdMap.get(yogaVideoVO.getId())));
                yogaVideoVO.setRelationPoseVideoId(poseVideoIdMap.get(yogaAndPoseIdMap.get(yogaVideoVO.getId())).getId());
            }
        });
    }

    private ProjBaseDetailVO getPoseVideoInfo(ResYogaVideo yogaVideo, ResYogaVideoDetailVO yogaVideoDetailVO) {

        if (Objects.isNull(yogaVideo)) {
            return null;
        }

        List<ProjClassicYogaVideoPoseRelation> poseRelationList = poseRelationService.listRelationByYogaVideoIds(Lists.newArrayList(yogaVideo.getId()));
        if (CollectionUtils.isEmpty(poseRelationList)) {
            return null;
        }

        List<Integer> poseVideoIds = poseRelationList.stream().map(relation -> relation.getProjYogaPoseVideoId()).collect(Collectors.toList());
        List<ProjYogaPoseVideo> poseVideoList = (List<ProjYogaPoseVideo>) poseVideoService.listByIds(poseVideoIds);
        if (CollectionUtils.isEmpty(poseVideoList)) {
            return null;
        }

        ProjYogaPoseVideo poseVideo = poseVideoList.get(GlobalConstant.ZERO);
        yogaVideoDetailVO.setRelationPoseVideoId(poseVideo.getId());
        return convert2BaseDetail(poseVideo);
    }

    private ProjBaseDetailVO convert2BaseDetail(ProjYogaPoseVideo poseVideo) {

        ProjBaseDetailVO detailVO = new ProjBaseDetailVO();
        BeanUtils.copyProperties(poseVideo, detailVO);
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveYogaVideo(ResYogaVideoAddReq yogaVideoAddReq) {
        this.check(yogaVideoAddReq, null);
        ResYogaVideo yogaVideo = new ResYogaVideo();
        BeanUtils.copyProperties(yogaVideoAddReq, yogaVideo);
        yogaVideo.setType(MyStringUtil.getJoinWithComma(yogaVideoAddReq.getTypeArr()));
        yogaVideo.setFocus(MyStringUtil.getJoinWithComma(yogaVideoAddReq.getFocusArr()));
        yogaVideo.setSpecialLimit(MyStringUtil.getJoinWithComma(yogaVideoAddReq.getSpecialLimitArr()));
        yogaVideo.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(yogaVideo);

        // 新增YogaVideo关系
        this.saveYogaVideoRelation(yogaVideo.getId(), yogaVideoAddReq);

        saveOrUpdateYogaVideoPoseRelation(yogaVideo.getId(), yogaVideoAddReq.getRelationPoseVideoId());
        i18nService.handleI18n(Collections.singletonList(yogaVideo), projInfoService.find(ProjCodeEnums.OOG200.getAppCode()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateYogaVideo(ResYogaVideoUpdateReq yogaVideoUpdateReq) {
        Integer id = yogaVideoUpdateReq.getId();
        ResYogaVideo yogaVideoFind = this.getById(id);
        if (Objects.isNull(yogaVideoFind)) {
            throw new BizException("Data not found");
        }
        this.check(yogaVideoUpdateReq, id);
        ResYogaVideo yogaVideo = new ResYogaVideo();
        BeanUtils.copyProperties(yogaVideoUpdateReq, yogaVideo);
        yogaVideo.setType(MyStringUtil.getJoinWithComma(yogaVideoUpdateReq.getTypeArr()));
        yogaVideo.setFocus(MyStringUtil.getJoinWithComma(yogaVideoUpdateReq.getFocusArr()));
        yogaVideo.setSpecialLimit(MyStringUtil.getJoinWithComma(yogaVideoUpdateReq.getSpecialLimitArr()));

        this.updateById(yogaVideo);

        // 删除YogaVideo关系
        this.deleteYogaVideoRelation(id);
        // 新增YogaVideo关系
        this.saveYogaVideoRelation(id, yogaVideoUpdateReq);

        this.saveOrUpdateYogaVideoPoseRelation(id, yogaVideoUpdateReq.getRelationPoseVideoId());
        i18nService.handleI18n(Collections.singletonList(yogaVideo), projInfoService.find(ProjCodeEnums.OOG200.getAppCode()));
    }

    private void saveOrUpdateYogaVideoPoseRelation(Integer yogaVideoId, Integer poseVideoId) {

        poseRelationService.deleteByYogaVideoId(yogaVideoId);
        if (Objects.isNull(poseVideoId)) {
            return;
        }

        ProjClassicYogaVideoPoseRelation poseRelation = new ProjClassicYogaVideoPoseRelation();
        poseRelation.setResYogaVideoId(yogaVideoId);
        poseRelation.setProjYogaPoseVideoId(poseVideoId);
        poseRelationService.save(poseRelation);
    }

    /**
     * 数据校验
     *
     * @param yogaVideoAddReq yogaVideoAddReq
     * @param id              id
     */
    private void check(ResYogaVideoAddReq yogaVideoAddReq, Integer id) {
        List<ResYogaVideoNextAddReq> nextList = yogaVideoAddReq.getNextList();
        if (Objects.equals(CORPSE, yogaVideoAddReq.getName()) && nextList != null && nextList.size() > 0) {
            throw new BizException("Corpse cannot select next");
        }

        Set<Integer> videoIdSet = new HashSet<>();
        Set<Integer> transitionIdSet = new HashSet<>();
        if (Objects.nonNull(nextList) && !nextList.isEmpty()) {
            for (ResYogaVideoNextAddReq nextAddReq : nextList) {
                Integer nextId = nextAddReq.getYogaVideo().getId();
                // 不能选择自己
                if (Objects.equals(nextId, id)) {
                    throw new BizException("Cannot selected self in next");
                }
                videoIdSet.add(nextId);
                transitionIdSet.add(nextAddReq.getTransition().getId());
            }
        }

        Set<Integer> videoCooldownIdSet = new HashSet<>();
        List<List<ResYogaVideoCooldownAddReq>> coolDownList = yogaVideoAddReq.getCoolDownList();
        if (Objects.nonNull(coolDownList) && !coolDownList.isEmpty()) {
            for (int i = 0; i < coolDownList.size(); i++) {
                List<ResYogaVideoCooldownAddReq> resYogaVideoCooldownAddReqs = coolDownList.get(i);
                for (int j = 0; j < resYogaVideoCooldownAddReqs.size(); j++) {
                    Integer cooldownVideoId = resYogaVideoCooldownAddReqs.get(j).getId();
                    if (j == 0) {
                        // 第一个必须在next部分配置过
                        if (!videoIdSet.contains(cooldownVideoId)) {
                            throw new BizException("The first Cooldown must be the one selected in Next");
                        }
                    }
                    videoCooldownIdSet.add(cooldownVideoId);
                }
            }
        }

        videoIdSet.addAll(videoCooldownIdSet);

        Map<Integer, ResYogaVideo> yogaVideoMap;
        if (!videoIdSet.isEmpty()) {
            // 查询next yogaVideo
            LambdaQueryWrapper<ResYogaVideo> queryYogaVideoWrapper = new LambdaQueryWrapper<>();
            queryYogaVideoWrapper.in(ResYogaVideo::getId, videoIdSet);
            yogaVideoMap = this.list(queryYogaVideoWrapper).stream().collect(Collectors.toMap(ResYogaVideo::getId, v -> v));
        } else {
            yogaVideoMap = new HashMap<>();
        }

        Map<Integer, ResTransition> transitionMap;
        if (!transitionIdSet.isEmpty()) {
            // 查询next transition
            LambdaQueryWrapper<ResTransition> queryTransitionWrapper = new LambdaQueryWrapper<>();
            queryTransitionWrapper.in(ResTransition::getId, transitionIdSet);
            transitionMap = resTransitionService.list(queryTransitionWrapper).stream().collect(Collectors.toMap(ResTransition::getId, v -> v));
        } else {
            transitionMap = new HashMap<>();
        }

        Set<String> videoNameSet = new HashSet<>();
        Set<String> transitionNameSet = new HashSet<>();
        if (Objects.nonNull(nextList) && !nextList.isEmpty()) {
            for (ResYogaVideoNextAddReq nextAddReq : nextList) {
                Integer nextId = nextAddReq.getYogaVideo().getId();
                ResYogaVideo yogaVideo = yogaVideoMap.get(nextId);
                Integer transitionId = nextAddReq.getTransition().getId();
                ResTransition transition = transitionMap.get(transitionId);

                if (yogaVideo == null) {
                    throw new BizException("Video Data id(" + nextId + ") in next not found");
                }
                if (transition == null) {
                    throw new BizException("TransitionData id(" + transitionId + ") in next not found");
                }

                String videoName = yogaVideo.getName();
                if (videoNameSet.contains(videoName)) {
                    throw new BizException("Video Data name(" + videoName + ") in next cannot be duplicated");
                }
                videoNameSet.add(videoName);

                String transitionName = transition.getName();
                if (transitionNameSet.contains(transitionName)) {
                    throw new BizException("TransitionData name(" + transitionName + ") in next cannot be duplicated");
                }
                transitionNameSet.add(transitionName);
            }
        }

        if (Objects.nonNull(coolDownList) && !coolDownList.isEmpty()) {
            for (int i = 0; i < coolDownList.size(); i++) {
                List<ResYogaVideoCooldownAddReq> resYogaVideoCooldownAddReqs = coolDownList.get(i);
                for (int j = 0; j < resYogaVideoCooldownAddReqs.size(); j++) {
                    Integer cooldownVideoId = resYogaVideoCooldownAddReqs.get(j).getId();
                    ResYogaVideo yogaVideo = yogaVideoMap.get(cooldownVideoId);
                    if (yogaVideo == null) {
                        throw new BizException("Video Data id(" + cooldownVideoId + ") in Cooldown not found");
                    }
                }
            }
        }
    }

    /**
     * 保存下一个链接动作
     *
     * @param id              id
     * @param yogaVideoAddReq yogaVideoAddReq
     */
    private void saveYogaVideoRelation(Integer id, ResYogaVideoAddReq yogaVideoAddReq) {
        List<ResYogaVideoNextAddReq> nextList = yogaVideoAddReq.getNextList();
        if (Objects.nonNull(nextList) && !nextList.isEmpty()) {
            List<ResYogaVideoConnection> connectionList = new ArrayList<>();
            for (ResYogaVideoNextAddReq nextAddReq : nextList) {
                ResYogaVideoConnection videoConnection = new ResYogaVideoConnection();
                videoConnection.setResYogaVideoNextId(nextAddReq.getYogaVideo().getId());
                videoConnection.setResTransitionId(nextAddReq.getTransition().getId());
                videoConnection.setResYogaVideoId(id);
                connectionList.add(videoConnection);
            }
            resYogaVideoConnectionService.saveBatch(connectionList);
        }

        List<List<ResYogaVideoCooldownAddReq>> coolDownList = yogaVideoAddReq.getCoolDownList();
        if (Objects.nonNull(coolDownList) && !coolDownList.isEmpty()) {
            List<ResYogaVideoCooldown> videoCooldownList = new ArrayList<>();
            for (int i = 0; i < coolDownList.size(); i++) {
                List<ResYogaVideoCooldownAddReq> resYogaVideoCooldownAddReqs = coolDownList.get(i);
                for (int j = 0; j < resYogaVideoCooldownAddReqs.size(); j++) {
                    ResYogaVideoCooldownAddReq cooldownAddReq = resYogaVideoCooldownAddReqs.get(j);
                    ResYogaVideoCooldown videoCooldown = new ResYogaVideoCooldown();
                    // 最后两位为分组索引，业务几乎不可能加100组，前面为数据的id,做到分组全局唯一
                    videoCooldown.setGroupId(id * 100 + i);
                    videoCooldown.setResYogaVideoLinkId(id);
                    videoCooldown.setResYogaVideoId(cooldownAddReq.getId());
                    int nextIndex = j + 1;
                    if (nextIndex < resYogaVideoCooldownAddReqs.size()) {
                        ResYogaVideoCooldownAddReq cooldownAddNextReq = resYogaVideoCooldownAddReqs.get(nextIndex);
                        videoCooldown.setResYogaVideoNextId(cooldownAddNextReq.getId());
                        Integer resTransitionId = this.getTransitionId(cooldownAddReq.getId(), cooldownAddNextReq.getId());
                        videoCooldown.setResTransitionId(resTransitionId);
                    }

                    videoCooldownList.add(videoCooldown);
                }
            }

            resYogaVideoCooldownService.saveBatch(videoCooldownList);
        }
    }

    /**
     * 根据resYogaVideoId和resYogaVideoNextId查询resTransitionId
     *
     * @param resYogaVideoId     resYogaVideoId
     * @param resYogaVideoNextId resYogaVideoNextId
     * @return Integer
     */
    private Integer getTransitionId(Integer resYogaVideoId, Integer resYogaVideoNextId) {
        LambdaQueryWrapper<ResYogaVideoConnection> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResYogaVideoConnection::getResYogaVideoId, resYogaVideoId);
        wrapper.eq(ResYogaVideoConnection::getResYogaVideoNextId, resYogaVideoNextId);
        ResYogaVideoConnection videoConnection = resYogaVideoConnectionService.getOne(wrapper);
        if (videoConnection == null) {
            throw new BizException("Transition not found between ID " + resYogaVideoId + " and ID " + resYogaVideoNextId);
        }
        return videoConnection.getResTransitionId();
    }

    /**
     * 删除YogaVideoConnection
     *
     * @param id id
     */
    private void deleteYogaVideoRelation(Integer id) {
        LambdaUpdateWrapper<ResYogaVideoConnection> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResYogaVideoConnection::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ResYogaVideoConnection::getResYogaVideoId, id);
        resYogaVideoConnectionService.update(new ResYogaVideoConnection(), wrapper);

        LambdaUpdateWrapper<ResYogaVideoCooldown> cooldownWrapper = new LambdaUpdateWrapper<>();
        cooldownWrapper.set(ResYogaVideoCooldown::getDelFlag, GlobalConstant.YES);
        cooldownWrapper.eq(ResYogaVideoCooldown::getResYogaVideoLinkId, id);
        resYogaVideoCooldownService.update(new ResYogaVideoCooldown(), cooldownWrapper);
    }

    @Override
    public ResYogaVideoDetailVO getYogaVideoDetail(Integer id) {
        ResYogaVideo yogaVideoFind = this.getById(id);
        if (Objects.isNull(yogaVideoFind)) {
            throw new BizException("Data not found");
        }

        ResYogaVideoDetailVO yogaVideoDetailVO = new ResYogaVideoDetailVO();
        BeanUtils.copyProperties(yogaVideoFind, yogaVideoDetailVO);
        yogaVideoDetailVO.setRelationPoseVideo(getPoseVideoInfo(yogaVideoFind, yogaVideoDetailVO));
        yogaVideoDetailVO.setTypeArr(MyStringUtil.getSplitWithComa(yogaVideoFind.getType()));
        yogaVideoDetailVO.setFocusArr(MyStringUtil.getSplitWithComa(yogaVideoFind.getFocus()));
        yogaVideoDetailVO.setSpecialLimitArr(MyStringUtil.getSplitWithComa(yogaVideoFind.getSpecialLimit()));

        // 查询VideoConnection
        LambdaQueryWrapper<ResYogaVideoConnection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResYogaVideoConnection::getResYogaVideoId, id);
        List<ResYogaVideoConnection> connectionList = resYogaVideoConnectionService.list(queryWrapper);

        Set<Integer> yogaVideoNextSet = Sets.newHashSet();
        Set<Integer> transitionSet = Sets.newHashSet();
        for (ResYogaVideoConnection videoConnection : connectionList) {
            yogaVideoNextSet.add(videoConnection.getResYogaVideoNextId());
            transitionSet.add(videoConnection.getResTransitionId());
        }

        List<ResYogaVideoNextDetailVO> nextList = new ArrayList<>();
        // 不为空才参与查询
        if (!yogaVideoNextSet.isEmpty()) {
            // 查询next yogaVideo
            LambdaQueryWrapper<ResYogaVideo> queryYogaVideoWrapper = new LambdaQueryWrapper<>();
            queryYogaVideoWrapper.in(ResYogaVideo::getId, yogaVideoNextSet);
            Map<Integer, ResYogaVideo> yogaVideoMap = this.list(queryYogaVideoWrapper).stream().collect(Collectors.toMap(ResYogaVideo::getId, v -> v));

            // 查询next transition
            LambdaQueryWrapper<ResTransition> queryTransitionWrapper = new LambdaQueryWrapper<>();
            queryTransitionWrapper.in(ResTransition::getId, transitionSet);
            Map<Integer, ResTransition> transitionMap = resTransitionService.list(queryTransitionWrapper).stream().collect(Collectors.toMap(ResTransition::getId, v -> v));

            for (ResYogaVideoConnection videoConnection : connectionList) {
                ResYogaVideoNextDetailVO nextDetailVO = new ResYogaVideoNextDetailVO();
                ResYogaVideo resYogaVideo = yogaVideoMap.get(videoConnection.getResYogaVideoNextId());
                ResTransition resTransition = transitionMap.get(videoConnection.getResTransitionId());

                if (resYogaVideo != null) {
                    ResYogaVideoNextYogaVideoDetailVO yogaVideo = new ResYogaVideoNextYogaVideoDetailVO();
                    BeanUtils.copyProperties(resYogaVideo, yogaVideo);
                    nextDetailVO.setYogaVideo(yogaVideo);
                }

                if (resTransition != null) {
                    ResYogaVideoNextTransitionDetailVO transition = new ResYogaVideoNextTransitionDetailVO();
                    BeanUtils.copyProperties(resTransition, transition);
                    nextDetailVO.setTransition(transition);
                }
                nextList.add(nextDetailVO);
            }
        }
        yogaVideoDetailVO.setNextList(nextList);

        List<List<ResYogaVideoCooldownAddVO>> coolDownList = new ArrayList<>();
        boolean needSearchCondition = Arrays.asList(yogaVideoDetailVO.getTypeArr()).contains("End");
        if (needSearchCondition) {
            // 查询YogaVideoCooldown
            LambdaQueryWrapper<ResYogaVideoCooldown> cooldownQueryWrapper = new LambdaQueryWrapper<>();
            cooldownQueryWrapper.eq(ResYogaVideoCooldown::getResYogaVideoLinkId, id);
            cooldownQueryWrapper.orderByAsc(ResYogaVideoCooldown::getId);
            List<ResYogaVideoCooldown> videoCooldownList = resYogaVideoCooldownService.list(cooldownQueryWrapper);
            if (!videoCooldownList.isEmpty()) {
                Set<Integer> yogaVideoSet = videoCooldownList.stream().map(ResYogaVideoCooldown::getResYogaVideoId).collect(Collectors.toSet());

                LambdaQueryWrapper<ResYogaVideo> cooldownVideoQueryWrapper = new LambdaQueryWrapper<>();
                cooldownVideoQueryWrapper.in(ResYogaVideo::getId, yogaVideoSet);
                Map<Integer, ResYogaVideo> cooldownYogaVideoMap = this.list(cooldownVideoQueryWrapper).stream().collect(Collectors.toMap(ResYogaVideo::getId, v -> v));

                Map<Integer, List<ResYogaVideoCooldown>> yogaVideoCooldownGroupBy = videoCooldownList.stream().collect(Collectors.groupingBy(ResYogaVideoCooldown::getGroupId, LinkedHashMap::new, Collectors.toList()));

                for (Map.Entry<Integer, List<ResYogaVideoCooldown>> entry : yogaVideoCooldownGroupBy.entrySet()) {
                    List<ResYogaVideoCooldownAddVO> yogaVideoCooldownAddVOList = new ArrayList<>();
                    for (ResYogaVideoCooldown videoCooldown : entry.getValue()) {
                        ResYogaVideoCooldownAddVO yogaVideoCooldownAddVO = new ResYogaVideoCooldownAddVO();
                        yogaVideoCooldownAddVO.setId(videoCooldown.getResYogaVideoId());
                        ResYogaVideo yogaVideo = cooldownYogaVideoMap.get(videoCooldown.getResYogaVideoId());
                        if (yogaVideo != null) {
                            yogaVideoCooldownAddVO.setName(yogaVideo.getName());
                            yogaVideoCooldownAddVO.setStatus(yogaVideo.getStatus());
                        }
                        yogaVideoCooldownAddVOList.add(yogaVideoCooldownAddVO);
                    }
                    coolDownList.add(yogaVideoCooldownAddVOList);
                }
            }

        }
        yogaVideoDetailVO.setCoolDownList(coolDownList);

        fillI18nConfigInfoForDetail(yogaVideoDetailVO);
        return yogaVideoDetailVO;
    }

    private void fillI18nConfigInfoForDetail(ResYogaVideoDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Set<Integer> updateEnableByIds(List<Integer> idList) {
        Map<Integer, ResYogaVideo> yogaVideoMap = this.list(new LambdaQueryWrapper<ResYogaVideo>().in(ResYogaVideo::getId, idList)).stream().collect(Collectors.toMap(ResYogaVideo::getId, o -> o));

        List<Integer> ids = idList.stream().filter(id -> {
            ResYogaVideo yogaVideo = yogaVideoMap.get(id);
            // 数据存在，数据状态不是启用的，可以启用
            return Objects.nonNull(yogaVideo) && !Objects.equals(yogaVideo.getStatus(), GlobalConstant.STATUS_ENABLE);
        }).collect(Collectors.toList());

        Set<Integer> failedIdSet = new LinkedHashSet<>();
        if (ids.isEmpty()) {
            return failedIdSet;
        }

        Set<Integer> transitionIdSet = new HashSet<>();
        // 查询connection
        Map<Integer, List<ResYogaVideoConnection>> connectionGroup = resYogaVideoConnectionService.list(new LambdaQueryWrapper<ResYogaVideoConnection>().in(ResYogaVideoConnection::getResYogaVideoId, ids)).stream().peek(o -> transitionIdSet.add(o.getResTransitionId())).collect(Collectors.groupingBy(ResYogaVideoConnection::getResYogaVideoId));

        // 查询cooldown
        Map<Integer, List<ResYogaVideoCooldown>> cooldownGroup = resYogaVideoCooldownService.list(new LambdaQueryWrapper<ResYogaVideoCooldown>().in(ResYogaVideoCooldown::getResYogaVideoLinkId, ids)).stream().collect(Collectors.groupingBy(ResYogaVideoCooldown::getResYogaVideoLinkId));

        Map<Integer, ResTransition> transitionMap;
        if (!transitionIdSet.isEmpty()) {
            transitionMap = resTransitionService.list(new LambdaQueryWrapper<ResTransition>().in(ResTransition::getId, transitionIdSet)).stream().collect(Collectors.toMap(ResTransition::getId, o -> o));
        } else {
            transitionMap = new HashMap<>();
        }

        Set<Integer> updateIdSet = new HashSet<>();
        for (Integer id : ids) {
            ResYogaVideo yogaVideo = yogaVideoMap.get(id);
            // corpse 特殊处理，corpse不需要校验
            if (Objects.equals(yogaVideo.getName(), CORPSE)) {
                updateIdSet.add(id);
                continue;
            }
            List<ResYogaVideoConnection> connectionList = connectionGroup.get(id);
            if (connectionList == null || connectionList.isEmpty()) {
                failedIdSet.add(id);
                continue;
            }
            if (yogaVideo.getType().contains("End")) {
                List<ResYogaVideoCooldown> cooldownList = cooldownGroup.get(id);
                if (cooldownList == null || cooldownList.isEmpty()) {
                    failedIdSet.add(id);
                    continue;
                }
            }
            boolean hasTransition = connectionList.stream().anyMatch(o -> {
                ResTransition resTransition = transitionMap.get(o.getResTransitionId());
                return resTransition == null || !Objects.equals(resTransition.getStatus(), GlobalConstant.STATUS_ENABLE);
            });
            if (hasTransition) {
                failedIdSet.add(id);
                continue;
            }

            updateIdSet.add(id);
        }

        // 启用
        if (!updateIdSet.isEmpty()) {
            LambdaUpdateWrapper<ResYogaVideo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ResYogaVideo::getStatus, GlobalConstant.STATUS_ENABLE);
            wrapper.in(ResYogaVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
            wrapper.in(ResYogaVideo::getId, updateIdSet);
            this.update(new ResYogaVideo(), wrapper);
        }

        return failedIdSet;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        // 禁用可以禁用的
        if (!idList.isEmpty()) {
            LambdaUpdateWrapper<ResYogaVideo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ResYogaVideo::getStatus, GlobalConstant.STATUS_DISABLE);
            wrapper.eq(ResYogaVideo::getStatus, GlobalConstant.STATUS_ENABLE);
            wrapper.in(ResYogaVideo::getId, idList);
            this.update(new ResYogaVideo(), wrapper);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            LambdaUpdateWrapper<ResYogaVideo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ResYogaVideo::getDelFlag, GlobalConstant.YES);
            wrapper.eq(ResYogaVideo::getStatus, GlobalConstant.STATUS_DRAFT);
            wrapper.eq(ResYogaVideo::getId, id);
            boolean flag = this.update(new ResYogaVideo(), wrapper);
            // 删除YogaVideo关系
            if (flag) {
                this.deleteYogaVideoRelation(id);
            }
        }

    }

    @Override
    public List<ResYogaVideo> listAllByIds(Collection<Integer> idList) {
        AtomicReference<List<ResYogaVideo>> result = new AtomicReference<>(CollUtil.newArrayList());
        Optional.ofNullable(idList).filter(CollUtil::isNotEmpty).ifPresent(ids -> {
            result.set(getBaseMapper().selectAllByIds(StrUtil.join(",", ids)));
        });
        return result.get();
    }

    @Override
    public List<ResYogaVideoDownloadVO> downloadList() {
        List<ResYogaVideo> videoList = list();
        if (CollUtil.isEmpty(videoList)) {
            return Collections.emptyList();
        }
        List<ResYogaVideoDownloadVO> downloadList = new ArrayList<>();
        for (ResYogaVideo resYogaVideo : videoList) {
            ResYogaVideoDownloadVO downloadVO = new ResYogaVideoDownloadVO();
            BeanUtils.copyProperties(resYogaVideo, downloadVO);
            downloadList.add(downloadVO);
        }
        return downloadList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importScriptByExcel(InputStream inputStream) {
        List<ResYogaVideoImportScriptReq> videoImportList = CollUtil.newArrayList();
        EasyExcel.read(inputStream, ResYogaVideoImportScriptReq.class, new AnalysisEventListener<ResYogaVideoImportScriptReq>() {
            @Override
            public void invoke(ResYogaVideoImportScriptReq row, AnalysisContext analysisContext) {
                videoImportList.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        List<String> failMessage = new ArrayList<>();
        if (CollUtil.isEmpty(videoImportList)) {
            return failMessage;
        }
        for (ResYogaVideoImportScriptReq videoReq : videoImportList) {
            Integer videoReqId = videoReq.getId();
            Set<ConstraintViolation<ResYogaVideoImportScriptReq>> violationSet = validator.validate(videoReq, Group.class);
            if (CollUtil.isNotEmpty(violationSet)) {
                violationSet.stream().findFirst().ifPresent(violation -> failMessage.add(String.format("%s:%s", videoReqId, violation.getMessage())));
            }
        }
        if(CollUtil.isNotEmpty(failMessage)){
            return failMessage;
        }
        List<ResYogaVideo> videoList = new ArrayList<>(videoImportList.size());
        for (ResYogaVideoImportScriptReq importScriptReq : videoImportList) {
            ResYogaVideo resYogaVideo = new ResYogaVideo();
            BeanUtils.copyProperties(importScriptReq, resYogaVideo);
            videoList.add(resYogaVideo);
        }
        updateBatchById(videoList);
        return failMessage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importByExcel(InputStream excelInputStream) {
        log.info("yoga video importByExcel Start-----------------");
        // 1、使用easyExcel,转换excel数据为ResYogaVideoImportReq对象
        List<ResYogaVideoImportReq> resYogaVideoImportReqReadList = CollUtil.newArrayList();
        EasyExcel.read(excelInputStream, ResYogaVideoImportReq.class, new AnalysisEventListener<ResYogaVideoImportReq>() {
            @Override
            public void invoke(ResYogaVideoImportReq row, AnalysisContext analysisContext) {
                resYogaVideoImportReqReadList.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        // 2、过滤不符合输入规范的数据
        List<String> failMessage = CollUtil.newArrayList();
        List<ResYogaVideoImportReq> resYogaVideoImportReqs = filterDirtyData(resYogaVideoImportReqReadList, failMessage);

        if (CollUtil.isNotEmpty(resYogaVideoImportReqs)) {
            Map<String, List<ResYogaVideoImportReq>> yogaVideoGroupBy = resYogaVideoImportReqs.stream().peek(o -> {

            }).collect(Collectors.groupingBy(ResYogaVideoImportReq::getName, LinkedHashMap::new, Collectors.toList()));

            List<ResYogaVideo> yogaVideoList = list();
            yogaVideoList = null == yogaVideoList ? new ArrayList<>() : yogaVideoList;
            Map<String, ResYogaVideo> resYogaVideoMap = yogaVideoList.stream().collect(Collectors.toMap(ResYogaVideo::getName, Function.identity(), (existing, replacement) -> replacement));

            for (Map.Entry<String, List<ResYogaVideoImportReq>> entry : yogaVideoGroupBy.entrySet()) {
                List<ResYogaVideoImportReq> yogaVideoImportList = entry.getValue();
                ResYogaVideoImportReq yogaVideoImportFirst = yogaVideoImportList.get(GlobalConstant.ZERO);
                String name = yogaVideoImportFirst.getName();
                ResYogaVideo video = resYogaVideoMap.get(name);
                if (null != video) {
                    continue;
                }
                ResYogaVideo resYogaVideo = new ResYogaVideo();
                BeanUtils.copyProperties(yogaVideoImportFirst, resYogaVideo);
                resYogaVideo.setEventName(resYogaVideo.getName());
                resYogaVideo.setStatus(GlobalConstant.STATUS_DRAFT);
                String poseTime = yogaVideoImportFirst.getPoseTime();
                if (StrUtil.isBlank(resYogaVideo.getType())) {
                    resYogaVideo.setType("Main");
                } else {
                    String[] typeArr = MyStringUtil.getSplitWithComa(resYogaVideo.getType());
                    String[] newTypeArr = new String[typeArr.length];
                    for (int i = 0; i < typeArr.length; i++) {
                        newTypeArr[i] = typeArr[i].trim();
                    }
                    resYogaVideo.setType(MyStringUtil.getJoinWithComma(newTypeArr) + ",Main");
                }
                if (StrUtil.isNotBlank(poseTime)) {
                    resYogaVideo.setPoseTime(Integer.parseInt(poseTime.replaceAll("s", "")) * 1000);
                }

                this.save(resYogaVideo);
                resYogaVideoMap.put(resYogaVideo.getName(), resYogaVideo);
            }

            List<ResYogaVideoConnection> allConnectionList = resYogaVideoConnectionService.list();
            Map<String, List<ResYogaVideoConnection>> connectionMap = allConnectionList.stream().collect(Collectors.groupingBy(item -> getConnectionKey(item.getResYogaVideoId(), item.getResYogaVideoNextId())));

            for (Map.Entry<String, List<ResYogaVideoImportReq>> entry : yogaVideoGroupBy.entrySet()) {
                List<ResYogaVideoImportReq> yogaVideoImportList = entry.getValue();
                //保存transition
                for (ResYogaVideoImportReq connection : yogaVideoImportList) {
                    String name = connection.getName();
                    String nextPoseName = connection.getNextPoseName();
                    // 为空不验证，不保存，表示只保存video，不保存Transition
                    if (StrUtil.isBlank(nextPoseName)) {
                        continue;
                    }
                    ResYogaVideo resYogaVideo = resYogaVideoMap.get(name);
                    ResYogaVideo nextResYogaVideo = resYogaVideoMap.get(nextPoseName);
                    String errName = name + "-- next pose -> " + nextPoseName;
                    if (Objects.isNull(nextResYogaVideo)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. Next Pose not found");
                        continue;
                    }

                    String connectionKey = getConnectionKey(resYogaVideo.getId(), nextResYogaVideo.getId());
                    //已存在关系就不保存关系
                    if (CollectionUtil.isNotEmpty(connectionMap.get(connectionKey))) {
                        continue;
                    }

                    if (Objects.equals(name, nextPoseName)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. Next Pose Cannot be the same as Main Pose Name");
                        continue;
                    }

                    String flowFrontVideoUrl = connection.getFlowFrontVideoUrl();
                    if (StrUtil.isBlank(flowFrontVideoUrl)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. flow_main cannot be empty");
                        continue;
                    }

                    Integer flowFrontVideoDuration = connection.getFlowFrontVideoDuration();
                    if (Objects.isNull(flowFrontVideoDuration)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. flow_main_duration cannot be empty");
                        continue;
                    }

                    String flowSideVideoUrl = connection.getFlowSideVideoUrl();
                    if (StrUtil.isBlank(flowSideVideoUrl)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. flow_assist cannot be empty");
                        continue;
                    }
                    Integer flowSideVideoDuration = connection.getFlowSideVideoDuration();
                    if (Objects.isNull(flowSideVideoDuration)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. flow_assist_duration cannot be empty");
                        continue;
                    }
                    String flowGuidanceAudioUrl = connection.getFlowGuidanceAudioUrl();
                    if (StrUtil.isBlank(flowGuidanceAudioUrl)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. flow_guidance cannot be empty");
                        continue;
                    }
                    Integer flowGuidanceAudioDuration = connection.getFlowGuidanceAudioDuration();
                    if (Objects.isNull(flowGuidanceAudioDuration)) {
                        failMessage.add(errName + ": Video save success,Transition and Next Pose save failed. flow_guidance_duration cannot be empty");
                        continue;
                    }

                    // 多个相同的nextPose，只保存一个
                    String transitionName = name + "-" + nextPoseName;

                    // 特殊处理 CORPSE 不能有next和cooldown
                    if (Objects.equals(name, CORPSE)) {
                        continue;
                    }

                    ResTransition resTransition = new ResTransition();
                    resTransition.setFrontVideoUrl(flowFrontVideoUrl);
                    resTransition.setFrontVideoDuration(flowFrontVideoDuration);
                    resTransition.setSideVideoUrl(flowSideVideoUrl);
                    resTransition.setSideVideoDuration(flowSideVideoDuration);
                    resTransition.setGuidanceAudioUrl(flowGuidanceAudioUrl);
                    resTransition.setGuidanceAudioDuration(flowGuidanceAudioDuration);
                    resTransition.setStatus(GlobalConstant.STATUS_DRAFT);
                    resTransition.setImageUrl("");
                    resTransition.setName(transitionName);
                    resTransitionService.save(resTransition);

                    ResYogaVideoConnection resYogaVideoConnection = new ResYogaVideoConnection();
                    resYogaVideoConnection.setResTransitionId(resTransition.getId());
                    resYogaVideoConnection.setResYogaVideoId(resYogaVideo.getId());
                    resYogaVideoConnection.setResYogaVideoNextId(nextResYogaVideo.getId());
                    resYogaVideoConnectionService.save(resYogaVideoConnection);
                    String resYogaVideoConnectionKey = getConnectionKey(resYogaVideoConnection.getResYogaVideoId(), resYogaVideoConnection.getResYogaVideoNextId());
                    connectionMap.put(resYogaVideoConnectionKey, Collections.singletonList(resYogaVideoConnection));
                }
            }
        }

        log.info("yoga video importByExcel Finish-----------------");
        return failMessage;
    }


    private String getConnectionKey(Integer videoId, Integer nextVideoId) {
        return videoId + "-" + nextVideoId;
    }

    /**
     * 过滤不符合业务规则的数据
     *
     * @param dataList    dataList
     * @param failMessage failMessage
     * @return list
     */
    private List<ResYogaVideoImportReq> filterDirtyData(List<ResYogaVideoImportReq> dataList, List<String> failMessage) {

        Set<String> i18nConfigNameSet = dataList.stream().map(ResYogaVideoImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
        Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));

        List<ResYogaVideoImportReq> meetsCondiData = CollUtil.newArrayList();
        Optional.ofNullable(dataList).filter(CollUtil::isNotEmpty).ifPresent(data -> {
            data.stream().forEach(req -> {
                try {

                    if (!i18nConfigNameIdMap.containsKey(req.getCoreVoiceConfigI18nName())) {
                        failMessage.add(req.getName() + ": English Voice Name Not Found in TTS config");
                        return;
                    } else {
                        req.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(req.getCoreVoiceConfigI18nName()));
                    }

                    Optional.ofNullable(validator.validate(req, Group.class)).ifPresent(result -> {
                        Optional<ConstraintViolation<ResYogaVideoImportReq>> firstError = result.stream().findFirst();
                        if (firstError.isPresent()) {
                            //校验失败，只记录第一条失败原因
                            failMessage.add(req.getName() + ":" + firstError.get().getMessage());
                        } else {
                            meetsCondiData.add(req);
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    failMessage.add(req.getName() + ":" + e.getMessage());
                }
            });
        });
        return meetsCondiData;
    }

    /**
     * 正侧切片任务状态返回
     */
    private void injectionTaskStatus(List<ResYogaVideoPageVO> videoList) {
        if (CollUtil.isEmpty(videoList)) {
            return;
        }

        Set<Integer> videoIds = videoList.stream().map(ResYogaVideoPageVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(RES_YOGA_VIDEO_SIDE.getTableName(), RES_YOGA_VIDEO_SIDE.getEntityFieldName(), videoIds);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(RES_YOGA_VIDEO_FRONT.getTableName(), RES_YOGA_VIDEO_FRONT.getEntityFieldName(), videoIds);
        if (CollUtil.isEmpty(sideStatusList) && CollUtil.isEmpty(frontStatusList)) {
            return;
        }
        sideStatusList = null == sideStatusList ? new ArrayList<>() : sideStatusList;
        frontStatusList = null == frontStatusList ? new ArrayList<>() : frontStatusList;
        Map<Integer, TaskResourceSectionStatusEnums> sideIdStatusMap = sideStatusList.stream().collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));
        Map<Integer, TaskResourceSectionStatusEnums> frontIdStatusMap = frontStatusList.stream().collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));
        videoList.forEach(video -> {
            Optional.ofNullable(sideIdStatusMap.get(video.getId())).ifPresent(video::setSideTaskStatus);
            Optional.ofNullable(frontIdStatusMap.get(video.getId())).ifPresent(video::setFrontTaskStatus);
        });
    }

}
