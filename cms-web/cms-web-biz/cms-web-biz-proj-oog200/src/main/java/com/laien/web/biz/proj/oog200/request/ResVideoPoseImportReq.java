package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "ResVideoPoseImportReq", description = "ResVideoPoseImportReq")
@ExcelIgnoreUnannotated
public class ResVideoPoseImportReq {

    @ApiModelProperty(value = "动作名称")
    @ExcelProperty(value = "Main Pose Name", converter = StringStringTrimConverter.class)
    private String name;

    @ApiModelProperty(value = "目标")
    @ExcelProperty(value = "Target", converter = StringStringTrimConverter.class)
    private String target;

    @ApiModelProperty(value = "难度")
    @ExcelProperty(value = "Difficulty", converter = StringStringTrimConverter.class)
    private String difficulty;

    @ApiModelProperty(value = "是否可以作为开始动作")
    @ExcelProperty(value = "Start Pose", converter = StringStringTrimConverter.class)
    private String startPose;

    @ApiModelProperty(value = "是否可以作为结束动作")
    @ExcelProperty(value = "End Pose", converter = StringStringTrimConverter.class)
    private String endPose;

    @ApiModelProperty(value = "下一个动作的名称")
    @ExcelProperty(value = "Next Pose", converter = StringStringTrimConverter.class)
    private String nextPose;

    @ApiModelProperty(value = "动作时长")
    @ExcelProperty(value = "时长（修正）", converter = StringStringTrimConverter.class)
    private String poseTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public String getStartPose() {
        return startPose;
    }

    public void setStartPose(String startPose) {
        this.startPose = startPose;
    }

    public String getEndPose() {
        return endPose;
    }

    public void setEndPose(String endPose) {
        this.endPose = endPose;
    }

    public String getNextPose() {
        return nextPose;
    }

    public void setNextPose(String nextPose) {
        this.nextPose = nextPose;
    }

    public String getPoseTime() {
        return poseTime;
    }

    public void setPoseTime(String poseTime) {
        this.poseTime = poseTime;
    }
}
