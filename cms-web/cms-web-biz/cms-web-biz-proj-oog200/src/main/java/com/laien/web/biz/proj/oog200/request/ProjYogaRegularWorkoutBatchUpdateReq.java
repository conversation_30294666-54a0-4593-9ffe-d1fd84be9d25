package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
@ApiModel(value = "YogaRegularWorkout 批量修改", description = "YogaRegularWorkout 批量修改")
public class ProjYogaRegularWorkoutBatchUpdateReq {

    @ApiModelProperty(value = "数据id")
    private Set<Integer> regularWorkoutIds;

    @JsonIgnore
    private Integer projId;

}
