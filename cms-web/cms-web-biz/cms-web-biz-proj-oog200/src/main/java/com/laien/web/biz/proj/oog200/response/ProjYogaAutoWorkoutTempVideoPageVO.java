package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
@ApiModel(value = "ProjYogaAutoWorkoutTempVideoPageVO", description = "ProjYogaAutoWorkoutTempVideoPageVO")
public class ProjYogaAutoWorkoutTempVideoPageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "动作类型 数组 Start、Main、End、CoolDown")
    private String type;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "动作时长 单位毫秒")
    private Integer poseTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;
}
