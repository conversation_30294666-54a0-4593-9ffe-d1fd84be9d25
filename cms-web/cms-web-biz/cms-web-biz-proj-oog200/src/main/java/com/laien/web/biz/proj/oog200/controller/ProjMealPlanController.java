package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.oog200.entity.ProjMealPlan;
import com.laien.web.biz.proj.oog200.request.ProjMealPlanAddReq;
import com.laien.web.biz.proj.oog200.request.ProjMealPlanListReq;
import com.laien.web.biz.proj.oog200.request.ProjMealPlanUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjMealPlanDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjMealPlanListVO;
import com.laien.web.biz.proj.oog200.service.IProjMealPlanService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:13
 */
@Api(tags = "项目管理: mealPlan")
@RestController
@RequestMapping("/proj/mealPlan")
public class ProjMealPlanController extends ResponseController {

    @Resource
    private IProjMealPlanService mealPlanService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjMealPlanListVO>> page(ProjMealPlanListReq pageReq) {

        List<ProjMealPlanListVO> pageRes = mealPlanService.selectMealPlanList(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjMealPlanAddReq addReq) {

        mealPlanService.saveMealPlan(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjMealPlanUpdateReq updateReq) {

        mealPlanService.updateMealPlan(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjMealPlanDetailVO> detail(@PathVariable Integer id) {

        ProjMealPlanDetailVO detailVO = mealPlanService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        mealPlanService.sort(idListReq);
        return succ();
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        mealPlanService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        mealPlanService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        mealPlanService.deleteByIds(idList);
        return succ();
    }

}
