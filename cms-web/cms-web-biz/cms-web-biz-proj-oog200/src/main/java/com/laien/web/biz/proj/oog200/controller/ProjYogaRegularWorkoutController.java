package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkout;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaRegularWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * oog200 workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Api(tags = "项目管理:yogaRegularWorkout")
@RestController
@RequestMapping("/proj/yogaRegularWorkout")
public class ProjYogaRegularWorkoutController extends ResponseController {

    @Resource
    private IProjYogaRegularWorkoutService projYogaRegularWorkoutService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaRegularWorkoutPageVO>> page(ProjYogaRegularWorkoutPageReq pageReq) {
        PageRes<ProjYogaRegularWorkoutPageVO> pageRes = projYogaRegularWorkoutService.selectWorkoutPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaRegularWorkoutAddReq yogaRegularWorkoutAddReq) {
        projYogaRegularWorkoutService.saveWorkout(yogaRegularWorkoutAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaRegularWorkoutUpdateReq yogaRegularWorkoutUpdateReq) {
        projYogaRegularWorkoutService.updateWorkout(yogaRegularWorkoutUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "批量修改")
    @PostMapping(value = "/updateBatch")
    public ResponseResult<Void> batchUpdate(@RequestBody ProjYogaRegularWorkoutBatchUpdateReq batchUpdateReq) {

        projYogaRegularWorkoutService.batchUpdateWorkout(batchUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaRegularWorkoutDetailVO> detail(@PathVariable Integer id) {
        ProjYogaRegularWorkoutDetailVO detailVO = projYogaRegularWorkoutService.getWorkoutDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaRegularWorkoutService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaRegularWorkoutService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaRegularWorkoutService.deleteByIds(idList);
        return succ();
    }


    @ApiOperation(value = "计算时长")
    @PostMapping("/computeDuration")
    public ResponseResult<Integer> computeDuration(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        Integer duration = projYogaRegularWorkoutService.getComputeDuration(idList);
        return succ(duration);
    }

}
