package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjCollectionTeacherVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/28
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjCollectionTeacherVO", description="ProjCollectionTeacherVO")
public class ProjCollectionTeacherVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "avatarUrl")
    private String avatarUrl;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;
}
