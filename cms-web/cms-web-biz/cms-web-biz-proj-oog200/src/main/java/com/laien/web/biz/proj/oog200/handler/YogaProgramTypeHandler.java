package com.laien.web.biz.proj.oog200.handler;

import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/18 14:15
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class YogaProgramTypeHandler extends BaseTypeHandler<List<YogaProgramTypeEnum>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<YogaProgramTypeEnum> parameter, JdbcType jdbcType) throws SQLException {

        // 将枚举集合转换为逗号分隔的字符串
        String value = parameter.stream()
                .map(type -> type.getCode().toString())
                .collect(Collectors.joining(","));
        ps.setString(i, value);
    }

    @Override
    public List<YogaProgramTypeEnum> getNullableResult(ResultSet rs, String columnName) throws SQLException {

        String value = rs.getString(columnName);
        return convertStringToEnumList(value);
    }

    @Override
    public List<YogaProgramTypeEnum> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {

        String value = rs.getString(columnIndex);
        return convertStringToEnumList(value);
    }

    @Override
    public List<YogaProgramTypeEnum> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {

        String value = cs.getString(columnIndex);
        return convertStringToEnumList(value);
    }

    private List<YogaProgramTypeEnum> convertStringToEnumList(String value) {

        if (value == null || value.isEmpty()) {
            return null;
        }

        return Arrays.stream(value.split(","))
                .map(code -> YogaProgramTypeEnum.getByCode(Integer.valueOf(code)))
                .collect(Collectors.toList());
    }
}
