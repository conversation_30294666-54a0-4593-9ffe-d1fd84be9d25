package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video slice 增加
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video slice 增加", description = "video slice 增加")
public class ResVideoSliceAddReq {

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频类型")
    private String videoType;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "focus")
    private String[] focusArr;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "视频地址")
    private String video1Url;

    @ApiModelProperty(value = "视频时长")
    private Integer video1Duration;

    @ApiModelProperty(value = "机位2视频地址")
    private String video2Url;

    @ApiModelProperty(value = "机位2视频时长")
    private Integer video2Duration;

    @ApiModelProperty(value = "多语言列表")
    private List<ResVideoSliceI18nReq> i18nList;

    @ApiModelProperty(value = "状态")
    private Integer status;

}
