package com.laien.web.biz.proj.oog200.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * unit
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjUnit对象", description="unit")
public class ProjUnitVO {


    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "单位名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
