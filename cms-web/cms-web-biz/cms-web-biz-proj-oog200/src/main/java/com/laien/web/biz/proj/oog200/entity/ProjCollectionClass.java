package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * proj collection class
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjCollectionClass对象", description = "proj collection class")
public class ProjCollectionClass extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImg;

    @ApiModelProperty(value = "详情图")
    private String detailImg;

    @ApiModelProperty(value = "描述")
    @TranslateField
    private String description;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "教练id")
    private Integer teacherId;

    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other")
    private String yogaType;

    @ApiModelProperty(value = "类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog")
    private YogaAutoWorkoutTemplateEnum type;

    @ApiModelProperty(value = "goal类型,多个用逗号分隔，取值 0:Learn Yoga Basics,1:Weight Loss,2:Improve Flexibility,3:Mindfulness")
    private String goal;
}
