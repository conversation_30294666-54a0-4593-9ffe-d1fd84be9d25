package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTask;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTemplate;
import com.laien.common.oog200.enums.ProjYogaAutoWorkoutTaskStatusEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaAutoWorkoutTemplateMapper;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempWorkoutPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTemplateDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTemplatePageVO;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * yoga auto workout生成模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class ProjYogaAutoWorkoutTemplateServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutTemplateMapper, ProjYogaAutoWorkoutTemplate> implements IProjYogaAutoWorkoutTemplateService {

    @Resource
    private IProjYogaAutoWorkoutTaskService projYogaAutoWorkoutTaskService;

    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;

    @Resource
    private IProjWallPilatesAutoWorkoutService projWallPilatesAutoWorkoutService;

    @Resource
    private IProjChairYogaAutoWorkoutService projChairYogaAutoWorkoutService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ProjYogaAutoWorkoutTemplateAddReq request, Integer projId) {
        //名称去除前后空格
        request.setName(request.getName().trim());
        //检查名称是否重复
        duplicateDetectionByName(request.getName(), null, projId);
        //校验minTime,maxTime
        minMaxTimeCheck(request.getMinTime(), request.getMaxTime());
        //存储数据
        ProjYogaAutoWorkoutTemplate projYogaAutoWorkoutTemplate = new ProjYogaAutoWorkoutTemplate();
        BeanUtils.copyProperties(request, projYogaAutoWorkoutTemplate);
        projYogaAutoWorkoutTemplate.setLanguage(StrUtil.join(",", request.getLanguageArr()));
        projYogaAutoWorkoutTemplate.setStatus(GlobalConstant.STATUS_DRAFT);
        projYogaAutoWorkoutTemplate.setProjId(projId);
        save(projYogaAutoWorkoutTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjYogaAutoWorkoutTemplateUpdateReq request, Integer projId) {
        //名称去除前后空格
        request.setName(request.getName().trim());
        //检查名称是否重复
        duplicateDetectionByName(request.getName(), request.getId(), projId);
        //校验minTime,maxTime
        minMaxTimeCheck(request.getMinTime(), request.getMaxTime());
        //存储数据
        ProjYogaAutoWorkoutTemplate projYogaAutoWorkoutTemplate = new ProjYogaAutoWorkoutTemplate();
        BeanUtils.copyProperties(request, projYogaAutoWorkoutTemplate);
        projYogaAutoWorkoutTemplate.setLanguage(StrUtil.join(",", request.getLanguageArr()));
        LambdaUpdateWrapper<ProjYogaAutoWorkoutTemplate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaAutoWorkoutTemplate::getId, projYogaAutoWorkoutTemplate.getId());
        //只有草稿状态才能被修改，从启用变为停用后的模版，不可被修改
        updateWrapper.eq(ProjYogaAutoWorkoutTemplate::getStatus, GlobalConstant.STATUS_DRAFT);
        update(projYogaAutoWorkoutTemplate, updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(Collection<Integer> idList) {
        Optional.ofNullable(idList).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("ID list cannot be empty"));
        LambdaUpdateWrapper<ProjYogaAutoWorkoutTemplate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaAutoWorkoutTemplate::getDelFlag, GlobalConstant.YES);
        wrapper.in(ProjYogaAutoWorkoutTemplate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaAutoWorkoutTemplate::getId, idList);
        this.update(new ProjYogaAutoWorkoutTemplate(), wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnableByIds(Collection<Integer> idList) {
        Optional.ofNullable(idList).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("ID list cannot be empty"));
        LambdaUpdateWrapper<ProjYogaAutoWorkoutTemplate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaAutoWorkoutTemplate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaAutoWorkoutTemplate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaAutoWorkoutTemplate::getId, idList);
        this.update(new ProjYogaAutoWorkoutTemplate(), wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDisableByIds(Collection<Integer> idList) {
        Optional.ofNullable(idList).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("ID list cannot be empty"));
        LambdaUpdateWrapper<ProjYogaAutoWorkoutTemplate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaAutoWorkoutTemplate::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaAutoWorkoutTemplate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaAutoWorkoutTemplate::getId, idList);
        this.update(new ProjYogaAutoWorkoutTemplate(), wrapper);
    }

    @Override
    public PageRes<ProjYogaAutoWorkoutTemplatePageVO> page(ProjYogaAutoWorkoutTemplatePageReq pageReq, Integer projId) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAutoWorkoutTemplate::getProjId, projId);
        queryWrapper.like(StrUtil.isNotBlank(pageReq.getTemplateName()), ProjYogaAutoWorkoutTemplate::getName, pageReq.getTemplateName());
        queryWrapper.eq(pageReq.getStatus() != null, ProjYogaAutoWorkoutTemplate::getStatus, pageReq.getStatus());
        queryWrapper.eq(!StringUtils.isEmpty(pageReq.getType()), ProjYogaAutoWorkoutTemplate::getType, pageReq.getType());
        queryWrapper.orderByDesc(ProjYogaAutoWorkoutTemplate::getCreateTime);
        Page<ProjYogaAutoWorkoutTemplate> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        page(page, queryWrapper);
        List<ProjYogaAutoWorkoutTemplatePageVO> resultRecords = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Integer> templateIds = page.getRecords().stream().map(ProjYogaAutoWorkoutTemplate::getId).collect(Collectors.toList());
            //查询最后任务的执行状态
            Map<Integer, ProjYogaAutoWorkoutTask> tempLastTaskStatus = projYogaAutoWorkoutTaskService.listLastTaskStatus(templateIds);
            //查询生成的workout数量
            Map<Integer, Integer> tempWorkoutNum = projYogaAutoWorkoutService.listWorkoutNum(templateIds);
            Map<Integer, Integer> wallPilatesAutoWorkoutNum = new HashMap<>();
            List<CountBO> countList = projWallPilatesAutoWorkoutService.findCountList(templateIds);
            if(CollUtil.isNotEmpty(countList)) {
                countList.forEach(item -> {
                    wallPilatesAutoWorkoutNum.put(item.getId(), item.getCount());
                });
            }
            Map<Integer, Integer> chairYogaAutoWorkoutNum = getTemplateGenerateNum4ChairYoga(templateIds);
            for (ProjYogaAutoWorkoutTemplate record : page.getRecords()) {
                ProjYogaAutoWorkoutTemplatePageVO projYogaAutoWorkoutTemplatePageVO = new ProjYogaAutoWorkoutTemplatePageVO();
                BeanUtils.copyProperties(record, projYogaAutoWorkoutTemplatePageVO);
                projYogaAutoWorkoutTemplatePageVO.setTaskStatus(Optional.ofNullable(tempLastTaskStatus.get(record.getId())).map(t -> t.getStatus()).orElse(ProjYogaAutoWorkoutTaskStatusEnum.SUCCESS).getCode());
                projYogaAutoWorkoutTemplatePageVO.setFailReason(Optional.ofNullable(tempLastTaskStatus.get(record.getId())).map(t -> t.getFailReason()).orElse(null));
                if(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getName().equals(record.getType())){
                    projYogaAutoWorkoutTemplatePageVO.setWorkoutNum(Optional.ofNullable(tempWorkoutNum.get(record.getId())).orElse(0));
                } else if(YogaAutoWorkoutTemplateEnum.WALL_PILATES.getName().equals(record.getType())){
                    projYogaAutoWorkoutTemplatePageVO.setWorkoutNum(Optional.ofNullable(wallPilatesAutoWorkoutNum.get(record.getId())).orElse(0));
                } else if (YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getName().equals(record.getType())) {
                    projYogaAutoWorkoutTemplatePageVO.setWorkoutNum(Optional.ofNullable(chairYogaAutoWorkoutNum.get(record.getId())).orElse(0));
                }
                resultRecords.add(projYogaAutoWorkoutTemplatePageVO);
            }
        }
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), resultRecords);
    }

    private Map<Integer, Integer> getTemplateGenerateNum4ChairYoga(Collection<Integer> templateIds) {

        List<CountBO> workoutCountList = projChairYogaAutoWorkoutService.listWorkoutCountByTemplate(templateIds);
        if (CollectionUtils.isEmpty(workoutCountList)) {
            return Collections.emptyMap();
        }
        return workoutCountList.stream().collect(Collectors.toMap(CountBO::getId, CountBO::getCount));
    }

    @Override
    public PageRes<ProjYogaAutoWorkoutTempWorkoutPageVO> pageWorkout(ProjYogaAutoWorkoutTempWorkoutPageReq pageReq, Integer templateId) {
        return projYogaAutoWorkoutService.page(pageReq, templateId);
    }

    @Override
    public PageRes<ProjYogaAutoWorkoutTempVideoPageVO> pageVideo(PageReq pageReq, Integer workoutId) {
        return projYogaAutoWorkoutService.pageVideo(pageReq, workoutId);
    }

    @Override
    public ProjYogaAutoWorkoutTemplateDetailVO detail(Integer id) {
        return Optional.ofNullable(getById(id)).map(temp -> {
            ProjYogaAutoWorkoutTemplateDetailVO result = new ProjYogaAutoWorkoutTemplateDetailVO();
            BeanUtils.copyProperties(temp, result);
            result.setLanguageArr(StrUtil.split(temp.getLanguage(), ",", true, true));
            return result;
        }).orElse(null);
    }

    @Override
    public List<ProjYogaAutoWorkoutTemplate> find(Integer status) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(null != status, ProjYogaAutoWorkoutTemplate::getStatus, status);
        return baseMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generate(Integer templateId, ProjYogaAutoWorkoutTemplateGenerateReq projYogaAutoWorkoutTemplateGenerateReq) {
        //检查当前template是否有任务在运行
        if (projYogaAutoWorkoutTaskService.getRunningTaskByTemplate(templateId) != null) {
            throw new BizException("The current template already has a task running");
        }
        //创建生成任务并执行
        projYogaAutoWorkoutTaskService.add(templateId, projYogaAutoWorkoutTemplateGenerateReq);
    }


    /**
     * 校验最小和最大时间
     *
     * @param minTime
     * @param maxTime
     */
    private void minMaxTimeCheck(Integer minTime, Integer maxTime) {
        if (minTime > maxTime) {
            throw new BizException("The minTime cannot exceed maxTime");
        }
    }

    /**
     * 检测名称是否重复
     *
     * @param templateName
     * @param currentDataId
     */
    private void duplicateDetectionByName(String templateName, Integer currentDataId, Integer projId) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAutoWorkoutTemplate::getName, templateName);
        queryWrapper.eq(ProjYogaAutoWorkoutTemplate::getProjId, projId);
        queryWrapper.last("limit 1");
        Optional.ofNullable(getOne(queryWrapper)).ifPresent(template -> {
            if (currentDataId == null) {
                throw new BizException("Duplicate template name");
            }
            if (template.getId().intValue() != currentDataId) {
                throw new BizException("Duplicate template name");
            }
        });
    }
}
