package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * ResYogaVideoImportScriptReq
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="ResYogaVideoImportScriptReq对象", description="ResYogaVideoImportScriptReq")
public class ResYogaVideoImportScriptReq {

    @ApiModelProperty(value = "数据id")
    @NotNull(message = "id cannot be null")
    @ExcelProperty(value = "id")
    private Integer id;

    @NotEmpty(message = "name script cannot be empty", groups = Group1.class)
    @Length(message = "The name script cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "name script", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名字文本")
    private String nameScript;

    // @NotEmpty(message = "guidance script cannot be empty", groups = Group1.class)
    // @Length(message = "The guidance script cannot exceed 500 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "guidance script", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "guidance文本")
    private String guidanceScript;

}
