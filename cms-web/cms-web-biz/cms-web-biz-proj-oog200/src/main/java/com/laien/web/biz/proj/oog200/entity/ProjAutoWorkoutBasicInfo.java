package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog200.enums.AutoWorkoutBasicInfoPointEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_image
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_auto_workout_basic_info")
@ApiModel(value="ProjAutoWorkoutBasicInfo对象", description="proj_image")
public class ProjAutoWorkoutBasicInfo extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "图片用途，0:Upper Body,1:Abs & Core,2:Lower Body,3:Upper Body+Abs & Core,4:Abs & Core+Lower Body,5:Fullbody,6:Learn Yoga Basics,7:Mindfulness,8:Weight Loss,9:Improve Flexibility")
    private AutoWorkoutBasicInfoPointEnum point;

    @ApiModelProperty(value = "plan类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private YogaAutoWorkoutTemplateEnum planType;

    @ApiModelProperty(value = "difficulty类型，0: Newbie,1: Beginner,2: Intermediate,3: Advanced")
    private DifficultyEnum difficulty;

    @ApiModelProperty(value = "详情图（默认女）")
    private String detailImage;

    @ApiModelProperty(value = "封面图(male)")
    private String coverImageMale;

    @ApiModelProperty(value = "complete_image")
    private String completeImage;

    @ApiModelProperty(value = "封面图（默认和女）")
    private String coverImage;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer sorted;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
