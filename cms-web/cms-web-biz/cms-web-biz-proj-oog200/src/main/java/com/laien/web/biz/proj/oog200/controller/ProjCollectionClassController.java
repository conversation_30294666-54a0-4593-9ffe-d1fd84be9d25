package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjCollectionClass;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassAddReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassListReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionClassUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassListVO;
import com.laien.web.biz.proj.oog200.service.IProjCollectionClassService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj collection class 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Api(tags = "项目管理:Class Collection")
@RestController
@RequestMapping("/proj/collectionClass")
public class ProjCollectionClassController extends ResponseController {

    @Resource
    private IProjCollectionClassService projCollectionClassService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjCollectionClassListVO>> list(ProjCollectionClassListReq listReq) {
        List<ProjCollectionClassListVO> list = projCollectionClassService.selectCollectionClassList(listReq);
        return succ(list);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjCollectionClassAddReq collectionClassAddReq) {
        projCollectionClassService.saveCollectionClass(collectionClassAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjCollectionClassUpdateReq collectionClassUpdateReq) {
        projCollectionClassService.updateCollectionClass(collectionClassUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjCollectionClassDetailVO> detail(@PathVariable Integer id) {
        ProjCollectionClassDetailVO detailVO = projCollectionClassService.getCollectionClassDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCollectionClassService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCollectionClassService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCollectionClassService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCollectionClassService.saveCollectionClassSort(idList);
        return succ();
    }

}
