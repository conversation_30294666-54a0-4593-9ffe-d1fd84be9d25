package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutAudioI18n;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaAutoWorkoutAudioI18nMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaAutoWorkoutAudioI18nService;
import com.laien.web.frame.exception.BizException;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 生成的workout的audio json的url 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
@Service
public class ProjYogaAutoWorkoutAudioI18nServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutAudioI18nMapper, ProjYogaAutoWorkoutAudioI18n> implements IProjYogaAutoWorkoutAudioI18nService {
    @Override
    public void delete(Integer workoutId, YogaAutoWorkoutTemplateEnum workoutType) {
        if(null == workoutId){
            throw new BizException("ProjYogaAutoWorkoutAudioI18n delete workoutId can not be null");
        }

        if(null == workoutType){
            throw new BizException("ProjYogaAutoWorkoutAudioI18n delete workoutType can not be null");
        }
        LambdaUpdateWrapper<ProjYogaAutoWorkoutAudioI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjYogaAutoWorkoutAudioI18n::getWorkoutId, workoutId)
                .eq(ProjYogaAutoWorkoutAudioI18n::getWorkoutType, workoutType);
        baseMapper.delete(wrapper);
    }
}
