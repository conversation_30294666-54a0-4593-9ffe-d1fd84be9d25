package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.common.file.bo.AudioJsonBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/11/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class YogaAudio<PERSON>sonBO extends AudioJsonBO {

    private Integer soundId;

    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;

    public YogaAudioJsonBO() {

    }

    public YogaAudioJsonBO(String id, String url, String name, BigDecimal time, Integer soundId, Boolean needTranslation) {
        super(id, url, name, time);
        this.soundId = soundId;
        this.needTranslation = needTranslation;
    }
}
