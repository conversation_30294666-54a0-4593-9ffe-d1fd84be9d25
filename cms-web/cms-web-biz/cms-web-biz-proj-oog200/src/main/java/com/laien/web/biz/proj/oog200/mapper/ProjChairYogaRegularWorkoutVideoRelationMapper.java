package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/11/4 17:46
 */

public interface ProjChairYogaRegularWorkoutVideoRelationMapper extends BaseMapper<ProjChairYogaRegularWorkoutVideoRelation> {

    @Select(value = "select video.*,relation.video_duration as duration from proj_chair_yoga_regular_workout_video_relation relation\n" +
            "inner join proj_chair_yoga_video video on relation.proj_chair_yoga_video_id = video.id \n" +
            "WHERE relation.proj_chair_yoga_regular_workout_id = #{workoutId} and relation.del_flag = 0\n" +
            "order by relation.id ASC \n")
    List<ProjChairYogaVideoPageVO> listRelationAndVideo(@Param("workoutId") Integer workoutId);
}
