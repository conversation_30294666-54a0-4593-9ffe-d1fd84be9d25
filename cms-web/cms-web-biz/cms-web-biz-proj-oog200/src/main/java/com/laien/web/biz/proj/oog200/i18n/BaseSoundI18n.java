package com.laien.web.biz.proj.oog200.i18n;

import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.oog200.enums.GenderEnums;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/7/3
 */
@Data
@NoArgsConstructor
public class BaseSoundI18n implements AppAudioCoreI18nModel {

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private GenderEnums gender;

    private ProjSound projSound;

    private Boolean needTranslation;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @AppAudioTranslateField(resultFieldName = "result")
    private String nameScript;

    @ApiModelProperty(value = "result")
    private List<AudioTranslateResultModel> result;

    public BaseSoundI18n(ProjSound projSound) {

        this.projSound = projSound;
        this.gender = projSound.getGender();
        this.coreVoiceConfigI18nId = projSound.getCoreVoiceConfigI18nId();
        this.needTranslation = projSound.getNeedTranslation();
        this.name = projSound.getSoundName();
        this.nameScript = projSound.getSoundScript();
    }

}
