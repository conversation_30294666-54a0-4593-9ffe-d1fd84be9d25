package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.common.m3u8.seq.annotation.ResourceSection;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 过渡
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResTransition对象", description="过渡")
public class ResTransition extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "图片")
    private String imageUrl;

    @ResourceSection(
            tableName = "res_transition",
            m3u8UrlColumn = "video_url",
            m3u8Url2532Column = "video2532_url",
            m3u8Text2kColumn = "front_m3u8_text2k",
            m3u8Text1080pColumn = "front_m3u8_text1080p",
            m3u8Text720pColumn = "front_m3u8_text720p",
            m3u8Text480pColumn = "front_m3u8_text480p",
            m3u8Text360pColumn = "front_m3u8_text360p",
            m3u8Text2532Column = "front_m3u8_text2532",
            durationColum = "front_video_duration",
            dirKey = "project-yoga-workout-m3u8"
    )
    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ResourceSection(
            tableName = "res_transition",
            m3u8Text2kColumn = "side_m3u8_text2k",
            m3u8Text1080pColumn = "side_m3u8_text1080p",
            m3u8Text720pColumn = "side_m3u8_text720p",
            m3u8Text480pColumn = "side_m3u8_text480p",
            m3u8Text360pColumn = "side_m3u8_text360p",
            m3u8Text2532Column = "side_m3u8_text2532",
            durationColum = "side_video_duration",
            dirKey = "project-yoga-workout-m3u8"
    )
    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "批量导入的id")
    private Integer importId;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "视频地址(正机位 2532 m3u8)")
    private String video2532Url;

    @ApiModelProperty(value = "frontM3u8Text2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @ApiModelProperty(value = "frontM3u8Text1080p对应的m3u8内容")
    private String frontM3u8Text1080p;

    @ApiModelProperty(value = "frontM3u8Text720p对应的m3u8内容")
    private String frontM3u8Text720p;

    @ApiModelProperty(value = "frontM3u8Text480p对应的m3u8内容")
    private String frontM3u8Text480p;

    @ApiModelProperty(value = "frontM3u8Text360p对应的m3u8内容")
    private String frontM3u8Text360p;

    @ApiModelProperty(value = "sideM3u8Text2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @ApiModelProperty(value = "sideM3u8Text1080p对应的m3u8内容")
    private String sideM3u8Text1080p;

    @ApiModelProperty(value = "sideM3u8Text720p对应的m3u8内容")
    private String sideM3u8Text720p;

    @ApiModelProperty(value = "sideM3u8Text480p对应的m3u8内容")
    private String sideM3u8Text480p;

    @ApiModelProperty(value = "sideM3u8Text360p对应的m3u8内容")
    private String sideM3u8Text360p;

    @ApiModelProperty(value = "frontM3u8Text2532对应的m3u8内容")
    private String frontM3u8Text2532;

    @ApiModelProperty(value = "sideM3u8Text2532对应的m3u8内容")
    private String sideM3u8Text2532;

    @ApiModelProperty(value = "guidance文本")
//    @TranslateField(
//            type = TranslationTaskTypeEnums.SPEECH,
//            audioUrlFieldName = "guidanceAudioUrl",
//            durationFieldName = "guidanceAudioDuration")
    private String guidanceScript;

}
