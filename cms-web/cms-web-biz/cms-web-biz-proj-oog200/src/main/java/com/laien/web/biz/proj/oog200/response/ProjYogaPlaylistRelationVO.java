package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.MusicTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2025/2/20 11:20
 */
@Data
public class ProjYogaPlaylistRelationVO {

    @ApiModelProperty(value = "播放列表id")
    private Integer projYogaPlaylistId;

    @ApiModelProperty(value = "音乐id")
    private Integer projYogaMusicId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "music 名字")
    private String musicName;

    @ApiModelProperty(value = "music 类型")
    private MusicTypeEnum musicType;

    @ApiModelProperty(value = "music展示名称")
    private String displayName;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

}
