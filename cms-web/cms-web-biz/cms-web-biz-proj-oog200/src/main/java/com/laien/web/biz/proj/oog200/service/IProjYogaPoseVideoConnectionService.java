package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.dto.ProjYogaPoseVideoConnectionDTO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoConnection;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/8/1 17:35
 */
public interface IProjYogaPoseVideoConnectionService extends IService<ProjYogaPoseVideoConnection> {

    List<ProjYogaPoseVideoConnection> listByPoseVideoId(Integer poseVideoId);

    List<ProjYogaPoseVideoConnection> listByNextPoseVideoId(Integer poseVideoId);

    ProjYogaPoseVideoConnection getByVideoConnection(Integer poseVideoId, Integer nextPoseVideoId);

    /**
     * 通过pose video Id 进行逻辑删除
     *
     * @param poseVideoId
     */
    void deleteByPoseVideoId(Integer poseVideoId);

    /**
     * 获取未删除的connection信息
     * @return
     */
    List<ProjYogaPoseVideoConnectionDTO> listValidConnection();
}
