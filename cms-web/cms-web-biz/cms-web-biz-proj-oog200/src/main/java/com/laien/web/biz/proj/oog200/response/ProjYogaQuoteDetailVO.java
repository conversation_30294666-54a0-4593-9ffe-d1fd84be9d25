package com.laien.web.biz.proj.oog200.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * yoga名言警句
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@Accessors(chain = true)
@TableName("proj_yoga_quote")
@ApiModel(value="ProjYogaQuote对象", description="yoga名言警句")
public class ProjYogaQuoteDetailVO {

    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "名言警句内容")
    private String content;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
