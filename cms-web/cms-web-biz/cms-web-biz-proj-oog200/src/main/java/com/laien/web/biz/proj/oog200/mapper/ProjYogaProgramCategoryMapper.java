package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramCategory;
import com.laien.web.biz.proj.oog200.handler.YogaProgramTypeHandler;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 21:20
 */
public interface ProjYogaProgramCategoryMapper extends BaseMapper<ProjYogaProgramCategory> {

    @Select("SELECT * FROM proj_yoga_program_category WHERE id = #{id} and del_flag = 0 ")
    @Results({
            @Result(column = "program_type", property = "programType", typeHandler = YogaProgramTypeHandler.class)
    })
    ProjYogaProgramCategory getEntityById(@Param("id") Integer id);

    @Select("<script>" +
            "SELECT * FROM proj_yoga_program_category " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    @Results({
            @Result(column = "program_type", property = "programType", typeHandler = YogaProgramTypeHandler.class)
    })
    List<ProjYogaProgramCategory> selectByIds(@Param("ids") List<Integer> ids);
}
