package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjCollectionTeacher;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherAddReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherListReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionTeacherListVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionTeacherVO;
import com.laien.web.biz.proj.oog200.service.IProjCollectionTeacherService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.utils.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 教练表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Api(tags = "项目管理:Collection Teacher")
@RestController
@RequestMapping("/proj/collectionTeacher")
@RequiredArgsConstructor
public class ProjCollectionTeacherController extends ResponseController {

    @Resource
    private IProjCollectionTeacherService projCollectionTeacherService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjCollectionTeacherVO>> list(ProjCollectionTeacherListReq listReq) {
        return succ(projCollectionTeacherService.list(listReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjCollectionTeacherAddReq req) {
        projCollectionTeacherService.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjCollectionTeacherUpdateReq req) {
        projCollectionTeacherService.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjCollectionTeacherVO> detail(@PathVariable Integer id) {
        return succ(projCollectionTeacherService.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCollectionTeacherService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCollectionTeacherService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCollectionTeacherService.deleteByIdList(idList);
        return succ();
    }
}
