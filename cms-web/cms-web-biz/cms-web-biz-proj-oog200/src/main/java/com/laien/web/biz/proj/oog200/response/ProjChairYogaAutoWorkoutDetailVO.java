package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel(value = "ProjChairYogaAutoWorkoutDetailVO", description = "ProjChairYogaAutoWorkoutDetailVO")
public class ProjChairYogaAutoWorkoutDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "锻炼部位，多选，示例值 Upper Body 、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "难度，可选值有 Newbie,Beginner,Intermediate,Advanced")
    private String difficulty;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "模版id")
    private Integer projYogaAutoTemplateId;

    @ApiModelProperty(value = "video列表")
    private List<ProjChairYogaVideoPageVO> videoList;

}
