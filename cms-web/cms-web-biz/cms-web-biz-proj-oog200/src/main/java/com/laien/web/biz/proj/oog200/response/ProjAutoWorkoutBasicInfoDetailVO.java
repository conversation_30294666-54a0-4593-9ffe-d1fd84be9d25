package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.AutoWorkoutBasicInfoPointEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjAutoWorkoutBasicInfoDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "图片用途")
    private AutoWorkoutBasicInfoPointEnum point;

    @ApiModelProperty(value = "plan类型")
    private YogaAutoWorkoutTemplateEnum planType;

    @ApiModelProperty(value = "difficulty类型")
    private DifficultyEnum difficulty;

    @ApiModelProperty(value = "详情图（默认和女）")
    private String detailImage;

    @ApiModelProperty(value = "封面图(male)")
    private String coverImageMale;

    @ApiModelProperty(value = "complete_image")
    private String completeImage;

    @ApiModelProperty(value = "封面图（默认和女）")
    private String coverImage;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer sorted;

    @ApiModelProperty(value = "autoWorkoutIdList")
    private List<Integer> autoWorkoutIdList;

    @ApiModelProperty(value = "autoWorkoutList")
    private List<AutoWorkoutListVO> autoWorkoutList;

}
