package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseUserAssignIdModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * video slice多语言
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResVideoSliceI18n对象", description="video slice多语言")
public class ResVideoSliceI18n extends BaseUserAssignIdModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "视频标题字幕")
    private String titleSubtitleUrl;

    @ApiModelProperty(value = "视频详细指导字幕url")
    private String guidanceDefaultUrl;

    @ApiModelProperty(value = "视频详细指导音频url")
    private String guidanceDefaultAudioUrl;

    @ApiModelProperty(value = "视频简略指导字幕url")
    private String guidanceLeastUrl;

    @ApiModelProperty(value = "视频简略指导音频url")
    private String guidanceLeastAudioUrl;


}
