package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.response.ProjAllergenVO;
import com.laien.web.biz.proj.oog200.service.IProjAllergenService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * allergen 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Api(tags = "项目管理:allergen")
@RestController
@RequestMapping("/proj/allergen")
public class ProjAllergenController extends ResponseController {

    @Resource
    private IProjAllergenService projAllergenService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjAllergenVO>> list() {
        return succ(projAllergenService.query(RequestContextUtils.getProjectId()));
    }
}
