package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2024/7/31 10:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="ProjYogaPoseVideo对象", description="proj yoga pose video")
public class ProjYogaPoseVideoConnection extends BaseModel {

    @ApiModelProperty(value = "proj yoga pose video id")
    private Integer projYogaPoseVideoId;

    @ApiModelProperty(value = "next proj yoga pose video id")
    private Integer projYogaPoseVideoNextId;

    @ApiModelProperty(value = "proj yoga pose transition id")
    private Integer projYogaPoseTransitionId;

    @ApiModelProperty(value = "项目Id")
    private Integer projId;

}
