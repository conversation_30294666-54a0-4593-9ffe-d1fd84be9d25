package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * oog200 regular workout 关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaRegularWorkoutVideoRelation对象", description="oog200 regular workout 关联表")
public class ProjYogaRegularWorkoutVideoRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "workout id")
    private Integer projYogaRegularWorkoutId;

    @ApiModelProperty(value = "video id")
    private Integer resYogaVideoId;

    @ApiModelProperty(value = "对应当前video指向下一个video的link 最后一个节点时，此项为null")
    private Integer resYogaVideoConnectionId;

    @ApiModelProperty(value = "真实的视频播放时长")
    private Integer realVideoDuration;

    @ApiModelProperty(value = "真实的过渡时长")
    private Integer realTransitionDuration;


}
