package com.laien.web.biz.proj.oog200.controller;


import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.web.biz.proj.oog200.entity.ProjPoseLibrary;
import com.laien.web.biz.proj.oog200.entity.ResPoseLibrary;
import com.laien.web.biz.proj.oog200.request.ProjPoseLibraryPageReq;
import com.laien.web.biz.proj.oog200.service.IProjPoseLibraryService;
import com.laien.web.biz.proj.oog200.service.IResPoseLibraryService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目poseLibrary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Api(tags = "项目管理:pose-library")
@RestController
@RequestMapping("/proj/poseLibrary")
public class ProjPoseLibraryController extends ResponseController {

    @Resource
    private IProjPoseLibraryService projPoseLibraryService;

    @Resource
    private IResPoseLibraryService resPoseLibraryService;

    private int sortNo = 0;

    @ApiOperation(value = "查询已使用的poseLibrary列表")
    @GetMapping("/list")
    public ResponseResult<PageRes<ResPoseLibrary>> list(ProjPoseLibraryPageReq pageReq) {
        Integer projId = RequestContextUtils.getProjectId();
        if (projId == null) {
            return fail("The project id cannot be empty");
        }
        final PageRes<ResPoseLibrary>[] result = new PageRes[]{new PageRes(pageReq.getPageNum(), pageReq.getPageSize(), 0, 0, Lists.newArrayList())};
        if (pageReq.getOrderByPoseName() == null) {
            LambdaQueryWrapper<ProjPoseLibrary> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjPoseLibrary::getProjId, projId);
            Page<ProjPoseLibrary> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
            queryWrapper.orderByAsc(ProjPoseLibrary::getSortNo);
            projPoseLibraryService.page(page, queryWrapper);
            result[0].setTotal(page.getTotal());
            result[0].setPages(page.getPages());
            Optional.ofNullable(page.getRecords()).filter(CollectionUtils::isNotEmpty).ifPresent(projPoseLibrariesList -> {
                List<Integer> poseLibraryIds = projPoseLibrariesList.stream().map(ProjPoseLibrary::getPoseLibraryId).collect(Collectors.toList());
                List<ResPoseLibrary> resPoseLibraryList = resPoseLibraryService.listByIds(poseLibraryIds);
                result[0].setList(resPoseLibraryList);
            });
            result[0].getList().stream().filter(pose -> StringUtils.isNotBlank(pose.getFocus())).forEach(pose -> pose.setFocus(Joiner.on(",").skipNulls().join(Splitter.on("|").omitEmptyStrings().trimResults().split(pose.getFocus()))));
        } else {
            LambdaQueryWrapper<ProjPoseLibrary> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjPoseLibrary::getProjId, projId);
            List<ProjPoseLibrary> projPoseLibraries = projPoseLibraryService.list(queryWrapper);
            result[0].setTotal(projPoseLibraries.size());
            List<Integer> resPoseLibraryIds = projPoseLibraries.stream().map(ProjPoseLibrary::getPoseLibraryId).collect(Collectors.toList());
            List<ResPoseLibrary> resPoseLibraryList = resPoseLibraryService.listByIds(resPoseLibraryIds, pageReq.getPageNum(), pageReq.getPageSize(), "pose_name", pageReq.getOrderByPoseName() == GlobalConstant.YES ? "ASC" : "DESC");
            result[0].setList(resPoseLibraryList);
            result[0].getList().stream().filter(pose -> StringUtils.isNotBlank(pose.getFocus())).forEach(pose -> pose.setFocus(Joiner.on(",").skipNulls().join(Splitter.on("|").omitEmptyStrings().trimResults().split(pose.getFocus()))));
        }
        return succ(result[0]);
    }

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@Validated(Group.class) @RequestBody IdListReq idListReq) {
        if (idListReq.getIdList() == null) {
            return succ();
        }
        LinkedHashSet<Integer> idList = Sets.newLinkedHashSet(idListReq.getIdList());
        Integer projId = RequestContextUtils.getProjectId();
        LambdaQueryWrapper<ProjPoseLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjPoseLibrary::getProjId, projId);
        List<ProjPoseLibrary> oldProjPostLibraryList = projPoseLibraryService.list(queryWrapper);
        Set<Integer> oldIdList = oldProjPostLibraryList.stream().map(ProjPoseLibrary::getPoseLibraryId).collect(Collectors.toSet());
        //删除
        Sets.SetView<Integer> difference = Sets.difference(oldIdList, idList);
        if (difference.size() > 0) {
            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjPoseLibrary::getProjId, projId);
            queryWrapper.in(ProjPoseLibrary::getPoseLibraryId, difference);
            projPoseLibraryService.remove(queryWrapper);
        }
        //新增数据
        final Integer[] sort = {sortNo};
        idList.stream().forEach(poseLibraryId -> {
            ProjPoseLibrary projPoseLibrary = new ProjPoseLibrary();
            projPoseLibrary.setPoseLibraryId(poseLibraryId);
            projPoseLibrary.setSortNo(sort[0]);
            projPoseLibrary.setProjId(projId);
            sort[0]++;
            if (!oldIdList.contains(poseLibraryId)) {
                projPoseLibraryService.save(projPoseLibrary);
            } else {
                LambdaUpdateWrapper<ProjPoseLibrary> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ProjPoseLibrary::getProjId, projPoseLibrary.getProjId());
                updateWrapper.eq(ProjPoseLibrary::getPoseLibraryId, projPoseLibrary.getPoseLibraryId());
                updateWrapper.set(ProjPoseLibrary::getSortNo, projPoseLibrary.getSortNo());
                projPoseLibraryService.update(new ProjPoseLibrary(), updateWrapper);
            }
        });
        return succ();
    }
}
