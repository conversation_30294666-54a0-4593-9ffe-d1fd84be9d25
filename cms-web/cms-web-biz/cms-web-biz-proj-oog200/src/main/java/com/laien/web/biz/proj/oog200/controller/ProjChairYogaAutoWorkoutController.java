package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaAutoWorkoutPageVO;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaAutoWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Author:  hhl
 * Date:  2024/9/29 10:18
 */

@Api(tags = "项目管理: Chair Yoga Auto Workout")
@RestController
@RequestMapping("/proj/chairYogaAutoWorkout")
public class ProjChairYogaAutoWorkoutController extends ResponseController {

    @Resource
    IProjChairYogaAutoWorkoutService workoutService;

    @ApiOperation(value = "分页-workout-列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjChairYogaAutoWorkoutPageVO>> page(ProjChairYogaAutoWorkoutPageReq pageReq) {

        PageRes<ProjChairYogaAutoWorkoutPageVO> pageRes = workoutService.page(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "分页-workout-video列表")
    @GetMapping("/page/video")
    public ResponseResult<PageRes<ProjChairYogaVideo>> pageWorkout(ProjChairYogaAutoWorkoutPageReq pageReq) {

        return succ(workoutService.page4RelationVideo(pageReq));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjChairYogaAutoWorkoutAddReq workoutAddReq) {
        workoutAddReq.setProjId(RequestContextUtils.getProjectId());
        workoutService.saveWorkout(workoutAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjChairYogaAutoWorkoutUpdateReq updateReq) {
        updateReq.setProjId(RequestContextUtils.getProjectId());
        workoutService.update(updateReq);
        return succ();
    }

    @ApiOperation(value = "批量修改")
    @PostMapping("/updateBatch")
    public ResponseResult<Void> batchUpdate(@RequestBody ProjChairYogaAutoWorkoutBatchUpdateReq batchUpdateReq) {

        batchUpdateReq.setProjId(RequestContextUtils.getProjectId());
        workoutService.batchUpdate(batchUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjChairYogaAutoWorkoutDetailVO> detail(@PathVariable Integer id) {

        return succ(workoutService.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        workoutService.updateEnableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        workoutService.updateDisableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        workoutService.deleteByIds(idListReq.getIdList());
        return succ();
    }
}
