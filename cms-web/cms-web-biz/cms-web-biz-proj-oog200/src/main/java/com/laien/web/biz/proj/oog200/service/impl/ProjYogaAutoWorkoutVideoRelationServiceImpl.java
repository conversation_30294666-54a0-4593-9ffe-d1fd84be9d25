package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaAutoWorkoutVideoRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaAutoWorkoutVideoRelationService;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * <p>
 * oog200 workout 关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class ProjYogaAutoWorkoutVideoRelationServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutVideoRelationMapper, ProjYogaAutoWorkoutVideoRelation> implements IProjYogaAutoWorkoutVideoRelationService {

    @Override
    public void removeByWorkout(Collection<Integer> workoutIds) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutVideoRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaAutoWorkoutVideoRelation::getProjYogaAutoWorkoutId, workoutIds);
        remove(queryWrapper);
    }
}
