package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaMusic;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaMusicMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaMusicDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaMusicPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaMusicService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_ENABLE;

/**
 * Author:  hhl
 * Date:  2025/2/19 15:18
 */
@Service
public class ProjYogaMusicServiceImpl extends ServiceImpl<ProjYogaMusicMapper, ProjYogaMusic> implements IProjYogaMusicService {

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public PageRes<ProjYogaMusicPageVO> pageQuery(ProjYogaMusicPageReq musicPageReq) {

        LambdaQueryWrapper<ProjYogaMusic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaMusic::getProjId, musicPageReq.getProjId());
        queryWrapper.eq(Objects.nonNull(musicPageReq.getId()), ProjYogaMusic::getId, musicPageReq.getId());
        queryWrapper.like(!StringUtils.isEmpty(musicPageReq.getMusicName()), ProjYogaMusic::getMusicName, musicPageReq.getMusicName());
        queryWrapper.like(Objects.nonNull(musicPageReq.getMusicType()), ProjYogaMusic::getMusicType, musicPageReq.getMusicType());
        queryWrapper.orderByDesc(ProjYogaMusic::getId);

        Page<ProjYogaMusic> musicPage = new Page<>(musicPageReq.getPageNum(), musicPageReq.getPageSize());
        IPage<ProjYogaMusic> iPage = this.page(musicPage, queryWrapper);
        PageRes<ProjYogaMusicPageVO> pageRes = PageConverter.convert(iPage, ProjYogaMusicPageVO.class);
        return pageRes;
    }

    @Override
    public void insert(ProjYogaMusicAddReq musicAddReq) {

        bizCheck(musicAddReq, null);
        ProjYogaMusic yogaMusic = new ProjYogaMusic();
        BeanUtils.copyProperties(musicAddReq, yogaMusic);

        yogaMusic.setStatus(STATUS_ENABLE);
        yogaMusic.setProjId(RequestContextUtils.getProjectId());
        save(yogaMusic);

        projLmsI18nService.handleI18n(Collections.singletonList(yogaMusic), yogaMusic.getProjId());
    }

    private void bizCheck(ProjYogaMusicAddReq addReq, Integer id) {

        LambdaQueryWrapper<ProjYogaMusic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaMusic::getMusicName, addReq.getMusicName());
        queryWrapper.ne(Objects.nonNull(id), ProjYogaMusic::getId, id);

        ProjYogaMusic yogaMusic = getOne(queryWrapper);
        if (Objects.nonNull(yogaMusic)) {
            throw new BizException("Music name is existed.");
        }
    }

    @Override
    public void update(ProjYogaMusicUpdateReq musicUpdateReq) {

        bizCheck(musicUpdateReq, musicUpdateReq.getId());
        ProjYogaMusic yogaMusic = getById(musicUpdateReq.getId());

        BeanUtils.copyProperties(musicUpdateReq, yogaMusic);
        updateById(yogaMusic);
        projLmsI18nService.handleI18n(Collections.singletonList(yogaMusic), RequestContextUtils.getProjectId());
    }

    @Override
    public ProjYogaMusicDetailVO getDetailById(Integer musicId) {

        ProjYogaMusic yogaMusic = getById(musicId);
        if (Objects.isNull(yogaMusic)) {
            return null;
        }

        ProjYogaMusicDetailVO musicDetailVO = new ProjYogaMusicDetailVO();
        BeanUtils.copyProperties(yogaMusic, musicDetailVO);
        return musicDetailVO;
    }
}
