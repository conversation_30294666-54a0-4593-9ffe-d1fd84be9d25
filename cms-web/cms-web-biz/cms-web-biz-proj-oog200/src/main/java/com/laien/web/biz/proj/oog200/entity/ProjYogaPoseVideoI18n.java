/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>ProjYogaPoseVideo 多语言 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
public class ProjYogaPoseVideoI18n {

    @ApiModelProperty(value = "名称多语言翻译")
    private String name;

    @ApiModelProperty(value = "名称翻译音频")
    private String nameFemale;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameFemaleDuration;


}