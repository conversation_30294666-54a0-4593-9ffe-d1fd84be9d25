package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.frame.entity.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * regular workout和category关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_regular_workout_category_relation")
@ApiModel(value="ProjYogaRegularWorkoutCategoryRelation对象", description="regular workout和category关系表")
public class ProjYogaRegularWorkoutCategoryRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    private Integer workoutId;

    @ApiModelProperty(value = "proj_yoga_regular_category表id")
    private Integer projYogaRegularCategoryId;

    @ApiModelProperty(value = "workout类型,0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga")
    private YogaAutoWorkoutTemplateEnum workoutType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
