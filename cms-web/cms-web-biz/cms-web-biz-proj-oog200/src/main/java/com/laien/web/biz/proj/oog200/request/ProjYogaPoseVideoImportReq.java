package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2024/8/12 11:37
 */
@Data
public class ProjYogaPoseVideoImportReq {

    @NotEmpty(message = "Video Name cannot be empty", groups = Group1.class)
    @Length(message = "The Video Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Video Name", converter = StringStringTrimConverter.class)
    private String name;

    @ExcelProperty(value = "core_voice_config_i18n_name",converter = StringStringTrimConverter.class)
    @NotEmpty(message = "coreVoiceConfigI18nId cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "翻译声音")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ExcelProperty(value = "Event Name", converter = StringStringTrimConverter.class)
    private String eventName;

    @ExcelProperty(value = "Image Url", converter = StringStringTrimConverter.class)
    private String imageUrl;

    @ExcelProperty(value = "Difficulty", converter = StringStringTrimConverter.class)
    private String difficulty;

    @Length(message = "The Pose Type cannot exceed 50 characters", min = 1, max = 50, groups = Group2.class)
    @NotEmpty(message = "Pose Type cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Pose Type", converter = StringStringTrimConverter.class)
    private String poseType;

    @Length(message = "The Pose Direction cannot exceed 50 characters", min = 1, max = 50, groups = Group2.class)
    @NotEmpty(message = "Pose Direction cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Pose Direction", converter = StringStringTrimConverter.class)
    private String poseDirection;

    @NotNull(message = "Calorie cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Calorie")
    private BigDecimal calorie;

    @Length(message = "The Front Video Url cannot exceed 255 characters", min = 1, max = 255, groups = Group2.class)
    @NotEmpty(message = "Front Video Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Url", converter = StringStringTrimConverter.class)
    private String frontVideoUrl;

    @Min(value = 1, message = "The Front Video Duration can't be less than 1.", groups = Group2.class)
    @Max(value = Integer.MAX_VALUE, message = "The Front Video Duration can't be more than 2147483647.", groups = Group2.class)
    @NotNull(message = "Front Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Duration")
    private Integer frontVideoDuration;

    @Length(message = "The Transition Name cannot exceed 255 characters", min = 1, max = 255, groups = Group2.class)
    @NotEmpty(message = "Side Video Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Side Video Url", converter = StringStringTrimConverter.class)
    private String sideVideoUrl;

    @Min(value = 1, message = "The Side Video Duration can't be less than 1.", groups = Group2.class)
    @Max(value = Integer.MAX_VALUE, message = "The Side Video Duration can't be more than 2147483647.", groups = Group2.class)
    @NotNull(message = "Side Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Side Video Duration")
    private Integer sideVideoDuration;

//    @NotEmpty(message = "Audio 1-1 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-1 Url", converter = StringStringTrimConverter.class)
    private String audio1_1_url;

//    @NotEmpty(message = "Audio 1-1 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-1 Duration")
    private Integer audio1_1_duration;

//    @NotEmpty(message = "Audio 1-1 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-2 Url", converter = StringStringTrimConverter.class)
    private String audio1_2_url;

//    @NotEmpty(message = "Audio 1-2 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-2 Duration")
    private Integer audio1_2_duration;

//    @NotEmpty(message = "Audio 1-3 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-3 Url", converter = StringStringTrimConverter.class)
    private String audio1_3_url;

//    @NotEmpty(message = "Audio 1-3 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-3 Duration")
    private Integer audio1_3_duration;

//    @NotEmpty(message = "Audio 1-4 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-4 Url", converter = StringStringTrimConverter.class)
    private String audio1_4_url;

//    @NotEmpty(message = "Audio 1-4 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-4 Duration")
    private Integer audio1_4_duration;

//    @NotEmpty(message = "Audio 1-5 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-5 Url", converter = StringStringTrimConverter.class)
    private String audio1_5_url;

//    @NotEmpty(message = "Audio 1-5 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-5 Duration")
    private Integer audio1_5_duration;

//    @NotEmpty(message = "Audio 1-6 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-6 Url", converter = StringStringTrimConverter.class)
    private String audio1_6_url;

//    @NotEmpty(message = "Audio 1-6 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 1-6 Duration")
    private Integer audio1_6_duration;

//    @NotEmpty(message = "Audio 2-1 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-1 Url", converter = StringStringTrimConverter.class)
    private String audio2_1_url;

//    @NotEmpty(message = "Audio 2-1 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-1 Duration")
    private Integer audio2_1_duration;

//    @NotEmpty(message = "Audio 2-2 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-2 Url", converter = StringStringTrimConverter.class)
    private String audio2_2_url;

//    @NotEmpty(message = "Audio 2-2 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-2 Duration")
    private Integer audio2_2_duration;

//    @NotEmpty(message = "Audio 2-3 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-3 Url", converter = StringStringTrimConverter.class)
    private String audio2_3_url;

//    @NotEmpty(message = "Audio 2-3 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-3 Duration")
    private Integer audio2_3_duration;

//    @NotEmpty(message = "Audio 2-4 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-4 Url", converter = StringStringTrimConverter.class)
    private String audio2_4_url;

//    @NotEmpty(message = "Audio 2-4 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-4 Duration")
    private Integer audio2_4_duration;

//    @NotEmpty(message = "Audio 2-5 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-5 Url", converter = StringStringTrimConverter.class)
    private String audio2_5_url;

//    @NotEmpty(message = "Audio 2-5 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-5 Duration")
    private Integer audio2_5_duration;

//    @NotEmpty(message = "Audio 2-6 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-6 Url", converter = StringStringTrimConverter.class)
    private String audio2_6_url;

//    @NotEmpty(message = "Audio 2-6 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 2-6 Duration")
    private Integer audio2_6_duration;

//    @NotEmpty(message = "Audio 3-1 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-1 Url", converter = StringStringTrimConverter.class)
    private String audio3_1_url;

//    @NotEmpty(message = "Audio 3-1 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-1 Duration")
    private Integer audio3_1_duration;

//    @NotEmpty(message = "Audio 3-4 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-4 Url", converter = StringStringTrimConverter.class)
    private String audio3_4_url;

//    @NotEmpty(message = "Audio 3-4 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-4 Duration")
    private Integer audio3_4_duration;

//    @NotEmpty(message = "Audio 3-5 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-5 Url", converter = StringStringTrimConverter.class)
    private String audio3_5_url;

//    @NotEmpty(message = "Audio 3-5 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-5 Duration")
    private Integer audio3_5_duration;

//    @NotEmpty(message = "Audio 3-6 Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-6 Url", converter = StringStringTrimConverter.class)
    private String audio3_6_url;

//    @NotEmpty(message = "Audio 3-6 Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Audio 3-6 Duration")
    private Integer audio3_6_duration;

}
