package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/23 16:38
 */
@Data
@ApiModel(value = "yoga pose video分页", description = "yoga pose video分页")
public class ProjChairYogaVideoPageReq extends PageReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "动作朝向， Central、 Left、 Right")
    private String direction;

    @ApiModelProperty(value = "动作类型，单选，示例值 Warmup、Main、Cooldown")
    private String type;

    @ApiModelProperty(value = "锻炼部位，多选，示例值 Upper Body 、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "锻炼姿势，单选，示例值 Seated、Standing")
    private String position;

    @ApiModelProperty(value = "排序字段，降序方式，可选值为name，为空默认Id降序")
    private String sortField;

    @JsonIgnore
    @ApiModelProperty(value = "排除的video name list")
    private List<String> yogaVideoNameList;
}
