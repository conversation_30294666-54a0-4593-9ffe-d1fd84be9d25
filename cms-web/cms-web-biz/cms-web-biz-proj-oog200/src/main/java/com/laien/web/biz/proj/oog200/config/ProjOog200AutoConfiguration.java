package com.laien.web.biz.proj.oog200.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Configuration
@MapperScan({"com.laien.web.biz.proj.oog200.mapper"})
@ComponentScan(value = "com.laien.web.biz.proj.oog200")
public class ProjOog200AutoConfiguration {
}
