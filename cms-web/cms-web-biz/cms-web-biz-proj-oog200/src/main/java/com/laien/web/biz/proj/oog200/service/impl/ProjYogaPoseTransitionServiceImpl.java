package com.laien.web.biz.proj.oog200.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.api.client.util.Sets;
import com.google.common.collect.Lists;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.biz.proj.oog200.constant.PoseWorkoutConstant;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseTransition;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseTransitionMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionImportReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseTransitionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseTransitionDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseTransitionPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseTransitionService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.PROJ_YOGA_POSE_TRANSITION_FRONT;


/**
 * Author:  hhl
 * Date:  2024/8/1 14:42
 */

@Service
public class ProjYogaPoseTransitionServiceImpl extends ServiceImpl<ProjYogaPoseTransitionMapper, ProjYogaPoseTransition> implements IProjYogaPoseTransitionService {

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @Resource
    private Validator validator;

    @Override
    public PageRes<ProjYogaPoseTransitionPageVO> selectPoseTransitionPage(ProjYogaPoseTransitionPageReq pageReq) {

        LambdaQueryWrapper<ProjYogaPoseTransition> queryWrapper = new LambdaQueryWrapper();
        wrapQuery(queryWrapper, pageReq);

        Page<ProjYogaPoseTransition> poseTransitionPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaPoseTransition> iPage = this.page(poseTransitionPage, queryWrapper);
        PageRes<ProjYogaPoseTransitionPageVO> pageRes = PageConverter.convert(iPage, ProjYogaPoseTransitionPageVO.class);

        injectionTaskStatus(pageRes.getList());
        return pageRes;
    }

    /**
     * 正侧切片任务状态返回
     */
    private void injectionTaskStatus(List<ProjYogaPoseTransitionPageVO> videoList) {

        if (CollectionUtils.isEmpty(videoList)) {
            return;
        }

        Set<Integer> videoIds = videoList.stream().map(ProjYogaPoseTransitionPageVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(PROJ_YOGA_POSE_TRANSITION_FRONT.getTableName(), PROJ_YOGA_POSE_TRANSITION_FRONT.getEntityFieldName(), videoIds);
        if (CollectionUtils.isEmpty(frontStatusList)) {
            return;
        }

        Map<Integer, TaskResourceSectionStatusEnums> frontIdStatusMap = frontStatusList.stream()
                .collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));
        videoList.forEach(video -> {
            Optional.ofNullable(frontIdStatusMap.get(video.getId())).ifPresent(video::setFrontTaskStatus);
        });
    }

    private void wrapQuery(LambdaQueryWrapper<ProjYogaPoseTransition> queryWrapper, ProjYogaPoseTransitionPageReq pageReq) {

        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjYogaPoseTransition::getStatus, pageReq.getStatus());
        queryWrapper.like(StringUtils.isNotBlank(pageReq.getName()), ProjYogaPoseTransition::getName, pageReq.getName());

        queryWrapper.orderByDesc(StringUtils.isNotBlank(pageReq.getSortField()), ProjYogaPoseTransition::getName);
        queryWrapper.orderByDesc(StringUtils.isBlank(pageReq.getSortField()), ProjYogaPoseTransition::getId);
    }

    @Override
    public void savePoseTransition(ProjYogaPoseTransitionAddReq addReq) {

        validatePoseTransition4Biz(addReq);
        ProjYogaPoseTransition poseTransition = new ProjYogaPoseTransition();
        poseTransition.setProjId(RequestContextUtils.getProjectId());
        poseTransition.setStatus(GlobalConstant.STATUS_DRAFT);

        BeanUtils.copyProperties(addReq, poseTransition);
        save(poseTransition);
    }

    @Override
    public void updatePoseTransition(ProjYogaPoseTransitionUpdateReq updateReq) {

        ProjYogaPoseTransition poseTransition = getById(updateReq.getId());
        if (Objects.isNull(poseTransition)) {
            throw new BizException("Record does not exist.");
        }

        // bug fix
        validatePoseTransition4Biz(updateReq);

        updatePoseTransition(updateReq, poseTransition);
    }

    private void updatePoseTransition(ProjYogaPoseTransitionUpdateReq updateReq, ProjYogaPoseTransition poseTransition) {

        poseTransition.setName(updateReq.getName());
        poseTransition.setFrontVideoUrl(updateReq.getFrontVideoUrl());
        poseTransition.setFrontVideoDuration(updateReq.getFrontVideoDuration());
        poseTransition.setImageUrl(updateReq.getImageUrl());
        updateById(poseTransition);
    }

    private void validatePoseTransition4Biz(ProjYogaPoseTransitionUpdateReq updateReq) {

        // 非空校验
        if (Objects.isNull(updateReq)) {
            throw new BizException("Transition can't be null.");
        }

        LambdaQueryWrapper<ProjYogaPoseTransition> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProjYogaPoseTransition::getName, updateReq.getName());
        queryWrapper.ne(ProjYogaPoseTransition::getId, updateReq.getId());
        ProjYogaPoseTransition poseTransition = getOne(queryWrapper);

        if (Objects.nonNull(poseTransition)) {
            throw new BizException("Transition name is existed.");
        }
    }


    private void validatePoseTransition4Biz(ProjYogaPoseTransitionAddReq addReq) {

        // 非空校验
        if (Objects.isNull(addReq)) {
            throw new BizException("Transition can't be null.");
        }

        LambdaQueryWrapper<ProjYogaPoseTransition> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProjYogaPoseTransition::getName, addReq.getName());
        ProjYogaPoseTransition poseTransition = getOne(queryWrapper);

        if (Objects.nonNull(poseTransition)) {
            throw new BizException("Transition name is existed.");
        }
    }

    @Override
    public ProjYogaPoseTransitionDetailVO getDetailById(Integer transitionId) {

        if (Objects.isNull(transitionId)) {
            return null;
        }

        ProjYogaPoseTransition poseTransition = this.getById(transitionId);
        if (Objects.isNull(poseTransition)) {
            return null;
        }

        ProjYogaPoseTransitionDetailVO detailVO = convert2Detail(poseTransition);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        LambdaUpdateWrapper<ProjYogaPoseTransition> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaPoseTransition::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjYogaPoseTransition::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjYogaPoseTransition::getId, idList);
        update(updateWrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        LambdaUpdateWrapper<ProjYogaPoseTransition> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaPoseTransition::getStatus, GlobalConstant.STATUS_DISABLE);
        updateWrapper.eq(ProjYogaPoseTransition::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjYogaPoseTransition::getId, idList);
        update(updateWrapper);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        LambdaUpdateWrapper<ProjYogaPoseTransition> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaPoseTransition::getDelFlag, GlobalConstant.YES);
        updateWrapper.eq(ProjYogaPoseTransition::getStatus, GlobalConstant.STATUS_DRAFT);
        updateWrapper.in(ProjYogaPoseTransition::getId, idList);
        update(updateWrapper);
    }

    @Override
    public List<ProjYogaPoseTransition> listEnableTransition(Collection<Integer> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaPoseTransition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseTransition::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(ProjYogaPoseTransition::getId, idList);
        return list(queryWrapper);
    }

    private ProjYogaPoseTransitionDetailVO convert2Detail(ProjYogaPoseTransition poseTransition) {

        ProjYogaPoseTransitionDetailVO detailVO = new ProjYogaPoseTransitionDetailVO();
        BeanUtils.copyProperties(poseTransition, detailVO);
        return detailVO;
    }

    @Override
    @Transactional
    public List<String> importPoseTransition(MultipartFile file) {

        if (Objects.isNull(file)) {
            return Collections.emptyList();
        }

        List<ProjYogaPoseTransitionImportReq> poseTransitionImportList = parsePoseTransitionImport(file);
        List<String> errorMessageList = Lists.newArrayList();
        List<ProjYogaPoseTransitionImportReq> validPoseTransitionImportList = filterPoseTransitionImport(poseTransitionImportList, errorMessageList);

        Integer projId = RequestContextUtils.getProjectId();
        saveBatch4Import(validPoseTransitionImportList, projId);
        return errorMessageList;
    }

    private void saveBatch4Import(List<ProjYogaPoseTransitionImportReq> importTransitionList, Integer projId) {

        Set<String> existedNameSet = listExistedTransitionName();
        List<ProjYogaPoseTransition> transitionList = importTransitionList.stream()
                .filter(poseTransition -> !existedNameSet.contains(poseTransition.getName()))
                .map(poseTransition -> {
                    ProjYogaPoseTransition transition = new ProjYogaPoseTransition();
                    BeanUtils.copyProperties(poseTransition, transition);
                    transition.setProjId(projId);
                    return transition;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(transitionList)) {
            return;
        }
        saveBatch(transitionList);
    }

    private Set<String> listExistedTransitionName() {

        List<ProjYogaPoseTransition> poseTransitionList = list();
        if (CollectionUtils.isEmpty(poseTransitionList)) {
            return Collections.emptySet();
        }

        Set<String> existedNameSet = poseTransitionList.stream()
                .filter(poseTransition -> Objects.equals(poseTransition.getDelFlag(), GlobalConstant.NO))
                .map(ProjYogaPoseTransition::getName).collect(Collectors.toSet());
        return existedNameSet;
    }

    private List<ProjYogaPoseTransitionImportReq> filterPoseTransitionImport(List<ProjYogaPoseTransitionImportReq> poseVideoImportList, List<String> errorMessageList) {

        Set<String> transitionNameSet = Sets.newHashSet();
        List<ProjYogaPoseTransitionImportReq> validTransitionVideoList = poseVideoImportList.stream()
                .filter(poseTransition -> {
                    Set<ConstraintViolation<ProjYogaPoseTransitionImportReq>> violationSet = validator.validate(poseTransition, Group.class);
                    if (!CollectionUtils.isEmpty(violationSet)) {
                        Optional<ConstraintViolation<ProjYogaPoseTransitionImportReq>> violation = violationSet.stream().findAny();
                        errorMessageList.add(String.format(PoseWorkoutConstant.VIDEO_IMPORT_ERROR, poseTransition.getName(), violation.get().getMessage()));
                        return false;
                    }
                    return true;
                }).peek(poseTransition -> {
                    if (transitionNameSet.contains(poseTransition.getName())) {
                        throw new BizException("Pose transition name can't be repeated, repeated name is " + poseTransition.getName());
                    }
                    transitionNameSet.add(poseTransition.getName());
                }).collect(Collectors.toList());

        return validTransitionVideoList;
    }

    private List<ProjYogaPoseTransitionImportReq> parsePoseTransitionImport(MultipartFile file) {

        List<ProjYogaPoseTransitionImportReq> poseTransitionImportList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), ProjYogaPoseTransitionImportReq.class, new AnalysisEventListener<ProjYogaPoseTransitionImportReq>() {
                @Override
                public void invoke(ProjYogaPoseTransitionImportReq row, AnalysisContext analysisContext) {
                    poseTransitionImportList.add(row);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet(GlobalConstant.ZERO).doRead();
        } catch (IOException e) {
            log.warn("Import pose video error.");
            throw new BizException("Import pose video error.");
        }
        return poseTransitionImportList;
    }


    @Override
    public List<ProjYogaPoseTransition> listPoseTransitionByName(Collection<String> transitionNames) {

        if (CollectionUtils.isEmpty(transitionNames)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaPoseTransition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaPoseTransition::getName, transitionNames);
        return list(queryWrapper);
    }

}
