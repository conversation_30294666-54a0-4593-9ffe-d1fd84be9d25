package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseGroup;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupListReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseGroupUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupListVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseGroupService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj yoga pose grouping 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Api(tags = "项目管理:Pose Workout Group")
@RestController
@RequestMapping("/proj/yogaPoseGroup")
public class ProjYogaPoseGroupController extends ResponseController {
    @Resource
    private IProjYogaPoseGroupService projYogaPoseGroupService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjYogaPoseGroupListVO>> list(ProjYogaPoseGroupListReq listReq) {
        return succ(projYogaPoseGroupService.list(listReq, RequestContextUtils.getProjectId()));
    }


    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaPoseGroupUpdateReq poseGroupReq) {
        projYogaPoseGroupService.update(poseGroupReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaPoseGroupAddReq poseGroupReq) {
        projYogaPoseGroupService.save(poseGroupReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaPoseGroupDetailVO> detail(@PathVariable Integer id) {
        ProjYogaPoseGroupDetailVO detailVO = projYogaPoseGroupService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseGroupService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseGroupService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseGroupService.deleteByIdList(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        projYogaPoseGroupService.sort(idListReq);
        return succ();
    }

}
