package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Video Generate 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Video Generate 分页", description = "Video Generate 分页")
public class ProjVideoGeneratePageReq extends PageReq {

    @ApiModelProperty(value = "template id")
    private Integer templateId;

}
