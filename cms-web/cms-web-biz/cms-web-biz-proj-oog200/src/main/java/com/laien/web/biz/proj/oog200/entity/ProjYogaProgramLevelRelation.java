package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2024/12/17 16:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaProgramLevelRelation对象", description="proj yoga program level relation")
public class ProjYogaProgramLevelRelation extends BaseModel {

    private Integer projYogaProgramLevelId;

    private Integer projYogaRegularWorkoutId;

    private YogaAutoWorkoutTemplateEnum videoType;

    private Integer projId;

}
