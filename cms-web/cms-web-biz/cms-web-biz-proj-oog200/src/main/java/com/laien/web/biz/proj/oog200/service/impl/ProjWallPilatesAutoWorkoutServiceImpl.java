package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.oog200.enums.*;
import com.laien.web.biz.proj.oog200.bo.*;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.web.biz.proj.oog200.handler.ProjWallPilatesWorkoutFileHandler;
import com.laien.web.biz.proj.oog200.mapper.ProjWallPilatesAutoWorkoutMapper;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesAutoWorkoutListVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.PoseDirectionEnum.*;
import static com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum.WALL_PILATES;
import static com.laien.web.biz.proj.oog200.constant.WallPilatesConstant.WALL_PILATES_WORKOUT_JSON;
import static com.laien.web.biz.proj.oog200.constant.WallPilatesConstant.WALL_PILATES_WORKOUT_M3U8;
import static com.laien.web.frame.async.config.ThreadPoolConfig.OTHER_TASK_THREAD_POOL;

/**
 * <p>
 * Wall pilates auto workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Slf4j
@Service
public class ProjWallPilatesAutoWorkoutServiceImpl extends ServiceImpl<ProjWallPilatesAutoWorkoutMapper, ProjWallPilatesAutoWorkout> implements IProjWallPilatesAutoWorkoutService {

    @Resource
    private IProjWallPilatesAutoWorkoutVideoRelationService projWallPilatesAutoWorkoutVideoRelationService;

    @Resource
    private IProjWallPilatesVideoService projWallPilatesVideoService;

    @Resource
    private IProjYogaAutoWorkoutTaskService projYogaAutoWorkoutTaskService;

    @Resource
    private FileService fileService;

    @Resource
    private IProjYogaAutoWorkoutTemplateService projYogaAutoWorkoutTemplateService;

    @Resource
    private IProjAutoWorkoutBasicInfoWorkoutRelationService basicInfoWorkoutRelationService;

    @Resource
    private ProjWallPilatesWorkoutFileHandler projWallPilatesWorkoutFileHandler;

    @Resource
    private IProjYogaAutoWorkoutAudioI18nService projYogaAutoWorkoutAudioI18nService;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public PageRes<ProjWallPilatesAutoWorkoutListVO> page(ProjWallPilatesAutoWorkoutPageReq pageReq, Integer projId) {
        Integer templateStatus = pageReq.getTemplateStatus();
        Integer projYogaAutoWorkoutTemplateId = pageReq.getProjYogaAutoWorkoutTemplateId();
        Set<Integer> templateIdSet = null;
        if (null == projYogaAutoWorkoutTemplateId) {
            List<ProjYogaAutoWorkoutTemplate> templateList = projYogaAutoWorkoutTemplateService.find(templateStatus);
            templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        }
        Integer id = pageReq.getId();
        Integer time = pageReq.getTime();
        Integer maxDuration = null;
        Integer minDuration = null;
        DurationEnum durationEnum = DurationEnum.getByCode(time);
        if (null != durationEnum) {
            minDuration = durationEnum.getMin();
            maxDuration = durationEnum.getMax();
        }
        Integer status = pageReq.getStatus();
        Integer updateStatus = pageReq.getUpdateStatus();
        Integer projYogaAutoWorkoutTaskId = pageReq.getProjYogaAutoWorkoutTaskId();
        String target = pageReq.getTarget();
        String position = pageReq.getPosition();

        LambdaQueryWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(null != projYogaAutoWorkoutTemplateId, ProjWallPilatesAutoWorkout::getProjYogaAutoWorkoutTemplateId, projYogaAutoWorkoutTemplateId)
                .eq(ProjWallPilatesAutoWorkout::getProjId, projId)
                .in(CollUtil.isNotEmpty(templateIdSet), ProjWallPilatesAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIdSet)
                .eq(null != id, ProjWallPilatesAutoWorkout::getId, id)
                .eq(null != status, ProjWallPilatesAutoWorkout::getStatus, status)
                .ge(null != minDuration, ProjWallPilatesAutoWorkout::getDuration, minDuration)
                .lt(null != maxDuration, ProjWallPilatesAutoWorkout::getDuration, maxDuration)
                .eq(null != updateStatus, ProjWallPilatesAutoWorkout::getUpdateStatus, updateStatus)
                .eq(null != projYogaAutoWorkoutTaskId, ProjWallPilatesAutoWorkout::getProjYogaAutoWorkoutTaskId, projYogaAutoWorkoutTaskId)
                .eq(StringUtils.isNotBlank(target), ProjWallPilatesAutoWorkout::getTarget, target)
                .eq(StringUtils.isNotBlank(position), ProjWallPilatesAutoWorkout::getPosition, position)
                .orderByDesc(BaseModel::getId);
        // 查询
        Page<ProjWallPilatesAutoWorkout> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        page(page, wrapper);
        List<ProjWallPilatesAutoWorkout> recordList = page.getRecords();
        List<ProjWallPilatesAutoWorkoutListVO> resultRecords = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(recordList)) {
            Set<Integer> taskIdSet = recordList.stream().map(ProjWallPilatesAutoWorkout::getProjYogaAutoWorkoutTaskId).collect(Collectors.toSet());
            Map<Integer, ProjYogaAutoWorkoutTask> taskMap = projYogaAutoWorkoutTaskService.listByIds(taskIdSet).stream()
                    .collect(Collectors.toMap(ProjYogaAutoWorkoutTask::getId,
                            Function.identity(),
                            (existing, replacement) -> replacement)
                    );
            for (ProjWallPilatesAutoWorkout record : recordList) {
                ProjWallPilatesAutoWorkoutListVO pageVO = new ProjWallPilatesAutoWorkoutListVO();
                BeanUtils.copyProperties(record, pageVO);
                pageVO.setDisplayUpdateStatus(ResUpdateStatusEnum.SingletonHolder.getStatusMap().get(pageVO.getUpdateStatus()));
                ProjYogaAutoWorkoutTask task = taskMap.get(record.getProjYogaAutoWorkoutTaskId());
                if (null != task) {
                    pageVO.setGenerateNum(task.getGenerateNum())
                            .setCreateUser(task.getCreateUser())
                            .setCleanUp(task.getCleanUp());
                }
                resultRecords.add(pageVO);
            }
        }
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), resultRecords);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void update(ProjWallPilatesAutoWorkoutUpdateReq workoutUpdateReq, Integer projId) {
        Integer id = workoutUpdateReq.getId();
        ProjWallPilatesAutoWorkout wallPilatesAutoWorkout = baseMapper.selectById(id);
        if (null == wallPilatesAutoWorkout) {
            throw new BizException("wallPilatesAutoWorkout not found");
        }
        ProjWallPilatesAutoWorkout workout = new ProjWallPilatesAutoWorkout();
        BeanUtils.copyProperties(workoutUpdateReq, workout);
        workout.setProjId(projId);
        List<Integer> videoIdList = workoutUpdateReq.getVideoIdList();
        List<ProjWallPilatesVideoBO> videoList = projWallPilatesVideoService.queryList(videoIdList);
        checkLeftRight(videoList);
        // 生成音视频文件
        ProjWallPilatesAutoWorkoutBO pilatesAutoWorkoutBO = new ProjWallPilatesAutoWorkoutBO();
        pilatesAutoWorkoutBO.setVideoList(videoList);
        projWallPilatesWorkoutFileHandler.generateFileWithMultiLanguage(Collections.singletonList(pilatesAutoWorkoutBO), projId);

        workout.setDuration(pilatesAutoWorkoutBO.getDuration())
                .setCalorie(pilatesAutoWorkoutBO.getCalorie())
                .setVideo2532Url(pilatesAutoWorkoutBO.getVideo2532Url())
                .setVideoM3u8Url(pilatesAutoWorkoutBO.getVideoM3u8Url())
                .setAudioShortJson(pilatesAutoWorkoutBO.getAudioShortJson())
                .setAudioLongJson(pilatesAutoWorkoutBO.getAudioLongJson());
        updateById(workout);
        // 保存多语言音频数据
        projYogaAutoWorkoutAudioI18nService.delete(workout.getId(), YogaAutoWorkoutTemplateEnum.WALL_PILATES);
        List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = pilatesAutoWorkoutBO.getMultiLanguageAudioShortJson().keySet()
                .stream()
                .map(language -> createWallPilatesProjYogaAutoWorkoutAudioI18n(workout.getId(), projId, language, pilatesAutoWorkoutBO))
                .collect(Collectors.toList());

        projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);
        // 视频关联关系保存
        projWallPilatesAutoWorkoutVideoRelationService.deleteByWallPilatesAutoWorkoutId(Collections.singleton(id));
        if (CollUtil.isEmpty(videoIdList)) {
            return;
        }
        List<ProjWallPilatesAutoWorkoutVideoRelation> videoRelationList = new ArrayList<>(videoIdList.size());
        videoIdList.forEach(item -> {
            ProjWallPilatesAutoWorkoutVideoRelation videoRelation = new ProjWallPilatesAutoWorkoutVideoRelation();
            videoRelation.setProjWallPilatesVideoId(item)
                    .setProjWallPilatesAutoWorkoutId(id);
            videoRelationList.add(videoRelation);
        });
        projWallPilatesAutoWorkoutVideoRelationService.saveBatch(videoRelationList);
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjWallPilatesAutoWorkout::getId, idList);
        update(new ProjWallPilatesAutoWorkout(), wrapper);
    }

    @Override
    public void checkLeftRight(List<ProjWallPilatesVideoBO> videoList) {
        // key：right video id,value:相同id的个数
        ProjWallPilatesVideoBO preLeftVideo = null;
        for (ProjWallPilatesVideoBO videoBO : videoList) {
            if (null != preLeftVideo && !Objects.equals(preLeftVideo.getLeftRightId(), videoBO.getId())) {
                throw new BizException(String.format("Left and right must be adjacent, left video name: %s", preLeftVideo.getName()));
            }
            if (PoseDirectionEnum.LEFT.getPoseDirection().equals(videoBO.getDirection())) {
                preLeftVideo = videoBO;
            } else {
                preLeftVideo = null;
            }
        }
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        Set<Integer> idSet = new HashSet<>(idList);
        List<ProjAutoWorkoutBasicInfoWorkoutRelation> workoutRelations = basicInfoWorkoutRelationService.listByPlanTypeAndWorkoutIds(WALL_PILATES, idSet);
        if (!org.springframework.util.CollectionUtils.isEmpty(workoutRelations)) {
            List<String> errorMessages = workoutRelations.stream().map(this::convertWorkoutBasicInfo2String).collect(Collectors.toList());
            throw new BizException(String.format("This workout cannot be disabled because it is used in the following images: \n%s", String.join("\n", errorMessages)));
        }
        LambdaUpdateWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWallPilatesAutoWorkout::getId, idList);
        this.update(new ProjWallPilatesAutoWorkout(), wrapper);
    }

    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        Set<Integer> idSet = new HashSet<>(idList);
        List<ProjAutoWorkoutBasicInfoWorkoutRelation> workoutRelations = basicInfoWorkoutRelationService.listByPlanTypeAndWorkoutIds(WALL_PILATES, idSet);
        if (!org.springframework.util.CollectionUtils.isEmpty(workoutRelations)) {
            List<String> errorMessages = workoutRelations.stream().map(this::convertWorkoutBasicInfo2String).collect(Collectors.toList());
            throw new BizException(String.format("This workout cannot be deleted because it is used in the following images: \n%s", String.join("\n", errorMessages)));
        }
        LambdaUpdateWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesAutoWorkout::getDelFlag, GlobalConstant.YES).in(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY).in(ProjWallPilatesAutoWorkout::getId, idList);
        this.update(new ProjWallPilatesAutoWorkout(), wrapper);
    }

    @Override
    public ProjWallPilatesAutoWorkoutDetailVO findDetailById(Integer id) {
        ProjWallPilatesAutoWorkout workout = baseMapper.selectById(id);
        if (null == workout) {
            return null;
        }
        ProjWallPilatesAutoWorkoutDetailVO detailVO = new ProjWallPilatesAutoWorkoutDetailVO();
        BeanUtils.copyProperties(workout, detailVO);
        List<ProjWallPilatesVideoListVO> videoList = projWallPilatesVideoService.findByWallPilatesAutoWorkoutId(id);
        detailVO.setVideoList(videoList);
        detailVO.setProjYogaAutoTemplateId(workout.getProjYogaAutoWorkoutTemplateId());
        return detailVO;
    }

    @Override
    public List<CountBO> findCountList(List<Integer> yogaAutoWorkoutTemplateIdList) {
        return baseMapper.findCount(yogaAutoWorkoutTemplateIdList);
    }

    @Override
    public void generate(ProjYogaAutoWorkoutTemplate template, ProjYogaAutoWorkoutTask projYogaAutoWorkoutTask) {
        String type = WALL_PILATES.getName();
        if (!type.equals(template.getType())) {
            String error = "template type illegal";
            ProjYogaAutoWorkoutTask task = new ProjYogaAutoWorkoutTask();
            task.setStatus(ProjYogaAutoWorkoutTaskStatusEnum.FAIL).setFailReason(error);
            projYogaAutoWorkoutTaskService.updateById(task);
            return;
        }
        try {
            ProjWallPilatesAutoWorkoutContextBO context = creatContext(template, projYogaAutoWorkoutTask);
            List<ProjWallPilatesAutoWorkoutBO> workoutBoList = new ArrayList<>();
            List<TargetEnum> targetList = Arrays.asList(TargetEnum.ABS_CORE, TargetEnum.UPPER_BODY, TargetEnum.LOWER_BODY);
            for (TargetEnum target : targetList) {
                List<PositionEnum> positionList = Arrays.asList(PositionEnum.LYING, PositionEnum.STANDING);
                for (PositionEnum position : positionList) {
                    for (int i = 0; i < projYogaAutoWorkoutTask.getGenerateNum(); i++) {
                        ProjWallPilatesAutoWorkoutBO workoutBO = generateWorkout(context, target, position);
                        workoutBoList.add(workoutBO);
                    }
                }
            }
            log.info("start generate file");

            workoutBoList = projWallPilatesWorkoutFileHandler.generateFileWithMultiLanguage(workoutBoList, template.getProjId());
            IProjWallPilatesAutoWorkoutService projWallPilatesAutoWorkoutService = SpringUtil.getBean(ProjWallPilatesAutoWorkoutServiceImpl.class);
            log.info("start doGenerateWorkout");
            projWallPilatesAutoWorkoutService.doGenerateWorkout(workoutBoList, context);
        } catch (Exception e) {
            log.error("generate wallPilatesAutoWorkout failed, templateId:{},taskId:{}, exception is", template.getId(), projYogaAutoWorkoutTask.getId(), e);
            ProjYogaAutoWorkoutTask generateTask = new ProjYogaAutoWorkoutTask();
            generateTask.setStatus(ProjYogaAutoWorkoutTaskStatusEnum.FAIL).setFailReason("failed reason ：" + StringUtils.substring(e.getMessage(), 0, 200)).setId(projYogaAutoWorkoutTask.getId());
            projYogaAutoWorkoutTaskService.updateById(generateTask);
            return;
        }
        ProjYogaAutoWorkoutTask generateTask = new ProjYogaAutoWorkoutTask();
        generateTask.setStatus(ProjYogaAutoWorkoutTaskStatusEnum.SUCCESS).setFailReason(GlobalConstant.EMPTY_STRING).setId(projYogaAutoWorkoutTask.getId());
        projYogaAutoWorkoutTaskService.updateById(generateTask);
    }

    @Override
    public PageRes<ProjWallPilatesVideoListVO> pageVideo(PageReq pageReq, Integer id) {
        return projWallPilatesVideoService.findPage(id, pageReq);
    }

    @Override
    public List<ProjWallPilatesAutoWorkout> query(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseModel::getId, idList);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public void updateFileBatch(final List<Integer> idList, final Integer projId) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        Set<Integer> idSet = new HashSet<>(idList);
        // 查出所有关联视频
        List<ProjWallPilatesVideoListVO> allRelationVideoList = projWallPilatesVideoService.findByWallPilatesAutoWorkoutIdSet(idSet);
        if (CollUtil.isEmpty(allRelationVideoList)) {
            return;
        }
        // 将workout修改为更新中
        LambdaUpdateWrapper<ProjWallPilatesAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BaseModel::getId, idSet)
                .set(ProjWallPilatesAutoWorkout::getUpdateStatus, ResUpdateStatusEnum.UPDATE.getStatus());
        int updateCount = baseMapper.update(new ProjWallPilatesAutoWorkout(), updateWrapper);
        if (updateCount <= GlobalConstant.ZERO) {
            return;
        }

        // 逐一重新生成，异步操作,使用预设线程池
        Executor executorService = applicationContext.getBean(OTHER_TASK_THREAD_POOL, Executor.class);
        List<Integer> allVideoIds = allRelationVideoList.stream()
                .map(ProjWallPilatesVideoListVO::getId)
                .collect(Collectors.toList());
        Map<Integer, ProjWallPilatesVideoBO> videoIdMap = projWallPilatesVideoService.queryList(allVideoIds).stream().collect(Collectors.toMap(ProjWallPilatesVideoBO::getId, Function.identity(), (existing, replacement) -> replacement));
        Map<Integer, List<ProjWallPilatesVideoListVO>> videoGroupByWorkoutId = allRelationVideoList.stream().collect(Collectors.groupingBy(ProjWallPilatesVideoListVO::getWorkoutId));

        // 批量重新生成所有workout
        idSet.stream().map(workoutId -> {
            ProjWallPilatesAutoWorkoutBO workoutBO = new ProjWallPilatesAutoWorkoutBO();
            workoutBO.setId(workoutId);
            List<ProjWallPilatesVideoBO> videoBOList = videoGroupByWorkoutId.get(workoutId).stream().map(ProjWallPilatesVideoListVO::getId).map(videoIdMap::get).collect(Collectors.toList());
            workoutBO.setVideoList(videoBOList);
            return workoutBO;
            // 每个workout独立生成并更新
        }).forEach(workoutBO -> executorService.execute(() -> applicationContext.getBean(ProjWallPilatesAutoWorkoutServiceImpl.class).generateFileAndUpdate(workoutBO, projId)));
    }

    @Transactional(rollbackFor = Throwable.class)
    public void generateFileAndUpdate(final ProjWallPilatesAutoWorkoutBO workoutBO, Integer projId) {
        ProjWallPilatesAutoWorkout autoWorkout = new ProjWallPilatesAutoWorkout();
        autoWorkout.setId(workoutBO.getId());
        try {
            // 生成文件
            projWallPilatesWorkoutFileHandler.generateFileWithMultiLanguage(Lists.newArrayList(workoutBO), projId).get(0);
            // 更新workout
            autoWorkout
                    .setCalorie(workoutBO.getCalorie())
                    .setDuration(workoutBO.getDuration())
                    .setVideo2532Url(workoutBO.getVideo2532Url())
                    .setVideoM3u8Url(workoutBO.getVideoM3u8Url())
                    .setAudioShortJson(workoutBO.getAudioShortJson())
                    .setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus())
                    .setAudioLongJson(workoutBO.getAudioLongJson());
            updateById(autoWorkout);
            // 更新多语言音频数据
            projYogaAutoWorkoutAudioI18nService.delete(workoutBO.getId(), YogaAutoWorkoutTemplateEnum.WALL_PILATES);
            // 保存多语言音频数据
            List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = workoutBO.getMultiLanguageAudioShortJson().keySet()
                    .stream()
                    .map(language ->
                            createWallPilatesProjYogaAutoWorkoutAudioI18n(workoutBO.getId(), projId, language, workoutBO))
                    .collect(Collectors.toList());

            projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);

        } catch (Exception ex) {
            log.warn("async update yoga auto workout failed, workout id is {}.", workoutBO.getId(), ex);
            autoWorkout.setUpdateStatus(ResUpdateStatusEnum.FAIL.getStatus());
            updateById(autoWorkout);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doGenerateWorkout(List<ProjWallPilatesAutoWorkoutBO> workoutList, ProjWallPilatesAutoWorkoutContextBO context) {
        ProjYogaAutoWorkoutTemplate template = context.getTemplate();
        Integer projId = template.getProjId();
        ProjYogaAutoWorkoutTask task = context.getProjYogaAutoWorkoutTask();
        Integer templateId = template.getId();
        Integer taskId = task.getId();
        if (GlobalConstant.YES == task.getCleanUp()) {
            deleteByTemplateId(templateId);
        }
        List<ProjWallPilatesAutoWorkout> wallPilatesAutoWorkoutList = new ArrayList<>();
        for (ProjWallPilatesAutoWorkoutBO wallPilatesWorkoutBO : workoutList) {
            ProjWallPilatesAutoWorkout autoWorkout = new ProjWallPilatesAutoWorkout();
            BeanUtils.copyProperties(wallPilatesWorkoutBO, autoWorkout);
            autoWorkout.setStatus(GlobalConstant.STATUS_DRAFT)
                    .setProjId(projId)
                    .setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus())
                    .setProjYogaAutoWorkoutTaskId(taskId)
                    .setProjYogaAutoWorkoutTemplateId(templateId);
            wallPilatesWorkoutBO.setWorkout(autoWorkout);
            wallPilatesAutoWorkoutList.add(autoWorkout);
        }
        // 保存workout数据
        saveBatch(wallPilatesAutoWorkoutList);
        // 保存多语言audio json 关联关系
        List<ProjYogaAutoWorkoutAudioI18n> allAudioI18n = workoutList.stream()
                .flatMap(workoutBO -> workoutBO.getMultiLanguageAudioShortJson().keySet()
                        .stream()
                        .map(language ->
                                createWallPilatesProjYogaAutoWorkoutAudioI18n(workoutBO.getWorkout().getId(), projId, language, workoutBO)))
                .collect(Collectors.toList());
        projYogaAutoWorkoutAudioI18nService.saveBatch(allAudioI18n);

        // 保存video 关联关系
        List<ProjWallPilatesAutoWorkoutVideoRelation> relationList = new ArrayList<>(GlobalConstant.THOUSAND);
        workoutList.forEach(workoutBO -> {

            ProjWallPilatesAutoWorkout workout = workoutBO.getWorkout();
            List<ProjWallPilatesVideoBO> videoList = workoutBO.getVideoList();
            for (ProjWallPilatesVideoBO videoBO : videoList) {
                ProjWallPilatesAutoWorkoutVideoRelation relation = new ProjWallPilatesAutoWorkoutVideoRelation();
                relation.setProjWallPilatesAutoWorkoutId(workout.getId())
                        .setProjYogaAutoWorkoutTemplateId(templateId)
                        .setProjWallPilatesVideoId(videoBO.getId());
                relationList.add(relation);
            }

        });
        projWallPilatesAutoWorkoutVideoRelationService.saveBatch(relationList);
    }

    private void deleteByTemplateId(Integer templateId) {
        LambdaQueryWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BaseModel::getId).eq(ProjWallPilatesAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateId).ne(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjWallPilatesAutoWorkout> workoutList = baseMapper.selectList(wrapper);
        Set<Integer> workoutIdSet = workoutList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        LambdaUpdateWrapper<ProjWallPilatesAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BaseModel::getId, workoutIdSet);
        baseMapper.delete(wrapper);
        projWallPilatesAutoWorkoutVideoRelationService.deleteByWallPilatesAutoWorkoutId(workoutIdSet);
    }

    @Override
    public ProjWallPilatesUploadFileUrlBO uploadFile(ProjWallPilatesUploadFileInfoBO uploadFileInfoBO) {

        // 合并视频，保存数据
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(uploadFileInfoBO.getTsTextMergeBO(), WALL_PILATES_WORKOUT_M3U8);
        String video2532Url = null;
        List<TsMergeBO> tsMerge2532BO = uploadFileInfoBO.getTsMerge2532BOList();
        if (CollectionUtils.isNotEmpty(tsMerge2532BO)) {
            UploadFileInfoRes video2532R2Info = fileService.uploadMergeTSForM3U8R2(tsMerge2532BO, WALL_PILATES_WORKOUT_M3U8);
            video2532Url = video2532R2Info.getFileRelativeUrl();
        }
        // 上传音频json
        UploadFileInfoRes shortAudioJson = fileService.uploadJsonR2(JacksonUtil.toJsonString(uploadFileInfoBO.getShortAudioList()), WALL_PILATES_WORKOUT_JSON);
        UploadFileInfoRes fullAudioJson = fileService.uploadJsonR2(JacksonUtil.toJsonString(uploadFileInfoBO.getLongAudioList()), WALL_PILATES_WORKOUT_JSON);
        ProjWallPilatesUploadFileUrlBO fileUrlBO = new ProjWallPilatesUploadFileUrlBO();
        fileUrlBO.setVideo2532Url(video2532Url)
                .setVideoM3u8Url(videoR2Info.getFileRelativeUrl())
                .setAudioShortJson(shortAudioJson.getFileRelativeUrl())
                .setAudioLongJson(fullAudioJson.getFileRelativeUrl());
        return fileUrlBO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(ProjWallPilatesAutoWorkoutAddReq workoutReq, Integer projId) {
        ProjWallPilatesAutoWorkout workout = new ProjWallPilatesAutoWorkout();
        BeanUtils.copyProperties(workoutReq, workout);
        workout.setProjId(projId);
        List<Integer> videoIdList = workoutReq.getVideoIdList();
        List<ProjWallPilatesVideoBO> videoList = projWallPilatesVideoService.queryList(videoIdList);
        checkLeftRight(videoList);
        // 生成多语言音视频文件
        ProjWallPilatesAutoWorkoutBO pilatesAutoWorkoutBO = new ProjWallPilatesAutoWorkoutBO();
        pilatesAutoWorkoutBO.setVideoList(videoList);
        projWallPilatesWorkoutFileHandler.generateFileWithMultiLanguage(Collections.singletonList(pilatesAutoWorkoutBO), projId);

        workout.setDuration(pilatesAutoWorkoutBO.getDuration())
                .setCalorie(pilatesAutoWorkoutBO.getCalorie())
                .setVideo2532Url(pilatesAutoWorkoutBO.getVideo2532Url())
                .setVideoM3u8Url(pilatesAutoWorkoutBO.getVideoM3u8Url())
                .setAudioShortJson(pilatesAutoWorkoutBO.getAudioShortJson())
                .setAudioLongJson(pilatesAutoWorkoutBO.getAudioLongJson())
                .setProjYogaAutoWorkoutTemplateId(workoutReq.getProjYogaAutoTemplateId());
        save(workout);
        // 保存多语言音频数据
        List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = pilatesAutoWorkoutBO.getMultiLanguageAudioShortJson().keySet()
                .stream()
                .map(language -> createWallPilatesProjYogaAutoWorkoutAudioI18n(workout.getId(), projId, language, pilatesAutoWorkoutBO))
                .collect(Collectors.toList());

        projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);
        // 保存关联关系
        if (CollUtil.isEmpty(videoIdList)) {
            return;
        }
        List<ProjWallPilatesAutoWorkoutVideoRelation> videoRelationList = new ArrayList<>(videoIdList.size());
        videoIdList.forEach(item -> {
            ProjWallPilatesAutoWorkoutVideoRelation videoRelation = new ProjWallPilatesAutoWorkoutVideoRelation();
            videoRelation.setProjWallPilatesVideoId(item)
                    .setProjWallPilatesAutoWorkoutId(workout.getId());
            videoRelationList.add(videoRelation);
        });
        projWallPilatesAutoWorkoutVideoRelationService.saveBatch(videoRelationList);
    }

    private static ProjYogaAutoWorkoutAudioI18n createWallPilatesProjYogaAutoWorkoutAudioI18n(Integer workoutId, Integer projId, String language, ProjWallPilatesAutoWorkoutBO pilatesAutoWorkoutBO) {
        return new ProjYogaAutoWorkoutAudioI18n().setWorkoutId(workoutId)
                .setProjId(projId)
                .setLanguage(language)
                .setWorkoutType(WALL_PILATES)
                .setAudioLongJsonUrl(pilatesAutoWorkoutBO.getMultiLanguageAudioLongJson().get(language))
                .setAudioShortJsonUrl(pilatesAutoWorkoutBO.getMultiLanguageAudioShortJson().get(language));
    }


    private ProjWallPilatesAutoWorkoutContextBO creatContext(ProjYogaAutoWorkoutTemplate
                                                                     template, ProjYogaAutoWorkoutTask projYogaAutoWorkoutTask) {
        ProjWallPilatesAutoWorkoutContextBO context = new ProjWallPilatesAutoWorkoutContextBO();
        List<ProjWallPilatesVideoBO> videoList = projWallPilatesVideoService.queryList();
        List<ProjWallPilatesVideoBO> avaiableVideoList = new ArrayList<>(videoList.size());
        Map<String, List<ProjWallPilatesVideoBO>> videoTargetMap = new HashMap<>();
        for (ProjWallPilatesVideoBO video : videoList) {
            String direction = video.getDirection();
            if (RIGHT.getPoseDirection().equals(direction)) {
                continue;
            }
            ProjWallPilatesVideoBO leftRightVideo = video.getLeftRightVideo();
            if (LEFT.getPoseDirection().equals(direction) && null == leftRightVideo) {
                continue;
            }
            String target = video.getTarget();
            List<String> targetList = Arrays.stream(target.split(GlobalConstant.COMMA)).collect(Collectors.toList());
            video.setTargetList(targetList);
            targetList.forEach(item -> {
                List<ProjWallPilatesVideoBO> targetVideoList = videoTargetMap.getOrDefault(item, new ArrayList<>());
                targetVideoList.add(video);
                videoTargetMap.put(item, targetVideoList);
            });
            avaiableVideoList.add(video);
        }
        // 按type进行分组
        Map<String, List<ProjWallPilatesVideoBO>> videoTypeMap = avaiableVideoList.stream().collect(Collectors.groupingBy(ProjWallPilatesVideoBO::getType));
        // video的type必须包含Warm Up/Cool Down/Main
        if (videoTypeMap.keySet().size() < 3) {
            throw new BizException("video type count not enough, must include Warm Up/Cool Down/Main");
        }

        Map<String, List<ProjWallPilatesVideoBO>> videoPositionMap = avaiableVideoList.stream().collect(Collectors.groupingBy(ProjWallPilatesVideoBO::getPosition));

        Map<String, List<ProjWallPilatesVideoBO>> videoDirectionMap = avaiableVideoList.stream().collect(Collectors.groupingBy(ProjWallPilatesVideoBO::getDirection));

        Map<String, List<ProjWallPilatesVideoBO>> targetMap = avaiableVideoList.stream().collect(Collectors.groupingBy(ProjWallPilatesVideoBO::getTarget));
        int targetLength = 3;
        if (targetMap.keySet().size() < targetLength) {
            throw new BizException("video target count not enough, must include all target");
        }
        context.setTemplate(template)
                .setProjYogaAutoWorkoutTask(projYogaAutoWorkoutTask)
                .setAllVideo(avaiableVideoList)
                .setVideoPositionMap(videoPositionMap)
                .setVideoTargetMap(videoTargetMap)
                .setVideoTypeMap(videoTypeMap)
                .setVideoDirectionMap(videoDirectionMap);
        return context;
    }


    private ProjWallPilatesAutoWorkoutBO generateWorkout(ProjWallPilatesAutoWorkoutContextBO context, TargetEnum
            target, PositionEnum position) {

        ProjWallPilatesAutoWorkoutBO autoWorkoutBO = new ProjWallPilatesAutoWorkoutBO();
        List<ProjWallPilatesVideoBO> projWorkoutResVideoList = new ArrayList<>();
        List<ProjWallPilatesVideoBO> warmUpVideoList = matchWarmUpOrCooldownVideoList(context, position, TypeEnum.WARM_UP);
        List<ProjWallPilatesVideoBO> cooldownVideoList = matchWarmUpOrCooldownVideoList(context, position, TypeEnum.COOL_DOWN);
        int matchedDuration = sumDuration(warmUpVideoList) + sumDuration(cooldownVideoList);
        List<ProjWallPilatesVideoBO> mainVideoList = null;
        int tryCount = 5;
        for (int i = 0; i <= tryCount; i++) {
            try {
                mainVideoList = matchMainVideoList(context, position, target, matchedDuration);
                break;
            } catch (Exception e) {
                if (i == tryCount) {
                    throw e;
                }
            }
        }

        projWorkoutResVideoList.addAll(warmUpVideoList);
        projWorkoutResVideoList.addAll(mainVideoList);
        projWorkoutResVideoList.addAll(cooldownVideoList);
        autoWorkoutBO.setTarget(target.getName())
                .setPosition(position.getName())
                .setVideoList(projWorkoutResVideoList);
        return autoWorkoutBO;
    }

    private Integer sumDuration(List<ProjWallPilatesVideoBO> videoList) {
        int duration = 0;
        for (ProjWallPilatesVideoBO video : videoList) {
            duration += video.getFrontVideoDuration() * 3 + video.getSideVideoDuration();
        }
        return duration;
    }


    private String convertWorkoutBasicInfo2String(ProjAutoWorkoutBasicInfoWorkoutRelation workoutRelation) {
        return String.format("%s(%d)", workoutRelation.getProjAutoWorkoutBasicInfoName(), workoutRelation.getProjAutoWorkoutBasicInfoId());
    }

    private List<ProjWallPilatesVideoBO> matchMainVideoList(ProjWallPilatesAutoWorkoutContextBO
                                                                    context, PositionEnum position, TargetEnum target, Integer matchedDuration) {
        List<ProjWallPilatesVideoBO> videoList = getVideoList(context, position, target, TypeEnum.MAIN);
        Collections.shuffle(videoList);
        List<ProjWallPilatesVideoBO> singleTargetVideoList = videoList.stream().filter(item -> item.getTargetList().size() == GlobalConstant.ONE).collect(Collectors.toList());
        List<ProjWallPilatesVideoBO> sortedVideoList = new ArrayList<>(videoList.size());
        sortedVideoList.addAll(singleTargetVideoList);
        videoList.removeAll(sortedVideoList);
        sortedVideoList.addAll(videoList);

        List<ProjWallPilatesVideoBO> otherVideoList = getVideoList(context, position, null, TypeEnum.MAIN);

        otherVideoList.removeAll(sortedVideoList);

        Collections.shuffle(otherVideoList);
        ProjYogaAutoWorkoutTemplate template = context.getTemplate();
        int minTime = template.getMinTime() * 60 * 1000 - matchedDuration;
        int maxTime = template.getMaxTime() * 60 * 1000 - matchedDuration;
        List<List<ProjWallPilatesVideoBO>> availableVideoList = new ArrayList<>();
        List<ProjWallPilatesVideoBO> machedVideoList = new ArrayList<>();
        int duration = 0;

        // count偏移量，sorted匹配上一个+1，otherVideoList匹配上一个-1
        int countOffset = 0;
        int maxCountOffset = 9;
        int minCountOffset = -2;
        int maxCycleCount = sortedVideoList.size() * GlobalConstant.THREE;
        boolean isBreak = false;
        while (!isBreak && maxCycleCount > GlobalConstant.ZERO) {
            maxCycleCount--;
            if (countOffset < maxCountOffset && CollUtil.isNotEmpty(sortedVideoList)) {
                ProjWallPilatesVideoBO video = sortedVideoList.remove(GlobalConstant.ZERO);
                String direction = video.getDirection();
                machedVideoList.add(video);
                if (LEFT.getPoseDirection().equals(direction)) {
                    countOffset += GlobalConstant.TWO;
                    duration += sumDuration(Arrays.asList(video, video.getLeftRightVideo()));
                } else {
                    countOffset += GlobalConstant.ONE;
                    duration += sumDuration(Collections.singletonList(video));
                }
            }

            if (countOffset > minCountOffset && CollUtil.isNotEmpty(otherVideoList)) {
                ProjWallPilatesVideoBO video = otherVideoList.remove(GlobalConstant.ZERO);
                String direction = video.getDirection();
                machedVideoList.add(video);
                if (LEFT.getPoseDirection().equals(direction)) {
                    countOffset -= GlobalConstant.TWO;
                    duration += sumDuration(Arrays.asList(video, video.getLeftRightVideo()));
                } else {
                    countOffset -= GlobalConstant.ONE;
                    duration += sumDuration(Collections.singletonList(video));
                }
            }

            if (duration >= minTime && duration < maxTime && countOffset <= maxCountOffset && countOffset >= minCountOffset) {
                availableVideoList.add(new ArrayList<>(machedVideoList));
            }
            if (duration >= maxTime) {
                isBreak = true;
            }
        }
        if (CollUtil.isEmpty(availableVideoList)) {
            throw new BizException("matchVideoByDuration failed, target: " + target + ",  videoType: " + TypeEnum.MAIN + ",  matchedDuration: " + matchedDuration);
        }
        Collections.shuffle(availableVideoList);
        List<ProjWallPilatesVideoBO> wallPilatesVideoList = availableVideoList.get(GlobalConstant.ZERO);
        List<ProjWallPilatesVideoBO> finalVideoList = new ArrayList<>();
        List<ProjWallPilatesVideoBO> containTargetVideoList = new ArrayList<>();
        List<ProjWallPilatesVideoBO> otherTargetVideoList = new ArrayList<>();
        for (ProjWallPilatesVideoBO video : wallPilatesVideoList) {
            List<String> targetList = video.getTargetList();
            if (targetList.contains(target.getName())) {
                if (targetList.size() == GlobalConstant.ONE) {
                    finalVideoList.add(video);
                    if (LEFT.getPoseDirection().equals(video.getDirection())) {
                        finalVideoList.add(video.getLeftRightVideo());
                    }
                } else {
                    containTargetVideoList.add(video);
                    if (LEFT.getPoseDirection().equals(video.getDirection())) {
                        containTargetVideoList.add(video.getLeftRightVideo());
                    }
                }
            } else {
                otherTargetVideoList.add(video);
                if (LEFT.getPoseDirection().equals(video.getDirection())) {
                    otherTargetVideoList.add(video.getLeftRightVideo());
                }
            }
        }
        finalVideoList.addAll(containTargetVideoList);
        finalVideoList.addAll(otherTargetVideoList);
        return finalVideoList;
    }

    private List<ProjWallPilatesVideoBO> matchWarmUpOrCooldownVideoList(ProjWallPilatesAutoWorkoutContextBO
                                                                                context, PositionEnum position, TypeEnum videoType) {
        List<ProjWallPilatesVideoBO> videoList = getVideoList(context, position, null, videoType);
        Map<String, List<ProjWallPilatesVideoBO>> videoDirectionMap = videoList.stream().collect(Collectors.groupingBy(ProjWallPilatesVideoBO::getDirection));
        List<ProjWallPilatesVideoBO> videoLeftList = videoDirectionMap.getOrDefault(LEFT.getPoseDirection(), new ArrayList<>());
        List<ProjWallPilatesVideoBO> videoCentralList = videoDirectionMap.getOrDefault(CENTRAL.getPoseDirection(), new ArrayList<>());
        ProjYogaAutoWorkoutTemplate template = context.getTemplate();
        Integer count;
        if (TypeEnum.WARM_UP == videoType) {
            count = template.getWarmUpCount();
        } else {
            count = template.getCoolDownCount();
        }
        if (count <= 0 || (CollectionUtils.isEmpty(videoList))) {
            throw new BizException("generate warm up or cool down or main failed");
        }

        Collections.shuffle(videoList);
        int num = 20;
        int maxCyclesCount = count * num;
        List<ProjWallPilatesVideoBO> matchedVideoList = new ArrayList<>();
        while (maxCyclesCount > 0 && matchedVideoList.size() < count) {
            maxCyclesCount--;
            int remainingCount = count - matchedVideoList.size();
            if ((remainingCount >= GlobalConstant.TWO)) {
                if (NumberUtil.isEven(remainingCount) && videoCentralList.size() == GlobalConstant.ONE) {
                    ProjWallPilatesVideoBO videoBO = videoLeftList.remove(GlobalConstant.ZERO);
                    matchedVideoList.add(videoBO);
                    matchedVideoList.add(videoBO.getLeftRightVideo());
                } else {
                    ProjWallPilatesVideoBO videoBO = videoList.remove(GlobalConstant.ZERO);
                    videoCentralList.remove(videoBO);
                    videoLeftList.remove(videoBO);
                    matchedVideoList.add(videoBO);
                    if (LEFT.getPoseDirection().equals(videoBO.getDirection())) {
                        matchedVideoList.add(videoBO.getLeftRightVideo());
                    }
                }

            } else {
                ProjWallPilatesVideoBO videoBO = videoCentralList.remove(GlobalConstant.ZERO);
                matchedVideoList.add(videoBO);
            }
        }
        if (matchedVideoList.size() != count) {
            log.error("generate warm up or cool down or main failed,match size not equals rule count");
            throw new BizException("generate warm up or cool down or main failed");
        }
        return matchedVideoList;
    }

    private List<ProjWallPilatesVideoBO> getVideoList(ProjWallPilatesAutoWorkoutContextBO context, PositionEnum
            position, TargetEnum target, TypeEnum videoType) {
        if (null == videoType) {
            throw new BizException("videoType not can be null");
        }
        List<ProjWallPilatesVideoBO> videoList = context.getVideoTypeMap().get(videoType.getValue());
        List<ProjWallPilatesVideoBO> videoListCopy = new ArrayList<>(videoList);
        if (null != position) {
            Map<String, List<ProjWallPilatesVideoBO>> positionMap = context.getVideoPositionMap();
            videoListCopy.retainAll(positionMap.getOrDefault(position.getName(), new ArrayList<>()));
        }
        if (null != target) {
            Map<String, List<ProjWallPilatesVideoBO>> targetMap = context.getVideoTargetMap();
            videoListCopy.retainAll(targetMap.getOrDefault(target.getName(), new ArrayList<>()));
        }
        return videoListCopy;
    }

}
