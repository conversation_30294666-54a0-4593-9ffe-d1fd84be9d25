package com.laien.web.biz.proj.oog200.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseGroup;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseGroupListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj yoga pose grouping Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface ProjYogaPoseGroupMapper extends BaseMapper<ProjYogaPoseGroup> {


    List<CountBO> findCount(@Param("status") Integer status, @Param("poseLevelIdSet") Set<Integer> poseLevelIdSet);

    List<ProjYogaPoseGroupListVO> findByPoseLevelId(@Param("poseLevelId") Integer poseLevelId);
}
