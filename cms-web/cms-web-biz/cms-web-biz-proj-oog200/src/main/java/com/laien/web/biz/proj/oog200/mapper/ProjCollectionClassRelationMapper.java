package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionClassRelation;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoStatusCountVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoVO;

import java.util.List;

/**
 * <p>
 * collection class relation Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface ProjCollectionClassRelationMapper extends BaseMapper<ProjCollectionClassRelation> {

    /**
     * 根据collectionClassId查询CollectionClassVideo
     *
     * @param collectionClassId collectionClassId
     * @return list
     */
    List<ProjCollectionClassVideoVO> selectClassesByCollectionId(Integer collectionClassId);

    /**
     * 查询collection class 各个状态数量
     *
     * @return list
     */
    List<ProjCollectionClassVideoStatusCountVO> selectCollectionClassStatusCount();
}
