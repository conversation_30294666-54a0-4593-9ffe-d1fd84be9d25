package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * oog200 workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_program_type")
@ApiModel(value="ProjYogaProgramType", description="oog200 workout")
public class ProjYogaProgramType extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "type code，小于1000的预留给python用")
    private Integer code;


}
