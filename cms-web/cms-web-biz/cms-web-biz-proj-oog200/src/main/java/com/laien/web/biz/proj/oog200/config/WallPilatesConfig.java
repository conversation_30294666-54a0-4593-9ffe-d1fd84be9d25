package com.laien.web.biz.proj.oog200.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Configuration
@ConfigurationProperties(prefix = "cms.biz.oog200.wall-pilates")
@Data
public class WallPilatesConfig {

    @ApiModelProperty(value = "first")
    private String first;

    @ApiModelProperty(value = "next")
    private String next;

    @ApiModelProperty(value = "last")
    private String last;

    @ApiModelProperty(value = "start")
    private String start;

    @ApiModelProperty(value = "rest")
    private String rest;

}
