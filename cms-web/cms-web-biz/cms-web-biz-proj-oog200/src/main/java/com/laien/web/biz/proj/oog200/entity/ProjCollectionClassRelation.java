package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * collection class relation
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCollectionClassRelation对象", description="collection class relation")
public class ProjCollectionClassRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "collection id")
    private Integer collectionClassId;

    @ApiModelProperty(value = "class id")
    private Integer videoClassId;


}
