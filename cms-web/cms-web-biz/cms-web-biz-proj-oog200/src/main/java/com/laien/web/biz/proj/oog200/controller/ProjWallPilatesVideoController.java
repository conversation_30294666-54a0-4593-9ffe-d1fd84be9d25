package com.laien.web.biz.proj.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesVideo;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesVideoResourceService;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesVideoService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 瑜伽视频 前端控制器ProjWallPilatesAutoWorkoutController.java
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Api(tags = "项目管理: wall pilates video")
@RestController
@RequestMapping("/proj/wallPilatesVideo")
public class ProjWallPilatesVideoController extends ResponseController {

    @Resource
    private IProjWallPilatesVideoService projWallPilatesVideoService;
    @Resource
    private IProjWallPilatesVideoResourceService projWallPilatesVideoResourceService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjWallPilatesVideoListVO>> page(ProjWallPilatesVideoPageReq pageReq) {
        PageRes<ProjWallPilatesVideoListVO> pageRes = projWallPilatesVideoService.page(pageReq, RequestContextUtils.getProjectId());
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjWallPilatesVideoAddReq videoReq) {
        projWallPilatesVideoService.save(videoReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjWallPilatesVideoUpdateReq videoReq) {
        projWallPilatesVideoService.update(videoReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjWallPilatesVideoDetailVO> detail(@PathVariable Integer id) {
        ProjWallPilatesVideoDetailVO detailVO = projWallPilatesVideoService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        List<Integer> failedIdList = projWallPilatesVideoService.updateEnableByIds(idList);
        if (CollUtil.isNotEmpty(failedIdList)) {
            String ids = StrUtil.join(GlobalConstant.COMMA, failedIdList);
            return fail("The following data(id: " + ids +") has an incorrect value or status");
        }
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list can not be empty");
        }
        projWallPilatesVideoService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list can not be empty");
        }
        projWallPilatesVideoService.deleteByIds(idList);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(projWallPilatesVideoService.importByExcel(excel.getInputStream(), RequestContextUtils.getProjectId()));
    }

}
