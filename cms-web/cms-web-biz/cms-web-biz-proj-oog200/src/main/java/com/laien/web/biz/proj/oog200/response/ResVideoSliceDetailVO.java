package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video slice 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video slice 详情", description = "video slice 详情")
public class ResVideoSliceDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频类型")
    private String videoType;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "focus")
    private String[] focusArr;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "机位1视频地址")
    private String video1Url;

    @ApiModelProperty(value = "机位1视频开始机位")
    private String video1StartStand;

    @ApiModelProperty(value = "机位1视频结束机位")
    private String video1EndStand;

    @ApiModelProperty(value = "机位1视频时长")
    private Integer video1Duration;

    @ApiModelProperty(value = "机位1视频地址")
    private String video2Url;

    @ApiModelProperty(value = "机位1视频开始机位")
    private String video2StartStand;

    @ApiModelProperty(value = "机位1视频结束机位")
    private String video2EndStand;

    @ApiModelProperty(value = "机位1视频时长")
    private Integer video2Duration;

    @ApiModelProperty(value = "多语言列表")
    private List<ResVideoSliceI18nVO> i18nList;

    @ApiModelProperty(value = "状态")
    private Integer status;

}
