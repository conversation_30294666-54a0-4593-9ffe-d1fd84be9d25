package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionClassRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjCollectionClassRelationMapper;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoStatusCountVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoVO;
import com.laien.web.biz.proj.oog200.service.IProjCollectionClassRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * collection class relation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Service
public class ProjCollectionClassRelationServiceImpl extends ServiceImpl<ProjCollectionClassRelationMapper, ProjCollectionClassRelation> implements IProjCollectionClassRelationService {

    @Override
    public List<ProjCollectionClassVideoVO> selectClassesByCollectionId(Integer collectionClassId) {
        return this.baseMapper.selectClassesByCollectionId(collectionClassId);
    }

    @Override
    public List<ProjCollectionClassVideoStatusCountVO> selectCollectionClassStatusCount() {
        return this.baseMapper.selectCollectionClassStatusCount();
    }
}
