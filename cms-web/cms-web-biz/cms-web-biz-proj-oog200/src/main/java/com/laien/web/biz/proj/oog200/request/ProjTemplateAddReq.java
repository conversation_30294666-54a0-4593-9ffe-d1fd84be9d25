package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: template 增加
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template 增加", description = "template 增加")
public class ProjTemplateAddReq {

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "多语言列表")
    private String[] languageArr;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "规则列表")
    private List<ProjTemplateRuleReq> ruleList;

}
