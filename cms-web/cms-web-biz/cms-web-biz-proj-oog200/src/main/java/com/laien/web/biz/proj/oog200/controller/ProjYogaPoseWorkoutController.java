package com.laien.web.biz.proj.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseWorkout;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutListVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj yoga pose workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Api(tags = "项目管理:Pose Library Workout")
@RestController
@RequestMapping("/proj/yogaPoseWorkout")
public class ProjYogaPoseWorkoutController extends ResponseController {

    @Resource
    private IProjYogaPoseWorkoutService projYogaPoseWorkoutService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaPoseWorkoutListVO>> page(ProjYogaPoseWorkoutPageReq pageReq) {
        PageRes<ProjYogaPoseWorkoutListVO> pageRes = projYogaPoseWorkoutService.page(pageReq,RequestContextUtils.getProjectId());
        return succ(pageRes);
    }


    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaPoseWorkoutUpdateReq yogaPoseWorkoutReq) {
        projYogaPoseWorkoutService.update(yogaPoseWorkoutReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaPoseWorkoutDetailVO> detail(@PathVariable Integer id) {
        ProjYogaPoseWorkoutDetailVO detailVO = projYogaPoseWorkoutService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseWorkoutService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseWorkoutService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaPoseWorkoutService.deleteByIdList(idList);
        return succ();
    }

    @ApiOperation(value = "自动生成 pose workout ", notes = "异步生成")
    @PostMapping("/autoGenerate")
    public ResponseResult<Void> autoGenerate() {

        Integer projectId = RequestContextUtils.getProjectId();
        boolean result = projYogaPoseWorkoutService.autoGenerateWorkout(projectId);
        if (result) {
            return succ();
        }
        return fail("No pose video available.");
    }

    @ApiOperation(value = "批量更新workout 音视频资源", notes = "异步更新")
    @PostMapping("/updateBatch")
    public ResponseResult<Void> updateBatch(@RequestBody IdListReq idListReq) {

        projYogaPoseWorkoutService.updateRes4Workout(idListReq,RequestContextUtils.getProjectId());
        return succ();
    }


    @ApiOperation(value = "下载workout excel")
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        List<ProjYogaPoseWorkoutDownloadVO> workoutDownloadList = projYogaPoseWorkoutService.workoutDownloadList();
        if(CollUtil.isEmpty(workoutDownloadList)){
            return;
        }
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''yogaPoseWorkout.xlsx";
        String sheetName = "yogaPoseWorkout";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ProjYogaPoseWorkoutDownloadVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(workoutDownloadList);
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(projYogaPoseWorkoutService.importByExcel(excel.getInputStream()));
    }
}
