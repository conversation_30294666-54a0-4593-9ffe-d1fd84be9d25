package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.oog200.enums.ProjYogaAutoWorkoutTaskStatusEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Yoga auto workout生成任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjYogaAutoWorkoutTask对象", description = "Yoga auto workout生成任务")
public class ProjYogaAutoWorkoutTask extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模版id")
    private Integer projYogaAutoWorkoutTemplateId;

    @ApiModelProperty(value = "生成的数量，如果是10，代表Beginner、intermediate各10")
    private Integer generateNum;

    @ApiModelProperty(value = "理想生成时长，单位分钟")
    private Integer expectTime;

    @ApiModelProperty(value = "是否清空之前的workout 1是 0否")
    private Integer cleanUp;

    @ApiModelProperty(value = "任务状态 0进行中 1失败 2成功")
    private ProjYogaAutoWorkoutTaskStatusEnum status;

    @ApiModelProperty(value = "失败原因")
    private String failReason;


}
