package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: collection class video 统计
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="collection class video 统计")
public class ProjCollectionClassVideoStatusCountVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "count")
    private Integer count;

}
