package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.web.biz.proj.oog200.request.DailyDishDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/31 16:45
 */
@Data
public class ProjMealPlanDetailVO {

    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "标签值，以英文逗号做分隔，如A,B,C")
    private String keywords;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "用于APP端，显示一个固定的标签")
    private Boolean replacementTag;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "daily dish detail")
    private List<DailyDishDetailVO> dailyDishList = Collections.emptyList();

}
