package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjTemplateTask;

import java.util.List;

/**
 * <p>
 * template task 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
public interface IProjTemplateTaskService extends IService<ProjTemplateTask> {

    /**
     * 开启一个template 任务
     *
     * @param templateId templateId
     * @param generateCount generateCount
     * @param cleanUp cleanUp
     * @return ProjTemplateTask
     */
    ProjTemplateTask saveStartTemplateTask(Integer templateId, Integer generateCount, Integer cleanUp);

    /**
     * 根据id修改任务状态
     *
     * @param id id
     * @param taskStatus taskStatus
     */
    void updateTaskStatusById(Integer id, Integer taskStatus);

    /**
     * 查询最后一次发布信息
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<ProjTemplateTask> selectLastTask(List<Integer> templateIdList);

}
