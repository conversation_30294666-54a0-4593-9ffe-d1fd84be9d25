package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutVideoDetailVO;

import java.util.List;

/**
 * <p>
 * oog200 regular workout 关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface ProjYogaRegularWorkoutVideoRelationMapper extends BaseMapper<ProjYogaRegularWorkoutVideoRelation> {

    /**
     * 查询regular workout 关联的video 列表
     *
     * @param workoutId workoutId
     * @return list
     */
    List<ProjYogaRegularWorkoutVideoDetailVO> selectYogaRegularWorkoutVideos(Integer workoutId);

}
