package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesAutoWorkout;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Wall pilates auto workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface ProjWallPilatesAutoWorkoutMapper extends BaseMapper<ProjWallPilatesAutoWorkout> {

    List<CountBO> findCount(@Param("yogaAutoWorkoutTemplateIdList") List<Integer> yogaAutoWorkoutTemplateIdList);
}
