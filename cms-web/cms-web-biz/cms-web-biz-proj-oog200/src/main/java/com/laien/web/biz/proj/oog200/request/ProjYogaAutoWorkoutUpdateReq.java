package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ProjYogaAutoWorkoutUpdateReq", description = "ProjYogaAutoWorkoutUpdateReq")
public class ProjYogaAutoWorkoutUpdateReq extends ProjYogaAutoWorkoutAddReq{

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
