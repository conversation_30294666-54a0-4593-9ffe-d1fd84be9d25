package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @author: hhl
 * @date: 2025/6/10
 */
@Slf4j
@Data
public class ProjYogaAwardImportReq {

    @NotEmpty(message = "Product Id cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Product Id", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "产品Id")
    private String productId;

    @NotEmpty(message = "Award Link cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Award Link", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "兑换链接")
    private String awardLink;

    @NotNull(message = "Expired Time cannot be null", groups = Group1.class)
    @ExcelProperty(value = "Expired Time(yyyy-MM-dd HH:mm:ss)")
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "兑换截止时间", notes = "时间格式为 yyyy-MM-dd HH:mm:ss")
    private String expiredTime;

    @Min(value = 1, message = "Duration cannot less than 1")
    @ExcelProperty(value = "Duration(month)")
    @ApiModelProperty(value = "持续时间，以月为单位")
    private Integer duration;

}
