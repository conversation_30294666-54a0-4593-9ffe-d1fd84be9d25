package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/11/4 17:17
 */
@Data
@ApiModel(value = "YogaRegularWorkout新增", description = "YogaRegularWorkout新增")
public class ProjChairYogaRegularWorkoutUpdateReq extends ProjChairYogaRegularWorkoutAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
