package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * note: YogaRegularWorkout详情
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "YogaRegularWorkout详情", description = "YogaRegularWorkout详情")
public class ProjWallPilatesRegularWorkoutDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "语种")
    private String[] languageArr;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "video的 2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json1")
    private String audioLongJson;

    @ApiModelProperty(value = "音频json2")
    private String audioShortJson;

    @ApiModelProperty(value = "video list")
    private List<ProjWallPilatesVideoListVO> videoList;

    @ApiModelProperty(value = "特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String[] specialLimitArr;

    @ApiModelProperty(value = "取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other")
    private String[] yogaTypeArr;


    @ApiModelProperty(value = "选中的category")
    private List<Integer> categoryIdList;

    @ApiModelProperty(value = "yogaDataSourceList")
    private List<YogaDataSourceEnum> yogaDataSourceList;

}
