package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: template 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template 详情", description = "template 详情")
public class ProjTemplateDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "多语言列表")
    private String[] languageArr;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "规则列表")
    private List<ProjTemplateRuleVO> ruleList;

}
