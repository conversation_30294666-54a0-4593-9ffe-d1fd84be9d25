package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * video generate relation
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjVideoGenerateRelation对象", description="video generate relation")
public class ProjVideoGenerateRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "template_id")
    private Integer templateId;

    @ApiModelProperty(value = "task id")
    private Integer taskId;

    @ApiModelProperty(value = "generate id")
    private Integer generateId;

    @ApiModelProperty(value = "video id")
    private Integer videoId;


}
