package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.GoalEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: collection class 列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collection class 列表", description = "collection class 列表")
public class ProjCollectionClassListReq {

    @ApiModelProperty(value = "类型")
    private YogaAutoWorkoutTemplateEnum type;

    @ApiModelProperty(value = "goal类型")
    private List<GoalEnum> goalList;

}
