package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * template rule
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjTemplateRule对象", description="template rule")
public class ProjTemplateRule extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "template id")
    private Integer templateId;

    @ApiModelProperty(value = "视频code")
    private String videoCode;

    @ApiModelProperty(value = "视频type")
    private String videoType;


}
