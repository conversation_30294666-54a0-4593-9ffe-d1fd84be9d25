package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjYogaQuote;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuoteAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuotePageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuoteUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaQuoteDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaQuoteService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * yoga名言警句 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Api(tags = "项目管理:yogaQuote")
@RestController
@RequestMapping("/proj/yogaQuote")
public class ProjYogaQuoteController extends ResponseController {

    @Resource
    private IProjYogaQuoteService projYogaQuoteService;

    @ApiOperation(value = "分页列表")
    @GetMapping( "/page")
    public ResponseResult<PageRes<ProjYogaQuoteDetailVO>> page(ProjYogaQuotePageReq pageReq) {
        return succ(projYogaQuoteService.page(pageReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaQuoteAddReq req) {
        projYogaQuoteService.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaQuoteUpdateReq req) {
        projYogaQuoteService.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaQuoteDetailVO> detail(@PathVariable Integer id) {
        ProjYogaQuoteDetailVO detailVO = projYogaQuoteService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaQuoteService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaQuoteService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projYogaQuoteService.deleteByIdList(idList);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(projYogaQuoteService.importByExcel(excel.getInputStream(), RequestContextUtils.getProjectId()));
    }
}
