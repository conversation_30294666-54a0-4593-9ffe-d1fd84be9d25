package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionClassRelation;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoStatusCountVO;
import com.laien.web.biz.proj.oog200.response.ProjCollectionClassVideoVO;

import java.util.List;

/**
 * <p>
 * collection class relation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface IProjCollectionClassRelationService extends IService<ProjCollectionClassRelation> {

    /**
     * 根据collectionClassId查询CollectionClassVideo
     *
     * @param collectionClassId collectionClassId
     * @return list
     */
    List<ProjCollectionClassVideoVO> selectClassesByCollectionId(Integer collectionClassId);

    /**
     * 查询collection class 各个状态数量
     *
     * @return list
     */
    List<ProjCollectionClassVideoStatusCountVO> selectCollectionClassStatusCount();

}
