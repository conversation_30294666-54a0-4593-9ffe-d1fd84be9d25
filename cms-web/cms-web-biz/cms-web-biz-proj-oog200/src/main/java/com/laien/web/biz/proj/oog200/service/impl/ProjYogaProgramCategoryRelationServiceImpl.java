package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgram;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramCategoryRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaProgramCategoryRelationMapper;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramCategoryRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/17 21:26
 */
@Slf4j
@Service
public class ProjYogaProgramCategoryRelationServiceImpl extends ServiceImpl<ProjYogaProgramCategoryRelationMapper, ProjYogaProgramCategoryRelation> implements IProjYogaProgramCategoryRelationService{

    @Resource
    private IProjYogaProgramService projYogaProgramService;

    @Override
    public List<ProjYogaProgramPageVO> listWorkoutByProgramCategoryIds(Collection<Integer> programCategoryIds) {

        if (CollectionUtils.isEmpty(programCategoryIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaProgramCategoryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaProgramCategoryRelation::getProjYogaProgramCategoryId, programCategoryIds);

        List<ProjYogaProgramCategoryRelation> relationList = list(queryWrapper);
        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }

        List<Integer> programIds = relationList.stream().map(e -> e.getProjYogaProgramId()).collect(Collectors.toList());
        Collection<ProjYogaProgram> programList = projYogaProgramService.listByIds(programIds);
        if (CollectionUtils.isEmpty(programList)) {
            return Collections.emptyList();
        }

        return programList.stream().map(program -> convert2PageVO(program)).collect(Collectors.toList());
    }

    private ProjYogaProgramPageVO convert2PageVO(ProjYogaProgram yogaProgram) {

        ProjYogaProgramPageVO projYogaProgramPageVO = new ProjYogaProgramPageVO();
        BeanUtils.copyProperties(yogaProgram, projYogaProgramPageVO);
        return projYogaProgramPageVO;
    }

    @Override
    public void deleteByProgramCategoryId(Integer programCategoryId) {

        LambdaUpdateWrapper<ProjYogaProgramCategoryRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaProgramCategoryRelation::getProjYogaProgramCategoryId, programCategoryId);
        updateWrapper.set(ProjYogaProgramCategoryRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjYogaProgramCategoryRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjYogaProgramCategoryRelation> listByProgramCategoryId(Collection<Integer> programCategoryIds) {

        if (CollectionUtils.isEmpty(programCategoryIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaProgramCategoryRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaProgramCategoryRelation::getProjYogaProgramCategoryId, programCategoryIds);
        return list(queryWrapper);
    }
}
