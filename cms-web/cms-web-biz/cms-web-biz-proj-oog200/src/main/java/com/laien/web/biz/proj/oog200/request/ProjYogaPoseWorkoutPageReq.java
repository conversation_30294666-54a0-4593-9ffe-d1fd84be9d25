package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * note: workout116分页
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ProjYogaPoseWorkoutPageReq", description = "ProjYogaPoseWorkoutPageReq")
public class ProjYogaPoseWorkoutPageReq extends PageReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "难度Newbie, Beginner, Intermediate, Advanced")
    private String difficulty;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "Standing，Seated，Supine，Prone，Arm & Leg Support")
    private String position;

    @ApiModelProperty(value = "取值Flexibility、Balance、Strength、Relaxation")
    private String focus;

}
