package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjAllergen;
import com.laien.web.biz.proj.oog200.mapper.ProjAllergenMapper;
import com.laien.web.biz.proj.oog200.response.ProjAllergenVO;
import com.laien.web.biz.proj.oog200.service.IProjAllergenService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * allergen 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Service
public class ProjAllergenServiceImpl extends ServiceImpl<ProjAllergenMapper, ProjAllergen> implements IProjAllergenService {

    @Override
    public List<ProjAllergenVO> query(Integer projId) {
        LambdaQueryWrapper<ProjAllergen> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjAllergen::getProjId, projId)
                .eq(ProjAllergen::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjAllergen> allergenList = baseMapper.selectList(wrapper);
        List<ProjAllergenVO> allergenVOList = new ArrayList<>();
        if (CollUtil.isEmpty(allergenList)) {
            return allergenVOList;
        }
        for (ProjAllergen allergen : allergenList) {
            ProjAllergenVO allergenVO = new ProjAllergenVO();
            BeanUtils.copyProperties(allergen, allergenVO);
            allergenVOList.add(allergenVO);
        }
        return allergenVOList;
    }
}
