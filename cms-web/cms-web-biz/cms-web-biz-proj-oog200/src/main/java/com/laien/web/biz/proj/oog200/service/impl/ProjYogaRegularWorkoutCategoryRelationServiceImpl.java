package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutCategoryRelation;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaRegularWorkoutCategoryRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaRegularWorkoutCategoryRelationService;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * regular workout和category关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
public class ProjYogaRegularWorkoutCategoryRelationServiceImpl extends ServiceImpl<ProjYogaRegularWorkoutCategoryRelationMapper, ProjYogaRegularWorkoutCategoryRelation> implements IProjYogaRegularWorkoutCategoryRelationService {
    @Override
    public void delete(Integer workoutId, YogaAutoWorkoutTemplateEnum workoutType) {
        LambdaUpdateWrapper<ProjYogaRegularWorkoutCategoryRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper
                .eq(ProjYogaRegularWorkoutCategoryRelation::getWorkoutId, workoutId)
                .eq(ProjYogaRegularWorkoutCategoryRelation::getWorkoutType, workoutType);
        baseMapper.delete(wrapper);
    }

    @Override
    public List<ProjYogaRegularWorkoutCategoryRelation> query(List<Integer> workoutIdList, YogaAutoWorkoutTemplateEnum workoutType) {
        if (CollUtil.isEmpty(workoutIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjYogaRegularWorkoutCategoryRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjYogaRegularWorkoutCategoryRelation::getWorkoutId, workoutIdList)
                .eq(ProjYogaRegularWorkoutCategoryRelation::getWorkoutType, workoutType)
                .orderByAsc(BaseModel::getId);

        return baseMapper.selectList(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRelation(List<Integer> idList, YogaAutoWorkoutTemplateEnum workoutType, Integer workoutId, Integer projId) {
        delete(workoutId, workoutType);
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        List<ProjYogaRegularWorkoutCategoryRelation> relationList = new ArrayList<>();
        for (Integer categoryId : idList) {
            ProjYogaRegularWorkoutCategoryRelation categoryRelation = new ProjYogaRegularWorkoutCategoryRelation();
            categoryRelation
                    .setWorkoutId(workoutId)
                    .setProjYogaRegularCategoryId(categoryId)
                    .setWorkoutType(workoutType).setProjId(projId);
            relationList.add(categoryRelation);
        }
        saveBatch(relationList);
    }
}
