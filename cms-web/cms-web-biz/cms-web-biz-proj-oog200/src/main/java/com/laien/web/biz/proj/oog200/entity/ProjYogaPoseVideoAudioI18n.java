package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.entity.BaseUserAssignIdModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2024/7/31 10:28
 */
@Data
@Accessors(chain = true)
public class ProjYogaPoseVideoAudioI18n extends BaseUserAssignIdModel {

    @ApiModelProperty(value = "pose video 第一阶段 解说音频")
    private String firstGuidanceAudioUrlFemale;

    @ApiModelProperty(value = "pose video 第一阶段 解说音频时长")
    private Integer firstGuidanceAudioDurationFemale;

    @ApiModelProperty(value = "pose video 第二阶段 解说音频")
    private String secondGuidanceAudioUrlFemale;

    @ApiModelProperty(value = "pose video 第二阶段 解说音频时长")
    private Integer secondGuidanceAudioDurationFemale;

    @ApiModelProperty(value = "pose video 第三阶段 解说音频")
    private String thirdGuidanceAudioUrlFemale;

    @ApiModelProperty(value = "pose video 第三阶段 解说音频时长")
    private Integer thirdGuidanceAudioDurationFemale;

    @ApiModelProperty(value = "pose video 第四阶段 解说音频")
    private String fourthGuidanceAudioUrlFemale;

    @ApiModelProperty(value = "pose video 第四阶段 解说音频时长")
    private Integer fourthGuidanceAudioDurationFemale;

    @ApiModelProperty(value = "pose video 第五阶段 解说音频")
    private String fifthGuidanceAudioUrlFemale;

    @ApiModelProperty(value = "pose video 第五阶段 解说音频时长")
    private Integer fifthGuidanceAudioDurationFemale;

    @ApiModelProperty(value = "pose video 第六阶段 解说音频")
    private String sixthGuidanceAudioUrlFemale;

    @ApiModelProperty(value = "pose video 第六阶段 解说音频时长")
    private Integer sixthGuidanceAudioDurationFemale;

}
