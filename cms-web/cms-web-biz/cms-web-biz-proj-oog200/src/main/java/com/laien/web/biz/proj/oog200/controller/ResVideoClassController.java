package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ResVideoClass;
import com.laien.web.biz.proj.oog200.request.ResVideoClassAddReq;
import com.laien.web.biz.proj.oog200.request.ResVideoClassPageReq;
import com.laien.web.biz.proj.oog200.request.ResVideoClassUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResVideoClassDetailVO;
import com.laien.web.biz.proj.oog200.response.ResVideoClassPageVO;
import com.laien.web.biz.proj.oog200.service.IResVideoClassService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * res video class 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Api(tags = "资源管理:Video Class")
@RestController
@RequestMapping("/res/videoClass")
public class ResVideoClassController extends ResponseController {

    @Resource
    private IResVideoClassService resVideoClassService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResVideoClassPageVO>> page(ResVideoClassPageReq pageReq) {
        PageRes<ResVideoClassPageVO> pageRes = resVideoClassService.selectVideoClassPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResVideoClassAddReq videoClassAddReq) {
        resVideoClassService.saveVideoClass(videoClassAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResVideoClassUpdateReq videoClassUpdateReq) {
        resVideoClassService.updateVideoClass(videoClassUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResVideoClassDetailVO> detail(@PathVariable Integer id) {
        ResVideoClassDetailVO detailVO = resVideoClassService.getVideoClassDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resVideoClassService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resVideoClassService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resVideoClassService.deleteByIds(idList);
        return succ();
    }


}
