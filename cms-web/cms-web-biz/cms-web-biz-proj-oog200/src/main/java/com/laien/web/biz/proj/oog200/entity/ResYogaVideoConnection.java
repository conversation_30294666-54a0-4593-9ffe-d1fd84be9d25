package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 瑜伽视频 -关联链路表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResYogaVideoConnection对象", description="瑜伽视频 -关联链路表")
public class ResYogaVideoConnection extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前yoga video id")
    private Integer resYogaVideoId;

    @ApiModelProperty(value = "下一个yoga video id")
    private Integer resYogaVideoNextId;

    @ApiModelProperty(value = "过渡视频id")
    private Integer resTransitionId;


}
