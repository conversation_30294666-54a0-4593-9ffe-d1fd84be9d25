package com.laien.web.biz.proj.oog200.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesAutoWorkout;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="Wall pilates auto workout")
public class ProjWallPilatesAutoWorkoutBO {


    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "projYogaAutoWorkoutTemplateId")
    private Integer projYogaAutoWorkoutTemplateId;

    @ApiModelProperty(value = "projYogaAutoWorkoutTaskId")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "Standing、Lying")
    private String position;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "Video 的2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioShortJson;

    @ApiModelProperty(value = "videoList")
    List<ProjWallPilatesVideoBO> videoList;

    @ApiModelProperty(value = "workout")
    ProjWallPilatesAutoWorkout workout;

    /**  多语言音频LongJson 不包含默认语言 */
    private Map<String,String> multiLanguageAudioLongJson ;

    /**  多语言音频ShortJson 不包含默认语言 */
    private Map<String,String> multiLanguageAudioShortJson ;

}
