package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.biz.proj.oog200.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * note: video 生成规则
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video 生成规则", description = "video 生成规则")
public class VideoGenerateRulesBO {

    @ApiModelProperty(value = "模板")
    private ProjTemplate template;

    @ApiModelProperty(value = "任务")
    private ProjTemplateTask templateTask;

    @ApiModelProperty(value = "规则")
    private List<ProjTemplateRule> ruleList;

    @ApiModelProperty(value = "数据分组")
    private Map<String, List<ResVideoSlice>> videoSliceDataGroup;

    @ApiModelProperty(value = "数据配套的其他数据")
    private Map<Integer, List<ResVideoSlice>> videoSliceDataSuiteMap;

    @ApiModelProperty(value = "多语言数据")
    private Map<Integer, Map<String, ResVideoSliceI18n>> videoSliceI18nContainer;

    @ApiModelProperty(value = "系统音ready")
    private String soundReadyUrl;
    @ApiModelProperty(value = "系统音Begin")
    private String soundBeginUrl;
    @ApiModelProperty(value = "系统音next")
    private String soundNextUrl;
    @ApiModelProperty(value = "系统音Finish")
    private String soundFinishUrl;

}
