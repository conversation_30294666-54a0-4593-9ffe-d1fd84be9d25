package com.laien.web.biz.proj.oog200.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionTeacher;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherAddReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionTeacherVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 *     ProjCollectionTeacherMapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/28
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjCollectionTeacherMapStruct {

    List<ProjCollectionTeacherVO> toVOList(List<ProjCollectionTeacher> dishList);

    ProjCollectionTeacherVO toVO(ProjCollectionTeacher projCollectionTeacher);

    ProjCollectionTeacher toEntity(ProjCollectionTeacherAddReq req);
}
