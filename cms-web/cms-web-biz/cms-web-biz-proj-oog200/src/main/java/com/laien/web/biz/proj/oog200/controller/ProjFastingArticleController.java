package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjFastingArticle;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleAddReq;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleListReq;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjFastingArticleDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjFastingArticleListVO;
import com.laien.web.biz.proj.oog200.service.IProjFastingArticleService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * fasting article 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Api(tags = "项目管理:fasting article")
@RestController
@RequestMapping("/proj/fastingArticle")
public class ProjFastingArticleController extends ResponseController {

    @Resource
    private IProjFastingArticleService fastingArticleService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjFastingArticleListVO>> list(ProjFastingArticleListReq articleListReq) {
        List<ProjFastingArticleListVO> articleList = fastingArticleService.list(articleListReq, RequestContextUtils.getProjectId());
        return succ(articleList);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFastingArticleAddReq articleAddReq) {
        fastingArticleService.save(articleAddReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFastingArticleUpdateReq articleUpdateReq) {
        fastingArticleService.update(articleUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFastingArticleDetailVO> detail(@PathVariable Integer id) {
        ProjFastingArticleDetailVO detailVO = fastingArticleService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        fastingArticleService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        fastingArticleService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list can not be empty");
        }
        fastingArticleService.deleteByIds(idList);
        return succ();
    }


    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        fastingArticleService.sort(idListReq);
        return succ();
    }

}
