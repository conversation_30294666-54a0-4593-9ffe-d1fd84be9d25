package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjMealPlanRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjMealPlanRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjMealPlanRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/31 18:11
 */
@Slf4j
@Service
public class ProjMealPlanRelationServiceImpl extends ServiceImpl<ProjMealPlanRelationMapper, ProjMealPlanRelation> implements IProjMealPlanRelationService {

    @Override
    public void deleteByMealPlanId(Integer mealPlanId) {

        LambdaUpdateWrapper<ProjMealPlanRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjMealPlanRelation::getProjMealPlanId, mealPlanId);
        updateWrapper.set(ProjMealPlanRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjMealPlanRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjMealPlanRelation> listByMealPlanId(Integer mealPlanId) {

        LambdaQueryWrapper<ProjMealPlanRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjMealPlanRelation::getProjMealPlanId, mealPlanId);
        return list(queryWrapper);
    }

}
