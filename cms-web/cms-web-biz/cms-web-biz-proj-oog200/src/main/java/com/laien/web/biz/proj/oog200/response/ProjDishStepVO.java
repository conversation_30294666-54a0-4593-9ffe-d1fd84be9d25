package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * dish step
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="dish step对象", description="dish step")
public class ProjDishStepVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "tip list")
    private List<ProjDishStepTipVO> tipList;

}
