package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Author:  hhl
 * Date:  2024/8/12 11:39
 */
@Data
public class ProjYogaPoseConnectionImportReq {

    @NotEmpty(message = "Pose Library Video Name cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Pose Library Video Name", converter = StringStringTrimConverter.class)
    private String poseVideoName;

    @NotEmpty(message = "Next Name cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Next Name", converter = StringStringTrimConverter.class)
    private String poseNextVideoName;

    @NotEmpty(message = "Transition Name cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Transition Name", converter = StringStringTrimConverter.class)
    private String transitionName;

}
