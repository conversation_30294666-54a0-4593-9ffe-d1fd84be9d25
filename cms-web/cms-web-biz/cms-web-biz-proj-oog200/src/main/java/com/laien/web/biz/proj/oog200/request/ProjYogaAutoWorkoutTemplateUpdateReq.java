package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * note: template 修改
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjYogaAutoWorkoutTemplate 修改", description = "ProjYogaAutoWorkoutTemplate 修改")
public class ProjYogaAutoWorkoutTemplateUpdateReq extends ProjYogaAutoWorkoutTemplateAddReq {

    @ApiModelProperty(value = "数据id")
    @NotNull(message = "The id of the current data cannot be null", groups = Group1.class)
    private Integer id;

}
