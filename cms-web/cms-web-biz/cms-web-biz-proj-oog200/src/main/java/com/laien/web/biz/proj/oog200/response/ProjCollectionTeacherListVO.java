package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: collection class 列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collection teacher 列表", description = "collection teacher 列表")
public class ProjCollectionTeacherListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "avatar image url")
    private String avatarUrl;

    @ApiModelProperty(value = "teacher description")
    private String description;

}
