package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjCollectionTeacher;
import com.laien.web.biz.proj.oog200.mapper.ProjCollectionTeacherMapper;
import com.laien.web.biz.proj.oog200.mapstruct.ProjCollectionTeacherMapStruct;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherAddReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherListReq;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionTeacherVO;
import com.laien.web.biz.proj.oog200.service.IProjCollectionTeacherService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 教练表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProjCollectionTeacherServiceImpl extends ServiceImpl<ProjCollectionTeacherMapper, ProjCollectionTeacher> implements IProjCollectionTeacherService {

    private final ProjCollectionTeacherMapStruct mapStruct;

    private final IProjLmsI18nService projLmsI18nService;

    private final IProjInfoService projInfoService;


    /**
     * 新接口获取教练列表
     *
     * @param listReq
     * @param projId
     * @return
     */
    @Override
    public List<ProjCollectionTeacherVO> list(ProjCollectionTeacherListReq listReq, Integer projId) {
        LambdaQueryWrapper<ProjCollectionTeacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(listReq.getName()), ProjCollectionTeacher::getName, listReq.getName())
                .eq(ObjUtil.isNotNull(listReq.getStatus()), ProjCollectionTeacher::getStatus, listReq.getStatus())
                .eq(ProjCollectionTeacher::getProjId, projId)
                .orderByDesc(BaseModel::getId);
        return mapStruct.toVOList(baseMapper.selectList(wrapper));
    }

    /**
     * 添加一个教练
     *
     * @param collectionTeacherAddReq
     * @param projId
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjCollectionTeacherAddReq collectionTeacherAddReq, Integer projId) {
        if (StringUtils.isBlank(collectionTeacherAddReq.getName())) {
            String error = "name is null";
            throw new BizException(error);
        }
        check(null, collectionTeacherAddReq, projId);
        ProjCollectionTeacher collectionTeacher = mapStruct.toEntity(collectionTeacherAddReq);
        collectionTeacher.setProjId(projId);
        save(collectionTeacher);

        projLmsI18nService.handleI18n(Collections.singletonList(collectionTeacher), projInfoService.getById(RequestContextUtils.getProjectId()));
    }

    @Override
    public ProjCollectionTeacherVO findDetailById(Integer id) {
        return mapStruct.toVO(baseMapper.selectById(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjCollectionTeacherUpdateReq req, Integer projId) {
        Integer id = req.getId();
        this.check(id, req, projId);
        ProjCollectionTeacher collectionTeacher = mapStruct.toEntity(req);
        //mapStruct.toEntity映射addReq，无id字段
        collectionTeacher.setId(id);
        this.updateById(collectionTeacher);
        projLmsI18nService.handleI18n(Collections.singletonList(collectionTeacher), projInfoService.getById(RequestContextUtils.getProjectId()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjCollectionTeacher> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjCollectionTeacher::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjCollectionTeacher::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjCollectionTeacher::getId, idList);
        this.update(new ProjCollectionTeacher(), updateWrapper);
        return idList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjCollectionTeacher> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCollectionTeacher::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjCollectionTeacher::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjCollectionTeacher::getId, idList);
        this.update(new ProjCollectionTeacher(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {
        LambdaUpdateWrapper<ProjCollectionTeacher> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCollectionTeacher::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjCollectionTeacher::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjCollectionTeacher::getId, idList);
        this.update(new ProjCollectionTeacher(), wrapper);
    }

    private void check(Integer id, ProjCollectionTeacherAddReq req, Integer projId) {
        LambdaQueryWrapper<ProjCollectionTeacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjCollectionTeacher::getName, req.getName())
                .eq(ProjCollectionTeacher::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjCollectionTeacher> dishList = baseMapper.selectList(wrapper);
        Set<String> nameSet = dishList.stream().map(ProjCollectionTeacher::getName).collect(Collectors.toSet());
        if (nameSet.contains(req.getName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
    }
}
