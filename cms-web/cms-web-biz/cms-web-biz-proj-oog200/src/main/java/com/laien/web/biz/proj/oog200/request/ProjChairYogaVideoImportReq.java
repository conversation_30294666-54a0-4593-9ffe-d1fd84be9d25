package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2024/9/24 15:16
 */
@Data
public class ProjChairYogaVideoImportReq {

    @NotEmpty(message = "Name cannot be empty", groups = Group1.class)
    @Length(message = "The Video Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Name", converter = StringStringTrimConverter.class)
    private String name;

    @ExcelProperty(value = "core_voice_config_i18n_name",converter = StringStringTrimConverter.class)
    @NotEmpty(message = "coreVoiceConfigI18nId cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "翻译声音")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @NotEmpty(message = "ImageUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Image Url", converter = StringStringTrimConverter.class)
    private String imageUrl;

    @NotEmpty(message = "Type cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Type", converter = StringStringTrimConverter.class)
    private String type;

    @NotEmpty(message = "Target cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Body Target", converter = StringStringTrimConverter.class)
    private String target;

    @NotEmpty(message = "Position cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Position", converter = StringStringTrimConverter.class)
    private String position;

    @NotEmpty(message = "Direction cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Direction", converter = StringStringTrimConverter.class)
    private String direction;

    @ApiModelProperty(value = "名称音频")
    @NotEmpty(message = "Name Audio Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Name Audio", converter = StringStringTrimConverter.class)
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    @Min(value = 1, message = "The Name Audio Duration can't be less than 1.", groups = Group2.class)
    @NotNull(message = "Name Audio Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Name Audio Duration")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "解说音频")
//    @NotEmpty(message = "Guidance Audio Url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Guidance Audio", converter = StringStringTrimConverter.class)
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
//    @Min(value = 1, message = "The Guidance Audio Duration can't be less than 1.", groups = Group2.class)
//    @NotNull(message = "Guidance Audio Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Guidance Audio Duration")
    private Integer guidanceAudioDuration;

    @NotNull(message = "Calorie cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Calorie")
    @Min(message = "Calorie minimum value cannot be less than 0", value = 0, groups = Group2.class)
    private BigDecimal calorie;

}
