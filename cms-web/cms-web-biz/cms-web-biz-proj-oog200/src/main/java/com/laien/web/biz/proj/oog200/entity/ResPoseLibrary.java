package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * pose表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResPoseLibrary对象", description="pose表")
public class ResPoseLibrary extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称,最多100个字符")
    @TranslateField(type = TranslationTaskTypeEnums.TEXT)
    private String poseName;

    @ApiModelProperty(value = "梵文名称，最多100个字符")
    private String sanskritName;

    @ApiModelProperty(value = "pose图 url地址")
    private String animationUrl;

    @ApiModelProperty(value = "是否为basic 1是 0否")
    private Integer basic;

    @TranslateField(type = TranslationTaskTypeEnums.TEXT)
    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @TranslateField(type = TranslationTaskTypeEnums.TEXT)
    @ApiModelProperty(value = "position")
    private String position;

    @TranslateField(type = TranslationTaskTypeEnums.TEXT)
    @ApiModelProperty(value = "focus")
    private String focus;

    @ApiModelProperty(value = "简介，最多1000字符，去除前后空格")
    @TranslateField(type = TranslationTaskTypeEnums.TEXT)
    private String description;

    @ApiModelProperty(value = "第三方视频链接")
    private String videoLinkUrl;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
