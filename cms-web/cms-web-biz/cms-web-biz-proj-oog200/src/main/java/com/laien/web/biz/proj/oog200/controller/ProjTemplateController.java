package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjTemplate;
import com.laien.web.biz.proj.oog200.response.ProjTemplateDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjTemplatePageVO;
import com.laien.web.biz.proj.oog200.response.ProjVideoGeneratePageVO;
import com.laien.web.biz.proj.oog200.service.IProjTemplateService;
import com.laien.web.biz.proj.oog200.service.IProjVideoGenerateService;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * template 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Api(tags = "项目管理:Template")
@RestController
@RequestMapping("/proj/template")
public class ProjTemplateController extends ResponseController {

    @Resource
    private IProjTemplateService projTemplateService;

    @Resource
    private IProjVideoGenerateService projVideoGenerateService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjTemplatePageVO>> page(ProjTemplatePageReq pageReq) {
        PageRes<ProjTemplatePageVO> pageRes = projTemplateService.selectTemplatePage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjTemplateAddReq templateAddReq) {
        projTemplateService.saveTemplate(templateAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjTemplateUpdateReq templateUpdateReq) {
        projTemplateService.updateTemplate(templateUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjTemplateDetailVO> detail(@PathVariable Integer id) {
        ProjTemplateDetailVO detailVO = projTemplateService.getTemplateDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projTemplateService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projTemplateService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projTemplateService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "video 生成")
    @PostMapping("/generate/{id}")
    public ResponseResult<Void> generate(@PathVariable Integer id, @RequestBody ProjTemplateGenerateReq generateReq) {
        Integer generateCount = generateReq.getGenerateCount();
        if (Objects.isNull(generateCount)) {
            return fail("Please enter the generate quantity");
        }

        projVideoGenerateService.startGenerateVideoData(id, RequestContextUtils.getProjectId(), generateReq);
        return succ();
    }

    @ApiOperation(value = "video workout 分页")
    @GetMapping("/generate/videoList")
    public ResponseResult<PageRes<ProjVideoGeneratePageVO>> videoList(ProjVideoGeneratePageReq videoGeneratePageReq) {
        PageRes<ProjVideoGeneratePageVO> pageRes = projVideoGenerateService.selectVideoGeneratePage(videoGeneratePageReq);
        return succ(pageRes);
    }

}
