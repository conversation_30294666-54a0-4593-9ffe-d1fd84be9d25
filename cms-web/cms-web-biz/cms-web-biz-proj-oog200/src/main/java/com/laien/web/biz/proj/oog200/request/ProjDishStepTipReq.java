package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * dish step tip
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="dish step tip对象", description="dish step tip")
public class ProjDishStepTipReq {

    @ApiModelProperty(value = "图片路径")
    private String imgUrl;

    @ApiModelProperty(value = "介绍")
    private String intro;

}
