package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: template 生成
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template 生成", description = "template 生成")
public class ProjTemplateGenerateReq {

    @ApiModelProperty(value = "生成数量")
    private Integer generateCount;

    @ApiModelProperty(value = "是否需要清理已生成的video 0 否，1是")
    private Integer cleanUp;

}
