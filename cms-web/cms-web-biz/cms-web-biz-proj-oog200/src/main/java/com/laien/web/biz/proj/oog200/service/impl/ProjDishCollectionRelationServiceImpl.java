package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjDishCollectionRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjDishCollectionRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjDishCollectionRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/3 11:05
 */
@Service
public class ProjDishCollectionRelationServiceImpl extends ServiceImpl<ProjDishCollectionRelationMapper, ProjDishCollectionRelation> implements IProjDishCollectionRelationService {


    @Override
    public void deleteByDishCollectionId(Integer dishCollectionId) {

        LambdaUpdateWrapper<ProjDishCollectionRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjDishCollectionRelation::getProjDishCollectionId, dishCollectionId);
        updateWrapper.set(ProjDishCollectionRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjDishCollectionRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjDishCollectionRelation> listByDishCollectionId(Integer dishCollectionId) {

        LambdaQueryWrapper<ProjDishCollectionRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjDishCollectionRelation::getProjDishCollectionId, dishCollectionId);
        return list(queryWrapper);
    }

}
