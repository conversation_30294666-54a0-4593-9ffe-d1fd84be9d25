package com.laien.web.biz.proj.oog200.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.web.biz.proj.oog200.handler.YogaProgramTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 20:00
 */
@Data
public class ProjYogaProgramDetailVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "programCategoryIdList")
    private List<Integer> programCategoryIdList;

    @ApiModelProperty(value = "课程类型")
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = YogaProgramTypeHandler.class)
    private List<YogaProgramTypeEnum> programPositionTypes;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "难度")
    private DifficultyEnum difficulty;

    @ApiModelProperty(value = "持续周数")
    private Integer duration;

    @ApiModelProperty(value = "playlist id")
    private Integer playlistId;

    @ApiModelProperty(value = "playlist detail")
    private ProjBaseDetailVO playlistDetail;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "program level list")
    private List<ProjYogaProgramLevelPageVO> programLevelList;
}
