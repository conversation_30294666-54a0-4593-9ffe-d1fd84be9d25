package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideo;
import com.laien.web.biz.proj.oog200.request.ResYogaVideoPageReq;
import com.laien.web.biz.proj.oog200.response.ResYogaVideoPageVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 瑜伽视频 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface ResYogaVideoMapper extends BaseMapper<ResYogaVideo> {

    /**
     * 分页列表查询
     *
     * @param page page
     * @param pageReq pageReq
     * @return page
     */
    Page<ResYogaVideoPageVO> selectYogaVideoPage(Page<ResYogaVideoPageVO> page, @Param("myPage") ResYogaVideoPageReq pageReq);

    @Select("SELECT * FROM res_yoga_video WHERE id IN (${ids})")
    List<ResYogaVideo> selectAllByIds(@Param("ids") String ids);
}
