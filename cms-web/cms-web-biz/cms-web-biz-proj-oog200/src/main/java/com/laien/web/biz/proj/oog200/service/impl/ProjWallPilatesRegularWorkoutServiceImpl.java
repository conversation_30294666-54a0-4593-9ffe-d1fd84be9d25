package com.laien.web.biz.proj.oog200.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.oog200.enums.ResUpdateStatusEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesAutoWorkoutBO;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesVideoBO;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesRegularWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesRegularWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutAudioI18n;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkoutCategoryRelation;
import com.laien.web.biz.proj.oog200.handler.ProjWallPilatesWorkoutFileHandler;
import com.laien.web.biz.proj.oog200.mapper.ProjWallPilatesRegularWorkoutMapper;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesRegularWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesRegularWorkoutPageVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularCategoryVO;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum.WALL_PILATES;
import static com.laien.web.frame.async.config.ThreadPoolConfig.OTHER_TASK_THREAD_POOL;

/**
 * <p>
 * oog200 workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Slf4j
@Service
public class ProjWallPilatesRegularWorkoutServiceImpl extends ServiceImpl<ProjWallPilatesRegularWorkoutMapper, ProjWallPilatesRegularWorkout> implements IProjWallPilatesRegularWorkoutService {

    @Resource
    private IProjWallPilatesVideoService projWallPilatesVideoService;
    @Resource
    private IProjWallPilatesRegularWorkoutVideoRelationService projWallPilatesRegularWorkoutVideoRelationService;
    @Resource
    private IProjYogaRegularWorkoutCategoryRelationService projYogaRegularWorkoutCategoryRelationService;
    @Resource
    private IProjYogaRegularCategoryService projYogaRegularCategoryService;
    @Resource
    private ProjWallPilatesWorkoutFileHandler projWallPilatesWorkoutFileHandler;
    @Resource
    private IProjYogaRegularWorkoutAudioI18nService projYogaRegularWorkoutAudioI18nService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjWallPilatesRegularWorkoutAddReq workoutReq, Integer projId) {
        check(workoutReq, null);
        List<Integer> videoIdList = workoutReq.getVideoIdList();
        // 基本数据
        ProjWallPilatesRegularWorkout workout = toWorkout(workoutReq, projId);
        ProjWallPilatesAutoWorkoutBO autoWorkoutBO = generateFileAndSet2Entity(projId, videoIdList, workout);
        save(workout);
        // 多语言数据保存
        saveMultiLanguageAudioJson(projId, workout, autoWorkoutBO);
        saveRelation(workout.getId(), videoIdList);

        projYogaRegularWorkoutCategoryRelationService.saveRelation(workoutReq.getCategoryIdList(), YogaAutoWorkoutTemplateEnum.WALL_PILATES, workout.getId(), projId);
        lmsI18nService.handleI18n(Collections.singletonList(workout), projId);
    }

    private ProjWallPilatesAutoWorkoutBO generateFileAndSet2Entity(Integer projId, List<Integer> videoIdList, ProjWallPilatesRegularWorkout workout) {
        // 生成多语言workout file
        List<ProjWallPilatesVideoBO> videoList = projWallPilatesVideoService.queryList(videoIdList);
        ProjWallPilatesAutoWorkoutBO autoWorkoutBO = new ProjWallPilatesAutoWorkoutBO();
        autoWorkoutBO.setVideoList(videoList);
        // 文件生成
        projWallPilatesWorkoutFileHandler.generateFileWithMultiLanguage(Collections.singletonList(autoWorkoutBO), projId).get(0);
        workout.setCalorie(autoWorkoutBO.getCalorie());
        workout.setDuration(autoWorkoutBO.getDuration());
        workout.setAudioLongJson(autoWorkoutBO.getAudioLongJson());
        workout.setAudioShortJson(autoWorkoutBO.getAudioShortJson());
        workout.setVideo2532Url(autoWorkoutBO.getVideo2532Url());
        workout.setVideoM3u8Url(autoWorkoutBO.getVideoM3u8Url());
        return autoWorkoutBO;
    }


    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjWallPilatesRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesRegularWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWallPilatesRegularWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjWallPilatesRegularWorkout::getId, idList);
        update(new ProjWallPilatesRegularWorkout(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {

        LambdaUpdateWrapper<ProjWallPilatesRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesRegularWorkout::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjWallPilatesRegularWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWallPilatesRegularWorkout::getId, idList);
        this.update(new ProjWallPilatesRegularWorkout(), wrapper);
    }

    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjWallPilatesRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWallPilatesRegularWorkout::getDelFlag, GlobalConstant.YES)
                .in(ProjWallPilatesRegularWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY)
                .in(ProjWallPilatesRegularWorkout::getId, idList);
        this.update(new ProjWallPilatesRegularWorkout(), wrapper);
    }

    @Override
    public ProjWallPilatesRegularWorkoutDetailVO findDetailById(Integer id) {
        ProjWallPilatesRegularWorkout workout = baseMapper.selectById(id);
        if (null == workout) {
            return null;
        }
        ProjWallPilatesRegularWorkoutDetailVO detailVO = new ProjWallPilatesRegularWorkoutDetailVO();
        BeanUtils.copyProperties(workout, detailVO);
        Set<Integer> workoutIdSet = new HashSet<>();
        workoutIdSet.add(id);
        List<ProjWallPilatesVideoListVO> videoList = projWallPilatesVideoService.findByWallPilatesRegularWorkoutIdSet(workoutIdSet);
        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = projYogaRegularWorkoutCategoryRelationService.query(Collections.singletonList(id), YogaAutoWorkoutTemplateEnum.WALL_PILATES);
        Set<Integer> categoryIdSet = categoryRelationList.stream().map(ProjYogaRegularWorkoutCategoryRelation::getProjYogaRegularCategoryId).collect(Collectors.toSet());
        List<ProjYogaRegularCategoryVO> categoryListVO = projYogaRegularCategoryService.query(categoryIdSet);
        if (CollUtil.isNotEmpty(categoryListVO)) {
            List<Integer> categoryIdList = categoryListVO.stream().map(ProjYogaRegularCategoryVO::getId).collect(Collectors.toList());
            detailVO.setCategoryIdList(categoryIdList);
        }

        detailVO.setVideoList(videoList).setLanguageArr(MyStringUtil.getSplitWithComa(workout.getLanguage()))
                .setSpecialLimitArr(MyStringUtil.getSplitWithComa(workout.getSpecialLimit()))
                .setYogaTypeArr(MyStringUtil.getSplitWithComa(workout.getYogaType()))
                .setYogaDataSourceList(YogaDataSourceEnum.convertToYogaDataSourceEnum(workout.getDataSources()));
        return detailVO;
    }

    @Override
    public PageRes<ProjWallPilatesRegularWorkoutPageVO> page(ProjWallPilatesRegularWorkoutPageReq pageReq, Integer projId) {

        // 查询
        Page<ProjWallPilatesRegularWorkout> idPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        List<Integer> idList = baseMapper.page(idPage, pageReq, projId, YogaAutoWorkoutTemplateEnum.WALL_PILATES);
        if (CollUtil.isEmpty(idList)) {
            return new PageRes<>(idPage.getCurrent(), idPage.getSize(), idPage.getTotal(), idPage.getPages(), new ArrayList<>());
        }
        LambdaQueryWrapper<ProjWallPilatesRegularWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseModel::getId, idList)
                .orderByDesc(BaseModel::getId);
        List<ProjWallPilatesRegularWorkout> recordList = list(wrapper);
        List<ProjWallPilatesRegularWorkoutPageVO> resultRecords = CollUtil.newArrayList();
        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = projYogaRegularWorkoutCategoryRelationService.query(idList, YogaAutoWorkoutTemplateEnum.WALL_PILATES);
        Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap = categoryRelationList.stream().collect(Collectors.groupingBy(ProjYogaRegularWorkoutCategoryRelation::getWorkoutId));
        Set<Integer> categoryIdSet = categoryRelationList.stream().map(ProjYogaRegularWorkoutCategoryRelation::getProjYogaRegularCategoryId).collect(Collectors.toSet());
        List<ProjYogaRegularCategoryVO> categoryListVO = projYogaRegularCategoryService.query(categoryIdSet);
        Map<Integer, ProjYogaRegularCategoryVO> categoryMap = categoryListVO.stream()
                .collect(Collectors.toMap(ProjYogaRegularCategoryVO::getId, item -> item));

        for (ProjWallPilatesRegularWorkout record : recordList) {
            ProjWallPilatesRegularWorkoutPageVO pageVO = new ProjWallPilatesRegularWorkoutPageVO();
            BeanUtils.copyProperties(record, pageVO);
            List<ProjYogaRegularCategoryVO> categoryList = getCategoryList(record, categoryRelationMap, categoryMap);
            pageVO.setCategoryList(categoryList);
            pageVO.setYogaDataSourceList(YogaDataSourceEnum.convertToYogaDataSourceEnum(record.getDataSources()));
            pageVO.setDisplayUpdateStatus(ResUpdateStatusEnum.SingletonHolder.getStatusMap().get(pageVO.getUpdateStatus()));
            resultRecords.add(pageVO);
        }
        return new PageRes<>(idPage.getCurrent(), idPage.getSize(), idPage.getTotal(), idPage.getPages(), resultRecords);
    }


    @Override
    public void updateFileBatch(Set<Integer> idSet, final Integer projId) {
        if (CollUtil.isEmpty(idSet)) {
            return;
        }
        List<ProjWallPilatesVideoListVO> allRelationVideoList = projWallPilatesVideoService.findByWallPilatesRegularWorkoutIdSet(idSet);
        if (CollUtil.isEmpty(allRelationVideoList)) {
            return;
        }
        LambdaUpdateWrapper<ProjWallPilatesRegularWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BaseModel::getId, idSet)
                .set(ProjWallPilatesRegularWorkout::getUpdateStatus, ResUpdateStatusEnum.UPDATE.getStatus());
        int updateCount = baseMapper.update(new ProjWallPilatesRegularWorkout(), updateWrapper);
        if (updateCount <= GlobalConstant.ZERO) {
            return;
        }

        // 逐一重新生成，异步操作,使用预设线程池
        Executor executorService = applicationContext.getBean(OTHER_TASK_THREAD_POOL, Executor.class);
        List<Integer> allVideoIds = allRelationVideoList.stream()
                .map(ProjWallPilatesVideoListVO::getId)
                .collect(Collectors.toList());
        Map<Integer, ProjWallPilatesVideoBO> videoIdMap = projWallPilatesVideoService.queryList(allVideoIds).stream().collect(Collectors.toMap(ProjWallPilatesVideoBO::getId, Function.identity(), (existing, replacement) -> replacement));
        Map<Integer, List<ProjWallPilatesVideoListVO>> videoGroupByWorkoutId = allRelationVideoList.stream().collect(Collectors.groupingBy(ProjWallPilatesVideoListVO::getWorkoutId));

        // 批量重新生成所有workout
        idSet.stream().map(workoutId -> {
            ProjWallPilatesAutoWorkoutBO workoutBO = new ProjWallPilatesAutoWorkoutBO();
            workoutBO.setId(workoutId);
            List<ProjWallPilatesVideoBO> videoBOList = videoGroupByWorkoutId.get(workoutId).stream().map(ProjWallPilatesVideoListVO::getId).map(videoIdMap::get).collect(Collectors.toList());
            workoutBO.setVideoList(videoBOList);
            return workoutBO;
            // 每个workout独立生成并更新
        }).forEach(workoutBO -> executorService.execute(() -> applicationContext.getBean(IProjWallPilatesRegularWorkoutService.class).generateFileAndUpdate(workoutBO, projId)));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void generateFileAndUpdate(ProjWallPilatesAutoWorkoutBO workoutBO, Integer projId) {

        ProjWallPilatesRegularWorkout regularWorkout = new ProjWallPilatesRegularWorkout();
        regularWorkout.setId(workoutBO.getId());
        try {
            // 生成文件
            projWallPilatesWorkoutFileHandler.generateFileWithMultiLanguage(Lists.newArrayList(workoutBO), projId);
            // 更新workout
            regularWorkout
                    .setCalorie(workoutBO.getCalorie())
                    .setDuration(workoutBO.getDuration())
                    .setVideo2532Url(workoutBO.getVideo2532Url())
                    .setVideoM3u8Url(workoutBO.getVideoM3u8Url())
                    .setAudioShortJson(workoutBO.getAudioShortJson())
                    .setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus())
                    .setAudioLongJson(workoutBO.getAudioLongJson());
            updateById(regularWorkout);
            // 更新多语言音频数据
            projYogaRegularWorkoutAudioI18nService.delete(workoutBO.getId(), YogaAutoWorkoutTemplateEnum.WALL_PILATES);
            // 保存多语言音频数据
            List<ProjYogaRegularWorkoutAudioI18n> audioI18ns = workoutBO.getMultiLanguageAudioShortJson().keySet()
                    .stream()
                    .map(language ->
                            createProjYogaRegularWorkoutAudioI18n(workoutBO.getId(), projId, language, workoutBO))
                    .collect(Collectors.toList());

            projYogaRegularWorkoutAudioI18nService.saveBatch(audioI18ns);

        } catch (Exception ex) {
            log.warn("async update yoga auto workout failed, workout id is {}.", workoutBO.getId(), ex);
            regularWorkout.setUpdateStatus(ResUpdateStatusEnum.FAIL.getStatus());
            updateById(regularWorkout);
        }
    }

    private static ProjYogaRegularWorkoutAudioI18n createProjYogaRegularWorkoutAudioI18n(Integer workoutId, Integer projId, String language, ProjWallPilatesAutoWorkoutBO pilatesAutoWorkoutBO) {
        return new ProjYogaRegularWorkoutAudioI18n().setWorkoutId(workoutId)
                .setProjId(projId)
                .setLanguage(language)
                .setWorkoutType(WALL_PILATES)
                .setAudioLongJsonUrl(pilatesAutoWorkoutBO.getMultiLanguageAudioLongJson().get(language))
                .setAudioShortJsonUrl(pilatesAutoWorkoutBO.getMultiLanguageAudioShortJson().get(language));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjWallPilatesRegularWorkoutUpdateReq workoutReq, Integer projId) {
        Integer id = workoutReq.getId();
        check(workoutReq, id);
        ProjWallPilatesRegularWorkout wallPilatesRegularWorkout = baseMapper.selectById(id);
        if (null == wallPilatesRegularWorkout) {
            throw new BizException("wallPilatesRegularWorkout not found");
        }
        List<Integer> videoIdList = workoutReq.getVideoIdList();
        ProjWallPilatesRegularWorkout workout = toWorkout(workoutReq, projId);
        // 生成多语言workout file
        ProjWallPilatesAutoWorkoutBO autoWorkoutBO = generateFileAndSet2Entity(projId, videoIdList, workout);
        // 保存多语言配置
        saveMultiLanguageAudioJson(projId, workout, autoWorkoutBO);
        updateById(workout);
        saveRelation(id, videoIdList);
        projYogaRegularWorkoutCategoryRelationService.saveRelation(workoutReq.getCategoryIdList(), YogaAutoWorkoutTemplateEnum.WALL_PILATES, workout.getId(), projId);
        lmsI18nService.handleI18n(Collections.singletonList(workout), projId);
    }

    private void saveMultiLanguageAudioJson(Integer projId, ProjWallPilatesRegularWorkout workout, ProjWallPilatesAutoWorkoutBO autoWorkoutBO) {
        // 多语言数据保存
        Optional.ofNullable(workout.getId()).ifPresent(workoutId -> projYogaRegularWorkoutAudioI18nService.delete(workoutId, WALL_PILATES));
        List<ProjYogaRegularWorkoutAudioI18n> audioI18ns = autoWorkoutBO.getMultiLanguageAudioShortJson().keySet()
                .stream()
                .map(language ->
                        createProjYogaRegularWorkoutAudioI18n(workout.getId(), projId, language, autoWorkoutBO))
                .collect(Collectors.toList());

        projYogaRegularWorkoutAudioI18nService.saveBatch(audioI18ns);
    }

    private ProjWallPilatesRegularWorkout toWorkout(ProjWallPilatesRegularWorkoutAddReq workoutReq, Integer projId) {
        ProjWallPilatesRegularWorkout workout = new ProjWallPilatesRegularWorkout();
        BeanUtils.copyProperties(workoutReq, workout);
        workout.setProjId(projId);
        String[] specialLimitArr = workoutReq.getSpecialLimitArr();
        String[] yogaTypeArr = workoutReq.getYogaTypeArr();
        workout.setLanguage(MyStringUtil.getJoinWithComma(workoutReq.getLanguageArr()))
                .setSpecialLimit(MyStringUtil.getJoinWithComma(specialLimitArr))
                .setYogaType(MyStringUtil.getJoinWithComma(yogaTypeArr))
                .setDataSources(YogaDataSourceEnum.dataSourceListToString(workoutReq.getYogaDataSourceList()));

        return workout;
    }

    private static List<ProjYogaRegularCategoryVO> getCategoryList(ProjWallPilatesRegularWorkout record, Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap, Map<Integer, ProjYogaRegularCategoryVO> categoryMap) {
        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = categoryRelationMap.getOrDefault(record.getId(), new ArrayList<>());
        List<ProjYogaRegularCategoryVO> categoryList = new ArrayList<>();
        for (ProjYogaRegularWorkoutCategoryRelation relation : categoryRelationList) {
            ProjYogaRegularCategoryVO categoryVO = categoryMap.get(relation.getProjYogaRegularCategoryId());
            if (null != categoryVO) {
                categoryList.add(categoryVO);
            }
        }
        return categoryList;
    }

    private void saveRelation(Integer id, List<Integer> videoIdList) {
        projWallPilatesRegularWorkoutVideoRelationService.deleteByWallPilatesRegularWorkoutIdSet(Collections.singleton(id));
        if (CollUtil.isEmpty(videoIdList)) {
            return;
        }
        List<ProjWallPilatesRegularWorkoutVideoRelation> videoRelationList = new ArrayList<>(videoIdList.size());
        videoIdList.forEach(item -> {
            ProjWallPilatesRegularWorkoutVideoRelation videoRelation = new ProjWallPilatesRegularWorkoutVideoRelation();
            videoRelation.setProjWallPilatesVideoId(item)
                    .setProjWallPilatesRegularWorkoutId(id);
            videoRelationList.add(videoRelation);
        });
        projWallPilatesRegularWorkoutVideoRelationService.saveBatch(videoRelationList);
    }

    private void check(ProjWallPilatesRegularWorkoutAddReq req, Integer id) {
        LambdaQueryWrapper<ProjWallPilatesRegularWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWallPilatesRegularWorkout::getName, req.getName())
                .ne(null != id, BaseModel::getId, id);
        List<ProjWallPilatesRegularWorkout> videoList = baseMapper.selectList(wrapper);
        Set<String> nameSet = videoList.stream().map(ProjWallPilatesRegularWorkout::getName).collect(Collectors.toSet());
        if (nameSet.contains(req.getName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWallPilatesRegularWorkout::getEventName, req.getEventName())
                .ne(null != id, BaseModel::getId, id);
        videoList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet = videoList.stream().map(ProjWallPilatesRegularWorkout::getName).collect(Collectors.toSet());
        if (eventNameSet.contains(req.getEventName())) {
            String error = "eventName already exists";
            throw new BizException(error);
        }
    }
}
