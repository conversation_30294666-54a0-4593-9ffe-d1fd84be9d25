package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2024/9/24 17:59
 */

@Data
public class ChairYogaWorkoutGenerateBO {

    private Integer generateDays;

    private Integer minMillSecond;

    private Integer maxMillSecond;

    private Integer warmUpCount;

    private Integer coolDownCount;

    private Integer templateId;

    private Integer templateTaskId;

    private ChairYogaWorkoutAudioBO audioBO;

    private String operationUserName;

    private Integer projId;

    private ChairYogaWorkoutVideoBO videoBO;

    private Map<String, List<ProjChairYogaVideo>> centralAndLeftVideoMap;

    private Map<Integer, Integer> frontVideoDurationMap;

    private Map<Integer, Integer> sideVideoDurationMap;

    private Map<Integer, ProjChairYogaVideo> videoMap;

    private Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap;

    private List<ProjChairYogaVideoSliceDetailVO> videoSliceList;

    private Map<String, List<ProjChairYogaVideo>> fullTargetVideoMap;

    private Map<String, List<ProjChairYogaVideo>> partTargetVideoMap;

    private Map<String, List<ProjChairYogaVideo>> withoutTargetVideoMap;

}
