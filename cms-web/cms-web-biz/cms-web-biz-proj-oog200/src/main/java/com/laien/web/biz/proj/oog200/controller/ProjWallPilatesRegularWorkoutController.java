package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesRegularWorkout;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesRegularWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesRegularWorkoutPageVO;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesRegularWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * oog200 workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Api(tags = "项目管理:wall pilates regular workout")
@RestController
@RequestMapping("/proj/wallPilatesRegularWorkout")
public class ProjWallPilatesRegularWorkoutController extends ResponseController {

    @Resource
    private IProjWallPilatesRegularWorkoutService pilatesRegularWorkoutService;

    @ApiOperation(value = "分页列表")
    @GetMapping( "/page")
    public ResponseResult<PageRes<ProjWallPilatesRegularWorkoutPageVO>> page(ProjWallPilatesRegularWorkoutPageReq pageReq) {
        return succ(pilatesRegularWorkoutService.page(pageReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjWallPilatesRegularWorkoutAddReq workoutReq) {
        pilatesRegularWorkoutService.save(workoutReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjWallPilatesRegularWorkoutUpdateReq workoutReq) {
        pilatesRegularWorkoutService.update(workoutReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjWallPilatesRegularWorkoutDetailVO> detail(@PathVariable Integer id) {
        ProjWallPilatesRegularWorkoutDetailVO detailVO = pilatesRegularWorkoutService.findDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        pilatesRegularWorkoutService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        pilatesRegularWorkoutService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        pilatesRegularWorkoutService.deleteByIdList(idList);
        return succ();
    }


    @ApiOperation(value = "批量更新workout 音视频资源", notes = "异步更新")
    @PostMapping("/updateBatch")
    public ResponseResult<Void> updateFileBatch(@RequestBody ProjYogaRegularWorkoutBatchUpdateReq idSetReq) {
        pilatesRegularWorkoutService.updateFileBatch(idSetReq.getRegularWorkoutIds(),RequestContextUtils.getProjectId());
        return succ();
    }



}
