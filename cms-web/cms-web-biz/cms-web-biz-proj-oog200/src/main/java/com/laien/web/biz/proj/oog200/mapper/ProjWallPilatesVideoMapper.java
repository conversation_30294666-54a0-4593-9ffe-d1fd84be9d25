package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.laien.web.biz.proj.oog200.bo.ProjWallPilatesVideoBO;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesVideo;
import com.laien.web.biz.proj.oog200.response.ProjWallPilatesVideoListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * wall pilates video资源 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public interface ProjWallPilatesVideoMapper extends BaseMapper<ProjWallPilatesVideo> {

    List<ProjWallPilatesVideoListVO> find(@Param("wallPilatesAutoWorkoutIdSet") Set<Integer> wallPilatesAutoWorkoutIdSet);

    List<ProjWallPilatesVideoListVO> findByWallPilatesRegularWorkoutIdSet(@Param("wallPilatesRegularWorkoutIdSet") Set<Integer> wallPilatesRegularWorkoutIdSet);

    IPage<ProjWallPilatesVideo> findPage(@Param("page") IPage<ProjWallPilatesVideo> page, @Param("wallPilatesAutoWorkoutId") Integer wallPilatesAutoWorkoutId);

    List<ProjWallPilatesVideoBO> queryByIdList(@Param("idList") List<Integer> idList);
}
