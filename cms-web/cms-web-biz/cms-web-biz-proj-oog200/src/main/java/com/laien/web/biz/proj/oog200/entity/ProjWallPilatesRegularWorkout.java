package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * oog200 workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_wall_pilates_regular_workout")
@ApiModel(value="ProjWallPilatesRegularWorkout对象", description="oog200 workout")
public class ProjWallPilatesRegularWorkout extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名字")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "难度")
    @TranslateField
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "描述")
    @TranslateField
    private String description;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "Video 的2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称")
    private String audioShortJson;

    @ApiModelProperty(value = "yoga类型Classic Yoga, Lazy Yoga,Somatic Yoga, Chair Yoga,Wall Pilates, Other")
    private String yogaType;

    @ApiModelProperty(value = "特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String specialLimit;

    @ApiModelProperty(value = "资源类型：0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog")
    private YogaAutoWorkoutTemplateEnum videoType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program")
    private String dataSources;


}
