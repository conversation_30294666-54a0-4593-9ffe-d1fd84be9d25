package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramRelation;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelPageVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:56
 */
public interface IProjYogaProgramRelationService extends IService<ProjYogaProgramRelation> {

    List<ProjYogaProgramLevelPageVO> listByProgramId(Integer programId);

    /**
     * 逻辑删除
     *
     * @param programId
     */
    void deleteByProgramId(Integer programId);

    /**
     *
     * @param programIds
     * @return
     */
    List<ProjYogaProgramRelation> listByProgramIds(Collection<Integer> programIds);
}
