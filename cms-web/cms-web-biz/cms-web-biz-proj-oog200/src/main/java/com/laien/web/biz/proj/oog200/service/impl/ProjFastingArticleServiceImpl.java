package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog200.enums.FastingArticleEnum;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjFastingArticle;
import com.laien.web.biz.proj.oog200.mapper.ProjFastingArticleMapper;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleAddReq;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleListReq;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjFastingArticleDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjFastingArticleListVO;
import com.laien.web.biz.proj.oog200.service.IProjFastingArticleService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * fasting article 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Service
public class ProjFastingArticleServiceImpl extends ServiceImpl<ProjFastingArticleMapper, ProjFastingArticle> implements IProjFastingArticleService {

    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ProjFastingArticleAddReq articleAddReq, Integer projId) {
        check(articleAddReq, null, projId);
        ProjFastingArticle article = new ProjFastingArticle();
        BeanUtils.copyProperties(articleAddReq, article);
        article.setProjId(projId);
        article.setStatus(GlobalConstant.STATUS_DRAFT);
        save(article);

        lmsI18nService.handleI18n(Collections.singletonList(article), RequestContextUtils.getProjectId());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjFastingArticleUpdateReq articleUpdateReq, Integer projId) {
        Integer id = articleUpdateReq.getId();
        ProjFastingArticle article = baseMapper.selectById(id);
        if (null == article) {
            throw new BizException("article not found");
        }
        ProjFastingArticle updateVideo = new ProjFastingArticle();
        check(articleUpdateReq, id, projId);
        BeanUtils.copyProperties(articleUpdateReq, updateVideo);
        updateVideo.setProjId(projId);
        updateById(updateVideo);

        lmsI18nService.handleI18n(Collections.singletonList(updateVideo), RequestContextUtils.getProjectId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFastingArticle::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFastingArticle::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjFastingArticle::getId, idList);
        update(new ProjFastingArticle(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {

        LambdaUpdateWrapper<ProjFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFastingArticle::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFastingArticle::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFastingArticle::getId, idList);
        this.update(new ProjFastingArticle(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<ProjFastingArticle> articleList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjFastingArticle info = new ProjFastingArticle();
            info.setSorted(i)
                    .setId(idList.get(i));
            articleList.add(info);
        }
        updateBatchById(articleList);
    }

    @Override
    public List<ProjFastingArticleListVO> list(ProjFastingArticleListReq articleListReq, Integer projId) {
        String titleName = articleListReq.getTitleName();
        FastingArticleEnum type = articleListReq.getType();
        Integer status = articleListReq.getStatus();

        LambdaQueryWrapper<ProjFastingArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(titleName), ProjFastingArticle::getTitleName, titleName)
                .eq(null != type, ProjFastingArticle::getType, type)
                .eq(null != status, ProjFastingArticle::getStatus, status)
                .orderByAsc(ProjFastingArticle::getSorted)
                .orderByDesc(BaseModel::getId);
        List<ProjFastingArticle> articleList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(articleList)) {
            return new ArrayList<>();
        }
        List<ProjFastingArticleListVO> articleVOList = new ArrayList<>(articleList.size());
        for (ProjFastingArticle article : articleList) {
            ProjFastingArticleListVO articleVO = new ProjFastingArticleListVO();
            BeanUtils.copyProperties(article, articleVO);
            articleVOList.add(articleVO);
        }
        return articleVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFastingArticle::getDelFlag, GlobalConstant.YES)
                .eq(ProjFastingArticle::getStatus, GlobalConstant.STATUS_DRAFT)
                .in(ProjFastingArticle::getId, idList);
        this.update(new ProjFastingArticle(), wrapper);
    }

    @Override
    public ProjFastingArticleDetailVO findDetailById(Integer id) {
        ProjFastingArticle article = baseMapper.selectById(id);
        if (null == article) {
            return null;
        }
        ProjFastingArticleDetailVO detailVO = new ProjFastingArticleDetailVO();
        BeanUtils.copyProperties(article, detailVO);
        return detailVO;
    }

    private void check(ProjFastingArticleAddReq articleReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjFastingArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFastingArticle::getTitleName, articleReq.getTitleName())
                .eq(ProjFastingArticle::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjFastingArticle> fastingArticleList = baseMapper.selectList(wrapper);
        Set<String> nameSet = fastingArticleList.stream().map(ProjFastingArticle::getTitleName).collect(Collectors.toSet());
        if (nameSet.contains(articleReq.getTitleName())) {
            String error = "title name already exists";
            throw new BizException(error);
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFastingArticle::getEventName, articleReq.getEventName())
                .eq(ProjFastingArticle::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        fastingArticleList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= fastingArticleList.stream().map(ProjFastingArticle::getEventName).collect(Collectors.toSet());
        if (eventNameSet.contains(articleReq.getEventName())) {
            String error = "eventName already exists";
            throw new BizException(error);
        }
    }
}
