package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.GoalEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value = "ProjYogaAutoWorkoutDetailVO", description = "ProjYogaAutoWorkoutDetailVO")
public class ProjYogaAutoWorkoutDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;


    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "goal")
    private GoalEnum goal;

    @ApiModelProperty(value = "模版id")
    private Integer projYogaAutoTemplateId;

    @ApiModelProperty(value = "video列表")
    private List<ProjYogaAutoWorkoutTempVideoPageVO> videoList;

}
