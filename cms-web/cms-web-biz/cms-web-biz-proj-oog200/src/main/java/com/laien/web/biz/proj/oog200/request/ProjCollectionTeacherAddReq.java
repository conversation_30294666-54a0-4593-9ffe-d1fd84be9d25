package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
/**
 * <p>
 * ProjCollectionTeacherAddReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/28
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjCollectionTeacherAddReq", description="ProjCollectionTeacherAddReq")
public class ProjCollectionTeacherAddReq {

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "avatarUrl")
    private String avatarUrl;

    @ApiModelProperty(value = "description")
    private String description;
}