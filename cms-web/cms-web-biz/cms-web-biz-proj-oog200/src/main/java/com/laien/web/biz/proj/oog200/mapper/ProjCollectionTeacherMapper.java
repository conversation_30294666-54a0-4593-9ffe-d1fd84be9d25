package com.laien.web.biz.proj.oog200.mapper;

import com.laien.web.biz.proj.oog200.entity.ProjCollectionTeacher;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.request.ProjCollectionTeacherListReq;
import com.laien.web.biz.proj.oog200.response.ProjCollectionTeacherVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 教练表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
public interface ProjCollectionTeacherMapper extends BaseMapper<ProjCollectionTeacher> {

}
