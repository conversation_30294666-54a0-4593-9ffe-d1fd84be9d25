package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimArrayConverter;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * note: yoga video导入
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video导入", description = "yoga video导入")
public class ResYogaVideoImportReq {

    @NotEmpty(message = "Main Pose Name cannot be empty", groups = Group1.class)
    @Length(message = "The Main Pose Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Main Pose Name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作展示名称")
    private String name;

//    @ApiModelProperty(value = "动作类型")
//    private String eventName;

    @NotEmpty(message = "imageUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "cover_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

//    @NotEmpty(message = "Pose Type cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Pose Type")
    @ApiModelProperty(value = "动作类型 数组 Start、Main、End、CoolDown")
    private String type;

    @NotEmpty(message = "Difficulty cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Difficulty", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @NotEmpty(message = "Position cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Position", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作体位 Steated、Standing、Prone、Supine、Arm & Leg Support")
    private String position;

    @NotEmpty(message = "Focus cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Focus", converter = StringStringTrimArrayConverter.class)
    @ApiModelProperty(value = "瑜伽派别 数组 Strength、Balancing、Relaxation、Flexbility")
    private String focus;

    @ExcelProperty(value = "Special Needs", converter = StringStringTrimArrayConverter.class)
    @ApiModelProperty(value = "特殊人群不可使用的 数组 Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String specialLimit;

    @NotEmpty(message = "Pose Duration cannot be empty", groups = Group1.class)
    @Pattern(message = "Pose Duration format error like (5s|10s|15s|20s|30s|40s|60s)", regexp = "\\b(5s|10s|15s|20s|30s|40s|60s)\\b", groups = Group2.class)
    @ExcelProperty(value = "Pose Duration", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "动作时长 单位毫秒")
    private String poseTime;

    @NotEmpty(message = "main_url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "main_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @NotNull(message = "main_duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "main_duration")
    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @NotEmpty(message = "assist_url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "assist_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @NotNull(message = "assist_duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "assist_duration")
    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @NotEmpty(message = "name_url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "name_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @NotNull(message = "name_duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "name_duration")
    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @NotEmpty(message = "guidance_url cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "guidance_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @NotNull(message = "guidance_duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "guidance_duration")
    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @NotNull(message = "Calorie cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Calorie")
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ExcelProperty(value = "id")
    @ApiModelProperty(value = "批量导入的id")
    private Integer importId;

    @ExcelProperty(value = "core_voice_config_i18n_name",converter = StringStringTrimConverter.class)
    @NotEmpty(message = "coreVoiceConfigI18nId cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "翻译声音")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    /**
     * ResTransition
     */
//    @ApiModelProperty(value = "动作展示名称")
//    private String flowName;

//    @ApiModelProperty(value = "图片")
//    private String imageUrl;

    @ExcelProperty(value = "flow_main_url")
    @ApiModelProperty(value = "正位视频")
    private String flowFrontVideoUrl;

    @ExcelProperty(value = "flow_main_duration")
    @ApiModelProperty(value = "正位视频时长")
    private Integer flowFrontVideoDuration;

    @ExcelProperty(value = "flow_assist_url")
    @ApiModelProperty(value = "侧位视频")
    private String flowSideVideoUrl;

    @ExcelProperty(value = "flow_assist_duration")
    @ApiModelProperty(value = "侧位视频时长")
    private Integer flowSideVideoDuration;

    @ExcelProperty(value = "flow_guidance_url")
    @ApiModelProperty(value = "解说音频")
    private String flowGuidanceAudioUrl;

    @ExcelProperty(value = "flow_guidance_duration")
    @ApiModelProperty(value = "解说音频时长")
    private Integer flowGuidanceAudioDuration;

    @ExcelProperty(value = "Next Pose", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "Next Pose Name")
    private String nextPoseName;

}

