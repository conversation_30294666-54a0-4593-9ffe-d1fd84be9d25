package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkout;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutPageReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * oog200 workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface ProjYogaRegularWorkoutMapper extends BaseMapper<ProjYogaRegularWorkout> {

    /**
     * 只查出id
     */
    List<Integer> page(@Param("page") Page<ProjYogaRegularWorkout> page,
                       @Param("pageReq") ProjYogaRegularWorkoutPageReq pageReq,
                       @Param("projId") Integer projId,
                       @Param("workoutType") YogaAutoWorkoutTemplateEnum workoutType);

}
