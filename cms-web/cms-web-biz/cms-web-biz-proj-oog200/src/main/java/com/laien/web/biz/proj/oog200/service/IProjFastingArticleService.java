package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjFastingArticle;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleAddReq;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleListReq;
import com.laien.web.biz.proj.oog200.request.ProjFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjFastingArticleDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjFastingArticleListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.List;

/**
 * <p>
 * fasting article 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface IProjFastingArticleService extends IService<ProjFastingArticle> {

    void save(ProjFastingArticleAddReq articleAddReq, Integer projId);

    void update(ProjFastingArticleUpdateReq articleUpdateReq, Integer projId);

    ProjFastingArticleDetailVO findDetailById(Integer id);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIds(List<Integer> idList);

    List<ProjFastingArticleListVO> list(ProjFastingArticleListReq articleListReq, Integer projId);

    void sort(IdListReq idListReq);
}
