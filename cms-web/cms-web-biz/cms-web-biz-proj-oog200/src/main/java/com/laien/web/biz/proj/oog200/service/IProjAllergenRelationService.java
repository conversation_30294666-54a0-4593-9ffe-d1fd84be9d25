package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import com.laien.web.biz.proj.oog200.entity.ProjAllergenRelation;

import java.util.List;

/**
 * <p>
 * allergen和业务表的关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface IProjAllergenRelationService extends IService<ProjAllergenRelation> {

    void saveBatch(List<Integer> allergenIdList, Integer projId, Integer dataId, AllergenRelationBusinessEnum businessType);

    List<ProjAllergenRelation> query(Integer dishId, AllergenRelationBusinessEnum businessType);
}
