package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoAudioDetailVO;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/8/7 15:33
 */
@Data
public class PoseWorkoutGenerateBO {

    private AudioJsonBO ready2GoAudio;

    private ProjYogaPoseVideo poseVideo;

    private List<PoseVideoBO> poseVideoBOList;

    private Integer round;

    private Integer splitOne;

    private Integer splitTwo;

    private Integer videoMaskDelay;

    private Integer firstVideoMaskDuration;

    private Integer firstVideoMaskBuffer;

    private Integer secondVideoMaskDuration;

    private Integer secondVideoMaskBuffer;

    private Integer thirdVideoMaskDuration;

    private Integer thirdVideoMaskBuffer;

    private ProjYogaPoseVideoAudioDetailVO roundOneAudio;

    private ProjYogaPoseVideoAudioDetailVO roundTwoAudio;

    private ProjYogaPoseVideoAudioDetailVO roundThreeAudio;

    private String operationUserName;

    private Map<Integer, AudioJsonBO> videoIndexAndAudioMap;


    public AudioJsonBO getAudioByAudioIndex(Integer index) {

        if (MapUtils.isEmpty(videoIndexAndAudioMap) || Objects.isNull(index)) {
            return null;
        }

        return videoIndexAndAudioMap.get(index);
    }

}
