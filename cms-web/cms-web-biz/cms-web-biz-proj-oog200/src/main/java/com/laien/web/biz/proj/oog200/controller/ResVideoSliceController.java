package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ResVideoSlice;
import com.laien.web.biz.proj.oog200.request.ResVideoSliceAddReq;
import com.laien.web.biz.proj.oog200.request.ResVideoSlicePageReq;
import com.laien.web.biz.proj.oog200.request.ResVideoSliceUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.response.ResVideoSlicePageVO;
import com.laien.web.biz.proj.oog200.service.IResVideoSliceService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * video slice 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Api(tags = "资源管理:视频Video slice")
@RestController
@RequestMapping("/res/videoSlice")
public class ResVideoSliceController extends ResponseController {

    @Resource
    private IResVideoSliceService iResVideoSliceService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResVideoSlicePageVO>> page(ResVideoSlicePageReq pageReq) {
        PageRes<ResVideoSlicePageVO> pageRes = iResVideoSliceService.selectVideoSlicePage(pageReq, GlobalConstant.TWO);
        return succ(pageRes);
    }

    @ApiOperation(value = "分页列表")
    @GetMapping("/pageV3")
    public ResponseResult<PageRes<ResVideoSlicePageVO>> pageV3(ResVideoSlicePageReq pageReq) {
        PageRes<ResVideoSlicePageVO> pageRes = iResVideoSliceService.selectVideoSlicePage(pageReq, GlobalConstant.THREE);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResVideoSliceAddReq videoSliceAddReq) {
        videoSliceAddReq.setStatus(GlobalConstant.STATUS_DISABLE);
        iResVideoSliceService.saveVideoSlice(videoSliceAddReq);
        return succ();
    }

    @ApiOperation(value = "新增为草稿")
    @PostMapping("/addAsDraft")
    public ResponseResult<Void> addAsDraft(@RequestBody ResVideoSliceAddReq videoSliceAddReq) {
        videoSliceAddReq.setStatus(GlobalConstant.STATUS_DRAFT);
        iResVideoSliceService.saveVideoSlice(videoSliceAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResVideoSliceUpdateReq videoSliceUpdateReq) {
        videoSliceUpdateReq.setStatus(GlobalConstant.STATUS_ENABLE);
        iResVideoSliceService.updateVideoSlice(videoSliceUpdateReq);
        return succ();
    }

    @ApiOperation(value = "修改为草稿")
    @PostMapping("/updateAsDraft")
    public ResponseResult<Void> updateAsDraft(@RequestBody ResVideoSliceUpdateReq videoSliceUpdateReq) {
        videoSliceUpdateReq.setStatus(GlobalConstant.STATUS_DRAFT);
        iResVideoSliceService.updateVideoSlice(videoSliceUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResVideoSliceDetailVO> detail(@PathVariable Integer id) {
        ResVideoSliceDetailVO detailVO = iResVideoSliceService.getVideoSliceDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        iResVideoSliceService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        iResVideoSliceService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        iResVideoSliceService.deleteByIds(idList);
        return succ();
    }

}
