package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: YogaRegularWorkout修改
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "YogaRegularWorkout修改", description = "YogaRegularWorkout修改")
public class ProjYogaRegularWorkoutUpdateReq extends ProjYogaRegularWorkoutAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
