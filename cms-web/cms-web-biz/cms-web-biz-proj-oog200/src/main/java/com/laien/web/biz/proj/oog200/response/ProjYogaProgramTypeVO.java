package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * oog200 workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
public class ProjYogaProgramTypeVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "type code，小于1000的预留给python用")
    private Integer code;

    private Integer id;

}
