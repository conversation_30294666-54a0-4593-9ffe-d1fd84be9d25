package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjWallPilatesVideoLeftRightDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

}
