package com.laien.web.biz.proj.oog200.util;//package com.laien.cms.util;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.excel.EasyExcel;
//import com.alibaba.excel.context.AnalysisContext;
//import com.alibaba.excel.event.AnalysisEventListener;
//import com.baomidou.mybatisplus.core.toolkit.SystemClock;
//import com.laien.cms.algorithm.WorkoutGenerated200Algorithm;
//import lombok.Data;
//import lombok.SneakyThrows;
//import org.jgrapht.Graph;
//import org.jgrapht.GraphPath;
//import org.jgrapht.graph.DefaultDirectedWeightedGraph;
//import org.jgrapht.graph.DefaultWeightedEdge;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.FileNotFoundException;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Collectors;
//
//public class TestGenerated {
//
//    @SneakyThrows
//    public static void main(String[] args) {
//        //1、使用easyExcel,转换excel数据为ResVideoPoseImportReq对象
//        List<ResVideoPoseImportReq> resVideoPoseImportReqs = parseExcelData();
//        //2、转换数据为计算的节点
//        Set<Node> allNodes = initNodes(resVideoPoseImportReqs);
//        Map<String, Node> nodeMapByName = allNodes.stream().collect(Collectors.toMap(Node::getName, t -> t));
//        Set<Link> allLinks = initLinks(resVideoPoseImportReqs, nodeMapByName);
//        //3、计算
//        Set<Node> endNodes = allNodes.stream().filter(n -> n.getIsEnd() == true).collect(Collectors.toSet());
//        Set<Node> startNodes = allNodes.stream().filter(n -> n.getIsStart() == true).collect(Collectors.toSet());
//        Set<Node> mainNodes = allNodes.stream().collect(Collectors.toSet());
//        Map<Node, List<Link>> linkMapByCurrent = allLinks.stream().collect(Collectors.groupingBy(Link::getCurrentNode));
//        List<GraphPath> allFeasiblePath = CollUtil.newArrayList();
//        int minDuration = 60 * 5;
//        int maxDuration = 60 * 30;
//        int bestDuration = 60 * 15;
//        long oldTime = SystemClock.now();
//        for (Node startNode : startNodes) {
//            System.out.println("以" + startNode + "开始，生成数据");
//            List<GraphPath> paths = getPaths(linkMapByCurrent, startNode, endNodes, mainNodes, minDuration, maxDuration, 1000, false);
//            allFeasiblePath.addAll(paths);
////            break;
//        }
//        System.out.println("总数量:" + allFeasiblePath.size() + ",耗时:" + (SystemClock.now() - oldTime) + " ms");
//        //按时间进行一个排序 离最佳时间越近的优先级越高
//        Collections.shuffle(allFeasiblePath);
//        allFeasiblePath = allFeasiblePath.stream().sorted((p1, p2) -> {
//            double total1 = p1.getWeight() + ((Node) p1.getStartVertex()).getPoseTime();
//            double total2 = p2.getWeight() + ((Node) p2.getStartVertex()).getPoseTime();
//            double abs1 = Math.abs(total1 - bestDuration);
//            double abs2 = Math.abs(total2 - bestDuration);
//            return abs2 < abs1 ? 1 : abs2 == abs1 ? 0 : -1;
//        }).collect(Collectors.toList());
//        //去掉部分相似的结果
//        List<GraphPath> result = CollUtil.newArrayList();
//        Iterator<GraphPath> it1 = allFeasiblePath.iterator();
//        Set<GraphPath> removePath = CollUtil.newHashSet();
//        while (it1.hasNext()) {
//            GraphPath next1 = it1.next();
//            if (removePath.contains(next1)) {
//                continue;
//            }
//            Iterator<GraphPath> it2 = allFeasiblePath.iterator();
//            while (it2.hasNext()) {
//                GraphPath next2 = it2.next();
//                if (removePath.contains(next2)) {
//                    continue;
//                }
//                if (next1 != next2) {
//                    List vertexList1 = next1.getVertexList();
//                    List vertexList2 = next2.getVertexList();
//                    double v = calculateJaccardSimilarity(vertexList1, vertexList2);
////                    System.out.println(v);
//                    if (v > 0.8) {
//                        removePath.add(next2);
//                    }
//                }
//            }
//        }
//        //去掉相似后
//        allFeasiblePath.removeAll(removePath);
//        for (GraphPath graphPath : allFeasiblePath) {
//            System.out.println(graphPath.getVertexList());
//        }
//        //根据不同的开始节点分组
//        Map<Object, List<GraphPath>> startNodePaths = allFeasiblePath.stream().collect(Collectors.groupingBy(GraphPath::getStartVertex));
//        for (Map.Entry<Object, List<GraphPath>> objectListEntry : startNodePaths.entrySet()) {
//            Object startNode = objectListEntry.getKey();
//            List<GraphPath> paths = objectListEntry.getValue();
//            System.out.println(startNode + ",path count:" + paths.size());
//        }
//
//    }
//
//    public static double calculateJaccardSimilarity(Collection set1, Collection set2) {
//        Set<?> intersection = new HashSet<>(set1);
//        intersection.retainAll(set2);
//        Set<?> union = new HashSet<>(set1);
//        union.addAll(set2);
//        return (double) intersection.size() / union.size();
//    }
//
//    /**
//     * 初始化所有的连接链路
//     *
//     * @param resVideoPoseImportReqs
//     * @param nodeMapByName
//     * @return
//     */
//    private static Set<Link> initLinks(List<ResVideoPoseImportReq> resVideoPoseImportReqs, Map<String, Node> nodeMapByName) {
//        Set<Link> allLinks = CollUtil.newHashSet();
//        for (ResVideoPoseImportReq resVideoPoseImportReq : resVideoPoseImportReqs) {
//            String currentNodeName = resVideoPoseImportReq.getName();
//            String nextNodeName = resVideoPoseImportReq.getNextPose();
//            Optional.ofNullable(nodeMapByName.get(currentNodeName)).ifPresent(currentNode -> {
//                Optional.ofNullable(nodeMapByName.get(nextNodeName)).ifPresent(nextNode -> {
//                    Link link = new Link();
//                    link.setCurrentNode(currentNode);
//                    link.setNextNode(nextNode);
//                    link.setPoseTime(nextNode.getPoseTime());
//                    allLinks.add(link);
//                });
//            });
//        }
//        return allLinks;
//    }
//
//    /**
//     * 初始化所有节点
//     *
//     * @param resVideoPoseImportReqs
//     * @return
//     */
//    private static Set<Node> initNodes(List<ResVideoPoseImportReq> resVideoPoseImportReqs) {
//        AtomicInteger idGenerator = new AtomicInteger(0);
//        Set<Node> allNodes = CollUtil.newHashSet();
//        resVideoPoseImportReqs.stream().collect(Collectors.groupingBy(ResVideoPoseImportReq::getName)).entrySet().stream().forEach(e -> {
//            String key = e.getKey();
//            Node node = new Node();
//            node.setId(idGenerator.addAndGet(1));
//            node.setName(key);
//            ResVideoPoseImportReq resVideoPoseImportReq = e.getValue().stream().findFirst().get();
//            node.setPoseTime(Integer.parseInt(StrUtil.subBefore(resVideoPoseImportReq.getPoseTime(), "s", true)));
//            node.setIsStart(StrUtil.equals(resVideoPoseImportReq.getStartPose(), "checked"));
//            node.setIsEnd(StrUtil.equals(resVideoPoseImportReq.getEndPose(), "checked"));
//            node.setDifficulty(resVideoPoseImportReq.getDifficulty());
//            allNodes.add(node);
//        });
//        return allNodes;
//    }
//
//    /**
//     * 解析excel数据
//     *
//     * @return
//     * @throws FileNotFoundException
//     */
//    private static List<ResVideoPoseImportReq> parseExcelData() throws FileNotFoundException {
//        List<ResVideoPoseImportReq> resVideoPoseImportReqs = CollUtil.newArrayList();
//        FileInputStream excelInputStream = new FileInputStream(new File("/Users/<USER>/Documents/待拍摄动作-主表.xlsx"));
//        EasyExcel.read(excelInputStream, ResVideoPoseImportReq.class, new AnalysisEventListener<ResVideoPoseImportReq>() {
//            @Override
//            public void invoke(ResVideoPoseImportReq row, AnalysisContext analysisContext) {
//                resVideoPoseImportReqs.add(row);
//            }
//
//            @Override
//            public void doAfterAllAnalysed(AnalysisContext context) {
//
//            }
//
//        }).sheet(0).doRead();
//        return resVideoPoseImportReqs;
//    }
//
//    private static List<GraphPath> getPaths(Map<Node, List<Link>> linkMapByCurrent, Node startNode, Set<Node> endNodes, Set<Node> mainNodes, Integer minDuration, Integer maxDuration, Integer maxPathCount, boolean isLoop) {
//        //获取开始节点的所有链接、为了保证数据尽可能的多样性，防止数据全部为同一个link下的数据，为每个link单独生成部分数据,防止出现大量的相似数据
//        List<Link> startNodeLinks = linkMapByCurrent.get(startNode);
//        int oneLinkPathCount = maxPathCount / startNodeLinks.size();
//        Collections.shuffle(startNodeLinks);
//        List<GraphPath> allLinkPath = CollUtil.newArrayList();
//        List<Node> copyMainNodes = CollUtil.newArrayList(mainNodes);
//        //循环为每个link生成数据
//        for (Link startNodeLink : startNodeLinks) {
//            //创建图
//            Graph<Node, DefaultWeightedEdge> graph = new DefaultDirectedWeightedGraph<>(DefaultWeightedEdge.class);
//            // 添加图节点
//            if (!mainNodes.contains(startNode)) {
//                graph.addVertex(startNode);
//            }
//
//            Collections.shuffle(copyMainNodes);
//            for (Node mainNode : copyMainNodes) {
//                graph.addVertex(mainNode);
//            }
//            //先生成开始节点的link
//            DefaultWeightedEdge defaultWeightedEdge = graph.addEdge(startNodeLink.getCurrentNode(), startNodeLink.getNextNode());
//            graph.setEdgeWeight(defaultWeightedEdge, startNodeLink.getPoseTime());
//            for (Link nodeLink : startNodeLinks) {
//                if (nodeLink != startNodeLink) {
//                    DefaultWeightedEdge defaultWeightedEdge2 = graph.addEdge(nodeLink.getCurrentNode(), nodeLink.getNextNode());
//                    graph.setEdgeWeight(defaultWeightedEdge2, nodeLink.getPoseTime());
//                }
//            }
//            //生成其他节点之间的关系
//            List<Map.Entry<Node, List<Link>>> entries = CollUtil.newArrayList(linkMapByCurrent.entrySet().stream().filter(e -> e.getKey() != startNode && mainNodes.contains(e.getKey())).collect(Collectors.toList()));
//            Collections.shuffle(entries);
//            entries.stream().forEach(e -> {
//                Node currentNode = e.getKey();
//                ArrayList<Link> links = CollUtil.newArrayList(e.getValue());
//                Collections.shuffle(links);
//                for (Link link : links) {
//                    DefaultWeightedEdge defaultWeightedEdge2 = graph.addEdge(currentNode, link.getNextNode());
//                    graph.setEdgeWeight(defaultWeightedEdge2, link.getPoseTime());
//                }
//            });
//            // 使用AllDirectedPaths类获取所有路径
//            WorkoutGenerated200Algorithm<Node, DefaultWeightedEdge> allPaths = new WorkoutGenerated200Algorithm<>(graph);
//            List<GraphPath<Node, DefaultWeightedEdge>> oneLinkPaths = allPaths.getAllPaths(CollUtil.newHashSet(startNode), mainNodes, endNodes, !isLoop, 50, minDuration, maxDuration - startNode.getPoseTime(), oneLinkPathCount);
//            allLinkPath.addAll(oneLinkPaths);
//        }
//        return allLinkPath;
//    }
//
//
//    @Data
//    static class Node {
//        private Integer id;
//        private String name;
//        private Integer poseTime;
//        private Boolean isStart;
//        private Boolean isEnd;
//        private String difficulty;
//
//        @Override
//        public String toString() {
//            return "Node{" +
//                    "name='" + name + '\'' +
//                    '}';
//        }
//    }
//
//    @Data
//    static
//    class Link {
//        private Node currentNode;
//        private Node nextNode;
//        private Integer poseTime;
//    }
//}
