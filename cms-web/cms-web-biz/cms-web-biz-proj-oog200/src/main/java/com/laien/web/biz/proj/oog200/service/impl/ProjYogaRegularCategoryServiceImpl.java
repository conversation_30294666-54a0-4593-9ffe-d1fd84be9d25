package com.laien.web.biz.proj.oog200.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog200.enums.YogaRegularCategoryTypeEnum;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularCategory;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaRegularCategoryMapper;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularCategoryVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaRegularCategoryService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * oog200 workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Service
public class ProjYogaRegularCategoryServiceImpl extends ServiceImpl<ProjYogaRegularCategoryMapper, ProjYogaRegularCategory> implements IProjYogaRegularCategoryService {

    @Override
    public List<ProjYogaRegularCategoryVO> query(Integer projId) {
        LambdaQueryWrapper<ProjYogaRegularCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaRegularCategory::getProjId, projId)
                .eq(ProjYogaRegularCategory::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjYogaRegularCategory> categoryList = baseMapper.selectList(wrapper);
        List<ProjYogaRegularCategoryVO> categoryListVO = new ArrayList<>();
        if (CollUtil.isEmpty(categoryList)) {
            return categoryListVO;
        }
        for (ProjYogaRegularCategory category : categoryList) {
            ProjYogaRegularCategoryVO categoryVO = new ProjYogaRegularCategoryVO();
            BeanUtils.copyProperties(category, categoryVO);
            YogaRegularCategoryTypeEnum type = category.getType();
            if (null != type) {
                categoryVO.setTypeName(type.getDisplayName());
            }
            categoryListVO.add(categoryVO);
        }
        return categoryListVO;
    }

    @Override
    public List<ProjYogaRegularCategoryVO> query(Set<Integer> idSet) {
        if(CollUtil.isEmpty(idSet)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjYogaRegularCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseModel::getId,idSet);
        List<ProjYogaRegularCategory> categoryList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(categoryList)){
            return new ArrayList<>();
        }
        List<ProjYogaRegularCategoryVO> categoryListVO = new ArrayList<>();
        for (ProjYogaRegularCategory category : categoryList) {
            ProjYogaRegularCategoryVO categoryVO = new ProjYogaRegularCategoryVO();
            BeanUtils.copyProperties(category, categoryVO);
            YogaRegularCategoryTypeEnum type = category.getType();
            if (null != type) {
                categoryVO.setTypeName(type.getDisplayName());
            }
            categoryListVO.add(categoryVO);
        }
        return categoryListVO;
    }
}
