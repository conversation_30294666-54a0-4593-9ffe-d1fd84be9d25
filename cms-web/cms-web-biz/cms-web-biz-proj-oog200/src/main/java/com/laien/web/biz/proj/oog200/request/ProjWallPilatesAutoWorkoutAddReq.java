package com.laien.web.biz.proj.oog200.request;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@Accessors(chain = true)
@TableName("proj_wall_pilates_auto_workout")
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="Wall pilates auto workout")
public class ProjWallPilatesAutoWorkoutAddReq {

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "Standing、Lying")
    private String position;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "videoIdList")
    private List<Integer> videoIdList;

    @ApiModelProperty(value = "模版id")
    private Integer projYogaAutoTemplateId;

}
