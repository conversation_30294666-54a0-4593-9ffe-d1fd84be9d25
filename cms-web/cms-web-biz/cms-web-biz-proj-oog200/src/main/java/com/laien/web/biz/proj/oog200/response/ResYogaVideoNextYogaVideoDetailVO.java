package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yoga video next 下一个视频
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video next 下一个视频", description = "yoga video next 下一个视频")
public class ResYogaVideoNextYogaVideoDetailVO {

    @ApiModelProperty(value = "下一个视频id")
    private Integer id;

    @ApiModelProperty(value = "下一个视频名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

}
