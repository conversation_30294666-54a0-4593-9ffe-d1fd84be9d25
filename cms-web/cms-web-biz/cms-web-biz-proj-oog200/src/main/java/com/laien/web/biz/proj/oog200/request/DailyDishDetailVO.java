package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.DishTypeEnum;
import com.laien.web.biz.proj.oog200.response.ProjDishListVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/12/31 16:12
 */
@Data
public class DailyDishDetailVO extends ProjDishListVO {

    @ApiModelProperty(value = "表示一个MealPlan中第几天")
    private Integer day;

    @ApiModelProperty(value = "表示一个Dish在当前的类型")
    private DishTypeEnum dishType;

}
