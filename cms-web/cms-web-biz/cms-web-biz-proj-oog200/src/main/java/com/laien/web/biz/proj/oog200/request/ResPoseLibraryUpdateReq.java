package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "修改pose请求", description = "修改pose请求")
public class ResPoseLibraryUpdateReq extends ResPoseLibraryAddReq {

    @ApiModelProperty(value = "数据id", required = true)
    @NotNull(message = "The pose ID cannot be empty", groups = Group1.class)
    private Integer id;
}
