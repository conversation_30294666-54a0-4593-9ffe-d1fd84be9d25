package com.laien.web.biz.proj.oog200.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * AutoWorkoutBasicInfoImportReq
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="AutoWorkoutBasicInfoImportReq对象", description="AutoWorkoutBasicInfoImportReq")
public class AutoWorkoutBasicInfoDownloadVO {

    @ExcelProperty(value = "Image Name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名字")
    private String name;

    @ExcelProperty(value = "Plan Type", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "planType")
    private String planTypeValue;

    @ExcelProperty(value = "Point (Goal/Target)", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "图片用途，Upper Body,Abs & Core,Lower Body,Upper Body+Abs & Core,Abs & Core+Lower Body,Fullbody,Learn Yoga Basics,Mindfulness,Weight Loss,Improve Flexibility")
    private String pointValue;

    @ExcelProperty(value = "Difficulty", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "difficulty")
    private String difficultyValue;

    @ApiModelProperty(value = "Auto Workout ID,多个用英文逗号分隔")
    @ExcelProperty(value = "Auto Workout ID")
    private String workoutId;

    @ApiModelProperty(value = "Cover Image 封面图")
    @ExcelProperty(value = "Cover Image")
    private String coverImage;

    @ApiModelProperty(value = "Detail Image 详情图")
    @ExcelProperty(value = "Detail Image")
    private String detailImage;

    @ApiModelProperty(value = "Cover Image (Male) 封面图")
    @ExcelProperty(value = "Cover Image (Male)")
    private String coverImageMale;

    @ApiModelProperty(value = "Complete Image")
    @ExcelProperty(value = "Complete Image")
    private String completeImage;

    @ExcelProperty(value = "Image ID")
    private Integer id;

}
