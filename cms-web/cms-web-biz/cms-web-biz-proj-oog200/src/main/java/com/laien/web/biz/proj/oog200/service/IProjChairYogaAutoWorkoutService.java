package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTask;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaAutoWorkoutPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/24 17:51
 */
public interface IProjChairYogaAutoWorkoutService extends IService<ProjChairYogaAutoWorkout> {

    void generateWorkout(ProjYogaAutoWorkoutTask workoutTask);

    void saveWorkout(ProjChairYogaAutoWorkoutAddReq workoutAddReq);

    void deleteByIds(Collection<Integer> workoutIds);

    void updateEnableByIds(Collection<Integer> workoutIds);

    void updateDisableByIds(Collection<Integer> workoutIds);

    void update(ProjChairYogaAutoWorkoutUpdateReq workoutUpdateReq);

    void batchUpdate(ProjChairYogaAutoWorkoutBatchUpdateReq batchUpdateReq);

    ProjChairYogaAutoWorkoutDetailVO findDetailById(Integer workoutId);

    Collection<ProjChairYogaAutoWorkout> listByWorkoutIds(Collection<Integer> workoutIds);

    List<CountBO> listWorkoutCountByTemplate(Collection<Integer> templateIds);

    PageRes<ProjChairYogaAutoWorkoutPageVO> page(ProjChairYogaAutoWorkoutPageReq pageReq);

    PageRes<ProjChairYogaVideo> page4RelationVideo(ProjChairYogaAutoWorkoutPageReq pageReq);
}
