package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaQuote;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuoteAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuotePageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaQuoteUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaQuoteDetailVO;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * yoga名言警句 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface IProjYogaQuoteService extends IService<ProjYogaQuote> {

    void save(ProjYogaQuoteAddReq req, Integer projId);

    void update(ProjYogaQuoteUpdateReq req, Integer projId);

    ProjYogaQuoteDetailVO findDetailById(Integer id);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);

    PageRes<ProjYogaQuoteDetailVO> page(ProjYogaQuotePageReq pageReq, Integer projId);

    List<String> importByExcel(InputStream inputStream, Integer projId);
}
