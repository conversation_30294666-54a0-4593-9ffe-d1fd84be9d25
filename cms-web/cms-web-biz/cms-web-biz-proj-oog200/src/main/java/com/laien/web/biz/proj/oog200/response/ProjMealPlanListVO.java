package com.laien.web.biz.proj.oog200.response;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/12/31 16:46
 */
@Data
public class ProjMealPlanListVO extends BaseModel {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "排序")
    private Integer sorted;

    @ApiModelProperty(value = "用于APP端，显示一个固定的标签")
    private Boolean replacementTag;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "标签值，以英文逗号做分隔，如A,B,C")
    private String keywords;

    private Integer status;

    @ApiModelProperty(value = "plan天数")
    private Integer days;

}
