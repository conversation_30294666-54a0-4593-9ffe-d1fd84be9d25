package com.laien.web.biz.proj.oog200.controller;

import cn.hutool.core.collection.CollUtil;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoConnection;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoAudioDetailVO;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseVideoPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.PROJ_YOGA_POSE_VIDEO_FRONT;


/**
 * Author:  hhl
 * Date:  2024/7/31 16:13
 */
@Api(tags = "项目管理: Pose Library Video")
@RestController
@RequestMapping("/proj/yogaPoseVideo")
public class ProjYogaPoseVideoController extends ResponseController {

    @Resource
    IProjYogaPoseVideoService poseVideoService;

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaPoseVideoPageVO>> page(ProjYogaPoseVideoPageReq pageReq) {

        PageRes<ProjYogaPoseVideoPageVO> pageRes = poseVideoService.selectYogaPoseVideoPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaPoseVideoAddReq poseVideoAddReq) {
        poseVideoService.saveYogaPoseVideo(poseVideoAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaPoseVideoUpdateReq poseVideoUpdateReq) {
        poseVideoService.updateYogaPoseVideo(poseVideoUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaPoseVideoDetailVO> detail(@PathVariable Integer id) {
        ProjYogaPoseVideoDetailVO videoDetailVO = poseVideoService.getDetailById(id);
        return succ(videoDetailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }

        //校验切片任务是否成功
        List<Integer> idList = idListReq.getIdList();
        List<TaskResourceSection> taskList = taskResourceSectionService.query(PROJ_YOGA_POSE_VIDEO_FRONT.getTableName(), PROJ_YOGA_POSE_VIDEO_FRONT.getEntityFieldName(), idList);
        if (CollUtil.isEmpty(taskList)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }

        List<Integer> completedIdList = TaskResourceSectionUtil.getCompletedIdList(taskList);
        if (CollUtil.isEmpty(completedIdList)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }

        List<Integer> failedIds = idList.stream()
                .filter(id -> !completedIdList.contains(id))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(failedIds)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + failedIds);
        }

        poseVideoService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }
        poseVideoService.updateDisableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }
        poseVideoService.deleteByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量导入Pose Video信息")
    @PostMapping("/v1/importByExcel")
    public ResponseResult<List<String>> importPoseVideo(@RequestParam("file") MultipartFile excel) {

        return succ(poseVideoService.importPoseVideo(excel));
    }

    @ApiOperation(value = "批量导入Pose Transition Connection 信息")
    @PostMapping("/v2/importByExcel")
    public ResponseResult<List<String>> importPoseVideoConnection(@RequestParam("file") MultipartFile excel) {

        return succ(poseVideoService.importPoseConnection(excel));
    }

    @ApiOperation(value = "获取audio配置")
    @GetMapping("/audio/config")
    public ResponseResult<List<ProjYogaPoseVideoAudioDetailVO>> getAudioConfig() {

        return succ(poseVideoService.listAudioConfig4PoseVideo());
    }

}
