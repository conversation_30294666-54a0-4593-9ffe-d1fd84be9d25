package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Data
public class ProjYogaUserPageReq extends PageReq {

    @ApiModelProperty(value = "用户Id", notes = "精确匹配")
    private Integer id;

    @ApiModelProperty(value = "第三方支付平台Id", notes = "精确匹配")
    private String adaptyId;

    @ApiModelProperty(value = "个人邀请码，保证唯一", notes = "精确匹配")
    private String inviteCode;

}
