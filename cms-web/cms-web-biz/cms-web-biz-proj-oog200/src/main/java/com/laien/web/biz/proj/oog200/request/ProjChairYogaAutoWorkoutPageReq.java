package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/9/29 14:05
 */
@Data
public class ProjChairYogaAutoWorkoutPageReq extends PageReq {

    @ApiModelProperty(value = "workout id")
    private Integer id;

    @ApiModelProperty(value = " 1-小于10 ，2-10-20，3-大于20")
    private Integer time;

    @ApiModelProperty(value = "workout 名字")
    private String name;

    @ApiModelProperty(value = "锻炼部位，示例值 Upper Body 、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "难度，可选值有 Newbie,Beginner,Intermediate,Advanced")
    private String difficulty;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 ")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "生成任务id")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "生成模板id")
    private Integer projYogaAutoWorkoutTemplateId;

    @ApiModelProperty(value = "模板状态 0草稿 1启用 2停用")
    private Integer templateStatus;
}
