package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 播放列表音乐关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPlaylistMusic对象", description="播放列表音乐关联表")
public class ProjYogaPlaylistRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "播放列表id")
    private Integer projYogaPlaylistId;

    @ApiModelProperty(value = "音乐id")
    private Integer projYogaMusicId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "music展示名称")
    private String displayName;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

}
