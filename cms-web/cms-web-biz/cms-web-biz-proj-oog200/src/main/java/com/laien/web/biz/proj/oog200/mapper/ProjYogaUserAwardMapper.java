package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAward;
import com.laien.web.biz.proj.oog200.entity.ProjYogaUserAward;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/9
 */
public interface ProjYogaUserAwardMapper extends BaseMapper<ProjYogaUserAward> {

    @Select(value = "select award.* from proj_yoga_user_award ua " +
            "inner join proj_yoga_award award on ua.award_id = award.id " +
            "where ua.user_id = #{userId} and ua.del_flag = 0 ")
    List<ProjYogaAward> listByUserId(Integer userId);

}
