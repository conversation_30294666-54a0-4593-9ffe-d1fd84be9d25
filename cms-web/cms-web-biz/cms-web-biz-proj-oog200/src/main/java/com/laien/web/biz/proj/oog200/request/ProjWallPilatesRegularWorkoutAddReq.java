package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * note: WallPilatesRegularWorkout新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "WallPilatesRegularWorkout新增", description = "WallPilatesRegularWorkout新增")
public class ProjWallPilatesRegularWorkoutAddReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "语种")
    private String[] languageArr;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "video id list")
    private List<Integer> videoIdList;


    @ApiModelProperty(value = "特殊人群不可使用的 数组 All Good、Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String[] specialLimitArr;

    @ApiModelProperty(value = "取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates和Other")
    private String[] yogaTypeArr;

    @ApiModelProperty(value = "yogaDataSourceList")
    private List<YogaDataSourceEnum> yogaDataSourceList;

    @ApiModelProperty(value = "yogaDataSourceList")
    private List<Integer> categoryIdList;


}
