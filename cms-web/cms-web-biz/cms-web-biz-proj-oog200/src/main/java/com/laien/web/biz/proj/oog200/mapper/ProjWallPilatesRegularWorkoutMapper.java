package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesRegularWorkout;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.request.ProjWallPilatesRegularWorkoutPageReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * oog200 workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
public interface ProjWallPilatesRegularWorkoutMapper extends BaseMapper<ProjWallPilatesRegularWorkout> {

    List<Integer> page(@Param("page") Page<ProjWallPilatesRegularWorkout> page,
                       @Param("pageReq") ProjWallPilatesRegularWorkoutPageReq pageReq,
                       @Param("projId") Integer projId,
                       @Param("workoutType") YogaAutoWorkoutTemplateEnum workoutType);
}
