package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.dto.ProjYogaPoseVideoConnectionDTO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoConnection;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/8/1 17:36
 */
public interface ProjYogaPoseVideoConnectionMapper extends BaseMapper<ProjYogaPoseVideoConnection> {

    @Select(value = "select distinct video1.name as videoName, video2.name as nextVideoName from proj_yoga_pose_video_connection con \n" +
            "inner join proj_yoga_pose_video video1 on con.proj_yoga_pose_video_id = video1.id \n" +
            "inner join proj_yoga_pose_video video2 on con.proj_yoga_pose_video_next_id = video2.id \n" +
            "where con.del_flag != 1 ")
    List<ProjYogaPoseVideoConnectionDTO> listValidConnect();
}
