package com.laien.web.biz.proj.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.laien.web.biz.proj.oog200.entity.ProjAutoWorkoutBasicInfo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseWorkout;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoAddReq;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoListReq;
import com.laien.web.biz.proj.oog200.request.ProjAutoWorkoutBasicInfoUpdateReq;
import com.laien.web.biz.proj.oog200.response.AutoWorkoutBasicInfoDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjAutoWorkoutBasicInfoDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjAutoWorkoutBasicInfoService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_image 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Api(tags = "项目管理:auto workout basic info")
@RestController
@RequestMapping("/proj/autoWorkoutBasicInfo")
public class ProjAutoWorkoutBasicInfoController extends ResponseController {

    @Resource
    private IProjAutoWorkoutBasicInfoService projAutoWorkoutBasicInfoService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjAutoWorkoutBasicInfoDetailVO>> list(ProjAutoWorkoutBasicInfoListReq listReq) {
        return succ(projAutoWorkoutBasicInfoService.list(listReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjAutoWorkoutBasicInfoAddReq infoReq) {
        projAutoWorkoutBasicInfoService.save(infoReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjAutoWorkoutBasicInfoUpdateReq infoReq) {
        projAutoWorkoutBasicInfoService.update(infoReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjAutoWorkoutBasicInfoDetailVO> detail(@PathVariable Integer id) {
        return succ(projAutoWorkoutBasicInfoService.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        projAutoWorkoutBasicInfoService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list can not be empty");
        }
        projAutoWorkoutBasicInfoService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        projAutoWorkoutBasicInfoService.sort(idListReq);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list can not be empty");
        }
        projAutoWorkoutBasicInfoService.deleteByIds(idList);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(projAutoWorkoutBasicInfoService.importByExcel(excel.getInputStream(), RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "下载excel")
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        List<AutoWorkoutBasicInfoDownloadVO> basicInfoDownloadList = projAutoWorkoutBasicInfoService.downloadList();
        if(CollUtil.isEmpty(basicInfoDownloadList)){
            return;
        }
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''AutoWorkoutBasicInfo.xlsx";
        String sheetName = "autoWorkoutBasicInfo";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), AutoWorkoutBasicInfoDownloadVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(basicInfoDownloadList);
    }

}
