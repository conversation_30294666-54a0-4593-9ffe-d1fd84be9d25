package com.laien.web.biz.proj.oog200.request;

import com.laien.common.oog200.enums.PlayTypeEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video class分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video class分页", description = "video class分页")
public class ResVideoClassPageReq extends PageReq {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private String orderBy;

    @ApiModelProperty(value = "video class类型")
    private YogaAutoWorkoutTemplateEnum type;

    @ApiModelProperty(value = "视频宽高比")
    private PlayTypeEnum playType;

//    @ApiModelProperty(value = "m3u8转换状态")
//    private Integer convertStatus;

}
