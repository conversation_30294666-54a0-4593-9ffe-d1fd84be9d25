package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.biz.proj.oog200.entity.ProjVideoGenerate;
import com.laien.web.biz.proj.oog200.entity.ProjVideoGenerateI18n;
import com.laien.web.biz.proj.oog200.entity.ProjVideoGenerateRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: video 生成结果
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video 生成结果", description = "video 生成结果")
public class VideoGeneratePackBO {

    @ApiModelProperty(value = "视频生成")
    private ProjVideoGenerate videoGenerate;
    @ApiModelProperty(value = "视频生成多语言")
    private List<ProjVideoGenerateI18n> videoGenerateI18nList;
    @ApiModelProperty(value = "字幕生成关系")
    private List<ProjVideoGenerateRelation> videoGenerateRelationList;

    public VideoGeneratePackBO(ProjVideoGenerate videoGenerate, List<ProjVideoGenerateI18n> videoGenerateI18nList, List<ProjVideoGenerateRelation> videoGenerateRelationList) {
        this.videoGenerate = videoGenerate;
        this.videoGenerateI18nList = videoGenerateI18nList;
        this.videoGenerateRelationList = videoGenerateRelationList;
    }

}
