package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.oog200.enums.DishTypeEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/12/31 14:57
 */
@Data
public class ProjMealPlanRelation extends BaseModel {

    private Integer projMealPlanId;

    private Integer day;

    private DishTypeEnum dishType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    private Integer projDishId;

}
