package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "ProjYogaAutoWorkoutTemplatePageVO", description = "ProjYogaAutoWorkoutTemplatePageVO")
public class ProjYogaAutoWorkoutTemplatePageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "模版名称")
    private String name;

    @ApiModelProperty(value = "模版描述")
    private String description;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "生成workout的最小时长，默认5，单位分钟")
    private Integer minTime;

    @ApiModelProperty(value = "生成workout的最大时长，默认35，单位分钟")
    private Integer maxTime;

    @ApiModelProperty(value = "template类型：Classic Yoga、Wall Pilates、Chair Yoga")
    private String type;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "已生成的workout 数量")
    private Integer workoutNum;

    @ApiModelProperty(value = "当前任务状态 0进行中 1失败 2成功")
    private Integer taskStatus;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

}
