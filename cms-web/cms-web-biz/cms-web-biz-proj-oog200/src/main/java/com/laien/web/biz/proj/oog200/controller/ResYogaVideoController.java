package com.laien.web.biz.proj.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideo;
import com.laien.web.biz.proj.oog200.request.ResYogaVideoAddReq;
import com.laien.web.biz.proj.oog200.request.ResYogaVideoPageReq;
import com.laien.web.biz.proj.oog200.request.ResYogaVideoUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResYogaVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ResYogaVideoDownloadVO;
import com.laien.web.biz.proj.oog200.response.ResYogaVideoPageVO;
import com.laien.web.biz.proj.oog200.service.IResYogaVideoService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.RES_YOGA_VIDEO_FRONT;


/**
 * <p>
 * 瑜伽视频 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Api(tags = "资源管理: yogaVideo")
@RestController
@RequestMapping("/res/yogaVideo")
public class ResYogaVideoController extends ResponseController {

    @Resource
    private IResYogaVideoService resYogaVideoService;
    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResYogaVideoPageVO>> page(ResYogaVideoPageReq pageReq) {
        PageRes<ResYogaVideoPageVO> pageRes = resYogaVideoService.selectYogaVideoPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResYogaVideoAddReq yogaVideoAddReq) {
        resYogaVideoService.saveYogaVideo(yogaVideoAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResYogaVideoUpdateReq yogaVideoUpdateReq) {
        resYogaVideoService.updateYogaVideo(yogaVideoUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResYogaVideoDetailVO> detail(@PathVariable Integer id) {
        ResYogaVideoDetailVO detailVO = resYogaVideoService.getYogaVideoDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        //校验切片任务是否成功
        List<TaskResourceSection> taskList = taskResourceSectionService.query(RES_YOGA_VIDEO_FRONT.getTableName(), RES_YOGA_VIDEO_FRONT.getEntityFieldName(), idList);
        if (CollUtil.isEmpty(taskList)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }
        List<Integer> completedIdList = TaskResourceSectionUtil.getCompletedIdList(taskList);
        if (CollUtil.isEmpty(completedIdList)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + idList);
        }
        List<Integer> failedIds = idList.stream()
                .filter(id -> !completedIdList.contains(id))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(failedIds)) {
            return fail("It cannot be enabled because video processing is not complete. failed id list:" + failedIds);
        }
        Set<Integer> failedIdSet = resYogaVideoService.updateEnableByIds(idList);
        if (!failedIdSet.isEmpty()) {
            String ids = StrUtil.join(",", failedIdSet);
            return fail("The following data(id: " + ids + ") has an incorrect value or status");
        }
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resYogaVideoService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resYogaVideoService.deleteByIds(idList);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(resYogaVideoService.importByExcel(excel.getInputStream()));
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/import")
    public ResponseResult<List<String>> importData(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(resYogaVideoService.importScriptByExcel(excel.getInputStream()));
    }

    @ApiOperation(value = "下载excel")
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        List<ResYogaVideoDownloadVO> videoList = resYogaVideoService.downloadList();
        if(CollUtil.isEmpty(videoList)){
            return;
        }
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''yogaVideo.xlsx";
        String sheetName = "yogaVideo";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ResYogaVideoDownloadVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(videoList);
    }

}
