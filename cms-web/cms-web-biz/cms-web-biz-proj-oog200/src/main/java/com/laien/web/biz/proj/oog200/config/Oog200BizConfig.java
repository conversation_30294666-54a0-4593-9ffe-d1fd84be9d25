package com.laien.web.biz.proj.oog200.config;

import com.laien.web.biz.proj.oog200.bo.Oog200VideoBO;
import com.laien.web.biz.proj.oog200.bo.Video200SysSoundBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * note: cms 业务相关配置
 *
 * <AUTHOR>
 */

@Configuration
@ConfigurationProperties(prefix = "cms.biz")
@Data
public class Oog200BizConfig {

    @ApiModelProperty(value = "oog200 Template Generate sys sound")
    private String templateSysSoundReady;
    @ApiModelProperty(value = "oog200 Template Generate sys sound")
    private String templateSysSoundBegin;
    @ApiModelProperty(value = "oog200 Template Generate sys sound")
    private String templateSysSoundNext;
    @ApiModelProperty(value = "oog200 Template Generate sys sound")
    private String templateSysSoundFinish;

    @ApiModelProperty(value = "oog200")
    private Video200SysSoundBO oog200;

    @ApiModelProperty(value = "oog200Video")
    private Oog200VideoBO oog200Video;

}
