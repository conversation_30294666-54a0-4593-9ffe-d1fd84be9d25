package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideoConnection;
import com.laien.web.biz.proj.oog200.mapper.ResYogaVideoConnectionMapper;
import com.laien.web.biz.proj.oog200.service.IResYogaVideoConnectionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 瑜伽视频 -关联链路表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class ResYogaVideoConnectionServiceImpl extends ServiceImpl<ResYogaVideoConnectionMapper, ResYogaVideoConnection> implements IResYogaVideoConnectionService {

    @Override
    public List<ResYogaVideoConnection> listByVideoIds(List<Integer> videoIds) {
        LambdaQueryWrapper<ResYogaVideoConnection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ResYogaVideoConnection::getResYogaVideoId, videoIds);
        return list(queryWrapper);
    }

    @Override
    public List<ResYogaVideoConnection> listByVideoNextIds(List<Integer> videoNextIds) {
        LambdaQueryWrapper<ResYogaVideoConnection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ResYogaVideoConnection::getResYogaVideoNextId, videoNextIds);
        return list(queryWrapper);
    }
}
