package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 教练表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCollectionTeacher对象", description="教练表")
public class ProjCollectionTeacher extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "avatar image url")
    private String avatarUrl;

    @ApiModelProperty(value = "teacher description")
    @TranslateField
    private String description;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;


}
