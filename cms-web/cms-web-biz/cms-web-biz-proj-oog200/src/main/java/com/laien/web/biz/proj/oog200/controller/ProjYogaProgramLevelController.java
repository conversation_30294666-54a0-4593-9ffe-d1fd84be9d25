package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.oog200.entity.ProjYogaProgramLevel;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramLevelUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramLevelPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaProgramLevelService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:13
 */
@Api(tags = "项目管理: yogaProgramLevel")
@RestController
@RequestMapping("/proj/yogaProgramLevel")
public class ProjYogaProgramLevelController extends ResponseController {

    @Resource
    private IProjYogaProgramLevelService programLevelService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaProgramLevelPageVO>> page(ProjYogaProgramLevelPageReq pageReq) {
        PageRes<ProjYogaProgramLevelPageVO> pageRes = programLevelService.selectProgramLevelPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaProgramLevelAddReq addReq) {
        programLevelService.saveProgramLevel(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaProgramLevelUpdateReq updateReq) {
        programLevelService.updateProgramLevel(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaProgramLevelDetailVO> detail(@PathVariable Integer id) {
        ProjYogaProgramLevelDetailVO detailVO = programLevelService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programLevelService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programLevelService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programLevelService.deleteByIds(idList);
        return succ();
    }

}
