package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjTemplateTask;

import java.util.List;

/**
 * <p>
 * template task Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
public interface ProjTemplateTaskMapper extends BaseMapper<ProjTemplateTask> {

    /**
     * 查询最后一次发布信息
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<ProjTemplateTask> selectLastTask(List<Integer> templateIdList);

}
