package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog200.enums.DishTypeEnum;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjDishCollection;
import com.laien.web.biz.proj.oog200.entity.ProjDishCollectionRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjDishCollectionMapper;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjDishCollectionDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjDishCollectionListVO;
import com.laien.web.biz.proj.oog200.response.ProjDishListVO;
import com.laien.web.biz.proj.oog200.service.IProjDishCollectionRelationService;
import com.laien.web.biz.proj.oog200.service.IProjDishCollectionService;
import com.laien.web.biz.proj.oog200.service.IProjDishService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/2 11:52
 */
@Slf4j
@Service
public class ProjDishCollectionServiceImpl extends ServiceImpl<ProjDishCollectionMapper, ProjDishCollection> implements IProjDishCollectionService {

    @Resource
    private IProjDishCollectionService dishCollectionService;

    @Resource
    private IProjDishCollectionRelationService collectionRelationService;

    @Resource
    private IProjDishService dishService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;


    @Override
    public List<ProjDishCollectionListVO> selectDishCollection(ProjDishCollectionListReq listReq) {

        LambdaQueryWrapper<ProjDishCollection> queryWrapper = wrapQueryWrapper(listReq);
        List<ProjDishCollection> dishCollectionList = list(queryWrapper);
        if (CollectionUtils.isEmpty(dishCollectionList)) {
            return Collections.emptyList();
        }

        return dishCollectionList.stream().map(dishCollection -> convert2ListVO(dishCollection)).collect(Collectors.toList());
    }

    private ProjDishCollectionListVO convert2ListVO(ProjDishCollection projDishCollection) {

        ProjDishCollectionListVO listVO = new ProjDishCollectionListVO();
        BeanUtils.copyProperties(projDishCollection, listVO);
        return listVO;
    }

    private LambdaQueryWrapper<ProjDishCollection> wrapQueryWrapper(ProjDishCollectionListReq pageReq) {

        LambdaQueryWrapper<ProjDishCollection> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjDishCollection::getName, pageReq.getName());
        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjDishCollection::getStatus, pageReq.getStatus());
        queryWrapper.orderByAsc(ProjDishCollection::getSorted);
        queryWrapper.orderByDesc(ProjDishCollection::getId);
        return queryWrapper;
    }

    @Override
    public void saveDishCollection(ProjDishCollectionAddReq addReq) {

        ProjDishCollection dishCollection = new ProjDishCollection();
        BeanUtils.copyProperties(addReq, dishCollection);
        checkMealPlan(addReq, null);

        dishCollection.setStatus(GlobalConstant.STATUS_DRAFT);
        dishCollection.setProjId(RequestContextUtils.getProjectId());
        save(dishCollection);
        projLmsI18nService.handleI18n(Collections.singletonList(dishCollection), RequestContextUtils.getProjectId());

        if (CollectionUtils.isEmpty(addReq.getDailyList())) {
            return;
        }
        List<ProjDishCollectionRelation> collectionRelations = addReq.getDailyList().stream().map(dish -> wrapCollRelation(dish, dishCollection.getId(), dishCollection.getProjId())).collect(Collectors.toList());
        collectionRelationService.saveBatch(collectionRelations);
    }

    private void checkMealPlan(ProjDishCollectionAddReq dishCollectionAddReq, Integer id) {

        LambdaQueryWrapper<ProjDishCollection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjDishCollection::getName, dishCollectionAddReq.getName())
                .eq(ProjDishCollection::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjDishCollection::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Dish collection name exists.");
        }

        LambdaQueryWrapper<ProjDishCollection> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjDishCollection::getEventName, dishCollectionAddReq.getEventName())
                .eq(ProjDishCollection::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjDishCollection::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Dish collection event name exists.");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateDishCollection(ProjDishCollectionUpdateReq updateReq) {

        ProjDishCollection dishCollection = this.getById(updateReq.getId());
        if (Objects.isNull(dishCollection)) {
            throw new BizException("Can't find dish collection.");
        }

        checkMealPlan(updateReq, updateReq.getId());
        BeanUtils.copyProperties(updateReq, dishCollection);
        updateById(dishCollection);
        updateNewTime(updateReq);

        projLmsI18nService.handleI18n(Collections.singletonList(dishCollection), RequestContextUtils.getProjectId());
        handleCollectionRelation(updateReq);
    }

    private void handleCollectionRelation(ProjDishCollectionUpdateReq updateReq) {

        collectionRelationService.deleteByDishCollectionId(updateReq.getId());
        if (CollectionUtils.isEmpty(updateReq.getDailyList())) {
            return;
        }

        List<ProjDishCollectionRelation> collectionRelations = updateReq.getDailyList().stream().map(dish -> wrapCollRelation(dish, updateReq.getId(), RequestContextUtils.getProjectId())).collect(Collectors.toList());
        collectionRelationService.saveBatch(collectionRelations);
    }

    private ProjDishCollectionRelation wrapCollRelation(DailyDishDetailVO dailyDish, Integer collectionId, Integer projId) {

        ProjDishCollectionRelation collectionRelation = new ProjDishCollectionRelation();
        collectionRelation.setProjDishId(dailyDish.getId());
        collectionRelation.setProjId(projId);
        collectionRelation.setDishType(dailyDish.getDishType());
        collectionRelation.setProjDishCollectionId(collectionId);
        return collectionRelation;
    }

    private void updateNewTime(ProjDishCollectionUpdateReq updateReq) {

        LambdaUpdateWrapper<ProjDishCollection> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjDishCollection::getNewStartTime, updateReq.getNewStartTime());
        updateWrapper.set(ProjDishCollection::getNewEndTime, updateReq.getNewEndTime());
        updateWrapper.eq(ProjDishCollection::getId, updateReq.getId());
        this.update(updateWrapper);
    }

    @Override
    public ProjDishCollectionDetailVO getDetailById(Integer dishCollectionId) {

        ProjDishCollection dishCollection = this.getById(dishCollectionId);
        if (Objects.isNull(dishCollection)) {
            throw new BizException("Can't find dish collection.");
        }

        ProjDishCollectionDetailVO collectionDetailVO = new ProjDishCollectionDetailVO();
        BeanUtils.copyProperties(dishCollection, collectionDetailVO);

        List<DailyDishDetailVO> dishList = getDishList(dishCollectionId);
        collectionDetailVO.setDailyList(dishList);
        return collectionDetailVO;
    }

    private List<DailyDishDetailVO> getDishList(Integer dishCollectionId) {

        List<ProjDishCollectionRelation> dishCollectionRelationList = collectionRelationService.listByDishCollectionId(dishCollectionId);
        if(CollectionUtils.isEmpty(dishCollectionRelationList)) {
            return Collections.emptyList();
        }

        List<Integer> dishIds = dishCollectionRelationList.stream().map(ProjDishCollectionRelation::getProjDishId).collect(Collectors.toList());
        ProjDishListReq dishListReq = new ProjDishListReq();
        dishListReq.setDishIds(dishIds);

        List<ProjDishListVO> dishList = dishService.list(dishListReq, RequestContextUtils.getProjectId());
        if (CollectionUtils.isEmpty(dishList)) {
            return Collections.emptyList();
        }

        Map<Integer, ProjDishListVO> dishIdMap = dishList.stream().collect(Collectors.toMap(ProjDishListVO::getId, Function.identity(), (k1, k2) -> k1));
        return dishCollectionRelationList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjDishId())).map(relation -> convert2DailyVO(relation.getDishType(), dishIdMap.get(relation.getProjDishId()))).collect(Collectors.toList());
    }

    private DailyDishDetailVO convert2DailyVO(DishTypeEnum dishType, ProjDishListVO dishList) {

        DailyDishDetailVO dailyDishDetailVO = new DailyDishDetailVO();
        BeanUtils.copyProperties(dishList, dailyDishDetailVO);
        dailyDishDetailVO.setDishType(dishType);
        return dailyDishDetailVO;
    }

    @Override
    public void sort(IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        List<ProjDishCollection> dishCollections = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjDishCollection dishCollection = new ProjDishCollection();
            dishCollection.setSorted(i).setId(idList.get(i));
            dishCollections.add(dishCollection);
        }
        updateBatchById(dishCollections);
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjDishCollection> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjDishCollection::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjDishCollection::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjDishCollection::getId, idList);
        this.update(new ProjDishCollection(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjDishCollection> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjDishCollection::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjDishCollection::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjDishCollection::getId, idList);
        this.update(new ProjDishCollection(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjDishCollection> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjDishCollection::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjDishCollection::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjDishCollection::getId, idList);
        this.update(new ProjDishCollection(), wrapper);
    }

}
