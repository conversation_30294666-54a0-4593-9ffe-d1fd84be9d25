package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj yoga pose workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjYogaPoseWorkout对象", description = "proj yoga pose workout")
public class ProjYogaPoseWorkoutUpdateReq extends ProjYogaPoseWorkoutAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;
}
