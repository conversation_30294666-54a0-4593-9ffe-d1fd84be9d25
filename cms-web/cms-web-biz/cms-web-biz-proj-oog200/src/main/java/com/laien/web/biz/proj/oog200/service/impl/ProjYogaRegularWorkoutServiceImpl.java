package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.common.oog200.enums.ResUpdateStatusEnum;
import com.laien.common.oog200.enums.SpecialLimitEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaRegularWorkoutMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularCategoryVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutVideoDetailVO;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.async.IAsyncProcess;
import com.laien.web.frame.async.service.IAsyncService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * oog200 workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Slf4j
@Service
public class ProjYogaRegularWorkoutServiceImpl extends ServiceImpl<ProjYogaRegularWorkoutMapper, ProjYogaRegularWorkout> implements IProjYogaRegularWorkoutService {

    @Resource
    private IProjYogaRegularWorkoutVideoRelationService projYogaRegularWorkoutVideoRelationService;
    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;
    @Resource
    private IResYogaVideoConnectionService resYogaVideoConnectionService;
    @Resource
    private IResYogaVideoService resYogaVideoService;
    @Resource
    private IResTransitionService resTransitionService;
    @Resource
    private IProjYogaRegularWorkoutService regularWorkoutService;

    @Resource
    private IProjYogaRegularWorkoutAudioI18nService projYogaRegularWorkoutAudioI18nService;

    @Resource
    private IAsyncService asyncService;
    @Resource
    private IProjYogaRegularWorkoutCategoryRelationService projYogaRegularWorkoutCategoryRelationService;
    @Resource
    private IProjYogaRegularCategoryService projYogaRegularCategoryService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Override
    public PageRes<ProjYogaRegularWorkoutPageVO> selectWorkoutPage(ProjYogaRegularWorkoutPageReq pageReq) {
        Integer projId = RequestContextUtils.getProjectId();

        Page<ProjYogaRegularWorkout> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        List<Integer> idList = baseMapper.page(page, pageReq, projId, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA);
        if (CollUtil.isEmpty(idList)) {
            return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), new ArrayList<>());
        }
        LambdaQueryWrapper<ProjYogaRegularWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(BaseModel::getId, idList)
                .orderByDesc(BaseModel::getId);
        Page<ProjYogaRegularWorkout> workouPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        workouPage.setPages(page.getPages());
        List<ProjYogaRegularWorkout> workoutList = list(wrapper);
        workouPage.setRecords(workoutList);

        PageRes<ProjYogaRegularWorkoutPageVO> pageRes = PageConverter.convert(workouPage, ProjYogaRegularWorkoutPageVO.class);
        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = projYogaRegularWorkoutCategoryRelationService.query(idList, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA);
        Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap = categoryRelationList.stream().collect(Collectors.groupingBy(ProjYogaRegularWorkoutCategoryRelation::getWorkoutId));
        Set<Integer> categoryIdSet = categoryRelationList.stream().map(ProjYogaRegularWorkoutCategoryRelation::getProjYogaRegularCategoryId).collect(Collectors.toSet());
        List<ProjYogaRegularCategoryVO> categoryListVO = projYogaRegularCategoryService.query(categoryIdSet);
        Map<Integer, ProjYogaRegularCategoryVO> categoryMap = categoryListVO.stream()
                .collect(Collectors.toMap(ProjYogaRegularCategoryVO::getId, item -> item));
        convertWorkoutFieldValue(pageRes.getList(),categoryMap, categoryRelationMap, workouPage.getRecords());
        return pageRes;
    }

    private void convertWorkoutFieldValue(List<ProjYogaRegularWorkoutPageVO> pageVOList,
                                          Map<Integer, ProjYogaRegularCategoryVO> categoryMap,
                                          Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap,
                                          List<ProjYogaRegularWorkout> workoutList) {

        if (CollectionUtils.isEmpty(pageVOList)) {
            return;
        }
        Map<Integer, ProjYogaRegularWorkout> workoutMap = workoutList.stream().collect(Collectors.toMap(BaseModel::getId, item -> item));

        pageVOList.forEach(pageVO -> {
            Integer updateStatus = pageVO.getUpdateStatus();
            pageVO.setDisplayUpdateStatus(ResUpdateStatusEnum.SingletonHolder.getStatusMap().get(updateStatus));
            List<ProjYogaRegularCategoryVO> categoryList = getCategoryList(pageVO, categoryRelationMap, categoryMap);
            pageVO.setCategoryList(categoryList);
            ProjYogaRegularWorkout workout = workoutMap.get(pageVO.getId());
            pageVO.setYogaDataSourceList(YogaDataSourceEnum.convertToYogaDataSourceEnum(workout.getDataSources()));
        });
    }

    @Transactional
    @Override
    public void saveWorkout(ProjYogaRegularWorkoutAddReq yogaRegularWorkoutAddReq) {
        this.checkWorkout(yogaRegularWorkoutAddReq, null);
        ProjYogaRegularWorkout yogaRegularWorkout = new ProjYogaRegularWorkout();
        BeanUtils.copyProperties(yogaRegularWorkoutAddReq, yogaRegularWorkout);
        yogaRegularWorkout.setStatus(GlobalConstant.STATUS_DRAFT);
        Integer projId = RequestContextUtils.getProjectId();
        yogaRegularWorkout.setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus());
        yogaRegularWorkout.setProjId(projId);
        yogaRegularWorkout.setLanguage(MyStringUtil.getJoinWithComma(yogaRegularWorkoutAddReq.getLanguageArr()));
        String[] specialLimitArr = yogaRegularWorkoutAddReq.getSpecialLimitArr();
        String[] yogaTypeArr = yogaRegularWorkoutAddReq.getYogaTypeArr();
        yogaRegularWorkout.setSpecialLimit(MyStringUtil.getJoinWithComma(specialLimitArr));
        yogaRegularWorkout.setYogaType(MyStringUtil.getJoinWithComma(yogaTypeArr));
        yogaRegularWorkout.setDataSources(YogaDataSourceEnum.dataSourceListToString(yogaRegularWorkoutAddReq.getYogaDataSourceList()));
        this.save(yogaRegularWorkout);

        // 保存生成信息
        this.saveGenerateInfoAndRelation(yogaRegularWorkout.getId(), yogaRegularWorkoutAddReq.getVideoIdList(), projId);
        projYogaRegularWorkoutCategoryRelationService.saveRelation(yogaRegularWorkoutAddReq.getCategoryIdList(), YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA, yogaRegularWorkout.getId(), projId);
        lmsI18nService.handleI18n(Collections.singletonList(yogaRegularWorkout), projId);
    }

    @Transactional
    @Override
    public void updateWorkout(ProjYogaRegularWorkoutUpdateReq yogaRegularWorkoutUpdateReq, Integer projId) {
        Integer id = yogaRegularWorkoutUpdateReq.getId();
        ProjYogaRegularWorkout yogaRegularWorkoutFind = this.getById(id);
        if (Objects.isNull(yogaRegularWorkoutFind)) {
            throw new BizException("Data not found");
        }
        this.checkWorkout(yogaRegularWorkoutUpdateReq, id);

        ProjYogaRegularWorkout yogaRegularWorkout = new ProjYogaRegularWorkout();
        BeanUtils.copyProperties(yogaRegularWorkoutUpdateReq, yogaRegularWorkout);
        yogaRegularWorkout.setLanguage(MyStringUtil.getJoinWithComma(yogaRegularWorkoutUpdateReq.getLanguageArr()));
        String[] specialLimitArr = yogaRegularWorkoutUpdateReq.getSpecialLimitArr();
        String[] yogaTypeArr = yogaRegularWorkoutUpdateReq.getYogaTypeArr();
        yogaRegularWorkout.setSpecialLimit(MyStringUtil.getJoinWithComma(specialLimitArr));
        yogaRegularWorkout.setYogaType(MyStringUtil.getJoinWithComma(yogaTypeArr));
        yogaRegularWorkout.setDataSources(YogaDataSourceEnum.dataSourceListToString(yogaRegularWorkoutUpdateReq.getYogaDataSourceList()));
        this.updateById(yogaRegularWorkout);
        // 支持修改为null 值
        LambdaUpdateWrapper<ProjYogaRegularWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaRegularWorkout::getNewStartTime, yogaRegularWorkoutUpdateReq.getNewStartTime());
        updateWrapper.set(ProjYogaRegularWorkout::getNewEndTime, yogaRegularWorkoutUpdateReq.getNewEndTime());
        updateWrapper.set(ProjYogaRegularWorkout::getUpdateStatus, ResUpdateStatusEnum.SUCCESS.getStatus());
        updateWrapper.set(StringUtils.isNotBlank(yogaRegularWorkoutUpdateReq.getUpdateUser()), ProjYogaRegularWorkout::getUpdateUser, yogaRegularWorkoutUpdateReq.getUpdateUser());
        updateWrapper.eq(ProjYogaRegularWorkout::getId, id);
        this.update(updateWrapper);

        // 先删除，在新增
        this.deleteRelation(id);
        // 保存生成信息
        this.saveGenerateInfoAndRelation(id, yogaRegularWorkoutUpdateReq.getVideoIdList(), projId);
        projYogaRegularWorkoutCategoryRelationService.saveRelation(yogaRegularWorkoutUpdateReq.getCategoryIdList(), YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA, yogaRegularWorkoutUpdateReq.getId(), projId);

        lmsI18nService.handleI18n(Collections.singletonList(yogaRegularWorkout), projId);
    }

    @Override
    public void batchUpdateWorkout(ProjYogaRegularWorkoutBatchUpdateReq batchUpdateReq) {

        if (Objects.isNull(batchUpdateReq) || CollectionUtils.isEmpty(batchUpdateReq.getRegularWorkoutIds())) {
            return;
        }

        Collection<ProjYogaRegularWorkout> workoutList = listByIds(batchUpdateReq.getRegularWorkoutIds());
        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        // 更新这一批次数据的状态
        workoutList.forEach(workout -> workout.setUpdateStatus(ResUpdateStatusEnum.UPDATE.getStatus()));
        updateBatchById(workoutList);

        // 异步更新workout
        Integer projId = RequestContextUtils.getProjectId();
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        asyncUpdateRes(workoutList, projId, userName);
    }

    private static List<ProjYogaRegularCategoryVO> getCategoryList(ProjYogaRegularWorkoutPageVO pageVO, Map<Integer, List<ProjYogaRegularWorkoutCategoryRelation>> categoryRelationMap, Map<Integer, ProjYogaRegularCategoryVO> categoryMap) {
        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = categoryRelationMap.getOrDefault(pageVO.getId(), new ArrayList<>());
        List<ProjYogaRegularCategoryVO> categoryList = new ArrayList<>();
        for (ProjYogaRegularWorkoutCategoryRelation relation : categoryRelationList) {
            ProjYogaRegularCategoryVO categoryVO = categoryMap.get(relation.getProjYogaRegularCategoryId());
            if (null != categoryVO) {
                categoryList.add(categoryVO);
            }
        }
        return categoryList;
    }

    private void asyncUpdateRes(Collection<ProjYogaRegularWorkout> workoutList, Integer projId, String operationUser) {

        try{
            asyncService.doSomethings(new IAsyncProcess() {
                @Override
                public void process() {
                    workoutList.forEach(workout -> {
                        try{
                            log.warn("Start to update res for yoga regular workout, workout id is {}.", workout.getId());
                            updateRes4Workout(workout.getId(), projId, operationUser);
                            log.warn("Finished to update res for yoga regular workout, workout id is {}.", workout.getId());
                        } catch (Exception ex) {
                            updateResStatus(workout.getId(), operationUser, ResUpdateStatusEnum.FAIL);
                            log.error("async update yoga regular workout failed, workout id is {}.", workout.getId());
                            log.warn(ex.getMessage(), ex);
                        }
                    });
                }
            });
        } catch (Exception exception) {
            log.warn(exception.getMessage(), exception);
        }
    }

    private boolean updateResStatus(Integer workoutId, String operationUser, ResUpdateStatusEnum statusEnum) {

        LambdaUpdateWrapper<ProjYogaRegularWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaRegularWorkout::getId, workoutId);
        updateWrapper.set(ProjYogaRegularWorkout::getUpdateUser, operationUser);
        updateWrapper.set(ProjYogaRegularWorkout::getUpdateStatus, statusEnum.getStatus());
        return update(new ProjYogaRegularWorkout(), updateWrapper);
    }

    private void updateRes4Workout(Integer workoutId, Integer projId, String operationUser) {

        List<ProjYogaRegularWorkoutVideoDetailVO> videoList = projYogaRegularWorkoutVideoRelationService.selectYogaRegularWorkoutVideos(workoutId);
        List<Integer> idList = videoList.stream().map(ProjYogaRegularWorkoutVideoDetailVO::getId).collect(Collectors.toList());
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            deleteRelation(workoutId);
            saveGenerateInfoAndRelation(workoutId, idList, projId);
            updateResStatus(workoutId, operationUser, ResUpdateStatusEnum.SUCCESS);
        });
    }

    /**
     * workout检查
     *
     * @param yogaRegularWorkoutAddReq yogaRegularWorkoutAddReq
     * @param id id
     */
    private void checkWorkout(ProjYogaRegularWorkoutAddReq yogaRegularWorkoutAddReq, Integer id) {
        LambdaQueryWrapper<ProjYogaRegularWorkout> queryWrapper = new LambdaQueryWrapper<>();
        Integer projId = Objects.isNull(yogaRegularWorkoutAddReq.getProjId()) ? RequestContextUtils.getProjectId() : yogaRegularWorkoutAddReq.getProjId();
        queryWrapper.eq(ProjYogaRegularWorkout::getName, yogaRegularWorkoutAddReq.getName())
                .eq(ProjYogaRegularWorkout::getProjId, projId)
                .ne(Objects.nonNull(id), ProjYogaRegularWorkout::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Workout name exists");
        }

        LambdaQueryWrapper<ProjYogaRegularWorkout> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjYogaRegularWorkout::getEventName, yogaRegularWorkoutAddReq.getEventName())
                .eq(ProjYogaRegularWorkout::getProjId, projId)
                .ne(Objects.nonNull(id), ProjYogaRegularWorkout::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Workout event name exists");
        }
    }

    private SpecialLimitEnum getSpecialLimitEnumByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (SpecialLimitEnum specialLimit : SpecialLimitEnum.values()) {
            if (specialLimit.getName().equals(name)) {
                return specialLimit;
            }
        }
        return null;
    }


    /**
     * 保存生成信息和关系
     */
    private void saveGenerateInfoAndRelation(Integer id, List<Integer> yogaVideoIdList, Integer projId) {
        ProjYogaAutoWorkoutServiceImpl.GenerateInfo generateInfo = projYogaAutoWorkoutService.getGenerateInfo(yogaVideoIdList, projId);
        ProjYogaRegularWorkout yogaRegularWorkout = new ProjYogaRegularWorkout();
        BeanUtils.copyProperties(generateInfo, yogaRegularWorkout);
        yogaRegularWorkout.setId(id);
        yogaRegularWorkout.setResYogaStartVideoId(generateInfo.getRelations().get(GlobalConstant.ZERO).getResYogaVideoId());
        Map<String, String> longJsonUrlMap = generateInfo.getLongJsonUrlMap();
        Map<String, String> shortJsonUrlMap = generateInfo.getShortJsonUrlMap();
        yogaRegularWorkout.setAudioLongJson(longJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE));
        yogaRegularWorkout.setAudioShortJson(shortJsonUrlMap.get(GlobalConstant.DEFAULT_LANGUAGE));
        // 以页面设置为准
        yogaRegularWorkout.setCalorie(null);
        this.updateById(yogaRegularWorkout);

        List<ProjYogaRegularWorkoutVideoRelation> workoutVideoRelationList = generateInfo.getRelations().stream()
                .map(o-> new ProjYogaRegularWorkoutVideoRelation().setResYogaVideoId(o.getResYogaVideoId())
                        .setResYogaVideoConnectionId(o.getResYogaVideoConnectionId())
                        .setRealVideoDuration(o.getRealVideoDuration())
                        .setRealTransitionDuration(o.getRealTransitionDuration())
                        .setProjYogaRegularWorkoutId(id))
                .collect(Collectors.toList());
        projYogaRegularWorkoutVideoRelationService.saveBatch(workoutVideoRelationList);

        saveAudioI18n(id, generateInfo.longJsonUrlMap, generateInfo.getShortJsonUrlMap(), projId);
    }

    private void saveAudioI18n(Integer workoutId, Map<String, String> longJsonUrlMap, Map<String, String> shortJsonUrlMap, Integer projId) {
        projYogaRegularWorkoutAudioI18nService.delete(workoutId, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA);
        Map<String, ProjYogaRegularWorkoutAudioI18n> workoutAudioI18nMap = new HashMap<>();
        longJsonUrlMap.forEach((language, longJsonUrl) -> {
            ProjYogaRegularWorkoutAudioI18n audioI18n = workoutAudioI18nMap.getOrDefault(language, new ProjYogaRegularWorkoutAudioI18n());
            audioI18n.setWorkoutId(workoutId)
                    .setProjId(projId)
                    .setLanguage(language)
                    .setWorkoutType(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA)
                    .setAudioLongJsonUrl(longJsonUrl)
                    .setAudioShortJsonUrl(shortJsonUrlMap.get(language));
            workoutAudioI18nMap.put(language, audioI18n);
        });
        projYogaRegularWorkoutAudioI18nService.saveBatch(workoutAudioI18nMap.values());
    }

    /**
     * 删除workout 关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        LambdaUpdateWrapper<ProjYogaRegularWorkoutVideoRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaRegularWorkoutVideoRelation::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjYogaRegularWorkoutVideoRelation::getProjYogaRegularWorkoutId, id);
        projYogaRegularWorkoutVideoRelationService.update(new ProjYogaRegularWorkoutVideoRelation(), wrapper);
    }

    @Override
    public ProjYogaRegularWorkoutDetailVO getWorkoutDetail(Integer id) {
        ProjYogaRegularWorkout yogaRegularWorkoutFind = this.getById(id);
        if (Objects.isNull(yogaRegularWorkoutFind)) {
            throw new BizException("Data not found");
        }

        ProjYogaRegularWorkoutDetailVO detailVO = new ProjYogaRegularWorkoutDetailVO();
        BeanUtils.copyProperties(yogaRegularWorkoutFind, detailVO);
        detailVO.setLanguageArr(MyStringUtil.getSplitWithComa(yogaRegularWorkoutFind.getLanguage()));
        detailVO.setSpecialLimitArr(MyStringUtil.getSplitWithComa(yogaRegularWorkoutFind.getSpecialLimit()));
        detailVO.setYogaTypeArr(MyStringUtil.getSplitWithComa(yogaRegularWorkoutFind.getYogaType()));
        detailVO.setYogaDataSourceList(YogaDataSourceEnum.convertToYogaDataSourceEnum(yogaRegularWorkoutFind.getDataSources()));

        List<ProjYogaRegularWorkoutCategoryRelation> categoryRelationList = projYogaRegularWorkoutCategoryRelationService.query(Collections.singletonList(id), YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA);
        Set<Integer> categoryIdSet = categoryRelationList.stream().map(ProjYogaRegularWorkoutCategoryRelation::getProjYogaRegularCategoryId).collect(Collectors.toSet());
        List<ProjYogaRegularCategoryVO> categoryListVO = projYogaRegularCategoryService.query(categoryIdSet);
        if (CollUtil.isNotEmpty(categoryListVO)) {
            List<Integer> categoryIdList = categoryListVO.stream().map(ProjYogaRegularCategoryVO::getId).collect(Collectors.toList());
            detailVO.setCategoryIdList(categoryIdList);
        }
        List<ProjYogaRegularWorkoutVideoDetailVO> videoList = projYogaRegularWorkoutVideoRelationService.selectYogaRegularWorkoutVideos(id);
        detailVO.setVideoList(videoList);

        return detailVO;
    }

    @Transactional
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjYogaRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaRegularWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaRegularWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaRegularWorkout::getId, idList);
        this.update(new ProjYogaRegularWorkout(), wrapper);
    }

    @Transactional
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjYogaRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaRegularWorkout::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaRegularWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaRegularWorkout::getId, idList);
        this.update(new ProjYogaRegularWorkout(), wrapper);
    }

    @Transactional
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            LambdaUpdateWrapper<ProjYogaRegularWorkout> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjYogaRegularWorkout::getDelFlag, GlobalConstant.YES);
            wrapper.eq(ProjYogaRegularWorkout::getStatus, GlobalConstant.STATUS_DRAFT);
            wrapper.eq(ProjYogaRegularWorkout::getId, id);
            boolean flag = this.update(new ProjYogaRegularWorkout(), wrapper);
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }

    @Override
    public Integer getComputeDuration(List<Integer> idList) {
        Set<Integer> idSet = new HashSet<>(idList);
        List<ResYogaVideo> videoList = resYogaVideoService.listAllByIds(idSet);
        // videoMap, key:id,value:video
        Map<Integer, ResYogaVideo> videoMap = videoList.stream()
                .collect(Collectors.toMap(ResYogaVideo::getId, resYogaVideo -> resYogaVideo));

        // 校验video 数据是否存在
        idList.forEach(id -> Optional.ofNullable(videoMap.get(id)).orElseThrow(() -> new BizException("Video id(" + id + ") Data not found")));

        List<ResYogaVideoConnection> connectionResultList = new ArrayList<>();
        Set<Integer> connectionVideoIdSet = new HashSet<>();
        int size = idList.size();
        for (int i = 0; i < size; i++) {
            Integer id = idList.get(i);
            ResYogaVideoConnection connection = new ResYogaVideoConnection();
            connection.setResYogaVideoId(id);
            int next = i + 1;
            if (next < size) {
                Integer nextId = idList.get(next);
                connection.setResYogaVideoNextId(nextId);
                connectionVideoIdSet.add(id);
            }
            connectionResultList.add(connection);
        }

        Map<Integer, List<ResYogaVideoConnection>> connectionListMap;
        if (!connectionVideoIdSet.isEmpty()) {
            // 按照video id 分组
            connectionListMap = resYogaVideoConnectionService.list(new LambdaQueryWrapper<ResYogaVideoConnection>().in(ResYogaVideoConnection::getResYogaVideoId, connectionVideoIdSet))
                    .stream().collect(Collectors.groupingBy(ResYogaVideoConnection::getResYogaVideoId));
        } else {
            connectionListMap = new HashMap<>(GlobalConstant.ZERO);
        }

        Set<Integer> transitionIdSet = new HashSet<>();
        connectionResultList.forEach(connection -> {
            Integer id = connection.getResYogaVideoId();
            Integer nextId = connection.getResYogaVideoNextId();
            if (Objects.nonNull(nextId)) {
                // nextId为空不会用到过渡，不需要查询connection
                List<ResYogaVideoConnection> connectionList = connectionListMap.get(id);
                if (Objects.isNull(connectionList)) {
                    throw new BizException("Video id(" + id + ") transition data empty");
                }
                Optional<ResYogaVideoConnection> findConnection = connectionList.stream()
                        .filter(c -> Objects.equals(nextId, c.getResYogaVideoNextId()) )
                        .findFirst();
                findConnection.ifPresent(o -> {
                    connection.setResTransitionId(o.getResTransitionId());
                    transitionIdSet.add(o.getResTransitionId());
                });
                findConnection.orElseThrow(() -> new BizException("Video id(" + id + ", " + nextId + ") transition data not setting"));
            }
        });

        Map<Integer, ResTransition> resTransitionMap;
        if (!transitionIdSet.isEmpty()) {
            resTransitionMap = resTransitionService.list(
                            new LambdaQueryWrapper<ResTransition>().in(ResTransition::getId, transitionIdSet))
                    .stream().collect(Collectors.toMap(ResTransition::getId, resTransition -> resTransition));
        } else {
            resTransitionMap = new HashMap<>(GlobalConstant.ZERO);
        }

        int duration = 0;
        List<Integer> durationList = new ArrayList<>();
        for (ResYogaVideoConnection connection : connectionResultList) {
            ResYogaVideo yogaVideo = videoMap.get(connection.getResYogaVideoId());
            int videoLoopCount = new BigDecimal(yogaVideo.getPoseTime())
                    .divide(new BigDecimal(yogaVideo.getFrontVideoDuration()),0, RoundingMode.HALF_UP).intValue();
            int currentDuration;
            for (int i = 0; i < videoLoopCount; i++) {
                if (durationList.size() % 2 == 0) {
                    currentDuration = yogaVideo.getFrontVideoDuration();
                } else {
                    currentDuration = yogaVideo.getSideVideoDuration();
                }
                duration += currentDuration;
                durationList.add(currentDuration);
            }

            Integer transitionId = connection.getResTransitionId();
            if (Objects.nonNull(transitionId)) {
                ResTransition resTransition = resTransitionMap.get(transitionId);
                Optional.ofNullable(resTransition).orElseThrow(() ->
                        new BizException("Video id(" + connection.getResYogaVideoId() + ", " + connection.getResYogaVideoNextId() + ") transition data not found"));
                if (durationList.size() % 2 == 0) {
                    currentDuration = resTransition.getFrontVideoDuration();
                } else {
                    currentDuration = resTransition.getSideVideoDuration();
                }
                duration += currentDuration;
                durationList.add(currentDuration);
            }
        }

        return duration;
    }
    
}
