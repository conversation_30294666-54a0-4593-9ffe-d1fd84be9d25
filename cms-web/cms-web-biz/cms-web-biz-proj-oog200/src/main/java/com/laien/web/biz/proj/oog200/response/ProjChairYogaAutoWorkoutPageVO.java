package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Author:  hhl
 * Date:  2024/9/29 14:17
 */
@Data
public class ProjChairYogaAutoWorkoutPageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "难度，可选值有 Newbie,Be<PERSON><PERSON>,Intermediate,Advanced")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "资源更新状态，这里只供前端显示 Succeeded、Updating、Failed")
    private String displayUpdateStatus;

    @ApiModelProperty(value = "生成任务id")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "生成数量")
    private Integer generateNum;

    @ApiModelProperty(value = "是否清空之前的workout 1是 0否")
    private Integer cleanUp;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

}
