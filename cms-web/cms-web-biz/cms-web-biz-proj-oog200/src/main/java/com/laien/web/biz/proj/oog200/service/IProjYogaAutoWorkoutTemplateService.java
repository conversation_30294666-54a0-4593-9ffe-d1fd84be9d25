package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTemplate;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempWorkoutPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTemplateDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTemplatePageVO;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * yoga auto workout生成模版 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IProjYogaAutoWorkoutTemplateService extends IService<ProjYogaAutoWorkoutTemplate> {


    /**
     * 增加模版
     *
     * @param projYogaAutoWorkoutTemplateAddReq
     */
    void add(ProjYogaAutoWorkoutTemplateAddReq projYogaAutoWorkoutTemplateAddReq, Integer projId);

    /**
     * 修改模版
     *
     * @param projYogaAutoWorkoutTemplateUpdateReq
     */
    void update(ProjYogaAutoWorkoutTemplateUpdateReq projYogaAutoWorkoutTemplateUpdateReq, Integer projId);


    /**
     * 批量删除
     *
     * @param ids
     */
    void deleteByIds(Collection<Integer> ids);

    /**
     * 批量启用
     *
     * @param ids
     */
    void updateEnableByIds(Collection<Integer> ids);

    /**
     * 批量禁用
     *
     * @param ids
     */
    void updateDisableByIds(Collection<Integer> ids);

    /**
     * 查询列表
     *
     * @param pageReq
     * @param projId
     * @return
     */
    PageRes<ProjYogaAutoWorkoutTemplatePageVO> page(ProjYogaAutoWorkoutTemplatePageReq pageReq, Integer projId);

    /**
     * 查询template生成的workout列表
     *
     * @param pageReq
     * @param templateId
     * @return
     */
    PageRes<ProjYogaAutoWorkoutTempWorkoutPageVO> pageWorkout(ProjYogaAutoWorkoutTempWorkoutPageReq pageReq, Integer templateId);


    /**
     * 查询template-workout关联的video
     *
     * @param pageReq
     * @param workoutId
     * @return
     */
    PageRes<ProjYogaAutoWorkoutTempVideoPageVO> pageVideo(PageReq pageReq, Integer workoutId);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    ProjYogaAutoWorkoutTemplateDetailVO detail(Integer id);

    List<ProjYogaAutoWorkoutTemplate> find(Integer status);


    /**
     * 生成workout
     *
     * @param templateId                             模版id
     * @param projYogaAutoWorkoutTemplateGenerateReq 本次生成的额外参数
     */
    void generate(Integer templateId, ProjYogaAutoWorkoutTemplateGenerateReq projYogaAutoWorkoutTemplateGenerateReq);
}
