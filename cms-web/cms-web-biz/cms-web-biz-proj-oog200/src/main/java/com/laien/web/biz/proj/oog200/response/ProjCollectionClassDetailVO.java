package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.GoalEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * note: collection class 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collection class 详情", description = "collection class 详情")
public class ProjCollectionClassDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImg;

    @ApiModelProperty(value = "详情图")
    private String detailImg;

    @ApiModelProperty(value = "描述")
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "class list")
    private List<ProjCollectionClassVideoVO> classList;

    @ApiModelProperty(value = "教练 id")
    private Integer teacherId;

    @ApiModelProperty(value = "取值：Classic Yoga、Lazy Yoga、Somatic Yoga、Chair Yoga、Wall Pilates、Tai Chi、Face Yoga、Meditation和Other")
    private String[] yogaTypeArr;

    @ApiModelProperty(value = "类型")
    private YogaAutoWorkoutTemplateEnum type;

    @ApiModelProperty(value = "goal类型")
    private List<GoalEnum> goalList;
}
