package com.laien.web.biz.proj.oog200.bo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * Author:  hhl
 * Date:  2024/10/30 14:24
 */
@Data
@RefreshScope
@ApiModel(value = "200 chair yoga video 配置", description = "200 chair yoga video 生成时的配置")
public class Oog200VideoBO {

    private String chairYogaVideo4Seated;

    private String chairYogaVideo4Standing;

}
