package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaUser;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaUserMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaUserPageReq;
import com.laien.web.biz.proj.oog200.service.IProjYogaUserService;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Service
public class ProjYogaUserServiceImpl extends ServiceImpl<ProjYogaUserMapper, ProjYogaUser> implements IProjYogaUserService {

    @Override
    public PageRes<ProjYogaUser> pageQuery(ProjYogaUserPageReq pageReq) {

        LambdaQueryWrapper<ProjYogaUser> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(Objects.nonNull(pageReq.getId()), ProjYogaUser::getId, pageReq.getId());
        queryWrapper.like(StringUtils.isNotBlank(pageReq.getAdaptyId()), ProjYogaUser::getAdaptyId, pageReq.getAdaptyId());
        queryWrapper.like(StringUtils.isNotBlank(pageReq.getInviteCode()), ProjYogaUser::getInviteCode, pageReq.getInviteCode());
        queryWrapper.orderByDesc(ProjYogaUser::getId);

        Page<ProjYogaUser> awardPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaUser> iPage = this.page(awardPage, queryWrapper);
        PageRes<ProjYogaUser> pageRes = PageConverter.convert(iPage, ProjYogaUser.class);
        return pageRes;
    }
}
