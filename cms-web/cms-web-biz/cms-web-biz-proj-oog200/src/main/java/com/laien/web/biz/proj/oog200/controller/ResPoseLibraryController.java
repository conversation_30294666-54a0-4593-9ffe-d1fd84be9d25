package com.laien.web.biz.proj.oog200.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ResPoseLibrary;
import com.laien.web.biz.proj.oog200.request.PoseLibraryPageReq;
import com.laien.web.biz.proj.oog200.request.ResPoseLibraryAddReq;
import com.laien.web.biz.proj.oog200.request.ResPoseLibraryUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResPoseLibraryDetailVO;
import com.laien.web.biz.proj.oog200.service.IResPoseLibraryService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseCode;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Optional;

import static com.laien.web.frame.constant.GlobalConstant.*;

;

/**
 * <p>
 * pose表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Api(tags = "资源管理:poseLibrary")
@RestController
@RequestMapping("/res/poseLibrary")
public class ResPoseLibraryController extends ResponseController {

    @Resource
    private IResPoseLibraryService resPoseLibraryService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private IProjInfoService projInfoService;

    @ApiOperation(value = "查询pose列表(分页)")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResPoseLibrary>> list(PoseLibraryPageReq poseLibraryPageReq) {
        LambdaQueryWrapper<ResPoseLibrary> queryWrapper = new LambdaQueryWrapper<>();
        //筛选pose名称
        Optional.ofNullable(poseLibraryPageReq.getPoseName()).filter(StringUtils::isNotBlank).ifPresent(name -> queryWrapper.like(ResPoseLibrary::getPoseName, name.trim()));
        //筛选difficulty
        Optional.ofNullable(poseLibraryPageReq.getDifficulty()).filter(StringUtils::isNotBlank).ifPresent(difficulty -> queryWrapper.eq(ResPoseLibrary::getDifficulty, difficulty.trim()));
        //筛选position
        Optional.ofNullable(poseLibraryPageReq.getPosition()).filter(StringUtils::isNotBlank).ifPresent(position -> queryWrapper.eq(ResPoseLibrary::getPosition, position.trim()));
        //筛选focus
        Optional.ofNullable(poseLibraryPageReq.getFocusList()).filter(CollectionUtils::isNotEmpty).ifPresent((focusList) -> {
            queryWrapper.and(wrapper -> {
                focusList.stream().forEach(focus -> wrapper.or().like(ResPoseLibrary::getFocus, "|" + focus.trim() + "|"));
            });
        });
        //筛选status
        Optional.ofNullable(poseLibraryPageReq.getStatus()).ifPresent(status -> queryWrapper.eq(ResPoseLibrary::getStatus, status));
        //排序
        Optional<Integer> orderByPoseNameOp = Optional.ofNullable(poseLibraryPageReq.getOrderByPoseName());
        if (orderByPoseNameOp.isPresent() && Lists.newArrayList(ONE, TWO).contains(orderByPoseNameOp.get())) {
            if (orderByPoseNameOp.get() == ONE) {
                queryWrapper.orderByAsc(ResPoseLibrary::getPoseName);
            } else {
                queryWrapper.orderByDesc(ResPoseLibrary::getPoseName);
            }
        } else {
            queryWrapper.orderByDesc(ResPoseLibrary::getCreateTime);
        }
        Page<ResPoseLibrary> page = new Page<>(poseLibraryPageReq.getPageNum(), poseLibraryPageReq.getPageSize());
        resPoseLibraryService.page(page, queryWrapper);
        page.getRecords().stream().filter(pose -> StringUtils.isNotBlank(pose.getFocus())).forEach(resPoseLibrary -> resPoseLibrary.setFocus(Joiner.on(",").skipNulls().join(Splitter.on("|").omitEmptyStrings().trimResults().split(resPoseLibrary.getFocus()))));
        return succ(PageConverter.convert(page));
    }

    @ApiOperation(value = "查询pose详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResPoseLibraryDetailVO> detail(@PathVariable Integer id) {
        Optional.ofNullable(id).orElseThrow(() -> new BizException("The pose library ID cannot be empty"));
        ResPoseLibrary resPoseLibrary = resPoseLibraryService.getById(id);
        Optional.ofNullable(resPoseLibrary).orElseThrow(() -> new BizException("Pose library does not exist"));
        ResPoseLibraryDetailVO resPoseLibraryDetailVO = new ResPoseLibraryDetailVO();
        BeanUtils.copyProperties(resPoseLibrary, resPoseLibraryDetailVO);
        Optional.ofNullable(resPoseLibrary.getFocus()).filter(StringUtils::isNotBlank).ifPresent(focus -> {
            resPoseLibraryDetailVO.setFocusList(Splitter.on("|").omitEmptyStrings().trimResults().splitToList(focus));
        });
        return succ(resPoseLibraryDetailVO);
    }

    private ResponseResult<Void> useName(String poseName, Integer poseId) {
        ResPoseLibrary poseLibrary = resPoseLibraryService.getOne(new LambdaQueryWrapper<ResPoseLibrary>().eq(ResPoseLibrary::getPoseName, poseName).in(ResPoseLibrary::getStatus, Lists.newArrayList(STATUS_DRAFT, STATUS_ENABLE)).last("limit 1"));
        if (poseLibrary == null || (poseId != null && poseId.intValue() == poseLibrary.getId())) {
            return succ();
        } else {
            return fail("The pose library name already exists");
        }
    }

    @ApiOperation(value = "删除pose library（支持批量）")
    @PostMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> del(@RequestBody IdListReq idListReq) {
        Optional.ofNullable(idListReq.getIdList()).filter(CollectionUtils::isNotEmpty).ifPresent((idList) -> {
            LambdaUpdateWrapper<ResPoseLibrary> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ResPoseLibrary::getDelFlag, GlobalConstant.YES);
            wrapper.in(ResPoseLibrary::getId, idList);
            wrapper.in(ResPoseLibrary::getStatus, STATUS_DRAFT);
            resPoseLibraryService.update(new ResPoseLibrary(), wrapper);
        });
        return succ();
    }

    @ApiOperation(value = "启用/禁用（支持批量）")
    @PostMapping("/enable")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        Optional.ofNullable(idListReq.getIdList()).filter(CollectionUtils::isNotEmpty).ifPresent((idList) -> {
            LambdaUpdateWrapper<ResPoseLibrary> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ResPoseLibrary::getStatus, STATUS_ENABLE);
            updateWrapper.in(ResPoseLibrary::getId, idList);
            updateWrapper.in(ResPoseLibrary::getStatus, Lists.newArrayList(STATUS_DISABLE, STATUS_DRAFT));
            resPoseLibraryService.update(new ResPoseLibrary(), updateWrapper);
        });
        return succ();
    }

    @ApiOperation(value = "启用/禁用（支持批量）")
    @PostMapping("/disable")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        Optional.ofNullable(idListReq.getIdList()).filter(CollectionUtils::isNotEmpty).ifPresent((idList) -> {
            LambdaUpdateWrapper<ResPoseLibrary> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ResPoseLibrary::getStatus, STATUS_DISABLE);
            updateWrapper.in(ResPoseLibrary::getId, idListReq.getIdList());
            updateWrapper.in(ResPoseLibrary::getStatus, Lists.newArrayList(STATUS_ENABLE));
            resPoseLibraryService.update(new ResPoseLibrary(), updateWrapper);
        });
        return succ();
    }


    @ApiOperation(value = "增加")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@Validated(Group.class) @RequestBody ResPoseLibraryAddReq resPoseLibraryAddReq) {
        //校验poseName是否重复
        ResPoseLibrary poseLibrary = resPoseLibraryService.getOne(new LambdaQueryWrapper<ResPoseLibrary>().eq(ResPoseLibrary::getPoseName, resPoseLibraryAddReq.getPoseName()).last("limit 1"));
        if (poseLibrary != null) {
            return fail("The pose library name already exists");
        }
        //校验Sanskrit Name
        ResPoseLibrary poseLibraryBySanskrit = resPoseLibraryService.getOne(new LambdaQueryWrapper<ResPoseLibrary>().eq(ResPoseLibrary::getSanskritName, resPoseLibraryAddReq.getSanskritName()).last("limit 1"));
        if (poseLibraryBySanskrit != null) {
            return fail("The sanskrit name of this pose library is repeated");
        }
        ResPoseLibrary resPoseLibrary = new ResPoseLibrary();
        BeanUtils.copyProperties(resPoseLibraryAddReq, resPoseLibrary);
        if (CollectionUtils.isNotEmpty(resPoseLibraryAddReq.getFocusList())) {
            resPoseLibrary.setFocus("|" + Joiner.on("|").join(resPoseLibraryAddReq.getFocusList()) + "|");
        }
        resPoseLibrary.setStatus(STATUS_DRAFT);
        resPoseLibraryService.save(resPoseLibrary);

        projLmsI18nService.handleI18n(Collections.singletonList(resPoseLibrary), projInfoService.find(ProjCodeEnums.OOG200.getAppCode()));
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@Validated(Group.class) @RequestBody ResPoseLibraryUpdateReq resPoseLibraryUpdateReq) {
        Integer id = resPoseLibraryUpdateReq.getId();
        if (id == null) {
            return fail("The pose ID cannot be empty");
        }
        //查询原数据
        ResPoseLibrary poseLibrary = resPoseLibraryService.getById(id);
        if (poseLibrary == null) {
            return fail("The pose library with id " + id + " does not exist");
        }
        //校验poseName是否重复
        ResPoseLibrary poseLibraryByName = resPoseLibraryService.getOne(new LambdaQueryWrapper<ResPoseLibrary>().eq(ResPoseLibrary::getPoseName, resPoseLibraryUpdateReq.getPoseName()).last("limit 1"));
        if (poseLibraryByName != null && poseLibraryByName.getId().intValue() != id) {
            return fail("The pose library name already exists");
        }
        //校验Sanskrit Name
        ResPoseLibrary poseLibraryBySanskrit = resPoseLibraryService.getOne(new LambdaQueryWrapper<ResPoseLibrary>().eq(ResPoseLibrary::getSanskritName, resPoseLibraryUpdateReq.getSanskritName()).last("limit 1"));
        if (poseLibraryBySanskrit != null && poseLibraryBySanskrit.getId().intValue() != id) {
            return fail("The sanskrit name of this pose library is repeated");
        }

        BeanUtils.copyProperties(resPoseLibraryUpdateReq, poseLibrary);
        if (CollectionUtils.isNotEmpty(resPoseLibraryUpdateReq.getFocusList())) {
            poseLibrary.setFocus("|" + Joiner.on("|").join(resPoseLibraryUpdateReq.getFocusList()) + "|");
        } else {
            poseLibrary.setFocus("");
        }
        resPoseLibraryService.updateById(poseLibrary);

        projLmsI18nService.handleI18n(Collections.singletonList(poseLibrary), projInfoService.find(ProjCodeEnums.OOG200.getAppCode()));
        return succ();
    }

    private ResponseResult<Void> checkDataIntegrity(ResPoseLibraryAddReq resPoseLibraryAddReq, Integer musicId) {
        ResponseResult<Void> checkNameExist = useName(resPoseLibraryAddReq.getPoseName(), musicId);
        if (!(checkNameExist.getCode() == ResponseCode.SUCCESS.getCode())) {
            return checkNameExist;
        }
        //pose name
        resPoseLibraryAddReq.setPoseName(resPoseLibraryAddReq.getPoseName().trim());
        return succ();
    }

}
