package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjDishStepTip;
import com.laien.web.biz.proj.oog200.mapper.ProjDishStepTipMapper;
import com.laien.web.biz.proj.oog200.response.ProjDishStepTipVO;
import com.laien.web.biz.proj.oog200.service.IProjDishStepTipService;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Dish step tip 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Service
public class ProjDishStepTipServiceImpl extends ServiceImpl<ProjDishStepTipMapper, ProjDishStepTip> implements IProjDishStepTipService {

    @Override
    public void delete(Collection<Integer> dishIdCollection) {
        LambdaUpdateWrapper<ProjDishStepTip> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjDishStepTip::getProjDishId, dishIdCollection);
        baseMapper.delete(wrapper);
    }



    @Override
    public List<ProjDishStepTipVO> query(Integer dishId) {
        LambdaQueryWrapper<ProjDishStepTip> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjDishStepTip::getProjDishId, dishId)
                .orderByAsc(BaseModel::getId);
        List<ProjDishStepTip> tipList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(tipList)){
            return new ArrayList<>();
        }
        List<ProjDishStepTipVO> tipVOList = new ArrayList<>(tipList.size());
        for (ProjDishStepTip tip : tipList) {
            ProjDishStepTipVO tipVO = new ProjDishStepTipVO();
            BeanUtils.copyProperties(tip,tipVO);
            tipVOList.add(tipVO);
        }
        return tipVOList;
    }
}
