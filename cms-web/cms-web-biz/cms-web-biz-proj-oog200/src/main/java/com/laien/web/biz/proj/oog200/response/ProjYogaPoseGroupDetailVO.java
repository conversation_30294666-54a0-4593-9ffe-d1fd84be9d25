package com.laien.web.biz.proj.oog200.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseGroup对象", description="proj yoga pose grouping")
public class ProjYogaPoseGroupDetailVO {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "eventName")
    private String eventName;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "分组用途，例如today pose、training path")
    private String type;

    @ApiModelProperty(value = "group 彩色封面")
    private String groupImgLightUrl;

    @ApiModelProperty(value = "group 黑色封面")
    private String groupImgDarkUrl;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "yogaPoseWorkoutList")
    private List<ProjYogaPoseWorkoutListVO> yogaPoseWorkoutList;


}
