package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * note: yogaQuote新增
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "yogaQuote新增", description = "yogaQuote新增")
public class ProjYogaQuoteUpdateReq extends ProjYogaQuoteAddReq{

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
