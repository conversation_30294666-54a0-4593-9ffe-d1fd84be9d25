package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:10
 */
@Data
public class ProjYogaProgramLevelDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "workout 列表")
    private List<ProjYogaRegularWorkoutPageVO> workoutList;

}
