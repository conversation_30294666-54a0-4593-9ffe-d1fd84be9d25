package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: template 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template 分页", description = "template 分页")
public class ProjTemplatePageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "当期模板语言")
    private String languages;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "video")
    private Integer videoCount;

    @ApiModelProperty(value = "任务状态 0待处理 1处理中 2成功 3失败")
    private Integer taskStatus;

    @ApiModelProperty(value = "描述")
    private String description;

}
