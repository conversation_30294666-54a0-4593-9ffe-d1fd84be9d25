package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: yoga video详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video详情", description = "yoga video详情")
public class ResYogaVideoDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "English Voice Name")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "动作类型 数组 Start、Main、End、CoolDown")
    private String[] typeArr;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "动作体位 Steated、Standing、Prone、Supine、Arm & Leg Support")
    private String position;

    @ApiModelProperty(value = "瑜伽派别 数组 Strength、Balancing、Relaxation、Flexbility")
    private String[] focusArr;

    @ApiModelProperty(value = "特殊人群不可使用的 数组 Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String[] specialLimitArr;

    @ApiModelProperty(value = "动作时长 单位毫秒")
    private Integer poseTime;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "关联的pose video信息")
    private ProjBaseDetailVO relationPoseVideo;

    @ApiModelProperty(value = "pose video id")
    private Integer relationPoseVideoId;

    @ApiModelProperty(value = "下一个衔接动作")
    private List<ResYogaVideoNextDetailVO> nextList;

    @ApiModelProperty(value = "cooldown 链路列表")
    private List<List<ResYogaVideoCooldownAddVO>> coolDownList;

    @ApiModelProperty(value = "名字文本")
    private String nameScript;

    @ApiModelProperty(value = "guidance文本")
    private String guidanceScript;

}
