package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.response.ProjYogaRegularCategoryVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaRegularCategoryService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * yogaRegularCategory前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Api(tags = "项目管理:yoga regular category")
@RestController
@RequestMapping("/proj/yogaRegularCategory")
public class ProjYogaRegularCategoryController extends ResponseController {

    @Resource
    private IProjYogaRegularCategoryService projYogaRegularCategoryService;

    @ApiOperation(value = "分页列表")
    @GetMapping( "/list")
    public ResponseResult<List<ProjYogaRegularCategoryVO>> list() {
        return succ(projYogaRegularCategoryService.query(RequestContextUtils.getProjectId()));
    }
}
