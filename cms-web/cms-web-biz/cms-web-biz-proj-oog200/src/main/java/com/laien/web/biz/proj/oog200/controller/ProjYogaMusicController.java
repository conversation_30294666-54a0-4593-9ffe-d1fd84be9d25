package com.laien.web.biz.proj.oog200.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog200.entity.ProjYogaMusic;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaMusicUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaMusicDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaMusicPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaMusicService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Author:  hhl
 * Date:  2025/2/20 12:06
 */
@Api(tags = "项目管理:Yoga Music")
@RestController
@RequestMapping("/proj/yogaMusic")
public class ProjYogaMusicController extends ResponseController {

    @Resource
    IProjYogaMusicService yogaMusicService;

    @ApiOperation(value = "分页接口")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaMusicPageVO>> list(ProjYogaMusicPageReq pageReq) {

        Integer projectId = RequestContextUtils.getProjectId();
        AssertUtil.notNull(projectId,"projId is null");
        pageReq.setProjId(projectId);
        PageRes<ProjYogaMusicPageVO> pageRes = yogaMusicService.pageQuery(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjYogaMusicAddReq addReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        AssertUtil.notNull(projectId,"projId is null");
        addReq.setProjId(projectId);
        yogaMusicService.insert(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjYogaMusicUpdateReq updateReq) {

        yogaMusicService.update(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaMusicDetailVO> detail(@PathVariable Integer id) {

        ProjYogaMusicDetailVO detailVO = yogaMusicService.getDetailById(id);
        return succ(detailVO);
    }

}
