package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.laien.web.biz.proj.oog200.algorithm.ProjYogaAutoWorkoutGenerateAlgorithm;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jgrapht.Graph;
import org.jgrapht.GraphPath;
import org.jgrapht.graph.DefaultDirectedWeightedGraph;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.laien.web.biz.core.constant.BizConstant.*;

@Service
@Slf4j
public class ProjYogaAutoWorkoutGenerateServiceImpl implements IProjYogaAutoWorkoutGenerateService {

    private static final int MAX_PATH_DEEP = 50;
    @Resource
    private IResYogaVideoService yogaVideoService;

    @Resource
    private IResYogaVideoConnectionService yogaVideoConnectionService;

    @Resource
    private IResTransitionService transitionService;

    @Resource
    private IProjYogaAutoWorkoutTemplateService projYogaAutoWorkoutTemplateService;

    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;

    @Resource
    private IResYogaVideoCooldownService resYogaVideoCooldownService;

    private static Integer runThreadCount = 2;

    //任务运行的线程池
    private ExecutorService taskExecutor = ThreadUtil.newExecutor(runThreadCount, runThreadCount);

    @Override
    public List<ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj> generate(ProjYogaAutoWorkoutTask task) {
        //1-准备数据
        ProjYogaAutoWorkoutTemplate template = projYogaAutoWorkoutTemplateService.getById(task.getProjYogaAutoWorkoutTemplateId());
        LambdaQueryWrapper<ResYogaVideo> queryWrapperByVideo = new LambdaQueryWrapper<>();
        queryWrapperByVideo.eq(ResYogaVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ResYogaVideo> allYogaVideo = yogaVideoService.list(queryWrapperByVideo);
        //获取所有的transition信息
        LambdaQueryWrapper<ResTransition> queryWrapperByTransition = new LambdaQueryWrapper<>();
        queryWrapperByTransition.eq(ResTransition::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ResTransition> allTransition = transitionService.list(queryWrapperByTransition);
        //获取所有的connect信息
        Set<Integer> allYogaVideoIds = allYogaVideo.stream().map(ResYogaVideo::getId).collect(Collectors.toSet());
        Optional.ofNullable(allYogaVideoIds).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,No available video found"));
        Set<Integer> allTransitionIds = allTransition.stream().map(ResTransition::getId).collect(Collectors.toSet());
        Optional.ofNullable(allTransitionIds).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,No available transition found"));
        LambdaQueryWrapper<ResYogaVideoConnection> queryWrapperByConnection = new LambdaQueryWrapper<>();
        queryWrapperByConnection.in(ResYogaVideoConnection::getResYogaVideoId, allYogaVideoIds)
                .in(ResYogaVideoConnection::getResYogaVideoNextId, allYogaVideoIds)
                .in(ResYogaVideoConnection::getResTransitionId, allTransitionIds);
        List<ResYogaVideoConnection> allConnection = yogaVideoConnectionService.list(queryWrapperByConnection);
        //获取所有的cooldown信息
        LambdaQueryWrapper<ResYogaVideoCooldown> queryWrapperByCoolDown = new LambdaQueryWrapper<>();
        queryWrapperByCoolDown.orderByAsc(ResYogaVideoCooldown::getId);
        List<ResYogaVideoCooldown> queryCoolDowns = resYogaVideoCooldownService.list(queryWrapperByCoolDown);
        List<ResYogaVideoCooldown> allCoolDown = CollUtil.newArrayList();
        //去掉包含了禁用video的cooldown
        Map<Integer, List<ResYogaVideoCooldown>> coolDownByVideoMap = queryCoolDowns.stream().collect(Collectors.groupingBy(ResYogaVideoCooldown::getResYogaVideoLinkId));
        coolDownByVideoMap.entrySet().stream().forEach(e -> {
            Integer videoId = e.getKey();
            List<ResYogaVideoCooldown> cooldownByVideoList = e.getValue();
            Map<Integer, List<ResYogaVideoCooldown>> cooldownByGroup = cooldownByVideoList.stream().collect(Collectors.groupingBy(ResYogaVideoCooldown::getGroupId));
            cooldownByGroup.entrySet().stream().forEach(e2 -> {
                List<ResYogaVideoCooldown> oneCoolDownConns = e2.getValue();
                boolean containerDisabledVideo = false;
                for (ResYogaVideoCooldown connection : oneCoolDownConns) {
                    if (!allYogaVideoIds.contains(connection.getResYogaVideoLinkId())) {
                        //
                        log.info(connection.getResYogaVideoLinkId() + " no");
                        containerDisabledVideo = true;
                        break;
                    }
                    if ((!allYogaVideoIds.contains(connection.getResYogaVideoId())) || (connection.getResYogaVideoNextId() != null && !allYogaVideoIds.contains(connection.getResYogaVideoNextId()))) {
                        log.info(connection.getResYogaVideoId() + " no or " + connection.getResYogaVideoNextId() + " no");
                        containerDisabledVideo = true;
                        break;
                    }
                }
                if (!containerDisabledVideo) {
                    allCoolDown.addAll(oneCoolDownConns);
                }
            });
        });
        log.info("本次参与生成的video数量:" + allYogaVideo.size());
        log.info(StrUtil.join(",", allYogaVideo.stream().map(ResYogaVideo::getName).collect(Collectors.toList())));
        //2-计算结果并保存
        return computeAndGet(template, task, allYogaVideo, allTransition, allConnection, allCoolDown);
    }


    /**
     * 计算路径
     *
     * @return
     */
    @SneakyThrows
    private List<ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj> computeAndGet(ProjYogaAutoWorkoutTemplate template, ProjYogaAutoWorkoutTask task, List<ResYogaVideo> allYogaVideo, List<ResTransition> allTransition, List<ResYogaVideoConnection> allConnection, List<ResYogaVideoCooldown> allCoolDown) {
        //1---参数校验
        Optional.ofNullable(template).orElseThrow(() -> new BizException("generate fail,template does not exist"));
        Optional.ofNullable(allYogaVideo).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,No available video found"));
        Optional.ofNullable(allTransition).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,No available transition found"));
        Optional.ofNullable(allConnection).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,No available connection found"));
        Optional.ofNullable(allCoolDown).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,No available cooldown found"));
        //2---video转为计算节点node,connection转为link
        List<Node> allNodes = parseNode(allYogaVideo);
        Map<Integer, Node> nodesMap = allNodes.stream().collect(Collectors.toMap(Node::getId, t -> t));
        List<Link> links = parseLink(allTransition, allConnection, nodesMap);
        //3---将cooldown转为额外的node和link
        extralNodeAndLink(nodesMap, allCoolDown, links, allNodes);
        adjustLinkPoseTime(allTransition, links, allConnection);
        nodesMap = allNodes.stream().collect(Collectors.toMap(Node::getId, t -> t));
        //按type分类
        List<Node> typeNodeByStart = allNodes.stream().filter(n -> n.getTypes().contains("Start")).collect(Collectors.toList());
        Optional.ofNullable(typeNodeByStart).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,type Start video does not exist"));
        List<Node> typeNodeByMain = allNodes;
        List<Node> typeNodeByEnd = allNodes.stream().filter(n -> n.getTypes().contains("End")).collect(Collectors.toList());
        Optional.ofNullable(typeNodeByEnd).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,type End video does not exist"));
//        List<Node> typeNodeByCoolDown = allNodes.stream().filter(n -> n.getTypes().contains("CoolDown")).collect(Collectors.toList());
//        Optional.ofNullable(typeNodeByCoolDown).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,type CoolDown video does not exist"));
        List<Node> typeNodeByCoolDown = allNodes.stream().filter(n -> n.getDirect() == NodeDirect.COOLDOWN).collect(Collectors.toList());
        Optional.ofNullable(typeNodeByCoolDown).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BizException("generate fail,type CoolDown video does not exist"));
        //按difficulty分类
        List<Node> difficultyNodeByBeginner = allNodes.stream().filter(n -> n.getDifficultyType() == NodeDifficulty.Beginner).collect(Collectors.toList());
        List<Node> difficultyNodeByIntermediate = allNodes.stream().filter(n -> n.getDifficultyType() == NodeDifficulty.Intermediate).collect(Collectors.toList());
        List<Node> difficultyNodeByNewbie = allNodes.stream().filter(n -> n.getDifficultyType() == NodeDifficulty.NEWBIE).collect(Collectors.toList());
        //没有测试数据，这里用excel临时的假数据
        Map<Node, List<Link>> nodeLinkMap = links.stream().collect(Collectors.groupingBy(Link::getCurrentNode));
        //参数
        Integer minTime = template.getMinTime() * 1000 * 60;
        Integer maxTime = template.getMaxTime() * 1000 * 60;
        Integer expectTime = 0;
        Integer generateNum = task.getGenerateNum();
        //4---开始计算路径
        //生成Beginner难度的路径
        List<GraphPath> pathBeginner = computeByPriority(typeNodeByStart, typeNodeByMain, typeNodeByEnd, typeNodeByCoolDown, difficultyNodeByBeginner, nodeLinkMap, minTime, maxTime, generateNum, NodeDifficulty.Beginner);
        //生成Intermediate难度的路径
        List<GraphPath> pathIntermediate = computeByPriority(typeNodeByStart, typeNodeByMain, typeNodeByEnd, typeNodeByCoolDown, difficultyNodeByIntermediate, nodeLinkMap, minTime, maxTime, generateNum, NodeDifficulty.Intermediate);
        List<GraphPath> pathNewbie = computeByPriority(typeNodeByStart, typeNodeByMain, typeNodeByEnd, typeNodeByCoolDown, difficultyNodeByNewbie, nodeLinkMap, minTime, maxTime, generateNum, NodeDifficulty.NEWBIE);
        //5---筛选并保存数据
        List<ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj> result = CollUtil.newArrayList();
        Optional.ofNullable(filterAndGet(template.getProjId(), template.getId(), task.getId(), difficultyNodeByBeginner, pathBeginner, typeNodeByCoolDown, expectTime, generateNum, DIFFICULTY_BEGINNER, allYogaVideo, allTransition, allConnection, nodesMap, allCoolDown)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            result.addAll(list);
        });
        Optional.ofNullable(filterAndGet(template.getProjId(), template.getId(), task.getId(), difficultyNodeByIntermediate, pathIntermediate, typeNodeByCoolDown, expectTime, generateNum, DIFFICULTY_INTERMEDIATE, allYogaVideo, allTransition, allConnection, nodesMap, allCoolDown)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            result.addAll(list);
        });
        Optional.ofNullable(filterAndGet(template.getProjId(), template.getId(), task.getId(), difficultyNodeByNewbie, pathNewbie, typeNodeByCoolDown, expectTime, generateNum, DIFFICULTY_NEWBIE, allYogaVideo, allTransition, allConnection, nodesMap, allCoolDown)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            result.addAll(list);
        });
        return result;
    }

    /**
     * 根据 difficulty以及expectTime优先规则，对路径进行排序并保存为workout
     *
     * @param priorityNodeGroups
     * @param paths
     * @param typeNodeByCoolDown
     * @param expectTime
     * @param generateNum
     */
    private List<ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj> filterAndGet(Integer projId, Integer tempId, Integer taskId, List<Node> priorityNodeGroups, List<GraphPath> paths, List<Node> typeNodeByCoolDown, Integer expectTime, Integer generateNum, String difficulty, List<ResYogaVideo> allYogaVideo, List<ResTransition> allTransition, List<ResYogaVideoConnection> allConnection, Map<Integer, Node> nodesMap, List<ResYogaVideoCooldown> allCoolDown) {
        List<ProjYogaAutoWorkoutServiceImpl.BuildGenrateObj> result = CollUtil.newArrayList();
        List<GraphPath> savePath = paths;
        if (CollUtil.isNotEmpty(paths) && paths.size() > generateNum) {
            savePath = CollUtil.newArrayList();
            Map<Object, List<GraphPath>> grapPaths = paths.stream().collect(Collectors.groupingBy(GraphPath::getStartVertex));
            List<Stack<GraphPath>> stackList = new Stack<>();
            for (Map.Entry<Object, List<GraphPath>> startEntry : grapPaths.entrySet()) {
                List<GraphPath> pathList = startEntry.getValue();
                Collections.shuffle(pathList);
                Stack<GraphPath> oneStartPath = new Stack<>();
                oneStartPath.addAll(pathList);
                stackList.add(oneStartPath);
            }
            while (savePath.size() < generateNum) {
                for (Stack<GraphPath> graphPaths : stackList) {
                    if (graphPaths.size() > 0) {
                        GraphPath pop = graphPaths.pop();
                        savePath.add(pop);
                    }
                }
            }
        }
        if (savePath.size() >= generateNum) {
            savePath = savePath.subList(0, generateNum);
        }
        //保存workout
        Map<Integer, List<ResYogaVideoCooldown>> coolDownByGroupMap = allCoolDown.stream().collect(Collectors.groupingBy(ResYogaVideoCooldown::getGroupId));
        Map<Integer, ResYogaVideo> videoMap = allYogaVideo.stream().collect(Collectors.toMap(ResYogaVideo::getId, t -> t));
        for (GraphPath graphPath : savePath) {
            List<Integer> videoList = ((List<Node>) graphPath.getVertexList()).stream().map(Node::getId).collect(Collectors.toList());
            List<ResYogaVideo> sortVideos = CollUtil.newArrayList();
            for (Integer videoId : videoList) {
                Node videoNode = nodesMap.get(videoId);
                if (videoNode.getDirect() == NodeDirect.COOLDOWN) {
                    //解析cooldown
                    String groupId = videoNode.getName();
                    List<ResYogaVideoCooldown> resYogaVideoCooldowns = coolDownByGroupMap.get(Integer.parseInt(groupId));
                    if (CollUtil.isNotEmpty(resYogaVideoCooldowns)) {
                        sortVideos.add(videoMap.get(resYogaVideoCooldowns.get(0).getResYogaVideoId()));
                        for (ResYogaVideoCooldown resYogaVideoCooldown : resYogaVideoCooldowns) {
                            if (resYogaVideoCooldown.getResYogaVideoNextId() != null) {
                                sortVideos.add(videoMap.get(resYogaVideoCooldown.getResYogaVideoNextId()));
                            }
                        }
                    }
                } else {
                    sortVideos.add(videoMap.get(videoId));
                }
            }
            result.add(projYogaAutoWorkoutService.buildByGenerate(projId, tempId, taskId, sortVideos, allConnection, allTransition, difficulty));
        }
        return result;
    }

    /**
     * 按优先级计算路径
     *
     * @param typeNodeByStart    Start节点
     * @param typeNodeByMain     Main节点
     * @param typeNodeByEnd      End节点
     * @param typeNodeByCoolDown CoolDown节点
     * @param priorityNodeGroups 优先使用的节点
     * @param nodeLinkMap        link
     * @param minTime            最小时长
     * @param maxTime            最大时长
     * @param generateNum        生成数量
     * @return
     */
    private List<GraphPath> computeByPriority(List<Node> typeNodeByStart, List<Node> typeNodeByMain, List<Node> typeNodeByEnd, List<Node> typeNodeByCoolDown, List<Node> priorityNodeGroups, Map<Node, List<Link>> nodeLinkMap, Integer minTime, Integer maxTime, Integer generateNum, NodeDifficulty nodeDifficulty) {
        //计算路径，分为两步，先根据difficulty,选择指定difficulty video进行生成，如果待选池数据不足再使用全部的video进行生成
        List<GraphPath> pathByDifficulty = CollUtil.newArrayList();
        //待选路径不足，用全部的再生成
        if (pathByDifficulty.size() < generateNum) {
            //priorityNodeGroups
            Set<Node> priorityNodes = CollUtil.newHashSet(priorityNodeGroups);
            //Map<Node, List<Link>> nodeLinkMap
            //生成Biginer时，如果某个视频有biginer的next，则使用该next，否则可以使用其他next，高级难度也是这样
//            Map<Node, List<Link>> priorityNodeLinkMap = MapUtil.newHashMap();
//            for (Map.Entry<Node, List<Link>> e : nodeLinkMap.entrySet()) {
//                Node key = e.getKey();
//                List<Link> alllinks = e.getValue();
//                List<Link> difficultyLinks = CollUtil.newArrayList();
//                for (Link oneLink : alllinks) {
//                    if (priorityNodes.contains(oneLink.getNextNode())) {
//                        difficultyLinks.add(oneLink);
//                    }
//                }
//                if (difficultyLinks.size() == 0) {
//                    difficultyLinks = alllinks;
//                } else {
//                    //所有跟coolDown连接的链路都必须包含
//                    List<Link> coolDownLinks = alllinks.stream().filter(oneLink -> oneLink.getNextNode().getDirect() == NodeDirect.COOLDOWN).collect(Collectors.toList());
//                    difficultyLinks.addAll(coolDownLinks);
//                }
//                priorityNodeLinkMap.put(key, difficultyLinks);
//            }
            //pathMark 只是用来对路径进行md5验证，防止生成重复的路径
            Set<String> pathMarks = Sets.newHashSet();
            if (CollUtil.isNotEmpty(pathByDifficulty)) {
                pathMarks = pathByDifficulty.stream().map(path -> MD5.create().digestHex(StrUtil.join(",", path.getVertexList()))).collect(Collectors.toSet());
            }
            List<List<GraphPath>> pathGroup = computePaths(nodeLinkMap, Sets.newHashSet(typeNodeByStart), Sets.newHashSet(typeNodeByCoolDown), Sets.newHashSet(typeNodeByMain), minTime, maxTime, generateNum, nodeDifficulty);
            for (List<GraphPath> graphPaths : pathGroup) {
                for (GraphPath graphPath : graphPaths) {
                    String md5 = MD5.create().digestHex(StrUtil.join(",", graphPath.getVertexList()));
                    if (!pathMarks.contains(md5)) {
                        pathByDifficulty.add(graphPath);
                        pathMarks.add(md5);
                    }
                }
            }
        }
        return pathByDifficulty;
    }

    /**
     * 根据传入的节点信息 计算路径
     *
     * @param nodeLinkMap
     * @param startNodes
     * @param coolDownNodes
     * @param mainNodes
     * @param minDuration
     * @param maxDuration
     * @param generateNum
     * @return
     */
    private List<List<GraphPath>> computePaths(Map<Node, List<Link>> nodeLinkMap, Set<Node> startNodes, Set<Node> coolDownNodes, Set<Node> mainNodes, Integer minDuration, Integer maxDuration, Integer generateNum, NodeDifficulty nodeDifficulty) {
        List<List<GraphPath>> graphPaths = CollUtil.newArrayList();
        List<GraphPath> loopPaths = computePathsByStart(nodeLinkMap, startNodes, coolDownNodes, mainNodes, minDuration, maxDuration, generateNum, false, nodeDifficulty);
        graphPaths.add(loopPaths);
        return graphPaths;
    }

    /**
     * 根据startNode计算路径
     *
     * @param linkMapByCurrent
     * @param startNodes
     * @param endNodes
     * @param mainNodes
     * @param minDuration
     * @param maxDuration
     * @param targetGenerateCount
     * @param isLoop
     * @return
     */
    @SneakyThrows
    private List<GraphPath> computePathsByStart(Map<Node, List<Link>> linkMapByCurrent, Set<Node> startNodes, Set<Node> endNodes, Set<Node> mainNodes, Integer minDuration, Integer maxDuration, Integer targetGenerateCount, boolean isLoop, NodeDifficulty nodeDifficulty) {
        List<Node> startNodeList = CollUtil.newArrayList(startNodes);
        //打乱start的顺序
        Collections.shuffle(startNodeList);
        List<List<Node>> startGroups = Lists.partition(startNodeList, (startNodeList.size() / 2) + 1);
        //获取开始节点的所有链接、为了保证数据尽可能的多样性，防止数据全部为同一个link下的数据，为每个link单独生成部分数据,防止出现大量的相似数据
        List<GraphPath> allLinkPath = Lists.newCopyOnWriteArrayList();
        List<CompletableFuture> completableFutureList = CollUtil.newArrayList();
        AtomicInteger manythreadGeneratedCount = new AtomicInteger(0);
        for (List<Node> oneGroup : startGroups) {
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                //所有要参与计算的节点
                Set<Node> allCalNodes = CollUtil.newHashSet(mainNodes);
                allCalNodes.addAll(oneGroup);
                allCalNodes.addAll(endNodes);
                //打乱一下顺序
                List<Node> nodes = CollUtil.newArrayList(allCalNodes);
                Collections.shuffle(nodes);
                allCalNodes = Sets.newLinkedHashSet(nodes);
                //创建图
                Graph<Node, DefaultWeightedEdge> graph = new DefaultDirectedWeightedGraph<>(DefaultWeightedEdge.class);
                for (Node curNode : allCalNodes) {
                    graph.addVertex(curNode);
                }
                //必须要把所有节点全部加入图后，才能加关系，否则会出现next节点未加入图，出现节点不存在的提示
//                    Integer minLinkPoseTime = 10000;
                for (Node curNode : allCalNodes) {
                    List<Link> links = linkMapByCurrent.get(curNode);
                    if (CollUtil.isNotEmpty(links)) {
                        for (Link link : links) {
                            if (allCalNodes.contains(link.getCurrentNode()) && allCalNodes.contains(link.getNextNode())) {
                                DefaultWeightedEdge defaultWeightedEdge2 = graph.addEdge(link.getCurrentNode(), link.getNextNode());
                                if (defaultWeightedEdge2 != null) {
                                    graph.setEdgeWeight(defaultWeightedEdge2, link.getPoseTime());
                                }
                            }
                        }
                    }
                }
                //路径长度
//                    int maxPathLength = (int) Math.round((maxDuration / (minLinkPoseTime * 1.0d))) + 1;
                // 使用AllDirectedPaths类获取所有路径
                ProjYogaAutoWorkoutGenerateAlgorithm<Node, DefaultWeightedEdge> allPaths = new ProjYogaAutoWorkoutGenerateAlgorithm<>(graph);
                List<GraphPath<Node, DefaultWeightedEdge>> oneLinkPaths = allPaths.getAllPaths(CollUtil.newHashSet(oneGroup), mainNodes, endNodes, !isLoop, MAX_PATH_DEEP, minDuration, maxDuration, manythreadGeneratedCount, 10000, nodeDifficulty, SystemClock.now() + (1000 * 60 * 10));
                allLinkPath.addAll(oneLinkPaths);
            }, taskExecutor);
            completableFutureList.add(completableFuture);
        }
        try {
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[completableFutureList.size()])).get();
        } catch (Exception e) {
            log.error("computePathsByStart error", e);
            e.printStackTrace();
            throw e;
        } finally {

        }
        return allLinkPath;
    }

    /**
     * 将视频转换为节点
     *
     * @param videoList
     * @return
     */
    private List<Node> parseNode(List<ResYogaVideo> videoList) {
        List<Node> allNodes = videoList.stream().map(v -> {
            Node node = new Node();
            BeanUtils.copyProperties(v, node);
            String name = v.getName();
            String noDirectName = name;
            NodeDirect direct = NodeDirect.NONE;
            if (name.contains(" (")) {
                String last = StrUtil.subAfter(name, " (", true).trim().toUpperCase();
                if (last.equals("LEFT)")) {
                    direct = NodeDirect.LEFT;
                }
                if (last.equals("RIGHT)")) {
                    direct = NodeDirect.RIGHT;
                }
                noDirectName = StrUtil.subBefore(name, " (", true);
            }
            node.setDirect(direct);
            node.setNoDirectName(noDirectName);
            if (DIFFICULTY_BEGINNER.equals(v.getDifficulty())) {
                node.setDifficultyType(NodeDifficulty.Beginner);
            }else if(DIFFICULTY_NEWBIE.equals(v.getDifficulty())){
                node.setDifficultyType(NodeDifficulty.NEWBIE);
            } else if (DIFFICULTY_INTERMEDIATE.equals(v.getDifficulty()) || DIFFICULTY_ADVANCED.equals(v.getDifficulty())) {
                node.setDifficultyType(NodeDifficulty.Intermediate);
            }
            Set<String> types = StrUtil.split(v.getType(), ",", true, true).stream().collect(Collectors.toSet());
            node.setTypes(types);
            return node;
        }).collect(Collectors.toList());

        //绑定左右关系
        Map<String, List<Node>> leftRightMap = allNodes.stream().filter(n -> n.getDirect() != NodeDirect.NONE).collect(Collectors.groupingBy(Node::getNoDirectName));
        for (Map.Entry<String, List<Node>> entry : leftRightMap.entrySet()) {
            List<Node> nodeList = entry.getValue();
            List<Node> leftNodes = nodeList.stream().filter(n -> n.getDirect() == NodeDirect.LEFT).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(leftNodes)) {
                List<Node> rightNodes = nodeList.stream().filter(n -> n.getDirect() == NodeDirect.RIGHT).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(rightNodes)) {
                    for (Node leftNode : leftNodes) {
                        leftNode.setRightNode(rightNodes.get(0));
                    }
                }
            }
        }
        return allNodes;
    }

    private void extralNodeAndLink(Map<Integer, Node> nodesMap, List<ResYogaVideoCooldown> allCoolDown, List<Link> links, List<Node> nodes) {
        Table<Node, Node, Link> linkTable = HashBasedTable.create();
        for (Link link : links) {
            linkTable.put(link.getCurrentNode(), link.getNextNode(), link);
        }
        final Integer[] firstCoolDownId = {-1};
        Map<Integer, List<ResYogaVideoCooldown>> coolDownByVideoMap = allCoolDown.stream().collect(Collectors.groupingBy(ResYogaVideoCooldown::getResYogaVideoLinkId));
        coolDownByVideoMap.entrySet().stream().forEach(e -> {
            Integer videoId = e.getKey();
            List<ResYogaVideoCooldown> cooldownByVideoList = e.getValue();
            Map<Integer, List<ResYogaVideoCooldown>> cooldownByGroup = cooldownByVideoList.stream().collect(Collectors.groupingBy(ResYogaVideoCooldown::getGroupId));
            cooldownByGroup.entrySet().stream().forEach(e2 -> {
                List<ResYogaVideoCooldown> cooldownConnections = e2.getValue();
                List<Link> cooldownLinks = CollUtil.newArrayList();
                //第一条链路为 原next的链路
                Node videoNode = nodesMap.get(videoId);
                Node firstTargetNode = nodesMap.get(cooldownConnections.get(0).getResYogaVideoId());
                Link firstLink = linkTable.get(videoNode, firstTargetNode);
                if (firstLink != null) {
                    cooldownLinks.add(firstLink);
                    Integer poseTime = firstLink.getPoseTime();
                    //后续的为新链路
                    for (ResYogaVideoCooldown cooldownConnection : cooldownConnections) {
                        if (cooldownConnection.getResYogaVideoNextId() != null) {
                            Link link = linkTable.get(nodesMap.get(cooldownConnection.getResYogaVideoId()), nodesMap.get(cooldownConnection.getResYogaVideoNextId()));
                            cooldownLinks.add(link);
                            poseTime += link.getPoseTime();
                        }
                    }
                    //将cooldown模拟为一个node
                    Node coolDownNode = new Node();
                    coolDownNode.setId(firstCoolDownId[0]);
                    coolDownNode.setName(e2.getKey() + "");
                    coolDownNode.setDirect(NodeDirect.COOLDOWN);
                    coolDownNode.setTypes(CollUtil.newHashSet());
                    coolDownNode.setPoseTime(poseTime);
                    coolDownNode.setDifficultyType(NodeDifficulty.NONE);
                    nodes.add(coolDownNode);
                    //模拟link
                    Link link = new Link();
                    link.setCurrentNode(videoNode);
                    link.setNextNode(coolDownNode);
                    link.setDifficulty(NodeDifficulty.NONE);
                    link.setPoseTime(poseTime);
                    links.add(link);
                    firstCoolDownId[0]--;
                } else {
                    log.info("not found link " + videoId + "," + cooldownConnections.get(0).getResYogaVideoId());
                }
            });
        });
    }

    /**
     * 将transition和connection转为link，代表节点连接
     *
     * @param transitionList
     * @param connectionList
     * @return
     */
    private List<Link> parseLink(List<ResTransition> transitionList, List<ResYogaVideoConnection> connectionList, Map<Integer, Node> nodesMap) {
        Map<Integer, ResTransition> transitionMap = transitionList.stream().collect(Collectors.toMap(ResTransition::getId, t -> t));
        return connectionList.stream().map(c -> {
            Link link = new Link();
            link.setCurrentNode(nodesMap.get(c.getResYogaVideoId()));
            Node nextNode = nodesMap.get(c.getResYogaVideoNextId());
            link.setNextNode(nextNode);
            link.setPoseTime(nextNode.getPoseTime() + transitionMap.get(c.getResTransitionId()).getFrontVideoDuration());
            link.setDifficulty(nextNode.getDifficultyType());
            return link;
        }).collect(Collectors.toList());
    }

    private void adjustLinkPoseTime(List<ResTransition> transitionList, List<Link> links, List<ResYogaVideoConnection> connectionList) {
        Map<Integer, ResTransition> transitionMap = transitionList.stream().collect(Collectors.toMap(ResTransition::getId, t -> t));
        Table<Integer, Integer, ResYogaVideoConnection> connectionTable = HashBasedTable.create();
        for (ResYogaVideoConnection resYogaVideoConnection : connectionList) {
            connectionTable.put(resYogaVideoConnection.getResYogaVideoId(), resYogaVideoConnection.getResYogaVideoNextId(), resYogaVideoConnection);
        }
        for (Link link : links) {
            try {
                Node nextNode = link.getNextNode();
                if (link.getNextNode().getDirect() == NodeDirect.LEFT) {
                    link.setPoseTime(nextNode.getPoseTime() + transitionMap.get(connectionTable.get(link.getCurrentNode().getId(), link.getNextNode().getId()).getResTransitionId()).getFrontVideoDuration() + nextNode.getRightNode().getPoseTime());
                }
                if (nextNode.getDirect() == NodeDirect.RIGHT) {
                    link.setPoseTime(transitionMap.get(connectionTable.get(link.getCurrentNode().getId(), link.getNextNode().getId()).getResTransitionId()).getFrontVideoDuration());
                }
            } catch (Exception e) {
                log.error(link.getCurrentNode().getId() + "," + link.getNextNode().getId(), e);
            }
        }
    }

    @AllArgsConstructor
    @Getter
    public static enum NodeDirect {

        NONE(0),
        LEFT(1),
        RIGHT(2),
        COOLDOWN(3);


        private Integer code;
    }

    @AllArgsConstructor
    @Getter
    public static enum NodeDifficulty {

        Beginner(1),
        Intermediate(2),
        NONE(3),
        NEWBIE(4);

        private Integer code;
    }

    @Data
    public static class Node {
        private Integer id;
        private String name;
        private String noDirectName;
        private Integer poseTime;
        private NodeDirect direct;
        private Node rightNode;
        private Set<String> types;
        private NodeDifficulty difficultyType;

        @Override
        public String toString() {
            return "Node{" +
                    "name='" + name + '\'' +
                    '}';
        }
    }

    @Data
    public static class Link {
        private Node currentNode;
        private Node nextNode;
        private Integer poseTime;
        private NodeDifficulty difficulty;
    }
}
