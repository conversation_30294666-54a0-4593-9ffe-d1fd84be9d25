/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseWorkout;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>Yoga Pose workout 业务对象</p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@Accessors(chain = true)
public class ProjYogaPoseWorkoutBO {

    private Integer id;

    private ProjYogaPoseVideo poseVideo;

    private String name;

    private String eventName;

    private String sanskritName;

    private String coverImgUrl;

    private String detailImgUrl;

    private String poseLightImgUrl;

    private String poseDarkImgUrl;

    private String difficulty;

    private String instructions;

    private String benefits;

    private String chairVariation;

    private String chairVariationImgUrl;

    private String chairVariationTips;

    private Integer flexibility;

    private Integer balance;

    private Integer strength;

    private Integer relaxation;

    private BigDecimal calorie;

    private Integer duration;

    private Integer subscription;

    private String videoM3u8Url;

    private String video2532Url;

    private String audioLongJson;

    private String audioShortJson;

    private String videoMaskJson;

    /**  多语言音频LongJson 不包含默认语言 */
    private Map<String,String> multiLanguageAudioLongJson ;

    /**  多语言音频ShortJson 不包含默认语言 */
    private Map<String,String> multiLanguageAudioShortJson ;

    /**  多语言音频 videoMaskJson 不包含默认语言 */
    private Map<String,String> multiLanguageVideoMaskJson ;

    private String focus;

    private Integer projYogaPoseVideoId;

    private String position;

    private Integer projId;

    private Integer status;

    private String createUser;

}