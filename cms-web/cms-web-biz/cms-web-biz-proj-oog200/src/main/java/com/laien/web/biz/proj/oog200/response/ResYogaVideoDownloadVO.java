package com.laien.web.biz.proj.oog200.response;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * ResYogaVideoDownloadVO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="ResYogaVideoDownloadVO对象", description="ResYogaVideoDownloadVO")
public class ResYogaVideoDownloadVO {

    @ApiModelProperty(value = "数据id")
    @ExcelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @ExcelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "名字文本")
    @ExcelProperty(value = "name script")
    private String nameScript;

    @ApiModelProperty(value = "guidance文本")
    @ExcelProperty(value = "guidance script")
    private String guidanceScript;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    @ExcelProperty(value = "status")
    private Integer status;
}
