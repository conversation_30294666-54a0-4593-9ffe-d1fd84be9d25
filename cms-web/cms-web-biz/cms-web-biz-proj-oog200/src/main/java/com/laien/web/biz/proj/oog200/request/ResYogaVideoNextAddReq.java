package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yoga video next详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video next详情", description = "yoga video next详情")
public class ResYogaVideoNextAddReq {

    @ApiModelProperty(value = "下一个视频")
    private ResYogaVideoNextYogaVideoAddReq yogaVideo;

    @ApiModelProperty(value = "下一个视频过渡")
    private ResYogaVideoNextTransitionAddReq transition;

}
