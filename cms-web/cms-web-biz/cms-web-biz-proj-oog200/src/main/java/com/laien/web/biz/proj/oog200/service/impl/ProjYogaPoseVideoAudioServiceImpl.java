package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoAudio;
import com.laien.common.oog200.enums.PoseVideoPlayEnum;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseVideoAudioMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoAudioDetailVO;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoImportReq;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoAudioService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/8/2 12:19
 */
@Service
public class ProjYogaPoseVideoAudioServiceImpl extends ServiceImpl<ProjYogaPoseVideoAudioMapper, ProjYogaPoseVideoAudio> implements IProjYogaPoseVideoAudioService {


    @Override
    public List<ProjYogaPoseVideoAudioDetailVO> listByPoseVideoId(Integer poseVideoId) {

        LambdaQueryWrapper<ProjYogaPoseVideoAudio> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseVideoAudio::getProjYogaPoseVideoId, poseVideoId);
        List<ProjYogaPoseVideoAudio> poseVideoAudioList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(poseVideoAudioList)) {
            return Collections.emptyList();
        }

        return poseVideoAudioList.stream().map(audio -> {
            ProjYogaPoseVideoAudioDetailVO audioDetailVO = new ProjYogaPoseVideoAudioDetailVO();
            BeanUtils.copyProperties(audio, audioDetailVO);

            // for third round，disable second and third audio
            if (Objects.equals(audio.getRoundIndex(), PoseVideoPlayEnum.THIRD.getRoundIndex())) {
                audioDetailVO.setSecondAudioEnable(Boolean.FALSE);
                audioDetailVO.setThirdAudioEnable(Boolean.FALSE);
            }
            return audioDetailVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteByPoseVideoId(Integer poseVideoId) {

        if (Objects.isNull(poseVideoId)) {
            return;
        }

        LambdaUpdateWrapper<ProjYogaPoseVideoAudio> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaPoseVideoAudio::getDelFlag, GlobalConstant.YES);
        updateWrapper.eq(ProjYogaPoseVideoAudio::getProjYogaPoseVideoId, poseVideoId);
        update(updateWrapper);
    }

    @Override
    public void saveAudio4Import(List<ProjYogaPoseVideoImportReq> videoImportReqList, List<ProjYogaPoseVideo> poseVideoList) {

        if (CollectionUtils.isEmpty(videoImportReqList) || CollectionUtils.isEmpty(poseVideoList)) {
            return;
        }

        Map<String, ProjYogaPoseVideo> poseVideoMap = poseVideoList.stream().collect(Collectors.toMap(ProjYogaPoseVideo::getName, Function.identity(), (k1, k2) -> k1));
        List<ProjYogaPoseVideoAudio> poseVideoAudioList = videoImportReqList.stream()
                .filter(videoImportReq -> poseVideoMap.containsKey(videoImportReq.getName()))
                .map(videoImportReq -> {
                    ProjYogaPoseVideo poseVideo = poseVideoMap.get(videoImportReq.getName());
                    ProjYogaPoseVideoAudio audioOne = wrapAudioOne(videoImportReq, poseVideo);
                    ProjYogaPoseVideoAudio audioTwo = wrapAudioTwo(videoImportReq, poseVideo);
                    ProjYogaPoseVideoAudio audioThree = wrapAudioThree(videoImportReq, poseVideo);
                    return Lists.newArrayList(audioOne, audioTwo, audioThree);
                }).flatMap(Collection::stream).collect(Collectors.toList());
        saveBatch(poseVideoAudioList);
    }

    private ProjYogaPoseVideoAudio wrapAudioOne(ProjYogaPoseVideoImportReq videoImportReq, ProjYogaPoseVideo poseVideo) {

        ProjYogaPoseVideoAudio audioOne = new ProjYogaPoseVideoAudio();
        audioOne.setProjId(poseVideo.getProjId());
        audioOne.setProjYogaPoseVideoId(poseVideo.getId());
        audioOne.setRoundIndex(PoseVideoPlayEnum.FIRST.getRoundIndex());

        audioOne.setFirstGuidanceAudioUrl(videoImportReq.getAudio1_1_url());
        audioOne.setFirstGuidanceAudioDuration(videoImportReq.getAudio1_1_duration());
        audioOne.setSecondGuidanceAudioUrl(videoImportReq.getAudio1_2_url());
        audioOne.setSecondGuidanceAudioDuration(videoImportReq.getAudio1_2_duration());

        audioOne.setThirdGuidanceAudioUrl(videoImportReq.getAudio1_3_url());
        audioOne.setThirdGuidanceAudioDuration(videoImportReq.getAudio1_3_duration());
        audioOne.setFourthGuidanceAudioUrl(videoImportReq.getAudio1_4_url());
        audioOne.setFourthGuidanceAudioDuration(videoImportReq.getAudio1_4_duration());

        audioOne.setFifthGuidanceAudioUrl(videoImportReq.getAudio1_5_url());
        audioOne.setFifthGuidanceAudioDuration(videoImportReq.getAudio1_5_duration());
        audioOne.setSixthGuidanceAudioUrl(videoImportReq.getAudio1_6_url());
        audioOne.setSixthGuidanceAudioDuration(videoImportReq.getAudio1_6_duration());
        return audioOne;
    }

    private ProjYogaPoseVideoAudio wrapAudioTwo(ProjYogaPoseVideoImportReq videoImportReq, ProjYogaPoseVideo poseVideo) {

        ProjYogaPoseVideoAudio audioTwo = new ProjYogaPoseVideoAudio();
        audioTwo.setProjId(poseVideo.getProjId());
        audioTwo.setProjYogaPoseVideoId(poseVideo.getId());
        audioTwo.setRoundIndex(PoseVideoPlayEnum.SECOND.getRoundIndex());

        audioTwo.setFirstGuidanceAudioUrl(videoImportReq.getAudio2_1_url());
        audioTwo.setFirstGuidanceAudioDuration(videoImportReq.getAudio2_1_duration());
        audioTwo.setSecondGuidanceAudioUrl(videoImportReq.getAudio2_2_url());
        audioTwo.setSecondGuidanceAudioDuration(videoImportReq.getAudio2_2_duration());

        audioTwo.setThirdGuidanceAudioUrl(videoImportReq.getAudio2_3_url());
        audioTwo.setThirdGuidanceAudioDuration(videoImportReq.getAudio2_3_duration());
        audioTwo.setFourthGuidanceAudioUrl(videoImportReq.getAudio2_4_url());
        audioTwo.setFourthGuidanceAudioDuration(videoImportReq.getAudio2_4_duration());

        audioTwo.setFifthGuidanceAudioUrl(videoImportReq.getAudio2_5_url());
        audioTwo.setFifthGuidanceAudioDuration(videoImportReq.getAudio2_5_duration());
        audioTwo.setSixthGuidanceAudioUrl(videoImportReq.getAudio2_6_url());
        audioTwo.setSixthGuidanceAudioDuration(videoImportReq.getAudio2_6_duration());

        return audioTwo;
    }

    private ProjYogaPoseVideoAudio wrapAudioThree(ProjYogaPoseVideoImportReq videoImportReq, ProjYogaPoseVideo poseVideo) {

        ProjYogaPoseVideoAudio audioThree = new ProjYogaPoseVideoAudio();
        audioThree.setProjId(poseVideo.getProjId());
        audioThree.setProjYogaPoseVideoId(poseVideo.getId());
        audioThree.setRoundIndex(PoseVideoPlayEnum.THIRD.getRoundIndex());

        audioThree.setFirstGuidanceAudioUrl(videoImportReq.getAudio3_1_url());
        audioThree.setFirstGuidanceAudioDuration(videoImportReq.getAudio3_1_duration());
        audioThree.setFourthGuidanceAudioUrl(videoImportReq.getAudio3_4_url());
        audioThree.setFourthGuidanceAudioDuration(videoImportReq.getAudio3_4_duration());

        audioThree.setFifthGuidanceAudioUrl(videoImportReq.getAudio3_5_url());
        audioThree.setFifthGuidanceAudioDuration(videoImportReq.getAudio3_5_duration());
        audioThree.setSixthGuidanceAudioUrl(videoImportReq.getAudio3_6_url());
        audioThree.setSixthGuidanceAudioDuration(videoImportReq.getAudio3_6_duration());
        return audioThree;
    }
}
