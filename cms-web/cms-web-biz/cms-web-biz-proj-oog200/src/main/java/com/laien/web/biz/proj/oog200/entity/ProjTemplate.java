package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * template
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjTemplate对象", description="template")
public class ProjTemplate extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "当期模板语言")
    private String languages;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
