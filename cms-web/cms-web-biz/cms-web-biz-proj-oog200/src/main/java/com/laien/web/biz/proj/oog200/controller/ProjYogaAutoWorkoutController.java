package com.laien.web.biz.proj.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkout;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseWorkout;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjClassicYogaAutoWorkoutDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaAutoWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * Yoga Auto Workout 前端控制器
 * </p>
 */
@Api(tags = "项目管理:Yoga Auto Workout")
@RestController
@RequestMapping("/proj/yogaAutoWorkout")
public class ProjYogaAutoWorkoutController extends ResponseController {


    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;


    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody @Validated(Group.class) ProjYogaAutoWorkoutAddReq projYogaAutoWorkoutReq) {
        projYogaAutoWorkoutService.add(projYogaAutoWorkoutReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody @Validated(Group.class) ProjYogaAutoWorkoutUpdateReq projYogaAutoWorkoutUpdateReq) {
        projYogaAutoWorkoutService.update(projYogaAutoWorkoutUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "批量修改")
    @PostMapping("/updateBatch")
    public ResponseResult<Void> batchUpdate(@RequestBody ProjYogaAutoWorkoutBatchUpdateReq batchUpdateReq) {

        projYogaAutoWorkoutService.batchUpdate(batchUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaAutoWorkoutDetailVO> detail(@PathVariable Integer id) {
        return succ(projYogaAutoWorkoutService.detail(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        projYogaAutoWorkoutService.updateEnableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        projYogaAutoWorkoutService.updateDisableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        projYogaAutoWorkoutService.deleteByIds(idListReq.getIdList());
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(projYogaAutoWorkoutService.importByExcel(excel.getInputStream(), RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "下载excel")
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        List<ProjClassicYogaAutoWorkoutDownloadVO> workoutDownloadList = projYogaAutoWorkoutService.downloadList();
        if (CollUtil.isEmpty(workoutDownloadList)) {
            return;
        }
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''classicWorkout.xlsx";
        String sheetName = "classicWorkout";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ProjClassicYogaAutoWorkoutDownloadVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(workoutDownloadList);
    }
}
