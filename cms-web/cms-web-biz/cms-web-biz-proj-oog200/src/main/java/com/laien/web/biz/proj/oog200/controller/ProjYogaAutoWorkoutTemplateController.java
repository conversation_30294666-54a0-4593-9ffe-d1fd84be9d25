package com.laien.web.biz.proj.oog200.controller;


import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTemplate;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTempWorkoutPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTemplateDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaAutoWorkoutTemplatePageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaAutoWorkoutTemplateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * Yoga Auto Workout Template 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Api(tags = "项目管理:Yoga Auto Workout Template")
@RestController
@RequestMapping("/proj/yogaAutoWorkoutTemplate")
public class ProjYogaAutoWorkoutTemplateController extends ResponseController {

    @Resource
    private IProjYogaAutoWorkoutTemplateService projYogaAutoWorkoutTemplateService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjYogaAutoWorkoutTemplatePageVO>> page(ProjYogaAutoWorkoutTemplatePageReq pageReq) {
        return succ(projYogaAutoWorkoutTemplateService.page(pageReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "分页-workout列表")
    @GetMapping({"/page/{tempId}/workout","/page/workout"})
    public ResponseResult<PageRes<ProjYogaAutoWorkoutTempWorkoutPageVO>> pageWorkout(ProjYogaAutoWorkoutTempWorkoutPageReq pageReq, @PathVariable(required = false) Integer tempId) {
        return succ(projYogaAutoWorkoutTemplateService.pageWorkout(pageReq, pageReq.getProjYogaAutoWorkoutTemplateId()));
    }

    @ApiOperation(value = "分页-workout-video列表")
    @GetMapping("/page/{workoutId}/video")
    public ResponseResult<PageRes<ProjYogaAutoWorkoutTempVideoPageVO>> pageWorkout(PageReq pageReq, @PathVariable Integer workoutId) {
        return succ(projYogaAutoWorkoutTemplateService.pageVideo(pageReq, workoutId));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody @Validated(Group.class) ProjYogaAutoWorkoutTemplateAddReq projYogaAutoWorkoutTemplateAddReq) {
        projYogaAutoWorkoutTemplateService.add(projYogaAutoWorkoutTemplateAddReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody @Validated(Group.class) ProjYogaAutoWorkoutTemplateUpdateReq projYogaAutoWorkoutTemplateUpdateReq) {
        projYogaAutoWorkoutTemplateService.update(projYogaAutoWorkoutTemplateUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjYogaAutoWorkoutTemplateDetailVO> detail(@PathVariable Integer id) {
        return succ(projYogaAutoWorkoutTemplateService.detail(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        projYogaAutoWorkoutTemplateService.updateEnableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        projYogaAutoWorkoutTemplateService.updateDisableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        projYogaAutoWorkoutTemplateService.deleteByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "workout 生成")
    @PostMapping("/generate/{templateId}")
    public ResponseResult<Void> generate(@PathVariable Integer templateId, @RequestBody @Validated(Group.class) ProjYogaAutoWorkoutTemplateGenerateReq projYogaAutoWorkoutTemplateGenerateReq) {
        projYogaAutoWorkoutTemplateService.generate(templateId, projYogaAutoWorkoutTemplateGenerateReq);
        return succ();
    }

}
