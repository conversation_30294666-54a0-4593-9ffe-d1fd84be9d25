package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: transition修改
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "transition修改", description = "transition修改")
public class ResTransitionUpdateReq extends ResTransitionAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
