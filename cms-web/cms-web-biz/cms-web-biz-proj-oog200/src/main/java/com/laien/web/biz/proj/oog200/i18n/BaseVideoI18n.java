package com.laien.web.biz.proj.oog200.i18n;

import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AudioTranslateResultModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/7/2
 */
@Data
@NoArgsConstructor
public class BaseVideoI18n implements AppAudioCoreI18nModel {

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "video id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    @AppAudioTranslateField(resultFieldName = "result")
    private String name;

    @ApiModelProperty(value = "result")
    private List<AudioTranslateResultModel> result;

    @ApiModelProperty(value = "解说音频")
    @AppAudioTranslateField(resultFieldName = "guidanceResult")
    private String guidance;

    @ApiModelProperty(value = "guidanceResult")
    private List<AudioTranslateResultModel> guidanceResult;

    public BaseVideoI18n(Integer coreVoiceConfigI18nId, Integer id, String name, String guidance) {
        this.coreVoiceConfigI18nId = coreVoiceConfigI18nId;
        this.id = id;
        this.name = name;
        this.guidance = guidance;
    }

}
