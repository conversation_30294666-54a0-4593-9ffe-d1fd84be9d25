package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.oog200.enums.PlaylistTypeEnum;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.core.util.ShortLinkGenerateUtil;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPlaylist;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPlaylistRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPlaylistMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaPlaylistAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPlaylistUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistPageVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistRelationVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPlaylistRelationService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPlaylistService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import jodd.mutable.MutableInteger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_DISABLE;

/**
 * Author:  hhl
 * Date:  2025/2/19 15:46
 */
@Service
public class ProjYogaPlaylistServiceImpl extends ServiceImpl<ProjYogaPlaylistMapper, ProjYogaPlaylist> implements IProjYogaPlaylistService {

    @Resource
    private IProjYogaPlaylistRelationService playlistRelationService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private ShortLinkGenerateUtil shortLinkGenerateUtil;

    private MutableInteger sortNoDefault = new MutableInteger(1000);

    @Override
    public List<ProjYogaPlaylistPageVO> pageQuery(Integer projId) {

        LambdaQueryWrapper<ProjYogaPlaylist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPlaylist::getProjId, projId);
        queryWrapper.orderByDesc(ProjYogaPlaylist::getSortNo);
        queryWrapper.orderByDesc(ProjYogaPlaylist::getId);

        List<ProjYogaPlaylist> playlists = list(queryWrapper);
        if (CollectionUtils.isEmpty(playlists)) {
            return Collections.emptyList();
        }

        List<Integer> playlistIds = playlists.stream().map(ProjYogaPlaylist::getId).collect(Collectors.toList());
        List<ProjYogaPlaylistRelation> relationList = playlistRelationService.listByPlaylistIds(playlistIds);
        Map<Integer, List<ProjYogaPlaylistRelation>> playlistSumMap = relationList.stream().collect(Collectors.groupingBy(ProjYogaPlaylistRelation::getProjYogaPlaylistId));

        List<ProjYogaPlaylistPageVO> pageVOList = playlists.stream().map(playlist -> {
            ProjYogaPlaylistPageVO playlistPageVO = new ProjYogaPlaylistPageVO();
            BeanUtils.copyProperties(playlist, playlistPageVO);

            if (playlistSumMap.containsKey(playlist.getId())) {
                playlistPageVO.setMusicNum(playlistSumMap.get(playlist.getId()).size());
            }
            return playlistPageVO;
        }).collect(Collectors.toList());
        return pageVOList;
    }

    @Override
    public ProjYogaPlaylistDetailVO getDetailById(Integer id) {

        ProjYogaPlaylist playlist = getById(id);
        if (Objects.isNull(playlist)) {
            return null;
        }

        ProjYogaPlaylistDetailVO detailVO = new ProjYogaPlaylistDetailVO();
        BeanUtils.copyProperties(playlist, detailVO);

        List<ProjYogaPlaylistRelationVO> playlistRelationVOList = playlistRelationService.listByPlaylistId(playlist.getId());
        detailVO.setPlaylistRelationVOList(playlistRelationVOList);
        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void insert(ProjYogaPlaylistAddReq playlistAddReq) {
        bizCheck(playlistAddReq, null);
        ProjYogaPlaylist playlist = new ProjYogaPlaylist();
        BeanUtils.copyProperties(playlistAddReq, playlist);

        playlist.setStatus(STATUS_DISABLE);
        playlist.setSortNo(sortNoDefault.get());
        playlist.setProjId(RequestContextUtils.getProjectId());

        save(playlist);
        handleRelation(playlistAddReq.getPlaylistRelationVOList(), playlist);
        projLmsI18nService.handleI18n(Collections.singletonList(playlist), playlist.getProjId());
    }

    private void handleRelation(List<ProjYogaPlaylistRelationVO> relationVOList, ProjYogaPlaylist playlist) {

        // 删除已有的关系
        playlistRelationService.deleteByPlaylistId(Lists.newArrayList(playlist.getId()));

        if (CollectionUtils.isEmpty(relationVOList)) {
            return;
        }

        // 新增关系
        List<ProjYogaPlaylistRelation> relationList = relationVOList.stream().map(relation -> {
            ProjYogaPlaylistRelation playlistRelation = new ProjYogaPlaylistRelation();
            BeanUtils.copyProperties(relation, playlistRelation);
            playlistRelation.setProjId(playlist.getProjId());
            playlistRelation.setProjYogaPlaylistId(playlist.getId());
            return playlistRelation;
        }).collect(Collectors.toList());
        playlistRelationService.saveBatch(relationList);

        // 只针对SOUNDSCAPE设置shortLink
        if (Objects.equals(playlist.getPlaylistType(), PlaylistTypeEnum.SOUNDSCAPE) || Objects.equals(playlist.getPlaylistType(), PlaylistTypeEnum.MEDITATION)) {
            // 在生成shortLink时，projId对应App(关联projInfo中的Dynamic Link)，"/music/" 对应App内的功能模块，musicId 用于定位歌曲
            relationList.forEach(relation -> relation.setShortLink(shortLinkGenerateUtil.getWorkShortLink("/music/" + relation.getProjYogaMusicId(), relation.getProjId())));
            playlistRelationService.updateBatchById(relationList);
        }
    }

    private void bizCheck(ProjYogaPlaylistAddReq playlistAddReq, Integer id) {



        LambdaQueryWrapper<ProjYogaPlaylist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPlaylist::getPlaylistName, playlistAddReq.getPlaylistName());
        queryWrapper.eq(ProjYogaPlaylist::getPlaylistType, playlistAddReq.getPlaylistType());

        queryWrapper.eq(ProjYogaPlaylist::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.ne(Objects.nonNull(id), ProjYogaPlaylist::getId, id);
        queryWrapper.last("limit 1");

        ProjYogaPlaylist playlist = getOne(queryWrapper);
        if (Objects.nonNull(playlist)) {
            throw new BizException("Playlist name is existed.");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void sort(IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (!CollectionUtils.isEmpty(idList)) {
            int sortNoIndex = sortNoDefault.get();
            for (Integer id : idList) {
                LambdaUpdateWrapper<ProjYogaPlaylist> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProjYogaPlaylist::getId, id);
                wrapper.eq(ProjYogaPlaylist::getProjId, RequestContextUtils.getProjectId());
                wrapper.set(ProjYogaPlaylist::getSortNo, sortNoIndex);
                wrapper.set(ProjYogaPlaylist::getUpdateTime, LocalDateTime.now());
                wrapper.set(ProjYogaPlaylist::getUpdateUser, RequestContextUtils.getLoginUserName());
                update(new ProjYogaPlaylist(), wrapper);
                sortNoIndex--;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(ProjYogaPlaylistUpdateReq playlistUpdateReq) {

        bizCheck(playlistUpdateReq, playlistUpdateReq.getId());
        ProjYogaPlaylist playlist = getById(playlistUpdateReq.getId());

        BeanUtils.copyProperties(playlistUpdateReq, playlist);
        updateById(playlist);
        handleRelation(playlistUpdateReq.getPlaylistRelationVOList(), playlist);
        projLmsI18nService.handleI18n(Collections.singletonList(playlist), playlist.getProjId());
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaPlaylist> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPlaylist::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPlaylist::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaPlaylist::getId, idList);
        this.update(new ProjYogaPlaylist(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaPlaylist> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPlaylist::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaPlaylist::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPlaylist::getId, idList);
        this.update(new ProjYogaPlaylist(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjYogaPlaylist> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPlaylist::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjYogaPlaylist::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjYogaPlaylist::getId, idList);
        this.update(new ProjYogaPlaylist(), wrapper);
    }
}
