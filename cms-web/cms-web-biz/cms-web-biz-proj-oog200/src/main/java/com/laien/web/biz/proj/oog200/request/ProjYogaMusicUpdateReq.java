package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音乐表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaMusicUpdateReq对象")
public class ProjYogaMusicUpdateReq extends ProjYogaMusicAddReq {

    private Integer id;

}
