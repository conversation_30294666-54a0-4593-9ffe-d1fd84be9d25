package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "poseLibrary详情", description = "poseLibrary详情")
public class ResPoseLibraryDetailVO {

    @ApiModelProperty(value = "数据id", required = true)
    private Integer id;

    @ApiModelProperty(value = "名称,最多100个字符", required = true)
    private String poseName;

    @ApiModelProperty(value = "梵文名称，最多100个字符", required = true)
    private String sanskritName;

    @ApiModelProperty(value = "pose图 url地址", required = true)
    private String animationUrl;

    @ApiModelProperty(value = "是否为basic 1是 0否")
    private Integer basic;

    @ApiModelProperty(value = "difficulty", required = true)
    private String difficulty;

    @ApiModelProperty(value = "position", required = true)
    private String position;

    @ApiModelProperty(value = "focus", required = true)
    private List<String> focusList;

    @ApiModelProperty(value = "简介，最多1000字符，去除前后空格", required = true)
    private String description;

    @ApiModelProperty(value = "第三方视频链接", required = true)
    private String videoLinkUrl;

}
