package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjUnit;
import com.laien.web.biz.proj.oog200.mapper.ProjUnitMapper;
import com.laien.web.biz.proj.oog200.response.ProjUnitVO;
import com.laien.web.biz.proj.oog200.service.IProjUnitService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * unit 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Service
public class ProjUnitServiceImpl extends ServiceImpl<ProjUnitMapper, ProjUnit> implements IProjUnitService {

    @Override
    public List<ProjUnitVO> query(Integer projId) {
        LambdaQueryWrapper<ProjUnit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjUnit::getProjId, projId)
                .eq(ProjUnit::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjUnit> unitList = baseMapper.selectList(wrapper);
        List<ProjUnitVO> unitVOList = new ArrayList<>();
        if (CollUtil.isEmpty(unitList)) {
            return unitVOList;
        }
        for (ProjUnit unit : unitList) {
            ProjUnitVO unitVO = new ProjUnitVO();
            BeanUtils.copyProperties(unit, unitVO);
            unitVOList.add(unitVO);
        }
        return unitVOList;
    }
}
