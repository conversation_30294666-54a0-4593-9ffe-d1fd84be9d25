package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: transition新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "transition新增", description = "transition新增")
public class ResTransitionAddReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "图片")
    private String imageUrl;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "名字文本")
    private String nameScript;

    @ApiModelProperty(value = "guidance文本")
    private String guidanceScript;


}
