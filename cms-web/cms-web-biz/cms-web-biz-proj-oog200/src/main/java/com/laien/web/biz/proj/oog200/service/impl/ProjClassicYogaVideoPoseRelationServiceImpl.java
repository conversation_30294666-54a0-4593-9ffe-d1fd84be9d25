package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjClassicYogaVideoPoseRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjClassicYogaVideoPoseRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjClassicYogaVideoPoseRelationService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/11/6 16:41
 */
@Service
public class ProjClassicYogaVideoPoseRelationServiceImpl extends ServiceImpl<ProjClassicYogaVideoPoseRelationMapper, ProjClassicYogaVideoPoseRelation> implements IProjClassicYogaVideoPoseRelationService {


    @Override
    public List<ProjClassicYogaVideoPoseRelation> listRelationByYogaVideoIds(Collection<Integer> yogaVideoIds) {

        if (CollectionUtils.isEmpty(yogaVideoIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjClassicYogaVideoPoseRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjClassicYogaVideoPoseRelation::getResYogaVideoId, yogaVideoIds);
        return list(queryWrapper);
    }

    @Override
    public List<ProjClassicYogaVideoPoseRelation> listRelationByPoseVideoIds(Collection<Integer> poseVideoIds) {

        if (CollectionUtils.isEmpty(poseVideoIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjClassicYogaVideoPoseRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjClassicYogaVideoPoseRelation::getProjYogaPoseVideoId, poseVideoIds);
        return list(queryWrapper);
    }


    @Override
    public void deleteByYogaVideoId(Integer yogaVideoId) {

        if (Objects.isNull(yogaVideoId)) {
            return;
        }

        LambdaUpdateWrapper<ProjClassicYogaVideoPoseRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjClassicYogaVideoPoseRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjClassicYogaVideoPoseRelation::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ProjClassicYogaVideoPoseRelation::getUpdateUser, RequestContextUtils.getLoginUserName());

        updateWrapper.eq(ProjClassicYogaVideoPoseRelation::getResYogaVideoId, yogaVideoId);
        update(updateWrapper);
    }
}
