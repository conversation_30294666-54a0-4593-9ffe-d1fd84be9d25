package com.laien.web.biz.proj.oog200.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAward;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaAwardMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaAwardImportReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaAwardPageReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaAwardPageVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaAwardService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: hhl
 * @date: 2025/6/10
 */
@Service
@Slf4j
public class ProjYogaAwardServiceImpl extends ServiceImpl<ProjYogaAwardMapper, ProjYogaAward> implements IProjYogaAwardService {

    @Resource
    private Validator validator;

    @Override
    public PageRes<ProjYogaAwardPageVO> pageQuery(ProjYogaAwardPageReq pageReq) {

        LambdaQueryWrapper<ProjYogaAward> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(pageReq.getProductId()), ProjYogaAward::getProductId, pageReq.getProductId());
        queryWrapper.like(StringUtils.isNotBlank(pageReq.getAwardLink()), ProjYogaAward::getAwardLink, pageReq.getAwardLink());
        queryWrapper.eq(Objects.nonNull(pageReq.getDuration()), ProjYogaAward::getDuration, pageReq.getDuration());
        queryWrapper.eq(Objects.nonNull(pageReq.getUseFlag()), ProjYogaAward::getUseFlag, pageReq.getUseFlag());
        queryWrapper.orderByAsc(ProjYogaAward::getProductId).orderByAsc(ProjYogaAward::getExpiredTime);

        Page<ProjYogaAward> awardPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaAward> iPage = this.page(awardPage, queryWrapper);
        PageRes<ProjYogaAwardPageVO> pageRes = PageConverter.convert(iPage, ProjYogaAwardPageVO.class);
        return pageRes;
    }

    @Override
    public void delete(Collection<Integer> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        LambdaUpdateWrapper<ProjYogaAward> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjYogaAward::getId, ids);
        updateWrapper.eq(ProjYogaAward::getUseFlag, GlobalConstant.ONE);
        updateWrapper.set(ProjYogaAward::getDelFlag, GlobalConstant.ONE);
        update(updateWrapper);
    }

    @Scheduled(cron = "0 0 11 * * *")
    @Override
    public void awardManage() {

        LocalDateTime expireTime = LocalDateTime.now().plusDays(45);
        LambdaQueryWrapper<ProjYogaAward> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAward::getUseFlag, GlobalConstant.ZERO);
        queryWrapper.le(ProjYogaAward::getExpiredTime, expireTime);

        List<ProjYogaAward> expiredList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(expiredList)) {
            return;
        }

        Map<String, List<ProjYogaAward>> productAndAwardMap = expiredList.stream().collect(Collectors.groupingBy(ProjYogaAward::getProductId));
        productAndAwardMap.entrySet().forEach(entry -> {
            log.error("There are {} award expire time less than 45 days, product id is {}, please supplement in time.", entry.getValue().size(), entry.getKey());
        });
    }

    @Override
    public List<String> batchImport(MultipartFile file) {

        if (Objects.isNull(file)) {
            return Collections.emptyList();
        }

        List<ProjYogaAwardImportReq> awardImportReqList = parseYogaAwardImport(file);
        if (CollectionUtils.isEmpty(awardImportReqList)) {
            return Collections.emptyList();
        }

        List<String> errorMessageList = Lists.newLinkedList();
        List<ProjYogaAwardImportReq> validAwardList = filterYogaAwardImport(awardImportReqList, errorMessageList);
        if (!CollectionUtils.isEmpty(errorMessageList)) {
            return errorMessageList;
        }

        List<ProjYogaAward> awardEntityList = convert2Entity(validAwardList);
        saveBatch(awardEntityList);
        return Collections.emptyList();
    }

    private List<ProjYogaAward> convert2Entity(List<ProjYogaAwardImportReq> awardImportReqList) {

        Integer projId = RequestContextUtils.getProjectId();
        return awardImportReqList.stream().map(award -> {
            ProjYogaAward yogaAward = new ProjYogaAward();
            BeanUtils.copyProperties(award, yogaAward);
            yogaAward.setProjId(projId);
            yogaAward.setExpiredTime(getExpiredTime(award.getExpiredTime()));
            yogaAward.setUseFlag(GlobalConstant.ZERO);
            return yogaAward;
        }).collect(Collectors.toList());
    }

    private List<ProjYogaAwardImportReq> filterYogaAwardImport(List<ProjYogaAwardImportReq> awardImportReqList, List<String> errorMessageList) {

        LocalDateTime now = LocalDateTime.now();
        List<ProjYogaAwardImportReq> validAwardList = awardImportReqList.stream().filter(award -> {
            Set<ConstraintViolation<ProjYogaAwardImportReq>> violationSet = validator.validate(award, Group.class);
            if (!CollectionUtils.isEmpty(violationSet)) {
                List<String> errorInfoList = violationSet.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
                String errorInfo = JacksonUtil.toJsonString(errorInfoList);
                errorMessageList.add(String.format("%s : %s", award.getProductId(), errorInfo));
                return false;
            }

            LocalDateTime expiredTime = getExpiredTime(award.getExpiredTime());
            if (Objects.isNull(expiredTime)) {
                errorMessageList.add(String.format("%s : %s", award.getExpiredTime(), "Date format error"));
                return false;
            }

            if (expiredTime.isBefore(now)) {
                errorMessageList.add(String.format("%s : %s", award.getExpiredTime(), "Expired Time can't be less than now"));
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        return validAwardList;
    }


    private List<ProjYogaAwardImportReq> parseYogaAwardImport(MultipartFile file) {

        List<ProjYogaAwardImportReq> awardImportReqList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), ProjYogaAwardImportReq.class, new AnalysisEventListener<ProjYogaAwardImportReq>() {
                @Override
                public void invoke(ProjYogaAwardImportReq row, AnalysisContext analysisContext) {
                    awardImportReqList.add(row);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet(GlobalConstant.ZERO).doRead();
        } catch (IOException e) {
            log.warn("Import pose video error.");
            throw new BizException("Import pose video error.");
        }
        return awardImportReqList;
    }

    private LocalDateTime getExpiredTime(String expiredTime) {
        try {
            return LocalDateTime.parse(expiredTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return null;
        }
    }


}
