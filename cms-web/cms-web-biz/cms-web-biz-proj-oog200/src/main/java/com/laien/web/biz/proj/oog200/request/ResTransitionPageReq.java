package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: transition分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "transition分页", description = "transition分页")
public class ResTransitionPageReq extends PageReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "请求来源页面 video_add:video 添加修改页面")
    private String requestPage;

}
