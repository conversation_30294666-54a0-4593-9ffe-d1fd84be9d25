package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaProgram;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaProgramUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaProgramPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:55
 */
public interface IProjYogaProgramService extends IService<ProjYogaProgram> {

    /**
     * YogaProgram分页查询
     *
     * @param pageReq pageReq
     * @return ProjYogaProgramPageVO
     */
    PageRes<ProjYogaProgramPageVO> selectProgramPage(ProjYogaProgramPageReq pageReq);

    /**
     * YogaProgram新增
     *
     * @param addReq
     */
    void saveProgram(ProjYogaProgramAddReq addReq);

    /**
     * YogaProgram修改
     *
     * @param updateReq
     */
    void updateProgram(ProjYogaProgramUpdateReq updateReq);

    /**
     * ProjYogaProgramLevel详情
     *
     * @param videoId
     * @return ProjYogaProgramDetailVO
     */
    ProjYogaProgramDetailVO getDetailById(Integer programId);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
