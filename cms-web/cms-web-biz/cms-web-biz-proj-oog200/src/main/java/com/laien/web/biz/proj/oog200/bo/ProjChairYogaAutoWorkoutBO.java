/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkout;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p> chair yoga bo</p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@Accessors(chain = true)
public class ProjChairYogaAutoWorkoutBO {

    /**  用于辅助批量保存时确定关联关系 */
    private ProjChairYogaAutoWorkout entity;

    @ApiModelProperty(value = "锻炼部位，示例值 Upper Body 、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "难度，可选值有 Newbie,Be<PERSON>ner,Intermediate,Advanced")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "video的 2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称")
    private String audioShortJson;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    /**  多语言音频LongJson 不包含默认语言 */
    private Map<String,String> multiLanguageAudioLongJson ;

    /**  多语言音频ShortJson 不包含默认语言 */
    private Map<String,String> multiLanguageAudioShortJson ;

    /**  该workout对应的是ID列表 */
    private List<Integer> videoIdList;

    /**  该段视频的真实时长 */
    private Map<Integer, AtomicInteger> videoRealDuration;


}