package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.DishStyleEnum;
import com.laien.common.oog200.enums.DishTypeEnum;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Dish
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjDishListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "类型")
    private List<DishTypeEnum> typeList;

    @ApiModelProperty(value = "风格")
    private List<DishStyleEnum> styleList;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "resourceVideo切片任务状态")
    private TaskResourceSectionStatusEnums resourceVideoTaskStatus;

    @ApiModelProperty(value = "源视频地址")
    private String resourceVideoUrl;
}
