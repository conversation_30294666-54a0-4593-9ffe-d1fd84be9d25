/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.handler;

import cn.hutool.core.util.URLUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.common.oog200.enums.PoseDirectionEnum;
import com.laien.common.oog200.enums.PoseTypeEnum;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog200.bo.*;
import com.laien.web.biz.proj.oog200.config.Oog200BizConfig;
import com.laien.web.biz.proj.oog200.constant.PoseWorkoutConstant;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseTransition;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideoConnection;
import com.laien.web.biz.proj.oog200.i18n.BaseVideoI18n;
import com.laien.web.biz.proj.oog200.i18n.I18nAudioUtil;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseVideoAudioDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseTransitionService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoAudioService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoConnectionService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoService;
import com.laien.web.biz.resource.service.IResSoundService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>ProjYogaPoseWorkout 文件生成 </p>
 * <p>按原先业务逻辑，单个video生成失败不影响同一批次其他video的生成</p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Slf4j
@Component
public class ProjYogaPoseWorkoutFileHandler {

    private static final int PLAY_ROUND = 3;
    private static final String AUDIO_DIR_KEY = "project-yoga-pose-workout-json";
    private static final String M3U8_DIR_KEY = "project-yoga-pose-workout-m3u8";

    @Resource
    private Oog200BizConfig oog200BizConfig;

    @Resource
    private IProjYogaPoseVideoAudioService yogaPoseVideoAudioService;

    @Resource
    private FileService fileService;

    @Resource
    private IProjYogaPoseTransitionService yogaPoseTransitionService;

    @Resource
    private IProjYogaPoseVideoConnectionService yogaPoseVideoConnectionService;

    @Resource
    private IProjYogaPoseVideoService yogaPoseVideoService;

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    private IResSoundService resSoundService;

    @Resource
    private I18nAudioUtil i18nAudioUtil;

    public List<ProjYogaPoseWorkoutBO> generate(List<ProjYogaPoseVideo> poseVideoList, Integer projId, String operationUser) {

        // 加载项目语言
        List<String> projectLanguages = projInfoService.getLanguagesById(projId);
        List<String> excludeEnLanguages = projectLanguages.stream().filter(language -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)).collect(Collectors.toList());

        // 获取多语言Pose video 翻译结果
        List<BaseVideoI18n> videoI18nList = poseVideoList.stream().map(video -> new BaseVideoI18n(video.getCoreVoiceConfigI18nId(), video.getId(), video.getName(), "")).collect(Collectors.toList());
        Map<Integer,Map<String, YogaVideoI18nBO>> multiLanguagePoseVideoAudioMap = i18nAudioUtil.checkAndConvert2I18nMap(videoI18nList, excludeEnLanguages, true, false);

        // 系统音配置获取
        Video200SysSoundBO sysSoundBO = oog200BizConfig.getOog200();
        String ready2GoAudioName = sysSoundBO.getReady2GoPoseAudio();
        String emptyAudio = sysSoundBO.getEmptyAudio();
        AudioJsonBO ready2GoAudioBO = sysSoundBO.getAudio(ready2GoAudioName);
        AudioJsonBO emptyAudioBO = sysSoundBO.getAudio(emptyAudio);
        try (UploadTaskExecutor uploadTaskExecutor = new UploadTaskExecutor(poseVideoList.size() * (GlobalConstant.FOUR + excludeEnLanguages.size() * 2))) {
            List<ProjYogaPoseWorkoutBO> result = poseVideoList.stream().map(poseVideo -> {
                try {
                    // 逐一校验数据
                    validatePoseVideo(poseVideo);
                    return generateWorkoutBO(operationUser, poseVideo, ready2GoAudioBO,emptyAudioBO, uploadTaskExecutor, excludeEnLanguages,multiLanguagePoseVideoAudioMap);
                } catch (BizException e) {
                    log.error("pose video validate failed, poseVideoId:{}", poseVideo.getId(), e);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            // 等待所有上传任务执行完成或超时
            uploadTaskExecutor.join();
            return result;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("pose video yoga workout file failed", e);
            throw new BizException("wall pilates yoga workout file failed");
        }
    }

    private ProjYogaPoseWorkoutBO generateWorkoutBO(String operationUser, ProjYogaPoseVideo poseVideo, AudioJsonBO ready2GoAudioBO,AudioJsonBO emptyAudioBO, UploadTaskExecutor uploadTaskExecutor, List<String> excludeEnLanguages,Map<Integer,Map<String, YogaVideoI18nBO>> multiLanguagePoseVideoAudioMap) {
        // 默认语言数据生成数据
        PoseWorkoutGenerateBO generateBO = createGenerateBO(poseVideo, operationUser, ready2GoAudioBO);
        // duration 和 calorie
        BigDecimal totalCalorie = generateBO.getPoseVideoBOList().stream().map(PoseVideoBO::getCalorie).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer totalDuration = generateBO.getPoseVideoBOList().stream().map(PoseVideoBO::getVideoDuration).filter(Objects::nonNull).reduce(Integer::sum).orElse(GlobalConstant.ZERO);

        ProjYogaPoseWorkoutBO poseWorkoutBO = assemblePoseWorkout(totalDuration, totalCalorie, poseVideo, operationUser);
        // 视频
        Pair<TsTextMergeBO, List<TsMergeBO>> m3u8Pair = generateM3u8(generateBO);
        uploadTaskExecutor.execute(() -> {
            UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(m3u8Pair.getLeft(), M3U8_DIR_KEY);
            poseWorkoutBO.setVideoM3u8Url(videoR2Info.getFileRelativeUrl());
        });
        uploadTaskExecutor.execute(() -> {
            UploadFileInfoRes video2532Info = fileService.uploadMergeTSForM3U8R2(m3u8Pair.getRight(), M3U8_DIR_KEY);
            poseWorkoutBO.setVideo2532Url(video2532Info.getFileRelativeUrl());
        });
        // 默认语言音频文件
        Pair<List<AudioJsonBO>, List<AudioJsonBO>> audioJsonPair = generateAudioJson(generateBO);
        uploadTaskExecutor.execute(() -> {
            UploadFileInfoRes audioShortInfo = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonPair.getLeft()),AUDIO_DIR_KEY);
            poseWorkoutBO.setAudioShortJson(audioShortInfo.getFileRelativeUrl());
            // 将默认语言数据保存到多语言数据中去
            poseWorkoutBO.getMultiLanguageAudioShortJson().put(GlobalConstant.DEFAULT_LANGUAGE, audioShortInfo.getFileRelativeUrl());
        });
        uploadTaskExecutor.execute(() -> {
            UploadFileInfoRes audioLongInfo = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonPair.getRight()),AUDIO_DIR_KEY);
            poseWorkoutBO.setAudioLongJson(audioLongInfo.getFileRelativeUrl());
            // 将默认语言数据保存到多语言数据中去
            poseWorkoutBO.getMultiLanguageAudioLongJson().put(GlobalConstant.DEFAULT_LANGUAGE, audioLongInfo.getFileRelativeUrl());
        });
        // 默认语言 video mask
        List<VideoMaskBO> videoMaskList = generateVideoMask(generateBO);
        poseWorkoutBO.setVideoMaskJson(JacksonUtil.toJsonString(videoMaskList));
        // 多语言数据处理，多语言只处理系统音，其他为空
        poseWorkoutBO.setMultiLanguageAudioLongJson(new ConcurrentHashMap<>());
        poseWorkoutBO.setMultiLanguageAudioShortJson(new ConcurrentHashMap<>());
        poseWorkoutBO.setMultiLanguageVideoMaskJson(new ConcurrentHashMap<>());
        // 将默认语言video mask 保存到多语言数据中去
        poseWorkoutBO.getMultiLanguageVideoMaskJson().put(GlobalConstant.DEFAULT_LANGUAGE, poseWorkoutBO.getVideoMaskJson());
        excludeEnLanguages.forEach(language -> {
            // 修改多语言生成参数
            PoseWorkoutGenerateBO generateBOWithLanguage = createGenerateBOWithLanguage(generateBO,emptyAudioBO, multiLanguagePoseVideoAudioMap.get(poseVideo.getId()).get(language));
            // 生成音频数据
            Pair<List<AudioJsonBO>, List<AudioJsonBO>> audioJsonPairWithLanguage = generateAudioJson(generateBOWithLanguage);
            uploadTaskExecutor.execute(() -> {
                // 多语言数据中有可能存在空音频数据，过滤掉它
                UploadFileInfoRes audioShortInfo = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonPairWithLanguage.getLeft()),AUDIO_DIR_KEY);
                poseWorkoutBO.getMultiLanguageAudioShortJson().put(language, audioShortInfo.getFileRelativeUrl());
            });
            uploadTaskExecutor.execute(() -> {
                UploadFileInfoRes audioLongInfo = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioJsonPairWithLanguage.getRight()),AUDIO_DIR_KEY);
                poseWorkoutBO.getMultiLanguageAudioLongJson().put(language, audioLongInfo.getFileRelativeUrl());
            });
            // 生成mask video
            List<VideoMaskBO> videoMaskListWithLanguage = generateVideoMask(generateBOWithLanguage);
            videoMaskListWithLanguage = videoMaskListWithLanguage.stream().filter(videoMaskBO -> StringUtils.isNotBlank(videoMaskBO.getAudioUrl())).collect(Collectors.toList());
            poseWorkoutBO.getMultiLanguageVideoMaskJson().put(language, JacksonUtil.toJsonString(videoMaskListWithLanguage));

        });
        return poseWorkoutBO;
    }

    /**
     * <p>基于默认语言生成特定语言的生成参数，将系统音之外都置换为空数据</p>
     *
     * @param generateBO      默认语言生成参数
     * @param language        指定的语言，有朝一日或可替换为真实翻译后的数据
     * @param ready2GoAudioBO 指定语言的ready2GoAudioBO
     * @return com.laien.web.biz.proj.oog200.bo.PoseWorkoutGenerateBO
     * <AUTHOR>
     * @date 2025/4/15 17:15
     */
    private PoseWorkoutGenerateBO createGenerateBOWithLanguage(PoseWorkoutGenerateBO generateBO, AudioJsonBO emptyAudioBO,YogaVideoI18nBO poseVideoI18n) {

        // 使用Json深拷贝数据
        PoseWorkoutGenerateBO generateBOWithLanguage = JacksonUtil.parseObject(JacksonUtil.toJsonString(generateBO), PoseWorkoutGenerateBO.class);
        // 替换系统音为系统指定的数据
        int duration = emptyAudioBO.getTime().intValue();
        String path = URLUtil.getPath(emptyAudioBO.getUrl()).substring(GlobalConstant.ONE);
        Lists.newArrayList(generateBOWithLanguage.getRoundOneAudio(), generateBOWithLanguage.getRoundTwoAudio(), generateBOWithLanguage.getRoundThreeAudio()).forEach(audioDetailVO -> {
            audioDetailVO.setFirstGuidanceAudioUrl(path);
            audioDetailVO.setFirstGuidanceAudioDuration(duration);

            audioDetailVO.setSecondGuidanceAudioUrl(path);
            audioDetailVO.setSecondGuidanceAudioDuration(duration);

            audioDetailVO.setThirdGuidanceAudioUrl(path);
            audioDetailVO.setThirdGuidanceAudioDuration(duration);

            audioDetailVO.setFourthGuidanceAudioUrl(path);
            audioDetailVO.setFourthGuidanceAudioDuration(duration);

            audioDetailVO.setFifthGuidanceAudioUrl(path);
            audioDetailVO.setFifthGuidanceAudioDuration(duration);

            audioDetailVO.setSixthGuidanceAudioUrl(path);
            audioDetailVO.setSixthGuidanceAudioDuration(duration);
        });
        // 将 round one first 替换为翻译后的数据
        generateBOWithLanguage.getRoundOneAudio().setFirstGuidanceAudioUrl(poseVideoI18n.getNameScriptFemale());
        generateBOWithLanguage.getRoundOneAudio().setFirstGuidanceAudioDuration(poseVideoI18n.getNameScriptFemaleDuration());

        return generateBOWithLanguage;
    }

    private Pair<TsTextMergeBO, List<TsMergeBO>> generateM3u8(PoseWorkoutGenerateBO generateBO) {

        TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
        List<TsMergeBO> tsTextMerge2532BO = Lists.newArrayList();
        for (int i = GlobalConstant.ZERO; i < generateBO.getPoseVideoBOList().size(); i++) {
            PoseVideoBO poseVideoBO = generateBO.getPoseVideoBOList().get(i);
            assemble2532M3u8Text(poseVideoBO, tsTextMerge2532BO);
            assembleDynamicM3u8Text(poseVideoBO, tsTextMergeDynamicBO);
        }
        return Pair.of(tsTextMergeDynamicBO, tsTextMerge2532BO);
    }

    private List<VideoMaskBO> generateVideoMask(PoseWorkoutGenerateBO generateBO) {
        int duration = GlobalConstant.ZERO;
        List<VideoMaskBO> videoMaskList = Lists.newArrayList();
        for (int i = GlobalConstant.ZERO; i < generateBO.getPoseVideoBOList().size(); i++) {
            PoseVideoBO poseVideoBO = generateBO.getPoseVideoBOList().get(i);
            assembleVideoMask(generateBO, videoMaskList, duration, i);
            duration += Optional.of(poseVideoBO.getVideoDuration()).orElse(GlobalConstant.ZERO);
        }

        return videoMaskList;
    }

    private Pair<List<AudioJsonBO>, List<AudioJsonBO>> generateAudioJson(PoseWorkoutGenerateBO generateBO) {

        int duration = GlobalConstant.ZERO;
        List<AudioJsonBO> longAudioList = Lists.newArrayList();
        List<AudioJsonBO> shortAudioList = Lists.newArrayList();
        for (int i = GlobalConstant.ZERO; i < generateBO.getPoseVideoBOList().size(); i++) {
            PoseVideoBO poseVideoBO = generateBO.getPoseVideoBOList().get(i);
            assembleGuidanceAudio(generateBO, poseVideoBO, longAudioList, duration, i);
            assembleShortAudio(generateBO, poseVideoBO, shortAudioList, duration, i);
            duration += Optional.of(poseVideoBO.getVideoDuration()).orElse(GlobalConstant.ZERO);
        }

        return Pair.of(shortAudioList, longAudioList);
    }

    private void assemble2532M3u8Text(PoseVideoBO poseVideoBO, List<TsMergeBO> tsTextMerge2532BO) {

        tsTextMerge2532BO.add(new TsMergeBO(fileService.getAbsoluteR2Url(poseVideoBO.getVideoUrl()), poseVideoBO.getVideoDuration()));
    }

    private void assembleDynamicM3u8Text(PoseVideoBO poseVideoBO, TsTextMergeBO tsTextMergeDynamicBO) {

        tsTextMergeDynamicBO.addM3u8Text(poseVideoBO.getM3u8Text2k(), poseVideoBO.getM3u8Text1080p(), poseVideoBO.getM3u8Text720p(), poseVideoBO.getM3u8Text480p(), poseVideoBO.getM3u8Text360p());
    }

    private ProjYogaPoseWorkoutBO assemblePoseWorkout(int duration, BigDecimal calorie, ProjYogaPoseVideo poseVideo, String userName) {

        ProjYogaPoseWorkoutBO poseWorkout = new ProjYogaPoseWorkoutBO();
        poseWorkout.setPoseVideo(poseVideo);
        poseWorkout.setCalorie(calorie);
        poseWorkout.setDuration(duration);
        poseWorkout.setName(poseVideo.getName());
        poseWorkout.setStatus(GlobalConstant.STATUS_NOT_READY);

        poseWorkout.setProjId(poseVideo.getProjId());
        poseWorkout.setProjYogaPoseVideoId(poseVideo.getId());
        poseWorkout.setCreateUser(userName);

        poseWorkout.setCoverImgUrl(poseVideo.getImageUrl());
        poseWorkout.setDetailImgUrl(poseVideo.getImageUrl());
        return poseWorkout;
    }

    private void assembleGuidanceAudio(PoseWorkoutGenerateBO generateBO, PoseVideoBO poseVideoBO,
                                       List<AudioJsonBO> guidanceAudioList, int duration, int videoIndex) {

        if (videoIndex == GlobalConstant.ZERO) {
            setAudio4Round1_1(generateBO, poseVideoBO, guidanceAudioList, duration, true);
            return;
        }

        if (videoIndex == GlobalConstant.FIVE) {
            setAudio4Round2_1(generateBO, poseVideoBO, guidanceAudioList, duration, true);
            return;
        }

        if (videoIndex == GlobalConstant.TEN) {
            setAudio4Round3_1(generateBO, poseVideoBO, guidanceAudioList, duration);
            return;
        }

        AudioJsonBO audioJsonBO = generateBO.getAudioByAudioIndex(videoIndex);
        if (Objects.nonNull(audioJsonBO) && !StringUtils.isEmpty(audioJsonBO.getUrl())) {
            int playTime = duration + generateBO.getSplitOne();
            assembleAudioListByHalfUp(guidanceAudioList, audioJsonBO.getId(), audioJsonBO.getUrl(), audioJsonBO.getName(), playTime);
        }
    }

    private void setAudio4Round1_1(PoseWorkoutGenerateBO generateBO, PoseVideoBO poseVideoBO,
                                   List<AudioJsonBO> guidanceAudioList, int duration, boolean guidanceAudio) {
        // 1-2
        ProjYogaPoseVideoAudioDetailVO roundOneAudio = generateBO.getRoundOneAudio();
        if (guidanceAudio && !StringUtils.isEmpty(roundOneAudio.getSecondGuidanceAudioUrl())) {
            int playTime = duration + poseVideoBO.getVideoDuration() - roundOneAudio.getSecondGuidanceAudioDuration() - generateBO.getSplitTwo();
            assembleAudioListByHalfUp(guidanceAudioList, PoseWorkoutConstant.ROUND_1_2, fileService.getAbsoluteUrl(roundOneAudio.getSecondGuidanceAudioUrl()), FireBaseUrlSubUtils.getFileName(roundOneAudio.getSecondGuidanceAudioUrl()), playTime);
        }
    }

    private void setAudio4Round2_1(PoseWorkoutGenerateBO generateBO, PoseVideoBO poseVideoBO,
                                   List<AudioJsonBO> guidanceAudioList, int duration, boolean guidanceAudio) {
        // 2-2
        ProjYogaPoseVideoAudioDetailVO roundTwoAudio = generateBO.getRoundTwoAudio();
        if (guidanceAudio && !StringUtils.isEmpty(roundTwoAudio.getSecondGuidanceAudioUrl())) {
            int playTime = duration + poseVideoBO.getVideoDuration() - roundTwoAudio.getSecondGuidanceAudioDuration() - generateBO.getSplitTwo();
            assembleAudioListByHalfUp(guidanceAudioList, PoseWorkoutConstant.ROUND_2_2, fileService.getAbsoluteUrl(roundTwoAudio.getSecondGuidanceAudioUrl()), FireBaseUrlSubUtils.getFileName(roundTwoAudio.getSecondGuidanceAudioUrl()), playTime);
        }
    }

    private void setAudio4Round3_1(PoseWorkoutGenerateBO generateBO, PoseVideoBO poseVideoBO,
                                   List<AudioJsonBO> guidanceAudioList, int duration) {
        // ready 2 go audio
        AudioJsonBO ready2GoAudio = generateBO.getReady2GoAudio();
        int playTime = duration + poseVideoBO.getVideoDuration() - ready2GoAudio.getTime().intValue();
        assembleAudioListByHalfUp(guidanceAudioList, ready2GoAudio.getId(), ready2GoAudio.getUrl(), ready2GoAudio.getName(), playTime);
    }

    private void assembleVideoMask(PoseWorkoutGenerateBO generateBO, List<VideoMaskBO> videoMaskList, int duration, int videoIndex) {

        int playtime = duration + generateBO.getVideoMaskDelay();
        if (videoIndex == GlobalConstant.ZERO) {
            VideoMaskBO videoMaskBO = wrapVideoMaskBo(generateBO.getRoundOneAudio(), PoseWorkoutConstant.ROUND_1_1, playtime, generateBO.getFirstVideoMaskDuration(), generateBO.getFirstVideoMaskBuffer());
            videoMaskList.add(videoMaskBO);
        }

        if (videoIndex == GlobalConstant.FIVE) {
            VideoMaskBO videoMaskBO = wrapVideoMaskBo(generateBO.getRoundTwoAudio(), PoseWorkoutConstant.ROUND_2_1, playtime, generateBO.getSecondVideoMaskDuration(), generateBO.getSecondVideoMaskBuffer());
            videoMaskList.add(videoMaskBO);
        }

        if (videoIndex == GlobalConstant.TEN) {
            VideoMaskBO videoMaskBO = wrapVideoMaskBo(generateBO.getRoundThreeAudio(), PoseWorkoutConstant.ROUND_3_1, playtime, generateBO.getThirdVideoMaskDuration(), generateBO.getThirdVideoMaskBuffer());
            videoMaskList.add(videoMaskBO);
        }
    }

    private VideoMaskBO wrapVideoMaskBo(ProjYogaPoseVideoAudioDetailVO audioDetailVO, String audioId,
                                        int playtime, Integer duration, Integer buffer) {

        String audioUrl = fileService.getAbsoluteUrl(audioDetailVO.getFirstGuidanceAudioUrl());
        String audioName = FireBaseUrlSubUtils.getFileName(audioDetailVO.getFirstGuidanceAudioUrl());
        BigDecimal audioPlaytime = convertInt2BigDecimalByHalfUp(duration);

        BigDecimal videoMaskPlaytime = convertInt2BigDecimalByHalfUp(playtime);
        int videoMaskDuration = duration + audioDetailVO.getFirstGuidanceAudioDuration() + buffer;
        BigDecimal videoMaskDurationTime = convertInt2BigDecimalByHalfUp(videoMaskDuration);
        return ofVideoMaskBO(audioUrl, audioId, audioName, audioPlaytime, videoMaskPlaytime, videoMaskDurationTime);
    }

    private VideoMaskBO ofVideoMaskBO(String audioUrl, String audioId, String audioName,
                                      BigDecimal audioPlaytime, BigDecimal videoMaskPlaytime, BigDecimal videoMaskDuration) {

        VideoMaskBO videoMaskBO = new VideoMaskBO();
        videoMaskBO.setAudioId(audioId);
        videoMaskBO.setAudioUrl(audioUrl);
        videoMaskBO.setAudioPlaytime(audioPlaytime);

        videoMaskBO.setAudioName(audioName);
        videoMaskBO.setTime(videoMaskPlaytime);
        videoMaskBO.setDuration(videoMaskDuration);
        return videoMaskBO;
    }

    private BigDecimal convertInt2BigDecimalByHalfUp(int time) {

        return new BigDecimal(time).divide(new BigDecimal(GlobalConstant.SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP);
    }

    private void assembleShortAudio(PoseWorkoutGenerateBO generateBO, PoseVideoBO poseVideoBO,
                                    List<AudioJsonBO> shortAudioList, int duration, int videoIndex) {

        if (videoIndex == GlobalConstant.ZERO) {
            setAudio4Round1_1(generateBO, poseVideoBO, shortAudioList, duration, false);
            return;
        }

        if (videoIndex == GlobalConstant.FIVE) {
            setAudio4Round2_1(generateBO, poseVideoBO, shortAudioList, duration, false);
            return;
        }

        if (videoIndex == GlobalConstant.TEN) {
            setAudio4Round3_1(generateBO, poseVideoBO, shortAudioList, duration);
            return;
        }
    }

    private void assembleAudioListByHalfUp(List<AudioJsonBO> audioList, String id, String url, String name, int playTime) {

        AudioJsonBO audioJsonBO = new AudioJsonBO(id, url, name, convertInt2BigDecimalByHalfUp(playTime));
        audioList.add(audioJsonBO);
    }

    /**
     * <p>组装生成参数</p>
     *
     * @param poseVideo       视频
     * @param operationUser   创作人
     * @param ready2GoAudioBO 系统音，获取系统音涉及数据库查询，这里直接传入避免多次调用多次查询
     * @return com.laien.web.biz.proj.oog200.bo.PoseWorkoutGenerateBO
     * <AUTHOR>
     * @date 2025/4/15 17:02
     */
    private PoseWorkoutGenerateBO createGenerateBO(ProjYogaPoseVideo poseVideo, String operationUser, AudioJsonBO ready2GoAudioBO) {

        Video200SysSoundBO sysSoundBO = oog200BizConfig.getOog200();

        PoseWorkoutGenerateBO generateBO = new PoseWorkoutGenerateBO();
        generateBO.setReady2GoAudio(ready2GoAudioBO);

        generateBO.setSplitOne(sysSoundBO.getPoseAudioSplitOne());
        generateBO.setSplitTwo(sysSoundBO.getPoseAudioSplitTwo());
        generateBO.setVideoMaskDelay(sysSoundBO.getVideoMaskDelay());

        generateBO.setFirstVideoMaskDuration(sysSoundBO.getFirstVideoMaskDuration());
        generateBO.setFirstVideoMaskBuffer(sysSoundBO.getFirstVideoMaskBuffer());

        generateBO.setSecondVideoMaskDuration(sysSoundBO.getSecondVideoMaskDuration());
        generateBO.setSecondVideoMaskBuffer(sysSoundBO.getSecondVideoMaskBuffer());
        generateBO.setThirdVideoMaskDuration(sysSoundBO.getThirdVideoMaskDuration());
        generateBO.setThirdVideoMaskBuffer(sysSoundBO.getThirdVideoMaskBuffer());

        generateBO.setPoseVideo(poseVideo);
        generateBO.setOperationUserName(operationUser);
        generateBO.setRound(PLAY_ROUND);

        // 音频资源
        List<ProjYogaPoseVideoAudioDetailVO> audioDetailVOList = yogaPoseVideoAudioService.listByPoseVideoId(poseVideo.getId());
        if (CollectionUtils.isEmpty(audioDetailVOList) || audioDetailVOList.size() != generateBO.getRound()) {
            log.warn("Pose audio setting error, can't generate pose workout, pose video id is {}.", poseVideo.getId());
            throw new BizException("Pose audio setting error, can't generate pose workout.");
        }

        audioDetailVOList.sort(Comparator.comparing(ProjYogaPoseVideoAudioDetailVO::getRoundIndex));
        generateBO.setRoundOneAudio(audioDetailVOList.get(GlobalConstant.ZERO));
        generateBO.setRoundTwoAudio(audioDetailVOList.get(GlobalConstant.ONE));
        generateBO.setRoundThreeAudio(audioDetailVOList.get(GlobalConstant.TWO));
        // 音频资源
        assembleAudioMap(generateBO);

        // 视频资源
        setPoseVideo4PoseWorkout(generateBO);
        return generateBO;
    }

    private void setPoseVideo4PoseWorkout(PoseWorkoutGenerateBO generateBO) {

        ProjYogaPoseVideo mainPoseVideo = generateBO.getPoseVideo();
        if (Objects.equals(PoseDirectionEnum.LEFT.getPoseDirection(), mainPoseVideo.getPoseDirection())) {
            setLeftPoseVideo4PoseWorkout(generateBO);
        }

        if (Objects.equals(PoseDirectionEnum.CENTRAL.getPoseDirection(), mainPoseVideo.getPoseDirection())) {
            setCentralPoseVideo4PoseWorkout(generateBO);
        }
    }

    private void setCentralPoseVideo4PoseWorkout(PoseWorkoutGenerateBO generateBO) {

        // list transition
        ProjYogaPoseVideo mainPoseVideo = generateBO.getPoseVideo();
        List<ProjYogaPoseVideoConnection> poseVideo4StartList = yogaPoseVideoConnectionService.listByPoseVideoId(mainPoseVideo.getId());
        List<ProjYogaPoseVideoConnection> poseVideo4EndList = yogaPoseVideoConnectionService.listByNextPoseVideoId(mainPoseVideo.getId());
        if (CollectionUtils.isEmpty(poseVideo4StartList) || CollectionUtils.isEmpty(poseVideo4EndList)) {
            log.warn("Central pose connection setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose connection setting error, can't generate pose workout.");
        }

        // A -> B(poseVideo) -> A is true, other is false.
        ProjYogaPoseVideoConnection poseVideo4Start = poseVideo4StartList.get(GlobalConstant.ZERO);
        ProjYogaPoseVideoConnection poseVideo4End = poseVideo4EndList.stream()
                .filter(end -> Objects.equals(poseVideo4Start.getProjYogaPoseVideoNextId(), end.getProjYogaPoseVideoId())).findAny().get();
        if (Objects.isNull(poseVideo4End)) {
            log.warn("Central pose connection setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose connection setting error, can't generate pose workout.");
        }

        List<ProjYogaPoseVideo> poseVideoList = yogaPoseVideoService.listAllEnable(Lists.newArrayList(poseVideo4End.getProjYogaPoseVideoId()));
        if (CollectionUtils.isEmpty(poseVideoList)) {
            log.warn("Central pose connection setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose transition setting error, can't generate pose workout.");
        }

        Set<Integer> transitionSet = Sets.newHashSet(poseVideo4End.getProjYogaPoseTransitionId(), poseVideo4Start.getProjYogaPoseTransitionId());
        List<ProjYogaPoseTransition> poseTransitionList = yogaPoseTransitionService.listEnableTransition(transitionSet);
        if (CollectionUtils.isEmpty(poseTransitionList) || poseTransitionList.size() != transitionSet.size()) {
            log.warn("Central pose transition setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose transition setting error, can't generate pose workout.");
        }

        ProjYogaPoseVideo beginPoseVideo = poseVideoList.get(GlobalConstant.ZERO);
        Map<Integer, ProjYogaPoseTransition> transitionMap = poseTransitionList.stream().collect(Collectors.toMap(ProjYogaPoseTransition::getId, Function.identity()));
        ProjYogaPoseTransition transitionOne = transitionMap.get(poseVideo4End.getProjYogaPoseTransitionId());
        ProjYogaPoseTransition transitionTwo = transitionMap.get(poseVideo4Start.getProjYogaPoseTransitionId());

        List<PoseVideoBO> poseVideoBOList = Lists.newArrayList();
        for (int i = GlobalConstant.ZERO; i < PLAY_ROUND; i++) {

            poseVideoBOList.add(convert2PoseVideoBO(beginPoseVideo, true));
            poseVideoBOList.add(transitionConvert2PoseVideoBO(transitionOne));
            poseVideoBOList.add(convert2PoseVideoBO(mainPoseVideo, true));
            poseVideoBOList.add(convert2PoseVideoBO(mainPoseVideo, false));
            poseVideoBOList.add(transitionConvert2PoseVideoBO(transitionTwo));
        }
        generateBO.setPoseVideoBOList(poseVideoBOList);
    }


    private PoseVideoBO convert2PoseVideoBO(ProjYogaPoseVideo poseVideo, boolean front) {

        PoseVideoBO poseVideoBO = new PoseVideoBO();
        poseVideoBO.setCalorie(poseVideo.getCalorie());

        if (front) {
            poseVideoBO.setVideoUrl(poseVideo.getFrontVideoUrl());
            poseVideoBO.setVideoDuration(poseVideo.getFrontVideoDuration());
            poseVideoBO.setM3u8Text2k(poseVideo.getFrontM3u8Text2k());
            poseVideoBO.setM3u8Text1080p(poseVideo.getFrontM3u8Text1080p());
            poseVideoBO.setM3u8Text720p(poseVideo.getFrontM3u8Text720p());
            poseVideoBO.setM3u8Text480p(poseVideo.getFrontM3u8Text480p());
            poseVideoBO.setM3u8Text360p(poseVideo.getFrontM3u8Text360p());
        }

        if (!front) {
            poseVideoBO.setVideoUrl(poseVideo.getSideVideoUrl());
            poseVideoBO.setVideoDuration(poseVideo.getSideVideoDuration());
            poseVideoBO.setM3u8Text2k(poseVideo.getSideM3u8Text2k());
            poseVideoBO.setM3u8Text1080p(poseVideo.getSideM3u8Text1080p());
            poseVideoBO.setM3u8Text720p(poseVideo.getSideM3u8Text720p());
            poseVideoBO.setM3u8Text480p(poseVideo.getSideM3u8Text480p());
            poseVideoBO.setM3u8Text360p(poseVideo.getSideM3u8Text360p());
        }
        return poseVideoBO;
    }

    private PoseVideoBO transitionConvert2PoseVideoBO(ProjYogaPoseTransition poseTransition) {

        PoseVideoBO poseVideoBO = new PoseVideoBO();
        poseVideoBO.setCalorie(null);

        poseVideoBO.setVideoUrl(poseTransition.getFrontVideoUrl());
        poseVideoBO.setVideoDuration(poseTransition.getFrontVideoDuration());
        poseVideoBO.setM3u8Text2k(poseTransition.getFrontM3u8Text2k());
        poseVideoBO.setM3u8Text1080p(poseTransition.getFrontM3u8Text1080p());
        poseVideoBO.setM3u8Text720p(poseTransition.getFrontM3u8Text720p());
        poseVideoBO.setM3u8Text480p(poseTransition.getFrontM3u8Text480p());
        poseVideoBO.setM3u8Text360p(poseTransition.getFrontM3u8Text360p());
        return poseVideoBO;
    }

    private void setLeftPoseVideo4PoseWorkout(PoseWorkoutGenerateBO generateBO) {

        // left pose
        ProjYogaPoseVideo mainPoseVideo = generateBO.getPoseVideo();
        List<ProjYogaPoseVideoConnection> poseVideo4StartList = yogaPoseVideoConnectionService.listByPoseVideoId(mainPoseVideo.getId());
        List<ProjYogaPoseVideoConnection> poseVideo4EndList = yogaPoseVideoConnectionService.listByNextPoseVideoId(mainPoseVideo.getId());

        // can't be empty
        if (CollectionUtils.isEmpty(poseVideo4StartList) || CollectionUtils.isEmpty(poseVideo4EndList)) {
            log.warn("Left pose connection setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose connection setting error, can't generate pose workout.");
        }

        ProjYogaPoseVideoConnection leftVideo4Start = poseVideo4StartList.get(GlobalConstant.ZERO);
        ProjYogaPoseVideoConnection leftVideo4End = poseVideo4EndList.get(GlobalConstant.ZERO);

        ProjYogaPoseVideoConnection rightVideo4End = yogaPoseVideoConnectionService.getByVideoConnection(leftVideo4Start.getProjYogaPoseVideoNextId(), mainPoseVideo.getRightVideoId());
        if (Objects.isNull(rightVideo4End)) {
            log.warn("Right Pose connection setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose connection setting error, can't generate pose workout.");
        }

        ProjYogaPoseVideoConnection rightVideo4Start = yogaPoseVideoConnectionService.getByVideoConnection(mainPoseVideo.getRightVideoId(), leftVideo4End.getProjYogaPoseVideoId());
        if (Objects.isNull(rightVideo4Start)) {
            log.warn("Right Pose connection setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose connection setting error, can't generate pose workout.");
        }

        Set<Integer> transitionIdSet = Sets.newHashSet(leftVideo4End.getProjYogaPoseTransitionId(), leftVideo4Start.getProjYogaPoseTransitionId(), rightVideo4End.getProjYogaPoseTransitionId(), rightVideo4Start.getProjYogaPoseTransitionId());
        Collection<ProjYogaPoseTransition> transitionCollection = yogaPoseTransitionService.listEnableTransition(transitionIdSet);
        if (CollectionUtils.isEmpty(transitionCollection) || transitionCollection.size() != transitionIdSet.size()) {
            log.warn("Left-right Pose transition setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose transition setting error, can't generate pose workout.");
        }

        Map<Integer, ProjYogaPoseTransition> poseTransitionMap = transitionCollection.stream().collect(Collectors.toMap(ProjYogaPoseTransition::getId, Function.identity()));
        ProjYogaPoseTransition transitionOne = poseTransitionMap.get(leftVideo4End.getProjYogaPoseTransitionId());
        ProjYogaPoseTransition transitionTwo = poseTransitionMap.get(leftVideo4Start.getProjYogaPoseTransitionId());
        ProjYogaPoseTransition transitionThree = poseTransitionMap.get(rightVideo4End.getProjYogaPoseTransitionId());
        ProjYogaPoseTransition transitionFour = poseTransitionMap.get(rightVideo4Start.getProjYogaPoseTransitionId());

        Set<Integer> beginVideoIdSet = Sets.newHashSet(leftVideo4End.getProjYogaPoseVideoId(), leftVideo4Start.getProjYogaPoseVideoNextId(), mainPoseVideo.getRightVideoId());
        Collection<ProjYogaPoseVideo> poseVideoCollection = yogaPoseVideoService.listEnablePoseVideo(beginVideoIdSet);
        if (CollectionUtils.isEmpty(poseVideoCollection) || beginVideoIdSet.size() != poseVideoCollection.size()) {
            log.warn("Left-right Pose video setting error, can't generate pose workout, pose video id is {}.", mainPoseVideo.getId());
            throw new BizException("Pose video setting error, can't generate pose workout.");
        }

        Map<Integer, ProjYogaPoseVideo> beginPoseVideoMap = poseVideoCollection.stream().collect(Collectors.toMap(ProjYogaPoseVideo::getId, Function.identity()));
        ProjYogaPoseVideo beginOne = beginPoseVideoMap.get(leftVideo4End.getProjYogaPoseVideoId());
        ProjYogaPoseVideo beginTwo = beginPoseVideoMap.get(leftVideo4Start.getProjYogaPoseVideoNextId());
        ProjYogaPoseVideo rightPoseVideo = beginPoseVideoMap.get(mainPoseVideo.getRightVideoId());

        List<PoseVideoBO> poseVideoBOList = Lists.newArrayList();
        for (int i = GlobalConstant.ZERO; i < PLAY_ROUND; i++) {

            if (i == GlobalConstant.ONE) {
                poseVideoBOList.add(convert2PoseVideoBO(beginTwo, true));
                poseVideoBOList.add(transitionConvert2PoseVideoBO(transitionThree));
                poseVideoBOList.add(convert2PoseVideoBO(rightPoseVideo, true));
                poseVideoBOList.add(convert2PoseVideoBO(rightPoseVideo, false));
                poseVideoBOList.add(transitionConvert2PoseVideoBO(transitionFour));
                continue;
            }

            poseVideoBOList.add(convert2PoseVideoBO(beginOne, true));
            poseVideoBOList.add(transitionConvert2PoseVideoBO(transitionOne));
            poseVideoBOList.add(convert2PoseVideoBO(mainPoseVideo, true));
            poseVideoBOList.add(convert2PoseVideoBO(mainPoseVideo, false));
            poseVideoBOList.add(transitionConvert2PoseVideoBO(transitionTwo));
        }
        generateBO.setPoseVideoBOList(poseVideoBOList);
    }

    private void assembleAudioMap(PoseWorkoutGenerateBO generateBO) {

        Map<Integer, AudioJsonBO> videoIndexAndAudioMap = Maps.newHashMap();

        AudioJsonBO oneAudio = new AudioJsonBO();
        oneAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundOneAudio().getThirdGuidanceAudioUrl()));
        oneAudio.setId(PoseWorkoutConstant.ROUND_1_3);
        oneAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundOneAudio().getThirdGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.ONE, oneAudio);

        AudioJsonBO twoAudio = new AudioJsonBO();
        twoAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundOneAudio().getFourthGuidanceAudioUrl()));
        twoAudio.setId(PoseWorkoutConstant.ROUND_1_4);
        twoAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundOneAudio().getFourthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.TWO, twoAudio);

        AudioJsonBO threeAudio = new AudioJsonBO();
        threeAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundOneAudio().getFifthGuidanceAudioUrl()));
        threeAudio.setId(PoseWorkoutConstant.ROUND_1_5);
        threeAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundOneAudio().getFifthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.THREE, threeAudio);

        AudioJsonBO fourAudio = new AudioJsonBO();
        fourAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundOneAudio().getSixthGuidanceAudioUrl()));
        fourAudio.setId(PoseWorkoutConstant.ROUND_1_6);
        fourAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundOneAudio().getSixthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.FOUR, fourAudio);

        AudioJsonBO sixAudio = new AudioJsonBO();
        sixAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundTwoAudio().getThirdGuidanceAudioUrl()));
        sixAudio.setId(PoseWorkoutConstant.ROUND_2_3);
        sixAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundTwoAudio().getThirdGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.SIX, sixAudio);

        AudioJsonBO sevenAudio = new AudioJsonBO();
        sevenAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundTwoAudio().getFourthGuidanceAudioUrl()));
        sevenAudio.setId(PoseWorkoutConstant.ROUND_2_4);
        sevenAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundTwoAudio().getFourthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.SEVEN, sevenAudio);

        AudioJsonBO eightAudio = new AudioJsonBO();
        eightAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundTwoAudio().getFifthGuidanceAudioUrl()));
        eightAudio.setId(PoseWorkoutConstant.ROUND_2_5);
        eightAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundTwoAudio().getFifthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.EIGHT, eightAudio);

        AudioJsonBO nineAudio = new AudioJsonBO();
        nineAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundTwoAudio().getSixthGuidanceAudioUrl()));
        nineAudio.setId(PoseWorkoutConstant.ROUND_2_6);
        nineAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundTwoAudio().getSixthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.NINE, nineAudio);

        AudioJsonBO twelveAudio = new AudioJsonBO();
        twelveAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundThreeAudio().getFourthGuidanceAudioUrl()));
        twelveAudio.setId(PoseWorkoutConstant.ROUND_3_4);
        twelveAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundThreeAudio().getFourthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.TWELVE, twelveAudio);

        AudioJsonBO thirteenAudio = new AudioJsonBO();
        thirteenAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundThreeAudio().getFifthGuidanceAudioUrl()));
        thirteenAudio.setId(PoseWorkoutConstant.ROUND_3_5);
        thirteenAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundThreeAudio().getFifthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.THIRTEEN, thirteenAudio);

        AudioJsonBO fourteenAudio = new AudioJsonBO();
        fourteenAudio.setUrl(fileService.getAbsoluteR2Url(generateBO.getRoundThreeAudio().getSixthGuidanceAudioUrl()));
        fourteenAudio.setId(PoseWorkoutConstant.ROUND_3_6);
        fourteenAudio.setName(FireBaseUrlSubUtils.getFileName(generateBO.getRoundThreeAudio().getSixthGuidanceAudioUrl()));
        videoIndexAndAudioMap.put(GlobalConstant.FOURTEEN, fourteenAudio);

        generateBO.setVideoIndexAndAudioMap(videoIndexAndAudioMap);

    }


    private void validatePoseVideo(ProjYogaPoseVideo poseVideo) {

        if (Objects.isNull(poseVideo)) {
            throw new BizException("Pose video can't be null.");
        }

        if (StringUtils.isEmpty(poseVideo.getFrontVideoUrl()) || Objects.isNull(poseVideo.getFrontVideoDuration())) {
            throw new BizException("Pose front video can't be null.");
        }

        if (StringUtils.isEmpty(poseVideo.getSideVideoUrl()) || Objects.isNull(poseVideo.getSideVideoDuration())) {
            throw new BizException("Pose side video can't be null.");
        }

        if (Objects.equals(PoseTypeEnum.Begin.getPoseType(), poseVideo.getPoseType()) ||
                Objects.equals(PoseDirectionEnum.RIGHT.getPoseDirection(), poseVideo.getPoseDirection())) {
            throw new BizException("For begin pose or right pose, can't generate pose workout.");
        }

        if (Objects.equals(PoseDirectionEnum.LEFT.getPoseDirection(), poseVideo.getPoseDirection())
                && Objects.isNull(poseVideo.getRightVideoId())) {
            throw new BizException("For left pose, right video id can't be null.");
        }
    }
}