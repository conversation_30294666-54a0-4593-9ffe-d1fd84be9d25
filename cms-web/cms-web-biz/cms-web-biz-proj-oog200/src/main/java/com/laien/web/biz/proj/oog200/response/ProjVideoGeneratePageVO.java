package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * note: Video Generate 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Video Generate 分页", description = "Video Generate 分页")
public class ProjVideoGeneratePageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "template id")
    private Integer templateId;

    @ApiModelProperty(value = "task id")
    private Integer taskId;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "真实时长")
    private Integer realDuration;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "数据版本")
    private Integer dataVersion;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "video list")
    private List<ProjVideoGenerateVideoSliceVO> videoList;

}
