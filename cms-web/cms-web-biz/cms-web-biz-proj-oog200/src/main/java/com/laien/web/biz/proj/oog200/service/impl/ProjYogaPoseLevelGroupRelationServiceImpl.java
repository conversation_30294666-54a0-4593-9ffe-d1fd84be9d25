package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseLevelGroupRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseLevelGroupRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseLevelGroupRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.stereotype.Service;

/**
 * <p>
 * proj yoga pose grouping workout relation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
public class ProjYogaPoseLevelGroupRelationServiceImpl
        extends ServiceImpl<ProjYogaPoseLevelGroupRelationMapper, ProjYogaPoseLevelGroupRelation>
        implements IProjYogaPoseLevelGroupRelationService {

    @Override
    public void deleteByProjYogaPoseLevelId(Integer projYogaPoseLevelId) {
        LambdaUpdateWrapper<ProjYogaPoseLevelGroupRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjYogaPoseLevelGroupRelation::getProjYogaPoseLevelId, projYogaPoseLevelId)
                        .set(BaseModel::getDelFlag, GlobalConstant.YES);
        baseMapper.update(new ProjYogaPoseLevelGroupRelation(), wrapper);
    }

}


