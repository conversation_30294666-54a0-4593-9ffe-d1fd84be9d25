package com.laien.web.biz.proj.oog200.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseWorkout;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutListVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj yoga pose workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface ProjYogaPoseWorkoutMapper extends BaseMapper<ProjYogaPoseWorkout> {

    List<CountBO> findCount(@Param("status") Integer status, @Param("poseGroupIdSet") Set<Integer> poseGroupIdSet);

    List<ProjYogaPoseWorkoutListVO> findByPoseGroupId(@Param("poseGroupId") Integer poseGroupId);

    @Select(value = "select * from proj_yoga_pose_workout where name = #{name} and del_flag != 1 for update")
    ProjYogaPoseWorkout getByName(String name);
}
