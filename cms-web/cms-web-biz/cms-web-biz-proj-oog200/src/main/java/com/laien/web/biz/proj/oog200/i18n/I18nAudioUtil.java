package com.laien.web.biz.proj.oog200.i18n;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog200.bo.YogaVideoI18nBO;
import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: hhl
 * @date: 2025/7/2
 */
@Component
public class I18nAudioUtil {

    @Resource
    ICoreSpeechTaskI18nPubService speechTaskI18nPubService;

    @Resource
    FileService fileService;

    /**
     * Map<videoId, Map<language, YogaVideoI18nBO>>
     *
     * @return
     */
    public Map<Integer, Map<String, YogaVideoI18nBO>> checkAndConvert2I18nMap(
            List<BaseVideoI18n> videoI18nList, Collection<String> languages,
            boolean nameCheck, boolean guidanceCheck) {

        if (CollectionUtils.isEmpty(videoI18nList) || CollectionUtils.isEmpty(languages)) {
            return Collections.emptyMap();
        }

        Set<LanguageEnums> excludeEnLanguages = languages.stream().filter(language -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language))
                .map(language -> LanguageEnums.getByNameIgnoreCase(language)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(excludeEnLanguages)) {
            return Collections.emptyMap();
        }

        speechTaskI18nPubService.translate(videoI18nList, ProjCodeEnums.OOG200, excludeEnLanguages);
        Map<Integer, Map<String, YogaVideoI18nBO>> i18nMap = new HashMap<>();
        videoI18nList.forEach(videoI18n -> {
            Map<String, YogaVideoI18nBO> yogaVideoMap = new HashMap<>();
            i18nMap.put(videoI18n.getId(), yogaVideoMap);

            for (LanguageEnums language : excludeEnLanguages) {
                YogaVideoI18nBO yogaVideoI18nBO = new YogaVideoI18nBO();
                yogaVideoI18nBO.setId(videoI18n.getId());

                yogaVideoMap.put(language.getName(), yogaVideoI18nBO);
                if (nameCheck) {
                    Optional<AudioTranslateResultModel> resultModel = videoI18n.getResult().stream().filter(result -> Objects.equals(result.getLanguage(), language)).findAny();
                    if (!resultModel.isPresent()) {
                        throw new BizException("Video audio translate incomplete, id is " + videoI18n.getId());
                    }
                    yogaVideoI18nBO.setNameScript(resultModel.get().getText());
                    yogaVideoI18nBO.setNameScriptFemale(resultModel.get().getAudioUrl());
                    yogaVideoI18nBO.setNameScriptFemaleDuration(resultModel.get().getDuration());
                }

                if (guidanceCheck) {
                    Optional<AudioTranslateResultModel> resultModel = videoI18n.getGuidanceResult().stream().filter(result -> Objects.equals(result.getLanguage(), language)).findAny();
                    if (!resultModel.isPresent()) {
                        throw new BizException("Video audio translate incomplete, id is " + videoI18n.getId());
                    }
                    yogaVideoI18nBO.setGuidanceScript(resultModel.get().getText());
                    yogaVideoI18nBO.setGuidanceScriptFemale(resultModel.get().getAudioUrl());
                    yogaVideoI18nBO.setGuidanceScriptFemaleDuration(resultModel.get().getDuration());
                }
            }
        });

        return i18nMap;
    }

    /**
     * Map<soundName, Map<language, AudioJsonBO>>
     *
     */
    public Map<String, Map<String, AudioJsonBO>> convertSound2I18nMap(List<BaseSoundI18n> soundlist, Collection<String> languages) {

        if (CollectionUtils.isEmpty(soundlist) || CollectionUtils.isEmpty(languages)) {
            return Collections.emptyMap();
        }

        Set<LanguageEnums> excludeEnLanguages = languages.stream().filter(language -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language))
                .map(language -> LanguageEnums.getByNameIgnoreCase(language)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(excludeEnLanguages)) {
            return Collections.emptyMap();
        }

        speechTaskI18nPubService.translate(soundlist, ProjCodeEnums.OOG200, excludeEnLanguages);
        Map<String, Map<String, AudioJsonBO>> i18nMap = new HashMap<>();
        soundlist.forEach(sound -> {

            Map<String, AudioJsonBO> audioJsonBOMap = new HashMap<>();
            i18nMap.put(sound.getName(), audioJsonBOMap);
            for (String language : languages) {

                AudioJsonBO audioJsonBO = new AudioJsonBO();
                audioJsonBOMap.put(language, audioJsonBO);
                if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                    String url = sound.getProjSound().getUrl();
                    Integer duration = sound.getProjSound().getDuration();
                    convert2AudioJsonBO(url, duration, audioJsonBO);
                    continue;
                }

                if (sound.getNeedTranslation()) {
                    Optional<AudioTranslateResultModel> resultModel = sound.getResult().stream()
                            .filter(result -> Objects.equals(result.getLanguage().getName(), language))
                            .filter(result -> Objects.equals(result.getGender().getCode(), sound.getGender().getCode()))
                            .findAny();
                    if (!resultModel.isPresent()) {
                        throw new BizException("200 audio translate incomplete, name is " + sound.getName());
                    }
                    String url = resultModel.get().getAudioUrl();
                    Integer duration = resultModel.get().getDuration();
                    convert2AudioJsonBO(url, duration, audioJsonBO);
                } else {
                    String url = sound.getProjSound().getUrl();
                    Integer duration = sound.getProjSound().getDuration();
                    convert2AudioJsonBO(url, duration, audioJsonBO);
                }
            }
        });

        return i18nMap;
    }

    private void convert2AudioJsonBO(String url, Integer duration, AudioJsonBO audioJsonBO) {

        audioJsonBO.setUrl(fileService.getAbsoluteR2Url(url));
        audioJsonBO.setTime(new BigDecimal(duration));
        audioJsonBO.setName(FireBaseUrlSubUtils.getFileName(url));
        audioJsonBO.setId(FireBaseUrlSubUtils.getFileName(url));
    }

}
