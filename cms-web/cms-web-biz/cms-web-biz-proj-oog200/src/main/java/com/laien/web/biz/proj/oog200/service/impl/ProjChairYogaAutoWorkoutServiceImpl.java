package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.laien.common.oog200.enums.*;
import com.laien.web.biz.proj.oog200.bo.*;
import com.laien.web.biz.proj.oog200.config.Oog200BizConfig;
import com.laien.web.biz.proj.oog200.entity.*;
import com.laien.web.biz.proj.oog200.handler.ProjChairYogaWorkoutFileHandler;
import com.laien.web.biz.proj.oog200.mapper.ProjChairYogaAutoWorkoutMapper;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaAutoWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaAutoWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaAutoWorkoutPageVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.service.*;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.async.service.IAsyncService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum.CHAIR_YOGA;

/**
 * Author:  hhl
 * Date:  2024/9/25 10:47
 */
@Slf4j
@Service
public class ProjChairYogaAutoWorkoutServiceImpl extends ServiceImpl<ProjChairYogaAutoWorkoutMapper, ProjChairYogaAutoWorkout> implements IProjChairYogaAutoWorkoutService {

    @Resource
    private IProjYogaAutoWorkoutTaskService workoutTaskService;

    @Resource
    private IProjYogaAutoWorkoutTemplateService workoutTemplateService;

    @Resource
    private IProjChairYogaVideoService chairYogaVideoService;

    @Resource
    private IProjChairYogaVideoSliceService chairYogaVideoSliceService;

    @Resource
    private Oog200BizConfig oog200BizConfig;

    @Resource
    private IProjYogaAutoWorkoutTaskService autoWorkoutTaskService;

    @Resource
    private IProjChairYogaAutoWorkoutVideoRelationService workoutRelationService;

    @Resource
    private ProjChairYogaAutoWorkoutMapper chairYogaAutoWorkoutMapper;

    @Resource
    private IProjAutoWorkoutBasicInfoWorkoutRelationService basicInfoWorkoutRelationService;

    @Resource
    private ProjChairYogaWorkoutFileHandler projChairYogaWorkoutFileHandler;

    @Resource
    private IAsyncService asyncService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private IProjYogaAutoWorkoutAudioI18nService projYogaAutoWorkoutAudioI18nService;

    private static final String AUDIO_DIR_KEY = "project-chair-yoga-workout-json";
    private static final String M3U8_DIR_KEY = "project-chair-yoga-workout-m3u8";

    @Override
    public void generateWorkout(ProjYogaAutoWorkoutTask workoutTask) {

        try {
            ChairYogaWorkoutGenerateBO generateBO = createGenerateBO(workoutTask);
            Map<String, List<List<Integer>>> targetChairYogaVideoMap = combineChairYogaVideo(generateBO);
            List<ProjChairYogaAutoWorkoutBO> workoutBOList = generateWorkoutBO(targetChairYogaVideoMap);
            workoutBOList = projChairYogaWorkoutFileHandler.generateChairYogaWorkoutFile(workoutBOList, generateBO.getProjId(), generateBO.getVideoBO(), generateBO.getVideoMap(), generateBO.getVideoSliceMap());
            applicationContext.getBean(ProjChairYogaAutoWorkoutServiceImpl.class)
                    .saveAutoWorkoutAndRelation(generateBO, workoutBOList);
            updateTaskStatus(workoutTask, ProjYogaAutoWorkoutTaskStatusEnum.SUCCESS);
            cleanUp(workoutTask);
        } catch (Throwable exception) {

            workoutTask.setFailReason(exception.getMessage());
            updateTaskStatus(workoutTask, ProjYogaAutoWorkoutTaskStatusEnum.FAIL);
            log.warn(exception.getMessage(), exception);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void saveAutoWorkoutAndRelation(ChairYogaWorkoutGenerateBO generateBO, List<ProjChairYogaAutoWorkoutBO> workoutBOList) {

        List<ProjChairYogaAutoWorkout> workoutList = workoutBOList.stream().peek(bo -> {
            ProjChairYogaAutoWorkout workout = new ProjChairYogaAutoWorkout();
            workout.setTarget(bo.getTarget());
            workout.setProjId(generateBO.getProjId());
            workout.setCreateUser(generateBO.getOperationUserName());
            workout.setCreateTime(LocalDateTime.now());
            workout.setProjYogaAutoWorkoutTemplateId(generateBO.getTemplateId());
            workout.setProjYogaAutoWorkoutTaskId(generateBO.getTemplateTaskId());

            workout.setDuration(bo.getDuration());
            workout.setCalorie(bo.getCalorie());
            workout.setVideoM3u8Url(bo.getVideoM3u8Url());
            workout.setVideo2532Url(bo.getVideo2532Url());
            workout.setAudioLongJson(bo.getAudioLongJson());
            workout.setAudioShortJson(bo.getAudioShortJson());
            bo.setEntity(workout);
        }).map(ProjChairYogaAutoWorkoutBO::getEntity).collect(Collectors.toList());
        // 先批量保存workout
        saveBatch(workoutList);
        // 再保存与视频的关联关系
        List<ProjChairYogaAutoWorkoutVideoRelation> workoutVideoRelations = workoutBOList.stream()
                .flatMap(workoutBO -> getWorkoutVideoRelations(workoutBO).stream()).collect(Collectors.toList());
        workoutRelationService.saveBatch(workoutVideoRelations);
        // 最后保存多语言音频数据
        List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = workoutBOList.stream().flatMap(workoutBO -> getAudioI18ns(workoutBO).stream()).collect(Collectors.toList());
        projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);
    }

    private static List<ProjChairYogaAutoWorkoutVideoRelation> getWorkoutVideoRelations(ProjChairYogaAutoWorkoutBO workoutBO) {
        return workoutBO.getVideoIdList().stream().map(videoId -> {
            ProjChairYogaAutoWorkoutVideoRelation relation = new ProjChairYogaAutoWorkoutVideoRelation();
            relation.setProjChairYogaAutoWorkoutId(workoutBO.getEntity().getId());
            relation.setProjChairYogaVideoId(videoId);
            relation.setVideoDuration(workoutBO.getVideoRealDuration().get(videoId).get());
            return relation;
        }).collect(Collectors.toList());
    }

    private List<ProjChairYogaAutoWorkoutBO> generateWorkoutBO(Map<String, List<List<Integer>>> targetChairYogaVideoMap) {

        return targetChairYogaVideoMap.entrySet().stream().flatMap(combineEntry ->
                combineEntry.getValue().stream().map(videoIdList -> {
                    ProjChairYogaAutoWorkoutBO workout = new ProjChairYogaAutoWorkoutBO();
                    workout.setVideoIdList(videoIdList);
                    workout.setTarget(combineEntry.getKey());
                    return workout;
                })
        ).collect(Collectors.toList());
    }


    private void cleanUp(ProjYogaAutoWorkoutTask workoutTask) {

        if (!Objects.equals(workoutTask.getCleanUp(), GlobalConstant.YES)) {
            return;
        }

        LambdaQueryWrapper<ProjChairYogaAutoWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BaseModel::getId).eq(ProjChairYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, workoutTask.getProjYogaAutoWorkoutTemplateId())
                .ne(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE).ne(ProjChairYogaAutoWorkout::getProjYogaAutoWorkoutTaskId, workoutTask.getId());
        List<ProjChairYogaAutoWorkout> workoutList = list(wrapper);
        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        Set<Integer> workoutIdSet = workoutList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        LambdaUpdateWrapper<ProjChairYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BaseModel::getId, workoutIdSet);
        remove(wrapper);
        workoutRelationService.deleteByChairYogaAutoWorkoutIds(workoutIdSet);
    }

    private void updateTaskStatus(ProjYogaAutoWorkoutTask workoutTask, ProjYogaAutoWorkoutTaskStatusEnum statusEnum) {

        ProjYogaAutoWorkoutTask updateTask = new ProjYogaAutoWorkoutTask();
        updateTask.setStatus(statusEnum);
        updateTask.setFailReason(workoutTask.getFailReason());
        updateTask.setId(workoutTask.getId());
        autoWorkoutTaskService.updateById(updateTask);
    }

    private Integer computeRealDuration4Video(ProjChairYogaVideo yogaVideo, ChairYogaWorkoutVideoBO videoBO, Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap, AtomicBoolean front) {

        ProjChairYogaVideoSliceDetailVO easyStart = Objects.equals(yogaVideo.getPosition(), PositionEnum.STANDING.getName()) ? videoBO.getEasyStart4Standing() : videoBO.getEasyStart4Seated();
        Integer easyStartDuration = selectDuration4EasyStart(easyStart, front);
        Integer videoDuration = selectDuration4Video(videoSliceMap.get(yogaVideo.getId()), front);
        return easyStartDuration + videoDuration;
    }


    private Integer selectDuration4Video(List<ProjChairYogaVideoSliceDetailVO> videoSliceList, AtomicBoolean front4Duration) {

        Integer duration = videoSliceList.stream().sorted(Comparator.comparing(ProjChairYogaVideoSliceDetailVO::getSliceIndex)).mapToInt(video -> {
            if (front4Duration.get()) {
                front4Duration.set(false);
                return video.getFrontVideoDuration();
            } else {
                front4Duration.set(true);
                return video.getSideVideoDuration();
            }
        }).sum();
        return duration;
    }

    private Map<String, List<List<Integer>>> combineChairYogaVideo(ChairYogaWorkoutGenerateBO generateBO) {

        List<List<Integer>> upperBody = assembleChairYogaVideo(TargetEnum.UPPER_BODY.getName(), generateBO);
        List<List<Integer>> absCore = assembleChairYogaVideo(TargetEnum.ABS_CORE.getName(), generateBO);
        List<List<Integer>> lowerBody = assembleChairYogaVideo(TargetEnum.LOWER_BODY.getName(), generateBO);

        Map<String, List<List<Integer>>> chairYogaCombineMap = Maps.newHashMap();
        chairYogaCombineMap.put(TargetEnum.UPPER_BODY.getName(), upperBody);
        chairYogaCombineMap.put(TargetEnum.ABS_CORE.getName(), absCore);
        chairYogaCombineMap.put(TargetEnum.LOWER_BODY.getName(), lowerBody);
        return chairYogaCombineMap;
    }

    private List<List<Integer>> assembleChairYogaVideo(String target, ChairYogaWorkoutGenerateBO generateBO) {

        Map<String, List<ProjChairYogaVideo>> centralAndLeftVideoMap = generateBO.getCentralAndLeftVideoMap();
        List<ProjChairYogaVideo> coolDownList = centralAndLeftVideoMap.get(TypeEnum.COOL_DOWN.getValue());
        List<ProjChairYogaVideo> warmupList = centralAndLeftVideoMap.get(TypeEnum.WARM_UP.getValue());

        List<List<Integer>> assembleVideoList = new ArrayList<>();
        for (int i = 0; i < generateBO.getGenerateDays(); i++) {

            List<Integer> warmUp = randomSelectWithCount(TypeEnum.WARM_UP.getValue(), generateBO.getWarmUpCount(), warmupList);
            List<Integer> coolDown = randomSelectWithCount(TypeEnum.COOL_DOWN.getValue(), generateBO.getCoolDownCount(), coolDownList);
            List<Integer> main = randomSelectWithDuration(TypeEnum.MAIN.getValue(), target, warmUp, coolDown, generateBO);

            warmUp.addAll(main);
            warmUp.addAll(coolDown);
            assembleVideoList.add(warmUp);
        }
        return assembleVideoList;
    }

    private List<Integer> randomSelectWithDuration(String videoType, String target, List<Integer> warmUpList, List<Integer> coolDownList,
                                                   ChairYogaWorkoutGenerateBO generateBO) {

        Map<Integer, Integer> videoDurationMap = generateBO.getFrontVideoDurationMap();
        Integer easyStartDuration = getEasyStartDuration(generateBO.getVideoBO());
        Integer warmUpDuration = warmUpList.stream().mapToInt(videoId -> videoDurationMap.get(videoId) + easyStartDuration).sum();
        Integer coolDownDuration = coolDownList.stream().mapToInt(videoId -> videoDurationMap.get(videoId) + easyStartDuration).sum();
        int mainMinDuration = generateBO.getMinMillSecond() - warmUpDuration - coolDownDuration;
        int mainMaxDuration = generateBO.getMaxMillSecond() - warmUpDuration - coolDownDuration;

        // random duration for current workout
        int randomDuration4Main = randomDuration4Main(mainMinDuration, mainMaxDuration);
        int halfOfDuration = randomDuration4Main / GlobalConstant.TWO;
        AtomicInteger combineDuration4Main = new AtomicInteger(GlobalConstant.ZERO);

        // 一个workout可以包含seated和stand，保持先seated，再stand的顺序，不能交替出现。
        // 但如果seated足够的话，可以全由seated组成。
        List<Integer> combineSeatedVideoIdList = Lists.newArrayList();
        List<Integer> combineStandingVideoIdList = Lists.newArrayList();

        // 根据Target，可以将Video划分为三类，等于、包含、不包含。
        // 一个Target为Upper Body的Workout按时长划分，其Video组成为：50%（等于、包含，等于类型的Video足够时，不用包含类型的） + 50%组成（不包含）。
        List<ProjChairYogaVideo> fullTargetVideoList = generateBO.getFullTargetVideoMap().get(target);
        List<ProjChairYogaVideo> partTargetVideoList = generateBO.getPartTargetVideoMap().get(target);
        List<ProjChairYogaVideo> withoutTargetVideoList = generateBO.getWithoutTargetVideoMap().get(target);

        // for equals and include
        boolean fullMatch = combineVideoWithTargetAndPosition(fullTargetVideoList, combineSeatedVideoIdList, videoDurationMap, PositionEnum.SEATED.getName(), combineDuration4Main, mainMaxDuration, halfOfDuration, easyStartDuration);
        if (!fullMatch) {
            fullMatch = combineVideoWithTargetAndPosition(partTargetVideoList, combineSeatedVideoIdList, videoDurationMap, PositionEnum.SEATED.getName(), combineDuration4Main, mainMaxDuration, halfOfDuration, easyStartDuration);
        }

        if (!fullMatch) {
            fullMatch = combineVideoWithTargetAndPosition(fullTargetVideoList, combineStandingVideoIdList, videoDurationMap, PositionEnum.STANDING.getName(), combineDuration4Main, mainMaxDuration, halfOfDuration, easyStartDuration);
        }
        if (!fullMatch) {
            combineVideoWithTargetAndPosition(partTargetVideoList, combineStandingVideoIdList, videoDurationMap, PositionEnum.STANDING.getName(), combineDuration4Main, mainMaxDuration, halfOfDuration, easyStartDuration);
        }

        // for unInclude
        fullMatch = combineVideoWithTargetAndPosition(withoutTargetVideoList, combineSeatedVideoIdList, videoDurationMap, PositionEnum.SEATED.getName(), combineDuration4Main, mainMaxDuration, randomDuration4Main, easyStartDuration);
        if (!fullMatch) {
            combineVideoWithTargetAndPosition(withoutTargetVideoList, combineStandingVideoIdList, videoDurationMap, PositionEnum.STANDING.getName(), combineDuration4Main, mainMaxDuration, randomDuration4Main, easyStartDuration);
        }

        if (combineDuration4Main.get() < mainMinDuration) {
            log.warn("Video duration less than template min duration, video type : {}, min duration : {}, template id : {}.", videoType, mainMinDuration, generateBO.getTemplateId());
            throw new BizException(String.format("Video duration less than template min duration, video type : %s, min duration : %d, template id : %d.", videoType, mainMinDuration, generateBO.getTemplateId()));
        }

        combineSeatedVideoIdList.addAll(combineStandingVideoIdList);
        return combineSeatedVideoIdList;
    }

    private Integer getEasyStartDuration(ChairYogaWorkoutVideoBO videoBO) {

        if (Objects.isNull(videoBO) || Objects.isNull(videoBO.getEasyStart4Standing()) || Objects.isNull(videoBO.getEasyStart4Seated())) {
            return Integer.valueOf(GlobalConstant.ZERO);
        }

        return videoBO.getEasyStart4Seated().getFrontVideoDuration();
    }

    private boolean combineVideoWithTargetAndPosition(List<ProjChairYogaVideo> videoList, List<Integer> combineVideoIdList, Map<Integer, Integer> videoDurationMap,
                                                      String position, AtomicInteger combineDuration4Main, int maxDuration, int randomDuration, Integer easyStartDuration) {

        if (combineDuration4Main.get() > randomDuration) {
            return true;
        }

        if (CollectionUtils.isEmpty(videoList)) {
            return false;
        }

        List<ProjChairYogaVideo> positionVideoList = videoList.stream().filter(video -> Objects.equals(position, video.getPosition())).collect(Collectors.toList());
        Collections.shuffle(positionVideoList);
        for (ProjChairYogaVideo chairYogaVideo : positionVideoList) {

            if (Objects.equals(chairYogaVideo.getDirection(), PoseDirectionEnum.LEFT.getPoseDirection())) {
                int combineDuration = combineDuration4Main.get() + videoDurationMap.get(chairYogaVideo.getId()) + easyStartDuration + videoDurationMap.get(chairYogaVideo.getRightVideoId()) + easyStartDuration;
                if (combineDuration > maxDuration) {
                    continue;
                }

                combineDuration4Main.set(combineDuration);
                combineVideoIdList.add(chairYogaVideo.getId());
                combineVideoIdList.add(chairYogaVideo.getRightVideoId());
            }

            if (Objects.equals(chairYogaVideo.getDirection(), PoseDirectionEnum.CENTRAL.getPoseDirection())) {
                int combineDuration = combineDuration4Main.get() + videoDurationMap.get(chairYogaVideo.getId()) + easyStartDuration;
                if (combineDuration > maxDuration) {
                    continue;
                }

                combineDuration4Main.set(combineDuration);
                combineVideoIdList.add(chairYogaVideo.getId());
            }

            // 组合的视频时长已经满足需求，跳出循环返回结果
            if (combineDuration4Main.get() >= randomDuration) {
                return true;
            }
        }
        return false;
    }

    private int randomDuration4Main(int minDuration, int maxDuration) {

        List<Float> floats = Lists.newArrayList(0.1f, 0.3f, 0.5f, 0.7f);
        Collections.shuffle(floats);
        int bufferDuration = maxDuration - minDuration;
        return minDuration + (int) (bufferDuration * floats.get(GlobalConstant.ZERO));
    }

    private List<Integer> randomSelectWithCount(String videoType, Integer needCount, List<ProjChairYogaVideo> videoList) {

        if (CollectionUtils.isEmpty(videoList)) {
            log.warn("Video quantity less than template count, video type : {}, template count : {}.", videoType, needCount);
        }

        Collections.shuffle(videoList);
        List<Integer> videoIdList = Lists.newArrayList();

        for (ProjChairYogaVideo chairYogaVideo : videoList) {

            if (videoIdList.size() == needCount) {
                break;
            }

            if (Objects.equals(chairYogaVideo.getDirection(), PoseDirectionEnum.CENTRAL.getPoseDirection())) {
                videoIdList.add(chairYogaVideo.getId());
            }

            if (Objects.equals(chairYogaVideo.getPosition(), PoseDirectionEnum.LEFT.getPoseDirection())) {
                if (needCount - videoIdList.size() == GlobalConstant.ONE) {
                    continue;
                }
                videoIdList.add(chairYogaVideo.getId());
                videoIdList.add(chairYogaVideo.getRightVideoId());
            }
        }

        if (videoIdList.size() < needCount) {
            log.warn("Video quantity less than template count, video type : {}, template count : {}.", videoType, needCount);
            throw new BizException(String.format("Video quantity less than template count, video type : %s, template count : %d.", videoType, needCount));
        }
        return videoIdList;
    }

    private ChairYogaWorkoutGenerateBO createGenerateBO(ProjYogaAutoWorkoutTask workoutTask) {

        ChairYogaWorkoutGenerateBO generateBO = new ChairYogaWorkoutGenerateBO();
        generateBO.setOperationUserName(workoutTask.getCreateUser());
        generateBO.setTemplateTaskId(workoutTask.getId());
        generateBO.setGenerateDays(workoutTask.getGenerateNum());

        ChairYogaWorkoutAudioBO audioBO = getChairYogaWorkoutAudioBO();
        generateBO.setAudioBO(audioBO);

        ChairYogaWorkoutVideoBO videoBO = getChairYogaWorkoutVideoBO();
        generateBO.setVideoBO(videoBO);

        generateBoWithTemplate(generateBO, workoutTask.getProjYogaAutoWorkoutTemplateId());
        generateBoWithChairVideo(generateBO);
        return generateBO;
    }

    private ChairYogaWorkoutVideoBO getChairYogaWorkoutVideoBO() {

        ChairYogaWorkoutVideoBO videoBO = new ChairYogaWorkoutVideoBO();
        Oog200VideoBO oog200Video = oog200BizConfig.getOog200Video();

        List<String> videoNameList = Lists.newArrayList(oog200Video.getChairYogaVideo4Seated(), oog200Video.getChairYogaVideo4Standing());
        List<ProjChairYogaVideo> yogaVideoList = chairYogaVideoService.listByName(videoNameList);
        if (CollectionUtils.isEmpty(yogaVideoList)) {
            throw new BizException("Easy start video config error, please check it first.");
        }

        Map<String, Integer> videoNameAndIdMap = yogaVideoList.stream().collect(Collectors.toMap(ProjChairYogaVideo::getName, ProjChairYogaVideo::getId, (k1, k2) -> k1));
        List<ProjChairYogaVideoSliceDetailVO> sliceDetailVOList = chairYogaVideoSliceService.listByChairYogaVideoId(videoNameAndIdMap.values());
        if (CollectionUtils.isEmpty(sliceDetailVOList)) {
            throw new BizException("Easy start video config error, please check it first.");
        }

        Integer video4Seated = videoNameAndIdMap.get(oog200Video.getChairYogaVideo4Seated());
        if (Objects.nonNull(video4Seated)) {
            Optional<ProjChairYogaVideoSliceDetailVO> sliceDetailVO = sliceDetailVOList.stream().filter(slice -> Objects.equals(slice.getProjChairYogaVideoId(), video4Seated)).findAny();
            if (sliceDetailVO.isPresent()) {
                videoBO.setEasyStart4Seated(sliceDetailVO.get());
            }
        }

        Integer video4Standing = videoNameAndIdMap.get(oog200Video.getChairYogaVideo4Standing());
        if (Objects.nonNull(video4Standing)) {
            Optional<ProjChairYogaVideoSliceDetailVO> sliceDetailVO = sliceDetailVOList.stream().filter(slice -> Objects.equals(slice.getProjChairYogaVideoId(), video4Standing)).findAny();
            if (sliceDetailVO.isPresent()) {
                videoBO.setEasyStart4Standing(sliceDetailVO.get());
            }
        }

        if (Objects.isNull(videoBO.getEasyStart4Seated()) || Objects.isNull(videoBO.getEasyStart4Standing())) {
            throw new BizException("Easy start video config error, please check it first.");
        }
        return videoBO;
    }

    private ChairYogaWorkoutAudioBO getChairYogaWorkoutAudioBO() {

        ChairYogaWorkoutAudioBO audioBO = new ChairYogaWorkoutAudioBO();
        Video200SysSoundBO soundBO = oog200BizConfig.getOog200();

        audioBO.setFirstAudio(soundBO.getAudio(soundBO.getChairYogaFirst()));
        audioBO.setNextAudio(soundBO.getAudio(soundBO.getChairYogaNext()));
        audioBO.setLastAudio(soundBO.getAudio(soundBO.getChairYogaLast()));
        return audioBO;
    }

    private void generateBoWithChairVideo(ChairYogaWorkoutGenerateBO generateBO) {

        List<ProjChairYogaVideo> chairYogaVideoList = listValidChairYogaVideo();
        if (CollectionUtils.isEmpty(chairYogaVideoList)) {
            log.warn("Can't find chair yoga video to generate workout, template id : {}.", generateBO.getTemplateId());
            throw new BizException(String.format("Can't find chair yoga video to generate workout."));
        }

        // chair yoga
        Map<Integer, ProjChairYogaVideo> videoMap = chairYogaVideoList.stream().collect(
                Collectors.toMap(ProjChairYogaVideo::getId, Function.identity()));
        generateBO.setVideoMap(videoMap);

        // chair yoga slice
        Set<Integer> videoIdSet = videoMap.keySet();
        List<ProjChairYogaVideoSliceDetailVO> sliceDetailVOList = chairYogaVideoSliceService.listByChairYogaVideoId(videoIdSet);
        generateBO.setVideoSliceList(sliceDetailVOList);

        Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId));
        generateBO.setVideoSliceMap(videoSliceMap);

        // chair yoga duration
        Map<Integer, Integer> frontVideoDurationMap = Maps.newHashMap();
        Map<Integer, Integer> sideVideoDurationMap = Maps.newHashMap();
        chairYogaVideoSliceService.videoDurationCount(videoIdSet, frontVideoDurationMap, sideVideoDurationMap);
        generateBO.setFrontVideoDurationMap(frontVideoDurationMap);
        generateBO.setSideVideoDurationMap(sideVideoDurationMap);

        // filter video config
        List<Integer> configVideoIdList = parseConfigVideoId(generateBO.getVideoBO());

        // left and central chair yoga
        List<ProjChairYogaVideo> centralAndLeftVideos = chairYogaVideoList.stream().filter(video -> !configVideoIdList.contains(video.getId()))
                .filter(video -> Objects.equals(video.getDirection(), PoseDirectionEnum.LEFT.getPoseDirection()) || Objects.equals(video.getDirection(), PoseDirectionEnum.CENTRAL.getPoseDirection())).collect(Collectors.toList());
        Map<String, List<ProjChairYogaVideo>> centralAndLeftVideoMap = centralAndLeftVideos.stream().collect(Collectors.groupingBy(ProjChairYogaVideo::getType));
        generateBO.setCentralAndLeftVideoMap(centralAndLeftVideoMap);

        // full & part target
        Map<String, List<ProjChairYogaVideo>> fullTargetVideoMap = Maps.newHashMap();
        Map<String, List<ProjChairYogaVideo>> partTargetVideoMap = Maps.newHashMap();
        Map<String, List<ProjChairYogaVideo>> withoutTargetVideoMap = Maps.newHashMap();

        setTargetVideoMap(centralAndLeftVideos, fullTargetVideoMap, partTargetVideoMap, withoutTargetVideoMap);
        generateBO.setFullTargetVideoMap(fullTargetVideoMap);
        generateBO.setPartTargetVideoMap(partTargetVideoMap);
        generateBO.setWithoutTargetVideoMap(withoutTargetVideoMap);
    }

    private List<Integer> parseConfigVideoId(ChairYogaWorkoutVideoBO videoBO) {

        if (Objects.isNull(videoBO) || Objects.isNull(videoBO.getEasyStart4Seated()) || Objects.isNull(videoBO.getEasyStart4Standing())) {
            return Collections.emptyList();
        }

        List<Integer> chairYogaVideoIdList = Lists.newArrayList();
        chairYogaVideoIdList.add(videoBO.getEasyStart4Seated().getProjChairYogaVideoId());
        chairYogaVideoIdList.add(videoBO.getEasyStart4Standing().getProjChairYogaVideoId());
        return chairYogaVideoIdList;
    }

    private void setTargetVideoMap(List<ProjChairYogaVideo> chairYogaVideoList, Map<String, List<ProjChairYogaVideo>> fullTargetVideoMap,
                                   Map<String, List<ProjChairYogaVideo>> partTargetVideoMap, Map<String, List<ProjChairYogaVideo>> withoutTargetVideoMap) {

        Set<String> allTargets = Arrays.stream(TargetEnum.values()).map(TargetEnum::getName).collect(Collectors.toSet());
        for (ProjChairYogaVideo chairYogaVideo : chairYogaVideoList) {

            if (!Objects.equals(chairYogaVideo.getType(), TypeEnum.MAIN.getValue())) {
                continue;
            }

            List<String> targets = Arrays.stream(chairYogaVideo.getTarget().split(GlobalConstant.COMMA)).collect(Collectors.toList());
            List<String> withoutTargets = allTargets.stream().filter(target -> !targets.contains(target)).collect(Collectors.toList());
            if (Objects.equals(targets.size(), GlobalConstant.ONE)) {

                if (fullTargetVideoMap.containsKey(chairYogaVideo.getTarget())) {
                    fullTargetVideoMap.get(chairYogaVideo.getTarget()).add(chairYogaVideo);
                } else {
                    List<ProjChairYogaVideo> chairYogaVideos = Lists.newArrayList();
                    chairYogaVideos.add(chairYogaVideo);
                    fullTargetVideoMap.put(chairYogaVideo.getTarget(), chairYogaVideos);
                }

                setTargetVideoMap(withoutTargets, withoutTargetVideoMap, chairYogaVideo);
            } else {
                setTargetVideoMap(withoutTargets, withoutTargetVideoMap, chairYogaVideo);
                setTargetVideoMap(targets, partTargetVideoMap, chairYogaVideo);
            }
        }
    }

    private void setTargetVideoMap(List<String> withoutTargets, Map<String, List<ProjChairYogaVideo>> targetMap, ProjChairYogaVideo chairYogaVideo) {

        for (String target : withoutTargets) {
            if (targetMap.containsKey(target)) {
                targetMap.get(target).add(chairYogaVideo);
            } else {
                List<ProjChairYogaVideo> chairYogaVideos = Lists.newArrayList();
                chairYogaVideos.add(chairYogaVideo);
                targetMap.put(target, chairYogaVideos);
            }
        }
    }

    private List<ProjChairYogaVideo> listValidChairYogaVideo() {

        List<ProjChairYogaVideo> chairYogaVideoList = chairYogaVideoService.listValid4Generate();
        if (CollectionUtils.isEmpty(chairYogaVideoList)) {
            return Collections.emptyList();
        }

        Set<Integer> videoIdSet = chairYogaVideoList.stream().map(ProjChairYogaVideo::getId).collect(Collectors.toSet());
        return chairYogaVideoList.stream().filter(video -> {
            if (Objects.equals(PoseDirectionEnum.CENTRAL.getPoseDirection(), video.getDirection())) {
                return true;
            }
            if (Objects.equals(PoseDirectionEnum.RIGHT.getPoseDirection(), video.getDirection())) {
                return true;
            }
            if (Objects.equals(PoseDirectionEnum.LEFT.getPoseDirection(), video.getDirection())
                    && Objects.nonNull(video.getRightVideoId()) && videoIdSet.contains(video.getRightVideoId())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    private void generateBoWithTemplate(ChairYogaWorkoutGenerateBO generateBO, Integer templateId) {

        ProjYogaAutoWorkoutTemplate workoutTemplate = workoutTemplateService.getById(templateId);
        if (Objects.isNull(workoutTemplate)) {
            throw new BizException("Can't find workout template.");
        }

        if (!Objects.equals(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getName(), workoutTemplate.getType())) {
            throw new BizException("Template type error, just generate for chair_yoga.");
        }

        generateBO.setWarmUpCount(workoutTemplate.getWarmUpCount());
        generateBO.setCoolDownCount(workoutTemplate.getCoolDownCount());
        generateBO.setProjId(workoutTemplate.getProjId());

        generateBO.setTemplateId(templateId);
        generateBO.setMaxMillSecond(convert2MillSecond(workoutTemplate.getMaxTime()));
        generateBO.setMinMillSecond(convert2MillSecond(workoutTemplate.getMinTime()));
    }

    private Integer convert2MillSecond(Integer minutes) {

        return (int) Duration.ofMinutes(minutes).toMillis();
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteByIds(Collection<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return;
        }

        LambdaQueryWrapper<ProjChairYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjChairYogaAutoWorkout::getId, workoutIds);
        queryWrapper.in(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_NOT_READY, GlobalConstant.STATUS_DRAFT);
        List<ProjChairYogaAutoWorkout> workoutDeleteAbleList = list(queryWrapper);

        if (CollectionUtils.isEmpty(workoutDeleteAbleList)) {
            return;
        }

        Set<Integer> workoutSet = workoutDeleteAbleList.stream().map(ProjChairYogaAutoWorkout::getId).collect(Collectors.toSet());
        List<ProjAutoWorkoutBasicInfoWorkoutRelation> workoutRelations = basicInfoWorkoutRelationService.listByPlanTypeAndWorkoutIds(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, workoutSet);
        if (!CollectionUtils.isEmpty(workoutRelations)) {
            List<String> errorMessages = workoutRelations.stream().map(this::convertWorkoutBasicInfo2String).collect(Collectors.toList());
            throw new BizException(String.format("This workout cannot be deleted because it is used in the following images: \n%s", String.join("\n", errorMessages)));
        }

        deleteBatch(workoutIds);
        workoutRelationService.deleteByChairYogaAutoWorkoutIds(workoutSet);
    }

    private String convertWorkoutBasicInfo2String(ProjAutoWorkoutBasicInfoWorkoutRelation workoutRelation) {

        return String.format("%s(%d)", workoutRelation.getProjAutoWorkoutBasicInfoName(), workoutRelation.getProjAutoWorkoutBasicInfoId());
    }

    private void deleteBatch(Collection<Integer> workoutIds) {

        LambdaUpdateWrapper<ProjChairYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjChairYogaAutoWorkout::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateUser, RequestContextUtils.getLoginUserName());

        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateTime, LocalDateTime.now());
        updateWrapper.in(ProjChairYogaAutoWorkout::getId, workoutIds);
        update(updateWrapper);
    }

    @Override
    public void updateEnableByIds(Collection<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return;
        }

        LambdaUpdateWrapper<ProjChairYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateUser, RequestContextUtils.getLoginUserName());
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateTime, LocalDateTime.now());

        updateWrapper.in(ProjChairYogaAutoWorkout::getId, workoutIds);
        updateWrapper.in(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_DISABLE, GlobalConstant.STATUS_DRAFT);
        update(updateWrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return;
        }

        List<ProjAutoWorkoutBasicInfoWorkoutRelation> workoutRelations = basicInfoWorkoutRelationService.listByPlanTypeAndWorkoutIds(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, workoutIds);
        if (!CollectionUtils.isEmpty(workoutRelations)) {
            List<String> errorMessages = workoutRelations.stream().map(this::convertWorkoutBasicInfo2String).collect(Collectors.toList());
            throw new BizException(String.format("This workout cannot be disabled because it is used in the following images: \n%s", String.join("\n", errorMessages)));
        }

        LambdaUpdateWrapper<ProjChairYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_DISABLE);
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateUser, RequestContextUtils.getLoginUserName());

        updateWrapper.eq(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjChairYogaAutoWorkout::getId, workoutIds);
        update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveWorkout(ProjChairYogaAutoWorkoutAddReq workoutAddReq) {

        bizValidate4Update(workoutAddReq);
        // 生成文件
        ChairYogaWorkoutVideoBO videoBO = getChairYogaWorkoutVideoBO();
        Map<Integer, ProjChairYogaVideo> videoMap = chairYogaVideoService.listByIds(workoutAddReq.getVideoIdList()).stream().collect(Collectors.toMap(BaseModel::getId, Function.identity(), (k1, k2) -> k1));
        Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap = chairYogaVideoSliceService.listByChairYogaVideoId(videoMap.keySet()).stream().collect(Collectors.groupingBy(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId));
        ProjChairYogaAutoWorkoutBO workoutBO = BeanUtil.toBean(workoutAddReq, ProjChairYogaAutoWorkoutBO.class);
        projChairYogaWorkoutFileHandler.generateChairYogaWorkoutFile(Collections.singletonList(workoutBO), workoutAddReq.getProjId(), videoBO, videoMap, videoSliceMap);
        // 保存数据
        ProjChairYogaAutoWorkout autoWorkout = new ProjChairYogaAutoWorkout();
        BeanUtils.copyProperties(workoutBO, autoWorkout);
        autoWorkout.setProjYogaAutoWorkoutTemplateId(workoutAddReq.getProjYogaAutoTemplateId());
        autoWorkout.setStatus(GlobalConstant.STATUS_DISABLE);
        save(autoWorkout);
        workoutBO.setEntity(autoWorkout);
        // save video relation
        List<ProjChairYogaAutoWorkoutVideoRelation> workoutVideoRelations = getWorkoutVideoRelations(workoutBO);
        workoutRelationService.saveBatch(workoutVideoRelations);
        // save audio
        List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = getAudioI18ns(workoutBO);

        projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);
    }

    private static List<ProjYogaAutoWorkoutAudioI18n> getAudioI18ns(ProjChairYogaAutoWorkoutBO workoutBO) {
        return workoutBO.getMultiLanguageAudioShortJson().keySet().stream()
                .map(language ->
                        new ProjYogaAutoWorkoutAudioI18n()
                                .setWorkoutId(workoutBO.getEntity().getId())
                                .setProjId(workoutBO.getEntity().getProjId())
                                .setLanguage(language)
                                .setWorkoutType(CHAIR_YOGA)
                                .setAudioLongJsonUrl(workoutBO.getMultiLanguageAudioLongJson().get(language))
                                .setAudioShortJsonUrl(workoutBO.getMultiLanguageAudioShortJson().get(language)))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(ProjChairYogaAutoWorkoutUpdateReq workoutUpdateReq) {

        bizValidate4Update(workoutUpdateReq);
        // 重新生成workout文件
        ChairYogaWorkoutVideoBO videoBO = getChairYogaWorkoutVideoBO();
        Map<Integer, ProjChairYogaVideo> videoMap = chairYogaVideoService.listByIds(workoutUpdateReq.getVideoIdList()).stream().collect(Collectors.toMap(BaseModel::getId, Function.identity(), (k1, k2) -> k1));
        Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap = chairYogaVideoSliceService.listByChairYogaVideoId(videoMap.keySet()).stream().collect(Collectors.groupingBy(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId));
        ProjChairYogaAutoWorkoutBO workoutBO = BeanUtil.toBean(workoutUpdateReq, ProjChairYogaAutoWorkoutBO.class);
        projChairYogaWorkoutFileHandler.generateChairYogaWorkoutFile(Collections.singletonList(workoutBO), workoutUpdateReq.getProjId(), videoBO, videoMap, videoSliceMap);
        ProjChairYogaAutoWorkout autoWorkout = updateAutoWorkout(workoutUpdateReq,workoutBO);
        workoutBO.setEntity(autoWorkout);
        updateDifficulty(workoutUpdateReq);

        // 保存workout-视频关联关系
        workoutRelationService.deleteByChairYogaAutoWorkoutIds(Lists.newArrayList(autoWorkout.getId()));
        List<ProjChairYogaAutoWorkoutVideoRelation> workoutVideoRelations = getWorkoutVideoRelations(workoutBO);
        workoutRelationService.saveBatch(workoutVideoRelations);
        // 保存多语言音频文件
        projYogaAutoWorkoutAudioI18nService.delete(autoWorkout.getId(), CHAIR_YOGA);
        List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = getAudioI18ns(workoutBO);
        projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);

    }

    private void updateDifficulty(ProjChairYogaAutoWorkoutUpdateReq infoReq) {

        if (Objects.nonNull(infoReq.getDifficulty())) {
            return;
        }

        LambdaUpdateWrapper<ProjChairYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjChairYogaAutoWorkout::getDifficulty, null);
        updateWrapper.eq(ProjChairYogaAutoWorkout::getId, infoReq.getId());
        update(updateWrapper);
    }

    private void bizValidate4Update(ProjChairYogaAutoWorkoutAddReq workoutAddReq) {

        if (Objects.isNull(workoutAddReq) || CollectionUtils.isEmpty(workoutAddReq.getVideoIdList())) {
            throw new BizException("Video list param can't be empty.");
        }

        List<Integer> videoIds = workoutAddReq.getVideoIdList();
        List<ProjChairYogaVideo> chairYogaVideoList = chairYogaVideoService.listByVideoIds(videoIds);
        if (CollectionUtils.isEmpty(chairYogaVideoList)) {
            throw new BizException(String.format("Can't find video list by id, id : %s.", videoIds.toString()));
        }

        Map<Integer, ProjChairYogaVideo> videoIdMap = chairYogaVideoList.stream().collect(Collectors.toMap(ProjChairYogaVideo::getId, Function.identity()));
        List<Integer> unMatchVideoIds = listUnMatchIds(videoIds, videoIdMap);
        if (!CollectionUtils.isEmpty(unMatchVideoIds)) {
            throw new BizException(String.format("Video status is not enable, ids : %s.", unMatchVideoIds.toString()));
        }

        List<ProjChairYogaVideoSliceDetailVO> detailVOList = chairYogaVideoSliceService.listByChairYogaVideoId(videoIds);
        Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> videoSliceMap = detailVOList.stream().collect(Collectors.groupingBy(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId));
        AtomicBoolean front = new AtomicBoolean(true);
        ChairYogaWorkoutVideoBO videoBO = getChairYogaWorkoutVideoBO();

        Map<Integer, Integer> realDurationMap = videoIds.stream().distinct().collect(Collectors.toMap(Function.identity(), videoId -> {
            ProjChairYogaVideo yogaVideo = videoIdMap.get(videoId);
            return computeRealDuration4Video(yogaVideo, videoBO, videoSliceMap, front);
        }));
        workoutAddReq.setRealDurationMap(realDurationMap);
    }

    private Integer selectDuration4EasyStart(ProjChairYogaVideoSliceDetailVO easyStart, AtomicBoolean front4Duration) {

        if (front4Duration.get()) {
            front4Duration.set(false);
            return easyStart.getFrontVideoDuration();
        } else {
            front4Duration.set(true);
            return easyStart.getSideVideoDuration();
        }
    }

    private List<Integer> listUnMatchIds(List<Integer> videoIds, Map<Integer, ProjChairYogaVideo> videoIdMap) {

        return videoIds.stream().filter(id -> {
            if (!videoIdMap.containsKey(id)) {
                return true;
            }
            if (!Objects.equals(videoIdMap.get(id).getStatus(), GlobalConstant.STATUS_ENABLE)) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    private ProjChairYogaAutoWorkout updateAutoWorkout(ProjChairYogaAutoWorkoutUpdateReq workoutUpdateReq, ProjChairYogaAutoWorkoutBO workoutBO) {

        ProjChairYogaAutoWorkout workout4Update = getById(workoutUpdateReq.getId());
        if (Objects.isNull(workout4Update)) {
            throw new BizException(String.format("Can't find workout with id : %s .", workoutUpdateReq.getId()));
        }

        BeanUtils.copyProperties(workoutUpdateReq, workout4Update);
        String userName = StringUtils.isEmpty(workoutUpdateReq.getUpdateUser()) ? RequestContextUtils.getLoginUserName() : workoutUpdateReq.getUpdateUser();
        workout4Update.setUpdateUser(userName);
        workout4Update.setUpdateStatus(ResUpdateStatusEnum.SUCCESS.getStatus());
        workout4Update.setUpdateTime(LocalDateTime.now());

        workout4Update.setAudioShortJson(workoutBO.getAudioShortJson());
        workout4Update.setAudioLongJson(workoutBO.getAudioLongJson());
        workout4Update.setCalorie(workoutBO.getCalorie());
        workout4Update.setDuration(workoutBO.getDuration());
        workout4Update.setVideoM3u8Url(workoutBO.getVideoM3u8Url());
        workout4Update.setVideo2532Url(workoutBO.getVideo2532Url());
        workout4Update.setTarget(workoutBO.getTarget());

        saveOrUpdate(workout4Update);
        return workout4Update;
    }

    @Override
    public void batchUpdate(ProjChairYogaAutoWorkoutBatchUpdateReq batchUpdateReq) {

        if (Objects.isNull(batchUpdateReq) || CollectionUtils.isEmpty(batchUpdateReq.getWorkoutIds())) {
            return;
        }

        Collection<ProjChairYogaAutoWorkout> workoutList = listByIds(batchUpdateReq.getWorkoutIds());
        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        // 更新这一批次数据的状态
        workoutList.forEach(workout -> workout.setUpdateStatus(ResUpdateStatusEnum.UPDATE.getStatus()));
        updateBatchById(workoutList);
        // 异步更新workout
        asyncUpdateRes(batchUpdateReq.getProjId(),workoutList, RequestContextUtils.getLoginUserName());
    }

    private void asyncUpdateRes(Integer projId, Collection<ProjChairYogaAutoWorkout> workoutList, String operationUser) {

        try {
            asyncService.doSomethings(() -> workoutList.forEach(workout -> {
                try {
                    log.warn("Start to update res for chair yoga auto workout, workout id is {}.", workout.getId());
                    updateRes4Workout(projId,workout.getId(), operationUser);
                    log.warn("Finished to update res for chair yoga auto workout, workout id is {}.", workout.getId());
                } catch (Exception ex) {
                    updateResStatus2Failed(workout.getId(), operationUser);
                    log.warn("async update chair yoga auto workout failed, workout id is {}.", workout.getId());
                    log.warn(ex.getMessage(), ex);
                }
            }));
        } catch (Exception exception) {
            log.warn(exception.getMessage(), exception);
        }
    }

    private void updateRes4Workout(Integer projId,Integer workoutId, String operationUser) {

        ProjChairYogaAutoWorkoutDetailVO detailVO = findDetailById(workoutId);
        ProjChairYogaAutoWorkoutUpdateReq updateReq = wrapUpdateReq(projId,detailVO, operationUser);
        IProjChairYogaAutoWorkoutService workoutService = SpringUtil.getBean(IProjChairYogaAutoWorkoutService.class);
        workoutService.update(updateReq);
    }

    private ProjChairYogaAutoWorkoutUpdateReq wrapUpdateReq(Integer projId,ProjChairYogaAutoWorkoutDetailVO detailVO, String operationUser) {

        ProjChairYogaAutoWorkoutUpdateReq updateReq = new ProjChairYogaAutoWorkoutUpdateReq();
        BeanUtils.copyProperties(detailVO, updateReq);
        updateReq.setUpdateUser(operationUser);
        updateReq.setProjId(projId);

        List<Integer> videoIdList = detailVO.getVideoList().stream().map(ProjChairYogaVideoPageVO::getId).collect(Collectors.toList());
        updateReq.setVideoIdList(videoIdList);
        return updateReq;
    }

    private boolean updateResStatus2Failed(Integer workoutId, String operationUser) {

        LambdaUpdateWrapper<ProjChairYogaAutoWorkout> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(ProjChairYogaAutoWorkout::getId, workoutId);
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateUser, operationUser);
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ProjChairYogaAutoWorkout::getUpdateStatus, ResUpdateStatusEnum.FAIL.getStatus());
        return update(updateWrapper);
    }

    @Override
    public ProjChairYogaAutoWorkoutDetailVO findDetailById(Integer workoutId) {

        if (Objects.isNull(workoutId)) {
            return null;
        }

        ProjChairYogaAutoWorkout autoWorkout = getById(workoutId);
        if (Objects.isNull(autoWorkout)) {
            return null;
        }

        ProjChairYogaAutoWorkoutDetailVO detailVO = new ProjChairYogaAutoWorkoutDetailVO();
        BeanUtils.copyProperties(autoWorkout, detailVO);
        detailVO.setProjYogaAutoTemplateId(autoWorkout.getProjYogaAutoWorkoutTemplateId());

        // list video, order by relation_id asc
        List<ProjChairYogaVideoPageVO> chairYogaVideoList = workoutRelationService.listRelationByWorkoutId(workoutId);
        detailVO.setVideoList(chairYogaVideoList);
        return detailVO;
    }

    @Override
    public Collection<ProjChairYogaAutoWorkout> listByWorkoutIds(Collection<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return Collections.emptyList();
        }

        return listByIds(workoutIds);
    }

    @Override
    public List<CountBO> listWorkoutCountByTemplate(Collection<Integer> templateIds) {

        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }

        return chairYogaAutoWorkoutMapper.listWorkoutCountByTemplate(templateIds);
    }

    @Override
    public PageRes<ProjChairYogaAutoWorkoutPageVO> page(ProjChairYogaAutoWorkoutPageReq pageReq) {

        LambdaQueryWrapper<ProjChairYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        wrapPageQuery(queryWrapper, pageReq);

        Page<ProjChairYogaAutoWorkout> queryPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjChairYogaAutoWorkout> resultPage = this.page(queryPage, queryWrapper);
        if (Objects.isNull(resultPage) || CollectionUtils.isEmpty(resultPage.getRecords())) {
            return new PageRes(pageReq.getPageNum(), pageReq.getPageSize(), GlobalConstant.ZERO, GlobalConstant.ZERO, Collections.emptyList());
        }

        // 查询任务信息
        List<ProjChairYogaAutoWorkout> workoutList = resultPage.getRecords();
        Set<Integer> taskIds = workoutList.stream().map(ProjChairYogaAutoWorkout::getProjYogaAutoWorkoutTaskId).collect(Collectors.toSet());
        Collection<ProjYogaAutoWorkoutTask> projYogaAutoWorkoutTasks = workoutTaskService.listByIds(taskIds);

        Map<Integer, ProjYogaAutoWorkoutTask> workoutTaskMap = projYogaAutoWorkoutTasks.stream().collect(Collectors.toMap(ProjYogaAutoWorkoutTask::getId, t -> t));
        List<ProjChairYogaAutoWorkoutPageVO> resultRecords = workoutList.stream().map(workout -> convertEntity2PageVO(workout, workoutTaskMap)).collect(Collectors.toList());
        return new PageRes<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal(), resultPage.getPages(), resultRecords);
    }

    private ProjChairYogaAutoWorkoutPageVO convertEntity2PageVO(ProjChairYogaAutoWorkout workout, Map<Integer, ProjYogaAutoWorkoutTask> workoutTaskMap) {

        ProjChairYogaAutoWorkoutPageVO pageVO = new ProjChairYogaAutoWorkoutPageVO();
        BeanUtils.copyProperties(workout, pageVO);
        Integer updateStatus = pageVO.getUpdateStatus();
        pageVO.setDisplayUpdateStatus(ResUpdateStatusEnum.SingletonHolder.getStatusMap().get(updateStatus));

        if (workoutTaskMap.containsKey(workout.getProjYogaAutoWorkoutTaskId())) {
            pageVO.setCreateUser(workoutTaskMap.get(workout.getProjYogaAutoWorkoutTaskId()).getCreateUser());
            pageVO.setGenerateNum(workoutTaskMap.get(workout.getProjYogaAutoWorkoutTaskId()).getGenerateNum());
            pageVO.setCleanUp(workoutTaskMap.get(workout.getProjYogaAutoWorkoutTaskId()).getCleanUp());
        }
        return pageVO;
    }

    private void wrapPageQuery(LambdaQueryWrapper<ProjChairYogaAutoWorkout> queryWrapper, ProjChairYogaAutoWorkoutPageReq pageReq) {

        Integer projYogaAutoWorkoutTemplateId = pageReq.getProjYogaAutoWorkoutTemplateId();
        if (null == projYogaAutoWorkoutTemplateId) {
            List<ProjYogaAutoWorkoutTemplate> templateList = workoutTemplateService.find(pageReq.getTemplateStatus());
            Set<Integer> templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
            queryWrapper.in(!CollectionUtils.isEmpty(templateIdSet), ProjChairYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIdSet);
        }

        queryWrapper.eq(Objects.nonNull(projYogaAutoWorkoutTemplateId), ProjChairYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, projYogaAutoWorkoutTemplateId);
        queryWrapper.eq(Objects.nonNull(pageReq.getProjYogaAutoWorkoutTaskId()), ProjChairYogaAutoWorkout::getProjYogaAutoWorkoutTaskId, pageReq.getProjYogaAutoWorkoutTaskId());
        queryWrapper.eq(Objects.nonNull(pageReq.getId()), ProjChairYogaAutoWorkout::getId, pageReq.getId());

        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjChairYogaAutoWorkout::getStatus, pageReq.getStatus());
        queryWrapper.eq(!StringUtils.isEmpty(pageReq.getTarget()), ProjChairYogaAutoWorkout::getTarget, pageReq.getTarget());
        queryWrapper.eq(Objects.nonNull(pageReq.getUpdateStatus()), ProjChairYogaAutoWorkout::getUpdateStatus, pageReq.getUpdateStatus());
        queryWrapper.eq(!StringUtils.isEmpty(pageReq.getDifficulty()), ProjChairYogaAutoWorkout::getDifficulty, pageReq.getDifficulty());

        if (Objects.nonNull(pageReq.getTime())) {
            DurationEnum durationEnum = DurationEnum.getByCode(pageReq.getTime());
            queryWrapper.between(ProjChairYogaAutoWorkout::getDuration, durationEnum.getMin(), durationEnum.getMax());
        }
    }

    @Override
    public PageRes<ProjChairYogaVideo> page4RelationVideo(ProjChairYogaAutoWorkoutPageReq pageReq) {

        IPage<ProjChairYogaAutoWorkoutVideoRelation> iPage = workoutRelationService.page(pageReq);
        if (Objects.isNull(iPage) || CollectionUtils.isEmpty(iPage.getRecords())) {
            return new PageRes<>();
        }

        List<Integer> videoIds = iPage.getRecords().stream().map(ProjChairYogaAutoWorkoutVideoRelation::getProjChairYogaVideoId).collect(Collectors.toList());
        List<ProjChairYogaVideo> chairYogaVideoList = chairYogaVideoService.listByVideoIds(videoIds);
        Map<Integer, ProjChairYogaVideo> chairYogaVideoMap = chairYogaVideoList.stream().collect(Collectors.toMap(video -> video.getId(), Function.identity()));

        List<ProjChairYogaVideo> sortedVideoList = videoIds.stream().map(chairYogaVideoMap::get).collect(Collectors.toList());
        return new PageRes<>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal(), iPage.getTotal(), sortedVideoList);
    }
}
