package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjWallPilatesAutoWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjWallPilatesAutoWorkoutVideoRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjWallPilatesAutoWorkoutVideoRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <p>
 * Wall pilates auto workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Service
public class ProjWallPilatesAutoWorkoutVideoRelationServiceImpl extends ServiceImpl<ProjWallPilatesAutoWorkoutVideoRelationMapper, ProjWallPilatesAutoWorkoutVideoRelation> implements IProjWallPilatesAutoWorkoutVideoRelationService {

    @Override
    public void deleteByWallPilatesAutoWorkoutId(Set<Integer> wallPilatesAutoWorkoutIdList) {
        if(CollUtil.isEmpty(wallPilatesAutoWorkoutIdList)){
            return;
        }
        LambdaUpdateWrapper<ProjWallPilatesAutoWorkoutVideoRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjWallPilatesAutoWorkoutVideoRelation::getProjWallPilatesAutoWorkoutId, wallPilatesAutoWorkoutIdList)
                .set(BaseModel::getDelFlag, GlobalConstant.YES);
        baseMapper.update(new ProjWallPilatesAutoWorkoutVideoRelation(), wrapper);
    }

}
