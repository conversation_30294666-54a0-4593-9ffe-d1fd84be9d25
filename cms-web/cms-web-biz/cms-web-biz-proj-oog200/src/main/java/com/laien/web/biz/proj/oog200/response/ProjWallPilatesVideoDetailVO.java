package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjWallPilatesVideoDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "English Voice Name")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String[] targetArr;

    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String position;

    @ApiModelProperty(value = "动作类型 ： Warm Up、Main 、Cool Down")
    private String type;

    @ApiModelProperty(value = "当前动作方向 Left、Right、Central")
    private String direction;

    @ApiModelProperty(value = "关联左右动作leftRightDetail")
    private ProjWallPilatesVideoLeftRightDetailVO leftRightDetail;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "视频地址(正机位 2532 m3u8)")
    private String video2532Url;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

}
