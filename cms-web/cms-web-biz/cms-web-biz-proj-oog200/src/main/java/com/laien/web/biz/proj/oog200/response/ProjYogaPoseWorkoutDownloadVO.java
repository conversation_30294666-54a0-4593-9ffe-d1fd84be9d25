package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj yoga pose workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseWorkout对象", description="proj yoga pose workout")
public class ProjYogaPoseWorkoutDownloadVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "sanskrit name")
    private String sanskritName;

    @ApiModelProperty(value = "workout 封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "workout 详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "pose 彩色图像")
    private String poseLightImgUrl;

    @ApiModelProperty(value = "pose 黑白图像")
    private String poseDarkImgUrl;

    @ApiModelProperty(value = "难度Newbie, Beginner, Intermediate, Advanced")
    private String difficulty;

    @ApiModelProperty(value = "pose详细介绍")
    private String instructions;

    @ApiModelProperty(value = "pose的优势")
    private String benefits;

    @ApiModelProperty(value = "chair variation for seniors")
    private String chairVariation;

    @ApiModelProperty(value = "chair variation image")
    private String chairVariationImgUrl;

    @ApiModelProperty(value = "chair variation tips for beginner")
    private String chairVariationTips;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer flexibility;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer balance;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer strength;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer relaxation;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "Standing，Seated，Supine，Prone，Arm & Leg Support")
    private String position;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "取值Flexibility、Balance、Strength、Relaxation")
    private String focus;
}
