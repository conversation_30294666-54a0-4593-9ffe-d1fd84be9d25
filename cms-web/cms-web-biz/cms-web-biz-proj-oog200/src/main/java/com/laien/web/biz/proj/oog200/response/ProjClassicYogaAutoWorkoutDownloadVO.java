package com.laien.web.biz.proj.oog200.response;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjClassicYogaAutoWorkoutDownloadVO {

    @ExcelProperty(value = "Workout ID")
    @ApiModelProperty(value = "id")
    private Integer id;

    @ExcelProperty(value = "Time(s)")
    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ExcelProperty(value = "Calorie")
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ExcelProperty(value = "Difficulty")
    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ExcelProperty(value = "Goal")
    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String goal;

    @ExcelProperty(value = "Status")
    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;
}
