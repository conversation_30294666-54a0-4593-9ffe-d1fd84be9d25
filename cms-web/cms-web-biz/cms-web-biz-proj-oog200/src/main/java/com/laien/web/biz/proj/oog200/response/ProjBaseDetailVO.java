package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author:  hhl
 * Date:  2024/8/1 17:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "base detail VO")
public class ProjBaseDetailVO {

    @ApiModelProperty(value = "数据唯一标识")
    private Integer Id;

    @ApiModelProperty(value = "展示名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;
}
