package com.laien.web.biz.proj.oog200.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/8/1 14:18
 */
@Data
@ApiModel(value = "pose transition page query")
public class ProjYogaPoseTransitionPageReq extends PageReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "排序字段，降序方式，可选值为name，为空默认Id降序")
    private String sortField;

}
