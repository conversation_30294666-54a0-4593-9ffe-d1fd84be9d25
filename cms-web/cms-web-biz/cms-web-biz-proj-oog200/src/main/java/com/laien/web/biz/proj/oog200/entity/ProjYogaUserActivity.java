package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: hhl
 * @date: 2025/6/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_user_activity")
@ApiModel(value="ProjYogaUserActivity对象", description="yoga user activity")
public class ProjYogaUserActivity extends BaseModel {

    private Integer userId;

//    private Integer activityId;

    private String productId;

    private String inviteCode;

}
