package com.laien.web.biz.proj.oog200.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import com.laien.web.frame.validation.Group3;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * <p>
 * proj yoga pose workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Getter
@Setter
@EqualsAndHashCode
@ApiModel(value="ProjYogaPoseWorkout对象", description="proj yoga pose workout")
public class ProjYogaPoseWorkoutImportReq {

    @NotNull(message = "id cannot be null", groups = Group1.class)
    @ApiModelProperty(value = "数据id")
    @ExcelProperty(value = "id")
    private Integer id;

    @NotEmpty(message = "name cannot be empty", groups = Group1.class)
    @Length(message = "The name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名字")
    private String name;

    @Length(message = "The sanskritName cannot exceed 100 characters", max = 100, groups = Group1.class)
    @ExcelProperty(value = "sanskritName", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "sanskrit name")
    private String sanskritName;

    @NotEmpty(message = "coverImgUrl cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "workout 封面图")
    @ExcelProperty(value = "coverImgUrl")
    private String coverImgUrl;

    @NotEmpty(message = "detailImgUrl cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "workout 详情图")
    @ExcelProperty(value = "detailImgUrl")
    private String detailImgUrl;

    @NotEmpty(message = "poseLightImgUrl cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "pose 彩色图像")
    @ExcelProperty(value = "poseLightImgUrl")
    private String poseLightImgUrl;

    @NotEmpty(message = "poseDarkImgUrl cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "pose 黑白图像")
    @ExcelProperty(value = "poseDarkImgUrl")
    private String poseDarkImgUrl;

    @NotEmpty(message = "difficulty cannot be empty", groups = Group1.class)
    @Pattern(message = "difficulty The naming rule is incorrect", regexp = "\\b(Newbie|Beginner|Intermediate|Advanced)\\b", groups = Group3.class)
    @ExcelProperty(value = "difficulty", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "难度Newbie, Beginner, Intermediate, Advanced")
    private String difficulty;

    @Length(message = "The instructions cannot exceed 512 characters", max = 512, groups = Group1.class)
    @ApiModelProperty(value = "pose详细介绍")
    @ExcelProperty(value = "instructions", converter = StringStringTrimConverter.class)
    private String instructions;

    @Length(message = "The benefits cannot exceed 512 characters", max = 512, groups = Group1.class)
    @ApiModelProperty(value = "pose的优势")
    @ExcelProperty(value = "benefits", converter = StringStringTrimConverter.class)
    private String benefits;

    @Length(message = "The chairVariation cannot exceed 512 characters", max = 512, groups = Group1.class)
    @ApiModelProperty(value = "chair variation for seniors")
    @ExcelProperty(value = "chairVariation", converter = StringStringTrimConverter.class)
    private String chairVariation;

    @ApiModelProperty(value = "chair variation image")
    @ExcelProperty(value = "chairVariationImgUrl", converter = StringStringTrimConverter.class)
    private String chairVariationImgUrl;

    @Length(message = "The chairVariationTips cannot exceed 512 characters", max = 512, groups = Group2.class)
    @ApiModelProperty(value = "chair variation tips for beginner")
    @ExcelProperty(value = "chairVariationTips", converter = StringStringTrimConverter.class)
    private String chairVariationTips;

    @NotNull(message = "flexibility cannot be null", groups = Group1.class)
    @Min(message = "flexibility minimum value cannot be less than 0", value = 0, groups = Group2.class)
    @Max(message = "flexibility maximum value cannot exceed 5", value = 5, groups = Group3.class)
    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    @ExcelProperty(value = "flexibility")
    private Integer flexibility;

    @NotNull(message = "balance cannot be null", groups = Group1.class)
    @Min(message = "balance minimum value cannot be less than 0", value = 0, groups = Group2.class)
    @Max(message = "balance maximum value cannot exceed 5", value = 5, groups = Group3.class)
    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    @ExcelProperty(value = "balance")
    private Integer balance;

    @NotNull(message = "strength cannot be null", groups = Group1.class)
    @Min(message = "strength minimum value cannot be less than 0", value = 0, groups = Group2.class)
    @Max(message = "strength maximum value cannot exceed 5", value = 5, groups = Group3.class)
    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    @ExcelProperty(value = "strength")
    private Integer strength;

    @NotNull(message = "relaxation cannot be null", groups = Group1.class)
    @Min(message = "relaxation minimum value cannot be less than 0", value = 0, groups = Group2.class)
    @Max(message = "relaxation maximum value cannot exceed 5", value = 5, groups = Group3.class)
    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    @ExcelProperty(value = "relaxation")
    private Integer relaxation;

    @NotNull(message = "subscription cannot be null", groups = Group1.class)
    @Min(message = "subscription minimum value cannot be less than 0", value = 0, groups = Group2.class)
    @Max(message = "subscription maximum value cannot exceed 1", value = 1, groups = Group3.class)
    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    @ExcelProperty(value = "subscription")
    private Integer subscription;

    @NotEmpty(message = "position cannot be empty", groups = Group1.class)
    @Pattern(message = "position The naming rule is incorrect", regexp = "\\b(Standing|Seated|Supine|Prone|Arm & Leg Support)\\b", groups = Group2.class)
    @ExcelProperty(value = "position", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "Standing，Seated，Supine，Prone，Arm & Leg Support")
    private String position;

    @NotEmpty(message = "focus cannot be empty", groups = Group1.class)
    @Pattern(message = "focus The naming rule is incorrect", regexp = "\\b(Flexibility|Balance|Strength|Relaxation)\\b", groups = Group2.class)
    @ExcelProperty(value = "focus", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "取值Flexibility、Balance、Strength、Relaxation")
    private String focus;

}
