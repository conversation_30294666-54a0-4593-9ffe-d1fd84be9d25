package com.laien.web.biz.proj.oog200.response;

import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/7/31 14:57
 */
@Data
@ApiModel(value = "ProjYogaPoseTransitionPageVO")
public class ProjYogaPoseTransitionPageVO extends ProjOperationDetailVO{

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "图片")
    private String imageUrl;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "正机位切片任务状态")
    private TaskResourceSectionStatusEnums frontTaskStatus;

}
