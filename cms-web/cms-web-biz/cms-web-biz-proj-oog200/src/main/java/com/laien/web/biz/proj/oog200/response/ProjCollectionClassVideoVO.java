package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * note: collection class video 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "collection class video 详情", description = "collection class video 详情")
public class ProjCollectionClassVideoVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "image png")
    private String imagePng;

    @ApiModelProperty(value = "image gif")
    private String imageGif;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog")
    private YogaAutoWorkoutTemplateEnum type;

}
