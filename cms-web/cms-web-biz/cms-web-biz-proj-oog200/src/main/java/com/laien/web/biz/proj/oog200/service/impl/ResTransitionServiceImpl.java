package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.web.biz.core.util.SqlUtils;
import com.laien.web.biz.proj.oog200.entity.ResTransition;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideo;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideoConnection;
import com.laien.web.biz.proj.oog200.entity.ResYogaVideoCooldown;
import com.laien.web.biz.proj.oog200.mapper.ResTransitionMapper;
import com.laien.web.biz.proj.oog200.request.ResTransitionAddReq;
import com.laien.web.biz.proj.oog200.request.ResTransitionImportScriptReq;
import com.laien.web.biz.proj.oog200.request.ResTransitionPageReq;
import com.laien.web.biz.proj.oog200.request.ResTransitionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ResTransitionDetailVO;
import com.laien.web.biz.proj.oog200.response.ResTransitionDownloadVO;
import com.laien.web.biz.proj.oog200.response.ResTransitionPageVO;
import com.laien.web.biz.proj.oog200.service.IResTransitionService;
import com.laien.web.biz.proj.oog200.service.IResYogaVideoConnectionService;
import com.laien.web.biz.proj.oog200.service.IResYogaVideoCooldownService;
import com.laien.web.biz.proj.oog200.service.IResYogaVideoService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.RES_TRANSITION_FRONT;
import static com.laien.common.oog200.enums.TaskResourceSectionQueryEnums.RES_TRANSITION_SIDE;


/**
 * <p>
 * 过渡 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class ResTransitionServiceImpl extends ServiceImpl<ResTransitionMapper, ResTransition> implements IResTransitionService {

    @Resource
    private IResYogaVideoConnectionService resYogaVideoConnectionService;
    @Resource
    private IResYogaVideoCooldownService resYogaVideoCooldownService;
    @Resource
    private IResYogaVideoService resYogaVideoService;
    @Resource
    private ITaskResourceSectionService taskResourceSectionService;
    @Resource
    private Validator validator;
    @Resource
    private ICoreVoiceConfigI18nService i18nConfigService;

    @Override
    public PageRes<ResTransitionPageVO> selectTransitionPage(ResTransitionPageReq pageReq) {
        LambdaQueryWrapper<ResTransition> queryWrapper = new LambdaQueryWrapper<>();
        String name = pageReq.getName();
        Integer status = pageReq.getStatus();
        queryWrapper.like(StringUtils.isNotBlank(name), ResTransition::getName, SqlUtils.escapeLikeValue(name))
                .eq(Objects.nonNull(status), ResTransition::getStatus, status)
                .orderByAsc(Objects.equals(pageReq.getRequestPage(), "video_add"), ResTransition::getName)
                .orderByDesc(ResTransition::getId);

        Page<ResTransition> resVideoPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ResTransition> page = this.page(resVideoPage, queryWrapper);
        PageRes<ResTransitionPageVO> result = PageConverter.convert(page, ResTransitionPageVO.class);
        this.injectionTaskStatus(result.getList());
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveTransition(ResTransitionAddReq TransitionAddReq) {
        ResTransition transition = new ResTransition();
        BeanUtils.copyProperties(TransitionAddReq, transition);
        transition.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(transition);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTransition(ResTransitionUpdateReq TransitionUpdateReq) {
        Integer id = TransitionUpdateReq.getId();
        ResTransition transitionFind = this.getById(id);
        if (Objects.isNull(transitionFind)) {
            throw new BizException("Data not found");
        }
        ResTransition transition = new ResTransition();
        BeanUtils.copyProperties(TransitionUpdateReq, transition);
        this.updateById(transition);
    }

    @Override
    public ResTransitionDetailVO getTransitionDetail(Integer id) {
        ResTransition transitionFind = this.getById(id);
        if (Objects.isNull(transitionFind)) {
            throw new BizException("Data not found");
        }

        ResTransitionDetailVO detailVO = new ResTransitionDetailVO();
        BeanUtils.copyProperties(transitionFind, detailVO);
        fillI18nConfigInfoForDetail(detailVO);
        return detailVO;
    }

    private void fillI18nConfigInfoForDetail(ResTransitionDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResTransition> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResTransition::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResTransition::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ResTransition::getId, idList);
        this.update(new ResTransition(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Set<Integer> updateDisableByIds(List<Integer> idList) {
        Map<Integer, ResTransition> transitionMap = this.list(new LambdaQueryWrapper<ResTransition>().in(ResTransition::getId, idList))
                .stream().collect(Collectors.toMap(ResTransition::getId, o -> o));

        List<Integer> ids = idList.stream().filter(
                id -> {
                    ResTransition resTransition = transitionMap.get(id);
                    // id 数据不能存在，数据不是启用状态直接忽略不做任何操作
                    return Objects.nonNull(resTransition) && Objects.equals(resTransition.getStatus(), GlobalConstant.STATUS_ENABLE);
                }
        ).collect(Collectors.toList());

        Set<Integer> failedIdSet = new LinkedHashSet<>();
        if (ids.isEmpty()) {
            return failedIdSet;
        }

        Set<Integer> videoIds = new HashSet<>();
        // 查询connection
        Map<Integer, List<ResYogaVideoConnection>> connectionGroup = resYogaVideoConnectionService.list(
                        new LambdaQueryWrapper<ResYogaVideoConnection>().in(ResYogaVideoConnection::getResTransitionId, ids)
                ).stream().peek(o -> videoIds.add(o.getResYogaVideoId()))
                .collect(Collectors.groupingBy(ResYogaVideoConnection::getResTransitionId));

        // 查询cooldown
        Map<Integer, List<ResYogaVideoCooldown>> cooldownGroup = resYogaVideoCooldownService.list(
                        new LambdaQueryWrapper<ResYogaVideoCooldown>().in(ResYogaVideoCooldown::getResTransitionId, ids)
                ).stream().peek(o -> videoIds.add(o.getResYogaVideoId()))
                .collect(Collectors.groupingBy(ResYogaVideoCooldown::getResTransitionId));

        // 查询connection和cooldown 用到的启用的video
        Map<Integer, ResYogaVideo> videoMap;
        if (!videoIds.isEmpty()) {
            videoMap = resYogaVideoService.list(
                            new LambdaQueryWrapper<ResYogaVideo>().in(ResYogaVideo::getId, videoIds)
                                    .eq(ResYogaVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                    )
                    .stream().collect(Collectors.toMap(ResYogaVideo::getId, o -> o));
        } else {
            videoMap = new HashMap<>();
        }

        // 最后操作的id list
        List<Integer> idUpdateList = new ArrayList<>();
        for (Integer id : ids) {
            List<ResYogaVideoConnection> connectionList = connectionGroup.get(id);
            if (connectionList != null) {
                boolean flag = connectionList.stream()
                        .anyMatch(connection -> videoMap.get(connection.getResYogaVideoId()) != null);
                if (flag) {
                    failedIdSet.add(id);
                    continue;
                }
            }

            List<ResYogaVideoCooldown> cooldownList = cooldownGroup.get(id);
            if (cooldownList != null) {
                boolean flag = cooldownList.stream()
                        .anyMatch(cooldown -> cooldownGroup.get(cooldown.getResYogaVideoId()) != null);
                if (flag) {
                    failedIdSet.add(id);
                    continue;
                }
            }

            idUpdateList.add(id);
        }

        // 禁用可以禁用的
        if (!idUpdateList.isEmpty()) {
            LambdaUpdateWrapper<ResTransition> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ResTransition::getStatus, GlobalConstant.STATUS_DISABLE);
            wrapper.eq(ResTransition::getStatus, GlobalConstant.STATUS_ENABLE);
            wrapper.in(ResTransition::getId, idUpdateList);
            this.update(new ResTransition(), wrapper);
        }

        return failedIdSet;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResTransition> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResTransition::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ResTransition::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ResTransition::getId, idList);
        this.update(new ResTransition(), wrapper);
    }

    @Override
    public List<ResTransitionDownloadVO> downloadList() {
        List<ResTransition> transitionList = list();
        if (CollUtil.isEmpty(transitionList)) {
            return Collections.emptyList();
        }
        List<ResTransitionDownloadVO> downloadList = new ArrayList<>();
        for (ResTransition resTransition : transitionList) {
            ResTransitionDownloadVO downloadVO = new ResTransitionDownloadVO();
            BeanUtils.copyProperties(resTransition, downloadVO);
            downloadList.add(downloadVO);
        }
        return downloadList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importScriptByExcel(InputStream inputStream) {
        List<ResTransitionImportScriptReq> importList = CollUtil.newArrayList();
        EasyExcel.read(inputStream, ResTransitionImportScriptReq.class, new AnalysisEventListener<ResTransitionImportScriptReq>() {
            @Override
            public void invoke(ResTransitionImportScriptReq row, AnalysisContext analysisContext) {
                importList.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        List<String> failMessage = new ArrayList<>();
        if (CollUtil.isEmpty(importList)) {
            return failMessage;
        }
        for (ResTransitionImportScriptReq transitionReq : importList) {
            Integer transitionReqId = transitionReq.getId();
            Set<ConstraintViolation<ResTransitionImportScriptReq>> violationSet = validator.validate(transitionReq, Group.class);
            if (CollUtil.isNotEmpty(violationSet)) {
                violationSet.stream().findFirst().ifPresent(violation -> failMessage.add(String.format("%s:%s", transitionReqId, violation.getMessage())));
            }
        }
        if(CollUtil.isNotEmpty(failMessage)){
            return failMessage;
        }
        List<ResTransition> transitionList = new ArrayList<>(importList.size());
        for (ResTransitionImportScriptReq importScriptReq : importList) {
            ResTransition resTransition = new ResTransition();
            BeanUtils.copyProperties(importScriptReq, resTransition);
            transitionList.add(resTransition);
        }
        updateBatchById(transitionList);
        return failMessage;
    }

    /**
     * 正侧切片任务状态返回
     */
    private void injectionTaskStatus(List<ResTransitionPageVO> transitions) {
        if (CollUtil.isEmpty(transitions)) {
            return;
        }
        Set<Integer> transitionsIds = transitions.stream()
                .map(ResTransitionPageVO::getId)
                .collect(Collectors.toSet());
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(RES_TRANSITION_SIDE.getTableName(), RES_TRANSITION_SIDE.getEntityFieldName(), transitionsIds);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(RES_TRANSITION_FRONT.getTableName(), RES_TRANSITION_FRONT.getEntityFieldName(), transitionsIds);
        if (CollUtil.isEmpty(sideStatusList) && CollUtil.isEmpty(frontStatusList)) {
            return;
        }
        sideStatusList = null == sideStatusList ? new ArrayList<>() : sideStatusList;
        frontStatusList = null == frontStatusList ? new ArrayList<>() : frontStatusList;
        Map<Integer, TaskResourceSectionStatusEnums> sideIdStatusMap = sideStatusList.stream()
                .collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));
        Map<Integer, TaskResourceSectionStatusEnums> frontIdStatusMap = frontStatusList.stream()
                .collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));
        transitions.forEach(transition -> {
            Optional.ofNullable(sideIdStatusMap.get(transition.getId()))
                    .ifPresent(transition::setSideTaskStatus);
            Optional.ofNullable(frontIdStatusMap.get(transition.getId()))
                    .ifPresent(transition::setFrontTaskStatus);
        });
    }
}
