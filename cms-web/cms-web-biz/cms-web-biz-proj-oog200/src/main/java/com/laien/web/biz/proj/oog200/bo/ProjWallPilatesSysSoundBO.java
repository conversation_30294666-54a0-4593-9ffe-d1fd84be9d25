package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.common.file.bo.AudioJsonBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="Wall pilates auto workout")
public class ProjWallPilatesSysSoundBO {

    private AudioJsonBO firstAudio;
    private AudioJsonBO nextAudio;
    private AudioJsonBO lastAudio;
    private AudioJsonBO startAudio;
    private AudioJsonBO restAudio;

}
