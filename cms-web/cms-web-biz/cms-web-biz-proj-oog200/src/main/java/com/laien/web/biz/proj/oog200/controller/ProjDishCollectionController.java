package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.oog200.entity.ProjDishCollection;
import com.laien.web.biz.proj.oog200.request.ProjDishCollectionAddReq;
import com.laien.web.biz.proj.oog200.request.ProjDishCollectionListReq;
import com.laien.web.biz.proj.oog200.request.ProjDishCollectionUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjDishCollectionDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjDishCollectionListVO;
import com.laien.web.biz.proj.oog200.service.IProjDishCollectionService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:13
 */
@Api(tags = "项目管理: dishCollection")
@RestController
@RequestMapping("/proj/dishCollection")
public class ProjDishCollectionController extends ResponseController {

    @Resource
    private IProjDishCollectionService dishCollectionService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjDishCollectionListVO>> list(ProjDishCollectionListReq listReq) {

        List<ProjDishCollectionListVO> collectionVOList = dishCollectionService.selectDishCollection(listReq);
        return succ(collectionVOList);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjDishCollectionAddReq addReq) {

        dishCollectionService.saveDishCollection(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjDishCollectionUpdateReq updateReq) {

        dishCollectionService.updateDishCollection(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjDishCollectionDetailVO> detail(@PathVariable Integer id) {

        ProjDishCollectionDetailVO detailVO = dishCollectionService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        dishCollectionService.sort(idListReq);
        return succ();
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        dishCollectionService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        dishCollectionService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        dishCollectionService.deleteByIds(idList);
        return succ();
    }

}
