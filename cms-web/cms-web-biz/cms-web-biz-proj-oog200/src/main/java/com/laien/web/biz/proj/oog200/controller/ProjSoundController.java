package com.laien.web.biz.proj.oog200.controller;

import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import com.laien.web.biz.proj.oog200.request.ProjSoundAddReq;
import com.laien.web.biz.proj.oog200.request.ProjSoundPageReq;
import com.laien.web.biz.proj.oog200.request.ProjSoundUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjSoundDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjSoundPageVO;
import com.laien.web.biz.proj.oog200.service.IProjSoundService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Api(tags = "项目管理: sound")
@RestController
@RequestMapping("/proj/sound200")
public class ProjSoundController extends ResponseController {

    @Resource
    private IProjSoundService soundService;

    @ApiOperation(value = " sound 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSoundPageVO>> page(ProjSoundPageReq req) {
        req.setProjId(RequestContextUtils.getProjectId()) ;
        AssertUtil.notNull(RequestContextUtils.getProjectId(),"projId is null");
        PageRes<ProjSoundPageVO> pageRes = soundService.selectSoundPage(req);
        return succ(pageRes);
    }

    @ApiOperation(value = " Sound 增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSoundAddReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.saveSound(req);
        return succ();
    }

    @ApiOperation(value = " Sound 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSoundUpdateReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.updateSound(req);
        return succ();
    }

    @ApiOperation(value = " Sound 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSoundDetailVO> detail(@PathVariable Integer id) {
        ProjSoundDetailVO detailVO = soundService.getDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = " Sound 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = " Sound 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateDisableByIds(idList);
        return succ();
    }
}