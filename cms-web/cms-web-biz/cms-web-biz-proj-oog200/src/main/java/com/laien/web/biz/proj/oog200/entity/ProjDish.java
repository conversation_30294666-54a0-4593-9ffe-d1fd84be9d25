package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.common.m3u8.seq.annotation.ResourceSection;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * Dish
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_dish")
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjDish extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作展示名称")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型多选用英文逗号分隔，100:Breakfast,101:Lunch,102:Dinner,103:Meal Replacement")
    private String types;

    @ApiModelProperty(value = "风格，多选用英文逗号分隔100:Vegan,101:Mediterranean,102:Keto,103:Smoothie")
    private String styles;

    @ApiModelProperty(value = "准备时间，单位分钟")
    private Integer prepareTime;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "碳水含量")
    private BigDecimal carb;

    @ApiModelProperty(value = "蛋白质含量")
    private BigDecimal protein;

    @ApiModelProperty(value = "脂肪含量")
    private BigDecimal fat;

    @ResourceSection(
            tableName = "proj_dish",
            m3u8UrlColumn = "video_url",
            m3u8Url2532Column = "video2532_url",
            durationColum = "duration",
            dirKey = "project-dish-m3u8"
    )
    @ApiModelProperty(value = "源视频地址")
    private String resourceVideoUrl;

    @ApiModelProperty(value = "多分辨率m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "2532 的m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "视频时长")
    private Integer duration;

    @ApiModelProperty(value = "份数")
    private Integer serving;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer sorted;

}
