/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseUserAssignIdModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>ProjChairYogaVideo多语言数据 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
public class ProjChairYogaVideoI18n extends BaseUserAssignIdModel {

    @ApiModelProperty(value = "名称多语言翻译")
    private String name;

    @ApiModelProperty(value = "名称翻译音频")
    private String nameFemale;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameFemaleDuration;
}