package com.laien.web.biz.proj.oog200.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * yoga auto workout生成模版
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaAutoWorkoutTemplatePub对象", description="yoga auto workout生成模版")
public class ProjYogaAutoWorkoutTemplatePub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据版本号")
    private Integer version;

    @ApiModelProperty(value = "模版名称")
    private String name;

    @ApiModelProperty(value = "模版描述")
    private String description;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "生成workout的最小时长，默认5，单位分钟")
    private Integer minTime;

    @ApiModelProperty(value = "生成workout的最大时长，默认35，单位分钟")
    private Integer maxTime;

    @ApiModelProperty(value = "template类型：Classic Yoga、Wall Pilates、Chair Yoga")
    private String type;

    @ApiModelProperty(value = "warm up个数")
    private Integer warmUpCount;

    @ApiModelProperty(value = "cool down个数")
    private Integer coolDownCount;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
