/*
 * (C) Copyright 2015-2020, by Vera-Licona Research Group and Contributors.
 *
 * JGraphT : a free Java graph-theory library
 *
 * See the CONTRIBUTORS.md file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0, or the
 * GNU Lesser General Public License v2.1 or later
 * which is available at
 * http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html.
 *
 * SPDX-License-Identifier: EPL-2.0 OR LGPL-2.1-or-later
 */
package com.laien.web.biz.proj.oog200.algorithm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.SystemClock;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.web.biz.proj.oog200.service.impl.ProjYogaAutoWorkoutGenerateServiceImpl;
import lombok.Data;
import org.jgrapht.Graph;
import org.jgrapht.GraphPath;
import org.jgrapht.GraphTests;
import org.jgrapht.graph.GraphWalk;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * A Dijkstra-like algorithm to find all paths between two sets of nodes in a directed graph, with
 * options to search only simple paths and to limit the path length.
 *
 * @param <V> the graph vertex type
 * @param <E> the graph edge type
 * <AUTHOR> Gainer-Dewar, Google LLC
 */
public class ProjYogaAutoWorkoutGenerateAlgorithm<V, E> {
    private final Graph<V, E> graph;

    /**
     * Create a new instance.
     *
     * @param graph the input graph
     * @throws IllegalArgumentException if the graph is not directed
     */
    public ProjYogaAutoWorkoutGenerateAlgorithm(Graph<V, E> graph) {
        this.graph = GraphTests.requireDirected(graph);
    }

//    /**
//     * Calculate (and return) all paths from the source vertex to the target vertex.
//     *
//     * @param sourceVertex    the source vertex
//     * @param targetVertex    the target vertex
//     * @param simplePathsOnly if true, only search simple (non-self-intersecting) paths
//     * @param maxPathLength   maximum number of edges to allow in a path (if null, all paths are
//     *                        considered, which may be very slow due to potentially huge output)
//     * @return all paths from the source vertex to the target vertex
//     */
//    public List<GraphPath<V, E>> getAllPaths(
//            V sourceVertex, V targetVertex, boolean simplePathsOnly, Integer maxPathLength, Integer minPathWeight, Integer maxPathWeight, Integer maxPathCount) {
//        return getAllPaths(
//                Collections.singleton(sourceVertex), Collections.singleton(targetVertex),
//                simplePathsOnly, maxPathLength, minPathWeight, maxPathWeight, maxPathCount);
//    }

    /**
     * Calculate (and return) all paths from the source vertices to the target vertices.
     *
     * @param startNodes      the source vertices 起始的节点集合
     * @param mainNodes       the target vertices 所有可衔接的节点集合
     * @param endNodes        结尾的节点，必须包含至少一个该集合中的节点
     * @param simplePathsOnly if true, only search simple (non-self-intersecting) paths
     * @param maxPathLength   maximum number of edges to allow in a path (if null, all paths are
     *                        considered, which may be very slow due to potentially huge output)
     * @param minPathWeight   满足要求的最小权重
     * @param maxPathWeight   满足要求的最大权重
     * @param threadPathCount 路径的最大数量，当满足此条件后将停止继续查找路径
     * @return list of all paths from the sources to the targets containing no more than
     * maxPathLength edges
     */
    public List<GraphPath<V, E>> getAllPaths(
            Set<V> startNodes, Set<V> mainNodes, Set<V> endNodes, boolean simplePathsOnly,
            Integer maxPathLength, Integer minPathWeight, Integer maxPathWeight, AtomicInteger threadPathCount, Integer maxPathCount, ProjYogaAutoWorkoutGenerateServiceImpl.NodeDifficulty nodeDifficulty, Long timeoutStamp) {
        if ((maxPathLength != null) && (maxPathLength < 0)) {
            throw new IllegalArgumentException("maxPathLength must be non-negative if defined");
        }

        if (!simplePathsOnly && (maxPathLength == null)) {
            throw new IllegalArgumentException(
                    "If search is not restricted to simple paths, a maximum path length must be set to avoid infinite cycles");
        }

        if ((startNodes.isEmpty()) || (mainNodes.isEmpty())) {
            return Collections.emptyList();
        }

        // Decorate the edges with the minimum path lengths through them
        Map<E, Integer> edgeMinDistancesFromTargets =
                edgeMinDistancesBackwards(mainNodes, maxPathLength);

        // Generate all the paths

        return generatePaths(startNodes, mainNodes, endNodes, simplePathsOnly, maxPathLength,
                edgeMinDistancesFromTargets, minPathWeight, maxPathWeight, threadPathCount, maxPathCount, nodeDifficulty, timeoutStamp);
    }

    /**
     * Compute the minimum number of edges in a path to the targets through each edge, so long as it
     * is not greater than a bound.
     *
     * @param targetVertices the target vertices
     * @param maxPathLength  maximum number of edges to allow in a path (if null, all edges will be
     *                       considered, which may be expensive)
     * @return the minimum number of edges in a path from each edge to the targets, encoded in a Map
     */
    private Map<E, Integer> edgeMinDistancesBackwards(Set<V> targetVertices, Integer maxPathLength) {
        /*
         * We walk backwards through the network from the target vertices, marking edges and
         * vertices with their minimum distances as we go.
         */
        Map<E, Integer> edgeMinDistances = new HashMap<>();
        Map<V, Integer> vertexMinDistances = new HashMap<>();
        Queue<V> verticesToProcess = new LinkedList<>();

        // Input sanity checking
        if (maxPathLength != null) {
            if (maxPathLength < 0) {
                throw new IllegalArgumentException("maxPathLength must be non-negative if defined");
            }
            if (maxPathLength == 0) {
                return edgeMinDistances;
            }
        }

        // Bootstrap the process with the target vertices
        for (V target : targetVertices) {
            vertexMinDistances.put(target, 0);
            verticesToProcess.add(target);
        }

        // Work through the node queue. When it's empty, we're done!
        for (V vertex; (vertex = verticesToProcess.poll()) != null; ) {
            assert vertexMinDistances.containsKey(vertex);

            Integer childDistance = vertexMinDistances.get(vertex) + 1;

            // Check whether the incoming edges of this node are correctly
            // decorated
            for (E edge : graph.incomingEdgesOf(vertex)) {
                // Mark the edge if needed
                if (!edgeMinDistances.containsKey(edge)
                        || (edgeMinDistances.get(edge) > childDistance)) {
                    edgeMinDistances.put(edge, childDistance);
                }

                // Mark the edge's source vertex if needed
                V edgeSource = graph.getEdgeSource(edge);
                if (!vertexMinDistances.containsKey(edgeSource)
                        || (vertexMinDistances.get(edgeSource) > childDistance)) {
                    vertexMinDistances.put(edgeSource, childDistance);

                    if ((maxPathLength == null) || (childDistance < maxPathLength)) {
                        verticesToProcess.add(edgeSource);
                    }
                }
            }
        }

        assert verticesToProcess.isEmpty();
        return edgeMinDistances;
    }

    /**
     * Generate all paths from the sources to the targets, using pre-computed minimum distances.
     *
     * @param startNodes                  开始节点
     * @param mainNodes                   可连接节点
     * @param endNodes                    可结尾节点
     * @param simplePathsOnly             if true, only search simple (non-self-intersecting) paths (if null,
     *                                    all edges will be considered, which may be expensive)
     * @param maxPathDeep                 最大的深度，代表路径最大的节点长度
     * @param edgeMinDistancesFromTargets the minimum number of edges in a path to a target through
     *                                    each edge, as computed by {@code
     *                                    edgeMinDistancesBackwards}.
     * @param minPathDuration             路径需要满足的最短距离
     * @param maxPathDuration             路径需要满足的最大距离
     * @param manythreadGeneratedCount    多线程目前已生成的路径数量
     * @param targetGenerateCount         需要生成的路径数量
     * @param nodeDifficulty              本次生成的难度
     * @return a List of all GraphPaths from the sources to the targets satisfying the given
     * constraints
     */
    private List<GraphPath<V, E>> generatePaths(
            Set<V> startNodes, Set<V> mainNodes, Set<V> endNodes, boolean simplePathsOnly,
            Integer maxPathDeep, Map<E, Integer> edgeMinDistancesFromTargets, Integer minPathDuration, Integer maxPathDuration, AtomicInteger manythreadGeneratedCount, Integer targetGenerateCount, ProjYogaAutoWorkoutGenerateServiceImpl.NodeDifficulty nodeDifficulty, Long timeoutStamp) {
        /*
         * We walk forwards through the network from the source vertices, exploring all outgoing
         * edges whose minimum distances is small enough.
         */
        List<GraphPath<V, E>> completePaths = new ArrayList<>();

        Set<String> completePathMarks = Sets.newHashSet();
        Deque<List<E>> incompletePaths = new LinkedList<>();

        // Input sanity checking
        if (maxPathDeep != null && maxPathDeep < 0) {
            throw new IllegalArgumentException("maxPathLength must be non-negative if defined");
        }

        // Bootstrap the search with the source vertices
        for (V source : startNodes) {
            if (maxPathDeep != null && maxPathDeep == 0) {
                continue;
            }
            LeftRightArrayList<E> path2 = new LeftRightArrayList<>(minPathDuration, maxPathDuration);
            // source开始节点，edge是source多有的连线，edge是list
            for (E edge : outgoingEdgesOf(source, path2, nodeDifficulty)) {
                V n = graph.getEdgeTarget(edge);
                LeftRightArrayList<E> path = new LeftRightArrayList<>(minPathDuration, maxPathDuration);
                path.add(edge);
                if (path.isSkip()) {
                    continue;
                }
                if (endNodes.contains(n)) {
                    //如果既是main node又是 end node就走下面逻辑，处理只有两个video
                    GraphPath<V, E> completePath = makePath(path);
                    if (path.checkSuperfluousLeft() && path.checkNonrepetitive(5)) {
                        //保证路径不重复逻辑
                        String pathMark = getPathMark(completePath);
                        if (!completePathMarks.contains(pathMark)) {
                            completePaths.add(completePath);
                            completePathMarks.add(pathMark);
                        }
                    } else {
                        continue;
                    }
                }
                if (edgeMinDistancesFromTargets.containsKey(edge)
                        && (maxPathDeep == null || maxPathDeep > 1)) {
                    incompletePaths.add(path);
                }
            }
        }

        if (maxPathDeep != null && maxPathDeep == 0) {
            return completePaths;
        }

        // Walk through the queue of incomplete paths
        for (List<E> incompletePath; (incompletePath = incompletePaths.poll()) != null; ) {
            Integer lengthSoFar = incompletePath.size();
            if (((targetGenerateCount != null && manythreadGeneratedCount.get() >= targetGenerateCount)) || cn.hutool.core.date.SystemClock.now() >= timeoutStamp) {
                return completePaths;
            }
            assert (maxPathDeep == null) || (lengthSoFar < maxPathDeep);

            E leafEdge = incompletePath.get(lengthSoFar - 1);
            V leafNode = graph.getEdgeTarget(leafEdge);
            LeftRightArrayList<E> path2 = new LeftRightArrayList<>(incompletePath, minPathDuration, maxPathDuration);
            for (E outEdge : outgoingEdgesOf(leafNode, path2, nodeDifficulty)) {
                long now = SystemClock.now();
                // Proceed if the outgoing edge is marked and the mark
                // is sufficiently small
                if (edgeMinDistancesFromTargets.containsKey(outEdge) && ((maxPathDeep == null)
                        || ((edgeMinDistancesFromTargets.get(outEdge) + lengthSoFar) <= maxPathDeep))) {

                    if (((targetGenerateCount != null && manythreadGeneratedCount.get() >= targetGenerateCount)) || cn.hutool.core.date.SystemClock.now() >= timeoutStamp) {
                        return completePaths;
                    }
                    LeftRightArrayList<E> newPath = new LeftRightArrayList<>(incompletePath, minPathDuration, maxPathDuration);

                    // If requested, make sure this path isn't self-intersecting
                    if (simplePathsOnly && newPath.getNonrepetitiveEdge().contains(outEdge)) {
                        continue;
                    }

                    newPath.add(outEdge);

                    if (newPath.isSkip()) {
                        continue;
                    }

                    V edgeTarget = graph.getEdgeTarget(outEdge);

                    // If this path reaches a target, add it to completePaths
                    if (mainNodes.contains(edgeTarget) && endNodes.contains(edgeTarget)) {
                        if (newPath.checkSuperfluousLeft() && newPath.checkNonrepetitive(5)) {
                            GraphPath<V, E> completePath = makePath(newPath);
                            String pathMark = getPathMark(completePath);
                            if (!completePathMarks.contains(pathMark)) {
                                completePaths.add(completePath);
                                completePathMarks.add(pathMark);
                                manythreadGeneratedCount.addAndGet(1);
//                                System.out.println(SystemClock.now() - now + " add");
                            } else {
//                                System.out.println(SystemClock.now() - now + " c23");
                                continue;
                            }
                        } else {
//                            System.out.println(SystemClock.now() - now + "  c3");
                            continue;
                        }
                    }
                    // If this path is short enough, consider further
                    // extensions of it 这里改成addLast，防止某个start链路有问题永远无法结束，然后死在他的长链路中
                    //addLast 为广度优先，会平均遍历所有同级节点，会造成路径池子存量过多，内存占用高，有内存溢出风险
                    //addFirst 为深度优先，会优先遍历一个Start，当此Start所有情况遍历完成后，再进行下一个Start的遍历，配合maxPathDeep，没有内存溢出风险，内存消耗极小
//                    if (incompletePaths.size() < 100000) {
                    if (newPath.getPriority() > 0) {
                        incompletePaths.addFirst(newPath);
                    } else {
                        incompletePaths.addLast(newPath);
                    }
//                    }
                    // We use
//                    System.out.println(SystemClock.now() - now + " c4");
                }
            }
        }

        assert incompletePaths.isEmpty();
        return completePaths;
    }

    //cache source edge list
    private Map<V, List<E>> sourceEdgeListCache = Maps.newHashMap();

    /**
     * 使用一次 打乱一次
     * 总遍历次数不变，只是会让每次出现的顺序改变
     *
     * @param source
     * @param newPath 后面调整逻辑后，暂时不需要使用到他,可以留作备用
     * @return
     */
    private Collection<E> outgoingEdgesOf(V source, LeftRightArrayList<E> newPath, ProjYogaAutoWorkoutGenerateServiceImpl.NodeDifficulty nodeDifficulty) {
        List<E> es = sourceEdgeListCache.get(source);
        if (es == null) {
            Set<E> eSet = graph.outgoingEdgesOf(source);
            es = CollUtil.newArrayList(eSet);
            sourceEdgeListCache.put(source, es);
        }
        Collections.shuffle(es);
        //优先级处理
        es = es.stream().sorted((e1, e2) -> {
            V edgeTargetNode1 = graph.getEdgeTarget(e1);
            V edgeTargetNode2 = graph.getEdgeTarget(e2);
            if (((ProjYogaAutoWorkoutGenerateServiceImpl.Node) edgeTargetNode2).getDifficultyType() == nodeDifficulty && ((ProjYogaAutoWorkoutGenerateServiceImpl.Node) edgeTargetNode1).getDifficultyType() != nodeDifficulty) {
                return 1;
            }
            return 0;
        }).collect(Collectors.toList());


//        List<E> finalEdges = CollUtil.newArrayList(es);
//        if (CollUtil.isNotEmpty(es) && es.size() > 1) {
//            finalEdges = CollUtil.newArrayList();
//            Map<E, Integer> finalEdgesMap = MapUtil.newHashMap();
//            for (E e : es) {
//                finalEdgesMap.put(e, 0);
//            }
//            //第一优先级 不得出现 A>>B B>>A 的情况  满足这种情况+100分
//            if (newPath.size() >= 1) {
//                for (E e : es) {
//                    V preEdgeSourceNode = graph.getEdgeSource((E) newPath.get(newPath.size() - 1));
//                    V curEdgeTargetNode = graph.getEdgeTarget(e);
//                    Integer orDefault = finalEdgesMap.getOrDefault(e, 0);
//                    if (preEdgeSourceNode != curEdgeTargetNode) {
//                        finalEdgesMap.put(e, orDefault + 100);
//                    }
//                }
//            }
//            //第二优先级 不得使用重复的 edge，满足这种情况 +10分
//            if (newPath.size() >= 1) {
//                for (E e : es) {
//                    if (!newPath.getNonrepetitiveEdge().contains(e)) {
//                        Integer orDefault = finalEdgesMap.getOrDefault(e, 0);
//                        finalEdgesMap.put(e, orDefault + 10);
//                    }
//                }
//            }
        //第三优先级 优先使用同难度的flow
//            for (E e : es) {
//                V curEdgeTargetNode = graph.getEdgeTarget(e);
//                if (((ProjYogaAutoWorkoutGenerateServiceImpl.Node) curEdgeTargetNode).getDifficultyType() == nodeDifficulty) {
//                    Integer orDefault = finalEdgesMap.getOrDefault(e, 0);
//                    finalEdgesMap.put(e, orDefault + 1);
//                }
//            }
//            //TreeMap
//            Map<Integer, List<E>> treeMap = MapUtil.newHashMap();
//            for (Map.Entry<E, Integer> entry : finalEdgesMap.entrySet()) {
//                Integer value = entry.getValue();
//                E key = entry.getKey();
//                List<E> orDefault = treeMap.getOrDefault(value, CollUtil.newArrayList());
//                orDefault.add(key);
//                treeMap.put(value, orDefault);
//            }
//            List<Integer> priorityValues = treeMap.keySet().stream().sorted().collect(Collectors.toList());
//            Collections.reverse(priorityValues);
//            for (Integer priorityValue : priorityValues) {
//                List<E> es1 = treeMap.get(priorityValue);
//                Collections.shuffle(es1);
//                finalEdges.addAll(es1);
//            }
//        }
        return es;
    }

    private String getPathMark(GraphPath<V, E> completePath) {
        List<String> markList = completePath.getVertexList().stream().map(Objects::toString).collect(Collectors.toList());
        return MD5.create().digestHex(CollUtil.join(markList, ","));
    }


    /**
     * Transform an ordered list of edges into a GraphPath.
     * <p>
     * The weight of the generated GraphPath is set to the sum of the weights of the edges.
     *
     * @param edges the edges
     * @return the corresponding GraphPath
     */
    private GraphPath<V, E> makePath(List<E> edges) {
        V source = graph.getEdgeSource(edges.get(0));
        V target = graph.getEdgeTarget(edges.get(edges.size() - 1));
        double weight = edges.stream().mapToDouble(edge -> graph.getEdgeWeight(edge)).sum();
        return new GraphWalk<>(graph, source, target, edges, weight);
    }

    @Data
    public class PathPriority<E> {

        private E e;

        private Integer priority = 0;

    }


    /**
     * 自定义的一个集合类
     *
     * @param <F>
     */
    @Data
    public class LeftRightArrayList<F> extends ArrayList<F> {

        /**
         * 记录当前需要校验的Left 索引号
         */
        private List<Integer> leftIndex = CollUtil.newArrayList();

        /**
         * 记录当前需要校验的Right 索引号
         */
        private List<Integer> rightIndex = CollUtil.newArrayList();

        /**
         * 不重复的边集合
         */
        private Set<E> nonrepetitiveEdge = Sets.newHashSet();

        /**
         * 不重复的节点集合
         */
        private Set<V> nonrepetitiveNode = Sets.newHashSet();

        /**
         * 这条路径已经没有继续探索的必要，可以直接结束
         */
        private boolean isSkip = false;

        /**
         * 当前路径总时长
         */
        private double totalDuration = 0d;

        /**
         * 路径需满足的最小时长
         */
        private double minPathDuration;

        /**
         * 路径需满足的最大时长
         */
        private double maxPathDuration;

        private Integer priority = 1;

        public LeftRightArrayList(double minPathDuration, double maxPathDuration) {
            super();
            this.minPathDuration = minPathDuration;
            this.maxPathDuration = maxPathDuration;
        }

        public LeftRightArrayList(Collection<? extends F> c, double minPathDuration, double maxPathDuration) {
            super(c);
            if (c instanceof LeftRightArrayList) {
                leftIndex.addAll(((LeftRightArrayList<? extends F>) c).getLeftIndex());
                rightIndex.addAll(((LeftRightArrayList<? extends F>) c).getRightIndex());
                isSkip = ((LeftRightArrayList<? extends F>) c).isSkip();
                nonrepetitiveNode.addAll(((LeftRightArrayList<? extends F>) c).getNonrepetitiveNode());
                nonrepetitiveEdge.addAll(((LeftRightArrayList<? extends F>) c).getNonrepetitiveEdge());
                totalDuration = ((LeftRightArrayList<? extends F>) c).getTotalDuration();
            }
            this.minPathDuration = minPathDuration;
            this.maxPathDuration = maxPathDuration;
        }

        @Override
        /**
         * 添加逻辑，这里为了尽快砍掉无效的路径，因此在每一次增加节点后，都会校验节点是否满足后续继续探索的条件
         */
        public boolean add(F edge) {
            boolean addFlag = super.add(edge);
            //当前边的目标节点
            V targetNode = graph.getEdgeTarget((E) edge);
            //当前边的源节点
            V sourceNode = graph.getEdgeSource((E) edge);
            if (size() == 1) {
                if (((ProjYogaAutoWorkoutGenerateServiceImpl.Node) sourceNode).getDirect() == ProjYogaAutoWorkoutGenerateServiceImpl.NodeDirect.LEFT) {
                    leftIndex.add(-1);
                }
                if (((ProjYogaAutoWorkoutGenerateServiceImpl.Node) sourceNode).getDirect() == ProjYogaAutoWorkoutGenerateServiceImpl.NodeDirect.RIGHT) {
                    isSkip = true;
                    return addFlag;
                }
            }
            if (targetNode instanceof ProjYogaAutoWorkoutGenerateServiceImpl.Node) {
                E currentEdge = (E) edge;
                //记录当前路径的总时长
                if (totalDuration == 0d) {
                    //第一条边，要加上开始节点的时长
                    totalDuration += ((ProjYogaAutoWorkoutGenerateServiceImpl.Node) sourceNode).getPoseTime();
                }
                totalDuration += graph.getEdgeWeight(currentEdge);
                //
                if (size() > 1) {
                    F f = get(size() - 2);
                    V preSource = graph.getEdgeSource((E) f);
                    //当出现 A>>B 时 只有 B只有一个到A的flow时才允许直接接B>>A
                    if (preSource == targetNode) {
                        Set<E> eSet = graph.outgoingEdgesOf(preSource);
                        Sets.SetView<E> difference = Sets.difference(eSet, nonrepetitiveEdge);
                        if (!(difference.size() == 1 && difference.iterator().next() == edge)) {
                            isSkip = true;
                            return addFlag;
                        }
                    }
                }
                //当总时长已大于最大时长，此路径无效，不需要继续探索
                if (totalDuration > maxPathDuration) {
                    isSkip = true;
                    return addFlag;
                }
                //记录当前路径去重后的边和节点
                nonrepetitiveNode.add(targetNode);
                nonrepetitiveEdge.add(currentEdge);
                //当目标节点方向为右时
                if (((ProjYogaAutoWorkoutGenerateServiceImpl.Node) targetNode).getDirect() == ProjYogaAutoWorkoutGenerateServiceImpl.NodeDirect.RIGHT) {
                    rightIndex.add(size() - 1);
                    //如果右节点数多于左节点数，不满足先左后右的条件，路径无效
                    if (rightIndex.size() > leftIndex.size()) {
                        isSkip = true;
                        return addFlag;
                    } else {
                        //查找左节点 相同索引位置的节点，是否与当前节点是一对
                        Integer lThisIndex = leftIndex.get(rightIndex.size() - 1);
                        F lf = null;
                        ProjYogaAutoWorkoutGenerateServiceImpl.Node lNode = null;
                        if (lThisIndex == -1) {
                            lf = get(0);
                            lNode = (ProjYogaAutoWorkoutGenerateServiceImpl.Node) graph.getEdgeSource((E) lf);
                        } else {
                            lf = get(lThisIndex);
                            lNode = (ProjYogaAutoWorkoutGenerateServiceImpl.Node) graph.getEdgeTarget((E) lf);
                        }
                        if (!lNode.getNoDirectName().equals(((ProjYogaAutoWorkoutGenerateServiceImpl.Node) targetNode).getNoDirectName())) {
                            isSkip = true;
                            return addFlag;
                        }
                    }
                    //如果以上都通过，继续判断连续性,连续行只判断最近的两个节点，即可保证所有路径，因为每次新进来节点又会判断
                    if (leftIndex.size() >= 2 && rightIndex.size() >= 2) {
                        Integer lThisIndex = leftIndex.get(rightIndex.size() - 1);
                        Integer preThisIndex = leftIndex.get(rightIndex.size() - 2);
                        if (lThisIndex - preThisIndex == 1) {
                            Integer rLast = rightIndex.get(rightIndex.size() - 1);
                            Integer preRLast = rightIndex.get(rightIndex.size() - 2);
                            if (rLast - preRLast != 1) {
                                isSkip = true;
                                return addFlag;
                            }
                        }
                    }
                }
                //当目标节点方向为左时
                if (((ProjYogaAutoWorkoutGenerateServiceImpl.Node) targetNode).getDirect() == ProjYogaAutoWorkoutGenerateServiceImpl.NodeDirect.LEFT) {
                    leftIndex.add(size() - 1);
                }
            }
            return addFlag;
        }


        /**
         * 检查是否有多余的left节点没有匹配
         *
         * @return
         */
        public boolean checkSuperfluousLeft() {
            if (leftIndex.size() == rightIndex.size()) {
                return true;
            }
            return false;
        }

        /**
         * 不重复的节点数校验
         *
         * @param count
         * @return
         */
        public boolean checkNonrepetitive(Integer count) {
            return nonrepetitiveNode.size() >= count && totalDuration >= minPathDuration;
        }

    }


}
