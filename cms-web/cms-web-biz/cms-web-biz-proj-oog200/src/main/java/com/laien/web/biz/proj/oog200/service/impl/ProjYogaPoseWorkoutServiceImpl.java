package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.laien.common.oog200.enums.PoseDirectionEnum;
import com.laien.common.oog200.enums.PoseTypeEnum;
import com.laien.common.oog200.enums.ResUpdateStatusEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.bo.ProjYogaPoseWorkoutBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutAudioI18n;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseWorkout;
import com.laien.web.biz.proj.oog200.handler.ProjYogaPoseWorkoutFileHandler;
import com.laien.web.biz.proj.oog200.mapper.ProjYogaPoseWorkoutMapper;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutImportReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutListVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaAutoWorkoutAudioI18nService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseVideoService;
import com.laien.web.biz.proj.oog200.service.IProjYogaPoseWorkoutService;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.laien.web.frame.async.config.ThreadPoolConfig.OTHER_TASK_THREAD_POOL;

/**
 * <p>
 * proj yoga pose workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Slf4j
@Service
public class ProjYogaPoseWorkoutServiceImpl
        extends ServiceImpl<ProjYogaPoseWorkoutMapper, ProjYogaPoseWorkout> implements IProjYogaPoseWorkoutService {

    @Resource
    private ProjYogaPoseWorkoutFileHandler projYogaPoseWorkoutFileHandler;

    @Resource
    IProjYogaPoseVideoService yogaPoseVideoService;

    @Resource
    IProjYogaPoseWorkoutService yogaPoseWorkoutService;

    @Resource
    ProjYogaPoseWorkoutMapper poseWorkoutMapper;

    @Resource
    private Validator validator;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private IProjYogaAutoWorkoutAudioI18nService projYogaAutoWorkoutAudioI18nService;

    @Resource
    private IProjLmsI18nService lmsI18nService;

    private final Object INSERT_POSE_WORKOUT_LOCK = new Object();

    @Override
    @Transactional
    public void saveBatch(List<ProjYogaPoseWorkoutAddReq> yogaPoseWorkoutReqList, Integer projId) {
        if (CollUtil.isEmpty(yogaPoseWorkoutReqList)) {
            return;
        }
        List<ProjYogaPoseWorkout> yogaPoseWorkoutList = new ArrayList<>(yogaPoseWorkoutReqList.size());
        for (ProjYogaPoseWorkoutAddReq workoutAddReq : yogaPoseWorkoutReqList) {
            ProjYogaPoseWorkout workout = new ProjYogaPoseWorkout();
            BeanUtils.copyProperties(workoutAddReq, workout);
            workout.setProjId(projId);
            yogaPoseWorkoutList.add(workout);
        }
        saveBatch(yogaPoseWorkoutList);
        lmsI18nService.handleI18n(yogaPoseWorkoutList, projId);
    }

    @Override
    @Transactional
    public void update(ProjYogaPoseWorkoutUpdateReq yogaPoseWorkoutReq, Integer projId) {
        Integer id = yogaPoseWorkoutReq.getId();
        ProjYogaPoseWorkout projYogaPoseWorkout = baseMapper.selectById(id);
        if (null == projYogaPoseWorkout) {
            throw new BizException("projYogaPoseWorkout not found");
        }
        checkWorkout(yogaPoseWorkoutReq, id);
        ProjYogaPoseWorkout workout = new ProjYogaPoseWorkout();
        BeanUtils.copyProperties(yogaPoseWorkoutReq, workout);
        workout.setProjId(projId);
        Integer status = yogaPoseWorkoutReq.getStatus();
        if (null == status || GlobalConstant.STATUS_NOT_READY == status) {
            workout.setStatus(GlobalConstant.STATUS_DRAFT);
        }
        updateById(workout);
        lmsI18nService.handleI18n(Collections.singletonList(workout), projId);
    }

    @Override
    @Transactional
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjYogaPoseWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjYogaPoseWorkout::getId, idList);
        update(new ProjYogaPoseWorkout(), wrapper);
    }

    @Override
    @Transactional
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjYogaPoseWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseWorkout::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjYogaPoseWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjYogaPoseWorkout::getId, idList);
        this.update(new ProjYogaPoseWorkout(), wrapper);
    }

    @Override
    public PageRes<ProjYogaPoseWorkoutListVO> page(ProjYogaPoseWorkoutPageReq pageReq, Integer projId) {
        String difficulty = pageReq.getDifficulty();
        String position = pageReq.getPosition();
        Integer status = pageReq.getStatus();
        String name = pageReq.getName();
        String focus = pageReq.getFocus();
        LambdaQueryWrapper<ProjYogaPoseWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseWorkout::getProjId, projId)
                .like(StringUtils.isNotBlank(name), ProjYogaPoseWorkout::getName, name)
                .eq(StringUtils.isNotBlank(difficulty), ProjYogaPoseWorkout::getDifficulty, difficulty)
                .eq(StringUtils.isNotBlank(position), ProjYogaPoseWorkout::getPosition, position)
                .eq(null != status, ProjYogaPoseWorkout::getStatus, status)
                .eq(StringUtils.isNotBlank(focus), ProjYogaPoseWorkout::getFocus, focus)
                .orderByDesc(BaseModel::getCreateTime);
        Page<ProjYogaPoseWorkout> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjYogaPoseWorkout> workoutPage = page(page, wrapper);
        return PageConverter.convert(workoutPage, ProjYogaPoseWorkoutListVO.class);
    }

    @Override
    public ProjYogaPoseWorkoutDetailVO findDetailById(Integer id) {
        ProjYogaPoseWorkout projYogaPoseWorkout = baseMapper.selectById(id);
        if (null == projYogaPoseWorkout) {
            return null;
        }
        ProjYogaPoseWorkoutDetailVO detailVO = new ProjYogaPoseWorkoutDetailVO();
        BeanUtils.copyProperties(projYogaPoseWorkout, detailVO);
        return detailVO;
    }

    @Transactional
    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjYogaPoseWorkout> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjYogaPoseWorkout::getDelFlag, GlobalConstant.YES)
                .in(ProjYogaPoseWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY)
                .in(ProjYogaPoseWorkout::getId, idList);
        this.update(new ProjYogaPoseWorkout(), wrapper);
    }

    @Override
    public List<CountBO> findCount(Integer status, Set<Integer> poseGroupIdSet) {
        return baseMapper.findCount(status, poseGroupIdSet);
    }

    @Override
    public List<ProjYogaPoseWorkoutListVO> findByPoseGroupId(Integer poseGroupId) {
        return baseMapper.findByPoseGroupId(poseGroupId);
    }

    @Override
    public boolean autoGenerateWorkout(Integer projId) {

        List<ProjYogaPoseVideo> poseVideoList = yogaPoseVideoService.listAllEnable(Collections.emptyList());
        if (CollectionUtils.isEmpty(poseVideoList)) {
            log.warn("Pose video list is empty, can't generate workout.");
            return false;
        }

        // 筛选出可用于生成workout的 PoseVideo
        List<ProjYogaPoseVideo> poseVideoList4Generate = filterPoseVideo4WorkoutGenerate(poseVideoList);
        if (CollectionUtils.isEmpty(poseVideoList4Generate)) {
            log.warn("No new pose video for generate workout.");
            return false;
        }
        // 异步生成pose workout
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        // 依据video批量生成workout
        asyncGeneratePoseWorkout(poseVideoList4Generate, projId,userName);
        return true;
    }

    private void asyncGeneratePoseWorkout(List<ProjYogaPoseVideo> poseVideoList, Integer projId, String userName) {

        Executor executorService = applicationContext.getBean(OTHER_TASK_THREAD_POOL, Executor.class);
        executorService.execute(() -> projYogaPoseWorkoutFileHandler.generate(poseVideoList, projId, userName).forEach(workout -> {
            try {
                log.warn("Start to generate pose workout, pose video id is {}.", workout.getPoseVideo().getId());
                yogaPoseWorkoutService.save4AutoGeneratePoseWorkout(workout);
                log.warn("Generated pose workout, pose video id is {}.", workout.getPoseVideo().getId());
            } catch (Exception ex) {
                log.warn("Auto generate workout failed, pose video id is {}.", workout.getPoseVideo().getId());
                log.warn(ex.getMessage(), ex);
            }
        }));

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update4AutoGeneratePoseWorkout(ProjYogaPoseWorkoutBO generateBO){

        ProjYogaPoseWorkout workout = BeanUtil.toBean(generateBO, ProjYogaPoseWorkout.class);
        updatePoseWorkout(workout, generateBO.getId());
        // 关联多语言数据更新
        projYogaAutoWorkoutAudioI18nService.delete(generateBO.getId(),YogaAutoWorkoutTemplateEnum.POSE_LIBRARY);
        // 保存多语言数据
        List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = getProjYogaAutoWorkoutAudioI18ns(generateBO, workout);
        projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void save4AutoGeneratePoseWorkout(ProjYogaPoseWorkoutBO generateBO) {

        // 复制封面图片等基础字段数据、默认语言音频数据、视频数据
        ProjYogaPoseWorkout workout = BeanUtil.toBean(generateBO, ProjYogaPoseWorkout.class);
        // 由于新增数据需要判断名称重复，这里需要锁保护判断重复操作
        synchronized (INSERT_POSE_WORKOUT_LOCK) {
            if (!createPoseWorkout(workout)) {
                return;
            }
        }
        // 保存多语言数据
        List<ProjYogaAutoWorkoutAudioI18n> audioI18ns = getProjYogaAutoWorkoutAudioI18ns(generateBO, workout);
        projYogaAutoWorkoutAudioI18nService.saveBatch(audioI18ns);
    }

    private static List<ProjYogaAutoWorkoutAudioI18n> getProjYogaAutoWorkoutAudioI18ns(ProjYogaPoseWorkoutBO generateBO, ProjYogaPoseWorkout workout) {
        // 由于guidance在目前版本中不能翻译，因此，多语言情况下不能生成longJson，这里将其设置为shortJson
        return generateBO.getMultiLanguageAudioShortJson().keySet().stream().map(language ->
                new ProjYogaAutoWorkoutAudioI18n().setWorkoutId(workout.getId())
                        .setProjId(workout.getProjId())
                        .setLanguage(language)
                        .setWorkoutType(YogaAutoWorkoutTemplateEnum.POSE_LIBRARY)
                        .setAudioLongJsonUrl(generateBO.getMultiLanguageAudioLongJson().get(language))
                        .setAudioShortJsonUrl(generateBO.getMultiLanguageAudioShortJson().get(language))
                        .setVideoMaskJson(generateBO.getMultiLanguageVideoMaskJson().get(language))).collect(Collectors.toList());
    }

    private List<ProjYogaPoseVideo> filterPoseVideo4WorkoutGenerate(List<ProjYogaPoseVideo> poseVideoList) {

        List<ProjYogaPoseWorkout> poseWorkoutList = list();
        Set<Integer> existedPoseVideoIdSet = Sets.newHashSet();
        if (!CollectionUtils.isEmpty(poseWorkoutList)) {
            poseWorkoutList.stream()
                    .filter(workout -> Objects.nonNull(workout.getProjYogaPoseVideoId()))
                    .forEach(workout -> existedPoseVideoIdSet.add(workout.getProjYogaPoseVideoId()));
        }

        Predicate<ProjYogaPoseVideo> generatePredicate = poseVideo -> {

            // 不选begin的也不选right的
            if (Objects.equals(PoseTypeEnum.Begin.getPoseType(), poseVideo.getPoseType()) ||
                    Objects.equals(PoseDirectionEnum.RIGHT.getPoseDirection(), poseVideo.getPoseDirection())) {
                return false;
            }

            // left朝向的缺失right也不选择
            if (Objects.equals(PoseDirectionEnum.LEFT.getPoseDirection(), poseVideo.getPoseDirection())
                    && Objects.isNull(poseVideo.getRightVideoId())) {
                return false;
            }

            return !existedPoseVideoIdSet.contains(poseVideo.getId());
        };

        return poseVideoList.stream().filter(generatePredicate).collect(Collectors.toList());
    }

    @Override
    public void updateRes4Workout(IdListReq idListReq,Integer projId) {

        if (Objects.isNull(idListReq)) {
            return;
        }

        List<ProjYogaPoseWorkout> poseWorkoutList = listAllEnable(idListReq.getIdList());
        if (CollectionUtils.isEmpty(poseWorkoutList)) {
            return;
        }

        Map<Integer, Integer> poseVideoAndWorkoutMap = poseWorkoutList.stream().collect(Collectors.toMap(workout -> workout.getProjYogaPoseVideoId(), workout -> workout.getId(), (k1, k2) -> k1));
        List<ProjYogaPoseVideo> poseVideoList = yogaPoseVideoService.listAllEnable(poseVideoAndWorkoutMap.keySet());

        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        asyncUpdateRes4PoseWorkout(projId,poseVideoList, userName, poseVideoAndWorkoutMap);
    }

    private void asyncUpdateRes4PoseWorkout(Integer projId,List<ProjYogaPoseVideo> poseVideoList, String operationUser, Map<Integer, Integer> poseVideoAndWorkoutMap) {

        Set<Integer> videoIdSet = poseVideoList.stream().map(ProjYogaPoseVideo::getId).collect(Collectors.toSet());
        saveUpdating4UpdateRes(videoIdSet, operationUser);
        // 文件重新生成
        Executor executorService = applicationContext.getBean(OTHER_TASK_THREAD_POOL, Executor.class);
        executorService.execute(() -> projYogaPoseWorkoutFileHandler.generate(poseVideoList, projId, operationUser).forEach(workout -> {
            try {
                log.warn("Start to update res for yoga pose workout, workout id is {}.", poseVideoAndWorkoutMap.get(workout.getPoseVideo().getId()));
                workout.setId(poseVideoAndWorkoutMap.get(workout.getPoseVideo().getId()));
                yogaPoseWorkoutService.update4AutoGeneratePoseWorkout(workout);
                log.warn("Finished to update res for yoga pose workout, workout id is {}.", poseVideoAndWorkoutMap.get(workout.getPoseVideo().getId()));
            } catch (Exception ex) {
                saveFailed4UpdateRes(poseVideoAndWorkoutMap.get(workout.getPoseVideo().getId()));
                log.warn("Update pose workout failed, pose workout id is {}.", poseVideoAndWorkoutMap.get(workout.getPoseVideo().getId()));
                log.warn(ex.getMessage(), ex);
            }
        }));
    }

    private void saveUpdating4UpdateRes(Set<Integer> videoIdSet, String operationUser) {

        LambdaUpdateWrapper<ProjYogaPoseWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjYogaPoseWorkout::getUpdateStatus, ResUpdateStatusEnum.UPDATE.getStatus());
        updateWrapper.set(ProjYogaPoseWorkout::getUpdateUser, operationUser);
        updateWrapper.set(ProjYogaPoseWorkout::getUpdateTime, LocalDateTime.now());
        updateWrapper.in(ProjYogaPoseWorkout::getProjYogaPoseVideoId, videoIdSet);
        update(updateWrapper);
    }

    private void saveFailed4UpdateRes(Integer workoutId) {

        LambdaUpdateWrapper<ProjYogaPoseWorkout> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(ProjYogaPoseWorkout::getUpdateStatus, ResUpdateStatusEnum.FAIL.getStatus());
        updateWrapper.set(ProjYogaPoseWorkout::getUpdateTime, LocalDateTime.now());
        updateWrapper.eq(ProjYogaPoseWorkout::getId, workoutId);
        update(updateWrapper);
    }

    @Override
    public List<ProjYogaPoseWorkout> listAllEnable(Collection<Integer> ids) {

        LambdaQueryWrapper<ProjYogaPoseWorkout> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ProjYogaPoseWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(!CollectionUtils.isEmpty(ids), ProjYogaPoseWorkout::getId, ids);
        return list(queryWrapper);
    }

    @Override
    public List<ProjYogaPoseWorkoutDownloadVO> workoutDownloadList() {
        List<ProjYogaPoseWorkout> workoutList = list();
        if (CollUtil.isEmpty(workoutList)) {
            return null;
        }
        List<ProjYogaPoseWorkoutDownloadVO> workoutDownloadList = new ArrayList<>(workoutList.size());
        for (ProjYogaPoseWorkout workout : workoutList) {
            ProjYogaPoseWorkoutDownloadVO workoutDownloadVO = new ProjYogaPoseWorkoutDownloadVO();
            BeanUtils.copyProperties(workout, workoutDownloadVO);
            workoutDownloadList.add(workoutDownloadVO);
        }
        return workoutDownloadList;
    }

    @Transactional
    @Override
    public List<String> importByExcel(InputStream excelInputStream) {
        String colon = ":";
        String noDataError = " no data to update";
        String nameExistsError = " name already exists";
        String eventNameExistsError = " event already exists";
        List<ProjYogaPoseWorkoutImportReq> workoutListReq = CollUtil.newArrayList();
        EasyExcel.read(excelInputStream, ProjYogaPoseWorkoutImportReq.class, new AnalysisEventListener<ProjYogaPoseWorkoutImportReq>() {
            @Override
            public void invoke(ProjYogaPoseWorkoutImportReq row, AnalysisContext analysisContext) {
                workoutListReq.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        if (CollUtil.isEmpty(workoutListReq)) {
            return null;
        }
        Set<String> nameSet = workoutListReq.stream().map(ProjYogaPoseWorkoutImportReq::getName).collect(Collectors.toSet());
        if (nameSet.size() < workoutListReq.size()) {
            throw new BizException("yoga pose workout import excel has repeat name, do not update any data");
        }

        List<String> failMessage = CollUtil.newArrayList();
        List<ProjYogaPoseWorkout> workoutList = list();
        if (CollUtil.isEmpty(workoutList)) {
            throw new BizException("no data to update");
        }
        Map<Integer, ProjYogaPoseWorkout> workoutIdMap = workoutList.stream().collect(
                Collectors.toMap(ProjYogaPoseWorkout::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        Map<String, ProjYogaPoseWorkout> workoutNameMap = workoutList.stream().collect(
                Collectors.toMap(ProjYogaPoseWorkout::getName,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        Map<String, ProjYogaPoseWorkout> workoutEventNameMap = workoutList.stream().collect(
                Collectors.toMap(ProjYogaPoseWorkout::getEventName,
                        Function.identity(),
                        (existing, replacement) -> replacement)
        );
        List<ProjYogaPoseWorkout> updateWorkoutList = new ArrayList<>(workoutListReq.size());
        for (ProjYogaPoseWorkoutImportReq workoutReq : workoutListReq) {
            Integer workoutReqId = workoutReq.getId();
            Set<ConstraintViolation<ProjYogaPoseWorkoutImportReq>> violationSet = validator.validate(workoutReq, Group.class);
            if (CollUtil.isNotEmpty(violationSet)) {
                violationSet.stream().findFirst().ifPresent(violation -> failMessage.add(workoutReqId + colon + violation.getMessage()));
                continue;
            }
            String name = workoutReq.getName();
            if (!workoutIdMap.containsKey(workoutReqId)) {
                failMessage.add(workoutReqId + colon + noDataError);
                continue;
            }
            ProjYogaPoseWorkout poseWorkout = workoutNameMap.get(name);
            if (null != poseWorkout && !poseWorkout.getId().equals(workoutReqId)) {
                failMessage.add(workoutReqId + colon + name + nameExistsError);
                continue;
            }
            poseWorkout = workoutEventNameMap.get(name);
            if (null != poseWorkout && !poseWorkout.getId().equals(workoutReqId)) {
                failMessage.add(workoutReqId + colon + name + eventNameExistsError);
                continue;
            }
            ProjYogaPoseWorkout workout = new ProjYogaPoseWorkout();
            BeanUtils.copyProperties(workoutReq, workout);
            ProjYogaPoseWorkout currentWorkout = workoutIdMap.get(workoutReqId);
            Integer status = currentWorkout.getStatus();
            if (null == status || status.equals(GlobalConstant.STATUS_NOT_READY)) {
                workout.setStatus(GlobalConstant.STATUS_DRAFT);
            }
            workout.setEventName(name);
            updateWorkoutList.add(workout);
        }
        if (CollUtil.isNotEmpty(updateWorkoutList)) {
            updateBatchById(updateWorkoutList);
            lmsI18nService.handleI18n(updateWorkoutList, RequestContextUtils.getProjectId());
        }
        return failMessage;
    }

    private void updatePoseWorkout(ProjYogaPoseWorkout poseWorkout, Integer poseWorkoutId) {

        LambdaUpdateWrapper<ProjYogaPoseWorkout> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjYogaPoseWorkout::getId, poseWorkoutId);
        updateWrapper.set(ProjYogaPoseWorkout::getCalorie, poseWorkout.getCalorie());
        updateWrapper.set(ProjYogaPoseWorkout::getDuration, poseWorkout.getDuration());

        updateWrapper.set(ProjYogaPoseWorkout::getAudioLongJson, poseWorkout.getAudioLongJson());
        updateWrapper.set(ProjYogaPoseWorkout::getAudioShortJson, poseWorkout.getAudioShortJson());
        updateWrapper.set(ProjYogaPoseWorkout::getVideoMaskJson, poseWorkout.getVideoMaskJson());

        updateWrapper.set(ProjYogaPoseWorkout::getVideo2532Url, poseWorkout.getVideo2532Url());
        updateWrapper.set(ProjYogaPoseWorkout::getVideoM3u8Url, poseWorkout.getVideoM3u8Url());
        updateWrapper.set(ProjYogaPoseWorkout::getUpdateStatus, ResUpdateStatusEnum.SUCCESS.getStatus());
        updateWrapper.set(ProjYogaPoseWorkout::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    private boolean createPoseWorkout(ProjYogaPoseWorkout poseWorkout) {

        ProjYogaPoseWorkout queryPoseWorkout = poseWorkoutMapper.getByName(poseWorkout.getName());
        if (Objects.nonNull(queryPoseWorkout)) {
            log.warn("Pose workout already existed, name is {}.", poseWorkout.getName());
            return false;
        }
        return this.save(poseWorkout);
    }

    private void checkWorkout(ProjYogaPoseWorkoutAddReq projYogaPoseWorkoutAddReq, Integer id) {
        LambdaQueryWrapper<ProjYogaPoseWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseWorkout::getName, projYogaPoseWorkoutAddReq.getName())
                .ne(null != id, BaseModel::getId, id);
        if (baseMapper.selectCount(wrapper) > 0) {
            throw new BizException("name already exists");
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseWorkout::getEventName, projYogaPoseWorkoutAddReq.getEventName())
                .ne(null != id, BaseModel::getId, id);
        if (baseMapper.selectCount(wrapper) > 0) {
            throw new BizException("eventName already exists");
        }
    }
}
