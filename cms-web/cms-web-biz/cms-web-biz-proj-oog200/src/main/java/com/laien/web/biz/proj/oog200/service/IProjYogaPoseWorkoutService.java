package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.bo.CountBO;
import com.laien.web.biz.proj.oog200.bo.ProjYogaPoseWorkoutBO;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseVideo;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPoseWorkout;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaPoseWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutDownloadVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaPoseWorkoutListVO;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj yoga pose workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface IProjYogaPoseWorkoutService extends IService<ProjYogaPoseWorkout> {


    void saveBatch(List<ProjYogaPoseWorkoutAddReq> yogaPoseWorkoutReqList, Integer projId);

    void update(ProjYogaPoseWorkoutUpdateReq yogaPoseWorkoutReq, Integer projId);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    PageRes<ProjYogaPoseWorkoutListVO> page(ProjYogaPoseWorkoutPageReq pageReq, Integer projId);

    ProjYogaPoseWorkoutDetailVO findDetailById(Integer id);

    void deleteByIdList(List<Integer> idList);

    List<CountBO> findCount(Integer status, Set<Integer> poseGroupIdSet);

    List<ProjYogaPoseWorkoutListVO> findByPoseGroupId(Integer poseGroupId);

    /**
     * 异步生成pose workout
     *
     */
    boolean autoGenerateWorkout(Integer projId);

    void save4AutoGeneratePoseWorkout(ProjYogaPoseWorkoutBO generateBO);

    void update4AutoGeneratePoseWorkout(ProjYogaPoseWorkoutBO generateBO);

    /**
     * 重新生成workout的音视频资源
     *
     * @param idListReq
     */
    void updateRes4Workout(IdListReq idListReq,Integer projId);

    List<ProjYogaPoseWorkout> listAllEnable(Collection<Integer> ids);

    List<ProjYogaPoseWorkoutDownloadVO> workoutDownloadList();

    /**
     * yoga pose workout excel批量导入，并返回未导入成功的原因
     *
     */
    List<String> importByExcel(InputStream excelInputStream);
}
