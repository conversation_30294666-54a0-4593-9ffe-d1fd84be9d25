package com.laien.web.biz.proj.oog200.bo;

import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTask;
import com.laien.web.biz.proj.oog200.entity.ProjYogaAutoWorkoutTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Data
@Accessors(chain = true)
public class ProjWallPilatesAutoWorkoutContextBO {

    private ProjYogaAutoWorkoutTemplate template;
    private ProjYogaAutoWorkoutTask projYogaAutoWorkoutTask;
    private List<ProjWallPilatesVideoBO> allVideo;
    private Map<String, List<ProjWallPilatesVideoBO>> videoDirectionMap;
    private Map<String, List<ProjWallPilatesVideoBO>> videoPositionMap;
    private Map<String, List<ProjWallPilatesVideoBO>> videoTypeMap;
    private Map<String, List<ProjWallPilatesVideoBO>> videoTargetMap;

}
