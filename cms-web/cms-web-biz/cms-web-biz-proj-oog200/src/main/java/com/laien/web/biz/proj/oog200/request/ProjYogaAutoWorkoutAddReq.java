package com.laien.web.biz.proj.oog200.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog200.enums.GoalEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value = "ProjYogaAutoWorkoutUpdateReq", description = "ProjYogaAutoWorkoutUpdateReq")
public class ProjYogaAutoWorkoutAddReq {

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "video id列表")
    private List<Integer> videoIdList;

    @JsonIgnore
    @ApiModelProperty(value = "Update User")
    private String updateUser;

    @ApiModelProperty(value = "goal")
    private GoalEnum goal;

    @ApiModelProperty(value = "模版id")
    private Integer projYogaAutoTemplateId;

}
