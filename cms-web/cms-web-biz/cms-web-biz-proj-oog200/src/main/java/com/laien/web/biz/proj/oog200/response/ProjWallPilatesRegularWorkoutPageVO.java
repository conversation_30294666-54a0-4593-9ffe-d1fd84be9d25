package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * note: WallPilatesRegularWorkout分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "WallPilatesRegularWorkout分页", description = "WallPilatesRegularWorkout分页")
public class ProjWallPilatesRegularWorkoutPageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    @JsonIgnore
    private Integer updateStatus;

    @ApiModelProperty(value = "资源更新状态，这里只供前端显示 Succeeded、Updating、Failed")
    private String displayUpdateStatus;

    @ApiModelProperty(value = "选中的category")
    private List<ProjYogaRegularCategoryVO> categoryList;

    @ApiModelProperty(value = "yogaDataSourceList")
    private List<YogaDataSourceEnum> yogaDataSourceList;

    @ApiModelProperty(value = "资源类型：0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog")
    private YogaAutoWorkoutTemplateEnum videoType;

}
