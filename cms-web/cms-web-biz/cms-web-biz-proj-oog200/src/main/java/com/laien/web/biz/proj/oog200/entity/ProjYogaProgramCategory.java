package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.web.biz.proj.oog200.handler.YogaProgramTypeHandler;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaProgramCategory对象", description="proj yoga program category")
public class ProjYogaProgramCategory extends BaseModel {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "课程类型")
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = YogaProgramTypeHandler.class)
    private List<YogaProgramTypeEnum> programType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
