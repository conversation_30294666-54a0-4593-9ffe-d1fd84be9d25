package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaRegularWorkout;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutPageReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/11/4 17:09
 */
public interface ProjChairYogaRegularWorkoutMapper extends BaseMapper<ProjChairYogaRegularWorkout> {

    List<Integer> page(@Param("page") Page<ProjChairYogaRegularWorkout> page,
                       @Param("pageReq") ProjYogaRegularWorkoutPageReq pageReq,
                       @Param("projId") Integer projId,
                       @Param("workoutType") YogaAutoWorkoutTemplateEnum workoutType);

}
