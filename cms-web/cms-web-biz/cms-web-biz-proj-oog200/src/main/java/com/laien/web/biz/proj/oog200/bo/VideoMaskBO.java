package com.laien.web.biz.proj.oog200.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2024/8/9 11:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoMaskBO {

    @ApiModelProperty(value = "video mask playtime")
    private BigDecimal time;

    @ApiModelProperty(value = "video mask duration")
    private BigDecimal duration;

    @ApiModelProperty(value = "audio id")
    private String audioId;

    @ApiModelProperty(value = "audio url")
    @JsonProperty(value = "url")
    private String audioUrl;

    @ApiModelProperty(value = "audio name")
    private String audioName;

    @ApiModelProperty(value = "audio playtime")
    private BigDecimal audioPlaytime;
}
