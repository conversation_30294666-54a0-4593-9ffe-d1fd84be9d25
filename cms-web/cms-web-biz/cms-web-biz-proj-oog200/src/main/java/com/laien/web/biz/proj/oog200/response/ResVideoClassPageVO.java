package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.PlayTypeEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * note: video class分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video class分页", description = "video class分页")
public class ResVideoClassPageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "image png")
    private String imagePng;

    @ApiModelProperty(value = "image gif")
    private String imageGif;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "video class类型")
    private YogaAutoWorkoutTemplateEnum type;

    @ApiModelProperty(value = "视频宽高比")
    private PlayTypeEnum playType;

    @ApiModelProperty(value = "切片任务状态")
    private TaskResourceSectionStatusEnums taskStatus;

//    @ApiModelProperty(value = "m3u8转换状态")
//    private Integer convertStatus;
}
