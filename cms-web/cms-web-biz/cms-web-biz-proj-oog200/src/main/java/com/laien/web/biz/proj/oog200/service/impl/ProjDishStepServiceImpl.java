package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.entity.ProjDishStep;
import com.laien.web.biz.proj.oog200.entity.ProjDishStepTip;
import com.laien.web.biz.proj.oog200.mapper.ProjDishStepMapper;
import com.laien.web.biz.proj.oog200.request.ProjDishStepReq;
import com.laien.web.biz.proj.oog200.request.ProjDishStepTipReq;
import com.laien.web.biz.proj.oog200.response.ProjDishStepTipVO;
import com.laien.web.biz.proj.oog200.response.ProjDishStepVO;
import com.laien.web.biz.proj.oog200.service.IProjDishStepService;
import com.laien.web.biz.proj.oog200.service.IProjDishStepTipService;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Dish step 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Service
public class ProjDishStepServiceImpl extends ServiceImpl<ProjDishStepMapper, ProjDishStep> implements IProjDishStepService {

    @Resource
    private IProjDishStepTipService projDishStepTipService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(Integer dishId, List<ProjDishStepReq> dishStepList, Integer projId) {
        deleteBatch(Collections.singletonList(dishId));
        if (CollUtil.isEmpty(dishStepList)) {
            return;
        }
        Map<ProjDishStep, ProjDishStepReq> stepMap = new LinkedHashMap<>();
        for (ProjDishStepReq stepReq : dishStepList) {
            ProjDishStep step = new ProjDishStep();
            BeanUtils.copyProperties(stepReq, step);
            step.setProjDishId(dishId)
                    .setProjId(projId);
            stepMap.put(step, stepReq);
        }
        saveBatch(stepMap.keySet());
        List<ProjDishStepTip> stepTipList = new ArrayList<>();

        for (Map.Entry<ProjDishStep, ProjDishStepReq> entry : stepMap.entrySet()) {
            ProjDishStep step = entry.getKey();
            ProjDishStepReq stepReq = entry.getValue();
            List<ProjDishStepTipReq> tipList = stepReq.getTipList();
            if (CollUtil.isEmpty(tipList)) {
                continue;
            }
            for (ProjDishStepTipReq tipReq : tipList) {
                ProjDishStepTip stepTip = new ProjDishStepTip();
                BeanUtils.copyProperties(tipReq, stepTip);
                stepTip.setProjDishId(dishId)
                        .setProjDishStepId(step.getId())
                        .setProjId(projId);
                stepTipList.add(stepTip);
            }
        }
        if (CollUtil.isNotEmpty(stepTipList)) {
            projDishStepTipService.saveBatch(stepTipList);
            projLmsI18nService.handleI18n(stepTipList, projId);
        }

        List<ProjDishStep> dishSteps = stepMap.keySet().stream().collect(Collectors.toList());
        projLmsI18nService.handleI18n(dishSteps, projId);
    }

    @Override
    public List<ProjDishStepVO> query(Integer dishId) {
        LambdaQueryWrapper<ProjDishStep> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjDishStep::getProjDishId, dishId).orderByAsc(BaseModel::getId);
        List<ProjDishStep> stepList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(stepList)) {
            return new ArrayList<>();
        }
        List<ProjDishStepVO> stepVOList = new ArrayList<>(stepList.size());
        Map<Integer, List<ProjDishStepTipVO>> tipMap = projDishStepTipService.query(dishId).stream().collect(Collectors.groupingBy(ProjDishStepTipVO::getProjDishStepId));
        for (ProjDishStep step : stepList) {
            ProjDishStepVO stepVO = new ProjDishStepVO();
            BeanUtils.copyProperties(step, stepVO);
            List<ProjDishStepTipVO> tipList = tipMap.getOrDefault(step.getId(), new ArrayList<>());
            stepVO.setTipList(tipList);
            stepVOList.add(stepVO);
        }
        return stepVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBatch(Collection<Integer> dishIdCollection) {
        if(CollUtil.isEmpty(dishIdCollection)){
            return;
        }
        LambdaUpdateWrapper<ProjDishStep> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjDishStep::getProjDishId, dishIdCollection);
        baseMapper.delete(wrapper);
        projDishStepTipService.delete(dishIdCollection);
    }
}
