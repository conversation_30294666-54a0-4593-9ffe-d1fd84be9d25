package com.laien.web.biz.proj.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog200.entity.ProjYogaPlaylistRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjPlaylistRelationMapper;
import com.laien.web.biz.proj.oog200.response.ProjYogaPlaylistRelationVO;
import com.laien.web.biz.proj.oog200.service.IProjYogaPlaylistRelationService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/20 11:11
 */
@Service
public class ProjYogaPlaylistRelationServiceImpl extends ServiceImpl<ProjPlaylistRelationMapper, ProjYogaPlaylistRelation> implements IProjYogaPlaylistRelationService {

    @Resource
    ProjPlaylistRelationMapper relationMapper;

    @Override
    public void deleteByPlaylistId(List<Integer> playlistIds) {

        LambdaUpdateWrapper<ProjYogaPlaylistRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjYogaPlaylistRelation::getProjYogaPlaylistId, playlistIds);
        updateWrapper.set(ProjYogaPlaylistRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjYogaPlaylistRelation::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ProjYogaPlaylistRelation::getUpdateUser, RequestContextUtils.getLoginUserName());
        update(new ProjYogaPlaylistRelation(), updateWrapper);
    }

    @Override
    public List<ProjYogaPlaylistRelationVO> listByPlaylistId(Integer playlistId) {

        List<ProjYogaPlaylistRelationVO> relationVOList = relationMapper.listByPlaylistId(playlistId);
        return relationVOList;
    }

    @Override
    public List<ProjYogaPlaylistRelation> listByPlaylistIds(List<Integer> playlistIds) {

        LambdaQueryWrapper<ProjYogaPlaylistRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaPlaylistRelation::getProjYogaPlaylistId, playlistIds);
        List<ProjYogaPlaylistRelation> relationList = list(queryWrapper);

        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }
        return relationList;
    }
}
