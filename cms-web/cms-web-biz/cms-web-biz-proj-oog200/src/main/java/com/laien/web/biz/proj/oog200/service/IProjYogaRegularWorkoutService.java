package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjYogaRegularWorkout;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjYogaRegularWorkoutPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * oog200 workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IProjYogaRegularWorkoutService extends IService<ProjYogaRegularWorkout> {

    /**
     * YogaRegularWorkout分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjYogaRegularWorkoutPageVO> selectWorkoutPage(ProjYogaRegularWorkoutPageReq pageReq);

    /**
     * YogaRegularWorkout新增
     *
     * @param yogaRegularWorkoutAddReq yogaRegularWorkoutAddReq
     */
    void saveWorkout(ProjYogaRegularWorkoutAddReq yogaRegularWorkoutAddReq);

    /**
     * YogaRegularWorkout修改
     *
     * @param yogaRegularWorkoutUpdateReq yogaRegularWorkoutUpdateReq
     * @param projId
     */
    void updateWorkout(ProjYogaRegularWorkoutUpdateReq yogaRegularWorkoutUpdateReq, Integer projId);

    /**
     * 批量修改YogaRegularWorkout
     *
     * @param batchUpdateReq
     */
    void batchUpdateWorkout(ProjYogaRegularWorkoutBatchUpdateReq batchUpdateReq);

    /**
     * YogaRegularWorkout详情
     *
     * @param id id
     * @return ProjYogaRegularWorkoutDetailVO
     */
    ProjYogaRegularWorkoutDetailVO getWorkoutDetail(Integer id);

    /**
     *  YogaRegularWorkout启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     *  YogaRegularWorkout禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     *  YogaRegularWorkout删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 计算时长
     *
     * @param idList idList
     * @return Integer
     */
    Integer getComputeDuration(List<Integer> idList);

}
