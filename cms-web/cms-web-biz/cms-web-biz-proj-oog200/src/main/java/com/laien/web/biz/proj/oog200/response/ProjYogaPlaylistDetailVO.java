package com.laien.web.biz.proj.oog200.response;

import com.laien.common.oog200.enums.PlaylistTypeEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 项目播放列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPlaylist对象", description="项目播放列表")
public class ProjYogaPlaylistDetailVO extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "playlist type")
    private PlaylistTypeEnum playlistType;

    @ApiModelProperty(value = "列表名称")
    private String playlistName;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "手机详情图")
    private String phoneDetailImgUrl;

    @ApiModelProperty(value = "平板详情图")
    private String tabletDetailImgUrl;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "排序")
    private Integer sortNo;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "状态 1 启用 2 禁用")
    private Integer status;

    @ApiModelProperty(value = "relation")
    private List<ProjYogaPlaylistRelationVO> playlistRelationVOList;

}
