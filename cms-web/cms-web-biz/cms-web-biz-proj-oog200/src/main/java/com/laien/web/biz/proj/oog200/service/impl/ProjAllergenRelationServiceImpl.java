package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import com.laien.web.biz.proj.oog200.entity.ProjAllergenRelation;
import com.laien.web.biz.proj.oog200.mapper.ProjAllergenRelationMapper;
import com.laien.web.biz.proj.oog200.service.IProjAllergenRelationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * allergen和业务表的关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Service
public class ProjAllergenRelationServiceImpl extends ServiceImpl<ProjAllergenRelationMapper, ProjAllergenRelation> implements IProjAllergenRelationService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<Integer> allergenIdList, Integer projId, Integer dataId, AllergenRelationBusinessEnum businessType) {
        LambdaUpdateWrapper<ProjAllergenRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjAllergenRelation::getDataId, dataId)
                .eq(ProjAllergenRelation::getBusinessType, businessType)
                .eq(ProjAllergenRelation::getProjId, projId);
        baseMapper.delete(wrapper);
        if(CollUtil.isEmpty(allergenIdList)){
            return;
        }
        List<ProjAllergenRelation> relationList = new ArrayList<>();
        for (Integer allergenId : allergenIdList) {
            ProjAllergenRelation relation = new ProjAllergenRelation();
            relation.setDataId(dataId)
                    .setProjAllergenId(allergenId)
                    .setBusinessType(businessType)
                    .setProjId(projId);
            relationList.add(relation);
        }
        saveBatch(relationList);

    }

    @Override
    public List<ProjAllergenRelation> query(Integer dishId, AllergenRelationBusinessEnum businessType) {
        LambdaQueryWrapper<ProjAllergenRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjAllergenRelation::getDataId, dishId)
                .eq(ProjAllergenRelation::getBusinessType, businessType);
        return baseMapper.selectList(wrapper);
    }
}
