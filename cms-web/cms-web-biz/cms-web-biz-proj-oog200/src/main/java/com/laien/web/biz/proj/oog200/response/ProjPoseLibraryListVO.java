package com.laien.web.biz.proj.oog200.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "项目poseLibrary视图", description = "项目poseLibrary视图")
public class ProjPoseLibraryListVO {

    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "position")
    private String position;

    @ApiModelProperty(value = "focus")
    private String focus;
}
