package com.laien.web.biz.proj.oog200.util;//package com.laien.cms.util;
//
//import org.jgrapht.Graph;
//import org.jgrapht.GraphPath;
//import org.jgrapht.alg.shortestpath.AllDirectedPaths;
//import org.jgrapht.graph.DefaultDirectedWeightedGraph;
//import org.jgrapht.graph.DefaultEdge;
//import org.jgrapht.graph.DefaultWeightedEdge;
//import org.jgrapht.graph.SimpleDirectedGraph;
//
//import java.util.List;
//
//public class Test3 {
//
//    public static void main(String[] args) {
//        // 创建一个有向图
//        Graph<String, DefaultWeightedEdge> graph = new DefaultDirectedWeightedGraph<>(DefaultWeightedEdge.class);
//
//        // 添加图的边
//        graph.addVertex("A");
//        graph.addVertex("B");
//        graph.addVertex("C");
//        graph.addVertex("D");
//        graph.addEdge("A", "B");
//        graph.addEdge("A", "C");
//        graph.addEdge("B", "C");
//        graph.addEdge("B", "D");
//        graph.addEdge("B", "A");
//        graph.addEdge("C", "D");
//
//        graph.setEdgeWeight("A","B",2);
//
//        // 获取两个节点之间的所有路径（指定长度为3）
//        String startNode = "A";
//        String endNode = "D";
//        int pathLength = 3;
//
//        // 使用AllDirectedPaths类获取所有路径
//        AllDirectedPaths<String, DefaultWeightedEdge> allPaths = new AllDirectedPaths<>(graph);
//        List<GraphPath<String, DefaultWeightedEdge>> paths = allPaths.getAllPaths(startNode, endNode, false, 10);
//
//        // 输出所有路径
//        for (GraphPath<String, DefaultWeightedEdge> path : paths) {
//            System.out.println(path.getVertexList());
//        }
//    }
//}
