package com.laien.web.biz.proj.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaRegularWorkout;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaRegularWorkoutAddReq;
import com.laien.web.biz.proj.oog200.request.ProjChairYogaRegularWorkoutUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutBatchUpdateReq;
import com.laien.web.biz.proj.oog200.request.ProjYogaRegularWorkoutPageReq;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaRegularWorkoutDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaRegularWorkoutPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * oog200 chair yoga regular workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IProjChairYogaRegularWorkoutService extends IService<ProjChairYogaRegularWorkout> {

    /**
     * YogaRegularWorkout分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjChairYogaRegularWorkoutPageVO> selectWorkoutPage(ProjYogaRegularWorkoutPageReq pageReq);

    /**
     * YogaRegularWorkout新增
     *
     * @param yogaRegularWorkoutAddReq yogaRegularWorkoutAddReq
     */
    void saveWorkout(ProjChairYogaRegularWorkoutAddReq yogaRegularWorkoutAddReq);

    /**
     *  YogaRegularWorkout修改
     *
     * @param yogaRegularWorkoutUpdateReq yogaRegularWorkoutUpdateReq
     */
    void updateWorkout(ProjChairYogaRegularWorkoutUpdateReq yogaRegularWorkoutUpdateReq);

    /**
     * 批量修改YogaRegularWorkout
     *
     * @param batchUpdateReq
     */
    void batchUpdateWorkout(ProjYogaRegularWorkoutBatchUpdateReq batchUpdateReq);

    /**
     * YogaRegularWorkout详情
     *
     * @param id id
     * @return ProjYogaRegularWorkoutDetailVO
     */
    ProjChairYogaRegularWorkoutDetailVO getWorkoutDetail(Integer id);

    /**
     *  YogaRegularWorkout启用
     *
     * @param workoutIds idList
     */
    void updateEnableByIds(List<Integer> workoutIds);

    /**
     *  YogaRegularWorkout禁用
     *
     * @param workoutIds idList
     */
    void updateDisableByIds(List<Integer> workoutIds);

    /**
     *  YogaRegularWorkout删除
     *
     * @param workoutIds idList
     */
    void deleteByIds(List<Integer> workoutIds);
}
