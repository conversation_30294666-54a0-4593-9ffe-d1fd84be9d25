package com.laien.web.biz.proj.oog200.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * dish step
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="dish step对象", description="dish step")
public class ProjDishStepReq {

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "tip list")
    private List<ProjDishStepTipReq> tipList;

}
