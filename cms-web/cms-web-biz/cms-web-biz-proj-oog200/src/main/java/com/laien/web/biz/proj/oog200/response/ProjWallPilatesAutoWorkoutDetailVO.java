package com.laien.web.biz.proj.oog200.response;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@Accessors(chain = true)
@TableName("proj_wall_pilates_auto_workout")
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="Wall pilates auto workout")
public class ProjWallPilatesAutoWorkoutDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "projYogaAutoWorkoutTemplateId")
    private Integer projYogaAutoTemplateId;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "Standing、Lying")
    private String position;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "videoList")
    private List<ProjWallPilatesVideoListVO> videoList;

}
