package com.laien.web.biz.proj.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog200.enums.YogaProgramTypeEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:58
 */
@Data
public class ProjYogaProgramPageVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "programCategoryIdList")
    private List<Integer> programCategoryIdList = Collections.emptyList();

    @ApiModelProperty(value = "programPositionTypeList")
    private List<YogaProgramTypeEnum> programPositionTypes = Collections.emptyList();

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutNum = 0;

    @ApiModelProperty(value = "program level 数量")
    private Integer programLevelNum = 0;

    @ApiModelProperty(value = "难度")
    private DifficultyEnum difficulty;

    @ApiModelProperty(value = "持续周数")
    private Integer duration;

    @ApiModelProperty(value = "playlist id")
    private Integer playlistId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "描述")
    private String description;

}
