package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog200.entity.ProjSound;
import com.laien.web.biz.proj.oog200.mapper.ProjSoundMapper;
import com.laien.web.biz.proj.oog200.mapstruct.ProjSoundMapStruct;
import com.laien.web.biz.proj.oog200.request.ProjSoundAddReq;
import com.laien.web.biz.proj.oog200.request.ProjSoundPageReq;
import com.laien.web.biz.proj.oog200.request.ProjSoundUpdateReq;
import com.laien.web.biz.proj.oog200.response.ProjSoundDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjSoundPageVO;
import com.laien.web.biz.proj.oog200.service.IProjSoundService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSoundServiceImpl extends ServiceImpl<ProjSoundMapper, ProjSound>
        implements IProjSoundService {
    private final ProjSoundMapStruct mapStruct;
    private final ICoreVoiceConfigI18nService i18nConfigService;
    private final ICoreTextTaskI18nService i18nService;
    private final IProjInfoService projInfoService;

    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    @Override
    public PageRes<ProjSoundPageVO> selectSoundPage(ProjSoundPageReq req) {
        LambdaQueryWrapper<ProjSound> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(req.getId()), ProjSound::getId, req.getId())
                .like(StrUtil.isNotBlank(req.getSoundName()), ProjSound::getSoundName, req.getSoundName())
                .eq(ObjUtil.isNotNull(req.getStatus()), ProjSound::getStatus, req.getStatus())
                .eq(ObjUtil.isNotNull(req.getSoundType()), ProjSound::getSoundType, req.getSoundType())
                .eq(ObjUtil.isNotNull(req.getSoundSubType()), ProjSound::getSoundSubType, req.getSoundSubType())
                .eq(ObjUtil.isNotNull(req.getGender()), ProjSound::getGender, req.getGender())
                .in(CollUtil.isNotEmpty(req.getIds()), ProjSound::getId, req.getIds());
        query.orderByDesc(ProjSound::getId);
        IPage<ProjSound> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), query);
        List<ProjSoundPageVO> list = mapStruct.toPageList(page.getRecords());
        fillI18nConfigInfo(page.getRecords(),list);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    private void fillI18nConfigInfo(List<ProjSound> records, List<ProjSoundPageVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjSoundPageVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private void fillI18nConfigNameBySound(ProjSoundDetailVO detailVO) {
        Set<Integer> configIds = new HashSet<>();
        configIds.add(detailVO.getCoreVoiceConfigI18nId());
        Map<Integer, CoreVoiceConfigI18n> configMap = i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));

        CoreVoiceConfigI18n config = configMap.get(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }


    private Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ProjSound> records) {
        Set<Integer> configIds = records.stream().map(ProjSound::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }

    /**
     * 添加 sound
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveSound(ProjSoundAddReq req) {
        check(null, req);
        ProjSound projSound = mapStruct.toEntity(req);
        projSound.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(projSound);
        if (projSound.getNeedTranslation()) {
            handleI18n(ListUtil.of(projSound), projInfoService.getById(req.getProjId()));
        }
    }
    /**
     * 修改 sound
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSound(ProjSoundUpdateReq req) {
        check(req.getId(), req);
        ProjSound projSound = mapStruct.toEntity(req);
        LambdaUpdateWrapper<ProjSound> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjSound::getId, projSound.getId())
                .set(ProjSound::getCoreVoiceConfigI18nId, projSound.getCoreVoiceConfigI18nId())
                .set(ProjSound::getProjId, projSound.getProjId())
                .set(ProjSound::getSoundName, projSound.getSoundName())
                .set(ProjSound::getSoundType, projSound.getSoundType())
                .set(ProjSound::getSoundSubType, projSound.getSoundSubType())
                .set(ProjSound::getSoundScript, projSound.getSoundScript())
                .set(ProjSound::getUrl, projSound.getUrl())
                .set(ProjSound::getDuration, projSound.getDuration())
                .set(ProjSound::getGender, projSound.getGender())
                .set(ProjSound::getNeedTranslation, projSound.getNeedTranslation());
        this.update(new ProjSound(),updateWrapper);
        if (projSound.getNeedTranslation()) {
            handleI18n(ListUtil.of(projSound), projInfoService.getById(req.getProjId()));
        }
    }

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    @Override
    public ProjSoundDetailVO getDetail(Integer id) {
        ProjSound sound = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(sound),"Data not found");
        ProjSoundDetailVO detailVO = mapStruct.toDetailVO(sound);
        fillI18nConfigNameBySound(detailVO);
        return detailVO;
    }
    /**
     * 批量启用
     * @param idList
     */
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSound::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSound::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjSound::getId, idList);
       this.update(new ProjSound(), wrapper);
    }

    /**
     * 批量禁用
     * @param idList
     */
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSound::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjSound::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSound::getId, idList);
        this.update(new ProjSound(), wrapper);
    }

    private void check(Integer id, ProjSoundAddReq req) {
        LambdaQueryWrapper<ProjSound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSound::getSoundName, req.getSoundName())
                .eq(ProjSound::getProjId, req.getProjId())
                .ne(null != id, BaseModel::getId, id);
        List<ProjSound> soundList = baseMapper.selectList(wrapper);
        Set<String> nameSet = soundList.stream().map(ProjSound::getSoundName).collect(Collectors.toSet());
        if (nameSet.contains(req.getSoundName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
    }

    @Override
    public ProjSound getBySoundName(String soundName) {

        LambdaQueryWrapper<ProjSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSound::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ProjSound::getSoundName, soundName).last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<ProjSound> listBySoundNames(Collection<String> soundNames) {

        LambdaQueryWrapper<ProjSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjSound::getSoundName, soundNames);
        queryWrapper.eq(ProjSound::getStatus, GlobalConstant.STATUS_ENABLE);
        return list(queryWrapper);
    }

    private void handleI18n(List<? extends CoreI18nModel> i18nList, ProjInfo projInfo) {
        CreateTaskDTO createTaskDTO = new CreateTaskDTO(i18nList, projInfo.getTextLanguages(), projInfo.getLanguages(), projInfo.getAppCode());
        try {
            i18nService.batchSaveOrUpdate(createTaskDTO);
        } catch (Exception e) {
            log.error("handleI18n failed, createTaskDTO:{}", createTaskDTO, e);
            throw new BizException("handleI18n failed");
        }
    }


}
