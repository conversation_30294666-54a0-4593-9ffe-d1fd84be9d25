package com.laien.web.biz.proj.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.common.oog200.enums.PoseDirectionEnum;
import com.laien.common.oog200.enums.PositionEnum;
import com.laien.common.oog200.enums.TargetEnum;
import com.laien.common.oog200.enums.TypeEnum;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog200.bo.Oog200VideoBO;
import com.laien.web.biz.proj.oog200.config.Oog200BizConfig;
import com.laien.web.biz.proj.oog200.constant.PoseWorkoutConstant;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideo;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaVideoSlice;
import com.laien.web.biz.proj.oog200.mapper.ProjChairYogaVideoMapper;
import com.laien.web.biz.proj.oog200.request.*;
import com.laien.web.biz.proj.oog200.response.ProjBaseDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoDetailVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoSliceDetailVO;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaVideoService;
import com.laien.web.biz.proj.oog200.service.IProjChairYogaVideoSliceService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_CHAIR_YOGA_VIDEO_FRONT;
import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_CHAIR_YOGA_VIDEO_SIDE;

/**
 * Author:  hhl
 * Date:  2024/9/23 17:29
 */
@Slf4j
@Service
public class ProjChairYogaVideoServiceImpl extends ServiceImpl<ProjChairYogaVideoMapper, ProjChairYogaVideo> implements IProjChairYogaVideoService {

    @Resource
    ITaskResourceSectionService taskResourceSectionService;

    @Resource
    IProjChairYogaVideoSliceService chairYogaVideoSliceService;

    @Resource
    ProjChairYogaVideoMapper chairYogaVideoMapper;

    @Resource
    private Validator validator;

    @Resource
    private Oog200BizConfig oog200BizConfig;

    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Resource
    private ICoreVoiceConfigI18nService i18nConfigService;

    private static final String VIDEO_IS_NULL = "Chair yoga video can't be null.";
    private static final String VIDEO_NOT_FIND = "Chair Yoga Video can't find.";
    private static final String NAME_EXISTED = "Video name is existed.";
    private static final String VIDEO_IMPORT_ERROR = "Import chair yoga video error.";
    private static final String VIDEO_IMPORT_ERROR_FORMAT = "Import chair yoga video error, message : %s.";

    private static final String NAME_EXISTED_ERROR_FORMAT = "Video Name already existed, Video Name is %s.";
    private static final String DIRECTION_ERROR_FORMAT = "Direction value is invalid, please check it. invalid value is %s.";
    private static final String TARGET_ERROR_FORMAT = "Target value is invalid, please check it. invalid value is %s.";
    private static final String TYPE_ERROR_FORMAT = "Type value is invalid, please check it. invalid value is %s.";
    private static final String POSITION_ERROR_FORMAT = "Position value is invalid, please check it. invalid value is %s.";
    private static final String NAME_REPEATED_ERROR_FORMAT = "Ignored Repeated Video Name, repeated value is %s.";

    @Override
    public PageRes<ProjChairYogaVideoPageVO> selectChairYogaVideoPage(ProjChairYogaVideoPageReq pageReq) {

        LambdaQueryWrapper<ProjChairYogaVideo> queryWrapper = new LambdaQueryWrapper<>();
        filterByName(pageReq);
        wrapQuery(queryWrapper, pageReq);
        Page<ProjChairYogaVideo> poseVideoPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjChairYogaVideo> iPage = this.page(poseVideoPage, queryWrapper);

        PageRes<ProjChairYogaVideoPageVO> pageRes = PageConverter.convert(iPage, ProjChairYogaVideoPageVO.class);
        injectVideoDuration(pageRes.getList());
        injectionTaskStatus(pageRes.getList());
        return pageRes;
    }

    private void filterByName(ProjChairYogaVideoPageReq pageReq) {

        Oog200VideoBO oog200Video = oog200BizConfig.getOog200Video();
        List<String> videoNameList = Lists.newArrayList(oog200Video.getChairYogaVideo4Seated(), oog200Video.getChairYogaVideo4Standing());
        pageReq.setYogaVideoNameList(videoNameList);
    }

    private void injectVideoDuration(List<ProjChairYogaVideoPageVO> chairYogaVideoList) {

        if (CollectionUtils.isEmpty(chairYogaVideoList)) {
            return;
        }

        List<Integer> videoIds = chairYogaVideoList.stream().map(ProjChairYogaVideoPageVO::getId).collect(Collectors.toList());
        Map<Integer, Integer> frontVideoDurationMap = Maps.newHashMap();
        Map<Integer, Integer> sideVideoDurationMap = Maps.newHashMap();
        chairYogaVideoSliceService.videoDurationCount(videoIds, frontVideoDurationMap, sideVideoDurationMap);

        Integer easyStartDuration = getEasyStartDuration();
        chairYogaVideoList.forEach(video -> video.setDuration(easyStartDuration + frontVideoDurationMap.getOrDefault(video.getId(), 0)));
    }

    private Integer getEasyStartDuration() {

        Integer defaultValue = Integer.valueOf(GlobalConstant.ZERO);
        try {
            Oog200VideoBO oog200Video = oog200BizConfig.getOog200Video();
            List<String> videoNameList = Lists.newArrayList(oog200Video.getChairYogaVideo4Seated(), oog200Video.getChairYogaVideo4Standing());
            List<ProjChairYogaVideo> yogaVideoList = listByName(videoNameList);
            if (CollectionUtils.isEmpty(yogaVideoList)) {
                return defaultValue;
            }

            Map<String, Integer> videoNameAndIdMap = yogaVideoList.stream().collect(Collectors.toMap(ProjChairYogaVideo::getName, ProjChairYogaVideo::getId, (k1, k2) -> k1));
            List<ProjChairYogaVideoSliceDetailVO> sliceDetailVOList = chairYogaVideoSliceService.listByChairYogaVideoId(videoNameAndIdMap.values());
            if (CollectionUtils.isEmpty(sliceDetailVOList)) {
                return defaultValue;
            }

            Integer video4Seated = videoNameAndIdMap.get(oog200Video.getChairYogaVideo4Seated());
            if (Objects.nonNull(video4Seated)) {
                Optional<ProjChairYogaVideoSliceDetailVO> sliceDetailVO = sliceDetailVOList.stream().filter(slice -> Objects.equals(slice.getProjChairYogaVideoId(), video4Seated)).findAny();
                if (sliceDetailVO.isPresent()) {
                    return sliceDetailVO.get().getFrontVideoDuration();
                }
            }

            return defaultValue;
        } catch (Exception ex) {
            log.warn(ex.getMessage(), ex);
            return defaultValue;
        }
    }

    private void wrapQuery(LambdaQueryWrapper<ProjChairYogaVideo> queryWrapper, ProjChairYogaVideoPageReq pageReq) {

        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjChairYogaVideo::getStatus, pageReq.getStatus());
        queryWrapper.eq(!StringUtils.isEmpty(pageReq.getDirection()), ProjChairYogaVideo::getDirection, pageReq.getDirection());

        queryWrapper.eq(!StringUtils.isEmpty(pageReq.getPosition()), ProjChairYogaVideo::getPosition, pageReq.getPosition());
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getType()), ProjChairYogaVideo::getType, pageReq.getType());
        String target = pageReq.getTarget();
        queryWrapper.like(!StringUtils.isEmpty(target), ProjChairYogaVideo::getTarget, target);

        queryWrapper.notIn(!CollectionUtils.isEmpty(pageReq.getYogaVideoNameList()), ProjChairYogaVideo::getName, pageReq.getYogaVideoNameList());
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjChairYogaVideo::getName, pageReq.getName());
        if (StringUtils.isNotBlank(target)) {
            queryWrapper.last("order by LENGTH(target) asc, id desc");
        } else {
            queryWrapper.orderByDesc(BaseModel::getId);
        }
    }

    private void injectionTaskStatus(List<ProjChairYogaVideoPageVO> videoList) {

        if (CollectionUtils.isEmpty(videoList)) {
            return;
        }

        Set<Integer> videoIds = videoList.stream().map(ProjChairYogaVideoPageVO::getId).collect(Collectors.toSet());
        List<ProjChairYogaVideoSliceDetailVO> sliceDetailVOList = chairYogaVideoSliceService.listByChairYogaVideoId(videoIds);
        Set<Integer> videoSliceIds = sliceDetailVOList.stream().map(ProjChairYogaVideoSliceDetailVO::getId).collect(Collectors.toSet());

        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(PROJ_CHAIR_YOGA_VIDEO_SIDE.getTableName(), PROJ_CHAIR_YOGA_VIDEO_SIDE.getEntityFieldName(), videoSliceIds);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(PROJ_CHAIR_YOGA_VIDEO_FRONT.getTableName(), PROJ_CHAIR_YOGA_VIDEO_FRONT.getEntityFieldName(), videoSliceIds);
        if (CollectionUtils.isEmpty(sideStatusList) || CollectionUtils.isEmpty(frontStatusList)) {
            return;
        }

        Map<Integer, TaskResourceSectionStatusEnums> sideIdStatusMap = sideStatusList.stream()
                .collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));
        Map<Integer, TaskResourceSectionStatusEnums> frontIdStatusMap = frontStatusList.stream()
                .collect(Collectors.toMap(TaskResourceSection::getTableId, TaskResourceSection::getStatus));

        Map<Integer, List<ProjChairYogaVideoSliceDetailVO>> chairYogaAndSliceMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId));
        videoList.stream().filter(video -> chairYogaAndSliceMap.containsKey(video.getId())).forEach(video -> {

            Map<Integer, TaskResourceSectionStatusEnums> frontMap = new LinkedHashMap<>();
            video.setFrontTaskStatusMap(frontMap);
            Map<Integer, TaskResourceSectionStatusEnums> sideMap = new LinkedHashMap<>();
            video.setSideTaskStatusMap(sideMap);

            chairYogaAndSliceMap.get(video.getId()).stream().sorted(Comparator.comparing(ProjChairYogaVideoSliceDetailVO::getSliceIndex)).forEach(slice -> {
                sideMap.put(slice.getSliceIndex(), sideIdStatusMap.get(slice.getId()));
                frontMap.put(slice.getSliceIndex(), frontIdStatusMap.get(slice.getId()));
            });
        });
    }

    @Override
    public List<ProjChairYogaVideo> listByName(Collection<String> videoNameList) {

        if (CollectionUtils.isEmpty(videoNameList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjChairYogaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjChairYogaVideo::getName, videoNameList);
        queryWrapper.eq(ProjChairYogaVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveChairYogaVideo(ProjChairYogaVideoAddReq addReq) {

        bizValidate4Add(addReq);
        ProjChairYogaVideo chairYogaVideo = addChairYogaVideo(addReq);
        saveChairYogaVideoSlice(chairYogaVideo.getId(), addReq.getChairYogaVideoList());
        lmsI18nService.handleI18n(Collections.singletonList(chairYogaVideo), chairYogaVideo.getProjId());
    }

    private ProjChairYogaVideo addChairYogaVideo(ProjChairYogaVideoAddReq addReq) {

        ProjChairYogaVideo chairYogaVideo = new ProjChairYogaVideo();
        BeanUtils.copyProperties(addReq, chairYogaVideo);
        chairYogaVideo.setStatus(GlobalConstant.STATUS_DRAFT);

        chairYogaVideo.setProjId(RequestContextUtils.getProjectId());
        save(chairYogaVideo);
        return chairYogaVideo;
    }

    private void bizValidate4Add(ProjChairYogaVideoAddReq addReq) {

        if (Objects.isNull(addReq)) {
            throw new BizException(VIDEO_IS_NULL);
        }

        // pose name 唯一
        LambdaQueryWrapper<ProjChairYogaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjChairYogaVideo::getName, addReq.getName()).or().eq(ProjChairYogaVideo::getEventName, addReq.getEventName());

        ProjChairYogaVideo poseVideo = getOne(queryWrapper);
        if (Objects.nonNull(poseVideo) && Objects.equals(poseVideo.getName(), addReq.getName())) {
            throw new BizException(NAME_EXISTED);
        }

        if (Objects.nonNull(poseVideo) && Objects.equals(poseVideo.getEventName(), addReq.getEventName())) {
            throw new BizException("Video event name is existed.");
        }
    }

    private void bizValidate4Update(ProjChairYogaVideoUpdateReq updateReq) {

        if (Objects.isNull(updateReq)) {
            throw new BizException(VIDEO_IS_NULL);
        }

        // pose name 唯一
        LambdaQueryWrapper<ProjChairYogaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjChairYogaVideo::getName, updateReq.getName());
        queryWrapper.or().eq(ProjChairYogaVideo::getEventName, updateReq.getEventName());

        List<ProjChairYogaVideo> yogaVideoList = list(queryWrapper);
        if (CollectionUtils.isEmpty(yogaVideoList)) {
            return;
        }

        yogaVideoList.forEach(video -> {
            if (Objects.equals(video.getName(), updateReq.getName()) && !Objects.equals(video.getId(), updateReq.getId())) {
                throw new BizException(NAME_EXISTED);
            }
            if (Objects.equals(video.getEventName(), updateReq.getEventName()) && !Objects.equals(video.getId(), updateReq.getId())) {
                throw new BizException("Video event name is existed.");
            }
        });

    }

    private void saveChairYogaVideoSlice(Integer chairYogaVideoId, List<ProjChairYogaVideoSliceDetailVO> chairYogaVideoSlice) {

        if (CollectionUtils.isEmpty(chairYogaVideoSlice)) {
            return;
        }

        ProjChairYogaVideo chairYogaVideo = getById(chairYogaVideoId);
        if (Objects.isNull(chairYogaVideo)) {
            throw new BizException(VIDEO_NOT_FIND);
        }

        List<ProjChairYogaVideoSlice> chairYogaVideoSliceList = chairYogaVideoSlice.stream()
                .map(addSlice -> convertSliceDetail2Entity(addSlice, chairYogaVideo)).collect(Collectors.toList());
        chairYogaVideoSliceService.saveBatch(chairYogaVideoSliceList);
    }

    private ProjChairYogaVideoSlice convertSliceDetail2Entity(ProjChairYogaVideoSliceDetailVO detailVO, ProjChairYogaVideo chairYogaVideo) {

        ProjChairYogaVideoSlice yogaVideoSlice = new ProjChairYogaVideoSlice();
        yogaVideoSlice.setProjChairYogaVideoId(chairYogaVideo.getId());
        yogaVideoSlice.setProjId(chairYogaVideo.getProjId());
        yogaVideoSlice.setSliceIndex(detailVO.getSliceIndex());
        yogaVideoSlice.setFrontVideoUrl(detailVO.getFrontVideoUrl());

        yogaVideoSlice.setFrontVideoDuration(detailVO.getFrontVideoDuration());
        yogaVideoSlice.setSideVideoUrl(detailVO.getSideVideoUrl());
        yogaVideoSlice.setSideVideoDuration(detailVO.getSideVideoDuration());
        return yogaVideoSlice;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateChairYogaVideo(ProjChairYogaVideoUpdateReq updateReq) {

        bizValidate4Update(updateReq);
        updateYogaVideo(updateReq);
        updateSlice4ChairYogaVideo(updateReq);
    }

    private void updateSlice4ChairYogaVideo(ProjChairYogaVideoUpdateReq updateReq) {

        chairYogaVideoSliceService.deleteByChairYodaVideoId(updateReq.getId());

        saveChairYogaVideoSlice(updateReq.getId(), updateReq.getChairYogaVideoList());
    }

    private void updateYogaVideo(ProjChairYogaVideoUpdateReq updateReq) {

        ProjChairYogaVideo chairYogaVideo = getById(updateReq.getId());
        if (Objects.isNull(chairYogaVideo)) {
            throw new BizException(VIDEO_NOT_FIND);
        }
        BeanUtils.copyProperties(updateReq, chairYogaVideo);
        updateById(chairYogaVideo);
        lmsI18nService.handleI18n(Collections.singletonList(chairYogaVideo), chairYogaVideo.getProjId());
    }

    @Override
    public ProjChairYogaVideoDetailVO getDetailById(Integer videoId) {

        if (Objects.isNull(videoId)) {
            return null;
        }

        ProjChairYogaVideo chairYogaVideo = getById(videoId);
        if (Objects.isNull(chairYogaVideo)) {
            return null;
        }

        ProjChairYogaVideoDetailVO detailVO = convert2Detail(chairYogaVideo);
        List<ProjChairYogaVideoSliceDetailVO> sliceList = chairYogaVideoSliceService.listByChairYogaVideoId(Lists.newArrayList(videoId));
        detailVO.setChairYogaVideoList(sliceList);
        detailVO.setRightVideoDetail(getBaseDetail(chairYogaVideo.getRightVideoId()));

        fillI18nConfigInfoForDetail(detailVO);
        return detailVO;
    }

    private void fillI18nConfigInfoForDetail(ProjChairYogaVideoDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }

    private ProjBaseDetailVO getBaseDetail(Integer chairYogaVideoId) {

        ProjChairYogaVideo chairYogaVideo = getById(chairYogaVideoId);
        if (Objects.isNull(chairYogaVideo)) {
            return null;
        }

        ProjBaseDetailVO baseDetailVO = new ProjBaseDetailVO();
        BeanUtils.copyProperties(chairYogaVideo, baseDetailVO);
        return baseDetailVO;
    }

    private ProjChairYogaVideoDetailVO convert2Detail(ProjChairYogaVideo chairYogaVideo) {

        ProjChairYogaVideoDetailVO detailVO = new ProjChairYogaVideoDetailVO();
        BeanUtils.copyProperties(chairYogaVideo, detailVO);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        Collection<ProjChairYogaVideo> yogaVideoList = listByIds(idList);
        if (CollectionUtils.isEmpty(yogaVideoList)) {
            return;
        }

        List<Integer> unEnableIdList = Lists.newArrayList();
        List<Integer> enableIdList = Lists.newArrayList();
        yogaVideoList.forEach(video -> {
            if (Objects.equals(video.getDirection(), PoseDirectionEnum.LEFT.getPoseDirection()) && Objects.isNull(video.getRightVideoId())) {
                unEnableIdList.add(video.getId());
            } else {
                enableIdList.add(video.getId());
            }
        });

        if (!CollectionUtils.isEmpty(unEnableIdList)) {
            throw new BizException(String.format("Can't enable for these %s, please select right video first.", unEnableIdList.toString()));
        }

        LambdaUpdateWrapper<ProjChairYogaVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjChairYogaVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.set(ProjChairYogaVideo::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.set(ProjChairYogaVideo::getUpdateTime, LocalDateTime.now());

        wrapper.in(ProjChairYogaVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjChairYogaVideo::getId, enableIdList);
        this.update(new ProjChairYogaVideo(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjChairYogaVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjChairYogaVideo::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.set(ProjChairYogaVideo::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.set(ProjChairYogaVideo::getUpdateTime, LocalDateTime.now());

        wrapper.eq(ProjChairYogaVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjChairYogaVideo::getId, idList);
        this.update(new ProjChairYogaVideo(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjChairYogaVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjChairYogaVideo::getDelFlag, GlobalConstant.YES);
        wrapper.set(ProjChairYogaVideo::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.set(ProjChairYogaVideo::getUpdateTime, LocalDateTime.now());

        wrapper.in(ProjChairYogaVideo::getStatus, GlobalConstant.STATUS_NOT_READY, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjChairYogaVideo::getId, idList);
        this.update(new ProjChairYogaVideo(), wrapper);
    }

    @Override
    public List<String> importChairYogaVideo(MultipartFile file) {

        if (Objects.isNull(file)) {
            return Collections.emptyList();
        }

        List<ProjChairYogaVideoImportReq> importReqList = parseChairYogaVideoImport(file);
        if (CollectionUtils.isEmpty(importReqList)) {
            return Collections.emptyList();
        }

        List<String> errorMessages = Lists.newArrayList();
        List<ProjChairYogaVideoImportReq> filterImportList = filterChairYogaVideoImport(importReqList, errorMessages);
        if (CollectionUtils.isEmpty(filterImportList)) {
            return errorMessages;
        }

        List<ProjChairYogaVideoImportReq> validImportList = bizValidate4VideoImport(filterImportList, errorMessages);
        if (CollectionUtils.isEmpty(validImportList)) {
            return errorMessages;
        }

        List<ProjChairYogaVideo> yogaVideoList = validImportList.stream().map(importReq -> convertImport2Entity(importReq, RequestContextUtils.getProjectId())).collect(Collectors.toList());
        saveBatch(yogaVideoList);
        lmsI18nService.handleI18n(yogaVideoList, RequestContextUtils.getProjectId());
        return errorMessages;
    }

    private ProjChairYogaVideo convertImport2Entity(ProjChairYogaVideoImportReq importReq, Integer projId) {

        ProjChairYogaVideo yogaVideo = new ProjChairYogaVideo();
        BeanUtils.copyProperties(importReq, yogaVideo);
        yogaVideo.setEventName(importReq.getName());
        yogaVideo.setProjId(projId);
        return yogaVideo;
    }

    private List<ProjChairYogaVideoImportReq> bizValidate4VideoImport(List<ProjChairYogaVideoImportReq> filterImportList, List<String> errorMessages) {

        Set<String> videoNameSet = filterImportList.stream().map(ProjChairYogaVideoImportReq::getName).collect(Collectors.toSet());
        Map<String, ProjChairYogaVideo> existedVideoNameMap = getVideoNameMap(videoNameSet);
        return filterImportList.stream().filter(importReq -> {
            if (existedVideoNameMap.containsKey(importReq.getName())) {
                errorMessages.add(String.format(NAME_EXISTED_ERROR_FORMAT, importReq.getName()));
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    private Map<String, ProjChairYogaVideo> getVideoNameMap(Set<String> videoNameSet) {

        LambdaQueryWrapper<ProjChairYogaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjChairYogaVideo::getName, videoNameSet);
        List<ProjChairYogaVideo> videoList = list(queryWrapper);

        if (CollectionUtils.isEmpty(videoList)) {
            return Collections.emptyMap();
        }
        Map<String, ProjChairYogaVideo> videoNameMap = videoList.stream().collect(Collectors.toMap(video -> video.getName(), Function.identity(), (k1, k2) -> k1));
        return videoNameMap;
    }

    private List<ProjChairYogaVideoImportReq> filterChairYogaVideoImport(List<ProjChairYogaVideoImportReq> chairVideoImportList, List<String> errorMessageList) {

        Set<String> videoNameSet = new HashSet<>();
        Set<String> directionSet = Sets.newHashSet(PoseDirectionEnum.CENTRAL.getPoseDirection(), PoseDirectionEnum.LEFT.getPoseDirection(), PoseDirectionEnum.RIGHT.getPoseDirection());
        Set<String> typeSet = Sets.newHashSet(TypeEnum.WARM_UP.getValue(), TypeEnum.MAIN.getValue(), TypeEnum.COOL_DOWN.getValue());
        Set<String> positionSet = Sets.newHashSet(PositionEnum.SEATED.getName(), PositionEnum.STANDING.getName());
        Set<String> targetSet = Sets.newHashSet(TargetEnum.UPPER_BODY.getName(), TargetEnum.ABS_CORE.getName(), TargetEnum.LOWER_BODY.getName());

        Set<String> i18nConfigNameSet = chairVideoImportList.stream().map(ProjChairYogaVideoImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
        Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));

        return chairVideoImportList.stream().filter(poseVideo -> {

            if (!i18nConfigNameIdMap.containsKey(poseVideo.getCoreVoiceConfigI18nName())) {
                errorMessageList.add(poseVideo.getName() + ": English Voice Name Not Found in TTS config");
                return false;
            } else {
                poseVideo.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(poseVideo.getCoreVoiceConfigI18nName()));
            }

            Set<ConstraintViolation<ProjChairYogaVideoImportReq>> violationSet = validator.validate(poseVideo, Group.class);
            if (!CollectionUtils.isEmpty(violationSet)) {
                List<String> errorInfoList = violationSet.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
                String errorInfo = JacksonUtil.toJsonString(errorInfoList);
                errorMessageList.add(String.format(PoseWorkoutConstant.VIDEO_IMPORT_ERROR, poseVideo.getName(), errorInfo));
                return false;
            }

            if (!directionSet.contains(poseVideo.getDirection())) {
                errorMessageList.add(String.format(DIRECTION_ERROR_FORMAT, poseVideo.getDirection()));
                return false;
            }

            List<String> targets = Arrays.stream(MyStringUtil.getSplitWithComa(poseVideo.getTarget())).map(StringUtils::trim).collect(Collectors.toList());
            poseVideo.setTarget(CollUtil.join(targetSet, GlobalConstant.COMMA));
            for (String target : targets) {
                if (!targetSet.contains(target)) {
                    errorMessageList.add(String.format(TARGET_ERROR_FORMAT, poseVideo.getTarget()));
                    return false;
                }
            }

            if (!typeSet.contains(poseVideo.getType())) {
                errorMessageList.add(String.format(TYPE_ERROR_FORMAT, poseVideo.getType()));
                return false;
            }
            if (!positionSet.contains(poseVideo.getPosition())) {
                errorMessageList.add(String.format(POSITION_ERROR_FORMAT, poseVideo.getPosition()));
                return false;
            }

            if (videoNameSet.contains(poseVideo.getName())) {
                errorMessageList.add(String.format(NAME_REPEATED_ERROR_FORMAT, poseVideo.getName()));
                return false;
            }

            videoNameSet.add(poseVideo.getName());
            return true;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public List<String> importChairYogaVideoSlice(MultipartFile file) {

        if (Objects.isNull(file)) {
            return Collections.emptyList();
        }

        List<ProjChairYogaVideoSliceImportReq> importReqList = parseChairYogaVideoSliceImport(file);
        if (CollectionUtils.isEmpty(importReqList)) {
            return Collections.emptyList();
        }

        List<String> errorMessages = Lists.newArrayList();
        List<ProjChairYogaVideoSliceImportReq> filterImportList = filterChairYogaVideoSliceImport(importReqList, errorMessages);
        if (CollectionUtils.isEmpty(filterImportList)) {
            return errorMessages;
        }

        List<ProjChairYogaVideoSliceImportReq> validImportList = bizValidate4SliceImport(filterImportList, errorMessages);
        if (CollectionUtils.isEmpty(validImportList)) {
            return errorMessages;
        }

        Set<String> videoNameSet = validImportList.stream().map(ProjChairYogaVideoSliceImportReq::getName).collect(Collectors.toSet());
        Map<String, ProjChairYogaVideo> videoNameMap = getVideoNameMap(videoNameSet);
        List<ProjChairYogaVideoSlice> videoSliceList = validImportList.stream().map(importReq -> convertImport2Entity(importReq, videoNameMap.get(importReq.getName()), RequestContextUtils.getProjectId())).collect(Collectors.toList());
        chairYogaVideoSliceService.saveBatch(videoSliceList);
        return errorMessages;
    }

    private ProjChairYogaVideoSlice convertImport2Entity(ProjChairYogaVideoSliceImportReq importReq, ProjChairYogaVideo yogaVideo, Integer projId) {

        ProjChairYogaVideoSlice videoSlice = new ProjChairYogaVideoSlice();
        BeanUtils.copyProperties(importReq, videoSlice);
        videoSlice.setProjId(projId);
        videoSlice.setProjChairYogaVideoId(yogaVideo.getId());
        return videoSlice;
    }

    private List<ProjChairYogaVideoSliceImportReq> bizValidate4SliceImport(List<ProjChairYogaVideoSliceImportReq> filterImportList, List<String> errorMessages) {

        Set<String> videoNameSet = filterImportList.stream().map(ProjChairYogaVideoSliceImportReq::getName).collect(Collectors.toSet());
        Map<String, ProjChairYogaVideo> videoNameMap = getVideoNameMap(videoNameSet);
        Map<String, Integer> videoNameAndSliceIdMap = getVideoNameAndSliceIdMap(videoNameMap.values());

        return filterImportList.stream().filter(importReq -> {
            if (!videoNameMap.containsKey(importReq.getName())) {
                errorMessages.add(String.format("Can't find chair yoga video with name : %s.", importReq.getName()));
                return false;
            }
            if (videoNameAndSliceIdMap.containsKey(importReq.getName())) {
                errorMessages.add(String.format("Already insert slice for chair yoga video, video name : %s, sort : %s.", importReq.getName(), importReq.getSliceIndex()));
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    private Map<String, Integer> getVideoNameAndSliceIdMap(Collection<ProjChairYogaVideo> yogaVideos) {

        if (CollectionUtils.isEmpty(yogaVideos)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> videoIdAndNameMap = yogaVideos.stream().collect(Collectors.toMap(ProjChairYogaVideo::getId, ProjChairYogaVideo::getName, (k1, k2) -> k1));
        List<ProjChairYogaVideoSliceDetailVO> sliceDetailVOList = chairYogaVideoSliceService.listByChairYogaVideoId(videoIdAndNameMap.keySet());
        Set<Integer> existedSliceVideoIds = sliceDetailVOList.stream().map(ProjChairYogaVideoSliceDetailVO::getProjChairYogaVideoId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(existedSliceVideoIds)) {
            return Collections.emptyMap();
        }
        return existedSliceVideoIds.stream().collect(Collectors.toMap(id -> videoIdAndNameMap.get(id), id -> id, (k1, k2) -> k1));
    }

    private List<ProjChairYogaVideoSliceImportReq> filterChairYogaVideoSliceImport(List<ProjChairYogaVideoSliceImportReq> chairVideoSliceImportList, List<String> errorMessageList) {

        List<ProjChairYogaVideoSliceImportReq> validPoseVideoList = chairVideoSliceImportList.stream().filter(videoSlice -> {

            Set<ConstraintViolation<ProjChairYogaVideoSliceImportReq>> violationSet = validator.validate(videoSlice, Group.class);
            if (!CollectionUtils.isEmpty(violationSet)) {
                List<String> errorInfoList = violationSet.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
                String errorInfo = JacksonUtil.toJsonString(errorInfoList);
                errorMessageList.add(String.format(PoseWorkoutConstant.VIDEO_IMPORT_ERROR, videoSlice.getName(), errorInfo));
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        return validPoseVideoList;
    }

    @Override
    public List<ProjChairYogaVideo> listValid4Generate() {

        return chairYogaVideoMapper.listValid4Generate();
    }

    @Override
    public List<ProjChairYogaVideo> listByVideoIds(Collection<Integer> videoIds) {

        if (CollectionUtils.isEmpty(videoIds)) {
            return Collections.emptyList();
        }

        return chairYogaVideoMapper.listByIds(videoIds);
    }

    private List<ProjChairYogaVideoImportReq> parseChairYogaVideoImport(MultipartFile file) {

        List<ProjChairYogaVideoImportReq> poseVideoImportList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), ProjChairYogaVideoImportReq.class, new AnalysisEventListener<ProjChairYogaVideoImportReq>() {
                @Override
                public void invoke(ProjChairYogaVideoImportReq row, AnalysisContext analysisContext) {
                    poseVideoImportList.add(row);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet(GlobalConstant.ZERO).doRead();
        } catch (Exception e) {
            log.warn(VIDEO_IMPORT_ERROR);
            log.warn(e.getMessage(), e);
            throw new BizException(VIDEO_IMPORT_ERROR);
        }
        return poseVideoImportList;
    }

    private List<ProjChairYogaVideoSliceImportReq> parseChairYogaVideoSliceImport(MultipartFile file) {

        List<ProjChairYogaVideoSliceImportReq> poseVideoImportList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), ProjChairYogaVideoSliceImportReq.class, new AnalysisEventListener<ProjChairYogaVideoSliceImportReq>() {
                @Override
                public void invoke(ProjChairYogaVideoSliceImportReq row, AnalysisContext analysisContext) {
                    poseVideoImportList.add(row);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet(GlobalConstant.ZERO).doRead();
        } catch (Exception e) {
            log.warn(VIDEO_IMPORT_ERROR);
            log.warn(e.getMessage(), e);
            throw new BizException(VIDEO_IMPORT_ERROR);
        }
        return poseVideoImportList;
    }


}
