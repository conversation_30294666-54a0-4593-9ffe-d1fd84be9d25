package com.laien.web.biz.proj.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_image_auto_workout_relation
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_auto_workout_basic_info_workout_relation")
@ApiModel(value="ProjAutoWorkoutBasicInfoWorkoutRelation对象", description="proj_image_auto_workout_relation")
public class ProjAutoWorkoutBasicInfoWorkoutRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联表projAutoWorkoutBasicInfoId的id")
    private Integer projAutoWorkoutBasicInfoId;

    @ApiModelProperty(value = "关联表projAutoWorkoutBasicInfoId的name")
    private String projAutoWorkoutBasicInfoName;

    @ApiModelProperty(value = "关联自动生成的workout，由plan_type确定关联哪张表")
    private Integer autoWorkoutId;

    @ApiModelProperty(value = "plan类型，0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private YogaAutoWorkoutTemplateEnum planType;


}
