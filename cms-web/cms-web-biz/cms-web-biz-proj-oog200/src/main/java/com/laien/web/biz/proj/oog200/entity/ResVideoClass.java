package com.laien.web.biz.proj.oog200.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.common.oog200.enums.PlayTypeEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.web.common.m3u8.seq.annotation.ResourceSection;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * res video class
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResVideoClass对象", description="res video class")
public class ResVideoClass extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @TranslateField(type = TranslationTaskTypeEnums.TEXT)
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "image png")
    private String imagePng;

    @ApiModelProperty(value = "image gif")
    private String imageGif;

    @ResourceSection(
            tableName = "res_video_class",
            m3u8UrlColumn = "video_m3u8_url",
            m3u8Url2532Column = "video2532_m3u8_url",
            dirKey = "videoClass-m3u8"
    )
    @ApiModelProperty(value = "视频地址mp4")
    private String videoUrl;

    @ApiModelProperty(value = "多分辨率M3u8视频地址")
    private String videoM3u8Url;

    @ApiModelProperty(value = "2532分辨率视频地址")
    private String video2532M3u8Url;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "难度")
    @TranslateField(type = TranslationTaskTypeEnums.TEXT)
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "new 标签开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new 标签结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "视频的m3u8链接地址")
    private String m3u8Url;

    @ApiModelProperty(value = "video class类型，取值 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yog")
    private YogaAutoWorkoutTemplateEnum type;

    @ApiModelProperty(value = "视频宽高比")
    private PlayTypeEnum playType;

//    @ApiModelProperty(value = "m3u8转换状态")
//    private Integer convertStatus;

}
