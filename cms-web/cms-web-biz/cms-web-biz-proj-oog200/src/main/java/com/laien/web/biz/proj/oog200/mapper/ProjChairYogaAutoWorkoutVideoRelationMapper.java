package com.laien.web.biz.proj.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog200.entity.ProjChairYogaAutoWorkoutVideoRelation;
import com.laien.web.biz.proj.oog200.response.ProjChairYogaVideoPageVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/29 11:44
 */
public interface ProjChairYogaAutoWorkoutVideoRelationMapper extends BaseMapper<ProjChairYogaAutoWorkoutVideoRelation> {

    @Select(value = "select video.*,relation.video_duration as duration from proj_chair_yoga_auto_workout_video_relation relation\n" +
            "inner join proj_chair_yoga_video video on relation.proj_chair_yoga_video_id = video.id \n" +
            "WHERE relation.proj_chair_yoga_auto_workout_id = #{workoutId} and relation.del_flag = 0\n" +
            "order by relation.id ASC \n")
    List<ProjChairYogaVideoPageVO> listRelationAndVideo(@Param("workoutId") Integer workoutId);
}
