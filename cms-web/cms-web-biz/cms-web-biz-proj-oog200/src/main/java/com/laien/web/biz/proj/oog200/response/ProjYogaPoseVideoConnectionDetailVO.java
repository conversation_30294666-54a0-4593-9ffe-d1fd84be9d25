package com.laien.web.biz.proj.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author:  hhl
 * Date:  2024/8/1 17:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "pose video connect")
public class ProjYogaPoseVideoConnectionDetailVO {

    @ApiModelProperty(value = "next video info")
    private ProjBaseDetailVO nextPoseVideo;

    @ApiModelProperty(value = "transition info")
    private ProjBaseDetailVO poseTransitionVideo;

}
