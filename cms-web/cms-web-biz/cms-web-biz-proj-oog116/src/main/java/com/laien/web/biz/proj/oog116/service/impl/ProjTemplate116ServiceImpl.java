package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116Rule;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116Task;
import com.laien.web.biz.proj.oog116.mapper.ProjTemplate116Mapper;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116RuleReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116PageVO;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleDetailVO;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116RuleService;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116Service;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116TaskService;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116GenerateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.IdAndCountsRes;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * template116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjTemplate116ServiceImpl extends ServiceImpl<ProjTemplate116Mapper, ProjTemplate116> implements IProjTemplate116Service {

    @Resource
    private IProjTemplate116RuleService projTemplate116RuleService;
    @Resource
    private IProjTemplate116TaskService projTemplate116TaskService;
    @Resource
    private IProjWorkout116GenerateService projWorkout116GenerateService;

    @Resource
    private IProjInfoService projInfoService;
    @Autowired
    private IProjLmsI18nService projLmsI18nService;


    @Override
    public PageRes<ProjTemplate116PageVO> selectTemplate116Page(ProjTemplate116PageReq pageReq) {
        LambdaQueryWrapper<ProjTemplate116> queryWrapper = new LambdaQueryWrapper<>();
        String name = pageReq.getName();
        String durationRange = pageReq.getDurationRange();
        Integer status = pageReq.getStatus();
        queryWrapper.like(StringUtils.isNotBlank(name), ProjTemplate116::getName, name)
                .eq(StringUtils.isNotBlank(durationRange), ProjTemplate116::getDurationRange, durationRange)
                .eq(Objects.nonNull(status), ProjTemplate116::getStatus, status)
                .eq(ProjTemplate116::getProjId, RequestContextUtils.getProjectId())
                .orderByDesc(ProjTemplate116::getId);

        if (Objects.nonNull(pageReq.getTemplateType())) {
            queryWrapper.eq(ProjTemplate116::getTemplateType, pageReq.getTemplateType());
        }

        // 查询
        Page<ProjTemplate116> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.page(page, queryWrapper);
        List<ProjTemplate116> list = page.getRecords();
        List<Integer> ids = new ArrayList<>(list.size());
        List<ProjTemplate116PageVO> copyList = new ArrayList<>(list.size());
        for (ProjTemplate116 template116 : list) {
            ProjTemplate116PageVO pageVO = new ProjTemplate116PageVO();
            BeanUtils.copyProperties(template116, pageVO);
            copyList.add(pageVO);
            ids.add(template116.getId());
        }

        // 查询所包含的视频数量
        List<ProjTemplate116Task> taskList = projTemplate116TaskService.selectLastTask(ids);
        List<IdAndCountsRes> videoCountList = projWorkout116GenerateService.selectCountByTemplateIds(ids);
        for (ProjTemplate116PageVO template116PageVO : copyList) {
            template116PageVO.setVideoCount(GlobalConstant.ZERO);
            taskList.stream().filter(o -> Objects.equals(o.getProjTemplate116Id(), template116PageVO.getId()))
                    .findFirst()
                    .ifPresent(o -> template116PageVO.setTaskStatus(o.getStatus()));
            videoCountList.stream().filter(o -> Objects.equals(o.getId(), template116PageVO.getId()))
                    .findFirst()
                    .ifPresent(o -> template116PageVO.setVideoCount(o.getCounts()));
        }

        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), copyList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveTemplate116(ProjTemplate116AddReq template116AddReq) {
        // 检查Template
        this.checkTemplate(template116AddReq, null);

        // 保存
        ProjTemplate116 template116 = new ProjTemplate116();
        BeanUtils.copyProperties(template116AddReq, template116);
        template116.setProjId(RequestContextUtils.getProjectId());
        template116.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(template116);

        // 保存规则
        this.saveRuleList(template116.getId(), template116AddReq);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTemplate116(ProjTemplate116UpdateReq template116UpdateReq) {
        Integer id = template116UpdateReq.getId();
        ProjTemplate116 template116Find = this.getById(id);
        if (Objects.isNull(template116Find)) {
            throw new BizException("Data not found");
        }

        // 检查Template
        this.checkTemplate(template116UpdateReq, id);
        // 保存
        ProjTemplate116 template116 = new ProjTemplate116();
        BeanUtils.copyProperties(template116UpdateReq, template116);
        this.updateById(template116);

        // 删除之前的规则
        this.deleteRule(id);
        // 保存新的规则
        this.saveRuleList(id, template116UpdateReq);
    }

    /**
     * 保存规则
     *
     * @param id                id
     * @param template116AddReq template116AddReq
     */
    private void saveRuleList(Integer id, ProjTemplate116AddReq template116AddReq) {

        List<ProjTemplate116RuleReq> warmUpRuleList = template116AddReq.getWarmUpRuleList();
        List<ProjTemplate116RuleReq> mainRuleList = template116AddReq.getMainRuleList();
        List<ProjTemplate116RuleReq> coolDownRuleList = template116AddReq.getCoolDownRuleList();

        List<ProjTemplate116Rule> ruleSaveList = new ArrayList<>(warmUpRuleList.size() + mainRuleList.size() + coolDownRuleList.size());
        for (ProjTemplate116RuleReq template116RuleReq : warmUpRuleList) {
            ProjTemplate116Rule template116Rule = new ProjTemplate116Rule();
            BeanUtils.copyProperties(template116RuleReq, template116Rule);
            template116Rule.setProjTemplate116Id(id);
            template116Rule.setVideoType(ResVideo116TypeEnum.WARM_UP.getValue());
            ruleSaveList.add(template116Rule);
        }
        for (ProjTemplate116RuleReq template116RuleReq : mainRuleList) {
            ProjTemplate116Rule template116Rule = new ProjTemplate116Rule();
            BeanUtils.copyProperties(template116RuleReq, template116Rule);
            template116Rule.setProjTemplate116Id(id);
            template116Rule.setVideoType(ResVideo116TypeEnum.MAIN.getValue());
            ruleSaveList.add(template116Rule);
        }
        for (ProjTemplate116RuleReq template116RuleReq : coolDownRuleList) {
            ProjTemplate116Rule template116Rule = new ProjTemplate116Rule();
            BeanUtils.copyProperties(template116RuleReq, template116Rule);
            template116Rule.setProjTemplate116Id(id);
            template116Rule.setVideoType(ResVideo116TypeEnum.COOL_DOWN.getValue());
            ruleSaveList.add(template116Rule);
        }

        projTemplate116RuleService.saveBatch(ruleSaveList);
        //进行翻译任务
        projLmsI18nService.handleI18n(ruleSaveList, projInfoService.getById(RequestContextUtils.getProjectId()));

    }

    /**
     * 删除template rule
     *
     * @param id id
     */
    private void deleteRule(Integer id) {
        LambdaUpdateWrapper<ProjTemplate116Rule> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplate116Rule::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjTemplate116Rule::getProjTemplate116Id, id);
        projTemplate116RuleService.update(new ProjTemplate116Rule(), wrapper);
    }

    /**
     * template检查
     *
     * @param template116AddReq template116AddReq
     * @param id                id
     */
    private void checkTemplate(ProjTemplate116AddReq template116AddReq, Integer id) {
        LambdaQueryWrapper<ProjTemplate116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjTemplate116::getName, template116AddReq.getName())
                .eq(ProjTemplate116::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjTemplate116::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Template name exists");
        }
    }

    @Override
    public ProjTemplate116DetailVO getTemplate116Detail(Integer id) {
        ProjTemplate116 template116Find = this.getById(id);
        if (Objects.isNull(template116Find) || !Objects.equals(template116Find.getProjId(), RequestContextUtils.getProjectId())) {
            throw new BizException("Data not found");
        }
        ProjTemplate116DetailVO detailVO = new ProjTemplate116DetailVO();
        BeanUtils.copyProperties(template116Find, detailVO);

        LambdaQueryWrapper<ProjTemplate116Rule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjTemplate116Rule::getProjTemplate116Id, id);
        List<ProjTemplate116Rule> ruleList = projTemplate116RuleService.list(queryWrapper);
        List<ProjTemplate116RuleDetailVO> warmUpRuleList = new ArrayList<>();
        List<ProjTemplate116RuleDetailVO> mainRuleList = new ArrayList<>();
        List<ProjTemplate116RuleDetailVO> coolDownRuleList = new ArrayList<>();

        for (ProjTemplate116Rule template116Rule : ruleList) {
            String videoType = template116Rule.getVideoType();
            ProjTemplate116RuleDetailVO ruleVO = new ProjTemplate116RuleDetailVO();
            BeanUtils.copyProperties(template116Rule, ruleVO);
            if (Objects.equals(videoType, ResVideo116TypeEnum.WARM_UP.getValue())) {
                warmUpRuleList.add(ruleVO);
            } else if (Objects.equals(videoType, ResVideo116TypeEnum.MAIN.getValue())) {
                mainRuleList.add(ruleVO);
            } else if (Objects.equals(videoType, ResVideo116TypeEnum.COOL_DOWN.getValue())) {
                coolDownRuleList.add(ruleVO);
            }
        }

        detailVO.setWarmUpRuleList(warmUpRuleList);
        detailVO.setMainRuleList(mainRuleList);
        detailVO.setCoolDownRuleList(coolDownRuleList);

        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjTemplate116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplate116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjTemplate116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjTemplate116::getId, idList);
        this.update(new ProjTemplate116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjTemplate116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplate116::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjTemplate116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjTemplate116::getId, idList);
        this.update(new ProjTemplate116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            LambdaUpdateWrapper<ProjTemplate116> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjTemplate116::getDelFlag, GlobalConstant.YES);
            wrapper.in(ProjTemplate116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
            wrapper.eq(ProjTemplate116::getId, id);
            boolean flag = this.update(new ProjTemplate116(), wrapper);
            // 删除规则
            if (flag) {
                this.deleteRule(id);
            }
        }
    }

    @Override
    public List<Integer> queryIdList(Integer status) {
        LambdaQueryWrapper<ProjTemplate116> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(null != status, ProjTemplate116::getStatus, status);
        List<ProjTemplate116> templateList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(templateList)) {
            return new ArrayList<>();
        }
        return templateList.stream().map(BaseModel::getId).collect(Collectors.toList());
    }
}
