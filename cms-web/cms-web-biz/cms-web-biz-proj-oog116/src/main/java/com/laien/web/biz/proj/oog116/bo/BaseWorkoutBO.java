package com.laien.web.biz.proj.oog116.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2025/3/19 17:42
 */
@Accessors(chain = true)
@Data
public class BaseWorkoutBO {

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "video的 多分辨率 m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "video的 2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json, 英语")
    private String audioJsonUrl;

    @ApiModelProperty(value = "多语言音频json，包含英语，<language,audioUrl>")
    private Map<String, String> audioI18nUrl;

}
