package com.laien.web.biz.proj.oog116.config;

import com.google.common.collect.Lists;
import com.laien.web.biz.proj.oog116.bo.AudioJson116AllBO;
import com.laien.web.biz.proj.oog116.bo.AudioJson116BO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
public class ChairYogaConfigWrapper {
    private ChairYogaConfig femaleSoundConfig;
    private ChairYogaConfig maleSoundConfig;

    public List<AudioJson116BO> getAudioList() {
       //当前只有女声,不考虑男声
        List<AudioJson116BO> audioJsonBOList = Lists.newArrayList();
        for(int i = 0 ; i < femaleSoundConfig.getStartList4First().size(); i++) {
            AudioJson116BO firstAudioFemale = femaleSoundConfig.getSysSoundByName("", femaleSoundConfig.getStartList4First().get(i));
            firstAudioFemale.setTime(new BigDecimal(60));
            audioJsonBOList.add(firstAudioFemale);
        }
        return audioJsonBOList;
    }

    public AudioJson116AllBO getFirstAudio() {
        AudioJson116AllBO result = new AudioJson116AllBO();
        if(Objects.nonNull(maleSoundConfig)){
        AudioJson116BO firstAudioMale = maleSoundConfig.getSysSoundByName("chair yoga first", maleSoundConfig.getFirst());
        firstAudioMale.setTime(new BigDecimal(1));
        result.setMaleAudioJson116BO(firstAudioMale);
        }
        AudioJson116BO firstAudioFemale = femaleSoundConfig.getSysSoundByName("chair yoga first", femaleSoundConfig.getFirst());
        firstAudioFemale.setTime(new BigDecimal(1));
        result.setFemaleAudioJson116BO(firstAudioFemale);
        return result;
    }

    public AudioJson116AllBO getNextAudio() {
        AudioJson116AllBO result = new AudioJson116AllBO();
        if(Objects.nonNull(maleSoundConfig)) {
            AudioJson116BO nextAudioMale = maleSoundConfig.getSysSoundByName("chair yoga next", maleSoundConfig.getNext());
            nextAudioMale.setTime(new BigDecimal(1));
            result.setMaleAudioJson116BO(nextAudioMale);
        }
        AudioJson116BO nextAudioFemale = femaleSoundConfig.getSysSoundByName("chair yoga next", femaleSoundConfig.getNext());
        nextAudioFemale.setTime(new BigDecimal(1));
        result.setFemaleAudioJson116BO(nextAudioFemale);
        return result;
    }

    public AudioJson116AllBO getLastAudio() {
        AudioJson116AllBO result = new AudioJson116AllBO();
        if(Objects.nonNull(maleSoundConfig)) {
        AudioJson116BO finishAudioMale = maleSoundConfig.getSysSoundByName("chair yoga last", maleSoundConfig.getLast());
        finishAudioMale.setTime(new BigDecimal(1));
        result.setMaleAudioJson116BO(finishAudioMale);
        }
        AudioJson116BO finishAudioFemale = femaleSoundConfig.getSysSoundByName("chair yoga last", femaleSoundConfig.getLast());
        finishAudioFemale.setTime(new BigDecimal(1));
        result.setFemaleAudioJson116BO(finishAudioFemale);
        return result;

    }
}
