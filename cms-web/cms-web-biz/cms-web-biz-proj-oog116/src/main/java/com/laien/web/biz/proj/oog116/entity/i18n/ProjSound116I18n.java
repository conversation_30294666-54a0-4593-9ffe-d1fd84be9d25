package com.laien.web.biz.proj.oog116.entity.i18n;

import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.web.biz.proj.oog116.bo.AudioJson116BO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_sound116
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjSound116I18n", description = "ProjSound116I18n")
public class ProjSound116I18n extends BaseAudioI18nModel {

    @ApiModelProperty(value = "声音脚本")
    @AppAudioTranslateField(resultFieldName = "result")
    private String soundScript;

    public ProjSound116I18n(AudioJson116BO json116BO) {
        super.setUniqueKey(json116BO.getSoundId());
        super.setCoreVoiceConfigI18nId(json116BO.getCoreVoiceConfigI18nId());
        this.soundScript = json116BO.getSoundScript();
    }
}
