package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ProjRecoveryCategory116ProjWorkout116;
import com.laien.web.biz.proj.oog116.mapper.ProjRecoveryCategory116ProjWorkout116Mapper;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116DetailWorkoutVO;
import com.laien.web.biz.proj.oog116.service.IProjRecoveryCategory116ProjWorkout116Service;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * proj_recovery_category116_proj_workout116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class ProjRecoveryCategory116ProjWorkout116ServiceImpl extends ServiceImpl<ProjRecoveryCategory116ProjWorkout116Mapper, ProjRecoveryCategory116ProjWorkout116> implements IProjRecoveryCategory116ProjWorkout116Service {

    @Override
    public List<IdAndStatusCountsRes> selectWorkoutStatusCount(Integer projId) {
        return this.baseMapper.selectWorkoutStatusCount(projId);
    }

    @Override
    public List<ProjRecoveryCategory116DetailWorkoutVO> selectWorkoutByCategoryId(Integer categoryId) {
        return this.baseMapper.selectWorkoutByCategoryId(categoryId);
    }
}
