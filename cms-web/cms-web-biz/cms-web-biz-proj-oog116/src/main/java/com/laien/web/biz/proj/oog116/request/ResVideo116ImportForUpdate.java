package com.laien.web.biz.proj.oog116.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <p>修改数据导入</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 13:22
 */
@Data
public class ResVideo116ImportForUpdate {

    @ExcelIgnore
    private Integer rowNum;

    @ExcelProperty(value = "id", index = 0)
    private Integer id;

    @ExcelProperty(value = "coverImgUrl", index = 1)
    private String coverImgUrl;

    @ExcelProperty(value = "frontVideoUrl", index = 2)
    private String frontVideoUrl;

    @ExcelProperty(value = "sideVideoUrl", index = 3)
    private String sideVideoUrl;
}
