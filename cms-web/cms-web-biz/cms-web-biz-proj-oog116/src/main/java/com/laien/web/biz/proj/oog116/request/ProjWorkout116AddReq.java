package com.laien.web.biz.proj.oog116.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * note: workout116新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout116新增", description = "workout116新增")
public class ProjWorkout116AddReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "难度Easy，Medium，Hard")
    private String difficulty;

    @ApiModelProperty(value = "Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制，数组，取值Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String[] restrictionArr;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "exercise list")
    private List<ProjWorkout116ExerciseAddReq> exerciseList;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "身体部位 (多选)")
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)")
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具 (多选)")
    private List<SupportProp116Enums> supportProp;

}
