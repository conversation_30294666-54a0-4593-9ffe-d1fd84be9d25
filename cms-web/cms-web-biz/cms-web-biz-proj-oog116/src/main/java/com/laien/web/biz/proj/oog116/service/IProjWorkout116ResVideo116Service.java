package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116ResVideo116;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116ExerciseDetailVO;

import java.util.List;

/**
 * <p>
 * proj_workout116_res_video116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjWorkout116ResVideo116Service extends IService<ProjWorkout116ResVideo116> {

    /**
     * 根据workoutId 查询workout116 exercise
     *
     * @param workoutId workoutId
     * @return list
     */
    List<ProjWorkout116ExerciseDetailVO> selectExercisesByWorkoutId(Integer workoutId);

    List<ProjWorkout116ResVideo116> listByWorkoutId(Integer workoutId);

}
