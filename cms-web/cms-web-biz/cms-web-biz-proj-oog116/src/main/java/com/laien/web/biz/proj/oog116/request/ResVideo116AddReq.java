package com.laien.web.biz.proj.oog116.request;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Joiner;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.frame.constant.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * note: video116新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video116新增", description = "video116新增")
public class ResVideo116AddReq {

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频类型Warm Up/Cool Down/Main")
    private String type;

    @ApiModelProperty(value = "部位Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制，数组，取值Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String[] restrictionArr;

    @ApiModelProperty(value = "动作简介（How To Do）")
    private String instructions;

    @ApiModelProperty(value = "Video播放轮数，目前仅针对TaiChi类型Video")
    private Integer circuit;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontDuration;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioUrlDuration;

    @ApiModelProperty(value = "guidance")
    private String guidance;

    @ApiModelProperty(value = "Guidance音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "Instructions的音频")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "met,1-12的整数")
    private Integer met;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Chair Yoga、Gentle Cardio、Walking、Dumbbell (lightweight) 、Resistance Band、Dancing、Tai Chi 多选字段")
    private List<String> exerciseTypeList;

    @ApiModelProperty(value = "target：Full Body/Upper Body/Lower Body")
    private String target;

    @ApiModelProperty(value = "身体部位 (多选)")
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)")
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具")
    private SupportProp116Enums supportProp;

    @ApiModelProperty(value = "res video 切片")
    private List<ResVideo116SliceDetailVO> videosliceList;

    /**
     * <p>将exerciseType拼接为逗号分隔的字符串</p>
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/3/3 16:02
     */
    public String getExerciseType() {
        return Optional.ofNullable(this.exerciseTypeList).filter(CollUtil::isNotEmpty).map(list -> {
            Collections.sort(list);
            return Joiner.on(GlobalConstant.COMMA).join(list);
        }).orElse(null);
    }

}

