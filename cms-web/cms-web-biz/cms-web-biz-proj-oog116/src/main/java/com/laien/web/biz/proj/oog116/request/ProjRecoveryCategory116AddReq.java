package com.laien.web.biz.proj.oog116.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.oog116.enums.RecoveryCategory116ShowTypeEnums;
import com.laien.common.oog116.enums.RecoveryCategory116TypeEnums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p>
 * recovery category116 新增
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@ApiModel(value = "recovery category116 新增", description = "recovery category116 新增")
public class ProjRecoveryCategory116AddReq {

    @ApiModelProperty(value = "分类名称", required = true)
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @ApiModelProperty(value = "展示类型", required = true)
    @NotNull(message = "展示类型不能为空")
    private RecoveryCategory116ShowTypeEnums showType;

    @ApiModelProperty(value = "recoveryCategoryType", required = true)
    private List<RecoveryCategory116TypeEnums> type;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "图标")
    private String iconUrl;

    @ApiModelProperty(value = "简介")
    @Size(max = 1000, message = "简介不能超过1000字符")
    private String description;

    @ApiModelProperty(value = "workout list")
    private List<ProjRecoveryCategory116AddWorkoutReq> workoutList;
}
