package com.laien.web.biz.proj.oog116.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: template116 generate 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template116 generate 分页", description = "template116 generate 分页")
public class ProjWorkout116GeneratePageReq extends PageReq {

    @ApiModelProperty(value = "template id")
    private Integer templateId;

    @ApiModelProperty(value = "workout id")
    private Integer id;

    @ApiModelProperty(value = "Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制，数组，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String[] restrictionArr;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "videoIdList")
    private List<Integer> videoIdList;

    @ApiModelProperty(value = "templateStatus")
    private Integer templateStatus;

}
