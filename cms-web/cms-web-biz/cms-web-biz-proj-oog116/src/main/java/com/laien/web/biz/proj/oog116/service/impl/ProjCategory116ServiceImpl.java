package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.entity.ProjCategory116;
import com.laien.web.biz.proj.oog116.entity.ProjCategory116ProjWorkout116;
import com.laien.web.biz.proj.oog116.mapper.ProjCategory116Mapper;
import com.laien.web.biz.proj.oog116.request.ProjCategory116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjCategory116AddWorkoutReq;
import com.laien.web.biz.proj.oog116.request.ProjCategory116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjCategory116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjCategory116DetailWorkoutVO;
import com.laien.web.biz.proj.oog116.response.ProjCategory116ListVO;
import com.laien.web.biz.proj.oog116.service.IProjCategory116ProjWorkout116Service;
import com.laien.web.biz.proj.oog116.service.IProjCategory116Service;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * template116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjCategory116ServiceImpl extends ServiceImpl<ProjCategory116Mapper, ProjCategory116> implements IProjCategory116Service {

    @Resource
    private IProjCategory116ProjWorkout116Service projCategory116ProjWorkout116Service;
    @Resource
    private IProjLmsI18nService projLmsI18nService;
    @Resource
    private IProjInfoService projInfoService;

    @Override
    public List<ProjCategory116ListVO> selectCategoryList() {
        Integer projId = RequestContextUtils.getProjectId();
        LambdaQueryWrapper<ProjCategory116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjCategory116::getProjId, projId);
        queryWrapper.orderByAsc(ProjCategory116::getSortNo);
        queryWrapper.orderByDesc(ProjCategory116::getId);

        // 查询
        List<ProjCategory116> list = this.list(queryWrapper);
        List<ProjCategory116ListVO> copyList = new ArrayList<>(list.size());
        if (list.isEmpty()) {
            return copyList;
        }
        Map<Integer, List<IdAndStatusCountsRes>> videoStatusCountMap = projCategory116ProjWorkout116Service.selectWorkoutStatusCount(projId)
                .stream().collect(Collectors.groupingBy(IdAndStatusCountsRes::getId));

        for (ProjCategory116 category116 : list) {
            ProjCategory116ListVO listVO = new ProjCategory116ListVO();
            BeanUtils.copyProperties(category116, listVO);
            listVO.setTotalCount(GlobalConstant.ZERO);
            listVO.setEnableCount(GlobalConstant.ZERO);
            copyList.add(listVO);

            // 处理video class 总数和启用总数
            List<IdAndStatusCountsRes> statusCountVOList = videoStatusCountMap.get(category116.getId());
            if (statusCountVOList != null) {
                int total = 0;
                for (IdAndStatusCountsRes statusCountVO : statusCountVOList) {
                    total += statusCountVO.getCounts();
                    if (Objects.equals(statusCountVO.getStatus(), GlobalConstant.STATUS_ENABLE)) {
                        listVO.setEnableCount(statusCountVO.getCounts());
                    }
                }
                listVO.setTotalCount(total);
            }
        }

        return copyList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCategory(ProjCategory116AddReq category116AddReq) {
        // 检查Template
        this.checkCategory(category116AddReq, null);

        // 保存
        ProjCategory116 category116 = new ProjCategory116();
        BeanUtils.copyProperties(category116AddReq, category116);
        category116.setSortNo(GlobalConstant.ZERO);
        category116.setProjId(RequestContextUtils.getProjectId());
        category116.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(category116);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(category116), projInfoService.getById(RequestContextUtils.getProjectId()));


        // 保存规则
        this.saveRelation(category116.getId(), category116AddReq.getWorkoutList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCategory(ProjCategory116UpdateReq category116UpdateReq) {
        Integer id = category116UpdateReq.getId();
        ProjCategory116 category116Find = this.getById(id);
        if (Objects.isNull(category116Find)) {
            throw new BizException("Data not found");
        }

        // 检查Template
        this.checkCategory(category116UpdateReq, id);
        // 保存
        // 判断是否有更新为null的字段
        if (category116UpdateReq.getType() == null) {
            LambdaUpdateWrapper<ProjCategory116> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjCategory116::getType, null)
                    .eq(ProjCategory116::getId, id);
            this.update(updateWrapper);
        }
        ProjCategory116 category116 = new ProjCategory116();
        BeanUtils.copyProperties(category116UpdateReq, category116);
        this.updateById(category116);
        projLmsI18nService.handleI18n(ListUtil.of(category116), projInfoService.getById(RequestContextUtils.getProjectId()));
        // 删除之前的规则
        this.deleteRelation(id);
        // 保存新的规则
        this.saveRelation(id, category116UpdateReq.getWorkoutList());
    }

    /**
     * 保存category workout关系
     *
     * @param id id
     * @param workoutList workoutList
     */
    private void saveRelation(Integer id, List<ProjCategory116AddWorkoutReq> workoutList) {
        if (workoutList != null && !workoutList.isEmpty()) {
            List<ProjCategory116ProjWorkout116> relationList = new ArrayList<>();
            for (ProjCategory116AddWorkoutReq projCategory116AddWorkoutReq : workoutList) {
                ProjCategory116ProjWorkout116 category116ProjWorkout116 = new ProjCategory116ProjWorkout116();
                category116ProjWorkout116.setProjCategory116Id(id);
                category116ProjWorkout116.setProjWorkout116Id(projCategory116AddWorkoutReq.getId());
                relationList.add(category116ProjWorkout116);
            }

            projCategory116ProjWorkout116Service.saveBatch(relationList);
        }
    }

    /**
     * template检查
     *
     * @param category116AddReq category116AddReq
     * @param id id
     */
    private void checkCategory(ProjCategory116AddReq category116AddReq, Integer id) {
        LambdaQueryWrapper<ProjCategory116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjCategory116::getName, category116AddReq.getName())
                .eq(ProjCategory116::getProjId, RequestContextUtils.getProjectId())
                .eq(category116AddReq.getGender()!=null,ProjCategory116::getGender, category116AddReq.getGender())
                .isNull(category116AddReq.getGender() == null,ProjCategory116::getGender)
                .ne(Objects.nonNull(id), ProjCategory116::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Category name exists");
        }

        LambdaQueryWrapper<ProjCategory116> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjCategory116::getEventName, category116AddReq.getEventName())
                .eq(ProjCategory116::getProjId, RequestContextUtils.getProjectId())
                .eq(category116AddReq.getGender()!=null,ProjCategory116::getGender, category116AddReq.getGender())
                .isNull(category116AddReq.getGender() == null,ProjCategory116::getGender)
                .ne(Objects.nonNull(id), ProjCategory116::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Category event name exists");
        }
    }

    /**
     * 删除workout 关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        LambdaUpdateWrapper<ProjCategory116ProjWorkout116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCategory116ProjWorkout116::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjCategory116ProjWorkout116::getProjCategory116Id, id);
        projCategory116ProjWorkout116Service.update(new ProjCategory116ProjWorkout116(), wrapper);
    }

    @Override
    public ProjCategory116DetailVO getCategoryDetail(Integer id) {
        ProjCategory116 category116Find = this.getById(id);
        if (Objects.isNull(category116Find)) {
            throw new BizException("Data not found");
        }
        ProjCategory116DetailVO detailVO = new ProjCategory116DetailVO();
        BeanUtils.copyProperties(category116Find, detailVO);

        List<ProjCategory116DetailWorkoutVO> workoutList = projCategory116ProjWorkout116Service.selectWorkoutByCategoryId(id);
        detailVO.setWorkoutList(workoutList);

        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjCategory116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCategory116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjCategory116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjCategory116::getId, idList);
        this.update(new ProjCategory116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjCategory116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCategory116::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjCategory116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjCategory116::getId, idList);
        this.update(new ProjCategory116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            LambdaUpdateWrapper<ProjCategory116> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjCategory116::getDelFlag, GlobalConstant.YES);
            wrapper.eq(ProjCategory116::getStatus, GlobalConstant.STATUS_DRAFT);
            wrapper.eq(ProjCategory116::getId, id);
            boolean flag = this.update(new ProjCategory116(), wrapper);
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveSort(List<Integer> idList) {
        if (CollectionUtils.isNotEmpty(idList)) {
            int sortNoIndex = 1;
            Integer projId = RequestContextUtils.getProjectId();
            for (Integer id : idList) {
                LambdaUpdateWrapper<ProjCategory116> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProjCategory116::getId, id);
                wrapper.eq(ProjCategory116::getProjId, projId);
                wrapper.set(ProjCategory116::getSortNo, sortNoIndex);
                this.update(new ProjCategory116(), wrapper);
                sortNoIndex++;
            }
        }
    }

}
