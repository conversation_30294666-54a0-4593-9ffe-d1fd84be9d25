package com.laien.web.biz.proj.oog116.response;

import com.laien.common.oog116.enums.RecoveryCategory116ShowTypeEnums;
import com.laien.common.oog116.enums.RecoveryCategory116TypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * recovery category116 列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@ApiModel(value = "recovery category116 列表", description = "recovery category116 列表")
public class ProjRecoveryCategory116ListVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "展示类型")
    private RecoveryCategory116ShowTypeEnums showType;

    @ApiModelProperty(value = "recoveryCategoryType", required = true)
    private List<RecoveryCategory116TypeEnums> type;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "图标")
    private String iconUrl;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "workout 总数")
    private Integer totalCount;

    @ApiModelProperty(value = "workout 启用数")
    private Integer enableCount;
}
