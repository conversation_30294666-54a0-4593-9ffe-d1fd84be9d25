package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.oog116.enums.*;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.config.ChairYogaConfigWrapper;
import com.laien.web.biz.proj.oog116.config.Oog116BizConfig;
import com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116Generate;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116GenerateI18n;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.exception.AutoGenerateException;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Generate4ChairYogaService;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116GenerateService;
import com.laien.web.biz.proj.oog116.service.IResVideo116Service;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.Gender116Enums.FEMALE;
import static com.laien.common.oog116.enums.Gender116Enums.MALE;
import static com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum.*;
import static com.laien.web.frame.constant.GlobalConstant.SECOND_MILL;

@Slf4j
@Service
public class ProjWorkout116Generate4ChairYogaServiceImpl implements IProjWorkout116Generate4ChairYogaService {

    @Resource
    IProjWorkout116GenerateService workout116GenerateService;

    @Resource
    Oog116BizConfig oog116BizConfig;

    @Resource
    private FileService fileService;
    
    @Resource
    private IResVideo116Service resVideo116Service;

    private static final int PREVIEW_CIRCUIT = 1;

    @Override
    public List<ProjWorkout116GenerateBO> generateWorkoutByPlan(ExerciseType116Enums exerciseType, Equipment116Enums equipment, List<Restriction116Enums> restrictionList, Position116Enums position, ProjWorkout116ContextBO context) {

        // 没有相应资源，无需生成
        if (!exerciseType.getPositionList().contains(position) || !exerciseType.getEquipmentList().contains(equipment)) {
            return Collections.emptyList();
        }

        // 目前只有female的动作
        Integer femaleDay = exerciseType.getFemaleDay();
        List<ProjWorkout116GenerateBO> femaleWorkoutList = generateWorkoutByDay(exerciseType, equipment, restrictionList, position, femaleDay, context, FEMALE);
        return femaleWorkoutList;
    }

    private ProjWorkout116Generate wrapWorkout(ProjWorkout116ContextBO context,
                                               Position116Enums position,
                                               Equipment116Enums equipment,
                                               ExerciseType116Enums exerciseType,
                                               Gender116Enums gender,
                                               List<ResImage> resImages,
                                               int day) {

        ProjWorkout116Generate projWorkout116 = new ProjWorkout116Generate();
        Integer projTemplate116Id = context.getProjTemplate116Id();
        Integer projTemplate116TaskId = context.getProjTemplate116TaskId();
        ResImage resImage = resImages.get(day % resImages.size());

        projWorkout116
                .setProjTemplate116Id(projTemplate116Id)
                .setProjTemplate116TaskId(projTemplate116TaskId)
                .setPosition(position.getName())
                .setResImageId(resImage.getId())
                .setEquipment(equipment.getName())
                .setExerciseType(exerciseType.getName())
                .setGender(gender.getName());
        return projWorkout116;
    }

    private List<ProjWorkout116GenerateBO> generateWorkoutByDay(ExerciseType116Enums exerciseType,
                                                                Equipment116Enums equipment,
                                                                List<Restriction116Enums> restrictionList,
                                                                Position116Enums position,
                                                                Integer day,
                                                                ProjWorkout116ContextBO context,
                                                                Gender116Enums gender) {

        List<ResImage> resImages = context.matchImageList(position, day, exerciseType, gender);
        List<ProjWorkout116GenerateBO> projWorkout116BOList = new ArrayList<>();
        for (int i = 0; i < day; i++) {

            ProjWorkout116GenerateBO projWorkout116BO = new ProjWorkout116GenerateBO();
            try {
                List<ProjWorkout116GenerateResVideo116BO> resVideo116BOList = selectVideo4Workout(context, restrictionList, position, equipment, exerciseType, gender);
                projWorkout116BO.setProjWorkout116GenerateResVideo116List(resVideo116BOList);
            } catch (AutoGenerateException e) {
                log.warn(e.getMessage());
                log.warn("generate chair yoga workout failed, exercise type : {}, position : {}, gender : {}, restrictionList : {}.", exerciseType, position, gender, restrictionList);
                throw new BizException(String.format("generate chair yoga workout failed."));
            }

            ProjWorkout116Generate workout116Generate = wrapWorkout(context, position, equipment, exerciseType, gender, resImages, i);
            projWorkout116BO.setProjWorkout116Generate(workout116Generate);
            projWorkout116BO.setRestrictionList(restrictionList);
            projWorkout116BOList.add(projWorkout116BO);
        }
        return projWorkout116BOList;
    }


    /**
     * 按照排列组合生成一天的workout
     * 先根据配置挑选warm up、cooldown
     * 再根据模板时间剪去已挑选视频时间，进行main视频的挑选
     * <p>
     * <p>
     * 生成workout、workout和video的关系，未入库
     */
    private List<ProjWorkout116GenerateResVideo116BO> selectVideo4Workout(ProjWorkout116ContextBO context,
                                                                          List<Restriction116Enums> restrictionList,
                                                                          Position116Enums position,
                                                                          Equipment116Enums equipment,
                                                                          ExerciseType116Enums exerciseType,
                                                                          Gender116Enums gender) {

        List<String> restrictionNameList = restrictionList.stream().map(Restriction116Enums::getName).collect(Collectors.toList());
        List<ProjWorkout116GenerateResVideo116BO> warmUpVideoList = assembleVideo(context, restrictionNameList, position, WARM_UP, equipment, exerciseType, gender);
        List<ProjWorkout116GenerateResVideo116BO> coolDownVideoList = assembleVideo(context, restrictionNameList, position, COOL_DOWN, equipment, exerciseType, gender);

        if (CollectionUtils.isEmpty(warmUpVideoList) || CollectionUtils.isEmpty(coolDownVideoList)) {
            throw new AutoGenerateException("Can't generate workout for chair yoga, no enough video, such as warmup , cooldown.");
        }

        List<Integer> durationRange = computeDurationRange4Main(context.getTemplate116(), context, position, warmUpVideoList, coolDownVideoList);
        List<ProjWorkout116GenerateResVideo116BO> mainVideoList = Lists.newArrayList();
        if (durationRange.get(1) > 0) {
            mainVideoList = assembleVideo4Main(context, durationRange.get(0), durationRange.get(1), restrictionNameList, position, MAIN, equipment, exerciseType, gender);
            if (CollectionUtils.isEmpty(mainVideoList)) {
                throw new AutoGenerateException("Can't generate workout for chair yoga, no enough video, such as main.");
            }
        }

        List<ProjWorkout116GenerateResVideo116BO> workoutResVideoList = Lists.newArrayList();
        workoutResVideoList.addAll(warmUpVideoList);
        workoutResVideoList.addAll(mainVideoList);
        workoutResVideoList.addAll(coolDownVideoList);

        // 拉通整个workout来计算时长
        computeDurationWithCircuit(context, position.getName(), workoutResVideoList);
        return workoutResVideoList;
    }

    @Override
    public void computeDurationWithCircuit(ProjWorkout116ContextBO context, String position,
                                           List<ProjWorkout116GenerateResVideo116BO> workoutResVideoList) {

        AtomicBoolean front = new AtomicBoolean(true);
        ResVideo116 easyChairVideo = context.getEasyChairVideoMap().get(position);
        workoutResVideoList.forEach(resVideo116 -> {

            // preview duration
            Integer previewDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                    context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, resVideo116.getResVideo116Id(), easyChairVideo.getId());
            resVideo116.setPreviewDuration(previewDuration);

            // circuit duration
            List<Integer> circuitDurationList = new ArrayList<>();
            for (int i = 0; i < resVideo116.getCircuit(); i++) {
                Integer circuitDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                        context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, resVideo116.getResVideo116Id(), easyChairVideo.getId());
                circuitDurationList.add(circuitDuration);
            }
            resVideo116.setCircuitVideoDuration(convertList2String(circuitDurationList));
        });
    }

    private String convertList2String(List<Integer> circuitDurationList) {

        if (CollectionUtils.isEmpty(circuitDurationList)) {
            return "";
        }

        return StringUtils.joinWith(GlobalConstant.COMMA, circuitDurationList.toArray());
    }

    private Integer computeVideoDuration4Circuit(Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap,
                                                 Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap, AtomicBoolean front,
                                                 Integer videoId, Integer easyChairVideoId) {

        List<Integer> videoIdList = Lists.newArrayList(easyChairVideoId, videoId);
        int videoDuration = 0;
        for (Integer video116Id : videoIdList) {
            if (front.get()) {
                videoDuration += frontDurationMap.get(video116Id);
            } else {
                videoDuration += sideDurationMap.get(video116Id);
            }

            if (videoSliceMap.get(video116Id).size() % 2 != 0) {
                if (front.get()) {
                    front.set(false);
                } else {
                    front.set(true);
                }
            }
        }

        return videoDuration;
    }

    @Override
    public void setConfigChairYoga(Map<String, ResVideo116> easyChairMap) {
        //当前只有女声
        Integer seatedId = oog116BizConfig.getChairYoga().getFemaleSoundConfig().getChairYogaVideo4Seated();
        if (Objects.nonNull(seatedId)) {
            ResVideo116 seatedVideo = resVideo116Service.getById(seatedId);
            easyChairMap.put(Position116Enums.SEATED.getName(), seatedVideo);
        }

        Integer standingId = oog116BizConfig.getChairYoga().getFemaleSoundConfig().getChairYogaVideo4Standing();
        if (Objects.nonNull(standingId)) {
            ResVideo116 standingVideo = resVideo116Service.getById(standingId);
            easyChairMap.put(Position116Enums.STANDING.getName(), standingVideo);
        }
    }

    private List<ProjWorkout116GenerateResVideo116BO> assembleVideo4Main(ProjWorkout116ContextBO context,
                                                                         Integer minDuration,
                                                                         Integer maxDuration,
                                                                         List<String> restrictionList,
                                                                         Position116Enums position,
                                                                         ResVideo116TypeEnum video116TypeEnum,
                                                                         Equipment116Enums equipment,
                                                                         ExerciseType116Enums exerciseType,
                                                                         Gender116Enums gender) {

        List<ResVideo116> videoList = context.getVideoList(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        Map<String, List<ResVideo116>> leftRightVideoMap = context.getLeftRightVideoMap(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        ResVideo116 easyChairVideo = context.getEasyChairVideoMap().get(position.getName());

        // 播放轮数
        ProjTemplate116RuleVO rule = context.getRuleVideoTypeMap().get(video116TypeEnum.getValue()).get(0);
        Integer mainVideoRound = rule.getRounds();

        int randomDuration4Main = randomDuration4Main(minDuration, maxDuration);
        List<ResVideo116> matchVideoList = new LinkedList<>();
        Map<TargetEnums, LinkedList<ResVideo116>> targetVideoMap = videoList.stream().collect(Collectors.groupingBy(ResVideo116::getTarget, Collectors.toCollection(LinkedList::new)));
        Map<TargetEnums, Map<String, List<ResVideo116>>> targetLeftVideoMap = groupByTarget(leftRightVideoMap);

        // upper body >= 25%
        LinkedList<ResVideo116> upperVideoList = targetVideoMap.getOrDefault(TargetEnums.UPPER_BODY, Lists.newLinkedList());
        Map<String, List<ResVideo116>> upperLeftVideoMap = targetLeftVideoMap.getOrDefault(TargetEnums.UPPER_BODY, Collections.emptyMap());
        AtomicInteger combineDuration4Main = new AtomicInteger(0);
        int upperBodyDuration = (int) ((int)randomDuration4Main * 0.25);
        matchVideo4Duration(upperBodyDuration, combineDuration4Main, maxDuration, mainVideoRound, matchVideoList, easyChairVideo, upperVideoList, upperLeftVideoMap, context.getVideoSliceDurationMap());

        // lower body >= 30%
        LinkedList<ResVideo116> lowerVideoList = targetVideoMap.getOrDefault(TargetEnums.LOWER_BODY, Lists.newLinkedList());
        Map<String, List<ResVideo116>> lowerLeftVideoMap = targetLeftVideoMap.getOrDefault(TargetEnums.LOWER_BODY, Collections.emptyMap());
        int lowerBodyDuration = (int) ((int)randomDuration4Main * 0.30);
        matchVideo4Duration(lowerBodyDuration, combineDuration4Main, maxDuration, mainVideoRound, matchVideoList, easyChairVideo, lowerVideoList, lowerLeftVideoMap, context.getVideoSliceDurationMap());

        // full body -> other
        LinkedList<ResVideo116> fullVideoList = targetVideoMap.getOrDefault(TargetEnums.FULL_BODY, Lists.newLinkedList());
        Map<String, List<ResVideo116>> fullLeftVideoMap = targetLeftVideoMap.getOrDefault(TargetEnums.FULL_BODY, Collections.emptyMap());
        int fullBodyDuration = randomDuration4Main - combineDuration4Main.intValue();
        matchVideo4Duration(fullBodyDuration, combineDuration4Main, maxDuration, mainVideoRound, matchVideoList, easyChairVideo, fullVideoList, fullLeftVideoMap, context.getVideoSliceDurationMap());

        // 不够返回空集合，由上层调用处理
        if (combineDuration4Main.get() < minDuration) {
            return Collections.emptyList();
        }

        return matchVideoList.stream().map(resVideo -> createProjWorkoutResVideoRelation(context, rule, resVideo)).collect(Collectors.toList());
    }

    private Map<TargetEnums, Map<String, List<ResVideo116>>> groupByTarget(Map<String, List<ResVideo116>> leftRightVideoMap) {

        Map<TargetEnums, Map<String, List<ResVideo116>>> targetLeftVideoMap = new HashMap<>();
        leftRightVideoMap.entrySet().forEach(entry -> {
            TargetEnums targetEnum = entry.getValue().get(0).getTarget();
            if (targetLeftVideoMap.containsKey(targetEnum)) {
                targetLeftVideoMap.get(targetEnum).put(entry.getKey(), entry.getValue());
            } else {
                Map<String, List<ResVideo116>> leftMap = new HashMap<>();
                leftMap.put(entry.getKey(), entry.getValue());
                targetLeftVideoMap.put(targetEnum, leftMap);
            }
        });
        return targetLeftVideoMap;
    }

    // 根据Target 和 时长 获取Video，Video时长和，不可超过最大时长
    private void matchVideo4Duration(int randomDuration4Target,
                                     AtomicInteger combineDuration4Main,
                                     int maxDuration4Main,
                                     Integer mainVideoRound,
                                     List<ResVideo116> matchVideoList,
                                     ResVideo116 easyChairVideo,
                                     LinkedList<ResVideo116> videoList,
                                     Map<String, List<ResVideo116>> leftRightVideoMap,
                                     Map<Integer, Integer> videoSliceDurationMap) {

        if (CollectionUtils.isEmpty(videoList) && MapUtils.isEmpty(leftRightVideoMap)) {
            return;
        }

        Collections.shuffle(videoList);
        LinkedList<String> leftVideoNames = Lists.newLinkedList(leftRightVideoMap.keySet());
        Collections.shuffle(leftVideoNames);

        AtomicInteger combineDuration4Target = new AtomicInteger(GlobalConstant.ZERO);
        while (combineDuration4Target.get() < randomDuration4Target) {

            // 资源不够
            if (CollectionUtils.isEmpty(videoList) && CollectionUtils.isEmpty(leftVideoNames)) {
                break;
            }

            // 挑选正向
            if (RandomUtil.randomBoolean() && CollectionUtils.isNotEmpty(videoList)) {
                ResVideo116 resVideo116 = videoList.pollFirst();

                int videoDuration = (videoSliceDurationMap.get(easyChairVideo.getId()) + videoSliceDurationMap.get(resVideo116.getId())) * (resVideo116.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;
                if (combineDuration4Main.get() + videoDuration > maxDuration4Main) {
                    continue;
                }

                matchVideoList.add(resVideo116);
                combineDuration4Target.addAndGet(videoDuration);
                combineDuration4Main.addAndGet(videoDuration);
                continue;
            }

            // 挑选Left、Right
            if (CollectionUtils.isNotEmpty(leftVideoNames)) {

                String leftVideoName = leftVideoNames.pollFirst();
                // 包含left、right两个video
                List<ResVideo116> resVideo116List = leftRightVideoMap.get(leftVideoName);
                ResVideo116 leftVideo = resVideo116List.get(0);
                int leftVideoDuration = (videoSliceDurationMap.get(easyChairVideo.getId()) + videoSliceDurationMap.get(leftVideo.getId())) * (leftVideo.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;

                ResVideo116 rightVideo = resVideo116List.get(1);
                int rightVideoDuration = (videoSliceDurationMap.get(easyChairVideo.getId()) + videoSliceDurationMap.get(rightVideo.getId())) * (rightVideo.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;

                if (combineDuration4Main.get() + leftVideoDuration + rightVideoDuration > maxDuration4Main) {
                    continue;
                }

                matchVideoList.add(leftVideo);
                combineDuration4Target.addAndGet(leftVideoDuration);
                combineDuration4Main.addAndGet(leftVideoDuration);

                matchVideoList.add(rightVideo);
                combineDuration4Target.addAndGet(rightVideoDuration);
                combineDuration4Main.addAndGet(rightVideoDuration);
            }
        }
    }

    private int randomDuration4Main(int minDuration, int maxDuration) {

        List<Float> floats = Lists.newArrayList(0.1f, 0.3f, 0.5f, 0.7f);
        Collections.shuffle(floats);
        int bufferDuration = maxDuration - minDuration;
        return minDuration + (int) (bufferDuration * floats.get(GlobalConstant.ZERO));
    }

    private List<Integer> computeDurationRange4Main(ProjTemplate116 template116,
                                                    ProjWorkout116ContextBO context,
                                                    Position116Enums position,
                                                    List<ProjWorkout116GenerateResVideo116BO> warmUpVideoList,
                                                    List<ProjWorkout116GenerateResVideo116BO> cooldownVideoList) {

        ResVideo116 easyChairVideo = context.getEasyChairVideoMap().get(position.getName());
        Integer easyChairDuration = context.getVideoSliceDurationMap().get(easyChairVideo.getId());

        int warmUpDuration = warmUpVideoList.stream().mapToInt(video -> (video.getResVideoDuration() + easyChairDuration) * video.getRuleRound() * (video.getCircuit() + PREVIEW_CIRCUIT)).sum();
        int coolDownDuration = cooldownVideoList.stream().mapToInt(video -> (video.getResVideoDuration() + easyChairDuration) * video.getRuleRound() * (video.getCircuit() + PREVIEW_CIRCUIT)).sum();

        String[] durationArray = template116.getDurationRange().split("-");
        int minDuration = convert2MillSecond(Integer.parseInt(durationArray[0]));
        int maxDuration = convert2MillSecond(Integer.parseInt(durationArray[1]));

        int mainMinDuration = minDuration - warmUpDuration - coolDownDuration;
        mainMinDuration = mainMinDuration < 0 ? 0 : mainMinDuration;
        int mainMaxDuration = maxDuration - warmUpDuration - coolDownDuration;
        mainMaxDuration = mainMaxDuration < 0 ? 0 : mainMaxDuration;
        return Lists.newArrayList(mainMinDuration, mainMaxDuration);
    }

    private Integer convert2MillSecond(Integer minutes) {

        return (int) Duration.ofMinutes(minutes).toMillis();
    }



    private List<ProjWorkout116GenerateResVideo116BO> assembleVideo(ProjWorkout116ContextBO context,
                                                                    List<String> restrictionList,
                                                                    Position116Enums position,
                                                                    ResVideo116TypeEnum video116TypeEnum,
                                                                    Equipment116Enums equipment,
                                                                    ExerciseType116Enums exerciseType,
                                                                    Gender116Enums gender) {

        List<ResVideo116> videoList = context.getVideoList(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        Map<String, List<ResVideo116>> leftRightVideoMap = context.getLeftRightVideoMap(
                video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);

        ProjTemplate116RuleVO rule = context.getRuleVideoTypeMap().get(video116TypeEnum.getValue()).get(0);
        List<ResVideo116> ruleVideoList = matchVideo(rule.getCount(), videoList, leftRightVideoMap);
        return ruleVideoList.stream().map(video -> createProjWorkoutResVideoRelation(context, rule, video)).collect(Collectors.toList());
    }


    /**
     * 按照规则设置的数量匹配video
     *
     * @param count             当前规则需要匹配的video数量(最终ruleVideoList的size必须等于count)
     * @param videoList         当前规则可匹配的video
     * @param leftRightVideoMap 当前规则可匹配的left和right的video
     */
    private List<ResVideo116> matchVideo(int count,
                                         List<ResVideo116> videoList,
                                         Map<String, List<ResVideo116>> leftRightVideoMap) {

        if (count <= 0) {
            throw new AutoGenerateException("generate warm up or cool down or main failed for chair yoga.");
        }

        if (CollectionUtils.isEmpty(videoList) && MapUtils.isEmpty(leftRightVideoMap)) {
            throw new AutoGenerateException("generate warm up or cool down or main failed for chair yoga.");
        }
        leftRightVideoMap = null == leftRightVideoMap ? new HashMap<>() : leftRightVideoMap;

        List<String> leftRightVideoKeyList = new ArrayList<>(leftRightVideoMap.keySet());
        Collections.shuffle(leftRightVideoKeyList);

        Collections.shuffle(videoList);
        List<ResVideo116> ruleVideoList = new ArrayList<>();
        int cycles = count * 20;
        while (cycles > 0 && ruleVideoList.size() < count) {
            cycles--;

            int videoSize = videoList.size();
            if ((count - ruleVideoList.size() >= 2 && !leftRightVideoKeyList.isEmpty())
                    && (CollectionUtils.isEmpty(videoList) || (RandomUtil.randomBoolean()) || (videoSize == 1 && NumberUtil.isEven(count - ruleVideoList.size())))) {
                // 包含left和right的
                List<ResVideo116> video116List = leftRightVideoMap.get(leftRightVideoKeyList.remove(0));
                ruleVideoList.addAll(video116List);
                continue;
            }
            if (!videoList.isEmpty()) {
                ruleVideoList.add(videoList.remove(0));
            }
        }
        if (ruleVideoList.size() != count) {
            throw new AutoGenerateException("generate warm up or cool down or main failed,match size not equals rule count");
        }
        return ruleVideoList;
    }

    private ProjWorkout116GenerateResVideo116BO createProjWorkoutResVideoRelation(ProjWorkout116ContextBO context,
                                                                                  ProjTemplate116RuleVO item,
                                                                                  ResVideo116 resVideo116) {

        ProjWorkout116GenerateResVideo116BO projWorkoutResVideo = new ProjWorkout116GenerateResVideo116BO();
        projWorkoutResVideo
                .setRuleVO(item)
                .setRuleRound(item.getRounds())
                .setProjTemplate116Id(context.getProjTemplate116Id())
                .setProjTemplate116RuleId(item.getId())
                .setResVideo116Id(resVideo116.getId())
                .setProjTemplate116TaskId(context.getProjTemplate116TaskId());

        projWorkoutResVideo.setResVideo116(resVideo116);
        projWorkoutResVideo.setCircuit(resVideo116.getCircuit());

        Integer videoDuration = context.getVideoSliceDurationMap().get(resVideo116.getId());
        projWorkoutResVideo.setResVideoDuration(videoDuration);
        return projWorkoutResVideo;
    }

    @Override
    public void generateFile(List<ProjWorkout116GenerateBO> workout116BOList, ProjWorkout116ContextBO contextBO,
                             List<String> languageList, Boolean restrictionFlag) {

        Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap = contextBO.getVideoSliceMap();
        Map<String, ResVideo116> easyChairMap = contextBO.getEasyChairVideoMap();
        Map<Object, ProjResVideo116I18n> videoI18nMap = contextBO.getVideoI18nMap();
        ChairYogaGenerateSoundBO soundBO = createChairSoundBO(languageList);

        int corePoolSize = 50;
        ExecutorService executorService = createExecutor(corePoolSize);
        AtomicBoolean uploadSuccess = new AtomicBoolean(true);
        for (ProjWorkout116GenerateBO workout116BO : workout116BOList) {

            // 失败即终止
            if (!uploadSuccess.get()) {
                log.warn("oog116 chair yoga workout resource upload m3u8 and audio json failed");
                throw new BizException("oog116 chair yoga workout resource upload m3u8 and audio json failed");
            }

            ProjWorkout116Generate workout116Generate = workout116BO.getProjWorkout116Generate();
            TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
            TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
            AtomicBoolean front = new AtomicBoolean(true);
            AtomicBoolean front4Dynamic = new AtomicBoolean(true);

            int workoutDuration = 0;
            BigDecimal workoutCalorie = BigDecimal.ZERO;
            Map<String, List<AudioJson116BO>> languageAudioMap = initLanguageAudioMap(languageList);
            List<ProjWorkout116GenerateResVideo116BO> video116BOList = workout116BO.getProjWorkout116GenerateResVideo116List();
            LinkedList<TaiChiRoundBO> taiChiRoundBOList = wrapRoundBOList4GenerateWorkout(workout116BO.getProjWorkout116GenerateResVideo116List(), contextBO.getVideoIdMap());
            for (TaiChiRoundBO taiChiRoundBO : taiChiRoundBOList) {

                ResVideo116 currentVideo = taiChiRoundBO.getResVideo116();
                Integer currentVideoDuration = taiChiRoundBO.getResVideoDuration();
                ResVideo116 easyVideo = easyChairMap.get(currentVideo.getPosition());
                int videoRound = taiChiRoundBO.getVideoRound();

                boolean lastNode = taiChiRoundBOList.indexOf(taiChiRoundBO) + taiChiRoundBO.getCircuit() == taiChiRoundBOList.size() - 1;
                int easyStartDuration = assemble2532M3u8Text(videoSliceMap.get(easyVideo.getId()), front, tsTextMerge2532BO);
                assemble2532M3u8Text(videoSliceMap.get(currentVideo.getId()), front, tsTextMerge2532BO);

                assembleDynamicM3u8Text(videoSliceMap.get(easyVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);
                assembleDynamicM3u8Text(videoSliceMap.get(currentVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);
                assembleAudio(soundBO, currentVideo, videoI18nMap, languageAudioMap, currentVideoDuration, lastNode, workoutDuration, easyStartDuration, workout116Generate.getGender(), videoRound);

                // 在计算Video卡路里时，已经考虑了circuit 参数
                if (videoRound == 0) {
                    workoutCalorie = workoutCalorie.add(currentVideo.getCalorie());
                }

                workoutDuration += currentVideoDuration;
            }

            // 填充基础属性
            workout116Generate.setCalorie(workoutCalorie.intValue());
            workout116Generate.setDuration(workoutDuration);

            // 填充基础属性
            handleBase4Workout(workout116Generate, workoutCalorie, workoutDuration);
            handleRestriction(restrictionFlag, workout116Generate, video116BOList, workout116BO.getRestrictionList());
            handleLanguage(workout116Generate, languageList);

            // 文件上传
            executorService.execute(() -> uploadFile(workout116BO, uploadSuccess, tsTextMergeDynamicBO, languageAudioMap, tsTextMerge2532BO));
        }

        // 同步等待资源上传
        wait4UploadFile(executorService, uploadSuccess, corePoolSize);
    }

    private void handleLanguage(ProjWorkout116Generate workout116Generate, List<String> languageList) {

        String audioLanguages = workout116Generate.getAudioLanguages();
        if (StringUtils.isNotBlank(audioLanguages)) {
            Arrays.stream(audioLanguages.split(GlobalConstant.COMMA)).filter(language -> !languageList.contains(language)).forEach(languageList::add);
        }

        languageList.sort(String::compareTo);
        workout116Generate.setAudioLanguages(StringUtils.join(languageList, GlobalConstant.COMMA));
    }

    private void handleBase4Workout(ProjWorkout116Generate workout116Generate, BigDecimal workoutCalorie,
                                    int workoutDuration) {

        workout116Generate
                .setCalorie(workoutCalorie.setScale(0, RoundingMode.HALF_UP).intValue())
                .setDuration(workoutDuration)
                .setDataVersion(GlobalConstant.ONE)
                .setFileStatus(GlobalConstant.ONE);
    }

    private void handleRestriction(Boolean restrictionFlag, ProjWorkout116Generate workout116Generate,
                                   List<ProjWorkout116GenerateResVideo116BO> video116BOList, List<Restriction116Enums> restrictionList) {

        if (!restrictionFlag) {
            return;
        }

        Set<String> restrictionSet4Workout = handleRestriction4Workout(video116BOList);
        workout116Generate.setRestriction(String.join(GlobalConstant.COMMA, Restriction116Enums.eliminateRestriction(restrictionSet4Workout)))
                .setRestrictionSum(restrictionList.stream().mapToInt(Restriction116Enums::getValue).sum());
    }

    private Set<String> handleRestriction4Workout(List<ProjWorkout116GenerateResVideo116BO> video116BOList) {

        return video116BOList.stream().filter(video -> StringUtils.isNotBlank(video.getResVideo116().getRestriction()))
                .map(video -> {
                    return Arrays.stream(video.getResVideo116().getRestriction().split(GlobalConstant.COMMA)).filter(str -> !Objects.equals(str, BizConstant.NONE)).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toSet());
    }


    private void wait4UploadFile(ExecutorService executor, AtomicBoolean uploadSuccess, int waitTime) {

        // 停止接收新任务
        executor.shutdown();
        if (!uploadSuccess.get()) {
            executor.shutdownNow();
            throw new BizException("oog116 chair yoga workout upload m3u8 and audio json failed");
        }

        try {
            if (!executor.awaitTermination(waitTime, TimeUnit.MINUTES)) {
                throw new BizException("waiting oog116 chair yoga workout upload m3u8 and audio json overtime");
            }
        } catch (InterruptedException e) {
            throw new BizException("oog116 chair yoga workout executor interrupted exception");
        } finally {
            if (!uploadSuccess.get()) {
                executor.shutdownNow();
                throw new BizException("oog116 chair yoga workout upload m3u8 and audio json failed");
            }
        }
    }

    private ExecutorService createExecutor(int corePoolSize) {

        ExecutorService executor = new ThreadPoolExecutor(corePoolSize, corePoolSize, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(corePoolSize * 8), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    /**
     * 上传m3u8和audio json，同时将返回的url设置到ProjWorkout116Generate实体内    private List<TsMergeBO> videoList;
     * private List<AudioJson116BO> audioList;
     */
    private void uploadFile(ProjWorkout116GenerateBO workoutBO, AtomicBoolean uploadSuccess,
                            TsTextMergeBO tsTextMergeBO, Map<String, List<AudioJson116BO>> audioListMap,
                            TsTextMergeBO tsTextMerge2532BO) {

        if (!uploadSuccess.get()) {
            throw new BizException("oog116 upload m3u8 and audio json failed");
        }

        try {
            ProjWorkout116Generate projWorkout116Generate = workoutBO.getProjWorkout116Generate();

            // 上传m3u8，并保存相对地址
            UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(tsTextMergeBO, "project-workout116-m3u8");
            UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8(tsTextMerge2532BO, "project-workout116-m3u8");
            projWorkout116Generate.setVideoUrl(videoR2Info.getFileRelativeUrl()).setVideo2532Url(video2532R2Info.getFileRelativeUrl());

            List<ProjWorkout116GenerateI18n> generateI18nList = Lists.newArrayList();
            audioListMap.entrySet().stream().forEach(entry -> {

                // 上传音频JSON
                String language = entry.getKey();
                List<AudioJson116BO> audioList = entry.getValue();
                UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioList), "project-workout116-json");

                // 保存音频相对地址
                if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                    projWorkout116Generate.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                } else {
                    ProjWorkout116GenerateI18n generateI18n = new ProjWorkout116GenerateI18n();
                    generateI18n.setLanguage(language);
                    generateI18n.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                    generateI18nList.add(generateI18n);
                }
            });
            workoutBO.setI18nList(generateI18nList);

        } catch (Exception e) {
            uploadSuccess.set(false);
            log.warn("oog116 upload failed", e);
        }
    }

    private LinkedList<TaiChiRoundBO> wrapRoundBOList4GenerateWorkout(List<ProjWorkout116GenerateResVideo116BO> video116BOList, Map<Integer, ResVideo116> videoIdMap) {

        LinkedList<TaiChiRoundBO> taiChiRoundBOList = Lists.newLinkedList();
        List<ProjTemplate116RuleVO> ruleVOList = video116BOList.stream().map(ProjWorkout116GenerateResVideo116BO::getRuleVO).distinct().collect(Collectors.toList());
        Map<ProjTemplate116RuleVO, List<ProjWorkout116GenerateResVideo116BO>> ruleAndVideoMap = video116BOList.stream().collect(Collectors.groupingBy(ProjWorkout116GenerateResVideo116BO::getRuleVO));

        for (ProjTemplate116RuleVO ruleVO : ruleVOList) {
            List<ProjWorkout116GenerateResVideo116BO> ruleVideo116BOList = ruleAndVideoMap.get(ruleVO);
            for (int roundIndex = 0; roundIndex < ruleVO.getRounds(); roundIndex++) {
                for (ProjWorkout116GenerateResVideo116BO resVideo116BO : ruleVideo116BOList) {
                    int[] circuitDurations = Arrays.stream(resVideo116BO.getCircuitVideoDuration().split(GlobalConstant.COMMA)).mapToInt(Integer::parseInt).toArray();
                    for (int circuitIndex = -1; circuitIndex < resVideo116BO.getCircuit(); circuitIndex++) {

                        int videoDuration;
                        if (circuitIndex == -1) {
                            videoDuration = resVideo116BO.getPreviewDuration();
                        } else {
                            videoDuration = circuitDurations[circuitIndex];
                        }

                        TaiChiRoundBO taiChiRoundBO = new TaiChiRoundBO(resVideo116BO.getResVideo116(), roundIndex, circuitIndex, videoDuration);
                        taiChiRoundBOList.add(taiChiRoundBO);
                    }
                }
            }
        }

        return taiChiRoundBOList;
    }

    private Map<String, List<AudioJson116BO>> initLanguageAudioMap(List<String> languageList) {

        LinkedHashMap<String, List<AudioJson116BO>> audioListMap = new LinkedHashMap<>();
        for (String language : languageList) {
            audioListMap.put(language, new ArrayList<>(64));
        }
        return audioListMap;
    }


    @Override
    public void assembleDynamicM3u8Text(List<ResVideo116SliceDetailVO> sliceDetailVOList, AtomicBoolean front, TsTextMergeBO tsTextMergeDynamicBO) {

        sliceDetailVOList.stream().sorted(Comparator.comparing(ResVideo116SliceDetailVO::getSliceIndex)).forEach(detailVO -> {
            if (front.get()) {
                tsTextMergeDynamicBO.addM3u8Text(detailVO.getFrontM3u8Text2k(), detailVO.getFrontM3u8Text1080p(), detailVO.getFrontM3u8Text720p(), detailVO.getFrontM3u8Text480p(), detailVO.getFrontM3u8Text360p());
                front.set(false);
            } else {
                tsTextMergeDynamicBO.addM3u8Text(detailVO.getSideM3u8Text2k(), detailVO.getSideM3u8Text1080p(), detailVO.getSideM3u8Text720p(), detailVO.getSideM3u8Text480p(), detailVO.getSideM3u8Text360p());
                front.set(true);
            }
        });

    }

    @Override
    public int assemble2532M3u8Text(List<ResVideo116SliceDetailVO> sliceDetailVOList, AtomicBoolean front, TsTextMergeBO tsTextMerge2532BO) {

        AtomicInteger duration = new AtomicInteger(0);
        sliceDetailVOList.stream().sorted(Comparator.comparing(ResVideo116SliceDetailVO::getSliceIndex)).forEach(detailVO -> {
            if (front.get()) {
                tsTextMerge2532BO.addM3u8Text(detailVO.getFrontM3u8Text2532());
                duration.addAndGet(detailVO.getFrontVideoDuration());
                front.set(false);
            } else {
                tsTextMerge2532BO.addM3u8Text(detailVO.getSideM3u8Text2532());
                duration.addAndGet(detailVO.getSideVideoDuration());
                front.set(true);
            }
        });
        return duration.get();
    }

    @Override
    public void assembleAudio(ChairYogaGenerateSoundBO soundBO, ResVideo116 currentVideo,
                              Map<Object, ProjResVideo116I18n> videoI18nMap,
                              Map<String, List<AudioJson116BO>> languageAudioJsonMap,
                              Integer currentVideoDuration, boolean lastNode,
                              int workoutDuration, int easyStartDuration, String gender, int circuitIndex) {

        languageAudioJsonMap.entrySet().forEach(entry -> {

            ChairYogaGenerateSoundBO i18nSoundBO = soundBO.getI18nAudioMap().getOrDefault(entry.getKey(), soundBO);
            String guidanceUrl = getI18nGuidance(currentVideo, videoI18nMap, entry.getKey());
            String nameUrl = getI18nName(currentVideo, videoI18nMap, entry.getKey());

            int playTime = workoutDuration + GlobalConstant.HUNDRED;
            List<AudioJson116BO> guidanceAudioList = entry.getValue();
            boolean firstNode = guidanceAudioList.size() == 0;
            if (circuitIndex == -1) {

                // first audio
                if (firstNode) {
                    AudioJson116BO firstAudio = i18nSoundBO.getFirstAudio();
                    addSysAudioJson(guidanceAudioList, firstAudio, playTime, gender);
                    playTime += firstAudio.getDuration().intValue();
                }

                // next audio or last audio
                if (!firstNode) {
                    AudioJson116BO playAudio = lastNode ? i18nSoundBO.getLastAudio() : i18nSoundBO.getNextAudio();
                    addSysAudioJson(guidanceAudioList, playAudio, playTime, gender);
                    playTime += playAudio.getDuration().intValue();
                }

                // name audio
                playTime += GlobalConstant.THOUSAND;
                AudioJson116BO nameAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(nameUrl), fileService.getAbsoluteR2Url(nameUrl), FireBaseUrlSubUtils.getFileName(nameUrl), null, null, false,null,null,null);
                addSysAudioJson(guidanceAudioList, nameAudio, playTime, gender);

                // guidance audio
                playTime = workoutDuration + easyStartDuration + GlobalConstant.HUNDRED;
                AudioJson116BO guidanceAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(guidanceUrl), fileService.getAbsoluteR2Url(guidanceUrl), FireBaseUrlSubUtils.getFileName(guidanceUrl), null, null, true,null,null,null);
                addSysAudioJson(guidanceAudioList, guidanceAudio, playTime, gender);
            } else {

                List<AudioJson116BO> startAudioList = i18nSoundBO.getStartAudioList4First();
                AudioJson116BO startAudio = randomSelectOne(startAudioList);
                addSysAudioJson(guidanceAudioList, startAudio, playTime, gender);

                // guidance audio
                playTime = workoutDuration + easyStartDuration + GlobalConstant.HUNDRED;
                AudioJson116BO guidanceAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(guidanceUrl), fileService.getAbsoluteR2Url(guidanceUrl), FireBaseUrlSubUtils.getFileName(guidanceUrl), null, null, true,null,null,null);
                addSysAudioJson(guidanceAudioList, guidanceAudio, playTime, gender);
            }
        });
    }

    private AudioJson116BO randomSelectOne(List<AudioJson116BO> audioJsonBOList) {

        Collections.shuffle(audioJsonBOList);
        return audioJsonBOList.get(GlobalConstant.ZERO);
    }

    private String getI18nGuidance(ResVideo116 currentVideo, Map<Object, ProjResVideo116I18n> videoI18nMap, String language) {

        String guidanceUrl = currentVideo.getGuidanceAudioUrl();
        ProjResVideo116I18n i18nVideoMap = videoI18nMap.get(currentVideo.getId());
        Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = i18nVideoMap.getGuidanceResult() == null ? MapUtil.empty() :
                i18nVideoMap.getGuidanceResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
        LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
        if (MapUtils.isNotEmpty(guidanceMap) && guidanceMap.containsKey(le)) {
            AudioTranslateResultModel audioTranslateResultModel = guidanceMap.get(le);
            guidanceUrl = audioTranslateResultModel.getAudioUrl();
        }
        return guidanceUrl;
    }

    private String getI18nName(ResVideo116 currentVideo, Map<Object, ProjResVideo116I18n> videoI18nMap, String language) {

        String nameAudioUrl = currentVideo.getNameAudioUrl();
        ProjResVideo116I18n i18nVideoMap = videoI18nMap.get(currentVideo.getId());
        Map<LanguageEnums, AudioTranslateResultModel> nameMap = i18nVideoMap.getResult() == null ? MapUtil.empty() :
                i18nVideoMap.getResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
        LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
        if (MapUtils.isNotEmpty(nameMap) && nameMap.containsKey(le)) {
            AudioTranslateResultModel audioTranslateResultModel = nameMap.get(le);
            nameAudioUrl =  audioTranslateResultModel.getAudioUrl();
        }
        return nameAudioUrl;
    }

    private void addSysAudioJson(List<AudioJson116BO> audioList, AudioJson116BO sysAudioJson, int playTime, String gender) {

        if (MALE.getName().equals(gender)) {
            sysAudioJson = sysAudioJson.toMaleAudioJson();
        }

        AudioJson116BO audioJsonBO = new AudioJson116BO(sysAudioJson.getId(), sysAudioJson.getUrl(), sysAudioJson.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP), sysAudioJson.getDuration(), sysAudioJson.isClose(),null,null,null);
        audioList.add(audioJsonBO);
    }


    @Override
    public ChairYogaGenerateSoundBO createChairSoundBO(List<String> languageList) {

        // 系统音获取并验证是否完整(系统音不区分语言)
        ChairYogaConfigWrapper chairYogaConfigWrapper = oog116BizConfig.getChairYoga();
        AudioJson116AllBO firstAudio_en = chairYogaConfigWrapper.getFirstAudio();
        AudioJson116AllBO lastAudio_en = chairYogaConfigWrapper.getLastAudio();
        AudioJson116AllBO nextAudio_en = chairYogaConfigWrapper.getNextAudio();
        //当前只有女声
        ChairYogaGenerateSoundBO chairYogaGenerateSoundBO = new ChairYogaGenerateSoundBO();
        chairYogaGenerateSoundBO.setFirstAudio(firstAudio_en.getFemaleAudioJson116BO());
        chairYogaGenerateSoundBO.setNextAudio(nextAudio_en.getFemaleAudioJson116BO());
        chairYogaGenerateSoundBO.setLastAudio(lastAudio_en.getFemaleAudioJson116BO());

        List<AudioJson116BO> startAudioEnList4First = chairYogaConfigWrapper.getAudioList();
        chairYogaGenerateSoundBO.setStartAudioList4First(startAudioEnList4First);

        // 获取系统音多语言
        List<AudioJson116BO> soundList = Lists.newArrayList(firstAudio_en.getFemaleAudioJson116BO(), nextAudio_en.getFemaleAudioJson116BO(), lastAudio_en.getFemaleAudioJson116BO());
        soundList.addAll(startAudioEnList4First);
        Map<Integer, Map<String, AudioJson116BO>> audioJson116I18nMap = workout116GenerateService.getSoundI18n(soundList, languageList);

        // 根据语言匹配对应的音频
        Map<String, ChairYogaGenerateSoundBO> languageAudioMap = Maps.newHashMap();
        languageAudioMap.put(GlobalConstant.DEFAULT_LANGUAGE, chairYogaGenerateSoundBO);
        languageList.stream().filter(language -> !Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE))
                .forEach(language -> {
           //现在只有女声
            AudioJson116BO firstAudio = audioJson116I18nMap.get(firstAudio_en.getFemaleAudioJson116BO().getSoundId()).get(language);
            AudioJson116BO nextAudio = audioJson116I18nMap.get(nextAudio_en.getFemaleAudioJson116BO().getSoundId()).get(language);
            AudioJson116BO lastAudio = audioJson116I18nMap.get(lastAudio_en.getFemaleAudioJson116BO().getSoundId()).get(language);

            List<AudioJson116BO> startAudioList4First = startAudioEnList4First.stream().map(audio -> audioJson116I18nMap.get(audio.getSoundId()).get(language)).collect(Collectors.toList());
            ChairYogaGenerateSoundBO soundBO = new ChairYogaGenerateSoundBO().setFirstAudio(firstAudio).setNextAudio(nextAudio)
                    .setLastAudio(lastAudio).setStartAudioList4First(startAudioList4First);
            languageAudioMap.put(language, soundBO);
        });

        chairYogaGenerateSoundBO.setI18nAudioMap(languageAudioMap);
        return chairYogaGenerateSoundBO;
    }
}
