package com.laien.web.biz.proj.oog116.request;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.base.Joiner;
import com.laien.common.core.converter.GenericEnumNameConverter;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.web.biz.core.converter.GenericEnumListNameConverter;
import com.laien.web.biz.core.converter.StringStringTrimArrayConverter;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.biz.proj.oog116.validation.ResVideo116Restrictions;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import com.laien.web.frame.validation.Group3;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * res_video106
 * </p>
 * 使用easyexcel不能使用@Accessors
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@ApiModel(value = "ResVideo116ImportReq", description = "ResVideo116ImportReq")
public class ResVideo116ImportReq {

    @ApiModelProperty(value = "id")
    @ExcelProperty(value = "id")
    private Integer id;

    @NotEmpty(message = "target cannot be empty", groups = Group1.class)
    @Pattern(message = "target The naming rule is incorrect", regexp = "\\b(Full Body|Upper Body|Lower Body)\\b", groups = Group3.class)
    @ApiModelProperty(value = "target")
    @ExcelProperty(value = "target")
    private String target;

    @NotEmpty(message = "guidance cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "guidance")
    @ExcelProperty(value = "guidance")
    @Length(message = "The guidance cannot exceed 1000 characters", min = 1, max = 1000, groups = Group2.class)
    private String guidance;

    @Min(message = "circuit must be greater than or equal to 1", value = 1, groups = Group2.class)
    @Max(message = "circuit cannot exceed 5 ", value = 5, groups = Group3.class)
    @ExcelProperty(value = "circuit")
    @Pattern(message = "circuit cannot exceed 5", regexp = "^[1-5]$", groups = Group3.class)
    @ApiModelProperty(value = "circuit,1-5的整数")
    private String circuit;

    @NotEmpty(message = "name cannot be empty", groups = Group1.class)
    @Length(message = "The name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ApiModelProperty(value = "动作名称")
    @ExcelProperty(value = "name", converter = StringStringTrimConverter.class)
    private String name;

    @ApiModelProperty(value = "event name")
    @ExcelIgnore
    private String eventName;

    @NotEmpty(message = "coverImgUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "cover_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "Image地址")
    private String coverImgUrl;

    @NotEmpty(message = "type cannot be empty", groups = Group1.class)
    @Pattern(message = "type The naming rule is incorrect", regexp = "\\b(Warm Up|Cool Down|Main)\\b", groups = Group3.class)
    @ExcelProperty(value = "type", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "Warm Up、Cool Down、Main")
    private String type;

    @NotEmpty(message = "position cannot be empty", groups = Group1.class)
    @Pattern(message = "position The naming rule is incorrect", regexp = "\\b(Seated|Standing)\\b", groups = Group3.class)
    @ExcelProperty(value = "position", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "部位Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制,多个用英文逗号分隔，取值Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    @ResVideo116Restrictions(message = "restriction format error", groups = Group3.class)
    @ExcelProperty(value = "restriction", converter = StringStringTrimArrayConverter.class)
    private String restriction;

    @ApiModelProperty(value = "动作简介（How To Do）")
    @NotEmpty(message = "instructions cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "howtodo_text", converter = StringStringTrimConverter.class)
    @Length(message = "The instructions cannot exceed 1000 characters", min = 1, max = 1000, groups = Group2.class)
    private String instructions;

    @ExcelProperty(value = "main_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "正机位视频")
    private String frontVideoUrl;

    @ExcelProperty(value = "main_duration")
    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontDuration;

    @ExcelProperty(value = "core_voice_config_i18n_name",converter = StringStringTrimConverter.class)
    @NotEmpty(message = "core_voice_config_i18n_name cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "翻译声音")
    private String coreVoiceConfigI18nName;

    @ExcelProperty(value = "assist_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ExcelProperty(value = "assist_duration")
    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    @ExcelIgnore
    private String videoUrl;

    @NotEmpty(message = "nameAudioUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "name_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;



    @ExcelProperty(value = "name_url_duration")
    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioUrlDuration;

    @NotEmpty(message = "guidenceAudioUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "guidance_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "Guidence音频")
    private String guidanceAudioUrl;

    @NotEmpty(message = "instructionsAudioUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "howtodo_url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "Instructions的音频")
    private String instructionsAudioUrl;

    @NotNull(message = "met cannot be empty", groups = Group1.class)
    @Min(message = "met must be greater than or equal to 1", value = 1, groups = Group2.class)
    @Max(message = "met cannot exceed 12 ", value = 12, groups = Group3.class)
    @ExcelProperty(value = "met")
    @ApiModelProperty(value = "met,1-12的整数")
    @Pattern(message = "circuit cannot exceed 5", regexp = "^(1[0-2]|[1-9])$", groups = Group3.class)
    private String met;

    @ApiModelProperty(value = "卡路里")
    @ExcelIgnore
    private BigDecimal calorie;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    @ExcelIgnore
    private Integer status;

    @ApiModelProperty(value = "性别，Female/Male")
    @NotBlank(message = "性别不能为空", groups = Group1.class)
    @Pattern(
            regexp = "Female|Male",
            message = "性别必须是 Female、Male 之一",
            groups = Group2.class
    )
    @ExcelProperty(value = "gender", converter = StringStringTrimConverter.class)
    private String gender;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    @ExcelProperty(value = "exercise_type", converter = StringStringTrimConverter.class)
    private String exerciseType;

    @ApiModelProperty(value = "身体部位，多个用英文逗号分隔，取值Neck, Shoulder, Wrist, Back, SI Joint, Knee, Hip, Ankle")
    @ExcelProperty(value = "region", converter = GenericEnumListNameConverter.class)
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型，多个用英文逗号分隔，取值Relax, Mobility, Strength")
    @ExcelProperty(value = "focus", converter = GenericEnumListNameConverter.class)
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具，取值Chair, Mat, None")
    @ExcelProperty(value = "support_prop", converter = GenericEnumNameConverter.class)
    private SupportProp116Enums supportProp;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    @ExcelIgnore
    private String equipment;

    public String getEquipment() {
        return equipment;
    }

    public void setEquipment(String equipment) {
        this.equipment = equipment;
    }

    public String getGender() {
        return gender;
    }

    public Integer getNameAudioUrlDuration() {
        return nameAudioUrlDuration;
    }

    public void setNameAudioUrlDuration(Integer nameAudioUrlDuration) {
        this.nameAudioUrlDuration = nameAudioUrlDuration;
    }
    public String getExerciseType() {

        // 此字段为多选，这里按自然顺序重排序后再返回给使用者
        return Optional.ofNullable(exerciseType).filter(StringUtils::isNotBlank).map(et -> {
            List<String> list = CollUtil.newArrayList(et.split(GlobalConstant.COMMA));
            Collections.sort(list);
            return Joiner.on(GlobalConstant.COMMA).join(list);
        }).orElse(null);

    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public void setExerciseType(String exerciseType) {
        this.exerciseType = exerciseType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getCoverImgUrl() {
        return coverImgUrl;
    }

    public void setCoverImgUrl(String coverImgUrl) {
        this.coverImgUrl = coverImgUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getRestriction() {
        return restriction;
    }

    public void setRestriction(String restriction) {
        this.restriction = restriction;
    }

    public String getInstructions() {
        return instructions;
    }

    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    public String getFrontVideoUrl() {
        return frontVideoUrl;
    }

    public void setFrontVideoUrl(String frontVideoUrl) {
        this.frontVideoUrl = frontVideoUrl;
    }

    public Integer getFrontDuration() {
        return frontDuration;
    }

    public void setFrontDuration(Integer frontDuration) {
        this.frontDuration = frontDuration;
    }

    public String getSideVideoUrl() {
        return sideVideoUrl;
    }

    public void setSideVideoUrl(String sideVideoUrl) {
        this.sideVideoUrl = sideVideoUrl;
    }

    public Integer getSideDuration() {
        return sideDuration;
    }

    public void setSideDuration(Integer sideDuration) {
        this.sideDuration = sideDuration;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getNameAudioUrl() {
        return nameAudioUrl;
    }

    public void setNameAudioUrl(String nameAudioUrl) {
        this.nameAudioUrl = nameAudioUrl;
    }

    public String getGuidanceAudioUrl() {
        return guidanceAudioUrl;
    }

    public void setGuidanceAudioUrl(String guidanceAudioUrl) {
        this.guidanceAudioUrl = guidanceAudioUrl;
    }

    public String getInstructionsAudioUrl() {
        return instructionsAudioUrl;
    }

    public void setInstructionsAudioUrl(String instructionsAudioUrl) {
        this.instructionsAudioUrl = instructionsAudioUrl;
    }
    public String getCoreVoiceConfigI18nName() {
        return coreVoiceConfigI18nName;
    }

    public void setCoreVoiceConfigI18nName(String coreVoiceConfigI18nName) {
        this.coreVoiceConfigI18nName = coreVoiceConfigI18nName;
    }
    public String getMet() {
        return met;
    }

    public void setMet(String met) {
        this.met = met;
    }

    public BigDecimal getCalorie() {
        return calorie;
    }

    public void setCalorie(BigDecimal calorie) {
        this.calorie = calorie;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGuidance() {
        return guidance;
    }

    public void setGuidance(String guidance) {
        this.guidance = guidance;
    }

    public String getCircuit() {
        return circuit;
    }

    public void setCircuit(String circuit) {
        this.circuit = circuit;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public List<Focus116Enums> getFocus() {
        return focus;
    }

    public void setFocus(List<Focus116Enums> focus) {
        this.focus = focus;
    }

    public List<Region116Enums> getRegion() {
        return region;
    }

    public void setRegion(List<Region116Enums> region) {
        this.region = region;
    }

    public SupportProp116Enums getSupportProp() {
        return supportProp;
    }

    public void setSupportProp(SupportProp116Enums supportProp) {
        this.supportProp = supportProp;
    }

    public static void main(String[] args) {
        // 使用easy excel导出一个空文件
        // 2. 定义文件路径
        String fileName = "/Users/<USER>/Desktop/resVideo116ImportTemplate.xlsx";

        // 3. 写入 Excel 文件
        EasyExcel.write(fileName, ResVideo116ImportReq.class)
                .sheet("resVideo116ImportTemplate") // 指定 sheet 名称
                .doWrite(Collections.EMPTY_LIST); // 写入数据
    }

}
