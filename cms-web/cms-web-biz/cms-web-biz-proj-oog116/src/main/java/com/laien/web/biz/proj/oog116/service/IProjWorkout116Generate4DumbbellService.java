package com.laien.web.biz.proj.oog116.service;

import com.laien.common.oog116.enums.Equipment116Enums;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.common.oog116.enums.Position116Enums;
import com.laien.common.oog116.enums.Restriction116Enums;
import com.laien.web.biz.proj.oog116.bo.ProjWorkout116ContextBO;
import com.laien.web.biz.proj.oog116.bo.ProjWorkout116GenerateBO;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/7/16
 */

public interface IProjWorkout116Generate4DumbbellService {

    List<ProjWorkout116GenerateBO> generateWorkoutByPlan(ExerciseType116Enums exerciseType,
                                                         Equipment116Enums equipment,
                                                         List<Restriction116Enums> restrictionList,
                                                         Position116Enums position,
                                                         ProjWorkout116ContextBO context);

    void generateFile(List<ProjWorkout116GenerateBO> projWorkout116BOList, ProjWorkout116ContextBO contextBO,
                      List<String> languageList, Boolean restrictionFlag);

}
