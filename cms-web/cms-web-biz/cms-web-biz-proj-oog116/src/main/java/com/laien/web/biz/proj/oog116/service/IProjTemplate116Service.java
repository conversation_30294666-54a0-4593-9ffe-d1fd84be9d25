package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116PageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * template116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjTemplate116Service extends IService<ProjTemplate116> {

    /**
     * Template116 分页
     *
     * @param pageReq pageReq
     * @return ProjTemplate116PageVO
     */
    PageRes<ProjTemplate116PageVO> selectTemplate116Page(ProjTemplate116PageReq pageReq);

    /**
     * Template116 新增
     *
     * @param template116AddReq template116AddReq
     */
    void saveTemplate116(ProjTemplate116AddReq template116AddReq);

    /**
     * Template116 修改
     *
     * @param template116UpdateReq template116UpdateReq
     */
    void updateTemplate116(ProjTemplate116UpdateReq template116UpdateReq);

    /**
     * Template116 新增
     *
     * @param id id
     * @return ProjTemplate116DetailVO
     */
    ProjTemplate116DetailVO getTemplate116Detail(Integer id);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    List<Integer> queryIdList(Integer status);


}
