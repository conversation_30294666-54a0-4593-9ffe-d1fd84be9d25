package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ResVideo116Slice;
import com.laien.web.biz.proj.oog116.mapper.ResVideo116SliceMapper;
import com.laien.web.biz.proj.oog116.request.ResVideo116SliceImportReq;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.proj.oog116.service.IResVideo116SliceService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/2/26 16:19
 */
@Service
public class ResVideo116SliceServiceImpl extends ServiceImpl<ResVideo116SliceMapper, ResVideo116Slice> implements IResVideo116SliceService {

    @Resource
    private ResVideo116SliceMapper resVideo116SliceMapper;

    @Override
    public void deleteByResVideoId(Integer resVideoId) {

        if (Objects.isNull(resVideoId)) {
            return;
        }

        LambdaUpdateWrapper<ResVideo116Slice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ResVideo116Slice::getResVideo116Id, resVideoId);
        updateWrapper.eq(ResVideo116Slice::getDelFlag, GlobalConstant.NO);

        updateWrapper.set(ResVideo116Slice::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ResVideo116Slice::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ResVideo116Slice::getUpdateUser, RequestContextUtils.getLoginUserName());
        update(updateWrapper);
    }

    @Override
    public List<ResVideo116SliceDetailVO> listByResVideoId(Collection<Integer> resVideoIds) {

        if (CollectionUtils.isEmpty(resVideoIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ResVideo116Slice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ResVideo116Slice::getResVideo116Id, resVideoIds);
        queryWrapper.eq(ResVideo116Slice::getDelFlag, GlobalConstant.NO);
        List<ResVideo116Slice> sliceList = list(queryWrapper);

        if (CollectionUtils.isEmpty(sliceList)) {
            return Collections.emptyList();
        }
        return sliceList.stream().map(this::convert2DetailVO).collect(Collectors.toList());
    }

    private ResVideo116SliceDetailVO convert2DetailVO(ResVideo116Slice resVideo116Slice) {

        ResVideo116SliceDetailVO detailVO = new ResVideo116SliceDetailVO();
        BeanUtils.copyProperties(resVideo116Slice, detailVO);
        return detailVO;
    }

    @Override
    public void videoDurationCount(Collection<Integer> resVideoIds, Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap) {

        LambdaQueryWrapper<ResVideo116Slice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ResVideo116Slice::getResVideo116Id, resVideoIds);
        queryWrapper.eq(ResVideo116Slice::getDelFlag, GlobalConstant.NO);

        List<ResVideo116Slice> sliceList = list(queryWrapper);
        if (CollectionUtils.isEmpty(sliceList)) {
            return;
        }

        Map<Integer, List<ResVideo116Slice>> videoSLiceMap = sliceList.stream().collect(
                Collectors.groupingBy(ResVideo116Slice::getResVideo116Id));

        AtomicBoolean startFromFront = new AtomicBoolean(true);
        AtomicBoolean startFromSide = new AtomicBoolean(false);
        videoSLiceMap.entrySet().forEach(entry -> {
            computeDuration(entry, startFromFront, frontDurationMap);
            computeDuration(entry, startFromSide, sideDurationMap);
        });
    }

    private void computeDuration(Map.Entry<Integer, List<ResVideo116Slice>> entry, AtomicBoolean start, Map<Integer, Integer> durationMap) {
        Integer duration = entry.getValue().stream().sorted(Comparator.comparing(ResVideo116Slice::getSliceIndex)).mapToInt(slice -> {
            if (start.get()) {
                start.set(false);
                return slice.getFrontVideoDuration();
            } else {
                start.set(true);
                return slice.getSideVideoDuration();
            }
        }).sum();
        durationMap.put(entry.getKey(), duration);
    }

    @Override
    public void saveBatch(List<ResVideo116SliceImportReq> sliceImportList) {

        if (CollectionUtils.isEmpty(sliceImportList)) {
            return;
        }

        List<ResVideo116Slice> video116SliceList = sliceImportList.stream().map(validSlice -> {
            ResVideo116Slice resVideo116Slice = new ResVideo116Slice();
            BeanUtils.copyProperties(validSlice, resVideo116Slice);
            resVideo116Slice.setResVideo116Id(validSlice.getResVideo().getId());
            return resVideo116Slice;
        }).collect(Collectors.toList());
        saveBatch(video116SliceList);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateBatch(List<ResVideo116SliceImportReq> sliceImportList) {

        if (CollectionUtils.isEmpty(sliceImportList)) {
            return;
        }

        // 删除已有的关联
        List<Integer> videoIdList = sliceImportList.stream().map(req -> req.getResVideo().getId()).collect(Collectors.toList());
        LambdaUpdateWrapper<ResVideo116Slice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ResVideo116Slice::getResVideo116Id, videoIdList);
        updateWrapper.set(ResVideo116Slice::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ResVideo116Slice::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ResVideo116Slice::getUpdateUser, RequestContextUtils.getLoginUserName());
        baseMapper.update(new ResVideo116Slice(), updateWrapper);

        // 添加新的关联
        saveBatch(sliceImportList);
    }
}
