package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import com.laien.common.oog116.enums.*;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum;
import com.laien.web.biz.proj.oog116.entity.*;
import com.laien.web.biz.proj.oog116.exception.AutoGenerateException;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Generate4DumbbellService;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Generate4TaiChiService;
import com.laien.web.biz.proj.oog116.service.ProjWorkout116AssembleService;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.Gender116Enums.MALE;
import static com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum.*;

/**
 * @author: hhl
 * @date: 2025/7/16
 */
@Slf4j
@Service
public class ProjWorkout116Generate4DumbbellServiceImpl implements IProjWorkout116Generate4DumbbellService {

    @Resource
    IProjWorkout116Generate4TaiChiService generate4TaiChiService;

    @Resource
    ProjWorkout116AssembleService workout116AssembleService;

    private static final int PREVIEW_CIRCUIT = 1;

    @Override
    public List<ProjWorkout116GenerateBO> generateWorkoutByPlan(ExerciseType116Enums exerciseType, Equipment116Enums equipment,
                                                                List<Restriction116Enums> restrictionList, Position116Enums position,
                                                                ProjWorkout116ContextBO context) {

        // 只有male video
        Integer maleDay = exerciseType.getMaleDay();
        List<ProjWorkout116GenerateBO> maleWorkoutList = generateWorkoutByDay(exerciseType, equipment, restrictionList, position, maleDay, context, MALE);
        return maleWorkoutList;
    }

    @Override
    public void generateFile(List<ProjWorkout116GenerateBO> projWorkout116BOList, ProjWorkout116ContextBO contextBO,
                             List<String> languageList, Boolean restrictionFlag) {

        DumbbellGenerateSoundBO soundBO = workout116AssembleService.createDumbbellSoundBO(languageList, MALE.getName());
        List<List<ProjWorkout116ResVideo116>> relationList = convert2Relation(projWorkout116BOList);
        List<BaseWorkoutBO> workoutBOList = workout116AssembleService.generateFile4Dumbbell(contextBO, soundBO, relationList, languageList);

        // set workout info
        for (int i = 0; i < projWorkout116BOList.size(); i++) {
            ProjWorkout116GenerateBO generateBO = projWorkout116BOList.get(i);
            BaseWorkoutBO workoutBO = workoutBOList.get(i);
            setWorkoutInfo(generateBO, workoutBO, restrictionFlag);
        }
    }

    private void setWorkoutInfo(ProjWorkout116GenerateBO generateBO, BaseWorkoutBO workoutBO, Boolean restrictionFlag) {

        generateBO.getProjWorkout116Generate().setCalorie(workoutBO.getCalorie().intValue());
        generateBO.getProjWorkout116Generate().setDuration(workoutBO.getDuration());
        generateBO.getProjWorkout116Generate().setVideoUrl(workoutBO.getVideoUrl());

        generateBO.getProjWorkout116Generate().setVideo2532Url(workoutBO.getVideo2532Url());
        generateBO.getProjWorkout116Generate().setAudioJsonUrl(workoutBO.getAudioJsonUrl());
        generateBO.getProjWorkout116Generate().setDataVersion(GlobalConstant.ONE);
        generateBO.getProjWorkout116Generate().setFileStatus(GlobalConstant.ONE);

        String languageList = workoutBO.getAudioI18nUrl().keySet().stream().collect(Collectors.joining(GlobalConstant.COMMA));
        generateBO.getProjWorkout116Generate().setAudioLanguages(languageList);

        List<ProjWorkout116GenerateI18n> i18nList = workoutBO.getAudioI18nUrl().entrySet().stream().map(entry -> {
            ProjWorkout116GenerateI18n generateI18N = new ProjWorkout116GenerateI18n();
            generateI18N.setLanguage(entry.getKey());
            generateI18N.setAudioJsonUrl(entry.getValue());
            return generateI18N;
        }).collect(Collectors.toList());
        generateBO.setI18nList(i18nList);

        if (restrictionFlag) {
            Set<String> restrictionSet4Workout = handleRestriction4Workout(generateBO.getProjWorkout116GenerateResVideo116List());
            generateBO.getProjWorkout116Generate().setRestriction(String.join(GlobalConstant.COMMA, Restriction116Enums.eliminateRestriction(restrictionSet4Workout)))
                    .setRestrictionSum(generateBO.getRestrictionList().stream().mapToInt(Restriction116Enums::getValue).sum());
        }
    }

    private Set<String> handleRestriction4Workout(List<ProjWorkout116GenerateResVideo116BO> video116BOList) {

        return video116BOList.stream().filter(video -> StringUtils.isNotBlank(video.getResVideo116().getRestriction()))
                .map(video -> {
                    return Arrays.stream(video.getResVideo116().getRestriction().split(GlobalConstant.COMMA)).filter(str -> !Objects.equals(str, BizConstant.NONE)).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toSet());
    }


    private List<List<ProjWorkout116ResVideo116>> convert2Relation(List<ProjWorkout116GenerateBO> projWorkout116BOList) {

        return projWorkout116BOList.stream().map(videoList -> {
            return videoList.getProjWorkout116GenerateResVideo116List().stream().map(video116 -> {
                ProjWorkout116ResVideo116 workout116ResVideo116 = new ProjWorkout116ResVideo116();
                BeanUtils.copyProperties(video116, workout116ResVideo116);
                workout116ResVideo116.setUnitName(video116.getRuleVO().getUnitName());
                workout116ResVideo116.setRounds(video116.getRuleVO().getRounds());
                return workout116ResVideo116;
            }).collect(Collectors.toList());
        }).collect(Collectors.toList());
    }


    private List<ProjWorkout116GenerateBO> generateWorkoutByDay(ExerciseType116Enums exerciseType,
                                                                Equipment116Enums equipment,
                                                                List<Restriction116Enums> restrictionList,
                                                                Position116Enums position,
                                                                Integer day,
                                                                ProjWorkout116ContextBO context,
                                                                Gender116Enums gender) {

        List<ResImage> resImages = context.matchImageList(position, day, exerciseType, gender);
        List<ProjWorkout116GenerateBO> projWorkout116BOList = new ArrayList<>();
        for (int i = 0; i < day; i++) {

            ProjWorkout116GenerateBO projWorkout116BO = new ProjWorkout116GenerateBO();
            try {
                List<ProjWorkout116GenerateResVideo116BO> resVideo116BOList = selectVideo4Workout(context, restrictionList, position, equipment, exerciseType, gender);
                projWorkout116BO.setProjWorkout116GenerateResVideo116List(resVideo116BOList);
            } catch (AutoGenerateException e) {
                log.warn("generate dumbbell workout failed, exercise type : {}, position : {}, gender : {}, restrictionList : {}.", exerciseType, position, gender, restrictionList);
                throw new BizException(String.format("generate dumbbell workout failed."));
            }

            ProjWorkout116Generate workout116Generate = wrapWorkout(context, position, equipment, exerciseType, gender, resImages, i);
            projWorkout116BO.setProjWorkout116Generate(workout116Generate);
            projWorkout116BO.setRestrictionList(restrictionList);
            projWorkout116BOList.add(projWorkout116BO);
        }
        return projWorkout116BOList;
    }


    private ProjWorkout116Generate wrapWorkout(ProjWorkout116ContextBO context,
                                               Position116Enums position,
                                               Equipment116Enums equipment,
                                               ExerciseType116Enums exerciseType,
                                               Gender116Enums gender,
                                               List<ResImage> resImages,
                                               int day) {

        ProjWorkout116Generate projWorkout116 = new ProjWorkout116Generate();
        Integer projTemplate116Id = context.getProjTemplate116Id();
        Integer projTemplate116TaskId = context.getProjTemplate116TaskId();
        ResImage resImage = resImages.get(day % resImages.size());

        projWorkout116
                .setProjTemplate116Id(projTemplate116Id)
                .setProjTemplate116TaskId(projTemplate116TaskId)
                .setPosition(position.getName())
                .setResImageId(resImage.getId())
                .setEquipment(equipment.getName())
                .setExerciseType(exerciseType.getName())
                .setGender(gender.getName());
        return projWorkout116;
    }

    /**
     * 按照排列组合生成一天的workout
     * 先根据配置挑选warm up、cooldown
     * 再根据模板时间剪去已挑选视频时间，进行main视频的挑选
     * <p>
     * <p>
     * 生成workout、workout和video的关系，未入库
     */
    private List<ProjWorkout116GenerateResVideo116BO> selectVideo4Workout(ProjWorkout116ContextBO context,
                                                                          List<Restriction116Enums> restrictionList,
                                                                          Position116Enums position,
                                                                          Equipment116Enums equipment,
                                                                          ExerciseType116Enums exerciseType,
                                                                          Gender116Enums gender) {

        List<String> restrictionNameList = restrictionList.stream().map(Restriction116Enums::getName).collect(Collectors.toList());
        List<ProjWorkout116GenerateResVideo116BO> warmUpVideoList = assembleVideo(context, restrictionNameList, position, WARM_UP, equipment, exerciseType, gender);
        List<ProjWorkout116GenerateResVideo116BO> coolDownVideoList = assembleVideo(context, restrictionNameList, position, COOL_DOWN, equipment, exerciseType, gender);

        if (CollectionUtils.isEmpty(warmUpVideoList) || CollectionUtils.isEmpty(coolDownVideoList)) {
            log.warn("Can't generate workout for dumbbell, no enough video, such as warmup , cooldown.");
            throw new AutoGenerateException("Can't generate workout for dumbbell, no enough video, such as warmup , cooldown.");
        }

        List<Integer> durationRange = computeDurationRange4Main(context.getTemplate116(), warmUpVideoList, coolDownVideoList);
        List<ProjWorkout116GenerateResVideo116BO> mainVideoList = Lists.newArrayList();
        if (durationRange.get(0) > 0) {
            mainVideoList = assembleVideo4Main(context, durationRange.get(0), durationRange.get(1), restrictionNameList, position, MAIN, equipment, exerciseType, gender);
            if (CollectionUtils.isEmpty(mainVideoList)) {
                log.warn("Can't generate workout for dumbbell, no enough video, such as main.");
                throw new AutoGenerateException("Can't generate workout for dumbbell, no enough video, such as main.");
            }
        }

        List<ProjWorkout116GenerateResVideo116BO> workoutResVideoList = Lists.newLinkedList();
        workoutResVideoList.addAll(warmUpVideoList);
        workoutResVideoList.addAll(mainVideoList);
        workoutResVideoList.addAll(coolDownVideoList);

        generate4TaiChiService.computeDurationWithCircuit(context, workoutResVideoList);
        return workoutResVideoList;
    }

    private List<ProjWorkout116GenerateResVideo116BO> assembleVideo4Main(ProjWorkout116ContextBO context,
                                                                         Integer minDuration,
                                                                         Integer maxDuration,
                                                                         List<String> restrictionList,
                                                                         Position116Enums position,
                                                                         ResVideo116TypeEnum video116TypeEnum,
                                                                         Equipment116Enums equipment,
                                                                         ExerciseType116Enums exerciseType,
                                                                         Gender116Enums gender) {

        List<ResVideo116> videoList = context.getVideoList(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        Map<String, List<ResVideo116>> leftRightVideoMap = context.getLeftRightVideoMap(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        ProjTemplate116RuleVO rule = context.getRuleVideoTypeMap().get(video116TypeEnum.getValue()).get(0);
        Integer mainVideoRound = rule.getRounds();

        int randomDuration4Main = randomDuration4Main(minDuration, maxDuration);
        List<ResVideo116> matchVideoList = new LinkedList<>();
        Map<TargetEnums, LinkedList<ResVideo116>> targetVideoMap = videoList.stream().collect(Collectors.groupingBy(ResVideo116::getTarget, Collectors.toCollection(LinkedList::new)));
        Map<TargetEnums, Map<String, List<ResVideo116>>> targetLeftVideoMap = groupByTarget(leftRightVideoMap);

        // full body  40%
        LinkedList<ResVideo116> fullVideoList = targetVideoMap.getOrDefault(TargetEnums.FULL_BODY, Lists.newLinkedList());
        Map<String, List<ResVideo116>> fullLeftVideoMap = targetLeftVideoMap.getOrDefault(TargetEnums.FULL_BODY, Collections.emptyMap());
        AtomicInteger combineDuration4Main = new AtomicInteger(0);
        int fullBodyDuration = (int) ((int) randomDuration4Main * 0.40);
        matchVideo4Duration(fullBodyDuration, combineDuration4Main, maxDuration, mainVideoRound, matchVideoList, fullVideoList, fullLeftVideoMap, context.getVideoSliceDurationMap());

        // upper body 60%
        LinkedList<ResVideo116> upperVideoList = targetVideoMap.getOrDefault(TargetEnums.UPPER_BODY, Lists.newLinkedList());
        Map<String, List<ResVideo116>> upperLeftVideoMap = targetLeftVideoMap.getOrDefault(TargetEnums.UPPER_BODY, Collections.emptyMap());
        int upperBodyDuration = randomDuration4Main - combineDuration4Main.intValue();
        matchVideo4Duration(upperBodyDuration, combineDuration4Main, maxDuration, mainVideoRound, matchVideoList, upperVideoList, upperLeftVideoMap, context.getVideoSliceDurationMap());

        // upper body 不足时, 使用full body
        if (combineDuration4Main.get() < minDuration) {
            fullBodyDuration = randomDuration4Main - combineDuration4Main.intValue();
            matchVideo4Duration(fullBodyDuration, combineDuration4Main, maxDuration, mainVideoRound, matchVideoList, fullVideoList, fullLeftVideoMap, context.getVideoSliceDurationMap());
        }

        // 不够返回空集合，由上层调用处理
        if (combineDuration4Main.get() < minDuration) {
            return Collections.emptyList();
        }
        return matchVideoList.stream().map(resVideo -> createProjWorkoutResVideoRelation(context, rule, resVideo)).collect(Collectors.toList());
    }

    private Map<TargetEnums, Map<String, List<ResVideo116>>> groupByTarget(Map<String, List<ResVideo116>> leftRightVideoMap) {

        Map<TargetEnums, Map<String, List<ResVideo116>>> targetLeftVideoMap = new HashMap<>();
        leftRightVideoMap.entrySet().forEach(entry -> {
            TargetEnums targetEnum = entry.getValue().get(0).getTarget();
            if (targetLeftVideoMap.containsKey(targetEnum)) {
                targetLeftVideoMap.get(targetEnum).put(entry.getKey(), entry.getValue());
            } else {
                Map<String, List<ResVideo116>> leftMap = new HashMap<>();
                leftMap.put(entry.getKey(), entry.getValue());
                targetLeftVideoMap.put(targetEnum, leftMap);
            }
        });
        return targetLeftVideoMap;
    }

    private List<Integer> computeDurationRange4Main(ProjTemplate116 template116,
                                                    List<ProjWorkout116GenerateResVideo116BO> warmUpVideoList,
                                                    List<ProjWorkout116GenerateResVideo116BO> cooldownVideoList) {

        int warmUpDuration = warmUpVideoList.stream().mapToInt(video -> video.getResVideoDuration() * video.getRuleRound() * (video.getCircuit() + PREVIEW_CIRCUIT)).sum();
        int coolDownDuration = cooldownVideoList.stream().mapToInt(video -> video.getResVideoDuration() * video.getRuleRound() * (video.getCircuit() + PREVIEW_CIRCUIT)).sum();

        String[] durationArray = template116.getDurationRange().split("-");
        int minDuration = convert2MillSecond(Integer.parseInt(durationArray[0]));
        int maxDuration = convert2MillSecond(Integer.parseInt(durationArray[1]));

        Integer mainMinDuration = minDuration - warmUpDuration - coolDownDuration;
        Integer mainMaxDuration = maxDuration - warmUpDuration - coolDownDuration;
        return Lists.newArrayList(mainMinDuration, mainMaxDuration);
    }

    private Integer convert2MillSecond(Integer minutes) {

        return (int) Duration.ofMinutes(minutes).toMillis();
    }

    private List<ProjWorkout116GenerateResVideo116BO> assembleVideo(ProjWorkout116ContextBO context,
                                                                    List<String> restrictionList,
                                                                    Position116Enums position,
                                                                    ResVideo116TypeEnum video116TypeEnum,
                                                                    Equipment116Enums equipment,
                                                                    ExerciseType116Enums exerciseType,
                                                                    Gender116Enums gender) {

        List<ResVideo116> videoList = context.getVideoList(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        Map<String, List<ResVideo116>> leftRightVideoMap = context.getLeftRightVideoMap(
                video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);

        ProjTemplate116RuleVO rule = context.getRuleVideoTypeMap().get(video116TypeEnum.getValue()).get(0);
        List<ResVideo116> ruleVideoList = matchVideo(rule.getCount(), videoList, leftRightVideoMap);
        return ruleVideoList.stream().map(video -> createProjWorkoutResVideoRelation(context, rule, video)).collect(Collectors.toList());
    }


    // 根据Target 和 时长 获取Video，Video时长和，不可超过最大时长
    private void matchVideo4Duration(int randomDuration4Target,
                                     AtomicInteger combineDuration4Main,
                                     int maxDuration4Main,
                                     Integer mainVideoRound,
                                     List<ResVideo116> matchVideoList,
                                     LinkedList<ResVideo116> videoList,
                                     Map<String, List<ResVideo116>> leftRightVideoMap,
                                     Map<Integer, Integer> videoSliceDurationMap) {

        if (CollectionUtils.isEmpty(videoList) && MapUtils.isEmpty(leftRightVideoMap)) {
            return;
        }

        Collections.shuffle(videoList);
        LinkedList<String> leftVideoNames = Lists.newLinkedList(leftRightVideoMap.keySet());
        Collections.shuffle(leftVideoNames);

        AtomicInteger combineDuration4Target = new AtomicInteger(GlobalConstant.ZERO);
        while (combineDuration4Target.get() < randomDuration4Target) {

            // 资源不够
            if (CollectionUtils.isEmpty(videoList) && CollectionUtils.isEmpty(leftVideoNames)) {
                break;
            }

            // 挑选正向
            if (RandomUtil.randomBoolean() && CollectionUtils.isNotEmpty(videoList)) {
                ResVideo116 resVideo116 = videoList.pollFirst();

                int videoDuration = (videoSliceDurationMap.get(resVideo116.getId())) * (resVideo116.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;
                if (combineDuration4Main.get() + videoDuration > maxDuration4Main) {
                    continue;
                }

                matchVideoList.add(resVideo116);
                combineDuration4Target.addAndGet(videoDuration);
                combineDuration4Main.addAndGet(videoDuration);
                continue;
            }

            // 挑选Left、Right
            if (CollectionUtils.isNotEmpty(leftVideoNames)) {

                String leftVideoName = leftVideoNames.pollFirst();
                // 包含left、right两个video
                List<ResVideo116> resVideo116List = leftRightVideoMap.get(leftVideoName);
                ResVideo116 leftVideo = resVideo116List.get(0);
                int leftVideoDuration = (videoSliceDurationMap.get(leftVideo.getId())) * (leftVideo.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;

                ResVideo116 rightVideo = resVideo116List.get(1);
                int rightVideoDuration = (videoSliceDurationMap.get(rightVideo.getId())) * (rightVideo.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;

                if (combineDuration4Main.get() + leftVideoDuration + rightVideoDuration > maxDuration4Main) {
                    continue;
                }

                matchVideoList.add(leftVideo);
                combineDuration4Target.addAndGet(leftVideoDuration);
                combineDuration4Main.addAndGet(leftVideoDuration);

                matchVideoList.add(rightVideo);
                combineDuration4Target.addAndGet(rightVideoDuration);
                combineDuration4Main.addAndGet(rightVideoDuration);
            }
        }
    }

    private int randomDuration4Main(int minDuration, int maxDuration) {

        List<Float> floats = Lists.newArrayList(0.1f, 0.3f, 0.5f, 0.7f);
        Collections.shuffle(floats);
        int bufferDuration = maxDuration - minDuration;
        return minDuration + (int) (bufferDuration * floats.get(GlobalConstant.ZERO));
    }

    /**
     * 按照规则设置的数量匹配video
     *
     * @param count             当前规则需要匹配的video数量(最终ruleVideoList的size必须等于count)
     * @param videoList         当前规则可匹配的video
     * @param leftRightVideoMap 当前规则可匹配的left和right的video
     */
    private List<ResVideo116> matchVideo(int count,
                                         List<ResVideo116> videoList,
                                         Map<String, List<ResVideo116>> leftRightVideoMap) {
        if (count <= 0) {
            throw new BizException("generate warm up or cool down or main failed for dumbbell.");
        }

        if (CollectionUtils.isEmpty(videoList) && MapUtils.isEmpty(leftRightVideoMap)) {
            throw new AutoGenerateException("generate warm up or cool down or main failed for dumbbell.");
        }
        leftRightVideoMap = null == leftRightVideoMap ? new HashMap<>() : leftRightVideoMap;

        List<String> leftRightVideoKeyList = new ArrayList<>(leftRightVideoMap.keySet());
        Collections.shuffle(leftRightVideoKeyList);

        Collections.shuffle(videoList);
        List<ResVideo116> ruleVideoList = new ArrayList<>();
        int cycles = count * 20;
        while (cycles > 0 && ruleVideoList.size() < count) {
            cycles--;

            int videoSize = videoList.size();
            if ((count - ruleVideoList.size() >= 2 && !leftRightVideoKeyList.isEmpty())
                    && (CollectionUtils.isEmpty(videoList) || (RandomUtil.randomBoolean()) || (videoSize == 1 && NumberUtil.isEven(count - ruleVideoList.size())))) {
                // 包含left和right的
                List<ResVideo116> video116List = leftRightVideoMap.get(leftRightVideoKeyList.remove(0));
                ruleVideoList.addAll(video116List);
                continue;
            }
            if (!videoList.isEmpty()) {
                ruleVideoList.add(videoList.remove(0));
            }
        }
        if (ruleVideoList.size() != count) {
            log.warn("generate warm up or cool down or main failed,match size not equals rule count");
            throw new AutoGenerateException("generate warm up or cool down or main failed");
        }
        return ruleVideoList;
    }

    private ProjWorkout116GenerateResVideo116BO createProjWorkoutResVideoRelation(ProjWorkout116ContextBO context,
                                                                                  ProjTemplate116RuleVO item,
                                                                                  ResVideo116 resVideo116) {

        ProjWorkout116GenerateResVideo116BO projWorkoutResVideo = new ProjWorkout116GenerateResVideo116BO();
        projWorkoutResVideo
                .setRuleVO(item)
                .setRuleRound(item.getRounds())
                .setProjTemplate116Id(context.getProjTemplate116Id())
                .setProjTemplate116RuleId(item.getId())
                .setResVideo116Id(resVideo116.getId())
                .setProjTemplate116TaskId(context.getProjTemplate116TaskId());

        projWorkoutResVideo.setResVideo116(resVideo116);
        projWorkoutResVideo.setCircuit(resVideo116.getCircuit());

        Integer videoDuration = context.getVideoSliceDurationMap().get(resVideo116.getId());
        projWorkoutResVideo.setResVideoDuration(videoDuration);
        return projWorkoutResVideo;
    }


}
