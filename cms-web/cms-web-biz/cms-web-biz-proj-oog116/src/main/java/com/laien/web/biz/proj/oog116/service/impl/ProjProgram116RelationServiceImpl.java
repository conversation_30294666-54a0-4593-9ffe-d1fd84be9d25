package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ProjProgram116Relation;
import com.laien.web.biz.proj.oog116.mapper.ProjProgram116RelationMapper;
import com.laien.web.biz.proj.oog116.service.IProjProgram116RelationService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Service
@Slf4j
public class ProjProgram116RelationServiceImpl extends ServiceImpl<ProjProgram116RelationMapper, ProjProgram116Relation> implements IProjProgram116RelationService {

    @Override
    public List<ProjProgram116Relation> listByProgramIds(List<Integer> programIds) {

        if (CollectionUtils.isEmpty(programIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjProgram116Relation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjProgram116Relation::getProjProgram116Id, programIds)
                .orderByAsc(BaseModel::getId);
        List<ProjProgram116Relation> relationList = this.list(queryWrapper);
        return relationList;
    }

    @Override
    public void deleteByProgramIds(List<Integer> programIds) {

        if (CollectionUtils.isEmpty(programIds)) {
            return;
        }

        LambdaUpdateWrapper<ProjProgram116Relation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjProgram116Relation::getProjProgram116Id, programIds);
        updateWrapper.set(ProjProgram116Relation::getDelFlag, GlobalConstant.YES);
        this.update(updateWrapper);
    }
}
