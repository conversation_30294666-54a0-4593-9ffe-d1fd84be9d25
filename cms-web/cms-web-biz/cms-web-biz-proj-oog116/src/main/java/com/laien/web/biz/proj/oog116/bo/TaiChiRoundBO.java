package com.laien.web.biz.proj.oog116.bo;

import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2025/3/6 14:09
 */
@NoArgsConstructor
@Data
public class TaiChiRoundBO {

    private ResVideo116 resVideo116;

    private Integer unitRound;

    private Integer videoRound;

    private Integer resVideoDuration;

    private Integer previewDuration;

    private Integer circuit = 1;

    public TaiChiRoundBO(ResVideo116 resVideo116, Integer unitRound, Integer videoRound, Integer resVideoDuration) {
        this.resVideo116 = resVideo116;
        this.circuit = Objects.isNull(resVideo116) ? 1 : resVideo116.getCircuit();
        this.unitRound = unitRound;
        this.videoRound = videoRound;
        this.resVideoDuration = resVideoDuration;
    }

    public TaiChiRoundBO(ResVideo116 resVideo116, Integer unitRound, Integer videoRound, Integer resVideoDuration, Integer previewDuration) {
        this.resVideo116 = resVideo116;
        this.unitRound = unitRound;
        this.videoRound = videoRound;
        this.resVideoDuration = resVideoDuration;
        this.previewDuration = previewDuration;
    }
}
