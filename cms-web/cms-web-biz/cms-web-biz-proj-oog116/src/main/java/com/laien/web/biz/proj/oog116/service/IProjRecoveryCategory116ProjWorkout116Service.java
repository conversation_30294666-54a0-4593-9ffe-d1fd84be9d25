package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjRecoveryCategory116ProjWorkout116;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116DetailWorkoutVO;
import com.laien.web.frame.response.IdAndStatusCountsRes;

import java.util.List;

/**
 * <p>
 * proj_recovery_category116_proj_workout116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface IProjRecoveryCategory116ProjWorkout116Service extends IService<ProjRecoveryCategory116ProjWorkout116> {

    /**
     * 查询workout状态统计
     *
     * @param projId 项目ID
     * @return 状态统计列表
     */
    List<IdAndStatusCountsRes> selectWorkoutStatusCount(Integer projId);

    /**
     * 根据分类ID查询workout列表
     *
     * @param categoryId 分类ID
     * @return workout列表
     */
    List<ProjRecoveryCategory116DetailWorkoutVO> selectWorkoutByCategoryId(Integer categoryId);
}
