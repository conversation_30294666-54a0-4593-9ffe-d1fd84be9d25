package com.laien.web.biz.proj.oog116.config;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog116.bo.AudioJson116BO;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.service.IProjSound116Service;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.exception.BizException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "116 Chair Yoga 音视频配置")
public class ChairYogaConfig {

    @ApiModelProperty(value = "first")
    private String first;

    @ApiModelProperty(value = "next")
    private String next;

    @ApiModelProperty(value = "last")
    private String last;

    private List<String> startList4First;

    private Integer chairYogaVideo4Seated;

    private Integer chairYogaVideo4Standing;


    AudioJson116BO getSysSoundByName(String id, String name) {

        LambdaQueryWrapper<ProjSound116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSound116::getSoundName, name).last("limit 1");
        IProjSound116Service sound116Service = SpringUtil.getBean(IProjSound116Service.class);
        FileService fileService = SpringUtil.getBean(FileService.class);
        ProjSound116 sound = sound116Service.getOne(queryWrapper);
        if (sound == null) {
            throw new BizException("System sound '" + id + "' not find!");
        }
        String soundUrl = sound.getUrl();
        if (StringUtils.isBlank(soundUrl)) {
            throw new BizException("System sound '" + id + "' not set!");
        }

        // 目前只有女声
        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        Integer duration = sound.getDuration();
        String soundId = StringUtils.isBlank(id) ? soundName : id;

        return new AudioJson116BO(
                soundId,
                fileService.getAbsoluteR2Url(soundUrl),
                soundName,
                BigDecimal.ZERO,
                duration,
                false,
                sound.getId(),
                sound.getNeedTranslation(),
                sound.getGender(),
                sound.getSoundScript(),
                sound.getCoreVoiceConfigI18nId()
        );
    }

}
