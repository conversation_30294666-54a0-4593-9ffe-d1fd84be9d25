package com.laien.web.biz.proj.oog116.response;

import com.laien.common.oog116.enums.Template116TypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: Template116 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Template116 详情", description = "Template116 详情")
public class ProjTemplate116DetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "时长区间")
    private String durationRange;

    @ApiModelProperty(value = "模板类型")
    private Template116TypeEnums templateType;

    @ApiModelProperty(value = "生成多少天的")
    private Integer day;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "warmUp 规则列表")
    private List<ProjTemplate116RuleDetailVO> warmUpRuleList;
    @ApiModelProperty(value = "main 规则列表")
    private List<ProjTemplate116RuleDetailVO> mainRuleList;
    @ApiModelProperty(value = "coolDown 规则列表")
    private List<ProjTemplate116RuleDetailVO> coolDownRuleList;

}
