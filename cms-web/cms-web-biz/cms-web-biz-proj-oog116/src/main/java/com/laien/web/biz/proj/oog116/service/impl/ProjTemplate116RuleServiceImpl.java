package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116Rule;
import com.laien.web.biz.proj.oog116.mapper.ProjTemplate116RuleMapper;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116RuleService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * proj_template116_rule 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjTemplate116RuleServiceImpl extends ServiceImpl<ProjTemplate116RuleMapper, ProjTemplate116Rule> implements IProjTemplate116RuleService {

    @Override
    public List<ProjTemplate116RuleVO> findByProjTemplate116Id(Integer projTemplate116Id) {
        LambdaQueryWrapper<ProjTemplate116Rule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjTemplate116Rule::getProjTemplate116Id, projTemplate116Id);
        List<ProjTemplate116Rule> projTemplate116Rules = baseMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(projTemplate116Rules)){
            return null;
        }
        ArrayList<ProjTemplate116RuleVO> ruleList = new ArrayList<>(projTemplate116Rules.size());
        projTemplate116Rules.forEach(item -> {
            ProjTemplate116RuleVO projTemplate116RuleVO = new ProjTemplate116RuleVO();
            BeanUtils.copyProperties(item, projTemplate116RuleVO);
            ruleList.add(projTemplate116RuleVO);
        });
        return ruleList;
    }
}
