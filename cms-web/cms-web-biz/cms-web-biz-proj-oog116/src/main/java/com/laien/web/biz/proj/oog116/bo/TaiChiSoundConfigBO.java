package com.laien.web.biz.proj.oog116.bo;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.service.IProjSound116Service;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.exception.BizException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video111 生成系统音配置
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video106 生成系统音配置", description = "video106 生成系统音配置")
public class TaiChiSoundConfigBO {

    @ApiModelProperty(value = "first")
    private String first;

    @ApiModelProperty(value = "next")
    private String next;

    @ApiModelProperty(value = "last")
    private String last;

    @ApiModelProperty(value = "promptList4Second")
    private List<String> promptList4Second;

    @ApiModelProperty(value = "startList4First")
    private List<String> startList4First;

    @ApiModelProperty(value = "startList4Second")
    private List<String> startList4Second;

    public AudioJson116BO getSysSoundByName(String id, String name) {
        //移除原有逻辑,从新表查询 sound 数据
        LambdaQueryWrapper<ProjSound116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSound116::getSoundName, name).last("limit 1");
        IProjSound116Service sound116Service = SpringUtil.getBean(IProjSound116Service.class);
        FileService fileService = SpringUtil.getBean(FileService.class);

        ProjSound116 sound = sound116Service.getOne(queryWrapper);
        if (sound == null) {
            throw new BizException("System sound '" + id + "' not find!");
        }

        String soundUrl = sound.getUrl();
        if (StringUtils.isBlank(soundUrl)) {
            throw new BizException("System sound '" + id + "' not set!");
        }

        // 目前只有女声
//        String maleUrl = sound.getMaleUrl();
//        if (StringUtils.isBlank(maleUrl)) {
//            throw new BizException("System sound '" + id + "' not set!");
//        }

        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
//        String maleName = FireBaseUrlSubUtils.getFileName(maleUrl);
        Integer duration = sound.getDuration();
        String soundId = StringUtils.isBlank(id) ? soundName : id;

        return new AudioJson116BO(
                soundId,
                fileService.getAbsoluteR2Url(soundUrl),
                soundName,
                BigDecimal.ZERO,
                duration,
                false,
                sound.getId(),
                sound.getNeedTranslation(),
                sound.getGender(),
                sound.getSoundScript(),
                sound.getCoreVoiceConfigI18nId()
        );
    }



}
