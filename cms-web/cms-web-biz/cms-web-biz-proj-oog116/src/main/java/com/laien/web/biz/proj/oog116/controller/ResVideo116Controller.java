package com.laien.web.biz.proj.oog116.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.common.oog116.enums.TargetEnums;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.ResVideo116Slice;
import com.laien.web.biz.proj.oog116.request.ResVideo116AddReq;
import com.laien.web.biz.proj.oog116.request.ResVideo116PageReq;
import com.laien.web.biz.proj.oog116.request.ResVideo116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ResVideo116DetailVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116ExportVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116PageVO;
import com.laien.web.biz.proj.oog116.service.IResVideo116Service;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 116 video 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Api(tags = "资源管理:视频Video 116")
@RestController
@RequestMapping("/res/video116")
public class ResVideo116Controller extends ResponseController {

    private static final Logger log = LoggerFactory.getLogger(ResVideo116Controller.class);
    @Resource
    private IResVideo116Service resVideo116Service;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResVideo116PageVO>> page(ResVideo116PageReq pageReq) {
        PageRes<ResVideo116PageVO> pageRes = resVideo116Service.selectVideo116Page(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResVideo116AddReq Video116AddReq) {
        resVideo116Service.saveVideo116(Video116AddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResVideo116UpdateReq Video116UpdateReq) {
        resVideo116Service.updateVideo116(Video116UpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResVideo116DetailVO> detail(@PathVariable Integer id) {
        ResVideo116DetailVO detailVO = resVideo116Service.getVideo116Detail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        List<Integer> failedIdList = resVideo116Service.updateEnableByIds(idList);
        if (CollUtil.isEmpty(failedIdList)) {
            return succ();
        }
        return fail("It cannot be enabled because video processing is not complete.failed id list:" + failedIdList.toString());
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resVideo116Service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        resVideo116Service.deleteByIds(idList);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(resVideo116Service.importByExcel(excel.getInputStream()));
    }

    @SneakyThrows
    @ApiOperation(value = "全部导出")
    @GetMapping("/exportWithExcel")
    public void exportWithExcel(HttpServletResponse response) {

        LambdaQueryWrapper<ResVideo116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(ResVideo116::getId);

        List<ResVideo116ExportVO> data = resVideo116Service.list(queryWrapper).stream().map(v -> {
            ResVideo116ExportVO bean = BeanUtil.toBean(v, ResVideo116ExportVO.class);
            String target = Optional.ofNullable(v.getTarget()).map(TargetEnums::getName).orElse(null);
            bean.setTarget(target);
            // 导出时，exercise type 转别名
            if (StringUtils.isNotBlank(bean.getExerciseType())) {
                String exerciseType = Stream.of(bean.getExerciseType().split(GlobalConstant.COMMA))
                        .map(ExerciseType116Enums::getByName)
                        .filter(Objects::nonNull)
                        .map(ExerciseType116Enums::getImportAliasName)
                        .collect(Collectors.joining(GlobalConstant.COMMA));
                bean.setExerciseType(exerciseType);
            }
            return bean;
        }).collect(Collectors.toList());

        //I18n 名称转换
        Map<Integer, CoreVoiceConfigI18n> configMap = resVideo116Service.getVoiceConfigIdMap(data);
        for (ResVideo116ExportVO vo : data) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }

        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''resVideo116.xlsx";
        String sheetName = "resVideo116";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ResVideo116ExportVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(data);
    }

    @ApiOperation(value = "批量导入Res Video 116 Slice 信息", notes = "支持新增、更新操作。表格中存在任意一条错误数据，直接返回，不再进行新增、更新")
    @PostMapping("/v2/importByExcel")
    public ResponseResult<List<String>> importVideoSlice(@RequestParam("file") MultipartFile excel) {

        return succ(resVideo116Service.importVideoSlice(excel));
    }

    @SneakyThrows
    @ApiOperation(value = "批量导入修改图片")
    @PostMapping("/importByExcelUpdateImage")
    public ResponseResult<List<String>> importByExcelUpdateImage(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(resVideo116Service.importByExcelUpdateImage(excel.getInputStream()));
    }


    @SneakyThrows
    @ApiOperation(value = "批量导入")
    @PostMapping("/reGenerateM3U8")
    public ResponseResult<Void> reGenerateM3U8() {
        resVideo116Service.reGenerateAllM3U8();
        return ResponseResult.succ();
    }
}
