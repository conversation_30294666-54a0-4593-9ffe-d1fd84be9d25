package com.laien.web.biz.proj.oog116.entity.i18n;


import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * ProjResVideo116I18n
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjResVideo116I18n", description = "ProjResVideo116I18n")
public class ProjResVideo116I18n extends BaseAudioI18nModel {

    @AppAudioTranslateField(resultFieldName = "result")
    private String name;

    @AppAudioTranslateField(resultFieldName = "guidanceResult")
    private String guidance;

    @AppAudioTranslateField(resultFieldName = "instructionsResult")
    private String instructions;

    private List<AudioTranslateResultModel> guidanceResult;

    private List<AudioTranslateResultModel> instructionsResult;

    public ProjResVideo116I18n(ResVideo116 resVideo116) {
        super.setUniqueKey(resVideo116.getId());
        super.setCoreVoiceConfigI18nId(resVideo116.getCoreVoiceConfigI18nId());
        this.name = resVideo116.getName();
        this.guidance = resVideo116.getGuidance();
        this.instructions = resVideo116.getInstructions();
    }
}
