package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116GenerateResVideo116;
import com.laien.web.biz.proj.oog116.mapper.ProjWorkout116GenerateResVideo116Mapper;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116GenerateResVideo116Service;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.stereotype.Service;

/**
 * <p>
 * proj_workout116_generate和res_video116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjWorkout116GenerateResVideo116ServiceImpl extends ServiceImpl<ProjWorkout116GenerateResVideo116Mapper, ProjWorkout116GenerateResVideo116> implements IProjWorkout116GenerateResVideo116Service {

    @Override
    public void deleteByProjTemplate116Id(Integer template116Id, Integer taskId) {
        LambdaUpdateWrapper<ProjWorkout116GenerateResVideo116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjWorkout116GenerateResVideo116::getProjTemplate116Id, template116Id)
                .ne(ProjWorkout116GenerateResVideo116::getProjTemplate116TaskId, taskId)
                .set(BaseModel::getDelFlag, GlobalConstant.YES);
        baseMapper.update(new ProjWorkout116GenerateResVideo116(), wrapper);
    }
}
