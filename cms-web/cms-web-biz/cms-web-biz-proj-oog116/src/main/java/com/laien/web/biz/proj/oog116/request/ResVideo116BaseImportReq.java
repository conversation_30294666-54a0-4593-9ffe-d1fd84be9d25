package com.laien.web.biz.proj.oog116.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * Author:  hhl
 * Date:  2025/2/28 11:09
 */
@EqualsAndHashCode(of = {"name", "exerciseType", "position"})
@Data
public class ResVideo116BaseImportReq {

    @NotBlank(message = "Name cannot be blank", groups = Group1.class)
    @Length(message = "The Video Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Name", converter = StringStringTrimConverter.class)
    private String name;

    @NotBlank(message = "ExerciseType cannot be blank", groups = Group1.class)
    @ExcelProperty(value = "ExerciseType", converter = StringStringTrimConverter.class)
    private String exerciseType;

    @NotBlank(message = "Position cannot be blank", groups = Group1.class)
    @ExcelProperty(value = "Position", converter = StringStringTrimConverter.class)
    private String position;

}
