package com.laien.web.biz.proj.oog116.controller;

import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.request.ProjSound116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjSound116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjSound116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjSound116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjSound116PageVO;
import com.laien.web.biz.proj.oog116.service.IProjSound116Service;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Api(tags = "项目管理:sound116")
@RestController
@RequestMapping("/proj/sound116")
public class ProjSound116Controller extends ResponseController {

    @Resource
    private IProjSound116Service soundService;

    @ApiOperation(value = "sound116 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSound116PageVO>> page(ProjSound116PageReq req) {
        req.setProjId(RequestContextUtils.getProjectId()) ;
        AssertUtil.notNull(RequestContextUtils.getProjectId(),"projId is null");
        PageRes<ProjSound116PageVO> pageRes = soundService.selectSoundPage(req);
        return succ(pageRes);
    }

    @ApiOperation(value = "Sound116 增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSound116AddReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.saveSound(req);
        return succ();
    }

    @ApiOperation(value = "Sound116 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSound116UpdateReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.updateSound(req);
        return succ();
    }

    @ApiOperation(value = "Sound116 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSound116DetailVO> detail(@PathVariable Integer id) {
        ProjSound116DetailVO detailVO = soundService.getDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "Sound116 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Sound116 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateDisableByIds(idList);
        return succ();
    }
}
