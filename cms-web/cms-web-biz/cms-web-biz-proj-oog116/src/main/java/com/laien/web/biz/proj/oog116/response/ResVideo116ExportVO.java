package com.laien.web.biz.proj.oog116.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.common.core.converter.GenericEnumNameConverter;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.web.biz.core.converter.GenericEnumListNameConverter;
import com.laien.web.biz.core.converter.StringStringTrimArrayConverter;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>export data model</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/4 16:57
 */
@Data
public class ResVideo116ExportVO {

    @ExcelProperty(value = "id")
    private Integer id;

    @ExcelProperty(value = "target")
    private String target;

    @ExcelProperty(value = "guidance")
    private String guidance;

    @ExcelProperty(value = "circuit")
    private Integer circuit;

    @ExcelProperty(value = "name", converter = StringStringTrimConverter.class)
    private String name;

    @ExcelIgnore
    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ExcelProperty("core_voice_config_i18n_name")
    @ApiModelProperty(value = "coreVoiceConfigI18nName")
    private String coreVoiceConfigI18nName;

    @ExcelProperty(value = "cover_url", converter = StringStringTrimConverter.class)
    private String coverImgUrl;

    @ExcelProperty(value = "type", converter = StringStringTrimConverter.class)
    private String type;

    @ExcelProperty(value = "position", converter = StringStringTrimConverter.class)
    private String position;

    @ExcelProperty(value = "restriction", converter = StringStringTrimArrayConverter.class)
    private String restriction;

    @ExcelProperty(value = "howtodo_text", converter = StringStringTrimConverter.class)
    private String instructions;

    @ExcelProperty(value = "main_url", converter = StringStringTrimConverter.class)
    private String frontVideoUrl;

    @ExcelProperty(value = "main_duration")
    private Integer frontDuration;

    @ExcelProperty(value = "assist_url", converter = StringStringTrimConverter.class)
    private String sideVideoUrl;

    @ExcelProperty(value = "assist_duration")
    private Integer sideDuration;

    @ExcelProperty(value = "name_url", converter = StringStringTrimConverter.class)
    private String nameAudioUrl;

    @ExcelProperty(value = "name_url_duration")
    private Integer nameAudioUrlDuration;

    @ExcelProperty(value = "guidance_url", converter = StringStringTrimConverter.class)
    private String guidanceAudioUrl;

    @ExcelProperty(value = "howtodo_url", converter = StringStringTrimConverter.class)
    private String instructionsAudioUrl;

    @ExcelProperty(value = "met")
    private Integer met;

    @ExcelProperty(value = "gender", converter = StringStringTrimConverter.class)
    private String gender;

    @ExcelProperty(value = "exercise_type", converter = StringStringTrimConverter.class)
    private String exerciseType;

    @ExcelProperty(value = "region", converter = GenericEnumListNameConverter.class)
    private List<Region116Enums> region;

    @ExcelProperty(value = "focus", converter = GenericEnumListNameConverter.class)
    private List<Focus116Enums> focus;

    @ExcelProperty(value = "support_prop", converter = GenericEnumNameConverter.class)
    private SupportProp116Enums supportProp;
}
