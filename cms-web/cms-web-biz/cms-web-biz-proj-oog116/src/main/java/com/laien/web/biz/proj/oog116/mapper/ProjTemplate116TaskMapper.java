package com.laien.web.biz.proj.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116Task;

import java.util.List;

/**
 * <p>
 * proj_template116 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface ProjTemplate116TaskMapper extends BaseMapper<ProjTemplate116Task> {

    /**
     * 查询最后一次任务信息
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<ProjTemplate116Task> selectLastTask(List<Integer> templateIdList);

}
