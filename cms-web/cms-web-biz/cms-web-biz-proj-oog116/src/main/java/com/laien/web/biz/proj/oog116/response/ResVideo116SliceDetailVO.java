package com.laien.web.biz.proj.oog116.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/9/23 16:53
 */
@Data
public class ResVideo116SliceDetailVO {

    @ApiModelProperty(value = "res video116 id")
    @JsonIgnore
    private Integer resVideo116Id;

    @JsonIgnore
    @ApiModelProperty(value = "slice id")
    private Integer id;

    @ApiModelProperty(value = "切片索引，1，2，3，4...")
    private Integer sliceIndex;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "frontM3u8Text2532对应的m3u8内容")
    private String frontM3u8Text2532;

    @ApiModelProperty(value = "frontM3u8Text2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @ApiModelProperty(value = "frontM3u8Text1080p对应的m3u8内容")
    private String frontM3u8Text1080p;

    @ApiModelProperty(value = "frontM3u8Text720p对应的m3u8内容")
    private String frontM3u8Text720p;

    @ApiModelProperty(value = "frontM3u8Text480p对应的m3u8内容")
    private String frontM3u8Text480p;

    @ApiModelProperty(value = "frontM3u8Text360p对应的m3u8内容")
    private String frontM3u8Text360p;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "sideM3u8Text2532对应的m3u8内容")
    private String sideM3u8Text2532;

    @ApiModelProperty(value = "sideM3u8Text2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @ApiModelProperty(value = "sideM3u8Text1080p对应的m3u8内容")
    private String sideM3u8Text1080p;

    @ApiModelProperty(value = "sideM3u8Text720p对应的m3u8内容")
    private String sideM3u8Text720p;

    @ApiModelProperty(value = "sideM3u8Text480p对应的m3u8内容")
    private String sideM3u8Text480p;

    @ApiModelProperty(value = "sideM3u8Text360p对应的m3u8内容")
    private String sideM3u8Text360p;

}
