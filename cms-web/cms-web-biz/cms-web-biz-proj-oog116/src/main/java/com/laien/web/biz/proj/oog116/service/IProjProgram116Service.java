package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjProgram116;
import com.laien.web.biz.proj.oog116.request.ProjProgram116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjProgram116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjProgram116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjProgram116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjProgram116PageVO;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
public interface IProjProgram116Service extends IService<ProjProgram116> {

    /**
     * Program116分页查询
     *
     * @param pageReq pageReq
     * @return ProjProgram116PageVO
     */
    PageRes<ProjProgram116PageVO> selectProgramPage(ProjProgram116PageReq pageReq);

    /**
     * Program116新增
     *
     * @param addReq
     */
    void saveProgram(ProjProgram116AddReq addReq);

    /**
     * Program116修改
     *
     * @param updateReq
     */
    void updateProgram(ProjProgram116UpdateReq updateReq);

    /**
     * ProjProgram116Level详情
     *
     * @param videoId
     * @return ProjProgram116DetailVO
     */
    ProjProgram116DetailVO getDetailById(Integer programId);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
