package com.laien.web.biz.proj.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116Generate;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GeneratePageReq;
import com.laien.web.frame.response.IdAndCountsRes;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 116生成的workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface ProjWorkout116GenerateMapper extends BaseMapper<ProjWorkout116Generate> {

    /**
     * 查询video 生成数量
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<IdAndCountsRes> selectCountByTemplateIds(List<Integer> templateIdList);


    IPage<Integer> page(@Param("page") Page<ProjWorkout116Generate> page,
                                       @Param("pageReq") ProjWorkout116GeneratePageReq pageReq,
                                       @Param("restrictionSum") Integer restrictionSum,
                                       @Param("templateIdList") List<Integer> templateIdList);

    List<Integer> list(@Param("pageReq") ProjWorkout116GeneratePageReq pageReq,
                       @Param("restrictionSum") Integer restrictionSum,
                       @Param("templateIdList") List<Integer> templateIdList);

    @Select(value = "select id from proj_workout116_generate where file_status = #{fileStatus} and del_flag = 0")
    List<Integer> listByFileStatus(@Param("fileStatus") Integer fileStatus);

}
