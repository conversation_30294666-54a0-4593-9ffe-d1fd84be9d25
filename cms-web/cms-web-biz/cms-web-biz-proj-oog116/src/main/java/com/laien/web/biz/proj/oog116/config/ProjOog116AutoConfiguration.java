package com.laien.web.biz.proj.oog116.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Configuration
@MapperScan({"com.laien.web.biz.proj.oog116.mapper"})
@ComponentScan(value = "com.laien.web.biz.proj.oog116")
public class ProjOog116AutoConfiguration {
}
