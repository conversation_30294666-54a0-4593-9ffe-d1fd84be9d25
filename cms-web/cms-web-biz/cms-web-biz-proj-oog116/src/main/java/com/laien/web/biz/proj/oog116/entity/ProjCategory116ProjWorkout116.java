package com.laien.web.biz.proj.oog116.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_workout116_res_video116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCategory116ProjWorkout116对象", description="proj_workout116_res_video116")
public class ProjCategory116ProjWorkout116 extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_category116_id")
    private Integer projCategory116Id;

    @ApiModelProperty(value = "proj_workout116_id")
    private Integer projWorkout116Id;


}
