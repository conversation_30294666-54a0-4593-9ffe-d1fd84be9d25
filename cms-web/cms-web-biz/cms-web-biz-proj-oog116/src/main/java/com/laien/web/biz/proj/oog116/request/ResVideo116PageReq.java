package com.laien.web.biz.proj.oog116.request;

import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.common.oog116.enums.TargetEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: video116分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video116分页", description = "video116分页")
public class ResVideo116PageReq extends PageReq {

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "视频类型Warm Up/Cool Down/Main")
    private String type;

    @ApiModelProperty(value = "部位Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制,多个用英文逗号分隔，取值Shoulder, <PERSON>, <PERSON><PERSON>, <PERSON>nee, <PERSON><PERSON>, Hip;")
    private String[] restrictionArr;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "target")
    private TargetEnums target;

    @ApiModelProperty(value = "身体部位 (多选)")
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)")
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具")
    private SupportProp116Enums supportProp;

}
