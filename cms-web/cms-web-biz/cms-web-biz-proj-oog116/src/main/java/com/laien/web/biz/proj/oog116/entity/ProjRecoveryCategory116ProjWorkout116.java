package com.laien.web.biz.proj.oog116.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_recovery_category116_proj_workout116
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjRecoveryCategory116ProjWorkout116对象", description="proj_recovery_category116_proj_workout116")
public class ProjRecoveryCategory116ProjWorkout116 extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_recovery_category116_id")
    private Integer projRecoveryCategory116Id;

    @ApiModelProperty(value = "proj_workout116_id")
    private Integer projWorkout116Id;
}
