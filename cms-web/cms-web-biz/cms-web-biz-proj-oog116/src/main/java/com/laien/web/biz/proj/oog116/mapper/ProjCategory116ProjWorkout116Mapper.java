package com.laien.web.biz.proj.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog116.entity.ProjCategory116ProjWorkout116;
import com.laien.web.biz.proj.oog116.response.ProjCategory116DetailWorkoutVO;
import com.laien.web.frame.response.IdAndStatusCountsRes;

import java.util.List;

/**
 * <p>
 * proj_workout116_res_video116 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface ProjCategory116ProjWorkout116Mapper extends BaseMapper<ProjCategory116ProjWorkout116> {

    /**
     * 查询workout116 各个状态数量
     *
     * @param projId projId
     * @return list
     */
    List<IdAndStatusCountsRes> selectWorkoutStatusCount(Integer projId);

    /**
     * 根据categoryId查询workout
     *
     * @param categoryId categoryId
     * @return list
     */
    List<ProjCategory116DetailWorkoutVO> selectWorkoutByCategoryId(Integer categoryId);

}
