package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * recovery category116 修改
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "recovery category116 修改", description = "recovery category116 修改")
public class ProjRecoveryCategory116UpdateReq extends ProjRecoveryCategory116AddReq {

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "id不能为空")
    private Integer id;
}
