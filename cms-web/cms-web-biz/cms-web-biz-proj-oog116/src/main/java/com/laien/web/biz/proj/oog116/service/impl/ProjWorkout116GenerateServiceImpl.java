package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.*;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.oog116.enums.*;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.core.constant.ResImageConstant;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.config.FixedTemplateConfig;
import com.laien.web.biz.proj.oog116.config.Oog116BizConfig;
import com.laien.web.biz.proj.oog116.config.Video116SysSoundBOWrapper;
import com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum;
import com.laien.web.biz.proj.oog116.entity.*;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjSound116I18n;
import com.laien.web.biz.proj.oog116.exception.AutoGenerateException;
import com.laien.web.biz.proj.oog116.mapper.ProjWorkout116GenerateMapper;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GenerateM3u8Req;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GeneratePageReq;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116GeneratePageVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116GenerateVideoVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.proj.oog116.service.*;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.biz.resource.service.IResImageService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.IdAndCountsRes;
import com.laien.web.frame.response.PageRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.ExerciseType116Enums.*;
import static com.laien.common.oog116.enums.Gender116Enums.FEMALE;
import static com.laien.common.oog116.enums.Gender116Enums.MALE;
import static com.laien.common.oog116.enums.ProjTemplate116TaskStatusEnums.FAIL;
import static com.laien.common.oog116.enums.ProjTemplate116TaskStatusEnums.SUCCESS;
import static com.laien.web.biz.proj.oog116.constant.ResVideo116Constant.*;
import static com.laien.web.frame.constant.GlobalConstant.STATUS_ENABLE;
import static com.laien.web.frame.constant.GlobalConstant.YES;

/**
 * <p>
 * 116生成的workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
@Slf4j
public class ProjWorkout116GenerateServiceImpl
        extends ServiceImpl<ProjWorkout116GenerateMapper, ProjWorkout116Generate>
        implements IProjWorkout116GenerateService {

    @Resource
    private IProjTemplate116Service projTemplate116Service;
    @Resource
    private  ICoreSpeechTaskI18nPubService speechI18nPubService;
    @Resource
    private ProjWorkout116GenerateMapper workout116GenerateMapper;

    @Resource
    private IProjTemplate116TaskService projTemplate116TaskService;
    @Resource
    private IProjWorkout116GenerateResVideo116Service projWorkout116GenerateResVideo116Service;

    @Resource
    private IResVideo116Service resVideo116Service;

    @Resource
    private IResVideo116SliceService video116SliceService;

    @Resource
    private IProjWorkout116Generate4TaiChiService workoutTaiChiService;

    @Resource
    private IProjWorkout116Generate4ChairYogaService workoutChairYogaService;

    @Resource
    private IProjWorkout116Generate4DumbbellService workoutDumbbellService;

    @Resource
    private IProjTemplate116RuleService projTemplate116RuleService;

    @Resource
    private IResImageService resImageService;

    @Resource
    private Oog116BizConfig oog116BizConfig;

    @Resource
    private FileService fileService;

    @Resource
    private IProjWorkout116GenerateI18nService projWorkout116GenerateI18nService;
    @Resource
    private IProjWorkout116GenerateService workout116GenerateService;

    @Resource
    private FixedTemplateConfig fixedTemplateConfig;

    private final ExecutorService TASK_EXECUTOR_SERVICE = Executors.newFixedThreadPool(1);

    private final LinkedBlockingQueue<ProjWorkout116UpdateBO> workoutUpdateQueue = new LinkedBlockingQueue<>(GlobalConstant.THOUSAND);

    @Override
    public List<IdAndCountsRes> selectCountByTemplateIds(List<Integer> templateIdList) {
        return this.baseMapper.selectCountByTemplateIds(templateIdList);
    }

    @Override
    public List<Integer> queryIdList(ProjWorkout116GeneratePageReq generatePageReq) {
        Integer restrictionSum = getRestrictionSum(generatePageReq);
        List<Integer> templateIdList = getTemplateIdList(generatePageReq);
        if (null != templateIdList) {
            templateIdList.removeAll(fixedTemplateConfig.getFixedTemplateIdList());
        }
        Integer templateId = generatePageReq.getTemplateId();
        if (null != templateId && CollUtil.isEmpty(templateIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.list(generatePageReq, restrictionSum, templateIdList);
    }

    @Override
    public PageRes<ProjWorkout116GeneratePageVO> selectWorkoutGeneratePage(ProjWorkout116GeneratePageReq generatePageReq) {
        // 查询生成的workout
        Integer restrictionSum = getRestrictionSum(generatePageReq);
        Integer templateId = generatePageReq.getTemplateId();
        List<Integer> templateIdList = getTemplateIdList(generatePageReq);
        if (null != templateIdList) {
            templateIdList.removeAll(fixedTemplateConfig.getFixedTemplateIdList());
        }
        if (null != templateId && CollUtil.isEmpty(templateIdList)) {
            return new PageRes<>(GlobalConstant.ONE, GlobalConstant.ZERO, GlobalConstant.ZERO, GlobalConstant.ONE, new ArrayList<>());
        }

        Page<ProjWorkout116Generate> generatePage = new Page<>(generatePageReq.getPageNum(), generatePageReq.getPageSize());
        IPage<Integer> workoutIdPage = baseMapper.page(generatePage, generatePageReq, restrictionSum, templateIdList);
        List<Integer> workoutIdList = workoutIdPage.getRecords();
        if (workoutIdList.isEmpty()) {
            return new PageRes<>(workoutIdPage.getCurrent(), workoutIdPage.getSize(), workoutIdPage.getTotal(), workoutIdPage.getPages(), new ArrayList<>());
        }
        LambdaQueryWrapper<ProjWorkout116Generate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjWorkout116Generate::getId, workoutIdList);
        List<ProjWorkout116Generate> workoutList = baseMapper.selectList(queryWrapper);

        List<Integer> idList = new ArrayList<>(workoutIdList.size());
        Set<Integer> imageIdSet = new HashSet<>();
        workoutList.forEach(o -> {
            idList.add(o.getId());
            imageIdSet.add(o.getResImageId());
        });
        // 查询workout 关联的image
        LambdaQueryWrapper<ResImage> queryImageWrapper = new LambdaQueryWrapper<>();
        queryImageWrapper.in(ResImage::getId, imageIdSet);
        List<ResImage> imageList = resImageService.list(queryImageWrapper);
        Map<Integer, ResImage> imageMap = new HashMap<>();
        imageList.forEach(o -> imageMap.put(o.getId(), o));

        // 查询关联的rule和 video
        LambdaQueryWrapper<ProjWorkout116GenerateResVideo116> queryWorkoutVideoWrapper = new LambdaQueryWrapper<>();
        queryWorkoutVideoWrapper.in(ProjWorkout116GenerateResVideo116::getProjWorkout116GenerateId, idList);
        List<ProjWorkout116GenerateResVideo116> generateResVideoList = projWorkout116GenerateResVideo116Service.list(queryWorkoutVideoWrapper);
        Map<Integer, List<ProjWorkout116GenerateResVideo116>> generateResVideoMap = generateResVideoList.stream().collect(Collectors.groupingBy(ProjWorkout116GenerateResVideo116::getProjWorkout116GenerateId));
        Set<Integer> videoIdSet = new HashSet<>();
        Set<Integer> ruleIdSet = new HashSet<>();
        generateResVideoList.forEach(o -> {
            videoIdSet.add(o.getResVideo116Id());
            ruleIdSet.add(o.getProjTemplate116RuleId());
        });

        // video 查询
        LambdaQueryWrapper<ResVideo116> queryVideoWrapper = new LambdaQueryWrapper<>();
        queryVideoWrapper.in(ResVideo116::getId, videoIdSet);
        List<ResVideo116> resVideo116List = resVideo116Service.list(queryVideoWrapper);
        Map<Integer, ResVideo116> videoMap = new HashMap<>();
        resVideo116List.forEach(o -> videoMap.put(o.getId(), o));

        // rule 查询
        LambdaQueryWrapper<ProjTemplate116Rule> queryRuleWrapper = new LambdaQueryWrapper<>();
        queryRuleWrapper.in(ProjTemplate116Rule::getId, ruleIdSet);
        List<ProjTemplate116Rule> ruleList = projTemplate116RuleService.list(queryRuleWrapper);
        Map<Integer, ProjTemplate116Rule> ruleMap = new HashMap<>();
        ruleList.forEach(o -> ruleMap.put(o.getId(), o));


        // workout返回对象赋值处理
        List<ProjWorkout116GeneratePageVO> copyList = new ArrayList<>(workoutIdList.size());
        for (ProjWorkout116Generate generate : workoutList) {
            ProjWorkout116GeneratePageVO pageVO = new ProjWorkout116GeneratePageVO();
            BeanUtils.copyProperties(generate, pageVO);
            pageVO.setRestrictionOption(Restriction116Enums.getRestrictionString(generate.getRestrictionSum()));
            ResImage image = imageMap.get(generate.getResImageId());
            if (image != null) {
                pageVO.setName(image.getName());
                pageVO.setDescription(image.getDescription());
                if (MALE.getName().equals(pageVO.getGender())) {
                    pageVO.setCoverImgUrl(image.getCoverImageMale());
                    pageVO.setDetailImgUrl(image.getDetailImageMale());
                } else {
                    pageVO.setCoverImgUrl(image.getCoverImage());
                    pageVO.setDetailImgUrl(image.getDetailImage());
                }

            }
            List<ProjWorkout116GenerateResVideo116> videoByIdList = generateResVideoMap.get(generate.getId());
            List<ProjWorkout116GenerateVideoVO> videoList = new ArrayList<>();
            if (videoByIdList != null) {
                for (ProjWorkout116GenerateResVideo116 generateResVideo116 : videoByIdList) {
                    ProjWorkout116GenerateVideoVO generateVideoVO = new ProjWorkout116GenerateVideoVO();
                    ResVideo116 video116 = videoMap.get(generateResVideo116.getResVideo116Id());
                    if (video116 != null) {
                        BeanUtils.copyProperties(video116, generateVideoVO);
                    }
                    ProjTemplate116Rule template116Rule = ruleMap.get(generateResVideo116.getProjTemplate116RuleId());
                    if (template116Rule != null) {
                        generateVideoVO.setUnitName(template116Rule.getUnitName());
                        generateVideoVO.setVideoType(template116Rule.getVideoType());
                        generateVideoVO.setCount(template116Rule.getCount());
                        generateVideoVO.setRounds(template116Rule.getRounds());
                    }
                    videoList.add(generateVideoVO);
                }
            }
            pageVO.setVideoList(videoList);
            copyList.add(pageVO);
        }
        return new PageRes<>(workoutIdPage.getCurrent(), workoutIdPage.getSize(), workoutIdPage.getTotal(), workoutIdPage.getPages(), copyList);
    }

    @Override
    public void generate(Integer projTemplate116Id, Integer projTemplate116TaskId, ProjInfo projInfo) {
        ProjTemplate116 template116 = projTemplate116Service.getById(projTemplate116Id);
        if (!checkTemplate(projTemplate116TaskId, template116)) {
            return;
        }
        try {
            ProjWorkout116ContextBO context = creatContext(template116, projTemplate116TaskId, projInfo);
            List<List<Restriction116Enums>> restrictionCombination = Restriction116Enums.getRestrictionCombination();
            List<ExerciseType116Enums> exerciseTypeList = template116.getTemplateType().getExerciseType116EnumsList();
            IProjWorkout116GenerateService projWorkout116GenerateService = SpringUtil.getBean(IProjWorkout116GenerateService.class);

            for (ExerciseType116Enums exerciseType : exerciseTypeList) {
                List<ProjWorkout116GenerateBO> projWorkout116BOList = new ArrayList<>();
                for (Equipment116Enums equipment : exerciseType.getEquipmentList()) {
                    for (List<Restriction116Enums> restrictionList : restrictionCombination) {
                        for (Position116Enums position : exerciseType.getPositionList()) {
                            generateWorkout(template116, projWorkout116BOList, context, exerciseType, equipment, restrictionList, position);
                        }
                    }
                }
                log.info("start generate file");
                // 最终使用的语言加上英语
                List<String> languageUseList = new ArrayList<>();
                languageUseList.add(GlobalConstant.DEFAULT_LANGUAGE);
                languageUseList.addAll(context.getLanguageList());

                generateFile(template116, projWorkout116BOList, languageUseList, context);
                log.info("start doGenerateWorkout");
                projWorkout116GenerateService.doGenerateWorkout(projWorkout116BOList, context);
            }
            projWorkout116GenerateService.cleanUp(context);

        } catch (Throwable e) {
            log.error("generate projWorkout116 failed, projTemplate116Id:{},projTemplate116TaskId:{}, exception is"
                    , projTemplate116Id, projTemplate116TaskId, e);
            projTemplate116TaskService.changeState(projTemplate116TaskId, FAIL, "failed reason ：" + StringUtils.substring(e.getMessage(), 0, 200));
            return;
        }
        projTemplate116TaskService.changeState(projTemplate116TaskId, SUCCESS, null);
    }

    private void generateWorkout(ProjTemplate116 template116, List<ProjWorkout116GenerateBO> projWorkout116BOList,
                                 ProjWorkout116ContextBO context, ExerciseType116Enums exerciseType,
                                 Equipment116Enums equipment, List<Restriction116Enums> restrictionList,
                                 Position116Enums position) {

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.TAI_CHI)) {
            List<ProjWorkout116GenerateBO> workoutList = workoutTaiChiService.generateWorkoutByPlan(exerciseType, equipment, restrictionList, position, context);
            projWorkout116BOList.addAll(workoutList);
        }

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.CHAIR_YOGA)) {
            List<ProjWorkout116GenerateBO> workoutList = workoutChairYogaService.generateWorkoutByPlan(exerciseType, equipment, restrictionList, position, context);
            projWorkout116BOList.addAll(workoutList);
        }

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.DUMBBELL_MIDWEIGHT)) {
            List<ProjWorkout116GenerateBO> workoutList = workoutDumbbellService.generateWorkoutByPlan(exerciseType, equipment, restrictionList, position, context);
            projWorkout116BOList.addAll(workoutList);
        }

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.NORMAL)) {
            Integer maleDay = exerciseType.getMaleDay();
            List<ProjWorkout116GenerateBO> maleWorkoutList = batchGenerateWorkout(exerciseType, equipment, restrictionList, position, maleDay, context, MALE);
            if (maleWorkoutList.size() < maleDay) {
                throw new BizException(String.format("male workout count not enough, exerciseType:%s, equipment:%s, restrictionList:%s, position:%s, maleDay:%s", exerciseType, equipment, restrictionList, position, maleDay));
            }
            Integer femaleDay = exerciseType.getFemaleDay();
            List<ProjWorkout116GenerateBO> femaleWorkoutList = batchGenerateWorkout(exerciseType, equipment, restrictionList, position, femaleDay, context, FEMALE);
            if (femaleWorkoutList.size() < femaleDay) {
                throw new BizException(String.format("femaleDay workout count not enough, exerciseType:%s, equipment:%s, restrictionList:%s, position:%s, femaleDay:%s", exerciseType, equipment, restrictionList, position, femaleDay));
            }
            projWorkout116BOList.addAll(maleWorkoutList);
            projWorkout116BOList.addAll(femaleWorkoutList);
        }

    }

    private void generateFile(ProjTemplate116 template116, List<ProjWorkout116GenerateBO> projWorkout116BOList,
                              List<String> languageUseList, ProjWorkout116ContextBO context) {

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.NORMAL)) {
            generateFile(projWorkout116BOList, languageUseList, context.getVideoI18nMap(), Boolean.TRUE, Boolean.TRUE, Boolean.TRUE);
        }

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.TAI_CHI)) {
            workoutTaiChiService.generateFile(projWorkout116BOList, context, languageUseList, Boolean.TRUE, Boolean.TRUE, Boolean.TRUE);
        }

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.CHAIR_YOGA)) {
            workoutChairYogaService.generateFile(projWorkout116BOList, context, languageUseList, Boolean.TRUE);
        }

        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.DUMBBELL_MIDWEIGHT)) {
            workoutDumbbellService.generateFile(projWorkout116BOList, context, languageUseList, Boolean.TRUE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanUp(ProjWorkout116ContextBO context) {
        Integer projTemplate116TaskId = context.getProjTemplate116TaskId();
        Integer template116Id = context.getProjTemplate116Id();
        ProjTemplate116Task task = projTemplate116TaskService.getById(projTemplate116TaskId);
        if (GlobalConstant.YES == task.getCleanUp()) {
            LambdaUpdateWrapper<ProjWorkout116Generate> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ProjWorkout116Generate::getProjTemplate116Id, template116Id)
                    .ne(ProjWorkout116Generate::getProjTemplate116TaskId, projTemplate116TaskId)
                    .set(ProjWorkout116Generate::getDelFlag, GlobalConstant.YES);
            baseMapper.update(new ProjWorkout116Generate(), wrapper);
            projWorkout116GenerateResVideo116Service.deleteByProjTemplate116Id(template116Id, projTemplate116TaskId);
        }
    }

    private List<ProjWorkout116GenerateBO> batchGenerateWorkout(ExerciseType116Enums exerciseType,
                                                                Equipment116Enums equipment,
                                                                List<Restriction116Enums> restrictionList,
                                                                Position116Enums position,
                                                                Integer day,
                                                                ProjWorkout116ContextBO context,
                                                                Gender116Enums gender) {
        if (null == day || day < 1) {
            return new ArrayList<>();
        }
        List<ResImage> resImages = context.matchImageList(position, day, exerciseType, gender);
        List<ProjWorkout116GenerateBO> projWorkout116BOList = new ArrayList<>();
        for (int i = 0; i < day; i++) {
            ProjWorkout116GenerateBO projWorkout116BO;
            try {
                projWorkout116BO = generateWorkout(context, restrictionList,
                        position, i, resImages, equipment, exerciseType, gender);
            } catch (AutoGenerateException e) {
                return new ArrayList<>();
            }
            projWorkout116BO.setRestrictionList(restrictionList);
            projWorkout116BOList.add(projWorkout116BO);
        }
        return projWorkout116BOList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doGenerateWorkout(List<ProjWorkout116GenerateBO> projWorkout116List, ProjWorkout116ContextBO context) {
        ProjTemplate116 template116 = context.getTemplate116();
        Integer template116Id = template116.getId();
        Integer projTemplate116TaskId = context.getProjTemplate116TaskId();

        List<ProjWorkout116Generate> workout116GenerateList = projWorkout116List
                .stream()
                .map(ProjWorkout116GenerateBO::getProjWorkout116Generate)
                .collect(Collectors.toList());
        saveBatch(workout116GenerateList);

        List<ProjWorkout116GenerateResVideo116> relations = new ArrayList<>(50000);
        List<ProjWorkout116GenerateI18n> i18ns = new ArrayList<>(projWorkout116List.size());
        projWorkout116List.forEach(item -> {
            ProjWorkout116Generate projWorkout116Generate = item.getProjWorkout116Generate();
            List<ProjWorkout116GenerateResVideo116BO> projWorkoutResVideoList = item.getProjWorkout116GenerateResVideo116List();
            projWorkoutResVideoList.forEach(relation -> {
                relation.setProjWorkout116GenerateId(projWorkout116Generate.getId())
                        .setProjTemplate116TaskId(projTemplate116TaskId)
                        .setProjTemplate116Id(template116Id);
            });

            projWorkoutResVideoList.forEach(relation -> {
                ProjWorkout116GenerateResVideo116 projWorkoutResVideo = new ProjWorkout116GenerateResVideo116();
                BeanUtils.copyProperties(relation, projWorkoutResVideo);
                relations.add(projWorkoutResVideo);
            });

            List<ProjWorkout116GenerateI18n> i18nList = item.getI18nList();
            if (CollUtil.isNotEmpty(i18nList)) {
                for (ProjWorkout116GenerateI18n i18n : i18nList) {
                    i18n.setId(projWorkout116Generate.getId());
                    i18ns.add(i18n);
                }
            }

        });
        projWorkout116GenerateResVideo116Service.saveBatch(relations);
        if (!i18ns.isEmpty()) {
            projWorkout116GenerateI18nService.saveBatch(i18ns);
        }
    }

    /**
     * 获取系统音多语言
     *
     * @param soundList soundList
     * @param languages languages
     * @return map
     */
    @Override
    public Map<Integer, Map<String, AudioJson116BO>> getSoundI18n(List<AudioJson116BO> soundList, List<String> languages) {
        //不处理英语
        List<String> languageList = languages.stream()
                .filter(e -> ObjUtil.notEqual(GlobalConstant.DEFAULT_LANGUAGE, e))
                .collect(Collectors.toList());
        if (languageList.isEmpty()) {
            return new HashMap<>();
        }
        List<ProjSound116I18n> sound116I18nList = new ArrayList<>();
        for (AudioJson116BO json116BO : soundList) {
            if (json116BO.getNeedTranslation()) {
                sound116I18nList.add(new ProjSound116I18n(json116BO));
            }
        }

        // 是否有翻译未完成(新翻译没有是否完成校验)
        Map<Object, ProjSound116I18n> i18nResult =
                speechI18nPubService.getI18nModelGroupByKey(sound116I18nList, languageList, ProjCodeEnums.OOG116);
        Map<Integer, Map<String, AudioJson116BO>> audioJson116I18nMap = new HashMap<>();
        for (AudioJson116BO audioJson116AllBO : soundList) {
            Boolean needTranslation = audioJson116AllBO.getNeedTranslation();
            ProjSound116I18n resSound116I18n = i18nResult.get(audioJson116AllBO.getSoundId());

            if (needTranslation && resSound116I18n == null) {
                throw new BizException("The System sound " + audioJson116AllBO.getId() + " translation language incomplete");
            }
            Map<LanguageEnums, AudioTranslateResultModel> soundMaleMap = MapUtil.empty();
            if (resSound116I18n != null) {
                 soundMaleMap = resSound116I18n.getResult() == null ? MapUtil.empty() :
                        resSound116I18n.getResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
            }
            Map<String, AudioJson116BO> resSoundI18nMap = new HashMap<>();
            for (String language : languageList) {
                AudioJson116BO json116BO;
                LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
                if (needTranslation) {
                    AudioTranslateResultModel resSoundMaleI18n = soundMaleMap.get(le);
                    if (resSoundMaleI18n == null) {
                        throw new BizException("The System sound " + audioJson116AllBO.getId() + " translation language incomplete");
                    }
                    json116BO = new AudioJson116BO(
                            audioJson116AllBO.getId(),
                            fileService.getAbsoluteR2Url(resSoundMaleI18n.getAudioUrl()),
                            FireBaseUrlSubUtils.getFileName(resSoundMaleI18n.getAudioUrl()),
                            audioJson116AllBO.getTime(),
                            resSoundMaleI18n.getDuration(),
                            false,
                            audioJson116AllBO.getSoundId(),
                            false,
                            audioJson116AllBO.getGender(),
                            audioJson116AllBO.getSoundScript(),
                            audioJson116AllBO.getCoreVoiceConfigI18nId()
                    );
                } else {
                    json116BO = audioJson116AllBO;
                }

                resSoundI18nMap.put(language, json116BO);
            }

            audioJson116I18nMap.put(audioJson116AllBO.getSoundId(), resSoundI18nMap);
        }

        return audioJson116I18nMap;
    }

    @Override
    public Boolean generateM3u8(ProjWorkout116GenerateM3u8Req req) {
        Integer templateId = req.getTemplateId();
        ProjTemplate116 template116 = projTemplate116Service.getById(templateId);
        if (ObjUtil.isNull(template116) || GlobalConstant.ONE == template116.getDelFlag()) {
            throw new BizException("template not exist, templateId:" + templateId);
        }

        Map<Object, ProjResVideo116I18n> videoI18nMap = new HashMap<>();
        List<ResVideo116> resVideo116List = resVideo116Service.queryList();
        if (ObjUtil.equal(Boolean.TRUE, req.getAudioFlag())) {
            // 校验video翻译是否完整
            List<String> validateLangList = req.getLanguages()
                    .stream()
                    .filter(e -> ObjUtil.notEqual(GlobalConstant.DEFAULT_LANGUAGE, e))
                    .collect(Collectors.toList());
            if (ObjUtil.equal(Boolean.TRUE, req.getAudioFlag())) {
                List<ProjResVideo116I18n> videoI18ns = resVideo116List.stream().map(ProjResVideo116I18n::new).collect(Collectors.toList());

                videoI18nMap = speechI18nPubService.getI18nModelGroupByKey(videoI18ns, validateLangList, ProjCodeEnums.OOG116);

                for (ResVideo116 resVideo116 : resVideo116List) {
                    ProjResVideo116I18n resVideo116I18n = videoI18nMap.get(resVideo116.getId());
                    if (resVideo116I18n == null) {
                        throw new BizException("The Video data translation language incomplete");
                    }
                    Map<LanguageEnums, AudioTranslateResultModel> nameMap = resVideo116I18n.getResult() == null ? MapUtil.empty() :
                            resVideo116I18n.getResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                    Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = resVideo116I18n.getGuidanceResult() == null ? MapUtil.empty() :
                            resVideo116I18n.getGuidanceResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                    Map<LanguageEnums, AudioTranslateResultModel> instructionsMap = resVideo116I18n.getInstructionsResult() == null ? MapUtil.empty() :
                            resVideo116I18n.getInstructionsResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                    for (String language : validateLangList) {
                        LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
                        if (ObjUtil.isNull(nameMap) || ObjUtil.isNull(guidanceMap) || ObjUtil.isNull(instructionsMap)) {
                            throw new BizException("The Video data translation language incomplete");
                        }
                        if (Objects.isNull(guidanceMap.get(le))) {
                            throw new BizException("The Video data translation language incomplete");
                        }
                        if (Objects.isNull(instructionsMap.get(le))) {
                            throw new BizException("The Video data translation language incomplete");
                        }
                        if (Objects.isNull(nameMap.get(le))) {
                            throw new BizException("The Video data translation language incomplete");
                        }
                    }
                }
            }
        }
        //查询generate
        LambdaQueryWrapper<ProjWorkout116Generate> query = new LambdaQueryWrapper<ProjWorkout116Generate>().eq(ProjWorkout116Generate::getProjTemplate116Id, templateId);
        if (CollectionUtil.isNotEmpty(req.getWorkoutIds())) {
            query.in(ProjWorkout116Generate::getId, req.getWorkoutIds());
        }
        List<ProjWorkout116Generate> generateList = this.list(query);
        List<Integer> workoutIds = generateList.stream()
                .map(ProjWorkout116Generate::getId)
                .collect(Collectors.toList());
        //文件处理状态赋值
        this.update(new LambdaUpdateWrapper<ProjWorkout116Generate>().set(ProjWorkout116Generate::getFileStatus, GlobalConstant.ZERO)
                .in(ProjWorkout116Generate::getId, workoutIds));

        Map<Object, ProjResVideo116I18n> finalVideoI18nMap = videoI18nMap;
        generateList.forEach(workout -> {
            TASK_EXECUTOR_SERVICE.execute(() -> {
                try {
                    List<ProjWorkout116GenerateBO> projWorkout116BOList = new ArrayList<>();
                    //查询中间表
                    List<ProjWorkout116GenerateResVideo116> relationList = projWorkout116GenerateResVideo116Service.list(new LambdaQueryWrapper<ProjWorkout116GenerateResVideo116>()
                            .eq(ProjWorkout116GenerateResVideo116::getProjWorkout116GenerateId, workout.getId()));
                    List<ProjWorkout116GenerateResVideo116BO> projWorkout116GenerateBOList = BeanUtil.copyToList(relationList, ProjWorkout116GenerateResVideo116BO.class);
                    //查询rule
                    List<Integer> ruleIds = relationList.stream()
                            .map(ProjWorkout116GenerateResVideo116::getProjTemplate116RuleId)
                            .collect(Collectors.toList());
                    List<ProjTemplate116Rule> rules = projTemplate116RuleService.list(new LambdaQueryWrapper<ProjTemplate116Rule>().in(ProjTemplate116Rule::getId, ruleIds));
                    List<ProjTemplate116RuleVO> projTemplate116RuleVOList = BeanUtil.copyToList(rules, ProjTemplate116RuleVO.class);
                    projWorkout116BOList.add(new ProjWorkout116GenerateBO().setProjWorkout116Generate(workout)
                            .setProjWorkout116GenerateResVideo116List(projWorkout116GenerateBOList)
                            .setRuleList(projTemplate116RuleVOList));
                    //生成m3u8
                    generateFile(projWorkout116BOList, req.getLanguages(), finalVideoI18nMap, req.getVideoFlag(), req.getAudioFlag(), Boolean.FALSE);
                    //更新workout
                    this.updateById(workout);
                    //更新i18n
                    projWorkout116BOList.forEach(workoutBO -> {
                        List<ProjWorkout116GenerateI18n> i18nList = workoutBO.getI18nList();
                        if (CollectionUtils.isNotEmpty(i18nList)) {
                            for (ProjWorkout116GenerateI18n i18n : i18nList) {
                                i18n.setId(workout.getId());
                                LambdaUpdateWrapper<ProjWorkout116GenerateI18n> wrapper = new LambdaUpdateWrapper<ProjWorkout116GenerateI18n>()
                                        .set(ProjWorkout116GenerateI18n::getAudioJsonUrl, i18n.getAudioJsonUrl())
                                        .eq(ProjWorkout116GenerateI18n::getId, i18n.getId())
                                        .eq(ProjWorkout116GenerateI18n::getLanguage, i18n.getLanguage());
                                boolean flag = projWorkout116GenerateI18nService.update(new ProjWorkout116GenerateI18n(), wrapper);
                                if (!flag) {
                                    projWorkout116GenerateI18nService.save(i18n);
                                }
                            }
                        }
                    });
                } catch (BizException be) {
                    workout.setFailMessage("Lack of audio resources: " + StringUtils.substring(be.getMessage(), 0, 200))
                            .setFileStatus(GlobalConstant.TWO);
                    this.updateById(workout);
                    log.error("Lack of audio resources", be);
                } catch (Exception e) {
                    workout.setFailMessage("Generate m3u8 error: " + StringUtils.substring(e.getMessage(), 0, 200))
                            .setFileStatus(GlobalConstant.TWO);
                    this.updateById(workout);
                    log.error("Generate m3u8 error", e);
                }
            });
        });
        return Boolean.TRUE;
    }

    private void checkVideoResource(ProjWorkout116GenerateM3u8Req req) {

        if (ObjUtil.equal(Boolean.FALSE, req.getAudioFlag())) {
            return;
        }

        // 如果需要生成多语言音频，需要先检查对应的音频翻译是否完成
        List<ResVideo116> resVideo116List = resVideo116Service.queryList();
        //采用新的翻译方式
        List<String> validateLangList = req.getLanguages()
                .stream()
                .filter(e -> ObjUtil.notEqual(GlobalConstant.DEFAULT_LANGUAGE, e))
                .collect(Collectors.toList());
        List<ProjResVideo116I18n> videoI18ns = resVideo116List.stream().map(ProjResVideo116I18n::new).collect(Collectors.toList());
        Map<Object, ProjResVideo116I18n> i18nResult = speechI18nPubService.getI18nModelGroupByKey(videoI18ns, validateLangList, ProjCodeEnums.OOG116);

        for (ResVideo116 resVideo116 : resVideo116List) {
            ProjResVideo116I18n projResVideo116I18n = i18nResult.get(resVideo116.getId());
            if (projResVideo116I18n.getResult() == null || projResVideo116I18n.getGuidanceResult() == null || projResVideo116I18n.getInstructionsResult() == null) {
                throw new BizException("The Video data translation language incomplete");
            }

            checkAudioTranslation(validateLangList, projResVideo116I18n);
        }
    }

    @Override
    public void checkVideoI18nResource(List<String> languageList, List<Integer> videoIdList) {

        if (CollectionUtils.isEmpty(languageList) || CollectionUtils.isEmpty(videoIdList)) {
            return;
        }

        List<String> validateLangList = languageList.stream().filter(e -> ObjUtil.notEqual(GlobalConstant.DEFAULT_LANGUAGE, e)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validateLangList)) {
            return;
        }
        //通过videoId 获取集合
        List<ResVideo116> resVideo116List = new ArrayList<>(resVideo116Service.listByIds(videoIdList));
        List<ProjResVideo116I18n> i18nModelList = resVideo116List.stream()
                .map(ProjResVideo116I18n::new)
                .collect(Collectors.toList());
        Map<Object, ProjResVideo116I18n> i18nResult =
                speechI18nPubService.getI18nModelGroupByKey(i18nModelList, languageList, ProjCodeEnums.OOG116);
        for(Integer videoId : videoIdList) {
            ProjResVideo116I18n i18n = i18nResult.get(videoId);
            if (i18n.getResult() == null || i18n.getGuidanceResult() == null || i18n.getInstructionsResult() == null) {
                throw new BizException("The Video data translation language incomplete");
            }
            checkAudioTranslation(validateLangList, i18n);
        }
    }

    private void checkAudioTranslation(List<String> validateLangList, ProjResVideo116I18n resVideo116I18n) {

        for (String language : validateLangList) {
            LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
            if (resVideo116I18n.getResult().stream().noneMatch(r ->le == r.getLanguage() && StringUtils.isNotBlank(r.getText()))) {
                throw new BizException("The Video data translation language incomplete");
            }
            if (resVideo116I18n.getGuidanceResult().stream().noneMatch(r ->le == r.getLanguage() && StringUtils.isNotBlank(r.getText()))) {
                    throw new BizException("The Video data translation language incomplete");
            }
            if (resVideo116I18n.getInstructionsResult().stream().noneMatch(r ->le == r.getLanguage() && StringUtils.isNotBlank(r.getText()))) {
                throw new BizException("The Video data translation language incomplete");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer generateM3u8ByQuery(ProjWorkout116GenerateM3u8Req req) {

        List<Integer> workoutIds = listMatchWorkoutId(req);
        if (CollectionUtils.isEmpty(workoutIds)) {
            return GlobalConstant.ZERO;
        }

        checkVideoResource(req);
        batchUpdateFileStatus(workoutIds);
        List<List<Integer>> workoutIdGroup = Lists.partition(workoutIds, GlobalConstant.FIVE_HUNDRED);
        List<ProjWorkout116UpdateBO> updateBOList = workoutIdGroup.stream().map(workoutIdList -> new ProjWorkout116UpdateBO().setWorkoutIds(workoutIdList).setGenerateM3u8(req)).collect(Collectors.toList());

        try {
            workoutUpdateQueue.addAll(updateBOList);
        } catch (Exception e) {
            log.warn(e.getMessage());
            throw new BizException("Please try later, update queue is full.");
        }

        TASK_EXECUTOR_SERVICE.submit(this::runWorkoutBatchUpdate);
        return workoutIds.size();
    }

    private List<Integer> listMatchWorkoutId(ProjWorkout116GenerateM3u8Req req) {

        if (CollectionUtils.isNotEmpty(req.getWorkoutIds())) {
            return req.getWorkoutIds();
        }

        List<Integer> workoutIds = queryIdList(req.getPageReq());
        return workoutIds;
    }

    private void batchUpdateFileStatus(List<Integer> workoutIds) {

        Lists.partition(workoutIds, GlobalConstant.THOUSAND).forEach(workoutIdList -> {
            LambdaUpdateWrapper<ProjWorkout116Generate> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjWorkout116Generate::getFileStatus, GlobalConstant.ZERO);
            updateWrapper.set(ProjWorkout116Generate::getUpdateTime, LocalDateTime.now());
            updateWrapper.set(ProjWorkout116Generate::getUpdateUser, RequestContextUtils.getLoginUserName());
            updateWrapper.in(ProjWorkout116Generate::getId, workoutIdList);
            this.update(updateWrapper);
        });
    }

    @Override
    public void generateM3u8Interrupt() {
        workoutUpdateQueue.clear();
        LambdaUpdateWrapper<ProjWorkout116Generate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjWorkout116Generate::getFileStatus, GlobalConstant.ZERO)
                .set(ProjWorkout116Generate::getFileStatus, GlobalConstant.ONE);
        baseMapper.update(new ProjWorkout116Generate(), updateWrapper);
    }


    private void runWorkoutBatchUpdate() {

        ProjWorkout116UpdateBO workout116UpdateBO;
        while ((workout116UpdateBO = workoutUpdateQueue.poll()) != null) {
            batchUpdateWorkout(workout116UpdateBO);
        }
    }

    private void batchUpdateWorkout(ProjWorkout116UpdateBO workout116UpdateBO) {

        Collection<ProjWorkout116Generate> workoutList = Collections.emptyList();
        try {

            ProjWorkout116GenerateM3u8Req req = workout116UpdateBO.getGenerateM3u8();
            workoutList = listByIds(workout116UpdateBO.getWorkoutIds());

            // 太极的独立更新
            Predicate<ProjWorkout116Generate> isTaiChiPred = workout -> Objects.equals(workout.getExerciseType(), TAI_CHI.getName());
            List<ProjWorkout116Generate> taiChiWorkoutList = workoutList.stream().filter(isTaiChiPred).collect(Collectors.toList());
            updateWorkout4TaiChi(taiChiWorkoutList, req);

            // Chair Yoga的独立更新
            Predicate<ProjWorkout116Generate> isGentleChairYogaPred = workout -> Objects.equals(workout.getExerciseType(), GENTLE_CHAIR_YOGA.getName());
            List<ProjWorkout116Generate> gentleChairYogaWorkoutList = workoutList.stream().filter(isGentleChairYogaPred).collect(Collectors.toList());
            updateWorkout4ChairYoga(gentleChairYogaWorkoutList, req);

            // dumbbell 独立更新
            Predicate<ProjWorkout116Generate> isDumbbellPred = workout -> Objects.equals(workout.getExerciseType(), DUMBBELL_MIDWEIGHT.getName());
            List<ProjWorkout116Generate> dumbbellWorkoutList = workoutList.stream().filter(isDumbbellPred).collect(Collectors.toList());
            updateWorkout4Dumbbell(dumbbellWorkoutList, req);

            // 其余走原有逻辑
            workoutList = workoutList.stream().filter(isTaiChiPred.negate()).filter(isDumbbellPred.negate())
                    .filter(isGentleChairYogaPred.negate()).collect(Collectors.toList());
            updateWorkout4Original(workoutList, req);

        } catch (BizException be) {
            log.warn("Lack of audio resources", be);
            if (CollectionUtils.isNotEmpty(workoutList)) {
                workoutList.forEach(workout -> workout.setFailMessage("Lack of audio resources: " + StringUtils.substring(be.getMessage(), 0, 200))
                        .setFileStatus(GlobalConstant.TWO));
                this.updateBatchById(workoutList);
            }
        } catch (Exception e) {
            log.warn("Generate m3u8 error", e);
            if (CollectionUtils.isNotEmpty(workoutList)) {
                workoutList.forEach(workout -> workout.setFailMessage("Generate m3u8 error: " + StringUtils.substring(e.getMessage(), 0, 200))
                        .setFileStatus(GlobalConstant.TWO));
                this.updateBatchById(workoutList);
            }
        }
    }

    private void updateWorkout4TaiChi(Collection<ProjWorkout116Generate> taiChiWorkoutList, ProjWorkout116GenerateM3u8Req req) {

        if (CollectionUtils.isEmpty(taiChiWorkoutList)) {
            return;
        }

        String exerciseTypeName = taiChiWorkoutList.stream().findFirst().get().getExerciseType();
        ProjWorkout116ContextBO contextBO = createContext(req.getLanguages(),null, exerciseTypeName);
        Set<Integer> workoutIds = taiChiWorkoutList.stream().map(ProjWorkout116Generate::getId).collect(Collectors.toSet());
        List<ProjWorkout116GenerateResVideo116> generateResVideo116List = projWorkout116GenerateResVideo116Service.list(new LambdaQueryWrapper<ProjWorkout116GenerateResVideo116>()
                .in(ProjWorkout116GenerateResVideo116::getProjWorkout116GenerateId, workoutIds));

        List<Integer> ruleIds = generateResVideo116List.stream().map(ProjWorkout116GenerateResVideo116::getProjTemplate116RuleId).distinct().collect(Collectors.toList());
        Collection<ProjTemplate116Rule> rules = projTemplate116RuleService.listByIds(ruleIds);
        Map<Integer, ProjTemplate116RuleVO> ruleIdAndCountMap = rules.stream().collect(Collectors.toMap(ProjTemplate116Rule::getId, templateRule -> {
            ProjTemplate116RuleVO template116RuleVO = new ProjTemplate116RuleVO();
            BeanUtils.copyProperties(templateRule, template116RuleVO);
            return template116RuleVO;
        }));

        List<ProjWorkout116GenerateResVideo116BO> projWorkout116GenerateBOList = generateResVideo116List.stream().map(generateResVideo116 -> {
            ProjWorkout116GenerateResVideo116BO video116BO = new ProjWorkout116GenerateResVideo116BO();
            BeanUtils.copyProperties(generateResVideo116, video116BO);

            ResVideo116 resVideo116 = contextBO.getVideoIdMap().get(generateResVideo116.getResVideo116Id());
            video116BO.setCircuit(resVideo116.getCircuit());
            ProjTemplate116RuleVO template116RuleVO = ruleIdAndCountMap.get(generateResVideo116.getProjTemplate116RuleId());

            video116BO.setRuleRound(template116RuleVO.getRounds());
            video116BO.setResVideo116(resVideo116);
            video116BO.setRuleVO(template116RuleVO);
            return video116BO;
        }).collect(Collectors.toList());

        workoutTaiChiService.computeDurationWithCircuit(contextBO, projWorkout116GenerateBOList);
        Map<Integer, List<ProjWorkout116GenerateResVideo116BO>> workoutVideoRelationBoMap = projWorkout116GenerateBOList.stream().collect(Collectors.groupingBy(ProjWorkout116GenerateResVideo116BO::getProjWorkout116GenerateId));

        List<ProjWorkout116GenerateBO> projWorkout116BOList = taiChiWorkoutList.stream().map(workout -> {
            return new ProjWorkout116GenerateBO().setProjWorkout116Generate(workout)
                    .setProjWorkout116GenerateResVideo116List(workoutVideoRelationBoMap.get(workout.getId()));
        }).collect(Collectors.toList());

        List<String> languageList = req.getLanguages();
        workoutTaiChiService.generateFile(projWorkout116BOList, contextBO, languageList, req.getVideoFlag(), req.getAudioFlag(), Boolean.FALSE);
        updateWorkoutAndRelation4TaiChi(projWorkout116BOList, taiChiWorkoutList);
    }

    private void updateWorkout4ChairYoga(Collection<ProjWorkout116Generate> chairYogaWorkoutList, ProjWorkout116GenerateM3u8Req req) {

        if (CollectionUtils.isEmpty(chairYogaWorkoutList)) {
            return;
        }

        Set<Integer> workoutIds = chairYogaWorkoutList.stream().map(ProjWorkout116Generate::getId).collect(Collectors.toSet());
        List<ProjWorkout116GenerateResVideo116> generateResVideo116List = projWorkout116GenerateResVideo116Service.list(new LambdaQueryWrapper<ProjWorkout116GenerateResVideo116>()
                .in(ProjWorkout116GenerateResVideo116::getProjWorkout116GenerateId, workoutIds));
        List<Integer> ruleIds = generateResVideo116List.stream().map(ProjWorkout116GenerateResVideo116::getProjTemplate116RuleId).distinct().collect(Collectors.toList());

        Collection<ProjTemplate116Rule> rules = projTemplate116RuleService.listByIds(ruleIds);
        Map<Integer, ProjTemplate116RuleVO> ruleIdAndCountMap = rules.stream().collect(Collectors.toMap(ProjTemplate116Rule::getId, templateRule -> {
            ProjTemplate116RuleVO template116RuleVO = new ProjTemplate116RuleVO();
            BeanUtils.copyProperties(templateRule, template116RuleVO);
            return template116RuleVO;
        }));
        List<String> validateLangList = req.getLanguages()
                .stream()
                .filter(e -> ObjUtil.notEqual(GlobalConstant.DEFAULT_LANGUAGE, e))
                .collect(Collectors.toList());
        String exerciseTypeName = chairYogaWorkoutList.stream().findFirst().get().getExerciseType();
        ProjWorkout116ContextBO contextBO = createContext(validateLangList,null, exerciseTypeName);
        List<ProjWorkout116GenerateResVideo116BO> projWorkout116GenerateBOList = generateResVideo116List.stream().map(generateResVideo116 -> {
            ProjWorkout116GenerateResVideo116BO video116BO = new ProjWorkout116GenerateResVideo116BO();
            BeanUtils.copyProperties(generateResVideo116, video116BO);

            ResVideo116 resVideo116 = contextBO.getVideoIdMap().get(generateResVideo116.getResVideo116Id());
            video116BO.setCircuit(resVideo116.getCircuit());
            ProjTemplate116RuleVO template116RuleVO = ruleIdAndCountMap.get(generateResVideo116.getProjTemplate116RuleId());

            video116BO.setRuleRound(template116RuleVO.getRounds());
            video116BO.setResVideo116(resVideo116);
            video116BO.setRuleVO(template116RuleVO);
            return video116BO;
        }).collect(Collectors.toList());

        String position = chairYogaWorkoutList.stream().findFirst().get().getPosition();
        workoutChairYogaService.computeDurationWithCircuit(contextBO, position, projWorkout116GenerateBOList);
        Map<Integer, List<ProjWorkout116GenerateResVideo116BO>> workoutVideoRelationBoMap = projWorkout116GenerateBOList.stream().collect(Collectors.groupingBy(ProjWorkout116GenerateResVideo116BO::getProjWorkout116GenerateId));

        List<ProjWorkout116GenerateBO> projWorkout116BOList = chairYogaWorkoutList.stream().map(workout -> {
            return new ProjWorkout116GenerateBO().setProjWorkout116Generate(workout)
                    .setProjWorkout116GenerateResVideo116List(workoutVideoRelationBoMap.get(workout.getId()));
        }).collect(Collectors.toList());

        List<String> languageList = req.getLanguages();
        workoutChairYogaService.generateFile(projWorkout116BOList, contextBO, languageList, Boolean.FALSE);
        updateWorkoutAndRelation4TaiChi(projWorkout116BOList, chairYogaWorkoutList);
    }

    private void updateWorkout4Dumbbell(Collection<ProjWorkout116Generate> dumbbellWorkoutList, ProjWorkout116GenerateM3u8Req req) {

        if (CollectionUtils.isEmpty(dumbbellWorkoutList)) {
            return;
        }

        String exerciseTypeName = dumbbellWorkoutList.stream().findFirst().get().getExerciseType();
        ProjWorkout116ContextBO contextBO = createContext(req.getLanguages(),null, exerciseTypeName);
        Set<Integer> workoutIds = dumbbellWorkoutList.stream().map(ProjWorkout116Generate::getId).collect(Collectors.toSet());
        List<ProjWorkout116GenerateResVideo116> generateResVideo116List = projWorkout116GenerateResVideo116Service.list(new LambdaQueryWrapper<ProjWorkout116GenerateResVideo116>()
                .in(ProjWorkout116GenerateResVideo116::getProjWorkout116GenerateId, workoutIds));

        List<Integer> ruleIds = generateResVideo116List.stream().map(ProjWorkout116GenerateResVideo116::getProjTemplate116RuleId).distinct().collect(Collectors.toList());
        Collection<ProjTemplate116Rule> rules = projTemplate116RuleService.listByIds(ruleIds);
        Map<Integer, ProjTemplate116RuleVO> ruleIdAndCountMap = rules.stream().collect(Collectors.toMap(ProjTemplate116Rule::getId, templateRule -> {
            ProjTemplate116RuleVO template116RuleVO = new ProjTemplate116RuleVO();
            BeanUtils.copyProperties(templateRule, template116RuleVO);
            return template116RuleVO;
        }));

        List<ProjWorkout116GenerateResVideo116BO> projWorkout116GenerateBOList = generateResVideo116List.stream().map(generateResVideo116 -> {
            ProjWorkout116GenerateResVideo116BO video116BO = new ProjWorkout116GenerateResVideo116BO();
            BeanUtils.copyProperties(generateResVideo116, video116BO);

            ResVideo116 resVideo116 = contextBO.getVideoIdMap().get(generateResVideo116.getResVideo116Id());
            video116BO.setCircuit(resVideo116.getCircuit());
            ProjTemplate116RuleVO template116RuleVO = ruleIdAndCountMap.get(generateResVideo116.getProjTemplate116RuleId());

            video116BO.setRuleRound(template116RuleVO.getRounds());
            video116BO.setResVideo116(resVideo116);
            video116BO.setRuleVO(template116RuleVO);
            return video116BO;
        }).collect(Collectors.toList());

        workoutTaiChiService.computeDurationWithCircuit(contextBO, projWorkout116GenerateBOList);
        Map<Integer, List<ProjWorkout116GenerateResVideo116BO>> workoutVideoRelationBoMap = projWorkout116GenerateBOList.stream().collect(Collectors.groupingBy(ProjWorkout116GenerateResVideo116BO::getProjWorkout116GenerateId));

        List<ProjWorkout116GenerateBO> projWorkout116BOList = dumbbellWorkoutList.stream().map(workout -> {
            return new ProjWorkout116GenerateBO().setProjWorkout116Generate(workout)
                    .setProjWorkout116GenerateResVideo116List(workoutVideoRelationBoMap.get(workout.getId()));
        }).collect(Collectors.toList());

        List<String> languageList = req.getLanguages();
        workoutDumbbellService.generateFile(projWorkout116BOList, contextBO, languageList, Boolean.FALSE);
        updateWorkoutAndRelation4TaiChi(projWorkout116BOList, dumbbellWorkoutList);
    }

    @Override
    public ProjWorkout116ContextBO createContext(List<String> langList , List<Integer> exerciseIdList, String exerciseTypeName) {

        List<ResVideo116> videoList = resVideo116Service.listByExerciseType(Collections.singletonList(exerciseTypeName));
        List<ResVideo116> workout4VideoList = videoList;
        if (CollectionUtils.isNotEmpty(exerciseIdList)) {
            Map<Integer, ResVideo116> taiChiVideoIdMap = videoList.stream().collect(Collectors.toMap(ResVideo116::getId, v -> v, (k1, k2) -> k2));
            workout4VideoList = exerciseIdList.stream().map(exerciseId -> {
                if (!taiChiVideoIdMap.containsKey(exerciseId)) {
                    throw new BizException(String.format("Video is not find or not tai chi, id : %s.", exerciseId));
                }
                return taiChiVideoIdMap.get(exerciseId);
            }).collect(Collectors.toList());
        }

        ProjWorkout116ContextBO updateContext = new ProjWorkout116ContextBO();
        if (Objects.equals(exerciseTypeName, GENTLE_CHAIR_YOGA.getName())) {

            Map<String, ResVideo116> easyChairMap = new HashMap<>();
            //通过id 查询,只有一种性别
            Integer seatedId = oog116BizConfig.getChairYoga().getFemaleSoundConfig().getChairYogaVideo4Seated();
            if (Objects.nonNull(seatedId)) {
                ResVideo116 seatedVideo = resVideo116Service.getById(seatedId);
                workout4VideoList.add(seatedVideo);
                easyChairMap.put(Position116Enums.SEATED.getName(), seatedVideo);
            }

            Integer standingId = oog116BizConfig.getChairYoga().getFemaleSoundConfig().getChairYogaVideo4Standing();
            if (Objects.nonNull(standingId)) {
                ResVideo116 standingVideo = resVideo116Service.getById(standingId);
                workout4VideoList.add(standingVideo);
                easyChairMap.put(Position116Enums.STANDING.getName(), standingVideo);
            }

            updateContext.setEasyChairVideoMap(easyChairMap);
        }

        Map<Integer, ResVideo116> videoIdMap = workout4VideoList.stream().collect(Collectors.toMap(ResVideo116::getId, v -> v, (k1, k2) -> k2));
        updateContext.setVideoIdMap(videoIdMap);

        // duration
        Map<Integer, Integer> frontVideoDurationMap = Maps.newHashMap();
        Map<Integer, Integer> sideVideoDurationMap = Maps.newHashMap();
        video116SliceService.videoDurationCount(videoIdMap.keySet(), frontVideoDurationMap, sideVideoDurationMap);
        updateContext.setVideoSliceDurationMap(frontVideoDurationMap);
        updateContext.setSideVideoSliceDurationMap(sideVideoDurationMap);

        List<ResVideo116SliceDetailVO> sliceDetailVOList = video116SliceService.listByResVideoId(videoIdMap.keySet());
        if (CollectionUtils.isNotEmpty(sliceDetailVOList)) {
            Map<Integer, List<ResVideo116SliceDetailVO>>  videoSliceMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ResVideo116SliceDetailVO::getResVideo116Id));
            updateContext.setVideoSliceMap(videoSliceMap);
        }
        //移除原有翻译逻辑,改为新的
        //通过videoId 获取集合
        List<ProjResVideo116I18n> i18nModelList = workout4VideoList.stream()
                .map(ProjResVideo116I18n::new)
                .collect(Collectors.toList());
        Map<Object, ProjResVideo116I18n> i18nResult =
                speechI18nPubService.getI18nModelGroupByKey(i18nModelList, langList, ProjCodeEnums.OOG116);

        updateContext.setVideoI18nMap(i18nResult);
        return updateContext;
    }



    private void updateWorkout4Original(Collection<ProjWorkout116Generate> workoutList, ProjWorkout116GenerateM3u8Req req) {

        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        // 查询Workout关联的Video
        Set<Integer> workoutIds = workoutList.stream().map(ProjWorkout116Generate::getId).collect(Collectors.toSet());
        List<ProjWorkout116GenerateResVideo116> relationList = projWorkout116GenerateResVideo116Service.list(new LambdaQueryWrapper<ProjWorkout116GenerateResVideo116>()
                .in(ProjWorkout116GenerateResVideo116::getProjWorkout116GenerateId, workoutIds));
        List<ProjWorkout116GenerateResVideo116BO> projWorkout116GenerateBOList = BeanUtil.copyToList(relationList, ProjWorkout116GenerateResVideo116BO.class);
        Map<Integer, List<ProjWorkout116GenerateResVideo116BO>> workoutVideoRelationBoMap = projWorkout116GenerateBOList.stream().collect(Collectors.groupingBy(ProjWorkout116GenerateResVideo116BO::getProjWorkout116GenerateId));

        // 查询Workout关联的rule
        List<Integer> ruleIds = relationList.stream()
                .map(ProjWorkout116GenerateResVideo116::getProjTemplate116RuleId).distinct().collect(Collectors.toList());
        List<ProjTemplate116Rule> rules = projTemplate116RuleService.list(new LambdaQueryWrapper<ProjTemplate116Rule>().in(ProjTemplate116Rule::getId, ruleIds));
        List<ProjTemplate116RuleVO> projTemplate116RuleVOList = BeanUtil.copyToList(rules, ProjTemplate116RuleVO.class);

        Map<Integer, ProjTemplate116RuleVO> ruleMap = projTemplate116RuleVOList.stream().collect(Collectors.toMap(ProjTemplate116RuleVO::getId, Function.identity()));
        Map<Integer, List<ProjTemplate116RuleVO>> workoutRuleMap = workoutVideoRelationBoMap.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(),
                entry -> entry.getValue().stream().filter(relation -> ruleMap.containsKey(relation.getProjTemplate116RuleId())).map(relation -> ruleMap.get(relation.getProjTemplate116RuleId())).collect(Collectors.toList())));

        // 组装更新所需BO
        List<ProjWorkout116GenerateBO> projWorkout116BOList = workoutList.stream().map(workout -> {
            return new ProjWorkout116GenerateBO().setProjWorkout116Generate(workout)
                    .setProjWorkout116GenerateResVideo116List(workoutVideoRelationBoMap.get(workout.getId()))
                    .setRuleList(workoutRuleMap.get(workout.getId()));
        }).collect(Collectors.toList());

       //新翻译逻辑
        List<ResVideo116> resVideo116List = resVideo116Service.queryList();

        List<ProjResVideo116I18n> i18nModelList = resVideo116List.stream()
                .map(ProjResVideo116I18n::new)
                .collect(Collectors.toList());
        Map<Object, ProjResVideo116I18n> i18nResult =
                speechI18nPubService.getI18nModelGroupByKey(i18nModelList, req.getLanguages(), ProjCodeEnums.OOG116);

        // 生成m3u8
        generateFile(projWorkout116BOList, req.getLanguages(), i18nResult, req.getVideoFlag(), req.getAudioFlag(), Boolean.FALSE);

        // 更新workout和对应I18N表
        workout116GenerateService.updateWorkoutAndRelation(projWorkout116BOList, workoutList);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateWorkoutAndRelation(List<ProjWorkout116GenerateBO> projWorkout116BOList, Collection<ProjWorkout116Generate> workoutList) {

        if (CollectionUtils.isEmpty(projWorkout116BOList) || CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        updateBatchById(workoutList);
        updateI18n4Workout(projWorkout116BOList);
    }

    private void updateWorkoutAndRelation4TaiChi(List<ProjWorkout116GenerateBO> projWorkout116BOList, Collection<ProjWorkout116Generate> workoutList) {

        if (CollectionUtils.isEmpty(projWorkout116BOList) || CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        updateBatchById(workoutList);
        updateRelation(projWorkout116BOList);
        updateI18n4Workout(projWorkout116BOList);
    }

    private void updateRelation(List<ProjWorkout116GenerateBO> projWorkout116BOList) {

        List<ProjWorkout116GenerateResVideo116> resVideo116List = projWorkout116BOList.stream().map(workoutBO -> {
            return workoutBO.getProjWorkout116GenerateResVideo116List().stream().map(resVideo116 -> {
                ProjWorkout116GenerateResVideo116  generateResVideo116 = new ProjWorkout116GenerateResVideo116();
                BeanUtil.copyProperties(resVideo116, generateResVideo116);
                return generateResVideo116;
            }).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());

        projWorkout116GenerateResVideo116Service.updateBatchById(resVideo116List);
    }

    private void updateI18n4Workout(List<ProjWorkout116GenerateBO> projWorkout116BOList) {

        List<ProjWorkout116GenerateI18n> generateI18nList = projWorkout116BOList.stream().filter(generateBO -> CollectionUtils.isNotEmpty(generateBO.getI18nList())).map(generateBO -> generateBO.getI18nList().stream().peek(i18n -> i18n.setId(generateBO.getProjWorkout116Generate().getId())).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(generateI18nList)) {
            return;
        }

        List<Integer> workoutIds = generateI18nList.stream().map(ProjWorkout116GenerateI18n::getId).distinct().collect(Collectors.toList());
        Collection<ProjWorkout116GenerateI18n> existedGenerateList = projWorkout116GenerateI18nService.listByIds(workoutIds);
        if (CollectionUtils.isEmpty(existedGenerateList)) {
            projWorkout116GenerateI18nService.saveBatch(generateI18nList);
            return;
        }

        Table<Integer, String, ProjWorkout116GenerateI18n> existedGenerateI18nMap = HashBasedTable.create();
        existedGenerateList.forEach(existedGenerateI18n -> existedGenerateI18nMap.put(existedGenerateI18n.getId(), existedGenerateI18n.getLanguage(), existedGenerateI18n));
        List<ProjWorkout116GenerateI18n> updateList = Lists.newArrayList();
        List<ProjWorkout116GenerateI18n> insertList = Lists.newArrayList();

        generateI18nList.forEach(generateI18n -> {
            if (existedGenerateI18nMap.contains(generateI18n.getId(), generateI18n.getLanguage())) {
                updateList.add(generateI18n);
            } else {
                insertList.add(generateI18n);
            }
        });

        projWorkout116GenerateI18nService.saveBatch(insertList);
        updateGenerateI18n(updateList);
    }

    private void updateGenerateI18n(List<ProjWorkout116GenerateI18n> updateList) {

        updateList.forEach(generateI18n -> {
            LambdaUpdateWrapper<ProjWorkout116GenerateI18n> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProjWorkout116GenerateI18n::getId, generateI18n.getId());
            updateWrapper.eq(ProjWorkout116GenerateI18n::getLanguage, generateI18n.getLanguage());
            updateWrapper.set(ProjWorkout116GenerateI18n::getAudioJsonUrl, generateI18n.getAudioJsonUrl());
            updateWrapper.set(ProjWorkout116GenerateI18n::getUpdateTime, LocalDateTime.now());
            updateWrapper.set(ProjWorkout116GenerateI18n::getUpdateUser, RequestContextUtils.getLoginUserName());
            projWorkout116GenerateI18nService.update(updateWrapper);
        });
    }

    /**
     * 生成m3u8和json文件
     */
    private void generateFile(List<ProjWorkout116GenerateBO> projWorkout116BOList,
                              List<String> languageList,
                              Map<Object, ProjResVideo116I18n> videoI18nMap,
                              Boolean videoFlag,
                              Boolean audioFlag,
                              Boolean restrictionFlag) {
        Map<Integer, List<ResVideo116>> videoGroupByIdMap = resVideo116Service.queryList().stream().collect(
                Collectors.groupingBy(BaseModel::getId));

        // 系统音获取并验证是否完整(系统音不区分语言)
        Video116SysSoundBOWrapper video116SysSoundBOWrapper = oog116BizConfig.getOog116();

        AudioJson116AllBO firstAudio_en = video116SysSoundBOWrapper.getFirstAudio();
        AudioJson116AllBO threeTwoOneAudio_en = video116SysSoundBOWrapper.getThreeTwoOneAudio();
        AudioJson116AllBO goAudio_en = video116SysSoundBOWrapper.getGoAudio();
        List<AudioJson116AllBO> halfwayAudioList = video116SysSoundBOWrapper.getHalfwayAudioList();
        List<AudioJson116AllBO> halfwayAudioListCopy = new ArrayList<>(halfwayAudioList);
        Collections.shuffle(halfwayAudioListCopy);
        AudioJson116AllBO halfwayAudio_en = null;
        AudioJson116AllBO restAudio_en = video116SysSoundBOWrapper.getRestAudio();
        AudioJson116AllBO nextAudio_en = video116SysSoundBOWrapper.getNextAudio();
        AudioJson116AllBO finishAudio_en = video116SysSoundBOWrapper.getFinishAudio();
        AudioJson116AllBO fiveFourThreeTwoOneAudio_en = video116SysSoundBOWrapper.getFiveFourThreeTwoOneAudio();
        AudioJson116AllBO firstThreeTwoOneAudio_en = video116SysSoundBOWrapper.getFirstThreeTwoOneAudio();

        // 系统音多语言
        List<AudioJson116BO> soundList = new ArrayList<>();
        soundList.addAll(getBOListByAllBO(firstAudio_en));
        soundList.addAll(getBOListByAllBO(threeTwoOneAudio_en));
        soundList.addAll(getBOListByAllBO(goAudio_en));
        soundList.addAll(getBOListByAllBO(restAudio_en));
        soundList.addAll(getBOListByAllBO(nextAudio_en));
        soundList.addAll(getBOListByAllBO(finishAudio_en));
        soundList.addAll(getBOListByAllBO(fiveFourThreeTwoOneAudio_en));
        soundList.addAll(getBOListByAllBO(firstThreeTwoOneAudio_en));
        for(AudioJson116AllBO ha : halfwayAudioList) {
            soundList.addAll(getBOListByAllBO(ha));
        }
        // 系统音多语言
        Map<Integer, Map<String, AudioJson116BO>> audioJson116I18nMap = this.getSoundI18n(soundList, languageList);

        // 线程池队列长度，同时也是等待线程超时时长
        int capacity = 240;
        ExecutorService executor = new ThreadPoolExecutor(60, 60, 1L, TimeUnit.MINUTES,
                new ArrayBlockingQueue<Runnable>(capacity), Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        AtomicBoolean uploadSuccess = new AtomicBoolean(true);
        for (ProjWorkout116GenerateBO workout : projWorkout116BOList) {
            String gender = workout.getProjWorkout116Generate().getGender();
            Set<String> restriction = new HashSet<>();
            Map<Integer, List<ProjTemplate116RuleVO>> ruleGroup = workout.getRuleList().stream().collect(
                    Collectors.groupingBy(ProjTemplate116RuleVO::getId));
            List<ProjWorkout116GenerateResVideo116BO> relationList = workout.getProjWorkout116GenerateResVideo116List();

            List<Integer> ruleIdList = new ArrayList<>();
            LinkedHashMap<Integer, List<ProjWorkout116GenerateResVideo116BO>> relationGroup = relationList.stream()
                    .peek(o -> {
                        Integer id = o.getProjTemplate116RuleId();
                        if (!ruleIdList.contains(id)) {
                            ruleIdList.add(id);
                        }
                    }).collect(Collectors.groupingBy(
                            ProjWorkout116GenerateResVideo116BO::getProjTemplate116RuleId, LinkedHashMap::new, Collectors.toList()));
            // 视频列表
            TsTextMergeBO tsTextMergeBO = new TsTextMergeBO();
            TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
            int size = relationList.size();
            LinkedHashMap<String, List<AudioJson116BO>> audioListMap = new LinkedHashMap<>();
            for (String language : languageList) {
                audioListMap.put(language, new ArrayList<>(64));
            }

            BigDecimal startTime = new BigDecimal("0.1");
            int durationTotal = 0;
            BigDecimal calorie = BigDecimal.ZERO;
            int ruleSize = ruleIdList.size();
            for (int i = 0; i < ruleSize; i++) {
                Integer ruleId = ruleIdList.get(i);
                List<ProjWorkout116GenerateResVideo116BO> relations = relationGroup.get(ruleId);
                int relationSize = relations.size();
                ProjTemplate116RuleVO projTemplate116RuleVO = ruleGroup.get(ruleId).get(0);
                // 视频音频生成
                int rounds = projTemplate116RuleVO.getRounds();
                for (int j = 0; j < rounds; j++) {
                    for (int k = 0; k < relationSize; k++) {
                        if (!uploadSuccess.get()) {
                            throw new BizException("oog116 upload m3u8 and audio json failed");
                        }
                        BigDecimal playTime = new BigDecimal(durationTotal + "")
                                .divide(new BigDecimal("1000.0"), 1, RoundingMode.UP).add(startTime);
                        ProjWorkout116GenerateResVideo116BO relationItem = relations.get(k);
                        Integer resVideo116Id = relationItem.getResVideo116Id();
                        ResVideo116 resVideo116 = videoGroupByIdMap.get(resVideo116Id).get(0);
                        String restrictions = resVideo116.getRestriction();
                        if (StringUtils.isNotBlank(restrictions)) {
                            restriction.addAll(Arrays.stream(restrictions.split(",")).collect(Collectors.toList()));
                        }
                        int frontDuration = resVideo116.getFrontDuration();
                        int sideDuration = resVideo116.getSideDuration();

                        int currentPreViewTime = durationTotal + frontDuration + sideDuration;
                        int currentVideoTime = frontDuration * 3 + sideDuration * 2;
                        int currentEndTime = durationTotal + currentVideoTime;

                        // 处理正侧正侧正
                        tsTextMergeBO.addM3u8Text(
                                resVideo116.getFrontM3u8Text2k(),
                                resVideo116.getFrontM3u8Text1080p(),
                                resVideo116.getFrontM3u8Text720p(),
                                resVideo116.getFrontM3u8Text480p(),
                                resVideo116.getFrontM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                resVideo116.getSideM3u8Text2k(),
                                resVideo116.getSideM3u8Text1080p(),
                                resVideo116.getSideM3u8Text720p(),
                                resVideo116.getSideM3u8Text480p(),
                                resVideo116.getSideM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                resVideo116.getFrontM3u8Text2k(),
                                resVideo116.getFrontM3u8Text1080p(),
                                resVideo116.getFrontM3u8Text720p(),
                                resVideo116.getFrontM3u8Text480p(),
                                resVideo116.getFrontM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                resVideo116.getSideM3u8Text2k(),
                                resVideo116.getSideM3u8Text1080p(),
                                resVideo116.getSideM3u8Text720p(),
                                resVideo116.getSideM3u8Text480p(),
                                resVideo116.getSideM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                resVideo116.getFrontM3u8Text2k(),
                                resVideo116.getFrontM3u8Text1080p(),
                                resVideo116.getFrontM3u8Text720p(),
                                resVideo116.getFrontM3u8Text480p(),
                                resVideo116.getFrontM3u8Text360p());
                        //make 2532 m3u8
                        if (StringUtils.isNotBlank(resVideo116.getFrontM3u8Text2532()) && StringUtils.isNotBlank(resVideo116.getSideM3u8Text2532())) {
                            tsTextMerge2532BO.addM3u8Text(resVideo116.getFrontM3u8Text2532(), null, null, null, null);
                            tsTextMerge2532BO.addM3u8Text(resVideo116.getSideM3u8Text2532(), null, null, null, null);
                            tsTextMerge2532BO.addM3u8Text(resVideo116.getFrontM3u8Text2532(), null, null, null, null);
                            tsTextMerge2532BO.addM3u8Text(resVideo116.getSideM3u8Text2532(), null, null, null, null);
                            tsTextMerge2532BO.addM3u8Text(resVideo116.getFrontM3u8Text2532(), null, null, null, null);
                        }

                        boolean isLast = i == ruleSize - 1 && j == rounds - 1 && k == relationSize - 1;
                        if (CollectionUtil.isEmpty(halfwayAudioListCopy)) {
                            halfwayAudioListCopy = new ArrayList<>(halfwayAudioList);
                            if (halfwayAudioListCopy.size() > 1) {
                                halfwayAudioListCopy.remove(halfwayAudio_en);
                            }
                            Collections.shuffle(halfwayAudioListCopy);
                        }
                        halfwayAudio_en = halfwayAudioListCopy.remove(0);
                        for (String language : languageList) {
                            AudioJson116BO firstAudio;
                            AudioJson116BO finishAudio;
                            AudioJson116BO nextAudio;
                            AudioJson116BO firstThreeTwoOneAudio;
                            AudioJson116BO goAudio;
                            AudioJson116BO halfwayAudio;
                            AudioJson116BO fiveFourThreeTwoOneAudio;
                            AudioJson116BO threeTwoOneAudio;
                            AudioJson116BO restAudio;
                            // video 替换多语言字段
                            String nameAudioUrl = resVideo116.getNameAudioUrl();
                            String guidanceAudioUrl = resVideo116.getGuidanceAudioUrl();
                            if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                                firstAudio = getAudioJson116BOByGender(firstAudio_en,gender);
                                finishAudio = getAudioJson116BOByGender(finishAudio_en,gender);
                                nextAudio = getAudioJson116BOByGender(nextAudio_en,gender);
                                firstThreeTwoOneAudio =getAudioJson116BOByGender(firstThreeTwoOneAudio_en,gender);

                                goAudio = getAudioJson116BOByGender(goAudio_en,gender);
                                halfwayAudio = getAudioJson116BOByGender(halfwayAudio_en,gender);
                                fiveFourThreeTwoOneAudio = getAudioJson116BOByGender(fiveFourThreeTwoOneAudio_en,gender);
                                threeTwoOneAudio = getAudioJson116BOByGender(threeTwoOneAudio_en,gender);
                                restAudio = getAudioJson116BOByGender(restAudio_en,gender);
                            } else {
                                firstAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(firstAudio_en, gender).getSoundId()).get(language);
                                finishAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(finishAudio_en, gender).getSoundId()).get(language);
                                nextAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(nextAudio_en, gender).getSoundId()).get(language);
                                firstThreeTwoOneAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(firstThreeTwoOneAudio_en, gender).getSoundId()).get(language);
                                goAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(goAudio_en, gender).getSoundId()).get(language);
                                halfwayAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(halfwayAudio_en, gender).getSoundId()).get(language);
                                fiveFourThreeTwoOneAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(fiveFourThreeTwoOneAudio_en, gender).getSoundId()).get(language);
                                threeTwoOneAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(threeTwoOneAudio_en, gender).getSoundId()).get(language);
                                restAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(restAudio_en, gender).getSoundId()).get(language);
                                LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
                                ProjResVideo116I18n video116I18n = videoI18nMap.get(resVideo116.getId());
                                Map<LanguageEnums, AudioTranslateResultModel> nameMap = video116I18n.getResult() == null ? MapUtil.empty() :
                                        video116I18n.getResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                                Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = video116I18n.getGuidanceResult() == null ? MapUtil.empty() :
                                        video116I18n.getGuidanceResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                                nameAudioUrl = nameMap.get(le).getAudioUrl();
                                guidanceAudioUrl = guidanceMap.get(le).getAudioUrl();

                            }

                            List<AudioJson116BO> audioList = audioListMap.get(language);
                            if (i == 0 && j == 0 && k == 0) {
                                // first audio
                                addSysAudioJson(audioList, firstAudio, playTime);
                            } else if (isLast) {
                                // finish audio
                                addSysAudioJson(audioList, finishAudio, playTime);
                            } else {
                                // next audio
                                addSysAudioJson(audioList, nextAudio, playTime);
                            }

                            // exercise sound name
                            AudioJson116BO videoNameAudio = new AudioJson116BO(
                                    "" + resVideo116.getId(),
                                    fileService.getAbsoluteR2Url(nameAudioUrl),
                                    FireBaseUrlSubUtils.getFileName(nameAudioUrl), new BigDecimal(3), null,
                                    false,null,null,null,null,null
                            );
                            addSysAudioJson(audioList, videoNameAudio, playTime);

                            // three two one audio
                            addSysAudioJsonBackward(audioList, firstThreeTwoOneAudio, currentPreViewTime, firstThreeTwoOneAudio.getDuration());

                            // go audio
                            addSysAudioJson(audioList, goAudio, playTime);

                            // Guidance audio
                            AudioJson116BO guidanceAudio = new AudioJson116BO(
                                    "" + resVideo116.getId(),
                                    fileService.getAbsoluteR2Url(guidanceAudioUrl),
                                    FireBaseUrlSubUtils.getFileName(guidanceAudioUrl), new BigDecimal(6), null,
                                    true,null,null,null,null,null
                            );
                            addSysAudioJson(audioList, guidanceAudio, playTime);

                            // halfway audio
                            addSysAudioJson(audioList, halfwayAudio, playTime);
                            if (isLast) {
                                addSysAudioJsonBackward(audioList, fiveFourThreeTwoOneAudio, currentEndTime, fiveFourThreeTwoOneAudio.getDuration());
                            } else {
                                int restDuration = restAudio.getDuration();
                                int threeTwoOneTime = restDuration + threeTwoOneAudio.getDuration() + 500;
                                // three two one audio
                                addSysAudioJsonBackward(audioList, threeTwoOneAudio, currentEndTime, threeTwoOneTime);
                                // rest audio
                                addSysAudioJsonBackward(audioList, restAudio, currentEndTime, restDuration);
                            }
                        }

                        durationTotal += currentVideoTime;
                        calorie = NumberUtil.add(calorie, resVideo116.getCalorie());
                    }
                }
            }
            // 合并视频，保存数据
            ProjWorkout116Generate projWorkout116Generate = workout.getProjWorkout116Generate();
            restriction.remove(BizConstant.NONE);
            projWorkout116Generate
                    .setDataVersion(1)
                    .setDuration(durationTotal)
                    .setCalorie(calorie.setScale(0, RoundingMode.HALF_UP).intValue())
                    .setPosition(workout.getProjWorkout116Generate().getPosition())
                    .setFileStatus(GlobalConstant.ONE);
            if (ObjUtil.equal(Boolean.TRUE, restrictionFlag)) {
                projWorkout116Generate.setRestriction(String.join(",", Restriction116Enums.eliminateRestriction(restriction)))
                        .setRestrictionSum(workout.getRestrictionList()
                                .stream()
                                .mapToInt(Restriction116Enums::getValue)
                                .sum());
            }
            if (ObjUtil.equal(Boolean.TRUE, audioFlag)) {
                HashSet<String> audioLanguageSet = new HashSet<>();
                String audioLanguages = projWorkout116Generate.getAudioLanguages();
                if (StrUtil.isNotBlank(audioLanguages)) {
                    audioLanguageSet.addAll(Arrays.stream(audioLanguages.split(StringPool.COMMA)).collect(Collectors.toSet()));
                }
                audioLanguageSet.addAll(languageList);
                projWorkout116Generate.setAudioLanguages(StringUtils.join(audioLanguageSet, StringPool.COMMA));
            }

            executor.execute(() -> uploadFile(workout, uploadSuccess, tsTextMergeBO, audioListMap, tsTextMerge2532BO, videoFlag, audioFlag));

        }
        if (!uploadSuccess.get()) {
            executor.shutdownNow();
            throw new BizException("oog116 upload m3u8 and audio json failed");
        }
        executor.shutdown();
        try {
            if (!executor.awaitTermination(10, TimeUnit.MINUTES)) {
                throw new BizException("waiting oog116 upload m3u8 and audio json overtime");
            }
        } catch (InterruptedException e) {
            throw new BizException("oog116 executor interrupted exception");
        }
        if (!uploadSuccess.get()) {
            executor.shutdownNow();
            throw new BizException("oog116 upload m3u8 and audio json failed");
        }
    }
    private List<AudioJson116BO> getBOListByAllBO(AudioJson116AllBO allBO) {
        return Lists.newArrayList(allBO.getFemaleAudioJson116BO(), allBO.getMaleAudioJson116BO());
    }

    private AudioJson116BO getAudioJson116BOByGender(AudioJson116AllBO audioJson116AllBO,String gender) {
        if (FEMALE.getName().equalsIgnoreCase(gender))
        {
            return audioJson116AllBO.getFemaleAudioJson116BO();
        }
        return  audioJson116AllBO.getMaleAudioJson116BO();
    }

    /**
     * 上传m3u8和audio json，同时将返回的url设置到ProjWorkout116Generate实体内    private List<TsMergeBO> videoList;
     * private List<AudioJson116BO> audioList;
     */
    private void uploadFile(ProjWorkout116GenerateBO workoutBO,
                            AtomicBoolean uploadSuccess,
                            TsTextMergeBO tsTextMergeBO,
                            LinkedHashMap<String, List<AudioJson116BO>> audioListMap,
                            TsTextMergeBO tsTextMerge2532BO,
                            Boolean videoFlag,
                            Boolean audioFlag) {
        if (!uploadSuccess.get()) {
            throw new BizException("oog116 upload m3u8 and audio json failed");
        }

        try {
            ProjWorkout116Generate projWorkout116Generate = workoutBO.getProjWorkout116Generate();
            if (ObjUtil.equal(Boolean.TRUE, videoFlag)) {
                // 合并视频，保存数据
                UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(
                        tsTextMergeBO, "project-workout116-m3u8");
                String video2532Url = null;
                if (!tsTextMerge2532BO.getM3u8Text2kList().isEmpty()) {
                    UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8(tsTextMerge2532BO, "project-workout116-m3u8");
                    video2532Url = video2532R2Info.getFileRelativeUrl();
                }
                projWorkout116Generate.setVideoUrl(videoR2Info.getFileRelativeUrl())
                        .setVideo2532Url(video2532Url);
            }

            if (ObjUtil.equal(Boolean.TRUE, audioFlag)) {
                // 保存英语以外的语种
                List<ProjWorkout116GenerateI18n> i18nList = new ArrayList<>();
                for (String language : audioListMap.keySet()) {
                    List<AudioJson116BO> audioList = audioListMap.get(language);
                    // 上传音频json
                    UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(
                            JacksonUtil.toJsonString(audioList), "project-workout116-json");
                    if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                        projWorkout116Generate.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                    } else {
                        ProjWorkout116GenerateI18n i18n = new ProjWorkout116GenerateI18n();
                        i18n.setLanguage(language);
                        i18n.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                        i18nList.add(i18n);
                    }
                }
                workoutBO.setI18nList(i18nList);
            }
        } catch (Exception e) {
            uploadSuccess.set(false);
            log.warn("oog116 upload failed", e);
        }
    }

    /**
     * 添加系统音
     * 时间点=sysAudioJson.getTime()+playTime
     */
    private void addSysAudioJson(List<AudioJson116BO> audioList, AudioJson116BO sysAudioJson, BigDecimal playTime) {
        AudioJson116BO audioJsonBO = new AudioJson116BO(
                sysAudioJson.getId(),
                sysAudioJson.getUrl(),
                sysAudioJson.getName(),
                NumberUtil.add(playTime, sysAudioJson.getTime()),
                sysAudioJson.getDuration(),
                sysAudioJson.isClose(),
                sysAudioJson.getSoundId(),
                sysAudioJson.getNeedTranslation(),
                sysAudioJson.getGender(),
                sysAudioJson.getSoundScript(),
                sysAudioJson.getCoreVoiceConfigI18nId()
        );
        audioList.add(audioJsonBO);
    }

    /**
     * 添加系统音 反着计算
     * 时间点=endTime - subTime
     */
    private void addSysAudioJsonBackward(List<AudioJson116BO> audioList, AudioJson116BO sysAudioJson, Integer endTime, Integer subTime) {

        int time = endTime - subTime;
        BigDecimal playTime = new BigDecimal(time + "").divide(new BigDecimal("1000.0"), 1, RoundingMode.HALF_UP);

        AudioJson116BO audioJsonBO = new AudioJson116BO(
                sysAudioJson.getId(),
                sysAudioJson.getUrl(),
                sysAudioJson.getName(),
                playTime,
                sysAudioJson.getDuration(),
                sysAudioJson.isClose(),
                sysAudioJson.getGender(),
                sysAudioJson.getSoundScript(),
                sysAudioJson.getCoreVoiceConfigI18nId()
        );
        audioList.add(audioJsonBO);
    }


    /**
     * 生成workout、workout和video的关系，未入库
     */
    private ProjWorkout116GenerateBO generateWorkout(ProjWorkout116ContextBO context,
                                                     List<Restriction116Enums> restrictionList,
                                                     Position116Enums position,
                                                     int index, List<ResImage> resImages,
                                                     Equipment116Enums equipment,
                                                     ExerciseType116Enums exerciseType,
                                                     Gender116Enums gender) {
        ResImage resImage = resImages.get(index % resImages.size());
        ProjWorkout116Generate projWorkout116 = new ProjWorkout116Generate();
        Integer projTemplate116Id = context.getProjTemplate116Id();
        Integer projTemplate116TaskId = context.getProjTemplate116TaskId();
        projWorkout116.setProjTemplate116Id(projTemplate116Id)
                .setProjTemplate116TaskId(projTemplate116TaskId)
                .setPosition(position.getName())
                .setResImageId(resImage.getId())
                .setEquipment(equipment.getName())
                .setExerciseType(exerciseType.getName())
                .setGender(gender.getName());
        ProjWorkout116GenerateBO projWorkout116GenerateBO = new ProjWorkout116GenerateBO();
        projWorkout116GenerateBO.setProjWorkout116Generate(projWorkout116);
        List<ProjWorkout116GenerateResVideo116BO> projWorkoutResVideoList = new ArrayList<>(32);
        projWorkout116GenerateBO.setProjWorkout116GenerateResVideo116List(projWorkoutResVideoList);
        List<String> restrictionNameList = restrictionList.stream().map(Restriction116Enums::getName).collect(Collectors.toList());
        projWorkoutResVideoList.addAll(assembleVideo(context, restrictionNameList, position, TYPE_WARM_UP, Equipment116Enums.NONE, exerciseType, gender));
        projWorkoutResVideoList.addAll(assembleVideo(context, restrictionNameList, position, TYPE_MAIN, equipment, exerciseType, gender));
        projWorkoutResVideoList.addAll(assembleVideo(context, restrictionNameList, position, TYPE_COOL_DOWN, Equipment116Enums.NONE, exerciseType, gender));
        List<ProjTemplate116RuleVO> ruleVOList = projWorkoutResVideoList.stream().map(
                        ProjWorkout116GenerateResVideo116BO::getRuleVO)
                .distinct()
                .sorted(Comparator.comparing(ProjTemplate116RuleVO::getId))
                .collect(Collectors.toList());
        projWorkout116GenerateBO.setRuleList(ruleVOList);
        return projWorkout116GenerateBO;
    }

    private Collection<? extends ProjWorkout116GenerateResVideo116BO> assembleVideo(ProjWorkout116ContextBO context,
                                                                                    List<String> restrictionList,
                                                                                    Position116Enums position,
                                                                                    String videoType,
                                                                                    Equipment116Enums equipment,
                                                                                    ExerciseType116Enums exerciseType,
                                                                                    Gender116Enums gender) {
        List<ResVideo116> videoList = context.getVideoList(videoType, restrictionList, position, equipment, exerciseType, gender);

        Map<String, List<ResVideo116>> leftRightVideoMap = context.getLeftRightVideoMap(
                videoType, restrictionList, position, equipment, exerciseType, gender
        );
        List<ProjTemplate116RuleVO> ruleVideoTypeList = context.getRuleVideoTypeMap().get(videoType);
        List<ProjWorkout116GenerateResVideo116BO> workoutGenerateResVideoList = new ArrayList<>(32);
        ProjTemplate116RuleVO rule = ruleVideoTypeList.get(0);
        Integer count = rule.getCount();
        List<ExerciseType116Enums> sameTargetExerciseTypeList = Arrays.asList(DUMBBELL, WALKING, DANCING, TAI_CHI);
        List<ResVideo116> ruleVideoList;
        if (TYPE_WARM_UP.equals(videoType) || TYPE_COOL_DOWN.equals(videoType) || sameTargetExerciseTypeList.contains(exerciseType)) {
            ruleVideoList = matchVideo(count, videoList, leftRightVideoMap);
        } else {
            final BigDecimal lowerBodyProportion = new BigDecimal("0.2");
            final BigDecimal fullBodyProportion = new BigDecimal("0.3");
            int lowerBodyCount = NumberUtil.round(NumberUtil.mul(count, lowerBodyProportion), GlobalConstant.ZERO, RoundingMode.CEILING).intValue();
            int fullBodyCount = NumberUtil.round(NumberUtil.mul(count, fullBodyProportion), GlobalConstant.ZERO, RoundingMode.CEILING).intValue();
            Map<TargetEnums, List<ResVideo116>> targetGroup = videoList.stream().collect(Collectors.groupingBy(ResVideo116::getTarget));

            Map<TargetEnums, Map<String, List<ResVideo116>>> targetLeftRightGroup = new HashMap<>();
            leftRightVideoMap.forEach((key, value) -> {
                ResVideo116 resVideo = value.get(0);
                TargetEnums target = resVideo.getTarget();
                Map<String, List<ResVideo116>> targetLeftRightMap = new HashMap<>();
                targetLeftRightGroup.put(target, targetLeftRightMap);
                targetLeftRightMap.put(key, value);
            });
            ruleVideoList = matchVideo(lowerBodyCount, targetGroup.get(TargetEnums.LOWER_BODY), targetLeftRightGroup.get(TargetEnums.LOWER_BODY));
            ruleVideoList.addAll(matchVideo(fullBodyCount, targetGroup.get(TargetEnums.FULL_BODY), targetLeftRightGroup.get(TargetEnums.FULL_BODY)));
            int upperBodyCount = count - ruleVideoList.size();
            if(upperBodyCount > 0) {
                ruleVideoList.addAll(matchVideo(upperBodyCount, targetGroup.get(TargetEnums.UPPER_BODY), targetLeftRightGroup.get(TargetEnums.UPPER_BODY)));
            }
        }
        ruleVideoList.forEach(finalVideo -> {
            workoutGenerateResVideoList.add(createProjWorkoutResVideo(context, rule, finalVideo));
        });
        return workoutGenerateResVideoList;
    }

    /**
     * 创建workout和video的关系
     */
    private ProjWorkout116GenerateResVideo116BO createProjWorkoutResVideo(ProjWorkout116ContextBO context,
                                                                          ProjTemplate116RuleVO item,
                                                                          ResVideo116 resVideo116) {
        ProjWorkout116GenerateResVideo116BO projWorkoutResVideo = new ProjWorkout116GenerateResVideo116BO();
        projWorkoutResVideo
                .setRuleVO(item)
                .setProjTemplate116Id(context.getProjTemplate116Id())
                .setProjTemplate116RuleId(item.getId())
                .setResVideo116Id(resVideo116.getId())
                .setProjTemplate116TaskId(context.getProjTemplate116TaskId());
        return projWorkoutResVideo;
    }

    /**
     * 按照规则设置的数量匹配video
     *
     * @param count             当前规则需要匹配的video数量(最终ruleVideoList的size必须等于count)
     * @param videoList         当前规则可匹配的video
     * @param leftRightVideoMap 当前规则可匹配的left和right的video
     */
    private List<ResVideo116> matchVideo(int count,
                                         List<ResVideo116> videoList,
                                         Map<String, List<ResVideo116>> leftRightVideoMap) {
        if (count <= 0) {
            throw new BizException("generate warm up or cool down or main failed");
        }

        if (CollectionUtils.isEmpty(videoList) && MapUtils.isEmpty(leftRightVideoMap)) {
            throw new AutoGenerateException("generate warm up or cool down or main failed");
        }
        leftRightVideoMap = null == leftRightVideoMap ? new HashMap<>() : leftRightVideoMap;

        List<String> leftRightVideoKeyList = new ArrayList<>(leftRightVideoMap.keySet());
        Collections.shuffle(leftRightVideoKeyList);

        Collections.shuffle(videoList);
        List<ResVideo116> ruleVideoList = new ArrayList<>();
        int cycles = count * 20;
        while (cycles > 0 && ruleVideoList.size() < count) {
            cycles--;

            int videoSize = videoList.size();
            if ((count - ruleVideoList.size() >= 2 && !leftRightVideoKeyList.isEmpty())
                    && (CollectionUtils.isEmpty(videoList) || (RandomUtil.randomBoolean()) || (videoSize == 1 && NumberUtil.isEven(count - ruleVideoList.size())))) {
                // 包含left和right的
                List<ResVideo116> video116List = leftRightVideoMap.get(leftRightVideoKeyList.remove(0));
                ruleVideoList.addAll(video116List);
                continue;
            }
            if (!videoList.isEmpty()) {
                ruleVideoList.add(videoList.remove(0));
            }
        }
        if (ruleVideoList.size() != count) {
            log.warn("generate warm up or cool down or main failed,match size not equals rule count");
            throw new AutoGenerateException("generate warm up or cool down or main failed");
        }
        return ruleVideoList;
    }

    /**
     * 创建上下文对象，并对数据进行校验
     */
    private ProjWorkout116ContextBO creatContext(ProjTemplate116 template116,
                                                 Integer projTemplate116TaskId,
                                                 ProjInfo projInfo) {
        ProjWorkout116ContextBO context = new ProjWorkout116ContextBO();
        Integer projTemplate116Id = template116.getId();
        List<ExerciseType116Enums> exerciseType116Enums = template116.getTemplateType().getExerciseType116EnumsList();
        String appCode = projInfo.getAppCode();
        String languagesStr = projInfo.getLanguages();

        List<String> exerciseTypeList = exerciseType116Enums.stream().map(ExerciseType116Enums::getName).collect(Collectors.toList());
        List<ResVideo116> videoList = resVideo116Service.listByExerciseType(exerciseTypeList);

        // 按type进行分组
        Map<String, List<ResVideo116>> videoTypeMap = videoList.stream()
                .collect(Collectors.groupingBy(ResVideo116::getType));
        // video的type必须包含Warm Up/Cool Down/Main
        if (videoTypeMap.keySet().size() < 3) {
            throw new BizException("video type count not enough, must include Warm Up/Cool Down/Main");
        }

        // 按exerciseType进行分组
        Map<String, List<ResVideo116>> exerciseTypeMap = new HashMap<>();
        videoList.stream()
                .filter(video -> StringUtils.isNotBlank(video.getExerciseType()))
                .forEach(video -> {
                    for (String exerciseType : MyStringUtil.getSplitWithComa(video.getExerciseType())) {
                        List<ResVideo116> exerciseVideoList = exerciseTypeMap.getOrDefault(exerciseType, new ArrayList<>());
                        exerciseVideoList.add(video);
                        exerciseTypeMap.put(exerciseType, exerciseVideoList);
                    }
                });

        // 按exerciseType进行分组
        Map<String, List<ResVideo116>> genderMap = videoList.stream()
                .collect(Collectors.groupingBy(ResVideo116::getGender));
        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.NORMAL) && genderMap.keySet().size() < 2) {
            throw new BizException("exercise type count not enough, must include Female、Male");
        }

        // 按exerciseType进行分组
        Map<String, List<ResVideo116>> equipmentMap = videoList.stream()
                .collect(Collectors.groupingBy(ResVideo116::getEquipment));
        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.NORMAL) && equipmentMap.keySet().size() < 3) {
            throw new BizException("equipment count not enough, must include No equipment、Dumbbell (lightweight)、Resistance band");
        }

        String[] languages = MyStringUtil.getSplitWithComa(languagesStr);
        // 添加默认语言到列表(数组)
        if (!Arrays.asList(languages).contains(GlobalConstant.DEFAULT_LANGUAGE)) {
            languages = ArrayUtil.append(languages, GlobalConstant.DEFAULT_LANGUAGE);
        }
        // 获取英语以外的语言
        List<String> languageList = Arrays.stream(languages)
                .filter(o -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, o))
                .collect(Collectors.toList());

        Set<Integer> videoIdSet = videoList.stream().map(ResVideo116::getId).collect(Collectors.toSet());
        Map<Object, ProjResVideo116I18n>  videoI18nMap = new HashMap<>();
        // 为空不校验翻译
        if (!languageList.isEmpty()) {
            List<ProjResVideo116I18n> i18nModelList = videoList.stream()
                    .map(ProjResVideo116I18n::new)
                    .collect(Collectors.toList());
            // 相关翻译数据是否完整
            videoI18nMap =  speechI18nPubService.getI18nModelGroupByKey(i18nModelList, languageList, ProjCodeEnums.OOG116);
            for (ResVideo116 resVideo116 : videoList) {
                ProjResVideo116I18n resVideo116I18n = videoI18nMap.get(resVideo116.getId());
                if (resVideo116I18n == null) {
                    throw new BizException("The Video data translation language incomplete");
                }
                Map<LanguageEnums, AudioTranslateResultModel> resultMap = resVideo116I18n.getResult() == null ? MapUtil.empty() :
                        resVideo116I18n.getResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                for (String language : languageList) {
                    LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
                    if (resultMap.get(le) == null) {
                        throw new BizException("The Video data translation language incomplete");
                    }
                }
            }
        }

        Map<String, List<ResVideo116>> videoPositionMap = videoList.stream()
                .collect(Collectors.groupingBy(ResVideo116::getPosition));

        Map<String, List<ResVideo116>> leftRightMap = getLeftRightMapWithTemplate(template116.getTemplateType(), videoList);

        Map<String, ResVideo116> easyChairMap = Maps.newHashMap();
        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.CHAIR_YOGA)) {
            workoutChairYogaService.setConfigChairYoga(easyChairMap);
            videoIdSet.addAll(easyChairMap.values().stream().map(ResVideo116::getId).collect(Collectors.toSet()));
        }

        Map<Integer, Integer> frontVideoDurationMap = Maps.newHashMap();
        Map<Integer, Integer> sideVideoDurationMap = Maps.newHashMap();
        Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap = Maps.newHashMap();
        if (Objects.equals(template116.getTemplateType(), Template116TypeEnums.TAI_CHI) ||
                Objects.equals(template116.getTemplateType(), Template116TypeEnums.CHAIR_YOGA) ||
                        Objects.equals(template116.getTemplateType(), Template116TypeEnums.DUMBBELL_MIDWEIGHT)) {

            video116SliceService.videoDurationCount(videoIdSet, frontVideoDurationMap, sideVideoDurationMap);
            List<ResVideo116SliceDetailVO> sliceDetailVOList = video116SliceService.listByResVideoId(videoIdSet);
            if (CollectionUtils.isNotEmpty(sliceDetailVOList)) {
                videoSliceMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ResVideo116SliceDetailVO::getResVideo116Id));
            }
        }

        List<ProjTemplate116RuleVO> allRuleList = projTemplate116RuleService.findByProjTemplate116Id(projTemplate116Id);
        if (CollectionUtils.isEmpty(allRuleList)) {
            throw new BizException("template rule count not enough, projTemplate116Id:" + projTemplate116Id);
        }

        Set<String> baseRuleTypeSet = Sets.newHashSet(ResVideo116TypeEnum.WARM_UP.getValue(), ResVideo116TypeEnum.MAIN.getValue(), ResVideo116TypeEnum.COOL_DOWN.getValue());
        List<ProjTemplate116RuleVO> ruleList = allRuleList.stream().filter(rule -> baseRuleTypeSet.contains(rule.getVideoType())).collect(Collectors.toList());
        Map<String, List<ProjTemplate116RuleVO>> ruleVideoTypeMap = ruleList.stream()
                .collect(Collectors.groupingBy(ProjTemplate116RuleVO::getVideoType));

        // rule的videoType必须包含warm_up、main、cool_down
        if (ruleVideoTypeMap.size() < 3) {
            throw new BizException("template rule count not enough, must include warm_up、main、cool_down");
        }

        Map<String, List<ResVideo116>> restrictionMap = videoList.stream()
                .collect(Collectors.groupingBy(ResVideo116::getRestriction));

        Map<String, List<ResVideo116>> videoRestrictionMap = new HashMap<>(32);

        for (Map.Entry<String, List<ResVideo116>> entry : restrictionMap.entrySet()) {
            String key = entry.getKey();
            List<String> restrictionList = Arrays.stream(key.split(",")).collect(Collectors.toList());
            for (String restriction : restrictionList) {
                List<ResVideo116> video116List = videoRestrictionMap.getOrDefault(restriction, new ArrayList<>());
                video116List.addAll(entry.getValue());
                videoRestrictionMap.put(restriction, video116List);
            }
        }

        if (videoRestrictionMap.keySet().size() < Restriction116Enums.values().length) {
            throw new BizException("video restriction count not enough, must include all restriction");
        }
        List<ResImage> resImageList = resImageService.find(appCode, ResImageConstant.FUNCTION_TEMPLATE_WORKOUT);
        Map<String, List<ResImage>> imageGenderGroup = resImageList.stream().collect(Collectors.groupingBy(ResImage::getGender));
        Map<String, List<ResImage>> femaleImageGroupByPoint = imageGenderGroup.getOrDefault(FEMALE.getName(),new ArrayList<>()).stream().collect(
                Collectors.groupingBy(ResImage::getPoint));

        Map<Integer, ResVideo116> videoIdMap = videoList.stream().collect(Collectors.toMap(ResVideo116::getId, v -> v, (k1, k2) -> k2));

        Map<String, List<ResImage>> maleImageGroupByPoint = imageGenderGroup.getOrDefault(MALE.getName(),new ArrayList<>()).stream().collect(
                Collectors.groupingBy(ResImage::getPoint));
        context.setTemplate116(template116)
                .setVideoIdMap(videoIdMap)
                .setProjTemplate116Id(projTemplate116Id)
                .setProjTemplate116TaskId(projTemplate116TaskId)
                .setLanguageList(languageList)
                .setVideoTypeMap(videoTypeMap)
                .setVideoSliceMap(videoSliceMap)
                .setEasyChairVideoMap(easyChairMap)
                .setVideoSliceDurationMap(frontVideoDurationMap)
                .setSideVideoSliceDurationMap(sideVideoDurationMap)
                .setVideoPositionMap(videoPositionMap)
                .setVideoLeftRightMap(leftRightMap)
                .setAllVideo(videoList)
                .setVideoI18nMap(videoI18nMap)
                .setRuleVideoTypeMap(ruleVideoTypeMap)
                .setRuleList(ruleList)
                .setVideoRestrictionMap(videoRestrictionMap)
                .setFemaleImageGroupByPoint(femaleImageGroupByPoint)
                .setMaleImageGroupByPoint(maleImageGroupByPoint)
                .setExerciseTypeMap(exerciseTypeMap)
                .setEquipmentMap(equipmentMap)
                .setGenderMap(genderMap);
        return context;
    }

    private Map<String, List<ResVideo116>> getLeftRightMapWithTemplate(Template116TypeEnums template116TypeEnums, List<ResVideo116> videoList) {

        if (Objects.equals(template116TypeEnums.getCode(), Template116TypeEnums.DUMBBELL_MIDWEIGHT.getCode())) {
            return getLeftRightMap4Dumbbell(videoList);
        } else {
            return getLeftRightMap(videoList);
        }
    }

    private Map<String, List<ResVideo116>> getLeftRightMap4Dumbbell(List<ResVideo116> videoList) {

        Table<String, String, List<ResVideo116>> leftAndPositionMap = HashBasedTable.create();
        for (ResVideo116 video : videoList) {
            String leftRightName = ProjWorkout116ContextBO.getLeftRightName(video.getName());
            if (StringUtils.isBlank(leftRightName)) {
                continue;
            }

            List<ResVideo116> video116List = leftAndPositionMap.get(leftRightName, video.getPosition());
            video116List = CollUtil.isEmpty(video116List) ? new ArrayList<>() : video116List;
            if (video116List.isEmpty()) {
                video116List.add(video);
            } else if (video116List.size() == 1) {
                ResVideo116 existedVideo = video116List.get(0);
                if (ProjWorkout116ContextBO.isLeft(existedVideo.getName()) && ProjWorkout116ContextBO.isRight(video.getName())) {
                    video116List.add(video);
                }
                if (ProjWorkout116ContextBO.isRight(existedVideo.getName()) && ProjWorkout116ContextBO.isLeft(video.getName())) {
                    video116List.remove(existedVideo);
                    video116List.add(video);
                    video116List.add(existedVideo);
                }
            }

            leftAndPositionMap.put(leftRightName, video.getPosition(), video116List);
        }

        Map<String, List<ResVideo116>> leftMap = new HashMap<>();
        leftAndPositionMap.rowMap().entrySet().forEach(entry -> {
            leftMap.put(entry.getKey(), entry.getValue().values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        });
        return leftMap;
    }

    /**
     * 获取所有左右的video
     */
    private Map<String, List<ResVideo116>> getLeftRightMap(List<ResVideo116> videoList) {
        Map<String, List<ResVideo116>> leftRightMap = new HashMap<>(64);
        for (ResVideo116 item : videoList) {
            String leftRightName = ProjWorkout116ContextBO.getLeftRightName(item.getName());

            if (StringUtils.isBlank(leftRightName)) {
                continue;
            }
            List<ResVideo116> video116List = leftRightMap.get(leftRightName);
            video116List = null == video116List ? new ArrayList<>() : video116List;
            if (video116List.isEmpty()) {
                video116List.add(item);
            } else if (video116List.size() == 1) {
                if (ProjWorkout116ContextBO.isRight(item.getName())) {
                    ResVideo116 video = video116List.get(0);
                    if (ProjWorkout116ContextBO.isLeft(video.getName())
                            && video.getPosition().equals(item.getPosition())
                            && video.getExerciseType().equals(item.getExerciseType())) {
                        video116List.add(item);
                    }
                } else {
                    ResVideo116 resVideo116 = video116List.get(0);
                    if (ProjWorkout116ContextBO.isRight(resVideo116.getName())
                            && resVideo116.getPosition().equals(item.getPosition())
                            && resVideo116.getExerciseType().equals(item.getExerciseType())
                            && resVideo116.getGender().equals(item.getGender())) {
                        video116List.clear();
                        video116List.add(item);
                        video116List.add(resVideo116);
                    }
                }
            }
            leftRightMap.put(leftRightName, video116List);
        }
        List<String> removeKeyList = new ArrayList<>();
        for (Map.Entry<String, List<ResVideo116>> entry : leftRightMap.entrySet()) {
            // 只有左右，所以必须是2
            if (entry.getValue().size() != 2) {
                removeKeyList.add(entry.getKey());
            }
        }
        removeKeyList.forEach(leftRightMap::remove);
        return leftRightMap;
    }

    /**
     * 校验template是否可用
     */
    private boolean checkTemplate(Integer projTemplate116TaskId, ProjTemplate116 template116) {
        Integer status = template116.getStatus();
        Integer delFlag = template116.getDelFlag();
        if (STATUS_ENABLE != status || YES == delFlag) {
            projTemplate116TaskService.changeState(
                    projTemplate116TaskId, FAIL, "template116 status not enable or is deleted");
            log.info("projTemplate116Task failed，status: {},del: {}", status, delFlag);
            return false;
        }
        return true;
    }


    private List<Integer> getTemplateIdList(ProjWorkout116GeneratePageReq generatePageReq) {
        Integer templateId = generatePageReq.getTemplateId();
        List<Integer> templateIdList = projTemplate116Service.queryIdList(generatePageReq.getTemplateStatus());
        if (null != templateId) {
            templateIdList = new ArrayList<>(CollUtil.intersection(Collections.singletonList(templateId), templateIdList));
        }
        return templateIdList;
    }

    private static Integer getRestrictionSum(ProjWorkout116GeneratePageReq generatePageReq) {
        String[] restrictionArr = generatePageReq.getRestrictionArr();
        Integer restrictionSum = null;
        if (restrictionArr != null) {
            int len = restrictionArr.length;
            if (len == 1 && Objects.equals(BizConstant.NONE, restrictionArr[0])) {
                restrictionSum = GlobalConstant.ZERO;
            } else if (len > 0) {
                restrictionSum = Restriction116Enums.sumByString(Arrays.asList(restrictionArr));
            }
        }
        return restrictionSum;
    }
}
