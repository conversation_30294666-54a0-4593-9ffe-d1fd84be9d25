package com.laien.web.biz.proj.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * recovery category116 详情 workout
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@ApiModel(value = "recovery category116 详情 workout", description = "recovery category116 详情 workout")
public class ProjRecoveryCategory116DetailWorkoutVO {

    @ApiModelProperty(value = "workout id")
    private Integer id;

    @ApiModelProperty(value = "workout 名称")
    private String name;

    @ApiModelProperty(value = "workout 封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "workout 类型")
    private String exerciseType;

    @ApiModelProperty(value = "workout 难度")
    private String difficulty;

    @ApiModelProperty(value = "workout 性别")
    private String gender;

    @ApiModelProperty(value = "workout 状态")
    private Integer status;
}
