package com.laien.web.biz.proj.oog116.request;

import com.laien.common.oog116.enums.Template116TypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: Template116 新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Template116 新增", description = "Template116 新增")
public class ProjTemplate116AddReq {

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "时长区间")
    private String durationRange;

    @ApiModelProperty(value = "模板类型")
    private Template116TypeEnums templateType;

    @ApiModelProperty(value = "生成多少天的")
    private Integer day;

    @ApiModelProperty(value = "warmUp 规则列表")
    private List<ProjTemplate116RuleReq> warmUpRuleList;
    @ApiModelProperty(value = "main 规则列表")
    private List<ProjTemplate116RuleReq> mainRuleList;
    @ApiModelProperty(value = "coolDown 规则列表")
    private List<ProjTemplate116RuleReq> coolDownRuleList;

//    @ApiModelProperty(value = "main(TaiChi) 规则列表")
//    private List<ProjTemplate116RuleReq> taiChiMainRuleList;
//
//    @ApiModelProperty(value = "warmUp(TaiChi) 规则列表")
//    private List<ProjTemplate116RuleReq> taiChiWarmUpRuleList;
//
//    @ApiModelProperty(value = "coolDown(TaiChi) 规则列表")
//    private List<ProjTemplate116RuleReq> taiChiCoolDownRuleList;

}
