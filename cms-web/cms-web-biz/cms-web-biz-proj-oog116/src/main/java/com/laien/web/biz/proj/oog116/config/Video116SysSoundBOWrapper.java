package com.laien.web.biz.proj.oog116.config;

import com.laien.web.biz.proj.oog116.bo.AudioJson116AllBO;
import com.laien.web.biz.proj.oog116.bo.AudioJson116BO;
import com.laien.web.biz.proj.oog116.bo.Video116SysSoundBO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class Video116SysSoundBOWrapper {
    private Video116SysSoundBO femaleSoundConfig;
    private Video116SysSoundBO maleSoundConfig;

    public AudioJson116AllBO getFirstAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO firstAudioMale = maleSoundConfig.getSysSoundByName("first", maleSoundConfig.getFirst());
            firstAudioMale.setTime(new BigDecimal(1));
            audioJson116AllBO.setMaleAudioJson116BO(firstAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO firstAudioFemale = femaleSoundConfig.getSysSoundByName("first", maleSoundConfig.getFirst());
            firstAudioFemale.setTime(new BigDecimal(1));
            audioJson116AllBO.setFemaleAudioJson116BO(firstAudioFemale);
        }
        return audioJson116AllBO;
    }

    /**
     * 获取第二个3 2 1音频
     */
    public AudioJson116AllBO getThreeTwoOneAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO threeTwoOneAudioMale = maleSoundConfig.getSysSoundByName("threeTwoOne", maleSoundConfig.getThreeTwoOne());
            threeTwoOneAudioMale.setTime(new BigDecimal(56));
            audioJson116AllBO.setMaleAudioJson116BO(threeTwoOneAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO threeTwoOneAudioFemale = femaleSoundConfig.getSysSoundByName("threeTwoOne", maleSoundConfig.getThreeTwoOne());
            threeTwoOneAudioFemale.setTime(new BigDecimal(56));
            audioJson116AllBO.setFemaleAudioJson116BO(threeTwoOneAudioFemale);
        }
        return audioJson116AllBO;
    }

    public List<AudioJson116AllBO> getHalfwayAudioList() {
        List<AudioJson116AllBO> audioJson116AllBOList = new ArrayList<>(32);
       for(int i=0;i<maleSoundConfig.getHalfwayList().size() || i<femaleSoundConfig.getHalfwayList().size(); i++) {
           AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
           if (maleSoundConfig != null) {
               AudioJson116BO halfwayAudioFemale = femaleSoundConfig.getSysSoundByName("halfway", femaleSoundConfig.getHalfwayList().get(i));
               halfwayAudioFemale.setTime(new BigDecimal(60));
               audioJson116AllBO.setFemaleAudioJson116BO(halfwayAudioFemale);
           }
           if (femaleSoundConfig != null) {
               AudioJson116BO halfwayAudioMale = maleSoundConfig.getSysSoundByName("halfway", maleSoundConfig.getHalfwayList().get(i));
               halfwayAudioMale.setTime(new BigDecimal(60));
               audioJson116AllBO.setMaleAudioJson116BO(halfwayAudioMale);
           }
           audioJson116AllBOList.add(audioJson116AllBO);
       }
        return audioJson116AllBOList;
    }

    /**
     * 获取第一个3 2 1音频
     */
    public AudioJson116AllBO getFirstThreeTwoOneAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO threeTwoOneAudioMale = maleSoundConfig.getSysSoundByName("threeTwoOne", maleSoundConfig.getThreeTwoOne());
            threeTwoOneAudioMale.setTime(new BigDecimal(13));
            audioJson116AllBO.setMaleAudioJson116BO(threeTwoOneAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO threeTwoOneAudioFemale = femaleSoundConfig.getSysSoundByName("threeTwoOne", maleSoundConfig.getThreeTwoOne());
            threeTwoOneAudioFemale.setTime(new BigDecimal(13));
            audioJson116AllBO.setFemaleAudioJson116BO(threeTwoOneAudioFemale);
        }
        return audioJson116AllBO;
    }

    public AudioJson116AllBO getGoAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO goAudioMale = maleSoundConfig.getSysSoundByName("go", maleSoundConfig.getGo());
            goAudioMale.setTime(new BigDecimal(30));
            audioJson116AllBO.setMaleAudioJson116BO(goAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO goAudioFemale = femaleSoundConfig.getSysSoundByName("go", maleSoundConfig.getGo());
            goAudioFemale.setTime(new BigDecimal(30));
            audioJson116AllBO.setFemaleAudioJson116BO(goAudioFemale);
        }
        return audioJson116AllBO;
    }

    public AudioJson116AllBO getRestAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO restAudioMale = maleSoundConfig.getSysSoundByName("rest", maleSoundConfig.getRest());
            restAudioMale.setTime(new BigDecimal(59));
            audioJson116AllBO.setMaleAudioJson116BO(restAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO restAudioFemale = femaleSoundConfig.getSysSoundByName("rest", maleSoundConfig.getRest());
            restAudioFemale.setTime(new BigDecimal(59));
            audioJson116AllBO.setFemaleAudioJson116BO(restAudioFemale);
        }
        return audioJson116AllBO;
    }

    public AudioJson116AllBO getNextAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO nextAudioMale = maleSoundConfig.getSysSoundByName("next", maleSoundConfig.getNext());
            nextAudioMale.setTime(new BigDecimal(1));
            audioJson116AllBO.setMaleAudioJson116BO(nextAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO nextAudioFemale = femaleSoundConfig.getSysSoundByName("next", maleSoundConfig.getNext());
            nextAudioFemale.setTime(new BigDecimal(1));
            audioJson116AllBO.setFemaleAudioJson116BO(nextAudioFemale);
        }
        return audioJson116AllBO;
    }

    public AudioJson116AllBO getFinishAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO finishAudioMale = maleSoundConfig.getSysSoundByName("finish", maleSoundConfig.getFinish());
            finishAudioMale.setTime(new BigDecimal(1));
            audioJson116AllBO.setMaleAudioJson116BO(finishAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO finishAudioFemale = femaleSoundConfig.getSysSoundByName("finish", maleSoundConfig.getFinish());
            finishAudioFemale.setTime(new BigDecimal(1));
            audioJson116AllBO.setFemaleAudioJson116BO(finishAudioFemale);
        }
        return audioJson116AllBO;
    }

    public AudioJson116AllBO getFiveFourThreeTwoOneAudio() {
        AudioJson116AllBO audioJson116AllBO = new AudioJson116AllBO();
        if (maleSoundConfig != null) {
            AudioJson116BO fiveFourThreeTwoOneAudioMale = maleSoundConfig.getSysSoundByName("fiveFourThreeTwoOne", maleSoundConfig.getFiveFourThreeTwoOne());
            fiveFourThreeTwoOneAudioMale.setTime(new BigDecimal(55));
            audioJson116AllBO.setMaleAudioJson116BO(fiveFourThreeTwoOneAudioMale);
        }
        if (femaleSoundConfig != null) {
            AudioJson116BO fiveFourThreeTwoOneAudioFemale = femaleSoundConfig.getSysSoundByName("fiveFourThreeTwoOne", maleSoundConfig.getFiveFourThreeTwoOne());
            fiveFourThreeTwoOneAudioFemale.setTime(new BigDecimal(55));
            audioJson116AllBO.setFemaleAudioJson116BO(fiveFourThreeTwoOneAudioFemale);
        }
        return audioJson116AllBO;
    }

}
