package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjCoach116;
import com.laien.web.biz.proj.oog116.request.ProjCoach116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjCoach116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjCoach116VO;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
public interface IProjCoach116Service extends IService<ProjCoach116> {

    List<ProjCoach116VO> list(String name, Integer status, Integer projId);

    ProjCoach116VO findDetailById(Integer id);

    void update(ProjCoach116UpdateReq req, Integer projectId);

    void save(ProjCoach116AddReq dishReq, Integer projId);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);
}
