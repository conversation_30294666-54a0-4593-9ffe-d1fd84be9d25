package com.laien.web.biz.proj.oog116.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GenerateM3u8Req;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116PageVO;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Service;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_workout116 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Api(tags = "项目管理:workout116")
@RestController
@RequestMapping({"/proj/workout116","/proj/recoveryWorkout116"})
public class ProjWorkout116Controller extends ResponseController {

    @Resource
    private IProjWorkout116Service projWorkout116Service;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjWorkout116PageVO>> page(ProjWorkout116PageReq pageReq) {
        PageRes<ProjWorkout116PageVO> pageRes = projWorkout116Service.selectWorkout116Page(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjWorkout116AddReq workout116AddReq) {
        projWorkout116Service.saveWorkout116(workout116AddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjWorkout116UpdateReq workout116UpdateReq) {
        projWorkout116Service.updateWorkout116(workout116UpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjWorkout116DetailVO> detail(@PathVariable Integer id) {
        ProjWorkout116DetailVO detailVO = projWorkout116Service.getWorkout116Detail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkout116Service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkout116Service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkout116Service.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量生成视频/音频m3u8文件")
    @PostMapping("/m3u8/generate")
    public ResponseResult<Boolean> generateM3u8(@RequestBody ProjWorkout116GenerateM3u8Req req) {
        if (ObjUtil.equal(Boolean.FALSE, req.getVideoFlag()) && ObjUtil.equal(Boolean.FALSE, req.getAudioFlag())) {
            return fail("please select file type");
        }
        if (ObjUtil.equal(Boolean.TRUE, req.getAudioFlag()) && CollUtil.isEmpty(req.getLanguages())) {
            return fail("please select audio language");
        }
        if (CollectionUtil.isEmpty(req.getWorkoutIds())) {
            return fail("please select workout");
        }
        return succ(projWorkout116Service.generateM3u8(req));
    }

}
