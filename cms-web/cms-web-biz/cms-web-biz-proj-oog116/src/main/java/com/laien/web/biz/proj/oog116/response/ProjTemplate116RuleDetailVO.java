package com.laien.web.biz.proj.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: template116 rule 列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template116 rule 列表", description = "template116 rule 列表")
public class ProjTemplate116RuleDetailVO {

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "播放循环次数")
    private Integer rounds;

}
