package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.entity.ProjProgram116;
import com.laien.web.biz.proj.oog116.entity.ProjProgram116Relation;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116;
import com.laien.web.biz.proj.oog116.mapper.ProjProgram116Mapper;
import com.laien.web.biz.proj.oog116.request.ProjProgram116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjProgram116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjProgram116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjCoach116VO;
import com.laien.web.biz.proj.oog116.response.ProjProgram116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjProgram116PageVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116PageVO;
import com.laien.web.biz.proj.oog116.service.IProjCoach116Service;
import com.laien.web.biz.proj.oog116.service.IProjProgram116RelationService;
import com.laien.web.biz.proj.oog116.service.IProjProgram116Service;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Service;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Service
@Slf4j
public class ProjProgram116ServiceImpl extends ServiceImpl<ProjProgram116Mapper, ProjProgram116> implements IProjProgram116Service {

    @Resource
    private IProjProgram116RelationService program116RelationService;

    @Resource
    private IProjCoach116Service coach116Service;

    @Resource
    private IProjWorkout116Service workout116Service;

    @Resource
    private IProjInfoService projInfoService;
    @Autowired
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public PageRes<ProjProgram116PageVO> selectProgramPage(ProjProgram116PageReq pageReq) {

        LambdaQueryWrapper<ProjProgram116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(pageReq.getName()), ProjProgram116::getName, pageReq.getName());
        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjProgram116::getStatus, pageReq.getStatus());
        queryWrapper.orderByDesc(ProjProgram116::getId);

        Page<ProjProgram116> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.page(page, queryWrapper);
        return convert2PageVO(page);
    }

    private PageRes<ProjProgram116PageVO> convert2PageVO(Page<ProjProgram116> page) {

        List<ProjProgram116PageVO> pageVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<Integer> programIds = page.getRecords().stream().map(ProjProgram116::getId).collect(Collectors.toList());
            List<ProjProgram116Relation> program116Relations = program116RelationService.listByProgramIds(programIds);
            Map<Integer, Long> programWorkoutCountMap = program116Relations.stream().collect(Collectors.groupingBy(ProjProgram116Relation::getProjProgram116Id, Collectors.counting()));

            page.getRecords().forEach(program116 -> {
                ProjProgram116PageVO pageVO = new ProjProgram116PageVO();
                BeanUtils.copyProperties(program116, pageVO);
                pageVO.setWorkoutNum(programWorkoutCountMap.getOrDefault(program116.getId(), 0l));
                pageVOList.add(pageVO);
            });
        }
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), pageVOList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveProgram(ProjProgram116AddReq addReq) {

        bizCheck(addReq, null);
        ProjProgram116 program116 = new ProjProgram116();
        BeanUtils.copyProperties(addReq, program116);

        program116.setStatus(GlobalConstant.STATUS_DRAFT);
        program116.setProjId(RequestContextUtils.getProjectId());
        this.save(program116);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(program116), projInfoService.getById(RequestContextUtils.getProjectId()));
        String eventName = program116.getId() + program116.getName();
        program116.setEventName(eventName);
        updateById(program116);

        handleProgram116Relation(program116.getId(), RequestContextUtils.getProjectId(), addReq.getWorkoutList());
    }

    private void bizCheck(ProjProgram116AddReq addReq, Integer programId) {

        LambdaQueryWrapper<ProjProgram116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(Objects.nonNull(programId), ProjProgram116::getId, programId);
        queryWrapper.eq(ProjProgram116::getName, addReq.getName());

        int count = count(queryWrapper);
        if (count > 0) {
            throw new BizException("Program name already exist.");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateProgram(ProjProgram116UpdateReq updateReq) {

        bizCheck(updateReq, updateReq.getId());
        updateBaseProgram(updateReq);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(this.getById(updateReq.getId())), projInfoService.getById(RequestContextUtils.getProjectId()));

        handleProgram116Relation(updateReq.getId(), RequestContextUtils.getProjectId(), updateReq.getWorkoutList());
    }

    private void handleProgram116Relation(Integer programId, Integer projId, List<ProjWorkout116PageVO> workoutList) {

        program116RelationService.deleteByProgramIds(Collections.singletonList(programId));
        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        List<Integer> workoutIds = workoutList.stream().map(ProjWorkout116PageVO::getId).collect(Collectors.toList());
        List<ProjProgram116Relation> relationList = workoutIds.stream().map(workoutId -> new ProjProgram116Relation(programId, workoutId, projId)).collect(Collectors.toList());
        program116RelationService.saveBatch(relationList);
    }

    private void updateBaseProgram(ProjProgram116UpdateReq updateReq) {

        LambdaUpdateWrapper<ProjProgram116> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjProgram116::getId, updateReq.getId());
        updateWrapper.set(ProjProgram116::getProjId, RequestContextUtils.getProjectId());
        updateWrapper.set(ProjProgram116::getName, updateReq.getName());
        updateWrapper.set(ProjProgram116::getEventName, updateReq.getId() + updateReq.getName());

        updateWrapper.set(ProjProgram116::getCoverImgUrl, updateReq.getCoverImgUrl());
        updateWrapper.set(ProjProgram116::getDetailImgUrl, updateReq.getDetailImgUrl());
        updateWrapper.set(ProjProgram116::getCoachId, updateReq.getCoachId());
        updateWrapper.set(ProjProgram116::getNewStartTime, updateReq.getNewStartTime());
        updateWrapper.set(ProjProgram116::getNewEndTime, updateReq.getNewEndTime());

        updateWrapper.set(ProjProgram116::getSubscription, updateReq.getSubscription());
        updateWrapper.set(ProjProgram116::getDescription, updateReq.getDescription());
        updateWrapper.set(ProjProgram116::getGoals, updateReq.getGoals());
        updateWrapper.set(ProjProgram116::getEquipment, updateReq.getEquipment());
        update(new ProjProgram116(), updateWrapper);
    }

    @Override
    public ProjProgram116DetailVO getDetailById(Integer programId) {

        ProjProgram116 projProgram116 = getById(programId);
        return convert2DetailVO(projProgram116);
    }

    private ProjProgram116DetailVO convert2DetailVO(ProjProgram116 projProgram116) {

        if (projProgram116 == null) {
            return null;
        }

        ProjProgram116DetailVO detailVO = new ProjProgram116DetailVO();
        BeanUtils.copyProperties(projProgram116, detailVO);

        ProjCoach116VO coach116VO = coach116Service.findDetailById(projProgram116.getCoachId());
        if (Objects.nonNull(coach116VO)) {
            detailVO.setCoach(coach116VO);
        }

        List<ProjProgram116Relation> relationList = program116RelationService.listByProgramIds(Collections.singletonList(projProgram116.getId()));
        if (CollectionUtils.isEmpty(relationList)) {
            return detailVO;
        }

        List<Integer> workoutIds = relationList.stream().map(ProjProgram116Relation::getProjWorkout116Id).collect(Collectors.toList());
        Collection<ProjWorkout116> workout116List = workout116Service.listByIds(workoutIds);
        if (CollectionUtils.isEmpty(workout116List)) {
            return detailVO;
        }

        Map<Integer, ProjWorkout116> workoutMap = workout116List.stream().collect(Collectors.toMap(BaseModel::getId, item -> item));
        List<ProjWorkout116PageVO> workout116PageVOList = new ArrayList<>();
        for (Integer workoutId : workoutIds) {
            ProjWorkout116 projWorkout = workoutMap.get(workoutId);
            if(null == projWorkout){
                continue;
            }
            ProjWorkout116PageVO pageVO = new ProjWorkout116PageVO();
            BeanUtils.copyProperties(projWorkout, pageVO);
            workout116PageVOList.add(pageVO);
        }

        detailVO.setWorkoutList(workout116PageVOList);
        return detailVO;
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjProgram116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjProgram116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjProgram116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjProgram116::getId, idList);
        this.update(new ProjProgram116(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjProgram116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjProgram116::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjProgram116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjProgram116::getId, idList);
        this.update(new ProjProgram116(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjProgram116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjProgram116::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjProgram116::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjProgram116::getId, idList);
        this.update(new ProjProgram116(), wrapper);
    }
}
