package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116ResVideo116;
import com.laien.web.biz.proj.oog116.mapper.ProjWorkout116ResVideo116Mapper;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116ExerciseDetailVO;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116ResVideo116Service;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_workout116_res_video116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjWorkout116ResVideo116ServiceImpl extends ServiceImpl<ProjWorkout116ResVideo116Mapper, ProjWorkout116ResVideo116> implements IProjWorkout116ResVideo116Service {

    @Override
    public List<ProjWorkout116ExerciseDetailVO> selectExercisesByWorkoutId(Integer workoutId) {
        return this.baseMapper.selectExercisesByWorkoutId(workoutId);
    }

    @Override
    public List<ProjWorkout116ResVideo116> listByWorkoutId(Integer workoutId) {

        if (Objects.isNull(workoutId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjWorkout116ResVideo116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkout116ResVideo116::getProjWorkout116Id, workoutId);
        return list(queryWrapper);
    }
}
