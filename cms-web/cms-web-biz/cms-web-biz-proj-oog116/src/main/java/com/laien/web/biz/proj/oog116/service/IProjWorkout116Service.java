package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GenerateM3u8Req;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116PageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * proj_workout116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjWorkout116Service extends IService<ProjWorkout116> {

    /**
     * workout116分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjWorkout116PageVO> selectWorkout116Page(ProjWorkout116PageReq pageReq);

    /**
     * workout116新增
     *
     * @param workout116AddReq workout116AddReq
     */
    void saveWorkout116(ProjWorkout116AddReq workout116AddReq);

    /**
     *  workout116修改
     *
     * @param workout116UpdateReq workout116UpdateReq
     */
    void updateWorkout116(ProjWorkout116UpdateReq workout116UpdateReq);

    /**
     * workout116详情
     *
     * @param id id
     * @return ProjWorkout116DetailVO
     */
    ProjWorkout116DetailVO getWorkout116Detail(Integer id);

    /**
     *  workout116启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     *  workout116禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     *  workout116删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 生成视频/音频的m3u8文件
     *
     * @param req 入参
     * @return true/false
     */
    Boolean generateM3u8(ProjWorkout116GenerateM3u8Req req);
}
