package com.laien.web.biz.proj.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog116.entity.ProjRecoveryCategory116ProjWorkout116;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116DetailWorkoutVO;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * proj_recovery_category116_proj_workout116 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ProjRecoveryCategory116ProjWorkout116Mapper extends BaseMapper<ProjRecoveryCategory116ProjWorkout116> {

    /**
     * 查询workout状态统计
     *
     * @param projId 项目ID
     * @return 状态统计列表
     */
    @Select("SELECT " +
            "c.proj_recovery_category116_id AS id, " +
            "w.`status`, " +
            "count(*) counts " +
            "FROM " +
            "proj_recovery_category116_proj_workout116 c " +
            "INNER JOIN proj_workout116 w ON w.id = c.proj_workout116_id " +
            "WHERE " +
            "w.proj_id = #{projId} " +
            "AND c.del_flag = 0 " +
            "AND w.del_flag = 0 " +
            "GROUP BY " +
            "c.proj_recovery_category116_id, " +
            "w.`status`")
    List<IdAndStatusCountsRes> selectWorkoutStatusCount(@Param("projId") Integer projId);

    /**
     * 根据分类ID查询workout列表
     *
     * @param categoryId 分类ID
     * @return workout列表
     */
    @Select("SELECT " +
            "w.id, " +
            "w.`name`, " +
            "w.cover_img_url, " +
            "w.exercise_type, " +
            "w.difficulty, " +
            "w.gender, " +
            "w.`status` " +
            "FROM " +
            "proj_recovery_category116_proj_workout116 cw " +
            "INNER JOIN proj_workout116 w ON cw.proj_workout116_id = w.id " +
            "WHERE " +
            "cw.del_flag = 0 " +
            "AND w.del_flag = 0 " +
            "AND cw.proj_recovery_category116_id = #{categoryId}")
    List<ProjRecoveryCategory116DetailWorkoutVO> selectWorkoutByCategoryId(@Param("categoryId") Integer categoryId);
}
