package com.laien.web.biz.proj.oog116.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_template116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjTemplate116Task对象", description="proj_template116")
public class ProjTemplate116Task extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "template id")
    private Integer projTemplate116Id;

    @ApiModelProperty(value = "是否需要清理已生成的video 0 否，1是")
    private Integer cleanUp;

    @ApiModelProperty(value = "失败信息")
    private String failureMessage;

    @ApiModelProperty(value = "任务状态 0处理中、1处理失败、2处理完成")
    private Integer status;


}
