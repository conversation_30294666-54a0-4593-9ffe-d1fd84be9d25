package com.laien.web.biz.proj.oog116.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_workout116_res_video116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116ResVideo116对象", description="proj_workout116_res_video116")
public class ProjWorkout116ResVideo116 extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单元名称")
    @TranslateField
    private String unitName;

    @ApiModelProperty(value = "循环次数")
    private Integer rounds;

    @ApiModelProperty(value = "Video播放轮数")
    private Integer circuit;

    @ApiModelProperty(value = "res_video116_duration")
    private Integer resVideoDuration;

    @ApiModelProperty(value = "video preview duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "该Video多轮的时长，以英文逗号分隔")
    private String circuitVideoDuration;

    @ApiModelProperty(value = "proj_workout116_id")
    private Integer projWorkout116Id;

    @ApiModelProperty(value = "res_video116_id")
    private Integer resVideo116Id;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
