package com.laien.web.biz.proj.oog116.service;

import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116ResVideo116;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116ExerciseAddReq;

import java.util.List;
import java.util.Map;

/**
 * @author: hhl
 * @date: 2025/7/14
 */
public interface ProjWorkout116AssembleService {

    /**
     * 针对有Slice的Video资源，目前仅支持recovery、Dumbbell(midweight)
     * 处理新增或者更新workout时的资源生成，包括m3u8,audioJson,relation
     *
     * @param workout116
     * @param exerciseList
     */
    void handleWorkout4SaveOrUpdate(ProjWorkout116 workout116, List<ProjWorkout116ExerciseAddReq> exerciseList);

    List<BaseWorkoutBO> generateFile4Dumbbell(ProjWorkout116ContextBO contextBO, DumbbellGenerateSoundBO dumbbellSoundBO,
                                              List<List<ProjWorkout116ResVideo116>> relationList, List<String> languageList);

    RecoveryGenerateSoundBO createRecoverySoundBO(List<String> languageList, String gender);

    DumbbellGenerateSoundBO createDumbbellSoundBO(List<String> languageList, String gender);

    void assembleAudio4Recovery(RecoveryGenerateSoundBO soundBO, ResVideo116 currentVideo,
                                Map<Object, ProjResVideo116I18n> videoI18nMap,
                                Map<String, List<AudioJson116BO>> languageAudioJsonMap, Integer currentVideoDuration,
                                boolean lastNode, int workoutDuration, int circuitIndex);

    void assembleAudio4Dumbbell(DumbbellGenerateSoundBO soundBO, ResVideo116 currentVideo,
                                Map<Object, ProjResVideo116I18n> videoI18nMap,
                                Map<String, List<AudioJson116BO>> languageAudioJsonMap, Integer currentVideoDuration,
                                boolean lastNode, int workoutDuration, int circuitIndex);

    /**
     * 针对有Slice的Video资源，目前仅支持recovery、Dumbbell(midweight)
     * 批量更新AudioJson
     *
     * @param workout116
     * @param languageList
     */
    void handleWorkout4BatchUpdate(ProjWorkout116 workout116, List<String> languageList);

}
