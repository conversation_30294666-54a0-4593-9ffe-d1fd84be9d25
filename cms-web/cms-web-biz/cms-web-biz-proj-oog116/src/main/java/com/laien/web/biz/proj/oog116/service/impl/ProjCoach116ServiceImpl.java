package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.entity.ProjCoach116;
import com.laien.web.biz.proj.oog116.mapper.ProjCoach116Mapper;
import com.laien.web.biz.proj.oog116.request.ProjCoach116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjCoach116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjCoach116VO;
import com.laien.web.biz.proj.oog116.service.IProjCoach116Service;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Service
@Slf4j
public class ProjCoach116ServiceImpl extends ServiceImpl<ProjCoach116Mapper, ProjCoach116> implements IProjCoach116Service {
    @Resource
    private IProjInfoService projInfoService;
    @Autowired
    private  IProjLmsI18nService projLmsI18nService;
    @Override
    public List<ProjCoach116VO> list(String name, Integer status, Integer projId) {

        LambdaQueryWrapper<ProjCoach116> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(name), ProjCoach116::getName, name)
                .eq(Objects.nonNull(status), ProjCoach116::getStatus, status)
                .orderByDesc(ProjCoach116::getId);

        List<ProjCoach116> coach116List = baseMapper.selectList(wrapper);
        return convert2ListVO(coach116List);
    }

    private List<ProjCoach116VO> convert2ListVO(List<ProjCoach116> coach116List) {

        if (CollectionUtils.isEmpty(coach116List)) {
            return Collections.emptyList();
        }

        return coach116List.stream().map(this::convert2VO).collect(Collectors.toList());
    }

    @Override
    public ProjCoach116VO findDetailById(Integer id) {

        ProjCoach116 coach116 = baseMapper.selectById(id);
        return convert2VO(coach116);
    }

    private ProjCoach116VO convert2VO(ProjCoach116 coach116) {

        if (coach116 == null) {
            return null;
        }

        ProjCoach116VO coach116VO = new ProjCoach116VO();
        BeanUtils.copyProperties(coach116, coach116VO);
        return coach116VO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjCoach116UpdateReq req, Integer projId) {

        Integer id = req.getId();
        bizCheck(id, req);

        ProjCoach116 entity = new ProjCoach116();
        BeanUtils.copyProperties(req, entity);
        this.updateById(entity);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(entity), projInfoService.getById(projId));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ProjCoach116AddReq req, Integer projId) {

        bizCheck(null, req);
        ProjCoach116 entity = new ProjCoach116();
        BeanUtils.copyProperties(req, entity);

        entity.setProjId(projId);
        entity.setStatus(GlobalConstant.STATUS_DRAFT);
        save(entity);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(entity), projInfoService.getById(projId));

    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {

        LambdaUpdateWrapper<ProjCoach116> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjCoach116::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjCoach116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjCoach116::getId, idList);
        this.update(new ProjCoach116(), updateWrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {

        LambdaUpdateWrapper<ProjCoach116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCoach116::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjCoach116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjCoach116::getId, idList);
        this.update(new ProjCoach116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {

        LambdaUpdateWrapper<ProjCoach116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjCoach116::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjCoach116::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjCoach116::getId, idList);
        this.update(new ProjCoach116(), wrapper);
    }

    private void bizCheck(Integer id, ProjCoach116AddReq req) {

        LambdaQueryWrapper<ProjCoach116> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjCoach116::getName, req.getName())
                .ne(Objects.nonNull(id), ProjCoach116::getId, id);

        int count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BizException("Coach name already exists");
        }
    }
}
