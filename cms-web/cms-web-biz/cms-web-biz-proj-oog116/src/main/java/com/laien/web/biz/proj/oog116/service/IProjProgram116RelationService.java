package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjProgram116Relation;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/5/15
 */

public interface IProjProgram116RelationService extends IService<ProjProgram116Relation> {

    List<ProjProgram116Relation> listByProgramIds(List<Integer> programIds);

    /**
     * 逻辑删除
     *
     * @param programIds
     */
    void deleteByProgramIds(List<Integer> programIds);

}
