package com.laien.web.biz.proj.oog116.response;

import com.laien.common.oog116.enums.Category116TypeEnums;
import com.laien.common.oog116.enums.Gender116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: category116 列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "category116 列表", description = "category116 列表")
public class ProjCategory116ListVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "展示类型")
    private String showType;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "性别")
    private Gender116Enums gender;

    @ApiModelProperty(value = "type")
    private Category116TypeEnums type;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "class 总数")
    private Integer totalCount;

    @ApiModelProperty(value = "class 启用数")
    private Integer enableCount;

    @ApiModelProperty(value = "icon url")
    private String iconUrl;

}
