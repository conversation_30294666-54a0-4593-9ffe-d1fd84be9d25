package com.laien.web.biz.proj.oog116.bo;

import com.laien.web.biz.proj.oog116.entity.ProjWorkout116GenerateResVideo116;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116GenerateResVideo116BO对象", description="ProjWorkout116GenerateResVideo116BO")
public class ProjWorkout116GenerateResVideo116BO extends ProjWorkout116GenerateResVideo116 {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ruleVO")
    private ProjTemplate116RuleVO ruleVO;

    @ApiModelProperty(value = "循环次数")
    private Integer ruleRound;

    @ApiModelProperty(value = "res_video116")
    private ResVideo116 resVideo116;

}
