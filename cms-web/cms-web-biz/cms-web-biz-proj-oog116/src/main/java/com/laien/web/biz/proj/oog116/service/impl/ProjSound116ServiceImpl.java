package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.mapper.ProjSound116Mapper;
import com.laien.web.biz.proj.oog116.mapstruct.ProjSound116MapStruct;
import com.laien.web.biz.proj.oog116.request.ProjSound116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjSound116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjSound116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjSound116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjSound116PageVO;
import com.laien.web.biz.proj.oog116.service.IProjSound116Service;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSound116ServiceImpl extends ServiceImpl<ProjSound116Mapper, ProjSound116>
        implements IProjSound116Service {
    private final ProjSound116MapStruct mapStruct;
    private final ICoreVoiceConfigI18nService i18nConfigService;
    private final ICoreTextTaskI18nService i18nService;
    private final IProjInfoService projInfoService;

    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    @Override
    public PageRes<ProjSound116PageVO> selectSoundPage(ProjSound116PageReq req) {
        LambdaQueryWrapper<ProjSound116> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(req.getId()), ProjSound116::getId, req.getId())
                .like(StrUtil.isNotBlank(req.getSoundName()), ProjSound116::getSoundName, req.getSoundName())
                .eq(ObjUtil.isNotNull(req.getStatus()), ProjSound116::getStatus, req.getStatus())
                .eq(ObjUtil.isNotNull(req.getSoundType()), ProjSound116::getSoundType, req.getSoundType())
                .eq(ObjUtil.isNotNull(req.getSoundSubType()), ProjSound116::getSoundSubType, req.getSoundSubType())
                .eq(ObjUtil.isNotNull(req.getGender()), ProjSound116::getGender, req.getGender())
                .in(CollUtil.isNotEmpty(req.getIds()), ProjSound116::getId, req.getIds());
        query.orderByDesc(ProjSound116::getId);
        IPage<ProjSound116> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), query);
        List<ProjSound116PageVO> list = mapStruct.toPageList(page.getRecords());
        fillI18nConfigInfo(page.getRecords(),list);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    private void fillI18nConfigInfo(List<ProjSound116> records, List<ProjSound116PageVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjSound116PageVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private void fillI18nConfigNameBySound(ProjSound116DetailVO detailVO) {
        Set<Integer> configIds = new HashSet<>();
        configIds.add(detailVO.getCoreVoiceConfigI18nId());
        Map<Integer, CoreVoiceConfigI18n> configMap = i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));

        CoreVoiceConfigI18n config = configMap.get(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }


    private Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ProjSound116> records) {
        Set<Integer> configIds = records.stream().map(ProjSound116::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }

    /**
     * 添加 sound
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveSound(ProjSound116AddReq req) {
        check(null, req);
        ProjSound116 projSound116 = mapStruct.toEntity(req);
        projSound116.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(projSound116);
        if (projSound116.getNeedTranslation()) {
            handleI18n(ListUtil.of(projSound116), projInfoService.getById(req.getProjId()));
        }
    }
    /**
     * 修改 sound
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSound(ProjSound116UpdateReq req) {
        check(req.getId(), req);
        ProjSound116 projSound116 = mapStruct.toEntity(req);
        this.updateById(projSound116);
        if (projSound116.getNeedTranslation()) {
            handleI18n(ListUtil.of(projSound116), projInfoService.getById(req.getProjId()));
        }
    }

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    @Override
    public ProjSound116DetailVO getDetail(Integer id) {
        ProjSound116 sound = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(sound),"Data not found");
        ProjSound116DetailVO detailVO = mapStruct.toDetailVO(sound);
        fillI18nConfigNameBySound(detailVO);
        return detailVO;
    }
    /**
     * 批量启用
     * @param idList
     */
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSound116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSound116::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSound116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjSound116::getId, idList);
       this.update(new ProjSound116(), wrapper);
    }

    /**
     * 批量禁用
     * @param idList
     */
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSound116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSound116::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjSound116::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSound116::getId, idList);
        this.update(new ProjSound116(), wrapper);
    }

    private void check(Integer id, ProjSound116AddReq req) {
        LambdaQueryWrapper<ProjSound116> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSound116::getSoundName, req.getSoundName())
                .eq(ProjSound116::getProjId, req.getProjId())
                .ne(null != id, BaseModel::getId, id);
        List<ProjSound116> soundList = baseMapper.selectList(wrapper);
        Set<String> nameSet = soundList.stream().map(ProjSound116::getSoundName).collect(Collectors.toSet());
        if (nameSet.contains(req.getSoundName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
    }


    private void handleI18n(List<? extends CoreI18nModel> i18nList, ProjInfo projInfo) {
        CreateTaskDTO createTaskDTO = new CreateTaskDTO(i18nList, projInfo.getTextLanguages(), projInfo.getLanguages(), projInfo.getAppCode());
        try {
            i18nService.batchSaveOrUpdate(createTaskDTO);
        } catch (Exception e) {
            log.error("handleI18n failed, createTaskDTO:{}", createTaskDTO, e);
            throw new BizException("handleI18n failed");
        }
    }


}
