package com.laien.web.biz.proj.oog116.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * @author: hhl
 * @date: 2025/7/14
 */
@Accessors(chain = true)
@Data
public class DumbbellGenerateSoundBO {

    private AudioJson116BO firstAudio;

    private AudioJson116BO nextAudio;

    private AudioJson116BO lastAudio;

    private AudioJson116BO restAudio;

    private AudioJson116BO goAudio;

    private AudioJson116BO three21Audio;

    private AudioJson116BO beepBeepAudio;

    private List<AudioJson116BO> promptAudioList;

    private Map<String, DumbbellGenerateSoundBO> i18nAudioMap;

}
