package com.laien.web.biz.proj.oog116.request;

import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.oog116.enums.Sound116SubTypeEnums;
import com.laien.common.oog116.enums.Sound116TypeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * Sound 分页
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Sound 分页", description = "Sound 分页")
public class ProjSound116PageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "ids")
    private List<Integer> ids;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "soundType")
    private Sound116TypeEnums soundType;

    @ApiModelProperty(value = "soundSubType")
    private Sound116SubTypeEnums soundSubType;

    @ApiModelProperty(value = "性别")
    private Gender116Enums gender;

    @ApiModelProperty(value = "项目id")
    private Integer projId;
}

