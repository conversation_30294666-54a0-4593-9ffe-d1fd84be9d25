package com.laien.web.biz.proj.oog116.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116TaskReq;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116UpdateReq;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GenerateM3u8Req;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GeneratePageReq;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116PageVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116GeneratePageVO;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116Service;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116TaskService;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116GenerateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * template116 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Api(tags = "项目管理:template116")
@RestController
@RequestMapping("/proj/template116")
public class ProjTemplate116Controller extends ResponseController {

    @Resource
    private IProjTemplate116TaskService projTemplate116TaskService;
    @Resource
    private IProjTemplate116Service projTemplate116Service;
    @Resource
    private IProjWorkout116GenerateService projWorkout116GenerateService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjTemplate116PageVO>> page(ProjTemplate116PageReq pageReq) {
        PageRes<ProjTemplate116PageVO> pageRes = projTemplate116Service.selectTemplate116Page(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjTemplate116AddReq template116AddReq) {
        projTemplate116Service.saveTemplate116(template116AddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjTemplate116UpdateReq template116UpdateReq) {
        projTemplate116Service.updateTemplate116(template116UpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjTemplate116DetailVO> detail(@PathVariable Integer id) {
        ProjTemplate116DetailVO detailVO = projTemplate116Service.getTemplate116Detail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projTemplate116Service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projTemplate116Service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projTemplate116Service.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "增加任务")
    @PostMapping("/task/add")
    public ResponseResult<Void> add(@RequestBody ProjTemplate116TaskReq taskReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        projTemplate116TaskService.add(taskReq, projectId);
        return succ();
    }

    @ApiOperation(value = "template116 workout video 分页")
    @GetMapping("/generate/videoList")
    public ResponseResult<PageRes<ProjWorkout116GeneratePageVO>> videoList(ProjWorkout116GeneratePageReq generatePageReq) {
        PageRes<ProjWorkout116GeneratePageVO> pageRes = projWorkout116GenerateService.selectWorkoutGeneratePage(generatePageReq);
        return succ(pageRes);
    }

    @Deprecated
    @ApiOperation(value = "批量生成视频/音频m3u8文件")
    @PostMapping("/m3u8/generate")
    public ResponseResult<Boolean> generateM3u8(@RequestBody ProjWorkout116GenerateM3u8Req req) {
        if (ObjUtil.equal(Boolean.FALSE, req.getVideoFlag()) && ObjUtil.equal(Boolean.FALSE, req.getAudioFlag())) {
            return fail("please select file type");
        }
        if (ObjUtil.equal(Boolean.TRUE, req.getAudioFlag()) && CollUtil.isEmpty(req.getLanguages())) {
            return fail("please select audio language");
        }
        if (ObjUtil.isNull(req.getTemplateId())) {
            return fail("please select template");
        }
        return succ(projWorkout116GenerateService.generateM3u8(req));
    }

    @ApiOperation(value = "根据查询条件，对符合条件的Workout，异步生成视频/音频m3u8文件, 同步返回受影响的条数。")
    @PostMapping("/m3u8/generate/query")
    public ResponseResult<Integer> generateM3u8ByQuery(@RequestBody ProjWorkout116GenerateM3u8Req req) {
        if (ObjUtil.equal(Boolean.FALSE, req.getVideoFlag()) && ObjUtil.equal(Boolean.FALSE, req.getAudioFlag())) {
            return fail("please select file type");
        }
        if (ObjUtil.equal(Boolean.TRUE, req.getAudioFlag()) && CollUtil.isEmpty(req.getLanguages())) {
            return fail("please select audio language");
        }
        return succ(projWorkout116GenerateService.generateM3u8ByQuery(req));
    }

    @ApiOperation(value = "清除m3u8生成任务")
    @PostMapping("/m3u8/generate/interrupt")
    public ResponseResult<Void> generateM3u8Interrupt() {
        projWorkout116GenerateService.generateM3u8Interrupt();
        return succ();
    }
}
