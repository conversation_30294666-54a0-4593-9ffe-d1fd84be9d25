package com.laien.web.biz.proj.oog116.controller;

import com.laien.web.biz.proj.oog116.entity.ProjCoach116;
import com.laien.web.biz.proj.oog116.request.ProjCoach116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjCoach116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjCoach116VO;
import com.laien.web.biz.proj.oog116.service.IProjCoach116Service;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Api(tags = "项目管理:Coach116")
@RestController
@RequestMapping("/proj/coach116")
public class ProjCoach116Controller extends ResponseController {

    @Resource
    private IProjCoach116Service service;

    @ApiOperation(value = "列表")
    @GetMapping( "/list")
    public ResponseResult<List<ProjCoach116VO>> list(String name, Integer status) {

        return succ(service.list(name, status, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjCoach116AddReq req) {
        service.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjCoach116UpdateReq req) {
        service.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjCoach116VO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        service.deleteByIdList(idList);
        return succ();
    }

}
