package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * recovery category116 workout 关联
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@ApiModel(value = "recovery category116 workout 关联", description = "recovery category116 workout 关联")
public class ProjRecoveryCategory116AddWorkoutReq {

    @ApiModelProperty(value = "workout id")
    private Integer id;
}
