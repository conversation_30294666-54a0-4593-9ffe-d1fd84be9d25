package com.laien.web.biz.proj.oog116.response;

import com.laien.common.oog116.enums.Template116TypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Template116 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Template116 分页", description = "Template116 分页")
public class ProjTemplate116PageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "时长区间")
    private String durationRange;

    @ApiModelProperty(value = "模板类型")
    private Template116TypeEnums templateType;

    @ApiModelProperty(value = "生成多少天的")
    private Integer day;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "video")
    private Integer videoCount;

    @ApiModelProperty(value = "任务状态 0待处理 1处理中 2成功 3失败")
    private Integer taskStatus;

}
