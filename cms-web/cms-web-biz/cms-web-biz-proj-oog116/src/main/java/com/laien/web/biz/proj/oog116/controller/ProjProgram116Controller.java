package com.laien.web.biz.proj.oog116.controller;

import com.laien.web.biz.proj.oog116.entity.ProjProgram116;
import com.laien.web.biz.proj.oog116.request.ProjProgram116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjProgram116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjProgram116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjProgram116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjProgram116PageVO;
import com.laien.web.biz.proj.oog116.service.IProjProgram116Service;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:13
 */
@Api(tags = "项目管理: Program116")
@RestController
@RequestMapping("/proj/program116")
public class ProjProgram116Controller extends ResponseController {

    @Resource
    private IProjProgram116Service programService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjProgram116PageVO>> page(ProjProgram116PageReq pageReq) {

        PageRes<ProjProgram116PageVO> pageRes = programService.selectProgramPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjProgram116AddReq addReq) {

        programService.saveProgram(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjProgram116UpdateReq updateReq) {

        programService.updateProgram(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjProgram116DetailVO> detail(@PathVariable Integer id) {
        ProjProgram116DetailVO detailVO = programService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        programService.deleteByIds(idList);
        return succ();
    }

}
