package com.laien.web.biz.proj.oog116.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.common.oog116.enums.TargetEnums;
import com.laien.web.common.m3u8.seq.annotation.ResourceSection;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

import static com.laien.common.domain.enums.TranslationTaskTypeEnums.MULTIPLE_TEXT;

/**
 * <p>
 * 116 video
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ResVideo116对象", description = "116 video")
@TableName(autoResultMap = true)
public class ResVideo116 extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作名称")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "nameAudioUrl")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频类型Warm Up/Cool Down/Main")
    private String type;

    @ApiModelProperty(value = "部位Seated/Standing")
    @TranslateField
    private String position;

    @ApiModelProperty(value = "限制,多个用英文逗号分隔，取值Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    @TranslateField(type = MULTIPLE_TEXT)
    private String restriction;

    @ApiModelProperty(value = "动作简介（How To Do）")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "instructionsAudioUrl")
    private String instructions;

    @ApiModelProperty(value = "Video总的播放轮数，目前仅针对TaiChi类型Video")
    private Integer circuit;

    @ApiModelProperty(value = "name audio url duration")
    private Integer nameAudioUrlDuration;

    @ResourceSection(
            tableName = "res_video116",
            m3u8UrlColumn = "video_url",
            m3u8Text2kColumn = "front_m3u8_text2k",
            m3u8Text1080pColumn = "front_m3u8_text1080p",
            m3u8Text720pColumn = "front_m3u8_text720p",
            m3u8Text480pColumn = "front_m3u8_text480p",
            m3u8Text360pColumn = "front_m3u8_text360p",
            m3u8Text2532Column = "front_m3u8_text2532",
            m3u8Url2532Column = "video2532_url",
            durationColum = "front_duration",
            dirKey = "project-workout116-m3u8"
    )
    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontDuration;

    @ResourceSection(
            tableName = "res_video116",
            m3u8Text2kColumn = "side_m3u8_text2k",
            m3u8Text1080pColumn = "side_m3u8_text1080p",
            m3u8Text720pColumn = "side_m3u8_text720p",
            m3u8Text480pColumn = "side_m3u8_text480p",
            m3u8Text360pColumn = "side_m3u8_text360p",
            m3u8Text2532Column = "side_m3u8_text2532",
            durationColum = "side_duration",
            dirKey = "project-workout116-m3u8"
    )
    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "guidance")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "guidanceAudioUrl")
    private String guidance;

    @ApiModelProperty(value = "Guidance音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "Instructions的音频")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "met,1-12的整数")
    private Integer met;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    @TranslateField
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "取值 10:Full Body,11:Upper Body,12:Lower Body")
    private TargetEnums target;

    @ApiModelProperty(value = "身体部位 (多选)")
    @TableField(typeHandler = Region116Enums.TypeHandler.class)
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)")
    @TableField(typeHandler = Focus116Enums.TypeHandler.class)
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具")
    private SupportProp116Enums supportProp;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "frontM3u8Text2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @ApiModelProperty(value = "frontM3u8Text1080p对应的m3u8内容")
    private String frontM3u8Text1080p;

    @ApiModelProperty(value = "frontM3u8Text720p对应的m3u8内容")
    private String frontM3u8Text720p;

    @ApiModelProperty(value = "frontM3u8Text480p对应的m3u8内容")
    private String frontM3u8Text480p;

    @ApiModelProperty(value = "frontM3u8Text360p对应的m3u8内容")
    private String frontM3u8Text360p;

    @ApiModelProperty(value = "sideM3u8Text2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @ApiModelProperty(value = "sideM3u8Text1080p对应的m3u8内容")
    private String sideM3u8Text1080p;

    @ApiModelProperty(value = "sideM3u8Text720p对应的m3u8内容")
    private String sideM3u8Text720p;

    @ApiModelProperty(value = "sideM3u8Text480p对应的m3u8内容")
    private String sideM3u8Text480p;

    @ApiModelProperty(value = "sideM3u8Text360p对应的m3u8内容")
    private String sideM3u8Text360p;


    @ApiModelProperty(value = "frontM3u8Text2532对应的m3u8内容")
    private String frontM3u8Text2532;

    @ApiModelProperty(value = "sideM3u8Text2532对应的m3u8内容")
    private String sideM3u8Text2532;

    @ApiModelProperty(value = "视频地址(正机位 2532 m3u8)")
    private String video2532Url;
}

