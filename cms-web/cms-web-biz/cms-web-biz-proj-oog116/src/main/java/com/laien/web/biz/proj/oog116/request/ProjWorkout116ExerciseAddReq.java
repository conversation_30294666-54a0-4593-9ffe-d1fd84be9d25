package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout116新增exercise list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout116新增exercise list", description = "workout116新增exercise list")
public class ProjWorkout116ExerciseAddReq {

    @ApiModelProperty(value = "exercise id")
    private Integer id;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "循环次数")
    private Integer rounds;

}
