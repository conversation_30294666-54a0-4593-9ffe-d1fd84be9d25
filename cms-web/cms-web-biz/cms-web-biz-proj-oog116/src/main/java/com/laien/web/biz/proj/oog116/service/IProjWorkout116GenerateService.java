package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.oog116.bo.AudioJson116BO;
import com.laien.web.biz.proj.oog116.bo.ProjWorkout116ContextBO;
import com.laien.web.biz.proj.oog116.bo.ProjWorkout116GenerateBO;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116Generate;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GenerateM3u8Req;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116GeneratePageReq;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116GeneratePageVO;
import com.laien.web.frame.response.IdAndCountsRes;
import com.laien.web.frame.response.PageRes;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 116生成的workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjWorkout116GenerateService extends IService<ProjWorkout116Generate> {

    void generate(Integer projTemplate116Id, Integer projTemplate116TaskId, ProjInfo projInfo);

    void cleanUp(ProjWorkout116ContextBO context);

    void doGenerateWorkout(List<ProjWorkout116GenerateBO> projWorkout116List, ProjWorkout116ContextBO context);

    /**
     * 查询video 生成数量
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<IdAndCountsRes> selectCountByTemplateIds(List<Integer> templateIdList);

    /**
     * template116生成workout结果查询
     *
     * @param generatePageReq generatePageReq
     * @return PageRes
     */
    PageRes<ProjWorkout116GeneratePageVO> selectWorkoutGeneratePage(ProjWorkout116GeneratePageReq generatePageReq);

    List<Integer> queryIdList(ProjWorkout116GeneratePageReq generatePageReq);

    /**
     * 获取系统音多语言
     *
     * @param soundList soundList
     * @param languageList languageList
     * @return map
     */
    Map<Integer, Map<String, AudioJson116BO>> getSoundI18n(List<AudioJson116BO> soundList, List<String> languageList);

    /**
     * 生成视频/音频的m3u8文件
     *
     * @param req 入参
     * @return true/false
     */
    Boolean generateM3u8(ProjWorkout116GenerateM3u8Req req);

    void checkVideoI18nResource(List<String> languageList, List<Integer> videoIdList);

    /**
     * 根据查询条件，对符合条件的Workout，批量生成视频/音频m3u8文件
     *
     * @param req
     * @return
     */
    Integer generateM3u8ByQuery(ProjWorkout116GenerateM3u8Req req);

    void updateWorkoutAndRelation(List<ProjWorkout116GenerateBO> projWorkout116BOList, Collection<ProjWorkout116Generate> workoutList);

    void generateM3u8Interrupt();

    /**
     * 只适用于tai chi 或者 chair yoga
     *
     * @param videoIdList
     * @param exerciseTypeName
     * @return
     */
    ProjWorkout116ContextBO createContext(List<String> langList, List<Integer> videoIdList, String exerciseTypeName);
}
