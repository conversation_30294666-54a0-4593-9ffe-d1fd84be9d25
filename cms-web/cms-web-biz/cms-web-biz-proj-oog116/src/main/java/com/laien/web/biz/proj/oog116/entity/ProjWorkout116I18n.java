package com.laien.web.biz.proj.oog116.entity;

import com.laien.web.frame.entity.BaseUserAssignIdModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_workout116 i18n
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116I18n对象", description="proj_category116多语言表")
public class ProjWorkout116I18n extends BaseUserAssignIdModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
