package com.laien.web.biz.proj.oog116.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * note: workout116详情exercise list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout116详情exercise list", description = "workout116详情exercise list")
public class ProjWorkout116ExerciseDetailVO implements AppAudioCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "动作名称")
    @AppAudioTranslateField(resultFieldName = "nameResult")
    private String name;
    @AppAudioTranslateField(resultFieldName = "instructionsResult")
    private String instructions;
    @AppAudioTranslateField(resultFieldName = "guidanceResult")
    private String guidance;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "Video播放轮数，目前仅针对TaiChi类型Video")
    private Integer circuit;

    @ApiModelProperty(value = "res video 切片")
    private List<ResVideo116SliceDetailVO> videosliceList = Collections.emptyList();

    @ApiModelProperty(value = "视频类型Warm Up/Cool Down/Main")
    private String type;

    @ApiModelProperty(value = "部位Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制,多个用英文逗号分隔，取值Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String restriction;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontDuration;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "Guidance音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "Instructions的音频")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "met,1-12的整数")
    private Integer met;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;


    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "循环次数")
    private Integer rounds;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "身体部位 (多选)")
    @TableField(typeHandler = Region116Enums.TypeHandler.class)
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)")
    @TableField(typeHandler = Focus116Enums.TypeHandler.class)
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具")
    private SupportProp116Enums supportProp;

    @ApiModelProperty(value = "frontM3u8Text2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @ApiModelProperty(value = "frontM3u8Text1080p对应的m3u8内容")
    private String frontM3u8Text1080p;

    @ApiModelProperty(value = "frontM3u8Text720p对应的m3u8内容")
    private String frontM3u8Text720p;

    @ApiModelProperty(value = "frontM3u8Text480p对应的m3u8内容")
    private String frontM3u8Text480p;

    @ApiModelProperty(value = "frontM3u8Text360p对应的m3u8内容")
    private String frontM3u8Text360p;

    @ApiModelProperty(value = "sideM3u8Text2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @ApiModelProperty(value = "sideM3u8Text1080p对应的m3u8内容")
    private String sideM3u8Text1080p;

    @ApiModelProperty(value = "sideM3u8Text720p对应的m3u8内容")
    private String sideM3u8Text720p;

    @ApiModelProperty(value = "sideM3u8Text480p对应的m3u8内容")
    private String sideM3u8Text480p;

    @ApiModelProperty(value = "sideM3u8Text360p对应的m3u8内容")
    private String sideM3u8Text360p;

    @ApiModelProperty(value = "frontM3u8Text2532对应的m3u8内容")
    private String frontM3u8Text2532;

    @ApiModelProperty(value = "sideM3u8Text2532对应的m3u8内容")
    private String sideM3u8Text2532;

    @JsonIgnore
    @TableField(exist = false)
    private List<AudioTranslateResultModel> nameResult;

    @JsonIgnore
    @TableField(exist = false)
    private List<AudioTranslateResultModel> instructionsResult;

    @JsonIgnore
    @TableField(exist = false)
    private List<AudioTranslateResultModel> guidanceResult;

}
