package com.laien.web.biz.proj.oog116.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/5
 */
@Configuration
@ConfigurationProperties(prefix = "laien.oog116")
@Data
public class FixedTemplateConfig {

    private List<Integer> fixedTemplateIdList = new ArrayList<>();

}
