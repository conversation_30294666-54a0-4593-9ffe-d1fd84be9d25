package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.request.ResVideo116AddReq;
import com.laien.web.biz.proj.oog116.request.ResVideo116PageReq;
import com.laien.web.biz.proj.oog116.request.ResVideo116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ResVideo116DetailVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116ExportVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116PageVO;
import com.laien.web.frame.response.PageRes;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 116 video 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IResVideo116Service extends IService<ResVideo116> {

    /**
     * video116分页
     *
     * @param pageReq pageReq
     * @return ResVideo116PageVO
     */
    PageRes<ResVideo116PageVO> selectVideo116Page(ResVideo116PageReq pageReq);

    /**
     * video116新增
     *
     * @param video116AddReq video116AddReq
     */
    void saveVideo116(ResVideo116AddReq video116AddReq);

    /**
     * ts生成m3u8
     *
     * @param frontVideoUrl frontVideoUrl
     * @param frontDuration frontDuration
     * @return String
     */
    String videoFrontForM3U8R2(String frontVideoUrl, Integer frontDuration);

    /**
     * video116修改
     *
     * @param video116UpdateReq video116UpdateReq
     */
    void updateVideo116(ResVideo116UpdateReq video116UpdateReq);

    /**
     * video116详情
     *
     * @param id id
     * @return ResVideo111DetailVO
     */
    ResVideo116DetailVO getVideo116Detail(Integer id);

    /**
     * 批量启用
     *
     * @param idList idList
     * @return
     */
    List<Integer> updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ResVideo116ExportVO> records);
    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * video116 excel批量导入，并返回未导入成功的原因
     *
     */
    List<String> importByExcel(InputStream excelInputStream);

    /**
     * 导入视频切片
     *
     * @param file
     * @return
     */
    List<String> importVideoSlice(MultipartFile file);


    void reGenerateAllM3U8();


    List<ResVideo116> queryList();

    List<ResVideo116> listByExerciseType(List<String> exerciseTypeList);

    List<String> importByExcelUpdateImage(InputStream inputStream);
}
