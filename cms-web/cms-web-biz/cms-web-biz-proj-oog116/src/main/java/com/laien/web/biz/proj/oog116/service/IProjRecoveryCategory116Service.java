package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjRecoveryCategory116;
import com.laien.web.biz.proj.oog116.request.ProjRecoveryCategory116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjRecoveryCategory116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116ListVO;

import java.util.List;

/**
 * <p>
 * proj_recovery_category116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface IProjRecoveryCategory116Service extends IService<ProjRecoveryCategory116> {

    /**
     * recovery category116 列表
     *
     * @return List
     */
    List<ProjRecoveryCategory116ListVO> selectCategoryList();

    /**
     * recovery category116 新增
     *
     * @param addReq addReq
     */
    void saveCategory(ProjRecoveryCategory116AddReq addReq);

    /**
     * recovery category116 修改
     *
     * @param updateReq updateReq
     */
    void updateCategory(ProjRecoveryCategory116UpdateReq updateReq);

    /**
     * recovery category116 详情
     *
     * @param id id
     * @return ProjRecoveryCategory116DetailVO
     */
    ProjRecoveryCategory116DetailVO getCategoryDetail(Integer id);

    /**
     * recovery category116 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * recovery category116 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * recovery category116 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * recovery category116 排序
     *
     * @param idList idList
     */
    void saveSort(List<Integer> idList);
}
