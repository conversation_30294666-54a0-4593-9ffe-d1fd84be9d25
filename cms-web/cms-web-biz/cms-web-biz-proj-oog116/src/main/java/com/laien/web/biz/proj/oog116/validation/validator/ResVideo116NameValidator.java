package com.laien.web.biz.proj.oog116.validation.validator;

import cn.hutool.core.util.StrUtil;
import com.laien.web.biz.proj.oog116.validation.ResVideo116Name;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ResVideo116NameValidator implements ConstraintValidator<ResVideo116Name, String> {

    private static String directionSign[] = {"left", "right"};

    private static String conformingDirectionSign[] = {" (Left)", " (left)", " (LEFT)", " (Right)", " (right)", " (RIGHT)"};

    @Override
    public void initialize(ResVideo116Name constraintAnnotation) {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        String lowerCaseName = value.toLowerCase();
        if (StrUtil.containsAny(lowerCaseName, directionSign)) {
            return StrUtil.containsAny(value, conformingDirectionSign);
        }
        return true;
    }
}