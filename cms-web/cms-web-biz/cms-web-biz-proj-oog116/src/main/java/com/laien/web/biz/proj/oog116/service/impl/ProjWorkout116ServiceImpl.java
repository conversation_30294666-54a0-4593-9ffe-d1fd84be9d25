package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.config.Oog116BizConfig;
import com.laien.web.biz.proj.oog116.config.Video116SysSoundBOWrapper;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116I18n;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116ResVideo116;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.mapper.ProjWorkout116Mapper;
import com.laien.web.biz.proj.oog116.request.*;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116ExerciseDetailVO;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116PageVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.proj.oog116.service.*;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import com.laien.web.frame.utils.PageConverter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.ExerciseType116Enums.DUMBBELL_MIDWEIGHT;
import static com.laien.common.oog116.enums.ExerciseType116Enums.RECOVERY;
import static com.laien.common.oog116.enums.Gender116Enums.FEMALE;

/**
 * <p>
 * proj_workout116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class ProjWorkout116ServiceImpl extends ServiceImpl<ProjWorkout116Mapper, ProjWorkout116> implements IProjWorkout116Service {
    @Resource
    private IProjWorkout116ResVideo116Service projWorkout116ResVideo116Service;
    @Resource
    private IProjWorkout116GenerateService projWorkout116GenerateService;
    @Resource
    private FileService fileService;
    @Resource
    private Oog116BizConfig oog116BizConfig;
    @Resource
    private IProjWorkout116I18nService projWorkout116I18nService;
    @Resource
    private IProjInfoService projInfoService;
    @Resource
    private IProjWorkout116Generate4TaiChiService generate4TaiChiService;
    @Resource
    private IProjWorkout116Generate4ChairYogaService generate4ChairYogaService;
    @Resource
    private IResVideo116SliceService video116SliceService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private final IProjLmsI18nService projLmsI18nService;
    @Resource
    private ICoreSpeechTaskI18nPubService speechTaskI18nPubService;
    @Resource
    private ProjWorkout116AssembleService workout116AssembleService;

    private final ExecutorService TASK_EXECUTOR_SERVICE = Executors.newFixedThreadPool(1);
    private final List<String> VIDEO_SLICE_LIST = Lists.newArrayList(RECOVERY.getName(), DUMBBELL_MIDWEIGHT.getName());


    @Override
    public PageRes<ProjWorkout116PageVO> selectWorkout116Page(ProjWorkout116PageReq pageReq) {
        String name = pageReq.getName();
        String difficulty = pageReq.getDifficulty();
        String position = pageReq.getPosition();
        String[] restrictionArr = pageReq.getRestrictionArr();
        Integer status = pageReq.getStatus();
        String equipment = pageReq.getEquipment();
        String exerciseType = pageReq.getExerciseType();
        String gender = pageReq.getGender();
        Integer fileStatus = pageReq.getFileStatus();
        LambdaQueryWrapper<ProjWorkout116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkout116::getProjId, RequestContextUtils.getProjectId())
                .like(StringUtils.isNotBlank(name), ProjWorkout116::getName, name)
                .eq(StringUtils.isNotBlank(difficulty), ProjWorkout116::getDifficulty, difficulty)
                .like(StringUtils.isNotBlank(equipment), ProjWorkout116::getEquipment, equipment)
                .eq(StringUtils.isNotBlank(exerciseType), ProjWorkout116::getExerciseType, exerciseType)
                .eq(StringUtils.isNotBlank(gender), ProjWorkout116::getGender, gender)
                .eq(StringUtils.isNotBlank(position), ProjWorkout116::getPosition, position)
                .eq(ObjUtil.isNotNull(fileStatus), ProjWorkout116::getFileStatus, fileStatus);
        if (Objects.nonNull(restrictionArr) && restrictionArr.length > GlobalConstant.ZERO) {
            queryWrapper.and(o -> {
                int len = restrictionArr.length;
                for (int i = 0; i < len; i++) {
                    o.apply("FIND_IN_SET({0}, restriction)", restrictionArr[i]);
                    o.or(i < len - 1);
                }
            });
        }
        queryWrapper.in(pageReq.getExcludeRecoveryWorkout()!=null && pageReq.getExcludeRecoveryWorkout(),
                ProjWorkout116::getExerciseType, ExerciseType116Enums.eliminateExerciseType(ExerciseType116Enums.RECOVERY));
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ProjWorkout116::getRegion, pageReq.getRegion(), false);
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ProjWorkout116::getFocus, pageReq.getFocus(), false);
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ProjWorkout116::getSupportProp, pageReq.getSupportProp(), false);
        queryWrapper.eq(Objects.nonNull(status), ProjWorkout116::getStatus, status)
                .orderByDesc(ProjWorkout116::getId);

        Page<ProjWorkout116> projWorkout116Page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ProjWorkout116> page = this.page(projWorkout116Page, queryWrapper);
        return PageConverter.convert(page, ProjWorkout116PageVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWorkout116(ProjWorkout116AddReq workout116AddReq) {
        this.checkWorkout(workout116AddReq, null);
        ProjWorkout116 workout116 = new ProjWorkout116();
        BeanUtils.copyProperties(workout116AddReq, workout116);
        workout116.setStatus(GlobalConstant.STATUS_DRAFT);
        Integer projId = RequestContextUtils.getProjectId();
        workout116.setProjId(projId);
        String[] restrictionArr = workout116AddReq.getRestrictionArr();
        if (restrictionArr != null && restrictionArr.length > 0) {
            workout116.setRestriction(MyStringUtil.getJoinWithComma(restrictionArr));
        } else {
            workout116.setRestriction(BizConstant.NONE);
        }
        workout116.setAudioLanguages(GlobalConstant.DEFAULT_LANGUAGE);
        this.save(workout116);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(workout116), projInfoService.getById(projId));

        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.TAI_CHI.getName())) {
            saveRelationAndGenerateFile4TaiChi(workout116.getId(), workout116AddReq.getExerciseList());
            return;
        }

        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.GENTLE_CHAIR_YOGA.getName())) {
            saveRelationAndGenerateFile4ChairYoga(workout116.getId(), workout116AddReq.getExerciseList());
            return;
        }

        if (VIDEO_SLICE_LIST.contains(workout116AddReq.getExerciseType())) {
            workout116AssembleService.handleWorkout4SaveOrUpdate(workout116, workout116AddReq.getExerciseList());
            return;
        }

        Integer id = workout116.getId();
        // 保存关系
        this.saveRelation(id, projId, workout116AddReq.getExerciseList());
        // 保存m3u8
        this.saveWorkout116Merge(workout116, null, Boolean.TRUE, Boolean.TRUE);

        // 临时替换exercise type
        replaceExerciseType(workout116);
    }

    private void replaceExerciseType(ProjWorkout116 workout116) {

        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.CARDIO_105.getName())) {
            workout116.setExerciseType(ExerciseType116Enums.GENTLE_CARDIO.getName());
            updateById(workout116);
        }

        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.DUMBBELL_MODERATE.getName())) {
            workout116.setExerciseType(ExerciseType116Enums.DUMBBELL.getName());
            updateById(workout116);
        }
    }

    private void saveRelationAndGenerateFile4ChairYoga(Integer workoutId, List<ProjWorkout116ExerciseAddReq> exerciseList) {

        ProjWorkout116 workout116 = getById(workoutId);
        bizCheck4TaiChiGenerate(workout116, exerciseList);
        List<String> languageList = projInfoService.getLanguagesById(workout116.getProjId());
        List<Integer> videoIdList = exerciseList.stream().map(ProjWorkout116ExerciseAddReq::getId).collect(Collectors.toList());
        projWorkout116GenerateService.checkVideoI18nResource(languageList, videoIdList);

        ProjWorkout116ContextBO contextBO = projWorkout116GenerateService.createContext(languageList,videoIdList, ExerciseType116Enums.GENTLE_CHAIR_YOGA.getName());
        List<ProjWorkout116ResVideo116> exercise2RelationBoList = exerciseList.stream().map(exercise -> new ProjWorkout116ResVideo116().setResVideo116Id(exercise.getId()).setRounds(exercise.getRounds()).setUnitName(exercise.getUnitName())).collect(Collectors.toList());
        List<ProjWorkout116ResVideo116> relationList = saveOrUpdateRelation4ChairYoga(workout116, exercise2RelationBoList, contextBO);
        List<ProjWorkout116I18n> generateI18nList = generateFile4ChairYoga(contextBO, workout116, relationList, languageList);
        updateWorkout4I18n(workout116, generateI18nList);
    }

    private void updateWorkoutFile4ChairYoga(ProjWorkout116 workout116, List<String> languageList) {

        List<ProjWorkout116ResVideo116> relationList = projWorkout116ResVideo116Service.listByWorkoutId(workout116.getId());
        List<Integer> videoIdList = relationList.stream().map(ProjWorkout116ResVideo116::getResVideo116Id).collect(Collectors.toList());
        projWorkout116GenerateService.checkVideoI18nResource(languageList, videoIdList);

        ProjWorkout116ContextBO contextBO = projWorkout116GenerateService.createContext(languageList,videoIdList, workout116.getExerciseType());
        transactionTemplate.execute(status -> {
            List<ProjWorkout116ResVideo116> updatedRelationList = saveOrUpdateRelation4ChairYoga(workout116, relationList, contextBO);
            List<ProjWorkout116I18n> generateI18nList = generateFile4ChairYoga(contextBO, workout116, updatedRelationList, languageList);
            updateWorkout4I18n(workout116, generateI18nList);
            return null;
        });
    }

    private List<ProjWorkout116ResVideo116> saveOrUpdateRelation4ChairYoga(ProjWorkout116 workout116,
                                                                           List<ProjWorkout116ResVideo116> exerciseList,
                                                                           ProjWorkout116ContextBO contextBO) {

        // delete old relation
        deleteRelation(workout116.getId());

        Map<Integer, ResVideo116> videoIdMap = contextBO.getVideoIdMap();
        List<ProjWorkout116ResVideo116> relationList = exerciseList.stream().map(relationBO -> {

            ProjWorkout116ResVideo116 relation = new ProjWorkout116ResVideo116();
            relation.setResVideo116Id(relationBO.getResVideo116Id());
            relation.setProjWorkout116Id(workout116.getId());

            relation.setProjId(workout116.getProjId());
            relation.setUnitName(relationBO.getUnitName());
            relation.setRounds(relationBO.getRounds());

            ResVideo116 video116 = videoIdMap.get(relationBO.getResVideo116Id());
            Integer circuit = Objects.isNull(video116.getCircuit()) ? 1 : video116.getCircuit();
            relation.setCircuit(circuit);
            return relation;
        }).collect(Collectors.toList());

        computeDuration4ChairYoga(contextBO, workout116.getPosition(), relationList);
        projWorkout116ResVideo116Service.saveBatch(relationList);
        //添加翻译任务
        projLmsI18nService.handleI18n(relationList, workout116.getProjId());

        return relationList;
    }

    public void computeDuration4ChairYoga(ProjWorkout116ContextBO context, String position,
                                          List<ProjWorkout116ResVideo116> workoutResVideoList) {

        AtomicBoolean front = new AtomicBoolean(true);
        ResVideo116 easyChairVideo = context.getEasyChairVideoMap().get(position);
        workoutResVideoList.forEach(resVideo116 -> {

            // preview duration
            List<Integer> videoIdList = Lists.newArrayList(easyChairVideo.getId(), resVideo116.getResVideo116Id());
            Integer previewDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                    context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, videoIdList);
            resVideo116.setPreviewDuration(previewDuration);

            // circuit duration
            List<Integer> circuitDurationList = new ArrayList<>();
            for (int i = 0; i < resVideo116.getCircuit(); i++) {
                Integer circuitDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                        context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, videoIdList);
                circuitDurationList.add(circuitDuration);
            }
            resVideo116.setCircuitVideoDuration(convertList2String(circuitDurationList));
        });
    }

    private String convertList2String(List<Integer> circuitDurationList) {

        if (CollectionUtils.isEmpty(circuitDurationList)) {
            return "";
        }

        return StringUtils.joinWith(GlobalConstant.COMMA, circuitDurationList.toArray());
    }

    private Integer computeVideoDuration4Circuit(Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap,
                                                 Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap, AtomicBoolean front,
                                                 List<Integer> videoIdList) {

        int videoDuration = 0;
        for (Integer video116Id : videoIdList) {
            if (front.get()) {
                videoDuration += frontDurationMap.get(video116Id);
            } else {
                videoDuration += sideDurationMap.get(video116Id);
            }

            if (videoSliceMap.get(video116Id).size() % 2 != 0) {
                if (front.get()) {
                    front.set(false);
                } else {
                    front.set(true);
                }
            }
        }

        return videoDuration;
    }

    private void saveRelationAndGenerateFile4TaiChi(Integer workoutId, List<ProjWorkout116ExerciseAddReq> exerciseList) {

        ProjWorkout116 workout116 = getById(workoutId);
        bizCheck4TaiChiGenerate(workout116, exerciseList);
        List<String> languageList = projInfoService.getLanguagesById(workout116.getProjId());
        List<Integer> videoIdList = exerciseList.stream().map(ProjWorkout116ExerciseAddReq::getId).collect(Collectors.toList());
        projWorkout116GenerateService.checkVideoI18nResource(languageList, videoIdList);

        ProjWorkout116ContextBO contextBO = projWorkout116GenerateService.createContext(languageList,videoIdList, workout116.getExerciseType());
        List<ProjWorkout116ResVideo116> exercise2RelationBoList = exerciseList.stream().map(exercise -> new ProjWorkout116ResVideo116().setResVideo116Id(exercise.getId()).setRounds(exercise.getRounds()).setUnitName(exercise.getUnitName())).collect(Collectors.toList());
        List<ProjWorkout116ResVideo116> relationList = saveOrUpdateRelation4TaiChi(workout116, exercise2RelationBoList, contextBO);
        List<ProjWorkout116I18n> generateI18nList = generateFile4TaiChi(contextBO, workout116, relationList, languageList);
        updateWorkout4I18n(workout116, generateI18nList);
    }

    private void updateWorkoutFile4TaiChi(ProjWorkout116 workout116, List<String> languageList) {

        List<ProjWorkout116ResVideo116> relationList = projWorkout116ResVideo116Service.listByWorkoutId(workout116.getId());
        List<Integer> videoIdList = relationList.stream().map(ProjWorkout116ResVideo116::getResVideo116Id).collect(Collectors.toList());
        projWorkout116GenerateService.checkVideoI18nResource(languageList, videoIdList);

        ProjWorkout116ContextBO contextBO = projWorkout116GenerateService.createContext(languageList,videoIdList, workout116.getExerciseType());
        transactionTemplate.execute(status -> {
            List<ProjWorkout116ResVideo116> updatedRelationList = saveOrUpdateRelation4TaiChi(workout116, relationList, contextBO);
            List<ProjWorkout116I18n> generateI18nList = generateFile4TaiChi(contextBO, workout116, updatedRelationList, languageList);
            updateWorkout4I18n(workout116, generateI18nList);
            return null;
        });
    }

    private void bizCheck4TaiChiGenerate(ProjWorkout116 workout116, List<ProjWorkout116ExerciseAddReq> exerciseList) {

        if (!(Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.TAI_CHI.getName()) || Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.GENTLE_CHAIR_YOGA.getName()))) {
            throw new BizException("Exercise type must be Tai Chi Or Chair Yoga.");
        }

        if (CollectionUtils.isEmpty(exerciseList)) {
            throw new BizException("Exercise list can not be empty.");
        }

        Optional<ProjWorkout116ExerciseAddReq> optional = exerciseList.stream().filter(exercise ->
                StringUtils.isBlank(exercise.getUnitName()) || (Objects.isNull(exercise.getRounds()) || exercise.getRounds() < 1)).findAny();
        if (optional.isPresent()) {
            throw new BizException("Unit name or rounds is illegal.");
        }
    }

    private void updateWorkout4I18n(ProjWorkout116 workout116, List<ProjWorkout116I18n> generateI18nList) {

        if (!CollectionUtils.isEmpty(generateI18nList)) {
            Set<String> audioLanguageSet = Sets.newHashSet(GlobalConstant.DEFAULT_LANGUAGE);
            String audioLanguages = workout116.getAudioLanguages();
            if (StrUtil.isNotBlank(audioLanguages)){
                audioLanguageSet.addAll(Arrays.stream(audioLanguages.split(StringPool.COMMA)).collect(Collectors.toSet()));
            }

            List<String> languages = generateI18nList.stream().map(ProjWorkout116I18n::getLanguage).distinct().collect(Collectors.toList());
            audioLanguageSet.addAll(languages);
            workout116.setAudioLanguages(StringUtils.join(audioLanguageSet, StringPool.COMMA));
        }

        workout116.setFileStatus(GlobalConstant.STATUS_ENABLE);
        this.updateById(workout116);
        if (CollectionUtils.isEmpty(generateI18nList)) {
            return;
        }

        generateI18nList.forEach(generateI18n -> {
            generateI18n.setProjId(workout116.getProjId());
            generateI18n.setId(workout116.getId());
        });

        List<ProjWorkout116I18n> existedI18nList = projWorkout116I18nService.listByWorkoutId(workout116.getId());
        if (CollectionUtils.isEmpty(existedI18nList)) {
            projWorkout116I18nService.saveBatch(generateI18nList);
            return;
        }

        Table<Integer, String, ProjWorkout116I18n> existedGenerateI18nMap = HashBasedTable.create();
        existedI18nList.forEach(existedGenerateI18n -> existedGenerateI18nMap.put(existedGenerateI18n.getId(), existedGenerateI18n.getLanguage(), existedGenerateI18n));
        List<ProjWorkout116I18n> updateList = Lists.newArrayList();
        List<ProjWorkout116I18n> insertList = Lists.newArrayList();

        generateI18nList.forEach(generateI18n -> {
            if (existedGenerateI18nMap.contains(generateI18n.getId(), generateI18n.getLanguage())) {
                updateList.add(generateI18n);
            } else {
                insertList.add(generateI18n);
            }
        });

        projWorkout116I18nService.saveBatch(insertList);
        updateGenerateI18n(updateList);
    }

    private void updateGenerateI18n(List<ProjWorkout116I18n> updateList) {

        updateList.forEach(generateI18n -> {
            LambdaUpdateWrapper<ProjWorkout116I18n> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProjWorkout116I18n::getId, generateI18n.getId());
            updateWrapper.eq(ProjWorkout116I18n::getLanguage, generateI18n.getLanguage());
            updateWrapper.set(ProjWorkout116I18n::getAudioJsonUrl, generateI18n.getAudioJsonUrl());
            updateWrapper.set(ProjWorkout116I18n::getUpdateTime, LocalDateTime.now());
            updateWrapper.set(ProjWorkout116I18n::getUpdateUser, RequestContextUtils.getLoginUserName());
            projWorkout116I18nService.update(updateWrapper);
        });
    }

    private List<ProjWorkout116I18n> generateFile4ChairYoga(ProjWorkout116ContextBO contextBO, ProjWorkout116 workout116,
                                                            List<ProjWorkout116ResVideo116> relationList, List<String> languageList) {

        Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap = contextBO.getVideoSliceMap();
        Map<String, ResVideo116> easyChairMap = contextBO.getEasyChairVideoMap();
        Map<Object, ProjResVideo116I18n> videoI18nMap = contextBO.getVideoI18nMap();
        ChairYogaGenerateSoundBO soundBO = generate4ChairYogaService.createChairSoundBO(languageList);
        Map<String, List<AudioJson116BO>> languageAudioMap = initLanguageAudioMap(languageList);

        TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
        TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
        AtomicBoolean front = new AtomicBoolean(true);
        AtomicBoolean front4Dynamic = new AtomicBoolean(true);

        int workoutDuration = 0;
        BigDecimal workoutCalorie = BigDecimal.ZERO;
        LinkedList<TaiChiRoundBO> taiChiRoundBOList = wrapRoundBOList4GenerateWorkout(relationList, contextBO.getVideoIdMap());
        for (TaiChiRoundBO taiChiRoundBO : taiChiRoundBOList) {

            ResVideo116 currentVideo = taiChiRoundBO.getResVideo116();
            Integer currentVideoDuration = taiChiRoundBO.getResVideoDuration();
            ResVideo116 easyVideo = easyChairMap.get(currentVideo.getPosition());
            int videoRound = taiChiRoundBO.getVideoRound();

            boolean lastNode = taiChiRoundBOList.indexOf(taiChiRoundBO) + taiChiRoundBO.getCircuit() == taiChiRoundBOList.size() - 1;
            int easyStartDuration = generate4ChairYogaService.assemble2532M3u8Text(videoSliceMap.get(easyVideo.getId()), front, tsTextMerge2532BO);
            generate4ChairYogaService.assemble2532M3u8Text(videoSliceMap.get(currentVideo.getId()), front, tsTextMerge2532BO);

            generate4ChairYogaService.assembleDynamicM3u8Text(videoSliceMap.get(easyVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);
            generate4ChairYogaService.assembleDynamicM3u8Text(videoSliceMap.get(currentVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);
            generate4ChairYogaService.assembleAudio(soundBO, currentVideo, videoI18nMap, languageAudioMap, currentVideoDuration, lastNode, workoutDuration, easyStartDuration, workout116.getGender(), videoRound);

            // 在计算Video卡路里时，已经考虑了circuit 参数
            if (videoRound == 0) {
                workoutCalorie = workoutCalorie.add(currentVideo.getCalorie());
            }

            workoutDuration += currentVideoDuration;
        }

        // 填充基础属性
        workout116.setCalorie(workoutCalorie.intValue());
        workout116.setDuration(workoutDuration);

        // 文件上传
        List<ProjWorkout116I18n> generateI18nList = Lists.newArrayList();
        uploadFileAndSaveUrl4TaiChi(workout116, generateI18nList, tsTextMergeDynamicBO, tsTextMerge2532BO, languageAudioMap);
        return generateI18nList;
    }

    private List<ProjWorkout116I18n> generateFile4TaiChi(ProjWorkout116ContextBO contextBO, ProjWorkout116 workout116,
                                                         List<ProjWorkout116ResVideo116> relationList, List<String> languageList) {

        Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap = contextBO.getVideoSliceMap();
        //采用新的翻译逻辑
        Map<Object, ProjResVideo116I18n> videoI18nMap = contextBO.getVideoI18nMap();
        TaiChiGenerateSoundBO soundBO = generate4TaiChiService.createTaiChiSoundBO(languageList);
        Map<String, List<AudioJson116BO>> languageAudioMap = initLanguageAudioMap(languageList);

        // TS拼接
        TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
        TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
        AtomicBoolean front = new AtomicBoolean(true);
        AtomicBoolean front4Dynamic = new AtomicBoolean(true);

        int workoutDuration = 0;
        BigDecimal workoutCalorie = BigDecimal.ZERO;
        LinkedList<TaiChiRoundBO> taiChiRoundBOList = wrapRoundBOList4GenerateWorkout(relationList, contextBO.getVideoIdMap());
        for (TaiChiRoundBO taiChiRoundBO : taiChiRoundBOList) {

            ResVideo116 currentVideo = taiChiRoundBO.getResVideo116();
            Integer currentVideoDuration = taiChiRoundBO.getResVideoDuration();

            boolean lastNode = taiChiRoundBOList.indexOf(taiChiRoundBO) + taiChiRoundBO.getCircuit() == taiChiRoundBOList.size() - 1;
            generate4TaiChiService.assembleGuidanceAudio(soundBO, currentVideo, videoI18nMap, languageAudioMap, currentVideoDuration, lastNode, workoutDuration, taiChiRoundBO.getVideoRound());
            generate4TaiChiService.assemble2532M3u8Text(videoSliceMap.get(currentVideo.getId()), front, tsTextMerge2532BO);
            generate4TaiChiService.assembleDynamicM3u8Text(videoSliceMap.get(currentVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);

            // 在计算Video卡路里时，已经考虑了circuit 参数
            if (taiChiRoundBO.getVideoRound() == 0) {
                workoutCalorie = workoutCalorie.add(currentVideo.getCalorie());
            }

            workoutDuration += currentVideoDuration;
        }

        // 填充基础属性
        workout116.setCalorie(workoutCalorie.intValue());
        workout116.setDuration(workoutDuration);

        //
        List<ProjWorkout116I18n> generateI18nList = Lists.newArrayList();
        uploadFileAndSaveUrl4TaiChi(workout116, generateI18nList, tsTextMergeDynamicBO, tsTextMerge2532BO, languageAudioMap);
        return generateI18nList;
    }

    private void uploadFileAndSaveUrl4TaiChi(ProjWorkout116 workout116, List<ProjWorkout116I18n> generateI18nList,
                                             TsTextMergeBO tsTextMergeBO, TsTextMergeBO tsTextMerge2532BO,
                                             Map<String, List<AudioJson116BO>> audioListMap) {

        // 上传m3u8，并保存相对地址
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(tsTextMergeBO, "project-workout116-m3u8");
        UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8(tsTextMerge2532BO, "project-workout116-m3u8");
        workout116.setVideoUrl(videoR2Info.getFileRelativeUrl()).setVideo2532Url(video2532R2Info.getFileRelativeUrl());

        audioListMap.entrySet().forEach(entry -> {

            // 上传音频JSON
            String language = entry.getKey();
            List<AudioJson116BO> audioList = entry.getValue();
            UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioList), "project-workout116-json");

            // 保存音频相对地址
            if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                workout116.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
            } else {
                ProjWorkout116I18n generateI18n = new ProjWorkout116I18n();
                generateI18n.setLanguage(language);
                generateI18n.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                generateI18nList.add(generateI18n);
            }
        });
    }

    private LinkedList<TaiChiRoundBO> wrapRoundBOList4GenerateWorkout(List<ProjWorkout116ResVideo116> video116BOList, Map<Integer, ResVideo116> videoIdMap) {

        LinkedList<TaiChiRoundBO> taiChiRoundBOList = Lists.newLinkedList();
        Map<String, List<ProjWorkout116ResVideo116>> unitAndVideoMap = video116BOList.stream().collect(Collectors.groupingBy(ProjWorkout116ResVideo116::getUnitName));
        LinkedHashMap<String, Integer> unitAndRoundMap = video116BOList.stream().collect(Collectors.toMap(video -> video.getUnitName(), video -> video.getRounds(), (k1, k2) -> k1, LinkedHashMap::new));

        unitAndRoundMap.entrySet().forEach(entry -> {
            List<ProjWorkout116ResVideo116> unitVideos = unitAndVideoMap.get(entry.getKey());
            for (int roundIndex = 0; roundIndex < entry.getValue(); roundIndex++) {
                for (ProjWorkout116ResVideo116 unitVideo : unitVideos) {
                    int[] circuitDurations = Arrays.stream(unitVideo.getCircuitVideoDuration().split(GlobalConstant.COMMA)).mapToInt(Integer::parseInt).toArray();
                    for (int circuitIndex = -1; circuitIndex < unitVideo.getCircuit(); circuitIndex++) {

                        int videoDuration;
                        if (circuitIndex == -1) {
                            videoDuration = unitVideo.getPreviewDuration();
                        } else {
                            videoDuration = circuitDurations[circuitIndex];
                        }

                        TaiChiRoundBO taiChiRoundBO = new TaiChiRoundBO(videoIdMap.get(unitVideo.getResVideo116Id()), roundIndex, circuitIndex, videoDuration);
                        taiChiRoundBOList.add(taiChiRoundBO);
                    }
                }
            }
        });
        return taiChiRoundBOList;
    }

    private Map<String, List<AudioJson116BO>> initLanguageAudioMap(List<String> languageList) {

        LinkedHashMap<String, List<AudioJson116BO>> audioListMap = new LinkedHashMap<>();
        for (String language : languageList) {
            audioListMap.put(language, new ArrayList<>(64));
        }
        return audioListMap;
    }

    private List<ProjWorkout116ResVideo116> saveOrUpdateRelation4TaiChi(ProjWorkout116 workout116,
                                                                        List<ProjWorkout116ResVideo116> exerciseList,
                                                                        ProjWorkout116ContextBO contextBO) {

        // delete old relation
        deleteRelation(workout116.getId());

        Map<Integer, ResVideo116> videoIdMap = contextBO.getVideoIdMap();
        List<ProjWorkout116ResVideo116> relationList = exerciseList.stream().map(relationBO -> {

            ProjWorkout116ResVideo116 relation = new ProjWorkout116ResVideo116();
            relation.setResVideo116Id(relationBO.getResVideo116Id());
            relation.setProjWorkout116Id(workout116.getId());

            relation.setProjId(workout116.getProjId());
            relation.setUnitName(relationBO.getUnitName());
            relation.setRounds(relationBO.getRounds());

            ResVideo116 video116 = videoIdMap.get(relationBO.getResVideo116Id());
            Integer circuit = Objects.isNull(video116.getCircuit()) ? 1 : video116.getCircuit();
            relation.setCircuit(circuit);
            return relation;
        }).collect(Collectors.toList());

        computeDuration4TaiChi(contextBO, relationList);
        projWorkout116ResVideo116Service.saveBatch(relationList);
        //添加翻译任务
        projLmsI18nService.handleI18n(relationList, projInfoService.getById(workout116.getProjId()));
        return relationList;
    }

    public void computeDuration4TaiChi(ProjWorkout116ContextBO context,
                                       List<ProjWorkout116ResVideo116> workoutResVideoList) {

        AtomicBoolean front = new AtomicBoolean(true);
        workoutResVideoList.forEach(resVideo116 -> {

            List<Integer> videoIdList = Collections.singletonList(resVideo116.getResVideo116Id());
            // preview duration
            Integer previewDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                    context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, videoIdList);
            resVideo116.setPreviewDuration(previewDuration);

            // circuit duration
            List<Integer> circuitDurationList = new ArrayList<>();
            for (int i = 0; i < resVideo116.getCircuit(); i++) {
                Integer circuitDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                        context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, videoIdList);
                circuitDurationList.add(circuitDuration);
            }
            resVideo116.setCircuitVideoDuration(convertList2String(circuitDurationList));
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWorkout116(ProjWorkout116UpdateReq workout116UpdateReq) {

        Integer id = workout116UpdateReq.getId();
        ProjWorkout116 workout116Find = this.getById(id);
        if (Objects.isNull(workout116Find)) {
            throw new BizException("Data not found");
        }
        this.checkWorkout(workout116UpdateReq, id);

        ProjWorkout116 workout116 = new ProjWorkout116();
        BeanUtils.copyProperties(workout116UpdateReq, workout116);
        String[] restrictionArr = workout116UpdateReq.getRestrictionArr();
        if (restrictionArr != null && restrictionArr.length > 0) {
            workout116.setRestriction(MyStringUtil.getJoinWithComma(restrictionArr));
        } else {
            workout116.setRestriction(BizConstant.NONE);
        }
        this.updateById(workout116);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(workout116), projInfoService.getById(workout116Find.getProjId()));

        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.TAI_CHI.getName())) {
            saveRelationAndGenerateFile4TaiChi(workout116.getId(), workout116UpdateReq.getExerciseList());
            return;
        }

        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.GENTLE_CHAIR_YOGA.getName())) {
            saveRelationAndGenerateFile4ChairYoga(workout116.getId(), workout116UpdateReq.getExerciseList());
            return;
        }

        // 生成需要保存projId
        workout116.setProjId(workout116Find.getProjId());
        if (VIDEO_SLICE_LIST.contains(workout116.getExerciseType())) {
            workout116AssembleService.handleWorkout4SaveOrUpdate(workout116, workout116UpdateReq.getExerciseList());
            return;
        }

        // 先删除，在新增
        this.deleteRelation(id);
        // 保存关系
        this.saveRelation(id, RequestContextUtils.getProjectId(), workout116UpdateReq.getExerciseList());
        // 保存m3u8
        this.saveWorkout116Merge(workout116,null,  Boolean.TRUE, Boolean.TRUE);

        // 临时替换exercise type
        replaceExerciseType(workout116);
    }

    /**
     * 合并视频
     *
     * @param workout116 workout116
     */
    private void saveWorkout116Merge(ProjWorkout116 workout116, List<String> languages, Boolean videoFlag, Boolean audioFlag) {
        Integer id =  workout116.getId();
        // 系统音获取并验证是否完整(系统音不区分语言)
        Video116SysSoundBOWrapper video116SysSoundBOWrapper = getSoundBO(workout116);

        AudioJson116AllBO firstAudio_en = video116SysSoundBOWrapper.getFirstAudio();
        AudioJson116AllBO threeTwoOneAudio_en = video116SysSoundBOWrapper.getThreeTwoOneAudio();
        AudioJson116AllBO goAudio_en = video116SysSoundBOWrapper.getGoAudio();
        List<AudioJson116AllBO> halfwayAudioList = video116SysSoundBOWrapper.getHalfwayAudioList();
        List<AudioJson116AllBO> halfwayAudioListCopy = new ArrayList<>(halfwayAudioList);
        Collections.shuffle(halfwayAudioListCopy);
        AudioJson116AllBO halfwayAudio_en = null;
        AudioJson116AllBO restAudio_en = video116SysSoundBOWrapper.getRestAudio();
        AudioJson116AllBO nextAudio_en = video116SysSoundBOWrapper.getNextAudio();
        AudioJson116AllBO finishAudio_en = video116SysSoundBOWrapper.getFinishAudio();
        AudioJson116AllBO fiveFourThreeTwoOneAudio_en = video116SysSoundBOWrapper.getFiveFourThreeTwoOneAudio();
        AudioJson116AllBO firstThreeTwoOneAudio_en = video116SysSoundBOWrapper.getFirstThreeTwoOneAudio();
        String gender = workout116.getGender();


        if (CollectionUtil.isEmpty(languages)) {
            // 通过projId获取projInfo
            BizExceptionUtil.throwIf(ObjUtil.isNull(workout116.getProjId()),"workout's projId is null,workoutId:"+id);
            ProjInfo projInfo = projInfoService.getById(workout116.getProjId());
            BizExceptionUtil.throwIf(ObjUtil.isNull(projInfo),"workout's projId projInfo is null,projId:"+workout116.getProjId());
            languages = Arrays.stream(MyStringUtil.getSplitWithComa(projInfo.getLanguages()))
                    .collect(Collectors.toList());
            // 如果不包含默认语言，添加默认语言到列表
            if (!languages.contains(GlobalConstant.DEFAULT_LANGUAGE)) {
                languages.add(GlobalConstant.DEFAULT_LANGUAGE);
            }
        }
        // 获取英语以外的语言
        List<String> validateLanguages = languages.stream()
                                                  .filter(o -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, o))
                                                  .collect(Collectors.toList());
        // 系统音多语言
        List<AudioJson116BO> soundList = new ArrayList<>();
        soundList.addAll(getBOListByAllBO(firstAudio_en));
        soundList.addAll(getBOListByAllBO(threeTwoOneAudio_en));
        soundList.addAll(getBOListByAllBO(goAudio_en));
        soundList.addAll(getBOListByAllBO(restAudio_en));
        soundList.addAll(getBOListByAllBO(nextAudio_en));
        soundList.addAll(getBOListByAllBO(finishAudio_en));
        soundList.addAll(getBOListByAllBO(fiveFourThreeTwoOneAudio_en));
        soundList.addAll(getBOListByAllBO(firstThreeTwoOneAudio_en));
        for(AudioJson116AllBO allBO :halfwayAudioList) {
            soundList.addAll(getBOListByAllBO(allBO));
        }

        List<ProjWorkout116ExerciseDetailVO> exerciseList = projWorkout116ResVideo116Service.selectExercisesByWorkoutId(id);
        Map<Integer, Map<String, AudioJson116BO>> audioJson116I18nMap = new HashMap<>();

        // 为空不校验翻译
        if (!validateLanguages.isEmpty()) {
            // 系统音多语言
            audioJson116I18nMap = projWorkout116GenerateService.getSoundI18n(soundList, validateLanguages);

            // video多语言翻译
            if (CollUtil.isNotEmpty(exerciseList)) speechTaskI18nPubService.translate(exerciseList, ProjCodeEnums.OOG116,CollUtil.newHashSet(LanguageEnums.getLanguageEnums(validateLanguages)));
            for (ProjWorkout116ExerciseDetailVO workout116ExerciseDetailVO : exerciseList) {
                List<AudioTranslateResultModel> nameResults = workout116ExerciseDetailVO.getNameResult();
                List<AudioTranslateResultModel> guidanceResults = workout116ExerciseDetailVO.getGuidanceResult();
                List<AudioTranslateResultModel> instructionsResults = workout116ExerciseDetailVO.getInstructionsResult();
                if (nameResults == null || guidanceResults == null ||instructionsResults == null) {
                    throw new BizException("The Video (id: " + workout116ExerciseDetailVO.getId() + ") data translation language incomplete");
                }
                Map<LanguageEnums, AudioTranslateResultModel> nameMap = nameResults.stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = guidanceResults.stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                Map<LanguageEnums, AudioTranslateResultModel> instructionsMap = instructionsResults.stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                for (String language : validateLanguages) {
                    AudioTranslateResultModel nameResult = nameMap.get(LanguageEnums.getByNameIgnoreCase(language));
                    AudioTranslateResultModel instructionsResult = instructionsMap.get(LanguageEnums.getByNameIgnoreCase(language));
                    AudioTranslateResultModel guidanceResult = guidanceMap.get(LanguageEnums.getByNameIgnoreCase(language));
                    if (ObjUtil.isNull(nameResult) || ObjUtil.isNull(instructionsResult) || ObjUtil.isNull(guidanceResult)) {
                        throw new BizException("The Video (id: " + workout116ExerciseDetailVO.getId() + ") data translation language incomplete");
                    }
                }
            }
        }

        List<String> unitList = new ArrayList<>();
        // 分组，按照循环次数添加
        LinkedHashMap<String, List<ProjWorkout116ExerciseDetailVO>> exerciseListGroupMap = exerciseList.stream()
                .peek(o -> {
                    String unitName = o.getUnitName();
                    if (!unitList.contains(unitName)) {
                        unitList.add(unitName);
                    }
                }).collect(Collectors.groupingBy(ProjWorkout116ExerciseDetailVO::getUnitName, LinkedHashMap::new, Collectors.toList()));
        // 视频列表
        TsTextMergeBO tsTextMergeBO = new TsTextMergeBO();
        TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
        BigDecimal startTime = new BigDecimal("0.1");
        int durationTotal = 0;
        BigDecimal calorie = BigDecimal.ZERO;
        int ruleSize = unitList.size();

        LinkedHashMap<String, List<AudioJson116BO>> audioListMap = new LinkedHashMap<>();
        for (String language : languages) {
            audioListMap.put(language, new ArrayList<>(64));
        }
        for (int i = 0; i < ruleSize; i++) {
            String ruleId = unitList.get(i);
            List<ProjWorkout116ExerciseDetailVO> relations = exerciseListGroupMap.get(ruleId);
            int relationSize = relations.size();
            ProjWorkout116ExerciseDetailVO exerciseDetailVO = relations.get(0);
            // 视频音频生成
            int rounds = exerciseDetailVO.getRounds();
            for (int j = 0; j < rounds; j++) {
                for (int k = 0; k < relationSize; k++) {
                    BigDecimal playTime = new BigDecimal(durationTotal + "")
                            .divide(new BigDecimal("1000.0"), 1, RoundingMode.UP).add(startTime);
                    ProjWorkout116ExerciseDetailVO relationItem = relations.get(k);
                    int frontDuration = relationItem.getFrontDuration();
                    int sideDuration = relationItem.getSideDuration();

                    int currentPreViewTime = durationTotal + frontDuration + sideDuration;
                    int currentVideoTime = frontDuration * 3 + sideDuration * 2;
                    int currentEndTime = durationTotal + currentVideoTime;

                    // 处理正侧正侧正
                    tsTextMergeBO.addM3u8Text(
                            relationItem.getFrontM3u8Text2k(),
                            relationItem.getFrontM3u8Text1080p(),
                            relationItem.getFrontM3u8Text720p(),
                            relationItem.getFrontM3u8Text480p(),
                            relationItem.getFrontM3u8Text360p());
                    tsTextMergeBO.addM3u8Text(
                            relationItem.getSideM3u8Text2k(),
                            relationItem.getSideM3u8Text1080p(),
                            relationItem.getSideM3u8Text720p(),
                            relationItem.getSideM3u8Text480p(),
                            relationItem.getSideM3u8Text360p());
                    tsTextMergeBO.addM3u8Text(
                            relationItem.getFrontM3u8Text2k(),
                            relationItem.getFrontM3u8Text1080p(),
                            relationItem.getFrontM3u8Text720p(),
                            relationItem.getFrontM3u8Text480p(),
                            relationItem.getFrontM3u8Text360p());
                    tsTextMergeBO.addM3u8Text(
                            relationItem.getSideM3u8Text2k(),
                            relationItem.getSideM3u8Text1080p(),
                            relationItem.getSideM3u8Text720p(),
                            relationItem.getSideM3u8Text480p(),
                            relationItem.getSideM3u8Text360p());
                    tsTextMergeBO.addM3u8Text(
                            relationItem.getFrontM3u8Text2k(),
                            relationItem.getFrontM3u8Text1080p(),
                            relationItem.getFrontM3u8Text720p(),
                            relationItem.getFrontM3u8Text480p(),
                            relationItem.getFrontM3u8Text360p());
                    if (StringUtils.isNotBlank(relationItem.getFrontM3u8Text2532()) && StringUtils.isNotBlank(relationItem.getSideM3u8Text2532())) {
                        tsTextMerge2532BO.addM3u8Text(relationItem.getFrontM3u8Text2532(), null, null, null, null);
                        tsTextMerge2532BO.addM3u8Text(relationItem.getSideM3u8Text2532(), null, null, null, null);
                        tsTextMerge2532BO.addM3u8Text(relationItem.getFrontM3u8Text2532(), null, null, null, null);
                        tsTextMerge2532BO.addM3u8Text(relationItem.getSideM3u8Text2532(), null, null, null, null);
                        tsTextMerge2532BO.addM3u8Text(relationItem.getFrontM3u8Text2532(), null, null, null, null);
                    }

                    boolean isLast = i == ruleSize - 1 && j == rounds - 1 && k == relationSize - 1;
                    if (CollectionUtil.isEmpty(halfwayAudioListCopy)) {
                        halfwayAudioListCopy = new ArrayList<>(halfwayAudioList);
                        if(halfwayAudioListCopy.size() > 1){
                            halfwayAudioListCopy.remove(halfwayAudio_en);
                        }
                        Collections.shuffle(halfwayAudioListCopy);
                    }
                    halfwayAudio_en = halfwayAudioListCopy.remove(0);
                    for (String language : languages) {
                        AudioJson116BO firstAudio;
                        AudioJson116BO finishAudio;
                        AudioJson116BO nextAudio;
                        AudioJson116BO firstThreeTwoOneAudio;
                        AudioJson116BO goAudio;
                        AudioJson116BO halfwayAudio;
                        AudioJson116BO fiveFourThreeTwoOneAudio;
                        AudioJson116BO threeTwoOneAudio;
                        AudioJson116BO restAudio;
                        // video 替换多语言字段
                        String nameAudioUrl = relationItem.getNameAudioUrl();
                        String guidanceAudioUrl = relationItem.getGuidanceAudioUrl();
                        if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                            firstAudio = getAudioJson116BOByGender(firstAudio_en,gender);
                            finishAudio = getAudioJson116BOByGender(finishAudio_en,gender);
                            nextAudio = getAudioJson116BOByGender(nextAudio_en,gender);
                            firstThreeTwoOneAudio = getAudioJson116BOByGender(firstThreeTwoOneAudio_en,gender);
                            goAudio = getAudioJson116BOByGender(goAudio_en,gender);
                            halfwayAudio = getAudioJson116BOByGender(halfwayAudio_en,gender);
                            fiveFourThreeTwoOneAudio = getAudioJson116BOByGender(fiveFourThreeTwoOneAudio_en,gender);
                            threeTwoOneAudio = getAudioJson116BOByGender(threeTwoOneAudio_en,gender);
                            restAudio = getAudioJson116BOByGender(restAudio_en,gender);
                        } else {
                            firstAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(firstAudio_en,gender).getSoundId()).get(language);
                            finishAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(finishAudio_en,gender).getSoundId()).get(language);
                            nextAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(nextAudio_en,gender).getSoundId()).get(language);
                            firstThreeTwoOneAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(firstThreeTwoOneAudio_en,gender).getSoundId()).get(language);
                            goAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(goAudio_en,gender).getSoundId()).get(language);
                            halfwayAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(halfwayAudio_en,gender).getSoundId()).get(language);
                            fiveFourThreeTwoOneAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(fiveFourThreeTwoOneAudio_en,gender).getSoundId()).get(language);
                            threeTwoOneAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(threeTwoOneAudio_en,gender).getSoundId()).get(language);
                            restAudio = audioJson116I18nMap.get(getAudioJson116BOByGender(restAudio_en,gender).getSoundId()).get(language);

                            Map<Integer, List<ProjWorkout116ExerciseDetailVO>> groupedMap = exerciseList.stream()
                                    .collect(Collectors.groupingBy(ProjWorkout116ExerciseDetailVO::getId));
                            ProjWorkout116ExerciseDetailVO projWorkout116ExerciseDetailVO = groupedMap.get(relationItem.getId()).get(0);
                            List<AudioTranslateResultModel> nameResults = projWorkout116ExerciseDetailVO.getNameResult();
                            List<AudioTranslateResultModel> guidanceResults = projWorkout116ExerciseDetailVO.getGuidanceResult();
                            Map<LanguageEnums, AudioTranslateResultModel> nameMap = nameResults.stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                            Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = guidanceResults.stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
                            AudioTranslateResultModel nameResult = nameMap.get(LanguageEnums.getByNameIgnoreCase(language));
                            AudioTranslateResultModel guideResult = guidanceMap.get(LanguageEnums.getByNameIgnoreCase(language));
                            nameAudioUrl = nameResult.getAudioUrl();
                            guidanceAudioUrl = guideResult.getAudioUrl();
                        }
                        List<AudioJson116BO> audioList = audioListMap.get(language);
                        if (i == 0 && j == 0 && k == 0) {
                            // first audio
                            addSysAudioJson(audioList, firstAudio, playTime);
                        } else if (isLast) {
                            // finish audio
                            addSysAudioJson(audioList, finishAudio, playTime);
                        } else {
                            // next audio
                            addSysAudioJson(audioList, nextAudio, playTime);
                        }

                        // exercise sound name
                        AudioJson116BO videoNameAudio = new AudioJson116BO(
                                "" + relationItem.getId(),
                                fileService.getAbsoluteR2Url(nameAudioUrl),
                                FireBaseUrlSubUtils.getFileName(nameAudioUrl), new BigDecimal(3), null,
                                false,null,null,null
                        );
                        addSysAudioJson(audioList, videoNameAudio, playTime);

                        // three two one audio
//                    addSysAudioJson(audioList, firstThreeTwoOneAudio, playTime);
                        addSysAudioJsonBackward(audioList, firstThreeTwoOneAudio, currentPreViewTime, firstThreeTwoOneAudio.getDuration());

                        // go audio
                        addSysAudioJson(audioList, goAudio, playTime);

                        // Guidance audio
                        AudioJson116BO guidanceAudio = new AudioJson116BO(
                                "" + relationItem.getId(),
                                fileService.getAbsoluteR2Url(guidanceAudioUrl),
                                FireBaseUrlSubUtils.getFileName(guidanceAudioUrl), new BigDecimal(6), null,
                                true,null,null,null
                        );
                        addSysAudioJson(audioList, guidanceAudio, playTime);

                        // halfway audio
                        addSysAudioJson(audioList, halfwayAudio, playTime);
                        if (isLast) {
                            // 54321 audio
//                        addSysAudioJson(audioList, fiveFourThreeTwoOneAudio, playTime);
                            addSysAudioJsonBackward(audioList, fiveFourThreeTwoOneAudio, currentEndTime, fiveFourThreeTwoOneAudio.getDuration());
                        } else {
                            // rest audio
                            int restDuration = restAudio.getDuration();
                            int threeTwoOneTime = restDuration + threeTwoOneAudio.getDuration() + 500;
                            // three two one audio
                            addSysAudioJsonBackward(audioList, threeTwoOneAudio, currentEndTime, threeTwoOneTime);
                            // rest audio
                            addSysAudioJsonBackward(audioList, restAudio, currentEndTime, restDuration);
//                        // three two one audio
//                        addSysAudioJson(audioList, threeTwoOneAudio, playTime);
//
//                        // rest audio
//                        addSysAudioJson(audioList, restAudio, playTime);
                        }

                    }

                    durationTotal += currentVideoTime;
                    calorie = NumberUtil.add(calorie, relationItem.getCalorie());
                }
            }
        }

        ProjWorkout116 workout116Update = new ProjWorkout116();
        // 合并视频，保存数据
        if (ObjUtil.equal(Boolean.TRUE, videoFlag)){
            UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(tsTextMergeBO, "project-workout116-m3u8");
            String video2532Url = null;
            if (!tsTextMerge2532BO.getM3u8Text2kList().isEmpty()) {
                UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8(tsTextMerge2532BO, "project-workout116-m3u8");
                video2532Url = video2532R2Info.getFileRelativeUrl();
            }
            workout116Update.setVideoUrl(videoR2Info.getFileRelativeUrl());
            workout116Update.setVideo2532Url(video2532Url);
        }

        // 上传音频json
        if (ObjUtil.equal(Boolean.TRUE, audioFlag)){
            List<ProjWorkout116I18n> i18ns = new ArrayList<>();
            for (String language : audioListMap.keySet()) {
                List<AudioJson116BO> audioList = audioListMap.get(language);
                UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(
                        JacksonUtil.toJsonString(audioList), "project-workout116-json");
                if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                    workout116Update.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                } else {
                    ProjWorkout116I18n i18n = new ProjWorkout116I18n();
                    i18n.setId(id);
                    i18n.setLanguage(language);
                    i18n.setProjId(workout116.getProjId());
                    i18n.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                    i18ns.add(i18n);
                }
            }
            HashSet<String> audioLanguageSet = new HashSet<>();
            String audioLanguages = workout116.getAudioLanguages();
            if (StrUtil.isNotBlank(audioLanguages)){
                audioLanguageSet.addAll(Arrays.stream(audioLanguages.split(StringPool.COMMA)).collect(Collectors.toSet()));
            }
            audioLanguageSet.addAll(languages);
            workout116Update.setAudioLanguages(StringUtils.join(audioLanguageSet, StringPool.COMMA));

            if (CollectionUtil.isNotEmpty(i18ns)) {
                for (ProjWorkout116I18n i18n : i18ns) {
                    LambdaUpdateWrapper<ProjWorkout116I18n> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.set(ProjWorkout116I18n::getAudioJsonUrl, i18n.getAudioJsonUrl());
                    wrapper.eq(ProjWorkout116I18n::getId, i18n.getId());
                    wrapper.eq(ProjWorkout116I18n::getLanguage, i18n.getLanguage());
                    boolean flag = projWorkout116I18nService.update(new ProjWorkout116I18n(), wrapper);
                    if (!flag) {
                        projWorkout116I18nService.save(i18n);
                    }
                }
            }
        }
        workout116Update.setId(id);
        workout116Update.setDuration(durationTotal);
        workout116Update.setFileStatus(GlobalConstant.ONE);
        this.updateById(workout116Update);
    }

    private Video116SysSoundBOWrapper getSoundBO(ProjWorkout116 workout116) {

        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.CARDIO_105.getName())) {
            return oog116BizConfig.getCardio105();
        }
        if (Objects.equals(workout116.getExerciseType(), ExerciseType116Enums.DUMBBELL_MODERATE.getName())) {
            return oog116BizConfig.getCardio105();
        }

        return oog116BizConfig.getOog116();
    }


    /**
     * 添加系统音
     * 时间点=sysAudioJson.getTime()+playTime
     */
    private void addSysAudioJson(List<AudioJson116BO> audioList, AudioJson116BO sysAudioJson, BigDecimal playTime) {

        AudioJson116BO audioJsonBO = new AudioJson116BO(
                sysAudioJson.getId(),
                sysAudioJson.getUrl(),
                sysAudioJson.getName(),
                NumberUtil.add(playTime, sysAudioJson.getTime()),
                sysAudioJson.getDuration(),
                sysAudioJson.isClose(),
                sysAudioJson.getGender(),
                sysAudioJson.getSoundScript(),
                sysAudioJson.getCoreVoiceConfigI18nId()
        );
        audioList.add(audioJsonBO);
    }
    private List<AudioJson116BO> getBOListByAllBO(AudioJson116AllBO allBO) {
        return Lists.newArrayList(allBO.getFemaleAudioJson116BO(), allBO.getMaleAudioJson116BO());
    }

    private AudioJson116BO getAudioJson116BOByGender(AudioJson116AllBO audioJson116AllBO,String gender) {
        if (FEMALE.getName().equalsIgnoreCase(gender))
        {
            return audioJson116AllBO.getFemaleAudioJson116BO();
        }
        return  audioJson116AllBO.getMaleAudioJson116BO();
    }
    /**
     * 添加系统音 反着计算
     * 时间点=endTime - subTime
     */
    private void addSysAudioJsonBackward(List<AudioJson116BO> audioList, AudioJson116BO sysAudioJson, Integer endTime, Integer subTime) {
        int time = endTime - subTime;
        BigDecimal playTime = new BigDecimal(time + "").divide(new BigDecimal("1000.0"), 1, RoundingMode.HALF_UP);

        AudioJson116BO audioJsonBO = new AudioJson116BO(
                sysAudioJson.getId(),
                sysAudioJson.getUrl(),
                sysAudioJson.getName(),
                playTime,
                sysAudioJson.getDuration(),
                sysAudioJson.isClose(),
                sysAudioJson.getGender(),
                sysAudioJson.getSoundScript(),
                sysAudioJson.getCoreVoiceConfigI18nId()
        );
        audioList.add(audioJsonBO);
    }

    /**
     * 保存workout exercise关系
     *
     * @param id id
     * @param exerciseList exerciseList
     */
    private void saveRelation(Integer id, Integer projId, List<ProjWorkout116ExerciseAddReq> exerciseList) {
        if (exerciseList != null && !exerciseList.isEmpty()) {
            List<ProjWorkout116ResVideo116> relationList = new ArrayList<>();
            for (int i = 0; i < exerciseList.size(); i++) {
                ProjWorkout116ExerciseAddReq exerciseAddReq = exerciseList.get(i);
                String unitName = exerciseAddReq.getUnitName();
                ProjWorkout116ResVideo116 relation = new ProjWorkout116ResVideo116();
                relation.setUnitName(unitName);
                relation.setRounds(exerciseAddReq.getRounds());
                relation.setProjWorkout116Id(id);
                relation.setResVideo116Id(exerciseAddReq.getId());
                relation.setProjId(projId);
                if (i > 0) {
                    ProjWorkout116ResVideo116 relationPrev = relationList.get(i - 1);
                    // 如果unitName为空或者相等，设置为上一条数据的unitName， rounds 也以上一条为准
                    if (StringUtils.isBlank(unitName) || Objects.equals(unitName, relationPrev.getUnitName())) {
                        relation.setUnitName(relationPrev.getUnitName());
                        relation.setRounds(relationPrev.getRounds());
                    }
                }
                relationList.add(relation);
            }
            projWorkout116ResVideo116Service.saveBatch(relationList);
            //添加翻译任务
            projLmsI18nService.handleI18n(relationList, projInfoService.getById(projId));

        }
    }

    /**
     * workout检查
     *
     * @param workout116AddReq workout116AddReq
     * @param id id
     */
    private void checkWorkout(ProjWorkout116AddReq workout116AddReq, Integer id) {
        LambdaQueryWrapper<ProjWorkout116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkout116::getName, workout116AddReq.getName())
                .eq(ProjWorkout116::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjWorkout116::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Workout name exists");
        }

        LambdaQueryWrapper<ProjWorkout116> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjWorkout116::getEventName, workout116AddReq.getEventName())
                .eq(ProjWorkout116::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjWorkout116::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Workout event name exists");
        }
    }

    /**
     * 删除workout 关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        LambdaUpdateWrapper<ProjWorkout116ResVideo116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkout116ResVideo116::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjWorkout116ResVideo116::getProjWorkout116Id, id);
        projWorkout116ResVideo116Service.update(new ProjWorkout116ResVideo116(), wrapper);
    }

    @Override
    public ProjWorkout116DetailVO getWorkout116Detail(Integer id) {
        ProjWorkout116 workout116Find = this.getById(id);
        if (Objects.isNull(workout116Find)) {
            throw new BizException("Data not found");
        }
        ProjWorkout116DetailVO detailVO = new ProjWorkout116DetailVO();
        BeanUtils.copyProperties(workout116Find, detailVO);
        String restriction = workout116Find.getRestriction();
        if (Objects.equals(restriction, BizConstant.NONE)) {
            detailVO.setRestrictionArr(new String[]{});
        } else {
            detailVO.setRestrictionArr(MyStringUtil.getSplitWithComa(restriction));
        }

        List<ProjWorkout116ExerciseDetailVO> exerciseList = projWorkout116ResVideo116Service.selectExercisesByWorkoutId(id);
        setProperty4TaiChi(exerciseList);
        detailVO.setExerciseList(exerciseList);
        return detailVO;
    }

    private void setProperty4TaiChi(List<ProjWorkout116ExerciseDetailVO> exerciseList) {

        if (Objects.isNull(exerciseList)) {
            return;
        }

        List<Integer> videoIds = exerciseList.stream().map(ProjWorkout116ExerciseDetailVO::getId).collect(Collectors.toList());
        List<ResVideo116SliceDetailVO> sliceDetailVOList = video116SliceService.listByResVideoId(videoIds);
        if (CollectionUtils.isEmpty(sliceDetailVOList)) {
            return;
        }

        Map<Integer, List<ResVideo116SliceDetailVO>> videoAndSliceMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ResVideo116SliceDetailVO::getResVideo116Id));
        exerciseList.forEach(video -> video.setVideosliceList(videoAndSliceMap.get(video.getId())));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWorkout116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkout116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWorkout116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjWorkout116::getId, idList);
        this.update(new ProjWorkout116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWorkout116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkout116::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjWorkout116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWorkout116::getId, idList);
        this.update(new ProjWorkout116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            LambdaUpdateWrapper<ProjWorkout116> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjWorkout116::getDelFlag, GlobalConstant.YES);
            wrapper.eq(ProjWorkout116::getStatus, GlobalConstant.STATUS_DRAFT);
            wrapper.eq(ProjWorkout116::getId, id);
            boolean flag = this.update(new ProjWorkout116(), wrapper);
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }

    @Override
    public Boolean generateM3u8(ProjWorkout116GenerateM3u8Req req) {
        List<ProjWorkout116> workouts = super.list(new LambdaQueryWrapper<ProjWorkout116>().in(ProjWorkout116::getId, req.getWorkoutIds()));
        List<Integer> workoutIds = workouts.stream()
                                           .map(ProjWorkout116::getId)
                                           .collect(Collectors.toList());
        //文件处理状态赋值
        this.update(new LambdaUpdateWrapper<ProjWorkout116>().set(ProjWorkout116::getFileStatus, GlobalConstant.ZERO)
                                                             .in(ProjWorkout116::getId, workoutIds));
        workouts.forEach(workout -> {
            TASK_EXECUTOR_SERVICE.execute(() -> {
                try {
                    if (Objects.equals(workout.getExerciseType(), ExerciseType116Enums.TAI_CHI.getName())) {
                        this.updateWorkoutFile4TaiChi(workout, req.getLanguages());
                    } else if (Objects.equals(workout.getExerciseType(), ExerciseType116Enums.GENTLE_CHAIR_YOGA.getName())){
                        this.updateWorkoutFile4ChairYoga(workout, req.getLanguages());
                    } else if (VIDEO_SLICE_LIST.contains(workout.getExerciseType())) {
                        workout116AssembleService.handleWorkout4BatchUpdate(workout, req.getLanguages());
                    } else {
                        this.saveWorkout116Merge(workout, req.getLanguages(), req.getVideoFlag(), req.getAudioFlag());
                    }
                    //TODO 其他类型的生成逻辑

                } catch (BizException be) {
                    workout.setFailMessage("Lack of audio resources : " + StringUtils.substring(be.getMessage(), 0, 200))
                           .setFileStatus(GlobalConstant.TWO);
                    this.updateById(workout);
                    log.error("Lack of audio resources", be);
                } catch (Exception e) {
                    workout.setFailMessage("Generate m3u8 error : " + StringUtils.substring(e.getMessage(), 0, 200))
                           .setFileStatus(GlobalConstant.TWO);
                    this.updateById(workout);
                    log.error("Generate m3u8 error", e);
                }
            });
        });
        return Boolean.TRUE;
    }
}
