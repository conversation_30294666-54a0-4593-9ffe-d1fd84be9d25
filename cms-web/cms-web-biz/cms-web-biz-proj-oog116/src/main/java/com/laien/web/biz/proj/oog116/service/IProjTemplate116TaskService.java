package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.oog116.enums.ProjTemplate116TaskStatusEnums;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116Task;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116TaskReq;

import java.util.List;

/**
 * <p>
 * proj_template116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjTemplate116TaskService extends IService<ProjTemplate116Task> {

    void add(ProjTemplate116TaskReq taskReq, Integer projectId);

    void changeState(Integer id, ProjTemplate116TaskStatusEnums status, String failureMessage);

    /**
     * 查询最后一次任务信息
     *
     * @param templateIdList templateIdList
     * @return list
     */
    List<ProjTemplate116Task> selectLastTask(List<Integer> templateIdList);

}
