package com.laien.web.biz.proj.oog116.request;

import com.laien.common.oog116.enums.Template116TypeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Template116 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Template116 分页", description = "Template116 分页")
public class ProjTemplate116PageReq extends PageReq {

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "时长区间")
    private String durationRange;

    @ApiModelProperty(value = "模板类型")
    private Template116TypeEnums templateType;

    @ApiModelProperty(value = "状态")
    private Integer status;

}
