package com.laien.web.biz.proj.oog116.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Accessors(chain = true)
@Data
public class ChairYogaGenerateSoundBO {

    private AudioJson116BO firstAudio;

    private AudioJson116BO nextAudio;

    private AudioJson116BO lastAudio;

    private List<AudioJson116BO> startAudioList4First;

    private Map<String, ChairYogaGenerateSoundBO> i18nAudioMap;

}
