package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.*;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.config.DumbbellConfigWrapper;
import com.laien.web.biz.proj.oog116.config.Oog116BizConfig;
import com.laien.web.biz.proj.oog116.config.RecoveryConfigWrapper;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116I18n;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116ResVideo116;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.request.ProjWorkout116ExerciseAddReq;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.proj.oog116.service.*;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.ExerciseType116Enums.*;
import static com.laien.web.frame.constant.GlobalConstant.SECOND_MILL;

/**
 * @author: hhl
 * @date: 2025/7/14
 */
@Slf4j
@Service
public class ProjWorkout116AssembleServiceImpl implements ProjWorkout116AssembleService {

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    @Lazy
    private IProjWorkout116Service projWorkout116Service;

    @Resource
    @Lazy
    private IProjWorkout116GenerateService projWorkout116GenerateService;

    @Resource
    private IProjWorkout116I18nService projWorkout116I18nService;

    @Resource
    private IProjWorkout116ResVideo116Service projWorkout116ResVideo116Service;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Resource
    private FileService fileService;

    @Resource
    private IProjWorkout116Generate4TaiChiService generate4TaiChiService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private Oog116BizConfig oog116BizConfig;

    private final String WORKOUT_M3U8_DIR_KEY = "project-workout116-m3u8";
    private final List<String> EXERCISE_LIST = Lists.newArrayList(TAI_CHI.getName(), CHAIR_YOGA.getName(), RECOVERY.getName(), DUMBBELL_MIDWEIGHT.getName());

    private static final ThreadPoolTaskExecutor poolTaskExecutor = createTaskExecutor4Generate();

    private static ThreadPoolTaskExecutor createTaskExecutor4Generate() {

        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(50);
        threadPoolTaskExecutor.setMaxPoolSize(50);
        threadPoolTaskExecutor.setQueueCapacity(3000);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        // 允许回收核心线程
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setThreadNamePrefix("116-workout-generate-thread-");
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Override
    public void handleWorkout4SaveOrUpdate(ProjWorkout116 workout116, List<ProjWorkout116ExerciseAddReq> exerciseList) {

        bizCheck(workout116, exerciseList);
        List<String> languageList = projInfoService.getLanguagesById(workout116.getProjId());
        List<Integer> videoIdList = exerciseList.stream().map(ProjWorkout116ExerciseAddReq::getId).collect(Collectors.toList());
        projWorkout116GenerateService.checkVideoI18nResource(languageList, videoIdList);

        ProjWorkout116ContextBO contextBO = projWorkout116GenerateService.createContext(languageList, videoIdList, workout116.getExerciseType());
        List<ProjWorkout116ResVideo116> exercise2RelationBoList = exerciseList.stream().map(this::convert2RelationVideo).collect(Collectors.toList());
        List<ProjWorkout116ResVideo116> relationList = saveOrUpdateRelation(workout116, exercise2RelationBoList, contextBO);

        BaseWorkoutBO workout116BO = null;
        if (Objects.equals(workout116.getExerciseType(), DUMBBELL_MIDWEIGHT.getName())) {
            workout116BO = generateFile4Dumbbell(contextBO, createDumbbellSoundBO(languageList, workout116.getGender()), Collections.singletonList(relationList), languageList).get(0);
        } else if (Objects.equals(workout116.getExerciseType(), RECOVERY.getName())) {
            workout116BO = generateFile4Recovery(contextBO, createRecoverySoundBO(languageList, workout116.getGender()), relationList, languageList);
        } else {
            throw new BizException("Can't support exercise type: " + workout116.getExerciseType());
        }

        List<ProjWorkout116I18n> generateI18nList = setWorkoutInfo(workout116BO, workout116);
        updateWorkout4I18n(workout116, generateI18nList);
    }

    @Override
    public RecoveryGenerateSoundBO createRecoverySoundBO(List<String> languageList, String gender) {

        // 系统音获取并验证是否完整(系统音不区分语言)
        RecoveryConfigWrapper recoverySoundConfigBOWrapper = oog116BizConfig.getRecovery();
        AudioJson116BO firstAudio_en = recoverySoundConfigBOWrapper.getFirstAudio(gender);
        AudioJson116BO nextAudio_en = recoverySoundConfigBOWrapper.getNextAudio(gender);
        AudioJson116BO lastAudio_en = recoverySoundConfigBOWrapper.getLastAudio(gender);

        RecoveryGenerateSoundBO generateSoundBO = new RecoveryGenerateSoundBO();
        generateSoundBO.setFirstAudio(firstAudio_en);
        generateSoundBO.setNextAudio(nextAudio_en);
        generateSoundBO.setLastAudio(lastAudio_en);

        List<AudioJson116BO> startEnAudioList = recoverySoundConfigBOWrapper.getStartAudioList(gender);
        generateSoundBO.setStartAudioList(startEnAudioList);

        // 获取系统音多语言
        List<AudioJson116BO> soundList = Lists.newArrayList(firstAudio_en, nextAudio_en, lastAudio_en);
        soundList.addAll(startEnAudioList);

        Map<Integer, Map<String, AudioJson116BO>> audioJson116I18nMap = projWorkout116GenerateService.getSoundI18n(soundList, languageList);

        // 根据语言匹配对应的音频
        Map<String, RecoveryGenerateSoundBO> languageAudioMap = Maps.newHashMap();
        languageAudioMap.put(GlobalConstant.DEFAULT_LANGUAGE, generateSoundBO);
        languageList.stream().filter(language -> !Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)).forEach(language -> {

            AudioJson116BO firstAudio = audioJson116I18nMap.get(firstAudio_en.getSoundId()).get(language);
            AudioJson116BO nextAudio = audioJson116I18nMap.get(nextAudio_en.getSoundId()).get(language);
            AudioJson116BO lastAudio = audioJson116I18nMap.get(lastAudio_en.getSoundId()).get(language);

            List<AudioJson116BO> startAudioList = startEnAudioList.stream().map(audio -> audioJson116I18nMap.get(audio.getSoundId()).get(language)).collect(Collectors.toList());

            RecoveryGenerateSoundBO soundBO = new RecoveryGenerateSoundBO().setFirstAudio(firstAudio).setNextAudio(nextAudio).setLastAudio(lastAudio)
                    .setStartAudioList(startAudioList);
            languageAudioMap.put(language, soundBO);
        });

        generateSoundBO.setI18nAudioMap(languageAudioMap);
        return generateSoundBO;
    }

    @Override
    public DumbbellGenerateSoundBO createDumbbellSoundBO(List<String> languageList, String gender) {

        // 系统音获取并验证是否完整(系统音不区分语言)
        DumbbellConfigWrapper dumbbellConfigWrapper = oog116BizConfig.getDumbbell();
        AudioJson116BO firstAudio_en = dumbbellConfigWrapper.getFirstAudio(gender);
        AudioJson116BO nextAudio_en = dumbbellConfigWrapper.getNextAudio(gender);
        AudioJson116BO lastAudio_en = dumbbellConfigWrapper.getLastAudio(gender);

        AudioJson116BO rest_en = dumbbellConfigWrapper.getRestAudio(gender);
        AudioJson116BO go_en = dumbbellConfigWrapper.getGoAudio(gender);
        AudioJson116BO three21_en = dumbbellConfigWrapper.getThree21(gender);
        AudioJson116BO beepBeep_en = dumbbellConfigWrapper.getBeepAudio(gender);

        DumbbellGenerateSoundBO generateSoundBO = new DumbbellGenerateSoundBO();
        generateSoundBO.setFirstAudio(firstAudio_en);
        generateSoundBO.setNextAudio(nextAudio_en);
        generateSoundBO.setLastAudio(lastAudio_en);
        generateSoundBO.setRestAudio(rest_en);
        generateSoundBO.setGoAudio(go_en);
        generateSoundBO.setThree21Audio(three21_en);
        generateSoundBO.setBeepBeepAudio(beepBeep_en);

        List<AudioJson116BO> promptEnAudioList = dumbbellConfigWrapper.getPromptAudioList(gender);
        generateSoundBO.setPromptAudioList(promptEnAudioList);

        // 获取系统音多语言
        List<AudioJson116BO> soundList = Lists.newArrayList(firstAudio_en, nextAudio_en, lastAudio_en, rest_en, go_en, three21_en, beepBeep_en);
        soundList.addAll(promptEnAudioList);
        Map<Integer, Map<String, AudioJson116BO>> audioJson116I18nMap = projWorkout116GenerateService.getSoundI18n(soundList, languageList);

        // 根据语言匹配对应的音频
        Map<String, DumbbellGenerateSoundBO> languageAudioMap = Maps.newHashMap();
        languageAudioMap.put(GlobalConstant.DEFAULT_LANGUAGE, generateSoundBO);
        languageList.stream().filter(language -> !Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)).forEach(language -> {

            AudioJson116BO firstAudio = audioJson116I18nMap.get(firstAudio_en.getSoundId()).get(language);
            AudioJson116BO nextAudio = audioJson116I18nMap.get(nextAudio_en.getSoundId()).get(language);
            AudioJson116BO lastAudio = audioJson116I18nMap.get(lastAudio_en.getSoundId()).get(language);

            AudioJson116BO restAudio = audioJson116I18nMap.get(rest_en.getSoundId()).get(language);
            AudioJson116BO goAudio = audioJson116I18nMap.get(go_en.getSoundId()).get(language);
            AudioJson116BO threeAudio = audioJson116I18nMap.get(three21_en.getSoundId()).get(language);
            AudioJson116BO beepBeepAudio = audioJson116I18nMap.get(beepBeep_en.getSoundId()).get(language);

            List<AudioJson116BO> promptAudioList = promptEnAudioList.stream().map(audio -> audioJson116I18nMap.get(audio.getSoundId()).get(language)).collect(Collectors.toList());
            DumbbellGenerateSoundBO soundBO = new DumbbellGenerateSoundBO().setFirstAudio(firstAudio).setNextAudio(nextAudio).setLastAudio(lastAudio)
                    .setRestAudio(restAudio).setGoAudio(goAudio).setThree21Audio(threeAudio).setBeepBeepAudio(beepBeepAudio).setPromptAudioList(promptAudioList);
            languageAudioMap.put(language, soundBO);
        });

        generateSoundBO.setI18nAudioMap(languageAudioMap);
        return generateSoundBO;
    }

    private List<ProjWorkout116I18n> setWorkoutInfo(BaseWorkoutBO workout116BO, ProjWorkout116 workout116) {

        workout116.setAudioJsonUrl(workout116BO.getAudioJsonUrl());
        workout116.setDuration(workout116BO.getDuration());
        workout116.setCalorie(workout116BO.getCalorie().intValue());
        workout116.setVideoUrl(workout116BO.getVideoUrl());
        workout116.setVideo2532Url(workout116BO.getVideo2532Url());

        List<ProjWorkout116I18n> workout116I18ns = workout116BO.getAudioI18nUrl().entrySet().stream()
                .map(entry -> new ProjWorkout116I18n().setLanguage(entry.getKey()).setAudioJsonUrl(entry.getValue()))
                .collect(Collectors.toList());
        return workout116I18ns;
    }

    private BaseWorkoutBO generateFile4Recovery(ProjWorkout116ContextBO contextBO, RecoveryGenerateSoundBO recoverySoundBO,
                                                List<ProjWorkout116ResVideo116> relationList, List<String> languageList) {

        // 采用新的翻译逻辑
        Map<Object, ProjResVideo116I18n> videoI18nMap = contextBO.getVideoI18nMap();
        Map<String, List<AudioJson116BO>> languageAudioMap = initLanguageAudioMap(languageList);

        // TS拼接
        TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
        TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
        AtomicBoolean front = new AtomicBoolean(true);
        AtomicBoolean front4Dynamic = new AtomicBoolean(true);

        int workoutDuration = 0;
        BaseWorkoutBO workout116 = new BaseWorkoutBO();

        BigDecimal workoutCalorie = BigDecimal.ZERO;
        Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap = contextBO.getVideoSliceMap();
        LinkedList<TaiChiRoundBO> roundBOList = wrapRoundBOList4GenerateWorkout(relationList, contextBO.getVideoIdMap());
        for (TaiChiRoundBO roundBO : roundBOList) {

            ResVideo116 currentVideo = roundBO.getResVideo116();
            Integer currentVideoDuration = roundBO.getResVideoDuration();

            boolean lastNode = roundBOList.indexOf(roundBO) + roundBO.getCircuit() == roundBOList.size() - 1;
            assembleAudio4Recovery(recoverySoundBO, currentVideo, videoI18nMap, languageAudioMap, currentVideoDuration, lastNode, workoutDuration, roundBO.getVideoRound());
            generate4TaiChiService.assemble2532M3u8Text(videoSliceMap.get(currentVideo.getId()), front, tsTextMerge2532BO);
            generate4TaiChiService.assembleDynamicM3u8Text(videoSliceMap.get(currentVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);

            // 在计算Video卡路里时，已经考虑了circuit 参数
            if (roundBO.getVideoRound() == 0) {
                workoutCalorie = workoutCalorie.add(currentVideo.getCalorie());
            }

            workoutDuration += currentVideoDuration;
        }

        // 填充基础属性
        workout116.setCalorie(workoutCalorie);
        workout116.setDuration(workoutDuration);

        AtomicBoolean uploadSuccess = new AtomicBoolean(true);
        uploadFileAndSaveUrl(workout116, tsTextMergeDynamicBO, tsTextMerge2532BO, uploadSuccess, languageAudioMap);
        return workout116;
    }

    @Override
    public List<BaseWorkoutBO> generateFile4Dumbbell(ProjWorkout116ContextBO contextBO, DumbbellGenerateSoundBO dumbbellSoundBO,
                                                     List<List<ProjWorkout116ResVideo116>> relationList, List<String> languageList) {

        List<CompletableFuture<Void>> futures = Lists.newLinkedList();
        AtomicBoolean uploadSuccess = new AtomicBoolean(true);
        List<BaseWorkoutBO> workout116List = Lists.newArrayList();
        Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap = contextBO.getVideoSliceMap();
        Map<Object, ProjResVideo116I18n> videoI18nMap = contextBO.getVideoI18nMap();
        for (List<ProjWorkout116ResVideo116> relation : relationList) {

            // 失败即终止
            if (!uploadSuccess.get()) {
                log.warn("oog116 dumbbell workout resource upload m3u8 and audio json failed");
                throw new BizException("oog116 dumbbell workout resource upload m3u8 and audio json failed");
            }

            // TS拼接
            TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
            TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
            AtomicBoolean front = new AtomicBoolean(true);
            AtomicBoolean front4Dynamic = new AtomicBoolean(true);

            int workoutDuration = 0;
            BaseWorkoutBO workout116 = new BaseWorkoutBO();
            workout116List.add(workout116);

            BigDecimal workoutCalorie = BigDecimal.ZERO;
            Map<String, List<AudioJson116BO>> languageAudioMap = initLanguageAudioMap(languageList);
            LinkedList<TaiChiRoundBO> roundBOList = wrapRoundBOList4GenerateWorkout(relation, contextBO.getVideoIdMap());
            for (TaiChiRoundBO roundBO : roundBOList) {

                ResVideo116 currentVideo = roundBO.getResVideo116();
                Integer currentVideoDuration = roundBO.getResVideoDuration();

                boolean lastNode = roundBOList.indexOf(roundBO) + roundBO.getCircuit() >= roundBOList.size() - 1;
                assembleAudio4Dumbbell(dumbbellSoundBO, currentVideo, videoI18nMap, languageAudioMap, currentVideoDuration, lastNode, workoutDuration, roundBO.getVideoRound());
                generate4TaiChiService.assemble2532M3u8Text(videoSliceMap.get(currentVideo.getId()), front, tsTextMerge2532BO);
                generate4TaiChiService.assembleDynamicM3u8Text(videoSliceMap.get(currentVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);

                // 在计算Video卡路里时，已经考虑了circuit 参数
                if (roundBO.getVideoRound() == 0) {
                    workoutCalorie = workoutCalorie.add(currentVideo.getCalorie());
                }

                workoutDuration += currentVideoDuration;
            }

            // 填充基础属性
            workout116.setCalorie(workoutCalorie);
            workout116.setDuration(workoutDuration);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                    uploadFileAndSaveUrl(workout116, tsTextMergeDynamicBO, tsTextMerge2532BO, uploadSuccess, languageAudioMap), poolTaskExecutor);
            futures.add(future);
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return workout116List;
    }

    @Override
    public void assembleAudio4Recovery(RecoveryGenerateSoundBO soundBO, ResVideo116 currentVideo,
                                       Map<Object, ProjResVideo116I18n> videoI18nMap,
                                       Map<String, List<AudioJson116BO>> languageAudioJsonMap, Integer currentVideoDuration,
                                       boolean lastNode, int workoutDuration, int circuitIndex) {

        languageAudioJsonMap.entrySet().forEach(entry -> {

            RecoveryGenerateSoundBO i18nSoundBO = soundBO.getI18nAudioMap().getOrDefault(entry.getKey(), soundBO);
            String guidanceUrl = getI18nGuidance(currentVideo, videoI18nMap, entry.getKey());
            ResVideo116 video4NameAudio = getI18nName(currentVideo, videoI18nMap, entry.getKey());

            int playTime = workoutDuration;
            List<AudioJson116BO> guidanceAudioList = entry.getValue();
            boolean firstNode = guidanceAudioList.size() == 0;
            if (circuitIndex == -1) {

                // first, next audio or last audio
                AudioJson116BO playAudio = firstNode ? i18nSoundBO.getFirstAudio() : lastNode ? i18nSoundBO.getLastAudio() : i18nSoundBO.getNextAudio();
                addSysAudioJson(guidanceAudioList, playAudio, playTime + GlobalConstant.HUNDRED);
                playTime += playAudio.getDuration().intValue();

                // name audio
                playTime += GlobalConstant.THOUSAND;
                String nameUrl = video4NameAudio.getNameAudioUrl();
                AudioJson116BO nameAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(nameUrl), fileService.getAbsoluteR2Url(nameUrl), FireBaseUrlSubUtils.getFileName(nameUrl), null, null, false, null, null, null);
                addSysAudioJson(guidanceAudioList, nameAudio, playTime);

                // guidance audio
                playTime += GlobalConstant.THOUSAND + video4NameAudio.getNameAudioUrlDuration();
                AudioJson116BO guidanceAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(guidanceUrl), fileService.getAbsoluteR2Url(guidanceUrl), FireBaseUrlSubUtils.getFileName(guidanceUrl), null, null, true, null, null, null);
                addSysAudioJson(guidanceAudioList, guidanceAudio, playTime);
            } else {

                List<AudioJson116BO> startAudioList = i18nSoundBO.getStartAudioList();
                AudioJson116BO startAudio = randomSelectOne(startAudioList);
                addSysAudioJson(guidanceAudioList, startAudio, playTime + GlobalConstant.HUNDRED);

                // guidance audio
                playTime += startAudio.getDuration() + GlobalConstant.THOUSAND;
                AudioJson116BO guidanceAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(guidanceUrl), fileService.getAbsoluteR2Url(guidanceUrl), FireBaseUrlSubUtils.getFileName(guidanceUrl), null, null, true, null, null, null);
                addSysAudioJson(guidanceAudioList, guidanceAudio, playTime);
            }
        });
    }

    private String getI18nGuidance(ResVideo116 currentVideo, Map<Object, ProjResVideo116I18n> videoI18nMap, String language) {

        String guidanceUrl = currentVideo.getGuidanceAudioUrl();
        ProjResVideo116I18n i18nVideoMap = videoI18nMap.get(currentVideo.getId());
        Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = i18nVideoMap.getGuidanceResult() == null ? MapUtil.empty() :
                i18nVideoMap.getGuidanceResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
        LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
        if (MapUtils.isNotEmpty(guidanceMap) && guidanceMap.containsKey(le)) {
            AudioTranslateResultModel audioTranslateResultModel = guidanceMap.get(le);
            guidanceUrl = audioTranslateResultModel.getAudioUrl();
        }
        return guidanceUrl;
    }

    private ResVideo116 getI18nName(ResVideo116 currentVideo, Map<Object, ProjResVideo116I18n> videoI18nMap, String language) {

        String nameAudioUrl = currentVideo.getNameAudioUrl();
        Integer nameAudioDuration = currentVideo.getNameAudioUrlDuration();

        ProjResVideo116I18n i18nVideoMap = videoI18nMap.get(currentVideo.getId());
        Map<LanguageEnums, AudioTranslateResultModel> nameMap = i18nVideoMap.getResult() == null ? MapUtil.empty() :
                i18nVideoMap.getResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
        LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);

        if (MapUtils.isNotEmpty(nameMap) && nameMap.containsKey(le)) {
            AudioTranslateResultModel audioTranslateResultModel = nameMap.get(le);
            nameAudioUrl = audioTranslateResultModel.getAudioUrl();
            nameAudioDuration = audioTranslateResultModel.getDuration();
        }

        ResVideo116 video116 = new ResVideo116();
        video116.setNameAudioUrl(nameAudioUrl);
        video116.setNameAudioUrlDuration(nameAudioDuration);
        return video116;
    }

    private AudioJson116BO randomSelectOne(List<AudioJson116BO> audioJsonBOList) {

        Collections.shuffle(audioJsonBOList);
        return audioJsonBOList.get(GlobalConstant.ZERO);
    }

    private void addSysAudioJson(List<AudioJson116BO> audioList, AudioJson116BO sysAudioJson, int playTime) {

        AudioJson116BO audioJsonBO = new AudioJson116BO(sysAudioJson.getId(), sysAudioJson.getUrl(), sysAudioJson.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP), sysAudioJson.getDuration(), sysAudioJson.isClose(), null, null, null);
        audioList.add(audioJsonBO);
    }

    public void assembleAudio4Dumbbell(DumbbellGenerateSoundBO soundBO, ResVideo116 currentVideo,
                                       Map<Object, ProjResVideo116I18n> videoI18nMap,
                                       Map<String, List<AudioJson116BO>> languageAudioJsonMap, Integer currentVideoDuration,
                                       boolean lastNode, int workoutDuration, int circuitIndex) {

        languageAudioJsonMap.entrySet().forEach(entry -> {

            DumbbellGenerateSoundBO i18nSoundBO = soundBO.getI18nAudioMap().getOrDefault(entry.getKey(), soundBO);
            String guidanceUrl = getI18nGuidance(currentVideo, videoI18nMap, entry.getKey());
            ResVideo116 video4NameAudio = getI18nName(currentVideo, videoI18nMap, entry.getKey());

            int playTime = workoutDuration;
            List<AudioJson116BO> guidanceAudioList = entry.getValue();
            boolean firstNode = guidanceAudioList.size() == 0;
            if (circuitIndex == -1) {

                // first, next audio or last audio
                AudioJson116BO playAudio = firstNode ? i18nSoundBO.getFirstAudio() : lastNode ? i18nSoundBO.getLastAudio() : i18nSoundBO.getNextAudio();
                addSysAudioJson(guidanceAudioList, playAudio, playTime + GlobalConstant.HUNDRED);
                playTime += playAudio.getDuration().intValue();

                // name audio
                playTime += GlobalConstant.THOUSAND;
                String nameUrl = video4NameAudio.getNameAudioUrl();
                AudioJson116BO nameAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(nameUrl), fileService.getAbsoluteR2Url(nameUrl), FireBaseUrlSubUtils.getFileName(nameUrl), null, null, false, null, null, null);
                addSysAudioJson(guidanceAudioList, nameAudio, playTime);

                // guidance audio
                playTime += GlobalConstant.THOUSAND + video4NameAudio.getNameAudioUrlDuration();
                AudioJson116BO guidanceAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(guidanceUrl), fileService.getAbsoluteR2Url(guidanceUrl), FireBaseUrlSubUtils.getFileName(guidanceUrl), null, null, true, null, null, null);
                addSysAudioJson(guidanceAudioList, guidanceAudio, playTime);

                // 321
                playTime = workoutDuration + currentVideoDuration - i18nSoundBO.getThree21Audio().getDuration();
                addSysAudioJson(guidanceAudioList, i18nSoundBO.getThree21Audio(), playTime);
            } else {

                // go
                AudioJson116BO goAudio = i18nSoundBO.getGoAudio();
                addSysAudioJson(guidanceAudioList, goAudio, playTime + GlobalConstant.HUNDRED);

                // prompt
                playTime = workoutDuration + currentVideoDuration / 2;
                List<AudioJson116BO> promptAudioList = i18nSoundBO.getPromptAudioList();
                AudioJson116BO promptAudio = randomSelectOne(promptAudioList);
                addSysAudioJson(guidanceAudioList, promptAudio, playTime);

                // 321
                AudioJson116BO restAudio = lastNode ? i18nSoundBO.getBeepBeepAudio() : i18nSoundBO.getRestAudio();
                if (!lastNode) {
                    playTime = workoutDuration + currentVideoDuration - i18nSoundBO.getThree21Audio().getDuration() - restAudio.getDuration();
                    addSysAudioJson(guidanceAudioList, i18nSoundBO.getThree21Audio(), playTime);
                }

                // rest
                playTime = workoutDuration + currentVideoDuration - restAudio.getDuration();
                addSysAudioJson(guidanceAudioList, restAudio, playTime);
            }
        });
    }

    private LinkedList<TaiChiRoundBO> wrapRoundBOList4GenerateWorkout(List<ProjWorkout116ResVideo116> video116BOList, Map<Integer, ResVideo116> videoIdMap) {

        LinkedList<TaiChiRoundBO> taiChiRoundBOList = Lists.newLinkedList();
        Map<String, List<ProjWorkout116ResVideo116>> unitAndVideoMap = video116BOList.stream().collect(Collectors.groupingBy(ProjWorkout116ResVideo116::getUnitName));
        LinkedHashMap<String, Integer> unitAndRoundMap = video116BOList.stream().collect(Collectors.toMap(video -> video.getUnitName(), video -> video.getRounds(), (k1, k2) -> k1, LinkedHashMap::new));

        unitAndRoundMap.entrySet().forEach(entry -> {
            List<ProjWorkout116ResVideo116> unitVideos = unitAndVideoMap.get(entry.getKey());
            for (int roundIndex = 0; roundIndex < entry.getValue(); roundIndex++) {
                for (ProjWorkout116ResVideo116 unitVideo : unitVideos) {
                    int[] circuitDurations = Arrays.stream(unitVideo.getCircuitVideoDuration().split(GlobalConstant.COMMA)).mapToInt(Integer::parseInt).toArray();
                    for (int circuitIndex = -1; circuitIndex < unitVideo.getCircuit(); circuitIndex++) {

                        int videoDuration;
                        if (circuitIndex == -1) {
                            videoDuration = unitVideo.getPreviewDuration();
                        } else {
                            videoDuration = circuitDurations[circuitIndex];
                        }

                        TaiChiRoundBO taiChiRoundBO = new TaiChiRoundBO(videoIdMap.get(unitVideo.getResVideo116Id()), roundIndex, circuitIndex, videoDuration);
                        taiChiRoundBOList.add(taiChiRoundBO);
                    }
                }
            }
        });
        return taiChiRoundBOList;
    }

    private Map<String, List<AudioJson116BO>> initLanguageAudioMap(List<String> languageList) {

        LinkedHashMap<String, List<AudioJson116BO>> audioListMap = new LinkedHashMap<>();
        for (String language : languageList) {
            audioListMap.put(language, new ArrayList<>(64));
        }
        return audioListMap;
    }

    private void uploadFileAndSaveUrl(BaseWorkoutBO workout116, TsTextMergeBO tsTextMergeBO,
                                      TsTextMergeBO tsTextMerge2532BO, AtomicBoolean uploadSuccess,
                                      Map<String, List<AudioJson116BO>> audioListMap) {

        if (!uploadSuccess.get()) {
            throw new BizException("oog116 upload m3u8 and audio json failed");
        }

        try{
            // 上传m3u8，并保存相对地址
            UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(tsTextMergeBO, WORKOUT_M3U8_DIR_KEY);
            UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8(tsTextMerge2532BO, WORKOUT_M3U8_DIR_KEY);
            workout116.setVideoUrl(videoR2Info.getFileRelativeUrl()).setVideo2532Url(video2532R2Info.getFileRelativeUrl());

            Map<String, String> audioI18nUrlMap = new HashMap<>();
            workout116.setAudioI18nUrl(audioI18nUrlMap);
            audioListMap.entrySet().forEach(entry -> {

                // 上传音频JSON
                String language = entry.getKey();
                List<AudioJson116BO> audioList = entry.getValue();
                UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioList), "project-workout116-json");

                // 保存音频相对地址
                if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                    workout116.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                }
                audioI18nUrlMap.put(language, audioJsonR2Info.getFileRelativeUrl());
            });
        } catch (Exception e) {
            uploadSuccess.set(false);
            log.warn("upload 116 file error", e);
        }
    }

    private ProjWorkout116ResVideo116 convert2RelationVideo(ProjWorkout116ExerciseAddReq exercise) {

        return new ProjWorkout116ResVideo116().setResVideo116Id(exercise.getId())
                .setRounds(exercise.getRounds()).setUnitName(exercise.getUnitName());
    }

    private void updateWorkout4I18n(ProjWorkout116 workout116, List<ProjWorkout116I18n> generateI18nList) {

        if (!CollectionUtils.isEmpty(generateI18nList)) {
            Set<String> audioLanguageSet = Sets.newHashSet(GlobalConstant.DEFAULT_LANGUAGE);
            String audioLanguages = workout116.getAudioLanguages();
            if (StrUtil.isNotBlank(audioLanguages)) {
                audioLanguageSet.addAll(Arrays.stream(audioLanguages.split(StringPool.COMMA)).collect(Collectors.toSet()));
            }

            List<String> languages = generateI18nList.stream().map(ProjWorkout116I18n::getLanguage).distinct().collect(Collectors.toList());
            audioLanguageSet.addAll(languages);
            workout116.setAudioLanguages(StringUtils.join(audioLanguageSet, StringPool.COMMA));
        }

        workout116.setFileStatus(GlobalConstant.STATUS_ENABLE);
        projWorkout116Service.updateById(workout116);
        if (CollectionUtils.isEmpty(generateI18nList)) {
            return;
        }

        generateI18nList.forEach(generateI18n -> {
            generateI18n.setProjId(workout116.getProjId());
            generateI18n.setId(workout116.getId());
        });

        List<ProjWorkout116I18n> existedI18nList = projWorkout116I18nService.listByWorkoutId(workout116.getId());
        if (CollectionUtils.isEmpty(existedI18nList)) {
            projWorkout116I18nService.saveBatch(generateI18nList);
            return;
        }

        Table<Integer, String, ProjWorkout116I18n> existedGenerateI18nMap = HashBasedTable.create();
        existedI18nList.forEach(existedGenerateI18n -> existedGenerateI18nMap.put(existedGenerateI18n.getId(), existedGenerateI18n.getLanguage(), existedGenerateI18n));
        List<ProjWorkout116I18n> updateList = Lists.newArrayList();
        List<ProjWorkout116I18n> insertList = Lists.newArrayList();

        generateI18nList.forEach(generateI18n -> {
            if (existedGenerateI18nMap.contains(generateI18n.getId(), generateI18n.getLanguage())) {
                updateList.add(generateI18n);
            } else {
                insertList.add(generateI18n);
            }
        });

        projWorkout116I18nService.saveBatch(insertList);
        updateGenerateI18n(updateList);
    }

    private void updateGenerateI18n(List<ProjWorkout116I18n> updateList) {

        updateList.forEach(generateI18n -> {
            LambdaUpdateWrapper<ProjWorkout116I18n> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProjWorkout116I18n::getId, generateI18n.getId());
            updateWrapper.eq(ProjWorkout116I18n::getLanguage, generateI18n.getLanguage());
            updateWrapper.set(ProjWorkout116I18n::getAudioJsonUrl, generateI18n.getAudioJsonUrl());
            updateWrapper.set(ProjWorkout116I18n::getUpdateTime, LocalDateTime.now());
            updateWrapper.set(ProjWorkout116I18n::getUpdateUser, RequestContextUtils.getLoginUserName());
            projWorkout116I18nService.update(updateWrapper);
        });
    }

    private List<ProjWorkout116ResVideo116> saveOrUpdateRelation(ProjWorkout116 workout116,
                                                                 List<ProjWorkout116ResVideo116> exerciseList,
                                                                 ProjWorkout116ContextBO contextBO) {

        // delete old relation
        deleteRelation(workout116.getId());

        Map<Integer, ResVideo116> videoIdMap = contextBO.getVideoIdMap();
        List<ProjWorkout116ResVideo116> relationList = exerciseList.stream().map(relationBO -> {

            ProjWorkout116ResVideo116 relation = new ProjWorkout116ResVideo116();
            relation.setResVideo116Id(relationBO.getResVideo116Id());
            relation.setProjWorkout116Id(workout116.getId());

            relation.setProjId(workout116.getProjId());
            relation.setUnitName(relationBO.getUnitName());
            relation.setRounds(relationBO.getRounds());

            ResVideo116 video116 = videoIdMap.get(relationBO.getResVideo116Id());
            Integer circuit = Objects.isNull(video116.getCircuit()) ? 1 : video116.getCircuit();
            relation.setCircuit(circuit);
            return relation;
        }).collect(Collectors.toList());

        computeDuration(contextBO, relationList);
        projWorkout116ResVideo116Service.saveBatch(relationList);
        //添加翻译任务
        projLmsI18nService.handleI18n(relationList, workout116.getProjId());
        return relationList;
    }

    public void computeDuration(ProjWorkout116ContextBO context,
                                List<ProjWorkout116ResVideo116> workoutResVideoList) {

        AtomicBoolean front = new AtomicBoolean(true);
        workoutResVideoList.forEach(resVideo116 -> {

            List<Integer> videoIdList = Collections.singletonList(resVideo116.getResVideo116Id());
            // preview duration
            Integer previewDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                    context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, videoIdList);
            resVideo116.setPreviewDuration(previewDuration);

            // circuit duration
            List<Integer> circuitDurationList = new ArrayList<>();
            for (int i = 0; i < resVideo116.getCircuit(); i++) {
                Integer circuitDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                        context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, videoIdList);
                circuitDurationList.add(circuitDuration);
            }
            resVideo116.setCircuitVideoDuration(convertList2String(circuitDurationList));
        });
    }

    private String convertList2String(List<Integer> circuitDurationList) {

        if (CollectionUtils.isEmpty(circuitDurationList)) {
            return "";
        }

        return StringUtils.joinWith(GlobalConstant.COMMA, circuitDurationList.toArray());
    }

    private Integer computeVideoDuration4Circuit(Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap,
                                                 Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap, AtomicBoolean front,
                                                 List<Integer> videoIdList) {

        int videoDuration = 0;
        for (Integer video116Id : videoIdList) {
            if (front.get()) {
                videoDuration += frontDurationMap.get(video116Id);
            } else {
                videoDuration += sideDurationMap.get(video116Id);
            }

            if (videoSliceMap.get(video116Id).size() % 2 != 0) {
                if (front.get()) {
                    front.set(false);
                } else {
                    front.set(true);
                }
            }
        }

        return videoDuration;
    }

    private void deleteRelation(Integer id) {
        LambdaUpdateWrapper<ProjWorkout116ResVideo116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkout116ResVideo116::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjWorkout116ResVideo116::getProjWorkout116Id, id);
        projWorkout116ResVideo116Service.update(new ProjWorkout116ResVideo116(), wrapper);
    }

    private void bizCheck(ProjWorkout116 workout116, List<ProjWorkout116ExerciseAddReq> exerciseList) {

        if (!EXERCISE_LIST.contains(workout116.getExerciseType())) {
            throw new BizException(String.format("Exercise type is invalid, exercise type is %s.", workout116.getExerciseType()));
        }

        if (CollectionUtils.isEmpty(exerciseList)) {
            throw new BizException("Exercise list can not be empty.");
        }

        Optional<ProjWorkout116ExerciseAddReq> optional = exerciseList.stream().filter(exercise ->
                StringUtils.isBlank(exercise.getUnitName()) || (Objects.isNull(exercise.getRounds()) || exercise.getRounds() < 1)).findAny();
        if (optional.isPresent()) {
            throw new BizException("Unit name or rounds is illegal.");
        }
    }

    @Override
    public void handleWorkout4BatchUpdate(ProjWorkout116 workout116, List<String> languageList) {

        List<ProjWorkout116ResVideo116> relationList = projWorkout116ResVideo116Service.listByWorkoutId(workout116.getId());
        List<Integer> videoIdList = relationList.stream().map(ProjWorkout116ResVideo116::getResVideo116Id).collect(Collectors.toList());
        projWorkout116GenerateService.checkVideoI18nResource(languageList, videoIdList);

        ProjWorkout116ContextBO contextBO = projWorkout116GenerateService.createContext(languageList, videoIdList, workout116.getExerciseType());
        transactionTemplate.execute(status -> {
            List<ProjWorkout116ResVideo116> updatedRelationList = saveOrUpdateRelation(workout116, relationList, contextBO);

            BaseWorkoutBO workout116BO = null;
            if (Objects.equals(workout116.getExerciseType(), DUMBBELL_MIDWEIGHT.getName())) {
                workout116BO = generateFile4Dumbbell(contextBO, createDumbbellSoundBO(languageList, workout116.getGender()), Collections.singletonList(updatedRelationList), languageList).get(0);
            } else if (Objects.equals(workout116.getExerciseType(), RECOVERY.getName())) {
                workout116BO = generateFile4Recovery(contextBO, createRecoverySoundBO(languageList, workout116.getGender()), updatedRelationList, languageList);
            } else {
                throw new BizException("Can't support exercise type: " + workout116.getExerciseType());
            }

            List<ProjWorkout116I18n> generateI18nList = setWorkoutInfo(workout116BO, workout116);
            updateWorkout4I18n(workout116, generateI18nList);
            return null;
        });
    }

}
