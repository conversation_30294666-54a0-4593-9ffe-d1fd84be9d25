package com.laien.web.biz.proj.oog116.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Oog116BizConfig {

    @ConfigurationProperties(value = "cms.biz.oog116")
    @Bean
    public Video116SysSoundBOWrapper getOog116() {
        return new Video116SysSoundBOWrapper();
    }

    @ConfigurationProperties(value = "cms.biz.oog116.cardio105")
    @Bean
    public Cardio105SoundBOWrapper getCardio105() {
        return new Cardio105SoundBOWrapper();
    }

    @ConfigurationProperties(value = "cms.biz.oog116.taichi")
    @Bean
    public TaiChiSoundConfigBOWrapper getTaichi() {
        return new TaiChiSoundConfigBOWrapper();
    }

    @ConfigurationProperties(value = "cms.biz.oog116.recovery")
    @Bean
    public RecoveryConfigWrapper getRecovery() {
        return new RecoveryConfigWrapper();
    }

    @ConfigurationProperties(value = "cms.biz.oog116.dumbbell")
    @Bean
    public DumbbellConfigWrapper getDumbbell() {
        return new DumbbellConfigWrapper();
    }

    @ConfigurationProperties(value = "cms.biz.oog116.chair-yoga")
    @Bean
    public ChairYogaConfigWrapper getChairYoga() {
        return new ChairYogaConfigWrapper();
    }

}
