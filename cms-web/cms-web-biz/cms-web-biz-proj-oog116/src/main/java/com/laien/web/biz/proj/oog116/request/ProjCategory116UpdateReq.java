package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: category116 修改
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "category116 修改", description = "category116 修改")
public class ProjCategory116UpdateReq extends ProjCategory116AddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
