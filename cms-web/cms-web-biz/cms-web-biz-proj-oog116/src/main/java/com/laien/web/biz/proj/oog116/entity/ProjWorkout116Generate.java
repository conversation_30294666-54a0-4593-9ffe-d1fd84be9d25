package com.laien.web.biz.proj.oog116.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 116生成的workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjWorkout116Generate对象", description = "116生成的workout")
public class ProjWorkout116Generate extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_template116_id")
    private Integer projTemplate116Id;

    @ApiModelProperty(value = "proj_template116_task_id")
    private Integer projTemplate116TaskId;

    @ApiModelProperty(value = "res_image_id")
    private Integer resImageId;

    @ApiModelProperty(value = "Seated/Standing")
    @TranslateField
    private String position;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String restriction;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "生成workout的时候的限制（限制枚举值的和）")
    private Integer restrictionSum;

    /**
     * 增加dataVersion，兼容app接口v1
     */
    @ApiModelProperty(value = "workout 数据版本，0：v1接口版本，1：v2及以上接口版本")
    private Integer dataVersion;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "video的 2532 m3u8地址")
    @TableField(value = "video2532_url")
    private String video2532Url;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;
}
