package com.laien.web.biz.proj.oog116.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog116.enums.Category116TypeEnums;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * template116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCategory116对象", description="template116")
public class ProjCategory116 extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类名称")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "展示类型")
    private String showType;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "简介")
    @TranslateField
    private String description;

    @ApiModelProperty(value = "icon url")
    private String iconUrl;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "性别")
    private Gender116Enums gender;

    @ApiModelProperty(value = "type")
    private Category116TypeEnums type;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
