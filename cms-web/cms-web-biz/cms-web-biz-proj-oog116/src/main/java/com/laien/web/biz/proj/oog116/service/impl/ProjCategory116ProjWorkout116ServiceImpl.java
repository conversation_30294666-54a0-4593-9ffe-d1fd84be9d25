package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ProjCategory116ProjWorkout116;
import com.laien.web.biz.proj.oog116.mapper.ProjCategory116ProjWorkout116Mapper;
import com.laien.web.biz.proj.oog116.response.ProjCategory116DetailWorkoutVO;
import com.laien.web.biz.proj.oog116.service.IProjCategory116ProjWorkout116Service;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * proj_workout116_res_video116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjCategory116ProjWorkout116ServiceImpl extends ServiceImpl<ProjCategory116ProjWorkout116Mapper, ProjCategory116ProjWorkout116> implements IProjCategory116ProjWorkout116Service {

    @Override
    public List<IdAndStatusCountsRes> selectWorkoutStatusCount(Integer projId) {
        return this.baseMapper.selectWorkoutStatusCount(projId);
    }

    @Override
    public List<ProjCategory116DetailWorkoutVO> selectWorkoutByCategoryId(Integer categoryId) {
        return this.baseMapper.selectWorkoutByCategoryId(categoryId);
    }

}
