package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116I18n;
import com.laien.web.biz.proj.oog116.mapper.ProjWorkout116I18nMapper;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116I18nService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * proj_workout116 i18n 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Service
public class ProjWorkout116I18nServiceImpl extends ServiceImpl<ProjWorkout116I18nMapper, ProjWorkout116I18n> implements IProjWorkout116I18nService {

    @Override
    public List<ProjWorkout116I18n> listByWorkoutId(Integer workoutId) {

        LambdaQueryWrapper<ProjWorkout116I18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkout116I18n::getId, workoutId);
        return list(queryWrapper);
    }
}
