package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "Sound116 编辑", description = "Sound116 编辑")
public class ProjSound116UpdateReq extends ProjSound116AddReq {

    @ApiModelProperty(value = "id")
    private Integer id;
}
