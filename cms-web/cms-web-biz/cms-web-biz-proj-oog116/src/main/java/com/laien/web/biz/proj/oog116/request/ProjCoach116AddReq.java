package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessCoachAddReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjCoach116AddReq", description="ProjCoach116AddReq")
public class ProjCoach116AddReq {

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "photoImgUrl")
    private String coverImgUrl;

    @ApiModelProperty(value = "introduction")
    private String introduction;

}
