package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjCategory116;
import com.laien.web.biz.proj.oog116.request.ProjCategory116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjCategory116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjCategory116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjCategory116ListVO;

import java.util.List;

/**
 * <p>
 * template116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjCategory116Service extends IService<ProjCategory116> {

    /**
     * category116 列表
     *
     * @return List
     */
    List<ProjCategory116ListVO> selectCategoryList();

    /**
     * category116 新增
     *
     * @param category116AddReq category116AddReq
     */
    void saveCategory(ProjCategory116AddReq category116AddReq);

    /**
     * category116 修改
     *
     * @param category116UpdateReq category116UpdateReq
     */
    void updateCategory(ProjCategory116UpdateReq category116UpdateReq);

    /**
     * category116 详情
     *
     * @param id id
     * @return ProjCategory116DetailVO
     */
    ProjCategory116DetailVO getCategoryDetail(Integer id);

    /**
     * category116 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * category116 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * category116 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * category116 排序
     *
     * @param idList idList
     */
    void saveSort(List<Integer> idList);

}
