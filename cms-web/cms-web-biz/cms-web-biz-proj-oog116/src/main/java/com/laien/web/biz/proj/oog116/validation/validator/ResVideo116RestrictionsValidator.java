package com.laien.web.biz.proj.oog116.validation.validator;

import cn.hutool.core.util.StrUtil;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.proj.oog116.validation.ResVideo116Restrictions;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

public class ResVideo116RestrictionsValidator implements ConstraintValidator<ResVideo116Restrictions, String> {


    private static String options[] = {"Shoulder", "Back", "Wrist", "Knee", "Ankle", "Hip"};

    @Override
    public void initialize(ResVideo116Restrictions constraintAnnotation) {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StrUtil.isBlank(value)) {
            return true;
        }
        if (value.equals(BizConstant.NONE)) {
            return true;
        }
        List<String> strs = StrUtil.splitTrim(value, ",");
        for (String str : strs) {
            if (StrUtil.containsAny(str, options)) {
                if (StrUtil.ordinalIndexOf(value, str, 2) != -1) {
                    return false;
                }
            } else {
                return false;
            }
        }
        return true;
    }
}