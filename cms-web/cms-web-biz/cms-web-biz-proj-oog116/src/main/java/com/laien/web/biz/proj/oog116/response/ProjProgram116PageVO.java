package com.laien.web.biz.proj.oog116.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Data
public class ProjProgram116PageVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "equipment")
    private String equipment;

    @ApiModelProperty(value = "goal选项，可填写多个，英文逗号分隔")
    private String goals;

    @ApiModelProperty(value = "是否收费, 1是；0否")
    private Integer subscription;

    @ApiModelProperty(value = "一个program下面的workout num")
    private Long workoutNum;

}
