package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Template116 修改
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Template116 修改", description = "Template116 修改")
public class ProjTemplate116UpdateReq extends ProjTemplate116AddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
