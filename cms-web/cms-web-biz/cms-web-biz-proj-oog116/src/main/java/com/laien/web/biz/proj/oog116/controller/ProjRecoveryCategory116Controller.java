package com.laien.web.biz.proj.oog116.controller;

import com.laien.common.frame.request.IdListReq;
import com.laien.web.biz.proj.oog116.request.ProjRecoveryCategory116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjRecoveryCategory116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116ListVO;
import com.laien.web.biz.proj.oog116.service.IProjRecoveryCategory116Service;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_recovery_category116 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Api(tags = "项目管理:recovery category 116")
@RestController
@RequestMapping("/proj/recoveryCategory116")
public class ProjRecoveryCategory116Controller extends ResponseController {

    @Resource
    private IProjRecoveryCategory116Service projRecoveryCategory116Service;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjRecoveryCategory116ListVO>> list() {
        List<ProjRecoveryCategory116ListVO> list = projRecoveryCategory116Service.selectCategoryList();
        return succ(list);
    }

    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjRecoveryCategory116AddReq addReq) {
        projRecoveryCategory116Service.saveCategory(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjRecoveryCategory116UpdateReq updateReq) {
        projRecoveryCategory116Service.updateCategory(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjRecoveryCategory116DetailVO> detail(@PathVariable Integer id) {
        if (Objects.isNull(id)) {
            return fail("id cannot be null");
        }
        ProjRecoveryCategory116DetailVO detailVO = projRecoveryCategory116Service.getCategoryDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq req) {
        projRecoveryCategory116Service.updateEnableByIds(req.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq req) {
        projRecoveryCategory116Service.updateDisableByIds(req.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestBody IdListReq req) {
        projRecoveryCategory116Service.deleteByIds(req.getIdList());
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq req) {
        projRecoveryCategory116Service.saveSort(req.getIdList());
        return succ();
    }
}
