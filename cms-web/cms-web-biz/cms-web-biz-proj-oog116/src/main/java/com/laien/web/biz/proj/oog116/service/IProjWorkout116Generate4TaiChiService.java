package com.laien.web.biz.proj.oog116.service;

import com.laien.common.oog116.enums.Equipment116Enums;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.common.oog116.enums.Position116Enums;
import com.laien.common.oog116.enums.Restriction116Enums;
import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.common.file.bo.TsTextMergeBO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Author:  hhl
 * Date:  2025/3/5 14:09
 */
public interface IProjWorkout116Generate4TaiChiService {

    public List<ProjWorkout116GenerateBO> generateWorkoutByPlan(ExerciseType116Enums exerciseType,
                                                                Equipment116Enums equipment,
                                                                List<Restriction116Enums> restrictionList,
                                                                Position116Enums position,
                                                                ProjWorkout116ContextBO context);

    public void generateFile(List<ProjWorkout116GenerateBO> projWorkout116BOList,
                             ProjWorkout116ContextBO contextBO,
                             List<String> languageList,
                             Boolean videoFlag,
                             Boolean audioFlag,
                             Boolean restrictionFlag);

    Integer computeDurationWithCircuit(ProjWorkout116ContextBO context, List<ProjWorkout116GenerateResVideo116BO> workoutResVideoList);

    /**
     * 组装多分辨率的多序列TS
     *
     * @param sliceDetailVOList
     * @param front
     * @param tsTextMergeDynamicBO
     */
    void assembleDynamicM3u8Text(List<ResVideo116SliceDetailVO> sliceDetailVOList, AtomicBoolean front, TsTextMergeBO tsTextMergeDynamicBO);

    /**
     * 组装2532分辨率的多序列TS
     *
     * @param sliceDetailVOList
     * @param front
     * @param tsTextMerge2532BO
     */
    void assemble2532M3u8Text(List<ResVideo116SliceDetailVO> sliceDetailVOList, AtomicBoolean front, TsTextMergeBO tsTextMerge2532BO);

    void assembleGuidanceAudio(TaiChiGenerateSoundBO soundBO, ResVideo116 currentVideo,
                               Map<Object, ProjResVideo116I18n> videoI18nMap,
                               Map<String, List<AudioJson116BO>> languageAudioJsonMap, Integer currentVideoDuration,
                               boolean lastNode, int workoutDuration, int circuitIndex);

    TaiChiGenerateSoundBO createTaiChiSoundBO(List<String> languageList);
}
