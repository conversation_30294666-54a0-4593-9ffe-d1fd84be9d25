package com.laien.web.biz.proj.oog116.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Data
public class ProjCoach116VO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "coverImgUrl")
    private String coverImgUrl;

    @ApiModelProperty(value = "introduction")
    private String introduction;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;

}
