package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 生成m3u8请求参数
 *
 * <AUTHOR>
 * @since 2024/7/26
 */
@Data
@ApiModel(value = "生成m3u8请求参数", description = "生成m3u8请求参数")
public class ProjWorkout116GenerateM3u8Req implements Serializable {
    private static final long serialVersionUID = 6580938892122401588L;

    @ApiModelProperty(value = "是否生成视频")
    private Boolean videoFlag;

    @ApiModelProperty(value = "是否生成音频")
    private Boolean audioFlag;

    @ApiModelProperty(value = "音频需生成的语言")
    private List<String> languages = new ArrayList<>();

    @ApiModelProperty(value = "workout ids")
    private List<Integer> workoutIds = new ArrayList<>();

    @ApiModelProperty(value = "116 template Id")
    private Integer templateId;

    @ApiModelProperty(value = "page Req")
    private ProjWorkout116GeneratePageReq pageReq;

}
