package com.laien.web.biz.proj.oog116.request;

import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: workout116分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout116分页", description = "workout116分页")
public class ProjWorkout116PageReq extends PageReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "难度Easy，Medium，Hard")
    private String difficulty;

    @ApiModelProperty(value = "Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制，数组，Should<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Hip;")
    private String[] restrictionArr;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "身体部位 (多选)")
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)")
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具 (多选)")
    private List<SupportProp116Enums> supportProp;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "排除recovery workout标识，true:排除，false:不排除")
    private Boolean excludeRecoveryWorkout = false;
}
