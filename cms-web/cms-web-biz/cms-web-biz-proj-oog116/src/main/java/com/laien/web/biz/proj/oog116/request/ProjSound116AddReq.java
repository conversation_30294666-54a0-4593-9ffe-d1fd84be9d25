package com.laien.web.biz.proj.oog116.request;

import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.oog116.enums.Sound116SubTypeEnums;
import com.laien.common.oog116.enums.Sound116TypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "Sound 新增", description = "Sound 新增")
public class ProjSound116AddReq {

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private Sound116TypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private Sound116SubTypeEnums soundSubType;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "声音")
    private String url;

    @ApiModelProperty(value = "duration")
    private Integer duration;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private Gender116Enums gender;

    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;
}
