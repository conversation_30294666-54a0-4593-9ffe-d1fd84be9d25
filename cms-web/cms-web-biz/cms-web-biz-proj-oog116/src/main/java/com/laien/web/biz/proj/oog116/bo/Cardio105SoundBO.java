package com.laien.web.biz.proj.oog116.bo;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.service.IProjSound116Service;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.exception.BizException;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * note: video111 生成系统音配置
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video106 生成系统音配置", description = "video106 生成系统音配置")
public class Cardio105SoundBO extends Video116SysSoundBO{

    @Override
    public AudioJson116BO getSysSoundByName(String id, String name) {

        LambdaQueryWrapper<ProjSound116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSound116::getSoundName, name).last("limit 1");
        IProjSound116Service sound116Service = SpringUtil.getBean(IProjSound116Service.class);
        FileService fileService = SpringUtil.getBean(FileService.class);
        ProjSound116 sound = sound116Service.getOne(queryWrapper);
        if (sound == null) {
            throw new BizException("System sound '" + id + "' not find!");
        }
        String soundUrl = sound.getUrl();
        if (StringUtils.isBlank(soundUrl)) {
            throw new BizException("System sound '" + id + "' not set!");
        }
//        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        String maleName = FireBaseUrlSubUtils.getFileName(soundUrl);
        Integer duration = sound.getDuration();
        return new AudioJson116BO(
                id,
                fileService.getAbsoluteR2Url(soundUrl),
                maleName,
                BigDecimal.ZERO,
                duration,
                false,
                sound.getId(),
                sound.getNeedTranslation(),
                sound.getGender(),
                sound.getSoundScript(),
                sound.getCoreVoiceConfigI18nId()
        );
    }
}
