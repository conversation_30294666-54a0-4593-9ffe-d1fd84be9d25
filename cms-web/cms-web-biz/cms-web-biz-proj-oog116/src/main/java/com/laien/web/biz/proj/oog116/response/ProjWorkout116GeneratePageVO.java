package com.laien.web.biz.proj.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * note: template116 generate 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template116 generate 分页", description = "template116 generate 分页")
public class ProjWorkout116GeneratePageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制 生成条件")
    private String restrictionOption;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String restriction;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 从image 获取
     */
    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "video list")
    private List<ProjWorkout116GenerateVideoVO> videoList;


    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;
}
