package com.laien.web.biz.proj.oog116.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjTemplate116Task对象", description="proj_template116")
public class ProjTemplate116TaskReq {


    @ApiModelProperty(value = "template id")
    private Integer projTemplate116Id;

    @ApiModelProperty(value = "是否需要清理已生成的video 0 否，1是")
    private Integer cleanUp;

}
