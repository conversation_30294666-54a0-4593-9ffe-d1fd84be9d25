package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116Rule;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;

import java.util.List;

/**
 * <p>
 * proj_template116_rule 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjTemplate116RuleService extends IService<ProjTemplate116Rule> {

    List<ProjTemplate116RuleVO> findByProjTemplate116Id(Integer projTemplate116Id);
}
