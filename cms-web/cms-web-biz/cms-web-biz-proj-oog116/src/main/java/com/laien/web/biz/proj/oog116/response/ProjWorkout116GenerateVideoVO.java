package com.laien.web.biz.proj.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * note: template116 generate video list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "template116 generate video list", description = "template116 generate video list")
public class ProjWorkout116GenerateVideoVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频类型Warm Up/Cool Down/Main")
    private String type;

    @ApiModelProperty(value = "部位Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制,多个用英文逗号分隔，取值Shoulder, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>;")
    private String restriction;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontDuration;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "Guidance音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "Instructions的音频")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "met,1-12的整数")
    private Integer met;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    /**
     * rule相关
     */
    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "warm_up、main、cool_down")
    private String videoType;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "播放循环次数")
    private Integer rounds;

}
