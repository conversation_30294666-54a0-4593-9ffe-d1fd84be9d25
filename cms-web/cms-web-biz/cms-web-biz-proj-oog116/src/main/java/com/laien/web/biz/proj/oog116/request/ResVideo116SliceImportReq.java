package com.laien.web.biz.proj.oog116.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * Author:  hhl
 * Date:  2024/9/24 15:25
 */
@Data
public class ResVideo116SliceImportReq {

    @JsonIgnore
    @ApiModelProperty(value = "res video")
    @ExcelIgnore
    private ResVideo116 resVideo;

    @Min(value = 0, message = "The Sort can't be less than 0.", groups = Group2.class)
    @NotNull(message = "Sort cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Sort")
    @ApiModelProperty(value = "切片索引，1，2，3，4...")
    private Integer sliceIndex;

    @NotBlank(message = "Name cannot be blank", groups = Group1.class)
    @Length(message = "The Video Name cannot exceed 100 characters", min = 1, max = 100, groups = Group2.class)
    @ExcelProperty(value = "Name", converter = StringStringTrimConverter.class)
    private String name;

    @NotBlank(message = "ExerciseType cannot be blank", groups = Group1.class)
    @ExcelProperty(value = "ExerciseType", converter = StringStringTrimConverter.class)
    private String exerciseType;

    @NotBlank(message = "Position cannot be blank", groups = Group1.class)
    @ExcelProperty(value = "Position", converter = StringStringTrimConverter.class)
    private String position;

    @NotBlank(message = "Front Video Url cannot be blank", groups = Group1.class)
    @ExcelProperty(value = "Front Video Url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @Min(value = 1, message = "The Front Video Duration can't be less than 1.", groups = Group2.class)
    @NotNull(message = "Front Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Front Video Duration")
    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @NotBlank(message = "Side Video Url cannot be blank", groups = Group1.class)
    @ExcelProperty(value = "Side Video Url", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @Min(value = 1, message = "The Side Video Duration can't be less than 1.", groups = Group2.class)
    @NotNull(message = "Side Video Duration cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Side Video Duration")
    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "性别，Female/Male")
    @NotBlank(message = "性别不能为空", groups = Group1.class)
    @Pattern(
            regexp = "Female|Male",
            message = "性别必须是 Female、Male 之一",
            groups = Group2.class
    )
    @ExcelProperty(value = "Gender", converter = StringStringTrimConverter.class)
    private String gender;

}
