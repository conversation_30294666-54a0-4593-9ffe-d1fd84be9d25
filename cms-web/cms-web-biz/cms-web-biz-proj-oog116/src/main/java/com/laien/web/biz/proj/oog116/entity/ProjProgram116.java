package com.laien.web.biz.proj.oog116.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjProgram116对象", description="proj program 116")
public class ProjProgram116 extends BaseModel implements CoreI18nModel {

    @ApiModelProperty(value = "动作展示名称")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "描述")
    @TranslateField
    private String description;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "equipment")
    @TranslateField
    private String equipment;

    @ApiModelProperty(value = "goal选项，可填写多个，英文逗号分隔")
    @TranslateField
    private String goals;

    @ApiModelProperty(value = "教练Id")
    private Integer coachId;

    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费, 1是；0否")
    private Integer subscription;

}
