package com.laien.web.biz.proj.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjTemplate116Rule对象", description="proj_template116_rule")
public class ProjTemplate116RuleVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "warm_up、main、cool_down")
    private String videoType;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "播放循环次数")
    private Integer rounds;

}
