package com.laien.web.biz.proj.oog116.request;

import com.laien.common.oog116.enums.Category116TypeEnums;
import com.laien.common.oog116.enums.Gender116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: category116 新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "category116 新增", description = "category116 新增")
public class ProjCategory116AddReq {

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "展示类型")
    private String showType;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "workout list")
    private List<ProjCategory116AddWorkoutReq> workoutList;

    @ApiModelProperty(value = "icon url")
    private String iconUrl;

    @ApiModelProperty(value = "性别")
    private Gender116Enums gender;

    @ApiModelProperty(value = "type")
    private Category116TypeEnums type;


}
