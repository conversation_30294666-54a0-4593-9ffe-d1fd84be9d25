package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.request.ProjSound116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjSound116PageReq;
import com.laien.web.biz.proj.oog116.request.ProjSound116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjSound116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjSound116PageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;


public interface IProjSound116Service extends IService<ProjSound116> {
    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    PageRes<ProjSound116PageVO> selectSoundPage(ProjSound116PageReq req);

    /**
     * 添加 sound
     *
     * @param req
     */
    void saveSound(ProjSound116AddReq req);

    /**
     * 修改 sound
     *
     * @param req
     */
    void updateSound(ProjSound116UpdateReq req);

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    ProjSound116DetailVO getDetail(Integer id);

    /**
     * 批量启用
     * @param idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     * @param idList
     */
    void updateDisableByIds(List<Integer> idList);
}