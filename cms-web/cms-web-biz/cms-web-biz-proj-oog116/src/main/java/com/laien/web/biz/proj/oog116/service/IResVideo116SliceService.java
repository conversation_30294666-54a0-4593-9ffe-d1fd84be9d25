package com.laien.web.biz.proj.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog116.entity.ResVideo116Slice;
import com.laien.web.biz.proj.oog116.request.ResVideo116SliceImportReq;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2024/9/23 18:02
 */
public interface IResVideo116SliceService extends IService<ResVideo116Slice> {

    void deleteByResVideoId(Integer resVideoId);

    List<ResVideo116SliceDetailVO> listByResVideoId(Collection<Integer> resVideoIds);

    void videoDurationCount(Collection<Integer> resVideoIds, Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap);

    void saveBatch(List<ResVideo116SliceImportReq> sliceList);

    void updateBatch(List<ResVideo116SliceImportReq> sliceList);
}
