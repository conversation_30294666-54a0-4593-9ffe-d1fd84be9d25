package com.laien.web.biz.proj.oog116.controller;


import com.laien.web.biz.proj.oog116.entity.ProjCategory116;
import com.laien.web.biz.proj.oog116.request.ProjCategory116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjCategory116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjCategory116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjCategory116ListVO;
import com.laien.web.biz.proj.oog116.service.IProjCategory116Service;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * template116 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Api(tags = "项目管理:category 116")
@RestController
@RequestMapping("/proj/category116")
public class ProjCategory116Controller extends ResponseController {

    @Resource
    private IProjCategory116Service projCategory116Service;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjCategory116ListVO>> list() {
        List<ProjCategory116ListVO> list = projCategory116Service.selectCategoryList();
        return succ(list);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjCategory116AddReq category116AddReq) {
        projCategory116Service.saveCategory(category116AddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjCategory116UpdateReq category116UpdateReq) {
        projCategory116Service.updateCategory(category116UpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjCategory116DetailVO> detail(@PathVariable Integer id) {
        ProjCategory116DetailVO detailVO = projCategory116Service.getCategoryDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCategory116Service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCategory116Service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCategory116Service.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projCategory116Service.saveSort(idList);
        return succ();
    }

}
