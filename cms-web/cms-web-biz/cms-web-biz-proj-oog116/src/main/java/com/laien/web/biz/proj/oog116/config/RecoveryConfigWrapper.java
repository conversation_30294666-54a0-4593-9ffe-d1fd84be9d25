package com.laien.web.biz.proj.oog116.config;

import com.google.common.collect.Lists;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.web.biz.proj.oog116.bo.AudioJson116BO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
public class RecoveryConfigWrapper {

    private RecoveryConfig femaleSoundConfig;
    private RecoveryConfig maleSoundConfig;

    public List<AudioJson116BO> getStartAudioList(String gender) {

        RecoveryConfig recoveryConfig = Objects.equals(gender, Gender116Enums.FEMALE.getName()) ? femaleSoundConfig : maleSoundConfig;
        List<AudioJson116BO> audioJsonBOList = Lists.newArrayList();
        for (int i = 0; i < recoveryConfig.getStartList().size(); i++) {
            AudioJson116BO firstAudioFemale = getByName(recoveryConfig.getStartList().get(i));
            audioJsonBOList.add(firstAudioFemale);
        }
        return audioJsonBOList;
    }

    public AudioJson116BO getFirstAudio(String gender) {

        String soundName = Objects.equals(gender, Gender116Enums.MALE.getName()) ? maleSoundConfig.getFirst() : femaleSoundConfig.getFirst();
        return getByName(soundName);
    }

    public AudioJson116BO getNextAudio(String gender) {

        String soundName = Objects.equals(gender, Gender116Enums.MALE.getName()) ? maleSoundConfig.getNext() : femaleSoundConfig.getNext();
        return getByName(soundName);
    }

    public AudioJson116BO getLastAudio(String gender) {

        String soundName = Objects.equals(gender, Gender116Enums.MALE.getName()) ? maleSoundConfig.getLast() : femaleSoundConfig.getLast();
        return getByName(soundName);
    }

    private AudioJson116BO getByName(String soundName) {

        AudioJson116BO audioJson116BO = maleSoundConfig.getSysSoundByName(soundName, soundName);
        audioJson116BO.setTime(new BigDecimal(1));
        return audioJson116BO;
    }

}
