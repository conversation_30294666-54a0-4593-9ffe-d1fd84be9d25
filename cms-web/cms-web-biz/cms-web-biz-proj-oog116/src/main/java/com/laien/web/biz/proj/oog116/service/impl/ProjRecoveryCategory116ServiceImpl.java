package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.entity.ProjRecoveryCategory116;
import com.laien.web.biz.proj.oog116.entity.ProjRecoveryCategory116ProjWorkout116;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116;
import com.laien.web.biz.proj.oog116.mapper.ProjRecoveryCategory116Mapper;
import com.laien.web.biz.proj.oog116.request.ProjRecoveryCategory116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjRecoveryCategory116AddWorkoutReq;
import com.laien.web.biz.proj.oog116.request.ProjRecoveryCategory116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116DetailWorkoutVO;
import com.laien.web.biz.proj.oog116.response.ProjRecoveryCategory116ListVO;
import com.laien.web.biz.proj.oog116.service.IProjRecoveryCategory116ProjWorkout116Service;
import com.laien.web.biz.proj.oog116.service.IProjRecoveryCategory116Service;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Service;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_recovery_category116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
@Slf4j
public class ProjRecoveryCategory116ServiceImpl extends ServiceImpl<ProjRecoveryCategory116Mapper, ProjRecoveryCategory116>
        implements IProjRecoveryCategory116Service {

    @Resource
    private IProjRecoveryCategory116ProjWorkout116Service projRecoveryCategory116ProjWorkout116Service;
    @Resource
    private IProjLmsI18nService projLmsI18nService;
    @Resource
    private IProjInfoService projInfoService;
    @Resource
    private IProjWorkout116Service projWorkout116Service;

    @Override
    public List<ProjRecoveryCategory116ListVO> selectCategoryList() {
        Integer projId = RequestContextUtils.getProjectId();
        LambdaQueryWrapper<ProjRecoveryCategory116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjRecoveryCategory116::getProjId, projId);
        queryWrapper.orderByAsc(ProjRecoveryCategory116::getSortNo);
        queryWrapper.orderByDesc(ProjRecoveryCategory116::getId);

        // 查询
        List<ProjRecoveryCategory116> list = this.list(queryWrapper);
        List<ProjRecoveryCategory116ListVO> copyList = new ArrayList<>(list.size());
        if (list.isEmpty()) {
            return copyList;
        }
        Map<Integer, List<IdAndStatusCountsRes>> workoutStatusCountMap = projRecoveryCategory116ProjWorkout116Service.selectWorkoutStatusCount(projId)
                .stream().collect(Collectors.groupingBy(IdAndStatusCountsRes::getId));

        for (ProjRecoveryCategory116 category : list) {
            ProjRecoveryCategory116ListVO listVO = new ProjRecoveryCategory116ListVO();
            BeanUtils.copyProperties(category, listVO);

            List<IdAndStatusCountsRes> statusCountsList = workoutStatusCountMap.get(category.getId());
            if (CollectionUtils.isNotEmpty(statusCountsList)) {
                int totalCount = statusCountsList.stream().mapToInt(IdAndStatusCountsRes::getCounts).sum();
                int enableCount = statusCountsList.stream()
                        .filter(item -> Objects.equals(item.getStatus(), GlobalConstant.STATUS_ENABLE))
                        .mapToInt(IdAndStatusCountsRes::getCounts).sum();
                listVO.setTotalCount(totalCount);
                listVO.setEnableCount(enableCount);
            } else {
                listVO.setTotalCount(0);
                listVO.setEnableCount(0);
            }
            copyList.add(listVO);
        }
        return copyList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCategory(ProjRecoveryCategory116AddReq addReq) {
        // 检查Category
        this.checkCategory(addReq, null);

        // 保存
        ProjRecoveryCategory116 category = new ProjRecoveryCategory116();
        BeanUtils.copyProperties(addReq, category);
        category.setSortNo(GlobalConstant.ZERO);
        category.setProjId(RequestContextUtils.getProjectId());
        category.setStatus(GlobalConstant.STATUS_DRAFT);
        category.setEventName("");
        this.save(category);
        //update eventName,调用生成videoUrl生成m3u
        LambdaUpdateWrapper<ProjRecoveryCategory116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjRecoveryCategory116::getEventName, category.getId()+"+"+category.getName())
                .eq(ProjRecoveryCategory116::getId, category.getId());
        update(category, wrapper);
        //进行翻译任务
        projLmsI18nService.handleI18n(ListUtil.of(category), projInfoService.getById(RequestContextUtils.getProjectId()));
        // 保存关联
        this.saveRelation(category.getId(), addReq.getWorkoutList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCategory(ProjRecoveryCategory116UpdateReq updateReq) {
        Integer id = updateReq.getId();
        ProjRecoveryCategory116 categoryFind = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(categoryFind), "Data not found");

        // 检查Category
        this.checkCategory(updateReq, id);
        // 保存
        ProjRecoveryCategory116 category = new ProjRecoveryCategory116();
        BeanUtils.copyProperties(updateReq, category);
        this.updateById(category);
        projLmsI18nService.handleI18n(ListUtil.of(category), projInfoService.getById(RequestContextUtils.getProjectId()));
        // 删除之前的关联
        this.deleteRelation(id);
        // 保存新的关联
        this.saveRelation(id, updateReq.getWorkoutList());
    }

    @Override
    public ProjRecoveryCategory116DetailVO getCategoryDetail(Integer id) {
        ProjRecoveryCategory116 categoryFind = this.getById(id);
        if (Objects.isNull(categoryFind)) {
            throw new BizException("Data not found");
        }
        ProjRecoveryCategory116DetailVO detailVO = new ProjRecoveryCategory116DetailVO();
        BeanUtils.copyProperties(categoryFind, detailVO);

        List<ProjRecoveryCategory116DetailWorkoutVO> workoutList = projRecoveryCategory116ProjWorkout116Service.selectWorkoutByCategoryId(id);
        detailVO.setWorkoutList(workoutList);

        return detailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjRecoveryCategory116> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjRecoveryCategory116::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjRecoveryCategory116::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjRecoveryCategory116> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjRecoveryCategory116::getStatus, GlobalConstant.STATUS_DISABLE)
                .in(ProjRecoveryCategory116::getId, idList);
        this.update(updateWrapper);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        this.removeByIds(idList);
        // 删除关联关系
        for (Integer id : idList) {
            this.deleteRelation(id);
        }
    }

    @Override
    public void saveSort(List<Integer> idList) {
        for (int i = 0; i < idList.size(); i++) {
            LambdaUpdateWrapper<ProjRecoveryCategory116> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjRecoveryCategory116::getSortNo, i)
                    .eq(ProjRecoveryCategory116::getId, idList.get(i));
            this.update(updateWrapper);
        }
    }

    /**
     * 检查Category
     */
    private void checkCategory(ProjRecoveryCategory116AddReq addReq, Integer excludeId) {
        Integer projId = RequestContextUtils.getProjectId();
        LambdaQueryWrapper<ProjRecoveryCategory116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjRecoveryCategory116::getName, addReq.getName())
                .eq(ProjRecoveryCategory116::getProjId, projId);
        if (Objects.nonNull(excludeId)) {
            queryWrapper.ne(ProjRecoveryCategory116::getId, excludeId);
        }
        ProjRecoveryCategory116 categoryFind = this.getOne(queryWrapper);
        if (Objects.nonNull(categoryFind)) {
            throw new BizException("Category name already exists");
        }
        Set<Object> workoutIds = addReq.getWorkoutList().stream().map(ProjRecoveryCategory116AddWorkoutReq::getId).collect(Collectors.toSet());
        //检查是否都是recovery workout
        LambdaQueryWrapper<ProjWorkout116> queryWorkoutWrapper = new LambdaQueryWrapper<>();
        queryWorkoutWrapper.in(ProjWorkout116::getId, workoutIds)
                .eq(ProjWorkout116::getProjId, projId)
                .eq(ProjWorkout116::getExerciseType, ExerciseType116Enums.RECOVERY.getName());
        if (projWorkout116Service.count(queryWorkoutWrapper) != workoutIds.size()) {
            throw new BizException("Workout is not recovery workout");
        }
    }

    /**
     * 保存关联关系
     */
    private void saveRelation(Integer categoryId, List<ProjRecoveryCategory116AddWorkoutReq> workoutList) {
        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }
        List<ProjRecoveryCategory116ProjWorkout116> relationList = new ArrayList<>();
        for (ProjRecoveryCategory116AddWorkoutReq workoutReq : workoutList) {
            ProjRecoveryCategory116ProjWorkout116 relation = new ProjRecoveryCategory116ProjWorkout116();
            relation.setProjRecoveryCategory116Id(categoryId);
            relation.setProjWorkout116Id(workoutReq.getId());
            relationList.add(relation);
        }
        projRecoveryCategory116ProjWorkout116Service.saveBatch(relationList);
    }

    /**
     * 删除关联关系
     */
    private void deleteRelation(Integer categoryId) {
        LambdaQueryWrapper<ProjRecoveryCategory116ProjWorkout116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjRecoveryCategory116ProjWorkout116::getProjRecoveryCategory116Id, categoryId);
        projRecoveryCategory116ProjWorkout116Service.remove(queryWrapper);
    }
}
