package com.laien.web.biz.proj.oog116.validation;

import com.laien.web.biz.proj.oog116.validation.validator.ResVideo116RestrictionsValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = ResVideo116RestrictionsValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ResVideo116Restrictions {
    String message();

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
