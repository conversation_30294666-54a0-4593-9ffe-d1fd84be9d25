package com.laien.web.biz.proj.oog116.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog116.entity.ProjSound116;
import com.laien.web.biz.proj.oog116.request.ProjSound116AddReq;
import com.laien.web.biz.proj.oog116.request.ProjSound116UpdateReq;
import com.laien.web.biz.proj.oog116.response.ProjSound116DetailVO;
import com.laien.web.biz.proj.oog116.response.ProjSound116PageVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSound116MapStruct {

    List<ProjSound116PageVO> toPageList(List<ProjSound116> entities);

    /**
     * 添加 request 转 entity
     * @param req
     * @return
     */
    ProjSound116 toEntity(ProjSound116AddReq req);

    /**
     * 修改 request 转 entity
     * @param req
     * @return
     */
    ProjSound116 toEntity(ProjSound116UpdateReq req);

    /**
     * entity 转详情
     * @param entity
     * @return
     */
    ProjSound116DetailVO toDetailVO(ProjSound116 entity);
}