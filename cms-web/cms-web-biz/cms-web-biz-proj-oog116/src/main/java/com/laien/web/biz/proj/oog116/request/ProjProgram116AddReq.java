package com.laien.web.biz.proj.oog116.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116PageVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Data
public class ProjProgram116AddReq {

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "equipment")
    private String equipment;

    @ApiModelProperty(value = "goal选项，可填写多个，英文逗号分隔")
    private String goals;

    @ApiModelProperty(value = "教练Id")
    private Integer coachId;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费, 1是；0否")
    private Integer subscription;

    @ApiModelProperty(value = "workout116 list")
    private List<ProjWorkout116PageVO> workoutList;
}
