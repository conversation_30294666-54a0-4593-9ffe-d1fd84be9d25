package com.laien.web.biz.proj.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/5/15
 */
@Data
public class ProjProgram116DetailVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "视频图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "equipment")
    private String equipment;

    @ApiModelProperty(value = "goal选项，可填写多个，英文逗号分隔")
    private String goals;

    @ApiModelProperty(value = "教练Id")
    private Integer coachId;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "是否收费, 1是；0否")
    private Integer subscription;

    @ApiModelProperty(value = "program 下的 workout 列表")
    private List<ProjWorkout116PageVO> workoutList;

    @ApiModelProperty(value = "教练信息")
    private ProjCoach116VO coach;

}
