package com.laien.web.biz.proj.oog116.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog116.enums.RecoveryCategory116ShowTypeEnums;
import com.laien.common.oog116.enums.RecoveryCategory116TypeEnums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p>
 * proj_recovery_category116
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjRecoveryCategory116对象", description="proj_recovery_category116")
@TableName(autoResultMap = true)
public class ProjRecoveryCategory116 extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称", required = true)
    @TranslateField
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "Event Name (ID+Workout Name)", required = true)
    @NotBlank(message = "Event Name不能为空")
    private String eventName;

    @ApiModelProperty(value = "封面图 (Png/Webp)")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图 (Png/Webp)")
    private String detailImgUrl;

    @ApiModelProperty(value = "图标 (Png/Webp)")
    private String iconUrl;

    @ApiModelProperty(value = "展示类型", required = true)
    @NotNull(message = "展示类型不能为空")
    private RecoveryCategory116ShowTypeEnums showType;

    @ApiModelProperty(value = "recoveryCategoryType", required = true)
    @NotNull(message = "recoveryCategoryType不能为空")
    @TableField(typeHandler = RecoveryCategory116TypeEnums.TypeHandler.class)
    private List<RecoveryCategory116TypeEnums> type;

    @ApiModelProperty(value = "描述 (最多1000字符)")
    @TranslateField
    @Size(max = 1000, message = "描述不能超过1000字符")
    private String description;

    @ApiModelProperty(value = "排序编号", required = true)
    @NotNull(message = "排序编号不能为空")
    private Integer sortNo;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "项目id", required = true)
    @NotNull(message = "项目id不能为空")
    private Integer projId;
}
