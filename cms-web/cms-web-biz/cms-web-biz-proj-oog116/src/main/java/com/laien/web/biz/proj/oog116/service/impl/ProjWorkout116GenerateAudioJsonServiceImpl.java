package com.laien.web.biz.proj.oog116.service.impl;

import com.laien.web.biz.proj.oog116.entity.ProjWorkout116GenerateAudioJson;
import com.laien.web.biz.proj.oog116.mapper.ProjWorkout116GenerateAudioJsonMapper;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116GenerateAudioJsonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 116生成的workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Service
public class ProjWorkout116GenerateAudioJsonServiceImpl extends ServiceImpl<ProjWorkout116GenerateAudioJsonMapper, ProjWorkout116GenerateAudioJson> implements IProjWorkout116GenerateAudioJsonService {

}
