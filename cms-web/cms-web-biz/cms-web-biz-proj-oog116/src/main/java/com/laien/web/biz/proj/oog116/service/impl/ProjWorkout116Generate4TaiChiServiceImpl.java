package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.oog116.enums.*;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog116.bo.*;
import com.laien.web.biz.proj.oog116.config.Oog116BizConfig;
import com.laien.web.biz.proj.oog116.config.TaiChiSoundConfigBOWrapper;
import com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116Generate;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116GenerateI18n;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.i18n.ProjResVideo116I18n;
import com.laien.web.biz.proj.oog116.exception.AutoGenerateException;
import com.laien.web.biz.proj.oog116.response.ProjTemplate116RuleVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Generate4TaiChiService;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116GenerateService;
import com.laien.web.biz.resource.entity.ResImage;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.Gender116Enums.FEMALE;
import static com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum.*;
import static com.laien.web.frame.constant.GlobalConstant.SECOND_MILL;

/**
 * Author:  hhl
 * Date:  2025/3/4 15:59
 */
@Slf4j
@Service
public class ProjWorkout116Generate4TaiChiServiceImpl implements IProjWorkout116Generate4TaiChiService {

    @Resource
    private Oog116BizConfig oog116BizConfig;

    @Resource
    private IProjWorkout116GenerateService workout116GenerateService;

    @Resource
    private FileService fileService;

    private static final int PREVIEW_CIRCUIT = 1;

    @Override
    public List<ProjWorkout116GenerateBO> generateWorkoutByPlan(ExerciseType116Enums exerciseType,
                                                                Equipment116Enums equipment,
                                                                List<Restriction116Enums> restrictionList,
                                                                Position116Enums position,
                                                                ProjWorkout116ContextBO context) {

        // 目前TaiChi没有Seated的动作，无需生成
        if (!exerciseType.getPositionList().contains(position) || !exerciseType.getEquipmentList().contains(equipment)) {
            return Collections.emptyList();
        }

        // 目前只有female的动作
        Integer femaleDay = exerciseType.getFemaleDay();
        List<ProjWorkout116GenerateBO> femaleWorkoutList = generateWorkoutByDay(exerciseType, equipment, restrictionList, position, femaleDay, context, FEMALE);
        return femaleWorkoutList;
    }

    private List<ProjWorkout116GenerateBO> generateWorkoutByDay(ExerciseType116Enums exerciseType,
                                                                Equipment116Enums equipment,
                                                                List<Restriction116Enums> restrictionList,
                                                                Position116Enums position,
                                                                Integer day,
                                                                ProjWorkout116ContextBO context,
                                                                Gender116Enums gender) {

        List<ResImage> resImages = context.matchImageList(position, day, exerciseType, gender);
        List<ProjWorkout116GenerateBO> projWorkout116BOList = new ArrayList<>();
        for (int i = 0; i < day; i++) {

            ProjWorkout116GenerateBO projWorkout116BO = new ProjWorkout116GenerateBO();
            try {
                List<ProjWorkout116GenerateResVideo116BO> resVideo116BOList = selectVideo4Workout(context, restrictionList, position, equipment, exerciseType, gender);
                projWorkout116BO.setProjWorkout116GenerateResVideo116List(resVideo116BOList);
            } catch (AutoGenerateException e) {
                log.warn("generate tai chi workout failed, exercise type : {}, position : {}, gender : {}, restrictionList : {}.", exerciseType, position, gender, restrictionList);
                throw new BizException(String.format("generate tai chi workout failed."));
            }

            ProjWorkout116Generate workout116Generate = wrapWorkout(context, position, equipment, exerciseType, gender, resImages, i);
            projWorkout116BO.setProjWorkout116Generate(workout116Generate);
            projWorkout116BO.setRestrictionList(restrictionList);
            projWorkout116BOList.add(projWorkout116BO);
        }
        return projWorkout116BOList;
    }

    private ProjWorkout116Generate wrapWorkout(ProjWorkout116ContextBO context,
                                               Position116Enums position,
                                               Equipment116Enums equipment,
                                               ExerciseType116Enums exerciseType,
                                               Gender116Enums gender,
                                               List<ResImage> resImages,
                                               int day) {

        ProjWorkout116Generate projWorkout116 = new ProjWorkout116Generate();
        Integer projTemplate116Id = context.getProjTemplate116Id();
        Integer projTemplate116TaskId = context.getProjTemplate116TaskId();
        ResImage resImage = resImages.get(day % resImages.size());

        projWorkout116
                .setProjTemplate116Id(projTemplate116Id)
                .setProjTemplate116TaskId(projTemplate116TaskId)
                .setPosition(position.getName())
                .setResImageId(resImage.getId())
                .setEquipment(equipment.getName())
                .setExerciseType(exerciseType.getName())
                .setGender(gender.getName());
        return projWorkout116;
    }

    /**
     * 按照排列组合生成一天的workout
     * 先根据配置挑选warm up、cooldown
     * 再根据模板时间剪去已挑选视频时间，进行main视频的挑选
     * <p>
     * <p>
     * 生成workout、workout和video的关系，未入库
     */
    private List<ProjWorkout116GenerateResVideo116BO> selectVideo4Workout(ProjWorkout116ContextBO context,
                                                                          List<Restriction116Enums> restrictionList,
                                                                          Position116Enums position,
                                                                          Equipment116Enums equipment,
                                                                          ExerciseType116Enums exerciseType,
                                                                          Gender116Enums gender) {

        List<String> restrictionNameList = restrictionList.stream().map(Restriction116Enums::getName).collect(Collectors.toList());
        List<ProjWorkout116GenerateResVideo116BO> warmUpVideoList = assembleVideo(context, restrictionNameList, position, WARM_UP, equipment, exerciseType, gender);
        List<ProjWorkout116GenerateResVideo116BO> coolDownVideoList = assembleVideo(context, restrictionNameList, position, COOL_DOWN, equipment, exerciseType, gender);

        if (CollectionUtils.isEmpty(warmUpVideoList) || CollectionUtils.isEmpty(coolDownVideoList)) {
            log.warn("Can't generate workout for tai chi, no enough video, such as warmup , cooldown.");
            throw new AutoGenerateException("Can't generate workout for tai chi, no enough video, such as warmup , cooldown.");
        }

        List<Integer> durationRange = computeDurationRange4Main(context.getTemplate116(), warmUpVideoList, coolDownVideoList);
        List<ProjWorkout116GenerateResVideo116BO> mainVideoList = Lists.newArrayList();
        if (durationRange.get(0) > 0) {
            mainVideoList = assembleVideo4Main(context, durationRange.get(0), durationRange.get(1), restrictionNameList, position, MAIN, equipment, exerciseType, gender);
            if (CollectionUtils.isEmpty(mainVideoList)) {
                log.warn("Can't generate workout for tai chi, no enough video, such as main.");
                throw new AutoGenerateException("Can't generate workout for tai chi, no enough video, such as main.");
            }
        }

        List<ProjWorkout116GenerateResVideo116BO> workoutResVideoList = Lists.newLinkedList();
        workoutResVideoList.addAll(warmUpVideoList);
        workoutResVideoList.addAll(mainVideoList);
        workoutResVideoList.addAll(coolDownVideoList);

        computeDurationWithCircuit(context, workoutResVideoList);
        return workoutResVideoList;
    }

    @Override
    public Integer computeDurationWithCircuit(ProjWorkout116ContextBO context, List<ProjWorkout116GenerateResVideo116BO> workoutResVideoList) {

        AtomicBoolean front = new AtomicBoolean(true);
        AtomicInteger videoDuration = new AtomicInteger(0);
        workoutResVideoList.forEach(resVideo116 -> {

            // preview duration
            Integer previewDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                    context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, resVideo116.getResVideo116Id());
            resVideo116.setPreviewDuration(previewDuration);

            // circuit duration
            List<Integer> circuitDurationList = new ArrayList<>();
            for (int i = 0; i < resVideo116.getCircuit(); i++) {
                Integer circuitDuration = computeVideoDuration4Circuit(context.getVideoSliceDurationMap(),
                        context.getSideVideoSliceDurationMap(), context.getVideoSliceMap(), front, resVideo116.getResVideo116Id());
                circuitDurationList.add(circuitDuration);
                videoDuration.addAndGet(circuitDuration);
            }
            resVideo116.setCircuitVideoDuration(convertList2String(circuitDurationList));
        });

        return videoDuration.get();
    }

    private String convertList2String(List<Integer> circuitDurationList) {

        if (CollectionUtils.isEmpty(circuitDurationList)) {
            return "";
        }

        return StringUtils.joinWith(GlobalConstant.COMMA, circuitDurationList.toArray());
    }

    private Integer computeVideoDuration4Circuit(Map<Integer, Integer> frontDurationMap, Map<Integer, Integer> sideDurationMap,
                                                 Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap, AtomicBoolean front,
                                                 Integer video116Id) {

        int videoDuration = 0;
        if (front.get()) {
            videoDuration += frontDurationMap.get(video116Id);
        } else {
            videoDuration += sideDurationMap.get(video116Id);
        }

        if (videoSliceMap.get(video116Id).size() % 2 != 0) {
            if (front.get()) {
                front.set(false);
            } else {
                front.set(true);
            }
        }
        return videoDuration;
    }

    private List<Integer> computeDurationRange4Main(ProjTemplate116 template116,
                                                    List<ProjWorkout116GenerateResVideo116BO> warmUpVideoList,
                                                    List<ProjWorkout116GenerateResVideo116BO> cooldownVideoList) {

        int warmUpDuration = warmUpVideoList.stream().mapToInt(video -> video.getResVideoDuration() * video.getRuleRound() * (video.getCircuit() + PREVIEW_CIRCUIT)).sum();
        int coolDownDuration = cooldownVideoList.stream().mapToInt(video -> video.getResVideoDuration() * video.getRuleRound() * (video.getCircuit() + PREVIEW_CIRCUIT)).sum();

        String[] durationArray = template116.getDurationRange().split("-");
        int minDuration = convert2MillSecond(Integer.parseInt(durationArray[0]));
        int maxDuration = convert2MillSecond(Integer.parseInt(durationArray[1]));

        Integer mainMinDuration = minDuration - warmUpDuration - coolDownDuration;
        Integer mainMaxDuration = maxDuration - warmUpDuration - coolDownDuration;
        return Lists.newArrayList(mainMinDuration, mainMaxDuration);
    }

    private List<ProjWorkout116GenerateResVideo116BO> assembleVideo4Main(ProjWorkout116ContextBO context,
                                                                         Integer minDuration,
                                                                         Integer maxDuration,
                                                                         List<String> restrictionList,
                                                                         Position116Enums position,
                                                                         ResVideo116TypeEnum video116TypeEnum,
                                                                         Equipment116Enums equipment,
                                                                         ExerciseType116Enums exerciseType,
                                                                         Gender116Enums gender) {

        List<ResVideo116> videoList = context.getVideoList(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        Map<String, List<ResVideo116>> leftRightVideoMap = context.getLeftRightVideoMap(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        ProjTemplate116RuleVO rule = context.getRuleVideoTypeMap().get(video116TypeEnum.getValue()).get(0);

        Integer mainVideoRound = rule.getRounds();
        List<ResVideo116> matchVideoList = matchVideo4Duration(minDuration, maxDuration, mainVideoRound, Lists.newLinkedList(videoList), leftRightVideoMap, context.getVideoSliceDurationMap());
        return matchVideoList.stream().map(resVideo -> createProjWorkoutResVideoRelation(context, rule, resVideo)).collect(Collectors.toList());
    }

    private Integer convert2MillSecond(Integer minutes) {

        return (int) Duration.ofMinutes(minutes).toMillis();
    }

    private List<ProjWorkout116GenerateResVideo116BO> assembleVideo(ProjWorkout116ContextBO context,
                                                                    List<String> restrictionList,
                                                                    Position116Enums position,
                                                                    ResVideo116TypeEnum video116TypeEnum,
                                                                    Equipment116Enums equipment,
                                                                    ExerciseType116Enums exerciseType,
                                                                    Gender116Enums gender) {

        List<ResVideo116> videoList = context.getVideoList(video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);
        Map<String, List<ResVideo116>> leftRightVideoMap = context.getLeftRightVideoMap(
                video116TypeEnum.getValue(), restrictionList, position, equipment, exerciseType, gender);

        ProjTemplate116RuleVO rule = context.getRuleVideoTypeMap().get(video116TypeEnum.getValue()).get(0);
        List<ResVideo116> ruleVideoList = matchVideo(rule.getCount(), videoList, leftRightVideoMap);
        return ruleVideoList.stream().map(video -> createProjWorkoutResVideoRelation(context, rule, video)).collect(Collectors.toList());
    }

    private List<ResVideo116> matchVideo4Duration(int minDuration,
                                                  int maxDuration,
                                                  Integer mainVideoRound,
                                                  LinkedList<ResVideo116> videoList,
                                                  Map<String, List<ResVideo116>> leftRightVideoMap,
                                                  Map<Integer, Integer> videoSliceDurationMap) {

        if (CollectionUtils.isEmpty(videoList) && MapUtils.isEmpty(leftRightVideoMap)) {
            return Collections.emptyList();
        }

        int randomDuration4Main = randomDuration4Main(minDuration, maxDuration);
        AtomicInteger combineDuration4Main = new AtomicInteger(GlobalConstant.ZERO);
        List<ResVideo116> matchVideoList = Lists.newArrayList();

        Collections.shuffle(videoList);
        LinkedList<String> leftVideoNames = Lists.newLinkedList(leftRightVideoMap.keySet());
        Collections.shuffle(leftVideoNames);

        while (combineDuration4Main.get() < randomDuration4Main) {

            // 资源不够
            if (CollectionUtils.isEmpty(videoList) && CollectionUtils.isEmpty(leftVideoNames)) {
                if (combineDuration4Main.get() < minDuration) {
                    log.warn("Can't generate workout for tai chi, no enough main video.");
                    matchVideoList = Collections.emptyList();
                }
                break;
            }

            // 挑选正向
            if (RandomUtil.randomBoolean() && CollectionUtils.isNotEmpty(videoList)) {
                ResVideo116 resVideo116 = videoList.pollFirst();

                int videoDuration = videoSliceDurationMap.get(resVideo116.getId()) * (resVideo116.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;
                if (combineDuration4Main.get() + videoDuration > maxDuration) {
                    continue;
                }

                matchVideoList.add(resVideo116);
                combineDuration4Main.addAndGet(videoDuration);
                continue;
            }

            // 挑选Left、Right
            if (CollectionUtils.isNotEmpty(leftVideoNames)) {

                String leftVideoName = leftVideoNames.pollFirst();
                // 包含left、right两个video
                List<ResVideo116> resVideo116List = leftRightVideoMap.get(leftVideoName);

                ResVideo116 leftVideo = resVideo116List.get(0);
                int leftVideoDuration = videoSliceDurationMap.get(leftVideo.getId()) * (leftVideo.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;
                ResVideo116 rightVideo = resVideo116List.get(1);
                int rightVideoDuration = videoSliceDurationMap.get(rightVideo.getId()) * (rightVideo.getCircuit() + PREVIEW_CIRCUIT) * mainVideoRound;

                if (combineDuration4Main.get() + leftVideoDuration + rightVideoDuration > maxDuration) {
                    continue;
                }

                matchVideoList.add(leftVideo);
                matchVideoList.add(rightVideo);
                combineDuration4Main.addAndGet(leftVideoDuration);
                combineDuration4Main.addAndGet(rightVideoDuration);
            }
        }

        return matchVideoList;
    }

    private int randomDuration4Main(int minDuration, int maxDuration) {

        List<Float> floats = Lists.newArrayList(0.1f, 0.3f, 0.5f, 0.7f);
        Collections.shuffle(floats);
        int bufferDuration = maxDuration - minDuration;
        return minDuration + (int) (bufferDuration * floats.get(GlobalConstant.ZERO));
    }

    /**
     * 按照规则设置的数量匹配video
     *
     * @param count             当前规则需要匹配的video数量(最终ruleVideoList的size必须等于count)
     * @param videoList         当前规则可匹配的video
     * @param leftRightVideoMap 当前规则可匹配的left和right的video
     */
    private List<ResVideo116> matchVideo(int count,
                                         List<ResVideo116> videoList,
                                         Map<String, List<ResVideo116>> leftRightVideoMap) {
        if (count <= 0) {
            throw new BizException("generate warm up or cool down or main failed for tai chi.");
        }

        if (CollectionUtils.isEmpty(videoList) && MapUtils.isEmpty(leftRightVideoMap)) {
            throw new AutoGenerateException("generate warm up or cool down or main failed for tai chi.");
        }
        leftRightVideoMap = null == leftRightVideoMap ? new HashMap<>() : leftRightVideoMap;

        List<String> leftRightVideoKeyList = new ArrayList<>(leftRightVideoMap.keySet());
        Collections.shuffle(leftRightVideoKeyList);

        Collections.shuffle(videoList);
        List<ResVideo116> ruleVideoList = new ArrayList<>();
        int cycles = count * 20;
        while (cycles > 0 && ruleVideoList.size() < count) {
            cycles--;

            int videoSize = videoList.size();
            if ((count - ruleVideoList.size() >= 2 && !leftRightVideoKeyList.isEmpty())
                    && (CollectionUtils.isEmpty(videoList) || (RandomUtil.randomBoolean()) || (videoSize == 1 && NumberUtil.isEven(count - ruleVideoList.size())))) {
                // 包含left和right的
                List<ResVideo116> video116List = leftRightVideoMap.get(leftRightVideoKeyList.remove(0));
                ruleVideoList.addAll(video116List);
                continue;
            }
            if (!videoList.isEmpty()) {
                ruleVideoList.add(videoList.remove(0));
            }
        }
        if (ruleVideoList.size() != count) {
            log.warn("generate warm up or cool down or main failed,match size not equals rule count");
            throw new AutoGenerateException("generate warm up or cool down or main failed");
        }
        return ruleVideoList;
    }

    private ProjWorkout116GenerateResVideo116BO createProjWorkoutResVideoRelation(ProjWorkout116ContextBO context,
                                                                                  ProjTemplate116RuleVO item,
                                                                                  ResVideo116 resVideo116) {

        ProjWorkout116GenerateResVideo116BO projWorkoutResVideo = new ProjWorkout116GenerateResVideo116BO();
        projWorkoutResVideo
                .setRuleVO(item)
                .setRuleRound(item.getRounds())
                .setProjTemplate116Id(context.getProjTemplate116Id())
                .setProjTemplate116RuleId(item.getId())
                .setResVideo116Id(resVideo116.getId())
                .setProjTemplate116TaskId(context.getProjTemplate116TaskId());

        projWorkoutResVideo.setResVideo116(resVideo116);
        projWorkoutResVideo.setCircuit(resVideo116.getCircuit());

        Integer videoDuration = context.getVideoSliceDurationMap().get(resVideo116.getId());
        projWorkoutResVideo.setResVideoDuration(videoDuration);
        return projWorkoutResVideo;
    }

    @Override
    public TaiChiGenerateSoundBO createTaiChiSoundBO(List<String> languageList) {

        // 系统音获取并验证是否完整(系统音不区分语言)
        TaiChiSoundConfigBOWrapper taiChiSoundConfigBOWrapper = oog116BizConfig.getTaichi();
        AudioJson116AllBO firstAudio_en = taiChiSoundConfigBOWrapper.getFirstAudio();
        AudioJson116AllBO nextAudio_en = taiChiSoundConfigBOWrapper.getNextAudio();
        AudioJson116AllBO lastAudio_en = taiChiSoundConfigBOWrapper.getLastAudio();

        TaiChiGenerateSoundBO taiChiGenerateSoundBO = new TaiChiGenerateSoundBO();
        taiChiGenerateSoundBO.setFirstAudio(firstAudio_en.getFemaleAudioJson116BO());
        taiChiGenerateSoundBO.setNextAudio(nextAudio_en.getFemaleAudioJson116BO());
        taiChiGenerateSoundBO.setLastAudio(lastAudio_en.getFemaleAudioJson116BO());

        List<AudioJson116BO> promptAudioEnList4Second = taiChiSoundConfigBOWrapper.getAudioListByPromptList4Second();
        taiChiGenerateSoundBO.setPromptAudioList4Second(promptAudioEnList4Second);

        List<AudioJson116BO> startAudioEnList4First = taiChiSoundConfigBOWrapper.getAudioListByStartList4First();
        List<AudioJson116BO> startAudioEnList4Second = taiChiSoundConfigBOWrapper.getAudioListByStartList4Second();
        taiChiGenerateSoundBO.setStartAudioList4First(startAudioEnList4First);
        taiChiGenerateSoundBO.setStartAudioList4Second(startAudioEnList4Second);

        // 获取系统音多语言
        List<AudioJson116BO> soundList = Lists.newArrayList(firstAudio_en.getFemaleAudioJson116BO(), nextAudio_en.getFemaleAudioJson116BO(), lastAudio_en.getFemaleAudioJson116BO());
        soundList.addAll(promptAudioEnList4Second);
        soundList.addAll(startAudioEnList4First);
        soundList.addAll(startAudioEnList4Second);

        Map<Integer, Map<String, AudioJson116BO>> audioJson116I18nMap = workout116GenerateService.getSoundI18n(soundList, languageList);

        // 根据语言匹配对应的音频
        Map<String, TaiChiGenerateSoundBO> languageAudioMap = Maps.newHashMap();
        languageAudioMap.put(GlobalConstant.DEFAULT_LANGUAGE, taiChiGenerateSoundBO);
        languageList.stream().filter(language -> !Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)).forEach(language -> {
            //只有女声
            AudioJson116BO firstAudio = audioJson116I18nMap.get(firstAudio_en.getFemaleAudioJson116BO().getSoundId()).get(language);
            AudioJson116BO nextAudio = audioJson116I18nMap.get(nextAudio_en.getFemaleAudioJson116BO().getSoundId()).get(language);
            AudioJson116BO lastAudio = audioJson116I18nMap.get(lastAudio_en.getFemaleAudioJson116BO().getSoundId()).get(language);

            List<AudioJson116BO> promptAudioList4Second = promptAudioEnList4Second.stream().map(audio -> audioJson116I18nMap.get(audio.getSoundId()).get(language)).collect(Collectors.toList());
            List<AudioJson116BO> startAudioList4First = startAudioEnList4First.stream().map(audio -> audioJson116I18nMap.get(audio.getSoundId()).get(language)).collect(Collectors.toList());
            List<AudioJson116BO> startAudioList4Second = startAudioEnList4Second.stream().map(audio -> audioJson116I18nMap.get(audio.getSoundId()).get(language)).collect(Collectors.toList());

            TaiChiGenerateSoundBO soundBO = new TaiChiGenerateSoundBO().setFirstAudio(firstAudio).setNextAudio(nextAudio).setLastAudio(lastAudio)
                    .setPromptAudioList4Second(promptAudioList4Second)
                    .setStartAudioList4First(startAudioList4First).setStartAudioList4Second(startAudioList4Second);
            languageAudioMap.put(language, soundBO);
        });

        taiChiGenerateSoundBO.setI18nAudioMap(languageAudioMap);
        return taiChiGenerateSoundBO;
    }

    /**
     * 生成m3u8和json文件
     */
    @Override
    public void generateFile(List<ProjWorkout116GenerateBO> projWorkout116BOList,
                             ProjWorkout116ContextBO contextBO, List<String> languageList,
                             Boolean videoFlag, Boolean audioFlag, Boolean restrictionFlag) {

        if (CollectionUtils.isEmpty(projWorkout116BOList)) {
            return;
        }

        TaiChiGenerateSoundBO soundBO = createTaiChiSoundBO(languageList);
        Map<Integer, List<ResVideo116SliceDetailVO>> videoSliceMap = contextBO.getVideoSliceMap();
        Map<Object, ProjResVideo116I18n> videoI18nMap = contextBO.getVideoI18nMap();

        int corePoolSize = 50;
        ExecutorService executorService = createExecutor(corePoolSize);
        AtomicBoolean uploadSuccess = new AtomicBoolean(true);

        for (ProjWorkout116GenerateBO generateBO : projWorkout116BOList) {

            // 失败即终止
            if (!uploadSuccess.get()) {
                log.warn("oog116 tai chi workout resource upload m3u8 and audio json failed");
                throw new BizException("oog116 tai chi workout resource upload m3u8 and audio json failed");
            }

            // TS拼接
            TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
            TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
            AtomicBoolean front = new AtomicBoolean(true);
            AtomicBoolean front4Dynamic = new AtomicBoolean(true);

            int workoutDuration = 0;
            BigDecimal workoutCalorie = BigDecimal.ZERO;

            // 多语言
            Map<String, List<AudioJson116BO>> languageAudioMap = initLanguageAudioMap(languageList);
            List<ProjWorkout116GenerateResVideo116BO> video116BOList = generateBO.getProjWorkout116GenerateResVideo116List();
            List<TaiChiRoundBO> taiChiRoundBOList = wrapRoundBOList4GenerateWorkout(video116BOList);
            for (int round = 0; round < taiChiRoundBOList.size(); round++) {

                TaiChiRoundBO taiChiRoundBO = taiChiRoundBOList.get(round);
                ResVideo116 currentVideo = taiChiRoundBO.getResVideo116();
                Integer currentVideoDuration = taiChiRoundBO.getResVideoDuration();

                if (audioFlag) {
                    boolean lastNode = taiChiRoundBOList.indexOf(taiChiRoundBO) + taiChiRoundBO.getCircuit() == taiChiRoundBOList.size() - 1;
                    assembleGuidanceAudio(soundBO, currentVideo, videoI18nMap, languageAudioMap, currentVideoDuration, lastNode, workoutDuration, taiChiRoundBO.getVideoRound());
                }

                if (videoFlag) {
                    assemble2532M3u8Text(videoSliceMap.get(currentVideo.getId()), front, tsTextMerge2532BO);
                    assembleDynamicM3u8Text(videoSliceMap.get(currentVideo.getId()), front4Dynamic, tsTextMergeDynamicBO);
                }

                // 在计算Video卡路里时，已经考虑了circuit 参数
                if (taiChiRoundBO.getVideoRound() == 0) {
                    workoutCalorie = workoutCalorie.add(currentVideo.getCalorie());
                }

                workoutDuration += currentVideoDuration;
            }

            // 填充基础属性
            ProjWorkout116Generate workout116Generate = generateBO.getProjWorkout116Generate();
            handleBase4Workout(workout116Generate, workoutCalorie, workoutDuration);
            handleRestriction(restrictionFlag, workout116Generate, video116BOList, generateBO.getRestrictionList());
            handleLanguage(audioFlag, workout116Generate, languageList);

            executorService.execute(() -> uploadFile(generateBO, uploadSuccess, tsTextMergeDynamicBO, languageAudioMap, tsTextMerge2532BO, videoFlag, audioFlag));
        }

        // 同步等待资源上传
        wait4UploadFile(executorService, uploadSuccess, corePoolSize);
    }

    private void wait4UploadFile(ExecutorService executor, AtomicBoolean uploadSuccess, int waitTime) {

        // 停止接收新任务
        executor.shutdown();
        if (!uploadSuccess.get()) {
            executor.shutdownNow();
            throw new BizException("oog116 tai chi workout upload m3u8 and audio json failed");
        }

        try {
            if (!executor.awaitTermination(waitTime, TimeUnit.MINUTES)) {
                throw new BizException("waiting oog116 tai chi workout upload m3u8 and audio json overtime");
            }
        } catch (InterruptedException e) {
            throw new BizException("oog116 tai chi workout executor interrupted exception");
        } finally {
            if (!uploadSuccess.get()) {
                executor.shutdownNow();
                throw new BizException("oog116 tai chi workout upload m3u8 and audio json failed");
            }
        }
    }

    private ExecutorService createExecutor(int corePoolSize) {

        ExecutorService executor = new ThreadPoolExecutor(corePoolSize, corePoolSize, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(corePoolSize * 8), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    private void handleBase4Workout(ProjWorkout116Generate workout116Generate, BigDecimal workoutCalorie,
                                    int workoutDuration) {

        workout116Generate
                .setCalorie(workoutCalorie.setScale(0, RoundingMode.HALF_UP).intValue())
                .setDuration(workoutDuration)
                .setDataVersion(GlobalConstant.ONE)
                .setFileStatus(GlobalConstant.ONE);
    }

    private void handleLanguage(Boolean audioFlag, ProjWorkout116Generate workout116Generate, List<String> languageList) {

        if (!audioFlag) {
            return;
        }

        String audioLanguages = workout116Generate.getAudioLanguages();
        if (StringUtils.isNotBlank(audioLanguages)) {
            Arrays.stream(audioLanguages.split(GlobalConstant.COMMA)).filter(language -> !languageList.contains(language)).forEach(languageList::add);
        }

        languageList.sort(String::compareTo);
        workout116Generate.setAudioLanguages(StringUtils.join(languageList, GlobalConstant.COMMA));
    }

    private void handleRestriction(Boolean restrictionFlag, ProjWorkout116Generate workout116Generate,
                                   List<ProjWorkout116GenerateResVideo116BO> video116BOList, List<Restriction116Enums> restrictionList) {

        if (!restrictionFlag) {
            return;
        }

        Set<String> restrictionSet4Workout = handleRestriction4Workout(video116BOList);
        workout116Generate.setRestriction(String.join(GlobalConstant.COMMA, Restriction116Enums.eliminateRestriction(restrictionSet4Workout)))
                .setRestrictionSum(restrictionList.stream().mapToInt(Restriction116Enums::getValue).sum());
    }


    /**
     * 上传m3u8和audio json，同时将返回的url设置到ProjWorkout116Generate实体内    private List<TsMergeBO> videoList;
     * private List<AudioJson116BO> audioList;
     */
    private void uploadFile(ProjWorkout116GenerateBO workoutBO, AtomicBoolean uploadSuccess, TsTextMergeBO tsTextMergeBO,
                            Map<String, List<AudioJson116BO>> audioListMap,
                            TsTextMergeBO tsTextMerge2532BO, Boolean videoFlag, Boolean audioFlag) {

        if (!uploadSuccess.get()) {
            throw new BizException("oog116 upload m3u8 and audio json failed");
        }

        try {
            ProjWorkout116Generate projWorkout116Generate = workoutBO.getProjWorkout116Generate();
            if (videoFlag) {
                // 上传m3u8，并保存相对地址
                UploadFileInfoRes videoR2Info = fileService.uploadMergeTsTextForM3u8(tsTextMergeBO, "project-workout116-m3u8");
                UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8(tsTextMerge2532BO, "project-workout116-m3u8");
                projWorkout116Generate.setVideoUrl(videoR2Info.getFileRelativeUrl()).setVideo2532Url(video2532R2Info.getFileRelativeUrl());
            }

            if (audioFlag) {
                List<ProjWorkout116GenerateI18n> generateI18nList = Lists.newArrayList();
                audioListMap.entrySet().stream().forEach(entry -> {

                    // 上传音频JSON
                    String language = entry.getKey();
                    List<AudioJson116BO> audioList = entry.getValue();
                    UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(JacksonUtil.toJsonString(audioList), "project-workout116-json");

                    // 保存音频相对地址
                    if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                        projWorkout116Generate.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                    } else {
                        ProjWorkout116GenerateI18n generateI18n = new ProjWorkout116GenerateI18n();
                        generateI18n.setLanguage(language);
                        generateI18n.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                        generateI18nList.add(generateI18n);
                    }
                });
                workoutBO.setI18nList(generateI18nList);
            }
        } catch (Exception e) {
            uploadSuccess.set(false);
            log.warn("oog116 upload failed", e);
        }
    }

    private Set<String> handleRestriction4Workout(List<ProjWorkout116GenerateResVideo116BO> video116BOList) {

        return video116BOList.stream().filter(video -> StringUtils.isNotBlank(video.getResVideo116().getRestriction()))
                .map(video -> {
                    return Arrays.stream(video.getResVideo116().getRestriction().split(GlobalConstant.COMMA)).filter(str -> !Objects.equals(str, BizConstant.NONE)).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toSet());
    }

    private List<TaiChiRoundBO> wrapRoundBOList4GenerateWorkout(List<ProjWorkout116GenerateResVideo116BO> video116BOList) {

        List<TaiChiRoundBO> taiChiRoundBOList = Lists.newArrayList();
        List<ProjTemplate116RuleVO> ruleVOList = video116BOList.stream().map(ProjWorkout116GenerateResVideo116BO::getRuleVO).distinct().collect(Collectors.toList());
        Map<ProjTemplate116RuleVO, List<ProjWorkout116GenerateResVideo116BO>> ruleAndVideoMap = video116BOList.stream().collect(Collectors.groupingBy(ProjWorkout116GenerateResVideo116BO::getRuleVO));

        for (ProjTemplate116RuleVO ruleVO : ruleVOList) {
            List<ProjWorkout116GenerateResVideo116BO> ruleVideo116BOList = ruleAndVideoMap.get(ruleVO);
            for (int roundIndex = 0; roundIndex < ruleVO.getRounds(); roundIndex++) {
                for (ProjWorkout116GenerateResVideo116BO resVideo116BO : ruleVideo116BOList) {
                    int[] circuitDurations = Arrays.stream(resVideo116BO.getCircuitVideoDuration().split(GlobalConstant.COMMA)).mapToInt(Integer::parseInt).toArray();
                    for (int circuitIndex = -1; circuitIndex < resVideo116BO.getCircuit(); circuitIndex++) {
                        int videoDuration;
                        if (circuitIndex == -1) {
                            videoDuration = resVideo116BO.getPreviewDuration();
                        } else {
                            videoDuration = circuitDurations[circuitIndex];
                        }

                        TaiChiRoundBO taiChiRoundBO = new TaiChiRoundBO(resVideo116BO.getResVideo116(), roundIndex, circuitIndex, videoDuration);
                        taiChiRoundBOList.add(taiChiRoundBO);
                    }
                }
            }
        }

        return taiChiRoundBOList;
    }

    private Map<String, List<AudioJson116BO>> initLanguageAudioMap(List<String> languageList) {

        LinkedHashMap<String, List<AudioJson116BO>> audioListMap = new LinkedHashMap<>();
        for (String language : languageList) {
            audioListMap.put(language, new ArrayList<>(64));
        }
        return audioListMap;
    }

    @Override
    public void assembleDynamicM3u8Text(List<ResVideo116SliceDetailVO> sliceDetailVOList, AtomicBoolean front, TsTextMergeBO tsTextMergeDynamicBO) {

        sliceDetailVOList.stream().sorted(Comparator.comparing(ResVideo116SliceDetailVO::getSliceIndex)).forEach(detailVO -> {
            if (front.get()) {
                tsTextMergeDynamicBO.addM3u8Text(detailVO.getFrontM3u8Text2k(), detailVO.getFrontM3u8Text1080p(), detailVO.getFrontM3u8Text720p(), detailVO.getFrontM3u8Text480p(), detailVO.getFrontM3u8Text360p());
                front.set(false);
            } else {
                tsTextMergeDynamicBO.addM3u8Text(detailVO.getSideM3u8Text2k(), detailVO.getSideM3u8Text1080p(), detailVO.getSideM3u8Text720p(), detailVO.getSideM3u8Text480p(), detailVO.getSideM3u8Text360p());
                front.set(true);
            }
        });
    }


    @Override
    public void assemble2532M3u8Text(List<ResVideo116SliceDetailVO> sliceDetailVOList, AtomicBoolean front, TsTextMergeBO tsTextMerge2532BO) {

        sliceDetailVOList.stream().sorted(Comparator.comparing(ResVideo116SliceDetailVO::getSliceIndex)).forEach(detailVO -> {
            if (front.get()) {
                tsTextMerge2532BO.addM3u8Text(detailVO.getFrontM3u8Text2532());
                front.set(false);
            } else {
                tsTextMerge2532BO.addM3u8Text(detailVO.getSideM3u8Text2532());
                front.set(true);
            }
        });
    }

    @Override
    public void assembleGuidanceAudio(TaiChiGenerateSoundBO soundBO, ResVideo116 currentVideo,
                                      Map<Object, ProjResVideo116I18n> videoI18nMap,
                                       Map<String, List<AudioJson116BO>> languageAudioJsonMap, Integer currentVideoDuration,
                                       boolean lastNode, int workoutDuration, int circuitIndex) {

        String gender = currentVideo.getGender();
        languageAudioJsonMap.entrySet().forEach(entry -> {

            TaiChiGenerateSoundBO i18nSoundBO = soundBO.getI18nAudioMap().getOrDefault(entry.getKey(), soundBO);
            List<AudioJson116BO> guidanceAudioList = entry.getValue();
            String guidanceUrl = getI18nGuidance(currentVideo, videoI18nMap, entry.getKey());

            // 第一轮播放 特殊处理
            if (circuitIndex == -1) {

                // first audio
                int playTime = workoutDuration + GlobalConstant.HUNDRED;
                boolean firstNode = guidanceAudioList.size() == 0;

                if (firstNode) {
                    AudioJson116BO firstAudio = i18nSoundBO.getFirstAudio();
                    addSysAudioJson(guidanceAudioList, firstAudio, playTime);
                    playTime += firstAudio.getDuration().intValue();
                }

                // next audio or last audio
                if (!firstNode) {
                    AudioJson116BO playAudio = lastNode ? i18nSoundBO.getLastAudio() : i18nSoundBO.getNextAudio();
                    addSysAudioJson(guidanceAudioList, playAudio, playTime);
                    playTime += playAudio.getDuration().intValue();
                }

                // guidance audio
                playTime += GlobalConstant.THOUSAND;
                AudioJson116BO guidanceAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(guidanceUrl), fileService.getAbsoluteR2Url(guidanceUrl), FireBaseUrlSubUtils.getFileName(guidanceUrl), null, null, true,null,null,null);
                addSysAudioJson(guidanceAudioList, guidanceAudio, playTime);
            }
            else if (circuitIndex == 0) {
                List<AudioJson116BO> startAudioList = i18nSoundBO.getStartAudioList4First();
                // 随机拿一个start sound
                int playTime = workoutDuration + GlobalConstant.HUNDRED;
                AudioJson116BO startAudio = randomSelectOne(startAudioList);
                addSysAudioJson(guidanceAudioList, startAudio, playTime );
                playTime += startAudio.getDuration().intValue();

                // guidance audio
                playTime += GlobalConstant.THOUSAND;
                AudioJson116BO guidanceAudio = new AudioJson116BO(FireBaseUrlSubUtils.getFileName(guidanceUrl), fileService.getAbsoluteR2Url(guidanceUrl), FireBaseUrlSubUtils.getFileName(guidanceUrl), null, null, true,null,null,null);
                addSysAudioJson(guidanceAudioList, guidanceAudio, playTime);
            }
            else {
                // 第二、三轮时使用不同的音频
                List<AudioJson116BO> startAudioList = i18nSoundBO.getStartAudioList4Second();
                List<AudioJson116BO> promptAudioList = i18nSoundBO.getPromptAudioList4Second();

                // 随机拿一个start sound
                int playTime = workoutDuration + GlobalConstant.HUNDRED;
                AudioJson116BO startAudio = randomSelectOne(startAudioList);
                addSysAudioJson(guidanceAudioList, startAudio, playTime);

                // 随机拿一个prompt sound
                playTime = workoutDuration + currentVideoDuration / 2;
                AudioJson116BO promptAudio = randomSelectOne(promptAudioList);

                // 兼容APP端逻辑
                promptAudio.setId("halfway");
                addSysAudioJson(guidanceAudioList, promptAudio, playTime);
            }
        });
    }

    private String getI18nGuidance(ResVideo116 currentVideo, Map<Object, ProjResVideo116I18n> videoI18nMap, String language) {
        //新翻译逻辑
        String guidanceUrl = currentVideo.getGuidanceAudioUrl();
        ProjResVideo116I18n i18nVideo = videoI18nMap.get(currentVideo.getId());
        Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = i18nVideo.getGuidanceResult() == null ? MapUtil.empty() :
                i18nVideo.getGuidanceResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
        LanguageEnums le = LanguageEnums.getByNameIgnoreCase(language);
        if (MapUtils.isNotEmpty(guidanceMap) && guidanceMap.containsKey(le)) {
            AudioTranslateResultModel audioTranslateResultModel = guidanceMap.get(le);
            guidanceUrl = audioTranslateResultModel.getAudioUrl();
        }
        return guidanceUrl;
    }

    private AudioJson116BO randomSelectOne(List<AudioJson116BO> audioJsonBOList) {

        Collections.shuffle(audioJsonBOList);
        return audioJsonBOList.get(GlobalConstant.ZERO);
    }

    private void addSysAudioJson(List<AudioJson116BO> audioList, AudioJson116BO sysAudioJson, int playTime ) {
        AudioJson116BO audioJsonBO = new AudioJson116BO(sysAudioJson.getId(), sysAudioJson.getUrl(), sysAudioJson.getName(), new BigDecimal(playTime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP), sysAudioJson.getDuration(), sysAudioJson.isClose(),sysAudioJson.getGender(),sysAudioJson.getSoundScript(),sysAudioJson.getCoreVoiceConfigI18nId());
        audioList.add(audioJsonBO);
    }

}
