package com.laien.web.biz.proj.oog116.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.common.oog116.enums.*;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog116.bo.ResVideo116RecoveryValidateBO;
import com.laien.web.biz.proj.oog116.config.Oog116BizConfig;
import com.laien.web.biz.proj.oog116.entity.ResVideo116;
import com.laien.web.biz.proj.oog116.entity.ResVideo116Slice;
import com.laien.web.biz.proj.oog116.mapper.ResVideo116Mapper;
import com.laien.web.biz.proj.oog116.request.*;
import com.laien.web.biz.proj.oog116.response.ResVideo116DetailVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116ExportVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116PageVO;
import com.laien.web.biz.proj.oog116.response.ResVideo116SliceDetailVO;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116Generate4TaiChiService;
import com.laien.web.biz.proj.oog116.service.IResVideo116Service;
import com.laien.web.biz.proj.oog116.service.IResVideo116SliceService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.validation.Group;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.laien.common.oog116.enums.ExerciseType116Enums.*;
import static com.laien.common.oog116.enums.TaskResourceSectionQueryEnums.RESVIDEO_116_FRONT;
import static com.laien.common.oog116.enums.TaskResourceSectionQueryEnums.RESVIDEO_116_SIDE;
import static com.laien.web.biz.proj.oog116.constant.ResVideo116TypeEnum.MAIN;
import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.RES_VIDEO116_SLICE_FRONT;
import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.RES_VIDEO116_SLICE_SIDE;
import static com.laien.web.frame.constant.GlobalConstant.NO;
import static com.laien.web.frame.constant.GlobalConstant.STATUS_ENABLE;

/**
 * <p>
 * 116 video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
@Slf4j
public class ResVideo116ServiceImpl extends ServiceImpl<ResVideo116Mapper, ResVideo116> implements IResVideo116Service {

    /**
     * 导入时使用别名
     */
    private static final String CHAIR_YOGA_IMPORT_ALIAS = "Chair-Based Cardio";

    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    private FileService fileService;

    @Resource
    private Validator validator;
    @Resource
    private  ICoreVoiceConfigI18nService i18nConfigService;
    @Resource
    private IResVideo116SliceService video116SliceService;

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;

    @Resource
    private IProjWorkout116Generate4TaiChiService generate4TaiChiService;

    @Resource
    private ResVideo116Mapper resVideo116Mapper;

    @Resource
    private Oog116BizConfig og116BizConfig;

    private final List<String> VIDEO_SLICE_LIST = Lists.newArrayList(TAI_CHI.getName(), GENTLE_CHAIR_YOGA.getName(), RECOVERY.getName(), DUMBBELL_MIDWEIGHT.getName());

    @Override
    public PageRes<ResVideo116PageVO> selectVideo116Page(ResVideo116PageReq pageReq) {
        LambdaQueryWrapper<ResVideo116> queryWrapper = new LambdaQueryWrapper<>();
        String name = pageReq.getName();
        String type = pageReq.getType();
        String position = pageReq.getPosition();
        String[] restrictionArr = pageReq.getRestrictionArr();
        Integer status = pageReq.getStatus();
        String equipment = pageReq.getEquipment();
        String gender = pageReq.getGender();
        String exerciseType = pageReq.getExerciseType();
        queryWrapper.like(StringUtils.isNotBlank(name), ResVideo116::getName, name)
                .eq(StringUtils.isNotBlank(type), ResVideo116::getType, type)
                .eq(StringUtils.isNotBlank(position), ResVideo116::getPosition, position)
                .eq(StringUtils.isNotBlank(equipment), ResVideo116::getEquipment, equipment)
                .eq(StringUtils.isNotBlank(gender), ResVideo116::getGender, gender)
                .eq(pageReq.getSupportProp() != null, ResVideo116::getSupportProp, pageReq.getSupportProp());

        // 过滤用于系统配置的Video/当前只有女声
        Integer seatedVideoId = og116BizConfig.getChairYoga().getFemaleSoundConfig().getChairYogaVideo4Seated();
        if (Objects.nonNull(seatedVideoId)) {
            queryWrapper.ne(ResVideo116::getId, seatedVideoId);
        }

        Integer standingVideoId = og116BizConfig.getChairYoga().getFemaleSoundConfig().getChairYogaVideo4Standing();
        if (Objects.nonNull(standingVideoId)) {
            queryWrapper.ne(ResVideo116::getId, standingVideoId);
        }

        if (Objects.nonNull(restrictionArr) && restrictionArr.length > GlobalConstant.ZERO) {
            queryWrapper.and(o -> {
                int len = restrictionArr.length;
                for (int i = 0; i < len; i++) {
                    o.apply("FIND_IN_SET({0}, restriction)", restrictionArr[i]);
                    o.or(i < len - 1);
                }
            });
        }

        if (StringUtils.isNotBlank(exerciseType)) {
            queryWrapper.and(o -> o.apply("FIND_IN_SET({0}, exercise_type)", exerciseType));
        }
        queryWrapper.eq(Objects.nonNull(status), ResVideo116::getStatus, status).orderByDesc(ResVideo116::getId);

        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ResVideo116::getRegion, pageReq.getRegion(), false);
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ResVideo116::getFocus, pageReq.getFocus(), false);

        Page<ResVideo116> resVideoPage = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        IPage<ResVideo116> page = this.page(resVideoPage, queryWrapper);
        PageRes<ResVideo116PageVO> pageVO = PageConverter.convert(page, ResVideo116PageVO.class);

        setProperty4TaiChi(pageVO.getList());
        injectionTaskStatus(pageVO.getList());
        return pageVO;
    }

    private void setProperty4TaiChi(List<ResVideo116PageVO> videoList) {

        if (CollectionUtils.isEmpty(videoList)) {
            return;
        }
        List<Integer> videoIdList = videoList.stream().map(ResVideo116PageVO::getId).collect(Collectors.toList());
        List<ResVideo116SliceDetailVO> sliceDetailVOList = video116SliceService.listByResVideoId(videoIdList);

        if (CollectionUtils.isEmpty(sliceDetailVOList)) {
            return;
        }
        Map<Integer, List<ResVideo116SliceDetailVO>> videoAndSliceMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ResVideo116SliceDetailVO::getResVideo116Id));
        videoList.forEach(resVideo116 -> resVideo116.setVideosliceList(videoAndSliceMap.get(resVideo116.getId())));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVideo116(ResVideo116AddReq video116AddReq) {
        ResVideo116 video116 = new ResVideo116();
        BeanUtils.copyProperties(video116AddReq, video116);
        String[] restrictionArr = video116AddReq.getRestrictionArr();
        if (restrictionArr != null && restrictionArr.length > 0) {
            video116.setRestriction(MyStringUtil.getJoinWithComma(restrictionArr));
        } else {
            video116.setRestriction(BizConstant.NONE);
        }
        // exercise type 自然排序后再做比对，避免相同选项因排序不同导致被识别为不同的数据
        String exerciseType = video116AddReq.getExerciseType();
        video116.setExerciseType(exerciseType);
        video116.setStatus(GlobalConstant.STATUS_DRAFT);
        String name = video116AddReq.getName();
        video116.setEventName(name);
        // target 转换为code
        TargetEnums target = Arrays.stream(TargetEnums.values()).filter(t -> Objects.equals(t.getName(), video116AddReq.getTarget())).findFirst().orElse(null);
        video116.setTarget(target);
        // 相同Exercise Type+相同Position，Name不能重复，由于历史数据中已经存在重复数据，不能使用数据库唯一索引保障名称不重复，这里采用先查询判断重复，再添加的方式
        // 由于事务具有隔离性，不同的事务在不同的数据视图上查询，这种方式不能保障最终的唯一性
        LambdaQueryWrapper<ResVideo116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideo116::getPosition, video116AddReq.getPosition());
        queryWrapper.eq(ResVideo116::getGender, video116AddReq.getGender());
        queryWrapper.eq(ResVideo116::getName, name);
        queryWrapper.eq(ResVideo116::getDelFlag, GlobalConstant.NO);
        List<ResVideo116> resVideo116s = this.baseMapper.selectList(queryWrapper);
        boolean uniqueIndexConflict = isUniqueIndexConflict(resVideo116s, exerciseType);
        BizExceptionUtil.throwIf(uniqueIndexConflict, "相同Exercise Type 和 Position，Name不能重复");
        this.save(video116);
        //添加翻译任务
        lmsI18nService.handleI18n(ListUtil.of(video116), projInfoService.find(ProjCodeEnums.OOG116.getAppCode()));

        String eventName = video116.getId() + name;
        video116.setEventName(eventName);
        updateById(video116);

        saveVideoSlice(video116, video116AddReq.getVideosliceList());
    }

    private boolean isUniqueIndexConflict(List<ResVideo116> resVideo116s, String exerciseType) {

        // 其余判断有无交集
        return resVideo116s.stream().anyMatch(video -> {
            // 如果有一方为空，只有都为都为空，则返回true
            if (StringUtils.isBlank(video.getExerciseType()) || StringUtils.isBlank(exerciseType)) {
                return Objects.equals(video.getExerciseType(), exerciseType);
            }
            // 其余判断有无交集
            ArrayList<String> videoExerciseType = CollUtil.newArrayList(video.getExerciseType().split(GlobalConstant.COMMA));
            ArrayList<String> reqExerciseType = CollUtil.newArrayList(exerciseType.split(GlobalConstant.COMMA));
            return videoExerciseType.stream().anyMatch(reqExerciseType::contains);
        });
    }

    @Override
    public String videoFrontForM3U8R2(String frontVideoUrl, Integer frontDuration) {
        List<TsMergeBO> videoList = new ArrayList<>(GlobalConstant.ONE);
        videoList.add(new TsMergeBO(fileService.getAbsoluteR2Url(frontVideoUrl), frontDuration));
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTSForM3U8R2(videoList, "video116-video");
        return videoR2Info.getFileRelativeUrl();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateVideo116(ResVideo116UpdateReq video116UpdateReq) {
        Integer id = video116UpdateReq.getId();
        ResVideo116 video116Find = this.getById(id);
        if (Objects.isNull(video116Find)) {
            throw new BizException("Data not found");
        }

        if (Objects.equals(video116Find.getStatus(), STATUS_ENABLE)) {
            throw new BizException("Data is enabled, can not update video116");
        }

        ResVideo116 video116 = new ResVideo116();
        BeanUtils.copyProperties(video116UpdateReq, video116);
        // target 转换为code
        TargetEnums target = Arrays.stream(TargetEnums.values()).filter(t -> Objects.equals(t.getName(), video116UpdateReq.getTarget())).findFirst().orElse(null);
        video116.setTarget(target);
        String[] restrictionArr = video116UpdateReq.getRestrictionArr();
        if (restrictionArr != null && restrictionArr.length > 0) {
            video116.setRestriction(MyStringUtil.getJoinWithComma(restrictionArr));
        } else {
            video116.setRestriction(BizConstant.NONE);
        }
        // 处理 exercise type
        video116.setExerciseType(video116UpdateReq.getExerciseType());
        LambdaQueryWrapper<ResVideo116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideo116::getPosition, video116UpdateReq.getPosition());
        queryWrapper.eq(ResVideo116::getName, video116UpdateReq.getName());
        queryWrapper.eq(ResVideo116::getGender, video116UpdateReq.getGender());
        queryWrapper.ne(ResVideo116::getId, video116UpdateReq.getId());
        queryWrapper.eq(ResVideo116::getDelFlag, GlobalConstant.NO);
        List<ResVideo116> resVideo116s = this.baseMapper.selectList(queryWrapper);
        boolean uniqueIndexConflict = isUniqueIndexConflict(resVideo116s, video116UpdateReq.getExerciseType());
        BizExceptionUtil.throwIf(uniqueIndexConflict, "相同Exercise Type 和 Position，Name不能重复");
        // 正机位生成m3u8
        if (!Objects.equals(video116.getFrontVideoUrl(), video116Find.getFrontVideoUrl())) {
            String videoUrl = this.videoFrontForM3U8R2(video116.getFrontVideoUrl(), video116.getFrontDuration());
            video116.setVideoUrl(videoUrl);
        }

        this.updateById(video116);
        //添加翻译任务
        lmsI18nService.handleI18n(ListUtil.of(video116), projInfoService.find(ProjCodeEnums.OOG116.getAppCode()));

        saveVideoSlice(video116, video116UpdateReq.getVideosliceList());
    }
    private void saveVideoSlice(ResVideo116 video116, List<ResVideo116SliceDetailVO> sliceDetailList) {

        video116SliceService.deleteByResVideoId(video116.getId());
        if (CollectionUtils.isEmpty(sliceDetailList)) {
            return;
        }

        List<ResVideo116Slice> sliceEntityList = sliceDetailList.stream().map(detail -> convert2SliceEntity(video116, detail)).collect(Collectors.toList());
        video116SliceService.saveBatch(sliceEntityList);
    }

    private ResVideo116Slice convert2SliceEntity(ResVideo116 video116, ResVideo116SliceDetailVO sliceDetailVO) {

        ResVideo116Slice resVideo116Slice = new ResVideo116Slice();
        BeanUtils.copyProperties(sliceDetailVO, resVideo116Slice);

        resVideo116Slice.setResVideo116Id(video116.getId());
        return resVideo116Slice;
    }

    @Override
    public ResVideo116DetailVO getVideo116Detail(Integer id) {
        ResVideo116 video116Find = this.getById(id);
        if (Objects.isNull(video116Find)) {
            throw new BizException("Data not found");
        }

        ResVideo116DetailVO detailVO = new ResVideo116DetailVO();
        BeanUtils.copyProperties(video116Find, detailVO);
        //查询声音 name
        if (Objects.nonNull(detailVO.getCoreVoiceConfigI18nId())) {
            CoreVoiceConfigI18n coreVoiceConfigI18n = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
            detailVO.setCoreVoiceConfigI18nName(coreVoiceConfigI18n!=null?coreVoiceConfigI18n.getName():null);
        }
        String restriction = video116Find.getRestriction();
        if (Objects.equals(restriction, BizConstant.NONE)) {
            detailVO.setRestrictionArr(new String[]{});
        } else {
            detailVO.setRestrictionArr(MyStringUtil.getSplitWithComa(restriction));
        }
        // target
        String targetName = Optional.ofNullable(video116Find.getTarget()).map(TargetEnums::getName).orElse(null);
        detailVO.setTarget(targetName);
        List<ResVideo116SliceDetailVO> sliceList = video116SliceService.listByResVideoId(Lists.newArrayList(id));
        detailVO.setVideosliceList(sliceList);
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ResVideo116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ResVideo116::getId, idList);
        queryWrapper.ne(ResVideo116::getStatus, GlobalConstant.ONE);

        List<ResVideo116> resVideo116s = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(resVideo116s)) {
            return idList;
        }

        // tai chi 单独处理
        Predicate<ResVideo116> isTaiChi = video -> VIDEO_SLICE_LIST.contains(video.getExerciseType());
        Set<Integer> taiChiVideoIds = resVideo116s.stream().filter(isTaiChi).map(ResVideo116::getId).collect(Collectors.toSet());
        List<Integer> unEnableList = Lists.newArrayList();
        enableVideo4TaiChi(unEnableList, taiChiVideoIds);

        // 非 tai chi
        idList = resVideo116s.stream().filter(isTaiChi.negate()).map(ResVideo116::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return unEnableList;
        }

        List<TaskResourceSection> taskList = taskResourceSectionService.query(RESVIDEO_116_FRONT.getTableName(), RESVIDEO_116_FRONT.getEntityFieldName(), idList);
        if (CollUtil.isEmpty(taskList)) {
            unEnableList.addAll(idList);
            return unEnableList;
        }

        List<Integer> completedIdList = TaskResourceSectionUtil.getCompletedIdList(taskList);
        if (CollUtil.isEmpty(completedIdList)) {
            unEnableList.addAll(idList);
            return unEnableList;
        }

        LambdaUpdateWrapper<ResVideo116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideo116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResVideo116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ResVideo116::getId, completedIdList);
        this.update(new ResVideo116(), wrapper);

        idList.removeAll(completedIdList);
        unEnableList.addAll(idList);
        return unEnableList;
    }

    private void enableVideo4TaiChi(List<Integer> unEnableList, Set<Integer> taiChiVideoIds) {

        if (CollUtil.isEmpty(taiChiVideoIds)) {
            return;
        }

        List<ResVideo116SliceDetailVO> sliceDetailVOList = video116SliceService.listByResVideoId(taiChiVideoIds);
        if (CollUtil.isEmpty(sliceDetailVOList)) {
            unEnableList.addAll(taiChiVideoIds);
            return;
        }

        // 获取太极视频切片的切片任务执行状态
        Set<Integer> videoSliceIds = sliceDetailVOList.stream().map(ResVideo116SliceDetailVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(RES_VIDEO116_SLICE_SIDE.getTableName(), RES_VIDEO116_SLICE_SIDE.getEntityFieldName(), videoSliceIds);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(RES_VIDEO116_SLICE_FRONT.getTableName(), RES_VIDEO116_SLICE_FRONT.getEntityFieldName(), videoSliceIds);
        if (CollectionUtils.isEmpty(sideStatusList) || CollectionUtils.isEmpty(frontStatusList)) {
            unEnableList.addAll(taiChiVideoIds);
        }

        List<Integer> enableAbleIdList = Lists.newArrayList();
        Map<Integer, Set<Integer>> videoAndSliceIdMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ResVideo116SliceDetailVO::getResVideo116Id, Collectors.mapping(ResVideo116SliceDetailVO::getId, Collectors.toSet())));
        taiChiVideoIds.forEach(videoId -> {

            if (!videoAndSliceIdMap.containsKey(videoId)) {
                unEnableList.add(videoId);
                return;
            }

            Set<Integer> sliceIds = videoAndSliceIdMap.get(videoId);
            boolean updateAble = checkTaskSection4Complete(frontStatusList, sliceIds);
            if (!updateAble) {
                unEnableList.add(videoId);
                return;
            }

            updateAble = checkTaskSection4Complete(sideStatusList, sliceIds);
            if (!updateAble) {
                unEnableList.add(videoId);
                return;
            }
            enableAbleIdList.add(videoId);
        });

        Map<Integer, List<ResVideo116SliceDetailVO>> videoAndSliceMap = sliceDetailVOList.stream().collect(Collectors.groupingBy(ResVideo116SliceDetailVO::getResVideo116Id));
        enableAbleIdList.forEach(videoId -> {
            List<ResVideo116SliceDetailVO> sliceDetailVOS = videoAndSliceMap.get(videoId).stream().sorted(Comparator.comparing(ResVideo116SliceDetailVO::getSliceIndex)).collect(Collectors.toList());

            AtomicBoolean front = new AtomicBoolean(true);
            TsTextMergeBO tsTextMerge2532BO = new TsTextMergeBO();
            generate4TaiChiService.assemble2532M3u8Text(sliceDetailVOS, front, tsTextMerge2532BO);
            UploadFileInfoRes video2532R2Info = fileService.uploadMergeTsTextForM3u8(tsTextMerge2532BO, "project-workout116-m3u8");

            front.set(true);
            TsTextMergeBO tsTextMergeDynamicBO = new TsTextMergeBO();
            generate4TaiChiService.assembleDynamicM3u8Text(sliceDetailVOS, front, tsTextMergeDynamicBO);
            UploadFileInfoRes videoDynamicR2Info = fileService.uploadMergeTsTextForM3u8(tsTextMergeDynamicBO, "project-workout116-m3u8");

            enableAndUpdateVideoUrl(videoId, video2532R2Info, videoDynamicR2Info);
        });
    }

    private void enableAndUpdateVideoUrl(Integer videoId, UploadFileInfoRes video2532R2Info, UploadFileInfoRes videoDynamicR2Info) {

        LambdaUpdateWrapper<ResVideo116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ResVideo116::getId, videoId);
        wrapper.in(ResVideo116::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);

        wrapper.set(ResVideo116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.set(ResVideo116::getVideoUrl, videoDynamicR2Info.getFileRelativeUrl());
        wrapper.set(ResVideo116::getVideo2532Url, video2532R2Info.getFileRelativeUrl());

        wrapper.set(ResVideo116::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.set(ResVideo116::getUpdateTime, LocalDateTime.now());
        this.update(new ResVideo116(), wrapper);
    }

    private boolean checkTaskSection4Complete(List<TaskResourceSection> taskList, Set<Integer> sliceIds) {

        List<TaskResourceSectionStatusEnums> taskStatusList = taskList.stream().filter(task -> sliceIds.contains(task.getTableId())).map(TaskResourceSection::getStatus).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskStatusList) || taskStatusList.size() != sliceIds.size()) {
            return false;
        }

        Optional<TaskResourceSectionStatusEnums> optional = taskStatusList.stream().filter(task -> !Objects.equals(task, TaskResourceSectionStatusEnums.COMPLETED)).findAny();
        return !optional.isPresent();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResVideo116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideo116::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ResVideo116::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResVideo116::getId, idList);
        this.update(new ResVideo116(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResVideo116> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResVideo116::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ResVideo116::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ResVideo116::getId, idList);
        this.update(new ResVideo116(), wrapper);
    }

    @Override
    public Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ResVideo116ExportVO> records) {
        Set<Integer> configIds = records.stream().map(ResVideo116ExportVO::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }

    /**
     * 初始化执行，不考虑多线程等并发问题
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importByExcel(InputStream excelInputStream) {
        log.info("resVideo116 importByExcel Start-----------------");
        //1、使用easyExcel,转换excel数据为ResVideo116ImportReq对象
        List<ResVideo116ImportReq> resVideo116ImportReqs = CollUtil.newArrayList();
        List<ResVideo116ImportReq> recoveryVideoImportReqs = CollUtil.newArrayList();
        EasyExcel.read(excelInputStream, ResVideo116ImportReq.class, new AnalysisEventListener<ResVideo116ImportReq>() {
            @Override
            public void invoke(ResVideo116ImportReq row, AnalysisContext analysisContext) {
                if (StringUtils.isNotBlank(row.getExerciseType())) {

                    String exerciseType = Stream.of(row.getExerciseType().split(GlobalConstant.COMMA))
                            .map(aliasName -> {
                                // 导入时，exercise type 由导入别名转为name
                                ExerciseType116Enums exerciseType116Enums = ExerciseType116Enums.getByImportAliasName(aliasName);
                                return Objects.isNull(exerciseType116Enums) ? aliasName : exerciseType116Enums.getName();
                            }).collect(Collectors.joining(GlobalConstant.COMMA));

                    row.setExerciseType(exerciseType);
                }
                if (row.getExerciseType().contains(RECOVERY.getName())) {
                    recoveryVideoImportReqs.add(row);
                } else {
                    resVideo116ImportReqs.add(row);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        //2、过滤不符合输入规范的数据
        List<String> failMessage = CollUtil.newArrayList();
        List<ResVideo116> resVideo116s = filterDirtyData(resVideo116ImportReqs, failMessage);
        resVideo116s.addAll(filterDirtyDataForRecovery(recoveryVideoImportReqs, failMessage));

        if (CollUtil.isNotEmpty(failMessage)) {
            return failMessage;
        }
        // 过滤出有id数据，作修改处理
        Map<Boolean, List<ResVideo116>> groupByIdIsNull = resVideo116s.stream().collect(Collectors.groupingBy(v -> Objects.isNull(v.getId())));
        // 3.1 有ID数据视为修改
        Optional.ofNullable(groupByIdIsNull.get(false)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            Lists.partition(list, 20).forEach(group -> {
                //确定要进行db操作时才进行 计算卡路里, 卡路里计算结果为null时，不设置修改卡路里
                group.forEach(v -> fillCalorieConsumer().accept(v));
                updateBatchById(group);
            });
        });

        //3.2 无ID数据视为新增
        Optional.ofNullable(groupByIdIsNull.get(true)).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            Lists.partition(list, 20).forEach(group -> {
                //确定要进行db操作时才进行 计算卡路里
                group.forEach(v -> fillCalorieConsumer().accept(v));
                saveBatch(group, group.size());
            });
        });

        Optional.of(resVideo116s).filter(CollUtil::isNotEmpty).ifPresent(list -> {
            List<Integer> idList = list.stream().map(BaseModel::getId).collect(Collectors.toList());
            // 批量插入后才可以获取到ID，再来处理eventName=id+name
            LambdaUpdateWrapper<ResVideo116> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(BaseModel::getId, idList);
            updateWrapper.eq(ResVideo116::getEventName, "");
            updateWrapper.setSql("event_name = CONCAT(id,name)");
            this.update(updateWrapper);
        });
        //添加翻译任务
        lmsI18nService.handleI18n(resVideo116s,  projInfoService.find(ProjCodeEnums.OOG116.getAppCode()));

        StringBuilder strBuilder = new StringBuilder();
        failMessage.forEach(s -> strBuilder.append("failMessage:").append(s).append("\r\n"));
        log.info(strBuilder.toString());
        log.info("resVideo116 importByExcel Finish-----------------");
        return failMessage;
    }

    private Collection<? extends ResVideo116> filterDirtyDataForRecovery(List<ResVideo116ImportReq> dataList,
                                                                         List<String> failMessage) {

        List<ResVideo116> resVideo116s = getUniqueResVideo(dataList);

        List<ResVideo116> meetsCondiData = CollUtil.newArrayList();
        Optional.of(dataList).filter(CollUtil::isNotEmpty).ifPresent(data -> {
            boolean validatedMain = false;
            //name 转为 config Id
            Set<String> nameSet = new HashSet<>();
            Set<String> i18nConfigNameSet = dataList.stream().map(ResVideo116ImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
            Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                    .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));
            for (ResVideo116ImportReq req : data) {
                String exerciseType = req.getExerciseType();
                //是否可以执行后续代码
                boolean canContinue = true;
                if (StringUtils.isNotBlank(exerciseType)) {
                    // 存在单选时，不能选多个值
                    if (exerciseType.split(GlobalConstant.COMMA).length > 1) {
                        failMessage.add(req.getName() + ":exerciseType只能选单选");
                        canContinue = false;
                    }
                    // exercise type 规则校验
                    boolean exerciseTypeNotExist = Arrays.stream(exerciseType.split(GlobalConstant.COMMA)).map(ExerciseType116Enums::getByName).anyMatch(Objects::isNull);
                    if (exerciseTypeNotExist) {
                        failMessage.add(req.getName() + ":exerciseType不存在");
                        canContinue = false;
                    }

                    // 依据exercise type 转换 equipment
                    String equipment = Arrays.stream(exerciseType.split(GlobalConstant.COMMA)).map(ExerciseType116Enums::getByName).filter(Objects::nonNull).flatMap(et -> et.getEquipmentList().stream()).map(Equipment116Enums::getName).distinct().sorted().collect(Collectors.joining(GlobalConstant.COMMA));
                    req.setEquipment(equipment);
                } else {
                    failMessage.add(req.getName() + ":exercise_type不能为空");
                    canContinue = false;
                }
                // 相同Exercise Type+相同Position + 相同Name + 相同gender 只保留第一条
                canContinue = checkIfConflicted(failMessage, resVideo116s, req, canContinue);
                req.setFrontVideoUrl(null);
                req.setSideVideoUrl(null);
                req.setFrontDuration(null);
                req.setSideDuration(null);

                if (!canContinue) {
                    continue;
                }
                try {
                    ResVideo116RecoveryValidateBO validateBO = BeanUtil.copyProperties(req, ResVideo116RecoveryValidateBO.class);
                    Optional.ofNullable(validator.validate(validateBO, Group.class)).ifPresent(result -> {
                        Optional<ConstraintViolation<ResVideo116RecoveryValidateBO>> firstError = result.stream().findFirst();
                        if (firstError.isPresent()) {
                            //校验失败，只记录第一条失败原因
                            failMessage.add(req.getName() + ":" + firstError.get().getMessage());
                        } else if (!i18nConfigNameIdMap.containsKey(req.getCoreVoiceConfigI18nName())) {
                            failMessage.add(req.getName() + ": English Voice Name Not Found in TTS config");
                        } else {
                            //生成video对象
                            ResVideo116 resVideo116 = new ResVideo116();
                            BeanUtils.copyProperties(req, resVideo116);
                            resVideo116.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(req.getCoreVoiceConfigI18nName()));
                            meetsCondiData.add(resVideo116);
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    failMessage.add(req.getName() + ":" + e.getMessage());
                }
            }
        });
        return meetsCondiData;
    }

    private boolean checkIfConflicted(List<String> failMessage, List<ResVideo116> resVideo116s, ResVideo116ImportReq req, boolean canContinue) {
        if (hasUniqueKeyConflicted(resVideo116s, req)) {
            failMessage.add(req.getName() + "相同Exercise Type+相同Position ,Name 不能重复");
            canContinue = false;
        } else {
            ResVideo116 resVideo116 = new ResVideo116()
                    .setExerciseType(req.getExerciseType())
                    .setPosition(req.getPosition())
                    .setName(req.getName())
                    .setGender(req.getGender());
            resVideo116s.add(resVideo116);
        }
        return canContinue;
    }

    private List<ResVideo116> getUniqueResVideo(List<ResVideo116ImportReq> dataList) {
        // 相同Exercise Type+相同Position，Name、gender不能重复，因此这里先批量查询出可能会重复的数据
        // 只查询必要的字段 id 、exercise type、position、name、gender
        LambdaQueryWrapper<ResVideo116> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BaseModel::getId, ResVideo116::getExerciseType, ResVideo116::getPosition, ResVideo116::getName, ResVideo116::getGender);
        dataList.stream().filter(data -> StringUtils.isNotBlank(data.getExerciseType()) && StringUtils.isNotBlank(data.getPosition()) && StringUtils.isNotBlank(data.getName()))
                .forEach(data ->
                        queryWrapper.or(wrapper ->
                                wrapper.eq(ResVideo116::getName, data.getName())
                                        .eq(ResVideo116::getPosition, data.getPosition())
                                        .eq(ResVideo116::getGender, data.getGender())
                                        .eq(BaseModel::getDelFlag, GlobalConstant.ZERO)
                        )
                );

        // 将请求数据和数据库中必然重复的数据一次性查询出来做判断，避免在循环中查询数据库
        List<ResVideo116> resVideo116s = this.baseMapper.selectList(queryWrapper);
        return resVideo116s;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importVideoSlice(MultipartFile file) {

        if (Objects.isNull(file)) {
            return Collections.emptyList();
        }

        List<ResVideo116SliceImportReq> sliceImportReqList = parseResVideoSliceImport(file);
        if (CollUtil.isEmpty(sliceImportReqList)) {
            return Collections.emptyList();
        }

        List<String> errorMessages = Lists.newArrayList();
        List<ResVideo116SliceImportReq> filterImportList = filterVideoSliceImport(sliceImportReqList, errorMessages);
        if (CollUtil.isEmpty(filterImportList) || CollUtil.isNotEmpty(errorMessages)) {
            return errorMessages;
        }

        List<ResVideo116SliceImportReq> insertList = Lists.newArrayList();
        List<ResVideo116SliceImportReq> updateList = Lists.newArrayList();
        bizValidate4VideoSliceImport(filterImportList, errorMessages, insertList, updateList);

        if (CollUtil.isNotEmpty(errorMessages)) {
            return errorMessages;
        }

        if (CollUtil.isNotEmpty(insertList)) {
            video116SliceService.saveBatch(insertList);
            updateCalorie4Video(insertList);
        }

        if (CollUtil.isNotEmpty(updateList)) {
            video116SliceService.updateBatch(updateList);
            updateCalorie4Video(updateList);
        }
        return errorMessages;
    }

    private void updateCalorie4Video(List<ResVideo116SliceImportReq> validVideoSliceList) {

        AtomicReference<BigDecimal> dividend = new AtomicReference<>();
        AtomicReference<BigDecimal> divisor = new AtomicReference<>();
        divisor.set(new BigDecimal(3600 * 1000));
        Map<ResVideo116, List<ResVideo116SliceImportReq>> videoAndSliceMap = validVideoSliceList.stream().collect(Collectors.groupingBy(ResVideo116SliceImportReq::getResVideo));
        videoAndSliceMap.entrySet().forEach(entry -> {

            if (Objects.equals(entry.getKey().getExerciseType(), RECOVERY.getName())) {
                return;
            }

            ResVideo116 video = entry.getKey();
            int videoDuration = computeVideoDuration(entry.getValue());
            dividend.set(new BigDecimal(video.getMet() * 75 * videoDuration * video.getCircuit()));

            BigDecimal calorie = dividend.get().divide(divisor.get(), 3, BigDecimal.ROUND_HALF_UP);
            video.setCalorie(calorie);
        });

        updateBatchById(videoAndSliceMap.keySet());
    }

    private int computeVideoDuration(List<ResVideo116SliceImportReq> importReqs) {

        AtomicBoolean isFront = new AtomicBoolean(true);
        int videoDuration = importReqs.stream().mapToInt(req -> {
            if (isFront.get()) {
                isFront.set(false);
                return req.getFrontVideoDuration();
            } else {
                isFront.set(true);
                return req.getSideVideoDuration();
            }
        }).sum();

        return videoDuration;
    }

    private String generateUniqueKey4ResVideo(String exerciseType, String position, String name, String gender) {

        return Joiner.on(GlobalConstant.COMMA).join(String.valueOf(exerciseType), position, name, gender);
    }

    private void bizValidate4VideoSliceImport(List<ResVideo116SliceImportReq> filterImportList, List<String> errorMessages,
                                              List<ResVideo116SliceImportReq> insertList, List<ResVideo116SliceImportReq> updateList) {

        List<ResVideo116> resVideo116List = list();
        if (CollUtil.isEmpty(resVideo116List)) {
            return;
        }

        // slice 导入，支持新增和导入
        Set<Integer> existedSliceVideoSet = listExistedSliceVideoSet();
        Map<String, ResVideo116> uniqueAndVideoMap = resVideo116List.stream().collect(Collectors.toMap(video -> generateUniqueKey4ResVideo(video.getExerciseType(), video.getPosition(), video.getName(), video.getGender()), Function.identity(), (k1, k2) -> k1));
        filterImportList.forEach(importReq -> {

            String uniqueKey = generateUniqueKey4ResVideo(importReq.getExerciseType(), importReq.getPosition(), importReq.getName(), importReq.getGender());
            if (!uniqueAndVideoMap.containsKey(uniqueKey)) {
                errorMessages.add(String.format("Can't find res video with ExerciseType_Position_Name, please check it. invalid value is %s_%s_%s.", importReq.getExerciseType(), importReq.getPosition(), importReq.getName()));
                return;
            }

            ResVideo116 resVideo116 = uniqueAndVideoMap.get(uniqueKey);
            importReq.setResVideo(resVideo116);
            if (existedSliceVideoSet.contains(resVideo116.getId())) {
                // 更新时，需要将原表数据状态修改为草稿
                resVideo116.setStatus(GlobalConstant.STATUS_DRAFT);
                updateList.add(importReq);
            } else {
                insertList.add(importReq);
            }
        });
    }

    private Set<Integer> listExistedSliceVideoSet() {

        List<ResVideo116Slice> sliceList = video116SliceService.list();
        if (CollUtil.isEmpty(sliceList)) {
            return Collections.emptySet();
        }

        Set<Integer> existedSliceVideoSet = sliceList.stream().map(ResVideo116Slice::getResVideo116Id).collect(Collectors.toSet());
        return existedSliceVideoSet;
    }

    private List<ResVideo116SliceImportReq> filterVideoSliceImport(List<ResVideo116SliceImportReq> videoSliceImportList, List<String> errorMessageList) {

        Set<String> exerciseTypeSet = Sets.newHashSet(Arrays.stream(ExerciseType116Enums.values()).map(ExerciseType116Enums::getName).collect(Collectors.toList()));
        Set<String> positionSet = Sets.newHashSet(Arrays.stream(Position116Enums.values()).map(Position116Enums::getName).collect(Collectors.toList()));
        Set<String> genderSet = Sets.newHashSet(Arrays.stream(Gender116Enums.values()).map(Gender116Enums::getName).collect(Collectors.toList()));

        return videoSliceImportList.stream().filter(video -> {

            Set<ConstraintViolation<ResVideo116SliceImportReq>> violationSet = validator.validate(video, Group.class);
            if (!CollectionUtils.isEmpty(violationSet)) {
                List<String> errorInfoList = violationSet.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
                String errorInfo = JacksonUtil.toJsonString(errorInfoList);
                errorMessageList.add(String.format("%s : %s", video.getName(), errorInfo));
                return false;
            }

            if (!exerciseTypeSet.contains(video.getExerciseType())) {
                errorMessageList.add(String.format("ExerciseType value is invalid, please check it. invalid value is %s.", video.getExerciseType()));
                return false;
            }

            if (!positionSet.contains(video.getPosition())) {
                errorMessageList.add(String.format("Position value is invalid, please check it. invalid value is %s.", video.getPosition()));
                return false;
            }

            if (!genderSet.contains(video.getGender())) {
                errorMessageList.add(String.format("Gender value is invalid, please check it. invalid value is %s.", video.getGender()));
                return false;
            }

            return true;
        }).collect(Collectors.toList());
    }

    private List<ResVideo116SliceImportReq> parseResVideoSliceImport(MultipartFile file) {

        List<ResVideo116SliceImportReq> poseVideoImportList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), ResVideo116SliceImportReq.class, new AnalysisEventListener<ResVideo116SliceImportReq>() {
                @Override
                public void invoke(ResVideo116SliceImportReq row, AnalysisContext analysisContext) {

                    if (StringUtils.isNotBlank(row.getExerciseType())) {

                        String exerciseType = Stream.of(row.getExerciseType().split(GlobalConstant.COMMA))
                                .map(aliasName -> {
                                    // 导入时，exercise type 由导入别名转为name
                                    ExerciseType116Enums exerciseType116Enums = ExerciseType116Enums.getByImportAliasName(aliasName);
                                    return Objects.isNull(exerciseType116Enums) ? aliasName : exerciseType116Enums.getName();
                                }).collect(Collectors.joining(GlobalConstant.COMMA));

                        row.setExerciseType(exerciseType);
                    }

                    poseVideoImportList.add(row);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet(GlobalConstant.ZERO).doRead();
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            throw new BizException("Import res video slice error.");
        }
        return poseVideoImportList;
    }

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public void reGenerateAllM3U8() {
        log.info("reGenerateAllM3U8 start");
        List<ResVideo116> list = list();
        for (ResVideo116 resVideo116 : list) {
            String frontForM3U8R2 = videoFrontForM3U8R2(resVideo116.getFrontVideoUrl(), resVideo116.getFrontDuration());
            LambdaUpdateWrapper<ResVideo116> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ResVideo116::getVideoUrl, frontForM3U8R2);
            updateWrapper.eq(ResVideo116::getId, resVideo116.getId());
            transactionTemplate.execute((status) -> {
                //1、直接使用update(Wrapper<T> updateWrapper)无法进入 updateFill,修改为update(T entity, Wrapper<T> updateWrapper),
                //2、无法触发事件ResourceSectionListener的事务提交,因此增加事务
                update(resVideo116, updateWrapper);
                return true;
            });
        }
        log.info("reGenerateAllM3U8 finish");
    }

    /**
     * 过滤不符合业务规则的数据
     *
     * @param dataList
     * @param failMessage
     * @return
     */
    private List<ResVideo116> filterDirtyData(final List<ResVideo116ImportReq> dataList, final List<String> failMessage) {

        // 相同Exercise Type+相同Position，Name、gender不能重复，因此这里先批量查询出可能会重复的数据
        // 只查询必要的字段 id 、exercise type、position、name、gender
        List<ResVideo116> resVideo116s = getUniqueResVideo(dataList);

        List<ResVideo116> meetsCondiData = CollUtil.newArrayList();
        Optional.of(dataList).filter(CollUtil::isNotEmpty).ifPresent(data -> {
            boolean validatedMain = false;
            //name 转为 config Id
            Set<String> nameSet = new HashSet<>();
            Set<String> i18nConfigNameSet = dataList.stream().map(ResVideo116ImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
            Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                    .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));
            for (ResVideo116ImportReq req : data) {
                String exerciseType = req.getExerciseType();
                //是否可以执行后续代码
                boolean canContinue = true;
                TargetEnums target = Arrays.stream(TargetEnums.values()).filter(t -> Objects.equals(t.getName(), req.getTarget())).findAny().orElse(null);
                if (target == null) {
                    failMessage.add(req.getName() + ":target不存在");
                    canContinue = false;
                }
                if (StringUtils.isNotBlank(exerciseType)) {
                    // 存在单选时，不能选多个值
                    boolean includeSingle = Arrays.stream(exerciseType.split(GlobalConstant.COMMA)).map(ExerciseType116Enums::getByName).filter(Objects::nonNull).anyMatch(ExerciseType116Enums::isSingle);
                    if (includeSingle && exerciseType.split(GlobalConstant.COMMA).length > 1) {
                        failMessage.add(req.getName() + ":exerciseType只能选单选");
                        canContinue = false;
                    }
                    // exercise type 规则校验
                    boolean exerciseTypeNotExist = Arrays.stream(exerciseType.split(GlobalConstant.COMMA)).map(ExerciseType116Enums::getByName).anyMatch(Objects::isNull);
                    if (exerciseTypeNotExist) {
                        failMessage.add(req.getName() + ":exerciseType不存在");
                        canContinue = false;
                    }

                    // 依据exercise type 转换 equipment
                    String equipment = Arrays.stream(exerciseType.split(GlobalConstant.COMMA)).map(ExerciseType116Enums::getByName).filter(Objects::nonNull).flatMap(et -> et.getEquipmentList().stream()).map(Equipment116Enums::getName).distinct().sorted().collect(Collectors.joining(GlobalConstant.COMMA));
                    req.setEquipment(equipment);
                } else {
                    failMessage.add(req.getName() + ":exercise_type不能为空");
                    canContinue = false;
                }

                if (MAIN.getValue().equals(req.getType()) && StringUtils.isBlank(exerciseType)) {
                    if (!validatedMain) {
                        failMessage.add(req.getName() + ":Main对应的exerciseType不能为空");
                        validatedMain = true;
                        canContinue = false;
                    }
                }

                // 相同Exercise Type+相同Position + 相同Name + 相同gender 只保留第一条
                canContinue = checkIfConflicted(failMessage, resVideo116s, req, canContinue);
                // Exercise Type 为 Tai Chi 或者GENTLE_CHAIR_YOGA 时，视频信息可以为空
                if (!VIDEO_SLICE_LIST.contains(exerciseType)) {

                    if (StringUtils.isBlank(req.getFrontVideoUrl())) {
                        failMessage.add(req.getName() + "：main_url不能为空");
                        canContinue = false;
                    }
                    if (StringUtils.isBlank(req.getSideVideoUrl())) {
                        failMessage.add(req.getName() + "：assist_url不能为空");
                        canContinue = false;
                    }
                    if (Objects.isNull(req.getFrontDuration())) {
                        failMessage.add(req.getName() + "：main_duration不能为空");
                        canContinue = false;
                    }

                    if (Objects.isNull(req.getSideDuration())) {
                        failMessage.add(req.getName() + "：assist_duration不能为空");
                        canContinue = false;
                    }

                } else {
                    req.setFrontVideoUrl(null);
                    req.setSideVideoUrl(null);
                    req.setFrontDuration(null);
                    req.setSideDuration(null);
                }

                if (!canContinue) {
                    continue;
                }
                try {
                    Optional.ofNullable(validator.validate(req, Group.class)).ifPresent(result -> {
                        Optional<ConstraintViolation<ResVideo116ImportReq>> firstError = result.stream().findFirst();
                        if (firstError.isPresent()) {
                            //校验失败，只记录第一条失败原因
                            failMessage.add(req.getName() + ":" + firstError.get().getMessage());
                        }else if (!i18nConfigNameIdMap.containsKey(req.getCoreVoiceConfigI18nName())) {
                            failMessage.add(req.getName() + ": English Voice Name Not Found in TTS config");
                        }
                        else {
                            //生成video对象
                            ResVideo116 resVideo116 = new ResVideo116();
                            BeanUtils.copyProperties(req, resVideo116);
                            // target 字段传入为枚举name，这里转为code
                            resVideo116.setTarget(target);
                            // circuit 为空，设置默认值为1
                            Integer circuit = Optional.ofNullable(req.getCircuit()).map(Integer::parseInt).orElse(1);
                            resVideo116.setCircuit(circuit);
                            // met
                            Integer met = Optional.ofNullable(req.getMet()).map(Integer::parseInt).orElse(null);
                            resVideo116.setMet(met);
                            if (Objects.isNull(resVideo116.getId())) {
                                resVideo116.setStatus(GlobalConstant.STATUS_DRAFT);
                            }
                            // 为restriction设置默认值
                            if (StringUtils.isBlank(resVideo116.getRestriction())) {
                                resVideo116.setRestriction(BizConstant.NONE);
                            }
                            resVideo116.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(req.getCoreVoiceConfigI18nName()));
                            meetsCondiData.add(resVideo116);
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    failMessage.add(req.getName() + ":" + e.getMessage());
                }
            }
        });
        return meetsCondiData;
    }

    /**
     * <p>判断唯一约束</p>
     *
     * @param potentialUniqueKeyConflictVideos 可能会存在重复的数据
     * @param req                              要判断的数据
     * @return boolean
     * <AUTHOR>
     * @date 2025/3/10 18:03
     */
    private boolean hasUniqueKeyConflicted(List<ResVideo116> potentialUniqueKeyConflictVideos, ResVideo116ImportReq req) {

        return potentialUniqueKeyConflictVideos.stream().anyMatch(video -> {

            boolean otherSame = !Objects.equals(video.getId(), req.getId())
                    && Objects.equals(video.getPosition(), req.getPosition())
                    && Objects.equals(video.getName(), req.getName())
                    && Objects.equals(video.getGender(), req.getGender());

            // 如果有一方为空，只有都为都为空，才认为一致返回true
            if (StringUtils.isBlank(video.getExerciseType()) || StringUtils.isBlank(req.getExerciseType())) {
                return otherSame && Objects.equals(video.getExerciseType(), req.getExerciseType());
            }
            // 其余判断有无交集
            ArrayList<String> videoExerciseType = CollUtil.newArrayList(video.getExerciseType().split(GlobalConstant.COMMA));
            ArrayList<String> reqExerciseType = CollUtil.newArrayList(req.getExerciseType().split(GlobalConstant.COMMA));

            return otherSame && videoExerciseType.stream().anyMatch(reqExerciseType::contains);
        });


    }

    private void injectionTaskStatus(List<ResVideo116PageVO> videoList) {
        if (CollUtil.isEmpty(videoList)) {
            return;
        }

        // 切片状态处理，太极类型和非太极类型分别处理
        Map<Boolean, List<ResVideo116PageVO>> videoGroupByIsTaiChi = videoList.stream().collect(Collectors.groupingBy(video -> VIDEO_SLICE_LIST.contains(video.getExerciseType())));
        // 处理太极类型数据
        injectionTaskStatus4TaiChi(videoGroupByIsTaiChi.get(Boolean.TRUE));
        // 处理非太极类型
        injectionTaskStatus4NotTaiChi(videoGroupByIsTaiChi.get(Boolean.FALSE));
    }

    private void injectionTaskStatus4NotTaiChi(List<ResVideo116PageVO> videoList) {

        if (CollUtil.isEmpty(videoList)) {
            return;
        }

        Set<Integer> idSet = videoList.stream().map(ResVideo116PageVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(RESVIDEO_116_SIDE.getTableName(), RESVIDEO_116_SIDE.getEntityFieldName(), idSet);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(RESVIDEO_116_FRONT.getTableName(), RESVIDEO_116_FRONT.getEntityFieldName(), idSet);
        if (CollUtil.isEmpty(sideStatusList) && CollUtil.isEmpty(frontStatusList)) {
            return;
        }
        // 视频ID -> TaskResourceSection
        Map<Integer, List<TaskResourceSection>> sideMap = sideStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        Map<Integer, List<TaskResourceSection>> frontMap = frontStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        for (ResVideo116PageVO video : videoList) {
            List<TaskResourceSection> sideList = sideMap.get(video.getId());
            if (CollUtil.isNotEmpty(sideList)) {
                TaskResourceSection sideTask = sideList.get(0);
                video.setSideTaskStatus(Lists.newArrayList(sideTask.getStatus()));
            }

            List<TaskResourceSection> frontList = frontMap.get(video.getId());
            if (CollUtil.isNotEmpty(frontList)) {
                TaskResourceSection frontTask = frontList.get(0);
                video.setFrontTaskStatus(Lists.newArrayList(frontTask.getStatus()));
            }
        }
    }

    private void injectionTaskStatus4TaiChi(List<ResVideo116PageVO> videoList) {

        if (CollUtil.isEmpty(videoList)) {
            return;
        }

        Set<Integer> videoIdList = videoList.stream().map(ResVideo116PageVO::getId).collect(Collectors.toSet());
        List<ResVideo116SliceDetailVO> sliceDetailVOList = video116SliceService.listByResVideoId(videoIdList);
        Set<Integer> videoSliceIds = sliceDetailVOList.stream().map(ResVideo116SliceDetailVO::getId).collect(Collectors.toSet());

        // 获取太极视频切片的切片任务执行状态
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(RES_VIDEO116_SLICE_SIDE.getTableName(), RES_VIDEO116_SLICE_SIDE.getEntityFieldName(), videoSliceIds);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(RES_VIDEO116_SLICE_FRONT.getTableName(), RES_VIDEO116_SLICE_FRONT.getEntityFieldName(), videoSliceIds);
        if (CollectionUtils.isEmpty(sideStatusList) || CollectionUtils.isEmpty(frontStatusList)) {
            return;
        }
        // 建立视频ID -> 切片的切片任务状态集合
        videoList.forEach(video -> {
            // 该视频的切片ID
            final List<Integer> sliceId = sliceDetailVOList.stream().filter(slice -> Objects.equals(slice.getResVideo116Id(), video.getId())).map(ResVideo116SliceDetailVO::getId).collect(Collectors.toList());
            // 这些切片对应的front切片任务状态集合
            List<TaskResourceSectionStatusEnums> videoFrontStatusList = frontStatusList.stream().filter(task -> sliceId.contains(task.getTableId())).map(TaskResourceSection::getStatus).distinct().collect(Collectors.toList());
            List<TaskResourceSectionStatusEnums> videoSideStatusList = sideStatusList.stream().filter(task -> sliceId.contains(task.getTableId())).map(TaskResourceSection::getStatus).distinct().collect(Collectors.toList());
            video.setFrontTaskStatus(videoFrontStatusList);
            video.setSideTaskStatus(videoSideStatusList);
        });
    }

    /**
     * <p>这里抽取了卡路里计算函数，使用的到的常量系数不会重复创建</p>
     *
     * @return java.util.function.Consumer<com.laien.web.biz.proj.oog116.entity.ResVideo116>
     * <AUTHOR>
     * @date 2025/3/7 10:08
     */
    private Consumer<ResVideo116> fillCalorieConsumer() {
        final BigDecimal divisor = new BigDecimal(3600 * 1000);
        return resVideo116 -> {
            try {
                resVideo116.setEventName("");
                // tai chi 和 chair yoga 不计算卡路里
                if (VIDEO_SLICE_LIST.contains(resVideo116.getExerciseType())) {
                    return;
                }
                Integer frontVideoDuration = Optional.ofNullable(resVideo116.getFrontDuration()).orElse(0);
                Integer sideVideoDuration = Optional.ofNullable(resVideo116.getSideDuration()).orElse(0);
                Integer videoDuration = frontVideoDuration + sideVideoDuration + frontVideoDuration;
                BigDecimal calorie = new BigDecimal(resVideo116.getMet() * 75 * videoDuration).divide(divisor, 3, RoundingMode.HALF_UP);
                resVideo116.setCalorie(calorie);
            } catch (Exception e) {
                log.warn("calculate calorie error, resVideo116:{}", resVideo116, e);
            }
        };
    }


    @Override
    public List<ResVideo116> queryList() {
        LambdaQueryWrapper<ResVideo116> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResVideo116::getStatus, STATUS_ENABLE).eq(BaseModel::getDelFlag, NO);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<ResVideo116> listByExerciseType(List<String> exerciseTypeList) {

        if (CollectionUtils.isEmpty(exerciseTypeList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ResVideo116> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResVideo116::getStatus, STATUS_ENABLE).eq(BaseModel::getDelFlag, NO);
        List<ResVideo116> video116List = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(video116List)) {
            return Collections.emptyList();
        }

        // 按exerciseType进行分组
        Map<String, List<ResVideo116>> exerciseTypeMap = new HashMap<>();
        video116List.stream()
                .filter(video -> StringUtils.isNotBlank(video.getExerciseType()))
                .forEach(video -> {
                    for (String exerciseType : MyStringUtil.getSplitWithComa(video.getExerciseType())) {
                        List<ResVideo116> exerciseVideoList = exerciseTypeMap.getOrDefault(exerciseType, new ArrayList<>());
                        exerciseVideoList.add(video);
                        exerciseTypeMap.put(exerciseType, exerciseVideoList);
                    }
                });

        Set<ResVideo116> video116Set = exerciseTypeMap.entrySet().stream().filter(entry -> exerciseTypeList.contains(entry.getKey()))
                .map(entry -> entry.getValue()).flatMap(Collection::stream).collect(Collectors.toSet());
        return new ArrayList<>(video116Set);
    }

    @Override
    public List<String> importByExcelUpdateImage(InputStream inputStream) {

        ArrayList<ResVideo116ImportForUpdate> list = new ArrayList<>();
        EasyExcel.read(inputStream, ResVideo116ImportForUpdate.class, new AnalysisEventListener<ResVideo116ImportForUpdate>() {
            @Override
            public void invoke(ResVideo116ImportForUpdate row, AnalysisContext context) {
                Integer rowIndex = context.readRowHolder().getRowIndex();
                row.setRowNum(rowIndex + 1);
                list.add(row);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();

        // 校验数据，有异常数据，直接不更新
        List<String> failMessage = CollUtil.newArrayList();
        if (CollUtil.isEmpty(list)) {
            failMessage.add("没有读取到数据");
            return failMessage;
        }

        list.forEach(resVideo116ImportForUpdate -> {
            if (null == resVideo116ImportForUpdate.getId()) {
                failMessage.add("第" + resVideo116ImportForUpdate.getRowNum() + "行，id为空");
            }
            if (StringUtils.isBlank(resVideo116ImportForUpdate.getCoverImgUrl())) {
                failMessage.add("第" + resVideo116ImportForUpdate.getRowNum() + "行，封面图片地址为空");
            }
            if (StringUtils.isBlank(resVideo116ImportForUpdate.getFrontVideoUrl())) {
                failMessage.add("第" + resVideo116ImportForUpdate.getRowNum() + "行，前部视频地址为空");
            }
            if (StringUtils.isBlank(resVideo116ImportForUpdate.getSideVideoUrl())) {
                failMessage.add("第" + resVideo116ImportForUpdate.getRowNum() + "行，侧部视频地址为空");
            }
        });

        Set<Integer> idSet = list.stream().map(ResVideo116ImportForUpdate::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(idSet)) {
            return failMessage;
        }
        idSet = this.baseMapper.selectBatchIds(idSet).stream().map(BaseModel::getId).collect(Collectors.toSet());
        Set<Integer> finalIdSet = idSet;
        list.forEach(resVideo116ImportForUpdate -> {
            if (!finalIdSet.contains(resVideo116ImportForUpdate.getId())) {
                failMessage.add("第" + resVideo116ImportForUpdate.getRowNum() + "行，id不存在");
            }

        });

        if (CollUtil.isNotEmpty(failMessage)) {
            return failMessage;
        }
        // 逐一更新
        list.forEach(resVideo116ImportForUpdate -> {

            String suffix = "?" + System.currentTimeMillis();
            LambdaUpdateWrapper<ResVideo116> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ResVideo116::getCoverImgUrl, resVideo116ImportForUpdate.getCoverImgUrl() + suffix);
            updateWrapper.set(ResVideo116::getFrontVideoUrl, resVideo116ImportForUpdate.getFrontVideoUrl() + suffix);
            updateWrapper.set(ResVideo116::getSideVideoUrl, resVideo116ImportForUpdate.getSideVideoUrl() + suffix);
            updateWrapper.eq(ResVideo116::getId, resVideo116ImportForUpdate.getId());
            this.baseMapper.update(null, updateWrapper);
        });

        return failMessage;
    }

}
