package com.laien.web.biz.proj.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog116.entity.ProjWorkout116ResVideo116;
import com.laien.web.biz.proj.oog116.response.ProjWorkout116ExerciseDetailVO;

import java.util.List;

/**
 * <p>
 * proj_workout116_res_video116 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface ProjWorkout116ResVideo116Mapper extends BaseMapper<ProjWorkout116ResVideo116> {

    /**
     * 根据workoutId 查询workout116 exercise
     *
     * @param workoutId workoutId
     * @return list
     */
/*    @Results(id = "exerciseDetailResultMap", value = {
            @Result(column = "region", property = "region",
                    typeHandler = Region116Enums.TypeHandler.class, jdbcType = JdbcType.INTEGER),
            @Result(column = "focus", property = "focus",
                    typeHandler = Focus116Enums.TypeHandler.class, jdbcType = JdbcType.INTEGER)
    })*/
    List<ProjWorkout116ExerciseDetailVO> selectExercisesByWorkoutId(Integer workoutId);

}
