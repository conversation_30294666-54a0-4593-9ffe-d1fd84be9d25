package com.laien.web.biz.proj.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog116.enums.ProjTemplate116TaskStatusEnums;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog116.config.FixedTemplateConfig;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116;
import com.laien.web.biz.proj.oog116.entity.ProjTemplate116Task;
import com.laien.web.biz.proj.oog116.mapper.ProjTemplate116TaskMapper;
import com.laien.web.biz.proj.oog116.request.ProjTemplate116TaskReq;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116Service;
import com.laien.web.biz.proj.oog116.service.IProjTemplate116TaskService;
import com.laien.web.biz.proj.oog116.service.IProjWorkout116GenerateService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.laien.common.oog116.enums.ProjTemplate116TaskStatusEnums.FAIL;


/**
 * <p>
 * proj_template116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjTemplate116TaskServiceImpl extends ServiceImpl<ProjTemplate116TaskMapper, ProjTemplate116Task>
        implements IProjTemplate116TaskService {

    @Resource
    private IProjInfoService projInfoService;
    @Resource
    private IProjWorkout116GenerateService projWorkout116GenerateService;
    @Resource
    private IProjTemplate116Service projTemplate116Service;
    @Resource
    private FixedTemplateConfig fixedTemplateConfig;
    private final ExecutorService TASK_EXECUTOR_SERVICE = Executors.newFixedThreadPool(1);

    @EventListener(ContextRefreshedEvent.class)
    public void tryExecuteTask() {
        LambdaQueryWrapper<ProjTemplate116Task> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseModel::getDelFlag, GlobalConstant.NO)
                .eq(ProjTemplate116Task::getStatus, ProjTemplate116TaskStatusEnums.RUNNING);
        List<ProjTemplate116Task> projTemplateTaskList = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(projTemplateTaskList)) {
            return;
        }
        for (ProjTemplate116Task task : projTemplateTaskList) {
            Integer projTemplateId = task.getProjTemplate116Id();
            ProjTemplate116 template = projTemplate116Service.getById(projTemplateId);
            if (null == template) {
                changeState(task.getId(), FAIL, "template not exist");
                continue;
            }
            if (GlobalConstant.STATUS_ENABLE != template.getStatus()) {
                changeState(task.getId(), FAIL, "template not enable");
                continue;
            }
            ProjInfo projInfo = projInfoService.getById(template.getProjId());
            TASK_EXECUTOR_SERVICE.execute(() -> {
                projWorkout116GenerateService.generate(projTemplateId, task.getId(), projInfo);
            });
        }
    }

    @Override
    public void add(ProjTemplate116TaskReq taskReq, Integer projectId) {
        Integer templateId = taskReq.getProjTemplate116Id();
        if(fixedTemplateConfig.getFixedTemplateIdList().contains(templateId)){
            throw new BizException("this fixed template not can generate");
        }
        LambdaQueryWrapper<ProjTemplate116Task> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjTemplate116Task::getProjTemplate116Id, templateId)
                .eq(ProjTemplate116Task::getStatus, ProjTemplate116TaskStatusEnums.RUNNING)
                .eq(BaseModel::getDelFlag, GlobalConstant.NO);
        Integer count = baseMapper.selectCount(wrapper);
        if (null != count && count > 0) {
            throw new BizException("has task in progress, please try again later");
        }
        ProjTemplate116Task task = new ProjTemplate116Task();
        BeanUtils.copyProperties(taskReq, task);
        ProjInfo projInfo = projInfoService.getById(projectId);
        baseMapper.insert(task);
        TASK_EXECUTOR_SERVICE.execute(() -> {
            projWorkout116GenerateService.generate(templateId, task.getId(), projInfo);
        });
    }

    @Override
    public void changeState(Integer id, ProjTemplate116TaskStatusEnums status, String failureMessage) {
        LambdaUpdateWrapper<ProjTemplate116Task> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjTemplate116Task::getStatus, status)
                .set(ProjTemplate116Task::getFailureMessage, failureMessage)
                .eq(BaseModel::getId, id);
        baseMapper.update(new ProjTemplate116Task(), wrapper);
    }

    @Override
    public List<ProjTemplate116Task> selectLastTask(List<Integer> templateIdList) {
        return this.baseMapper.selectLastTask(templateIdList);
    }
}
