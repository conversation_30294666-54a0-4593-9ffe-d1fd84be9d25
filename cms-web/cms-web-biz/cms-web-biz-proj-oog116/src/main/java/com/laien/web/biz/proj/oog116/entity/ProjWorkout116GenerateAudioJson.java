package com.laien.web.biz.proj.oog116.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 116生成的workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116GenerateAudioJson对象", description="116生成的workout")
public class ProjWorkout116GenerateAudioJson extends BaseModel {

    private static final long serialVersionUID = 1L;

    private Integer projWorkout116GenerateId;

    private String audioJson;

}
