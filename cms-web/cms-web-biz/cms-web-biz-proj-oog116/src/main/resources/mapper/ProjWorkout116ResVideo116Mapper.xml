<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog116.mapper.ProjWorkout116ResVideo116Mapper">

    <!-- 定义resultMap处理TypeHandler字段 -->
    <resultMap id="exerciseDetailResultMap" type="com.laien.web.biz.proj.oog116.response.ProjWorkout116ExerciseDetailVO">
        <result column="region" property="region" typeHandler="com.laien.common.oog116.enums.Region116Enums$TypeHandler"/>
        <result column="focus" property="focus" typeHandler="com.laien.common.oog116.enums.Focus116Enums$TypeHandler"/>
    </resultMap>

    <select id="selectExercisesByWorkoutId" resultMap="exerciseDetailResultMap">
        SELECT
            v.id,
            v.`name`,
            v.cover_img_url,
            v.instructions,
            v.core_voice_config_i18n_id,
            v.guidance,
            v.type,
            v.position,
            v.restriction,
            v.front_video_url,
            v.front_duration,
            v.side_video_url,
            v.side_duration,
            v.video_url,
            v.name_audio_url,
            v.guidance_audio_url,
            v.instructions_audio_url,
            v.met,
            v.calorie,
            v.`status`,
            v.gender,
            v.equipment,
            v.exercise_type,
            v.region,
            v.focus,
            v.support_prop,
            v.front_m3u8_text2k,
            v.front_m3u8_text1080p,
            v.front_m3u8_text720p,
            v.front_m3u8_text480p,
            v.front_m3u8_text360p,
            v.side_m3u8_text2k,
            v.side_m3u8_text1080p,
            v.side_m3u8_text720p,
            v.side_m3u8_text480p,
            v.side_m3u8_text360p,
            v.front_m3u8_text2532,
            v.side_m3u8_text2532,
            wv.circuit,
            wv.unit_name,
            wv.rounds
        FROM
            proj_workout116_res_video116 wv
                INNER JOIN res_video116 v ON v.id = wv.res_video116_id
        WHERE
            wv.del_flag = 0
          AND wv.proj_workout116_id = #{workoutId}
        order by wv.id
    </select>

</mapper>
