<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog116.mapper.ProjTemplate116TaskMapper">

    <select id="selectLastTask" resultType="com.laien.web.biz.proj.oog116.entity.ProjTemplate116Task">
        SELECT
            *
        FROM
            proj_template116_task
        WHERE
        id IN (
            SELECT
                max( id )
            FROM
                proj_template116_task
            WHERE
                del_flag = 0
            <foreach collection="list" item="item" open="and proj_template116_id in (" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY
            proj_template116_id
        )
    </select>

</mapper>