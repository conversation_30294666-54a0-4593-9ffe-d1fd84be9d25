<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog116.mapper.ProjCategory116ProjWorkout116Mapper">

    <select id="selectWorkoutStatusCount" resultType="com.laien.web.frame.response.IdAndStatusCountsRes">
        SELECT
            c.proj_category116_id AS id,
            w.`status`,
            count(*) counts
        FROM
            proj_category116_proj_workout116 c
                INNER JOIN proj_workout116 w ON w.id = c.proj_workout116_id
        WHERE
            w.proj_id = #{projId}
          AND c.del_flag = 0
          AND w.del_flag = 0
        GROUP BY
            c.proj_category116_id,
            w.`status`
    </select>

    <select id="selectWorkoutByCategoryId" resultType="com.laien.web.biz.proj.oog116.response.ProjCategory116DetailWorkoutVO">
        SELECT
            w.id,
            w.`name`,
            w.event_name,
            w.cover_img_url,
            w.difficulty,
            w.position,
            w.restriction,
            w.languages,
            w.calorie,
            w.duration,
            w.`status`
        FROM
            proj_category116_proj_workout116 cw
                INNER JOIN proj_workout116 w ON cw.proj_workout116_id = w.id
        WHERE
            cw.del_flag = 0
          AND w.del_flag = 0
          AND cw.proj_category116_id = #{categoryId}
    </select>


</mapper>