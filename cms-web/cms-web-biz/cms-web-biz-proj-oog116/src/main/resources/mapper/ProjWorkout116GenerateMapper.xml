<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog116.mapper.ProjWorkout116GenerateMapper">

    <sql id="queryTemplate">
        FROM proj_workout116_generate wg
        JOIN proj_workout116_generate_res_video116 v ON v.proj_workout116_generate_id = wg.id
        WHERE
        wg.del_flag = 0
        AND v.del_flag = 0
        <if test="templateIdList != null and templateIdList.size() > 0">
            AND wg.proj_template116_id IN
            <foreach item="item" collection="templateIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReq.id != null">
            AND wg.id = #{pageReq.id}
        </if>

        <if test="pageReq.position != null and pageReq.position != ''">
            AND wg.position = #{pageReq.position}
        </if>

        <if test="restrictionSum != null">
            AND wg.restriction_sum = #{restrictionSum}
        </if>

        <if test="pageReq.equipment != null and pageReq.equipment != ''">
            AND wg.equipment = #{pageReq.equipment}
        </if>

        <if test="pageReq.exerciseType != null and pageReq.exerciseType != ''">
            AND wg.exercise_type = #{pageReq.exerciseType}
        </if>

        <if test="pageReq.gender != null and pageReq.gender != ''">
            AND wg.gender = #{pageReq.gender}
        </if>

        <if test="pageReq.fileStatus != null">
            AND wg.file_status = #{pageReq.fileStatus}
        </if>
        <if test="pageReq.videoIdList != null and pageReq.videoIdList.size() > 0">
            AND v.res_video116_id IN
            <foreach item="item" collection="pageReq.videoIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY wg.id
        ORDER BY wg.id DESC
    </sql>

    <select id="selectCountByTemplateIds" resultType="com.laien.web.frame.response.IdAndCountsRes">
        SELECT
            count(*) counts,
            proj_template116_id id
        FROM
            proj_workout116_generate
        WHERE
            del_flag = 0
        <foreach collection="list" item="item" open="and proj_template116_id in (" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        proj_template116_id
    </select>
    <select id="page" resultType="java.lang.Integer">
        SELECT
            wg.id
        <include refid="queryTemplate" />

    </select>
    <select id="list" resultType="java.lang.Integer">
        SELECT
            wg.id
        <include refid="queryTemplate" />
    </select>

</mapper>