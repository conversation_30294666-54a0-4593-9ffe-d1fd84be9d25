<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cms-web</artifactId>
        <groupId>com.laien</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cms-web-biz</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>cms-web-biz-core</module>
        <module>cms-web-biz-resource</module>
        <module>cms-web-biz-proj-core</module>
        <module>cms-web-biz-proj-oog101</module>
        <module>cms-web-biz-proj-oog104</module>
        <module>cms-web-biz-proj-oog116</module>
        <module>cms-web-biz-proj-oog200</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-frame</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-common</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-lms-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-common-lms</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz-resource</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz-proj-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz-proj-oog101</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz-proj-oog104</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz-proj-oog116</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-biz-proj-oog200</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
