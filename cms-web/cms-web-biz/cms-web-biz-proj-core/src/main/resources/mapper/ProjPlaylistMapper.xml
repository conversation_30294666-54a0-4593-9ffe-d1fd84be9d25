<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.core.mapper.ProjPlaylistMapper">

    <select id="selectListByProjId" resultType="com.laien.web.biz.proj.core.response.ProjPlaylistListVO">
        SELECT
            pl.id,
            pl.playlist_type,
            pl.playlist_name,
            pl.phone_cover_img_url,
            pl.phone_detail_img_url,
            pl.create_user,
            pl.create_time,
            pl.update_user,
            pl.update_time,
            pl.subscription,
            COUNT( plm.proj_playlist_id ) musicNum,
            pl.status
        FROM
            proj_playlist pl
            JOIN proj_playlist_music plm ON pl.id = plm.proj_playlist_id
        WHERE
            pl.del_flag = 0
            AND plm.del_flag = 0
            <if test="projId != null">
                AND pl.proj_id = #{projId}
            </if>
            <if test="status != null">
                AND pl.status = #{status}
            </if>
<!--            <if test="myPage.playlistName != null">-->
<!--                AND pl.playlist_name like CONCAT('%', #{myPage.playlistName},'%')-->
<!--            </if>-->
<!--            <if test="myPage.subscription != null">-->
<!--                AND pl.subscription = #{myPage.subscription}-->
<!--            </if>-->
        GROUP BY
            plm.proj_playlist_id
        order by pl.sort_no desc,pl.create_time desc
    </select>
</mapper>