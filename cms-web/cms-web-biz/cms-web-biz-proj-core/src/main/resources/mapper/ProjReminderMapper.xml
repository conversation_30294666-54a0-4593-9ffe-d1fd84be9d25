<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.core.mapper.ProjReminderMapper">

    <select id="selectListByProjId" resultType="com.laien.web.biz.proj.core.response.ProjReminderListVO">
        SELECT
            rr.id,
            rr.title,
            rr.content
        FROM
            proj_reminder pr
        JOIN res_reminder rr ON pr.res_reminder_id = rr.id
        WHERE
            pr.del_flag = 0
        <if test="projId != null">
                AND pr.proj_id = #{projId}
            </if>
<!--            <if test="myPage.playlistName != null">-->
<!--                AND pl.playlist_name like CONCAT('%', #{myPage.playlistName},'%')-->
<!--            </if>-->
<!--            <if test="myPage.subscription != null">-->
<!--                AND pl.subscription = #{myPage.subscription}-->
<!--            </if>-->
    </select>
</mapper>