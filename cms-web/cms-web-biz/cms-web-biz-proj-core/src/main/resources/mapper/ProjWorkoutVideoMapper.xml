<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.core.mapper.ProjWorkoutVideoMapper">

    <select id="selectWorkoutVideoPage" resultType="com.laien.web.biz.proj.core.response.ProjWorkoutVideoPageVO">
        SELECT
            wv.id,
            wv.scene_id,
            wv.workout_name,
            wv.cover_img_url,
            wv.focus,
            wv.calorie,
            wv.duration,
            wv.subscription,
            wv.new_start_time,
            wv.new_end_time,
            wv.`status`,
            ws.scene_name
        FROM
            proj_workout_video wv
        INNER JOIN proj_workout_scene ws ON wv.scene_id = ws.id
        <where>
            wv.del_flag = 0
            AND wv.proj_id = #{myPage.projId}
            <if test="myPage.sceneIdArr != null and myPage.sceneIdArr.length > 0">
                <foreach collection="myPage.sceneIdArr" item="t" open=" AND wv.scene_id in (" separator="," close=")">
                    #{t}
                </foreach>
            </if>
            <if test="myPage.status != null">
                AND wv.`status` = #{myPage.status}
            </if>
            <if test="myPage.focusArr != null and myPage.focusArr.length > 0">
                <foreach collection="myPage.focusArr" item="t" open=" AND wv.focus in (" separator="," close=")">
                    #{t}
                </foreach>
            </if>
            <if test="myPage.workoutName != null and myPage.workoutName != ''">
                AND wv.workout_name like CONCAT('%', #{myPage.workoutName},'%')
            </if>
            <if test="myPage.subscription != null">
                AND wv.`subscription` = #{myPage.subscription}
            </if>
        </where>
        ORDER BY wv.id DESC
    </select>


</mapper>