<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.core.mapper.LanguageMapper">

    <insert id="insertLanguage">
        INSERT INTO ${tableName}_i18n
        <foreach collection="column.keys" item="key" open="(" close=")" separator=",">
            ${key}
        </foreach>
        VALUES
        <foreach collection="column.values" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
    </insert>

</mapper>