<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.core.mapper.ProjPublishLogMapper">

    <insert id="insertResToCmsApp">
        INSERT INTO ${tableName}_pub SELECT #{version} version, t.* FROM ${tableName} t
    </insert>

    <insert id="insertProjToCmsApp">
        INSERT INTO ${tableName}_pub SELECT #{version} version, t.* FROM ${tableName} t WHERE t.proj_id=${projId} AND del_flag = 0
    </insert>

    <insert id="insertProjRelationToCmsApp">
        INSERT INTO ${tableName}_pub SELECT #{version} version, t.* FROM ${tableName} t
        INNER JOIN ${mainTableName} t1 ON t1.id = t.${relationIdName}
        WHERE t1.proj_id = ${projId} AND t.del_flag = 0 AND t1.del_flag = 0
    </insert>

    <delete id="deleteProjToCmsApp">
        DELETE from ${tableParam.tableName}_pub t WHERE t.proj_id=${tableParam.projId}
        <if test="inOrNot">
            AND t.version in
        </if>
        <if test="!inOrNot">
            AND t.version not in
        </if>
        <foreach collection="versionList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteProjRelationToCmsApp">
        DELETE t
        FROM
        ${tableParam.tableName}_pub t
        INNER JOIN ${tableParam.mainTableName}_pub t1 ON t1.id = t.${tableParam.relationIdName}
        AND t.version = t1.version
        WHERE
        t1.proj_id = ${tableParam.projId}
        <if test="inOrNot">
            AND t.version in
        </if>
        <if test="!inOrNot">
            AND t.version not in
        </if>
        <foreach collection="versionList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectDetailIds" resultType="integer">
        SELECT id FROM ${tableName} WHERE proj_id=#{projId} and version=#{version} AND del_flag = 0
    </select>

</mapper>