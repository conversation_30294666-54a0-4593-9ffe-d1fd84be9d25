package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ResRegularVideo;
import com.laien.web.biz.proj.core.request.ResRegularVideoAddReq;
import com.laien.web.biz.proj.core.request.ResRegularVideoReq;
import com.laien.web.biz.proj.core.request.ResRegularVideoUpdateReq;
import com.laien.web.biz.proj.core.response.ResRegularVideoDetailVO;
import com.laien.web.biz.proj.core.response.ResRegularVideoPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * regular video 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
public interface IResRegularVideoService extends IService<ResRegularVideo> {

    /**
     * regular video 分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ResRegularVideoPageVO> selectRegularVideoPage(ResRegularVideoReq pageReq);

    /**
     * regular video 保存
     *
     * @param regularVideoAddReq regularVideoAddReq
     */
    void saveRegularVideo(ResRegularVideoAddReq regularVideoAddReq);

    /**
     * regular video 修改
     *
     * @param regularVideoUpdateReq regularVideoUpdateReq
     */
    void updateRegularVideo(ResRegularVideoUpdateReq regularVideoUpdateReq);

    /**
     * regular video 详情
     *
     * @param id id
     * @return ResRegularVideoDetailVO
     */
    ResRegularVideoDetailVO getRegularVideoDetail(Integer id);

    /**
     * regular video 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * regular video 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * regular video 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

}
