package com.laien.web.biz.proj.core.util;

import com.laien.common.core.enums.IEnumBase;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>SQL片段生成工具</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/11 11:20
 */
public class SqlSnippetUtil {

    private SqlSnippetUtil() {}
    
    /**
     * <p>多选交集为真的查询时，生成多个findInSetOr sql片段</p>
     *
     * @param enums 多选枚举
     * @param columnName 多选列名
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/2/11 11:27
     */
    public static String generateFindInSetOr(List<? extends IEnumBase> enums, String columnName){

        return enums.stream()
                .map(enumBase -> String.format("FIND_IN_SET('%s', %s)", enumBase.getCode(), columnName))
                .collect(Collectors.joining(" or "));
    }

    /**
     * <p>多选交集为真的查询时，生成多个findInSetAnd sql片段</p>
     *
     * @param enums 多选枚举
     * @param columnName 多选列名
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/2/11 11:27
     */
    public static String generateFindInSetAnd(List<? extends IEnumBase> enums, String columnName){

        return enums.stream()
                .map(enumBase -> String.format("FIND_IN_SET('%s', %s)", enumBase.getCode(), columnName))
                .collect(Collectors.joining(" and "));
    }

}
