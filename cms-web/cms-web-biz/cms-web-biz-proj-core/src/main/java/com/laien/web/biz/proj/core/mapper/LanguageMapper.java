package com.laien.web.biz.proj.core.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * note: 多语言处理
 *
 * <AUTHOR>
 */
public interface LanguageMapper {

    /**
     * 多语言保存
     *
     * @param tableName 表名
     * @param column 列 和 列值
     * @return int
     */
    int insertLanguage(@Param("tableName") String tableName, @Param("column") Map<String, Object> column);

}
