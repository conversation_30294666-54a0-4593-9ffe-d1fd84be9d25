package com.laien.web.biz.proj.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.request.ProjInfoSaveReq;
import com.laien.web.biz.proj.core.response.ProjInfoDetailRes;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.common.user.service.ISysPermsService;
import com.laien.web.common.user.vo.PermsVO;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

;

/**
 * <p>
 * 项目信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Api(tags = "项目管理:基本信息")
@RestController
@RequestMapping("/proj/info")
public class ProjInfoController extends ResponseController {

    @Resource
    private IProjInfoService projInfoService;

    @Resource
    private ISysPermsService sysPermsService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjInfo>> page(PageReq pageReq) {
        //先需要查看用户拥有的项目权限
        List<PermsVO> myOpPerms = sysPermsService.getMyOpPerms();
        Set<Integer> projIds = Sets.newHashSet();
        for (PermsVO myOpPerm : myOpPerms) {
            String proj_su = "proj_";
            if (myOpPerm.getPermsKey().startsWith(proj_su)) {
                projIds.add(Integer.parseInt(StringUtils.substringBefore(StringUtils.substringAfterLast(myOpPerm.getPermsKey(), proj_su), "_")));
            }
        }
        if (CollectionUtils.isEmpty(projIds)) {
            return succ(new PageRes<>());
        }
        LambdaQueryWrapper<ProjInfo> queryWrapper = new LambdaQueryWrapper<>();
        // 排序
        queryWrapper.in(CollectionUtils.isNotEmpty(projIds), ProjInfo::getId, projIds);
        queryWrapper.orderByAsc(ProjInfo::getAppCode);
        // 查询
        Page<ProjInfo> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize(/**/));
        projInfoService.page(page, queryWrapper);
        PageRes<ProjInfo> pageResult = PageConverter.convert(page);
        // 返回
        return succ(pageResult);
    }

    @ApiOperation(value = "list")
    @GetMapping("/list")
    public ResponseResult<List<ProjInfo>> list() {
        LambdaQueryWrapper<ProjInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(ProjInfo::getAppCode);
        return succ(projInfoService.list(queryWrapper));
    }


    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjInfoDetailRes> detail(@PathVariable Integer id) {
        ProjInfoDetailRes detailRes = projInfoService.selectProjDetail(id);
        return succ(detailRes);
    }

    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjInfoSaveReq saveReq) {
        projInfoService.saveProj(saveReq);
        return succ();
    }


    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjInfoSaveReq saveReq) {
        projInfoService.updateProj(saveReq);
        return succ();
    }
}
