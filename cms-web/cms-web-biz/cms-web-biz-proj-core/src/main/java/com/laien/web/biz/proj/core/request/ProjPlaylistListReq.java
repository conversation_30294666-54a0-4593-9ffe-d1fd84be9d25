package com.laien.web.biz.proj.core.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "播放列表查询请求", description = "播放列表查询请求")
public class ProjPlaylistListReq {

//    @ApiModelProperty(value = "列表名称")
//    private String playlistName;
//
//    @ApiModelProperty(value = "是否收费 0不收费 1收费")
//    private Integer subscription;

//    @ApiModelProperty(value = "项目id",required = true)
//    private Integer projId;
}
