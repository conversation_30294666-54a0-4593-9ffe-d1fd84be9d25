package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * workout scene
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkoutScene对象", description="workout scene")
public class ProjWorkoutScene extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景类型")
    private String sceneType;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
