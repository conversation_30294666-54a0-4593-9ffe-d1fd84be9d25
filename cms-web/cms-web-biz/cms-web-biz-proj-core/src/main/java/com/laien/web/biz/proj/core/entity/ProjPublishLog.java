package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 发布日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPublishLog对象", description="发布日志")
public class ProjPublishLog extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "是否是预发布")
    private Integer prePublish;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发布结果")
    private String result;

    @ApiModelProperty(value = "失败原因")
    private String failReason;


}
