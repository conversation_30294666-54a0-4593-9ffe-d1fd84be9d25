/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.core.entity;

import com.laien.web.biz.proj.core.workout.WorkoutGenerateBizEnum;
import com.laien.web.biz.proj.core.workout.WorkoutGeneratorTaskStatusEnum;
import com.laien.web.frame.entity.BaseModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>workout生成任务 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/1/6 15:21
 */
@Getter
@Setter
@Accessors(chain =true)
@NoArgsConstructor
public class ProjWorkoutGenerateTask extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**  项目ID */
    private Integer projId;
    /**
     * workout生成业务标识，{@link WorkoutGenerateBizEnum#getCode()}
     */
    private Integer bizCode;
    /**  分组ID，用于在同一个生成业务下做任务区分，例如106项目不同的模板 */
    private Integer groupId;
    /**
     * 扩展参数，将具体app传入的扩展数据转为JSON保存
     */
    private String extendDataJson;
    /**
     * 任务执行状态, {@link WorkoutGeneratorTaskStatusEnum#getCode()}
     */
    private Integer status;
    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;
    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;
    /**
     * 任务执行过程中产生的信息记录，如异常信息等
     */
    private String message;

}