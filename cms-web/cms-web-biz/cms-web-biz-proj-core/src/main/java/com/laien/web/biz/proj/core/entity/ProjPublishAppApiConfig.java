package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 发布app api 配置 用于清缓存
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPublishAppApiConfig对象", description="发布app api 配置 用于清缓存")
public class ProjPublishAppApiConfig extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "域名")
    private String domainName;

    @ApiModelProperty(value = "api 接口")
    private String api;

    @ApiModelProperty(value = "api 类型，list, detail....")
    private String apiType;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "备注")
    private String remark;


}
