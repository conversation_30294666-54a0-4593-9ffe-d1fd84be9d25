package com.laien.web.biz.proj.core.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * note: regular video 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "regular video 详情", description = "regular video 详情")
public class ResRegularVideoDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String exerciseName;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "met")
    private Integer met;

    @ApiModelProperty(value = "必备")
    private String equipment;

    @ApiModelProperty(value = "目的")
    private String[] targetArr;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "视频预览")
    private String videoPreviewUrl;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "音频预览")
    private String audioPreviewUrl;

    @ApiModelProperty(value = "音频地址")
    private String audioUrl;

    @ApiModelProperty(value = "状态")
    private Integer status;

}
