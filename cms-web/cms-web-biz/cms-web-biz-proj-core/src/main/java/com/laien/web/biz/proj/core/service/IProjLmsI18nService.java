package com.laien.web.biz.proj.core.service;

import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.biz.proj.core.entity.ProjInfo;

import java.util.List;

/**
 * <p>
 * 国际化表 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/25
 */
public interface IProjLmsI18nService {

    /**
     * 处理国际化数据
     *
     * @param i18nList
     * @param projInfo
     */
    void handleI18n(List<? extends CoreI18nModel> i18nList, ProjInfo projInfo);

    void handleI18n(List<? extends CoreI18nModel> i18nList, Integer projId);

    void handleI18n(List<? extends CoreI18nModel> i18nList);
}
