package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目请求地址
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjAppRequestUri对象", description="项目请求地址")
public class ProjAppRequestUri extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "请求地址")
    private String requestUri;

    @ApiModelProperty(value = "请求domain")
    private String requestDomain;

    @ApiModelProperty(value = "完整请求地址Md5")
    private String requestUrlMd5;

    @ApiModelProperty(value = "状态 0-正常 1-已删除")
    private Integer status;



}
