package com.laien.web.biz.proj.core.controller;


import com.laien.web.biz.proj.core.entity.ProjWorkoutVideo;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoAddReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoPageReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoUpdateReq;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoDetailVO;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoPageVO;
import com.laien.web.biz.proj.core.service.IProjWorkoutVideoService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * workout video 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Api(tags = "项目管理:workoutVideo")
@RestController
@RequestMapping("/proj/workoutVideo")
public class ProjWorkoutVideoController extends ResponseController {

    @Resource
    private IProjWorkoutVideoService projWorkoutVideoService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjWorkoutVideoPageVO>> page(ProjWorkoutVideoPageReq pageReq) {
        PageRes<ProjWorkoutVideoPageVO> pageRes = projWorkoutVideoService.selectWorkoutVideoPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@RequestBody ProjWorkoutVideoAddReq workoutVideoAddReq) {
        projWorkoutVideoService.saveWorkoutVideo(workoutVideoAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@RequestBody ProjWorkoutVideoUpdateReq workoutVideoUpdateReq) {
        projWorkoutVideoService.updateWorkoutVideo(workoutVideoUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjWorkoutVideoDetailVO> detail(@PathVariable Integer id) {
        ProjWorkoutVideoDetailVO detailVO = projWorkoutVideoService.selectWorkoutVideoDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkoutVideoService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkoutVideoService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkoutVideoService.deleteByIds(idList);
        return succ();
    }



}
