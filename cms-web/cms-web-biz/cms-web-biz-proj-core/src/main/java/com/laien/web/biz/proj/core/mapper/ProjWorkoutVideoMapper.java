package com.laien.web.biz.proj.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.core.entity.ProjWorkoutVideo;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoPageReq;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoPageVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * workout video Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
public interface ProjWorkoutVideoMapper extends BaseMapper<ProjWorkoutVideo> {

    /**
     * workout video分页
     *
     * @param page page
     * @param pageReq pageReq
     * @return Page
     */
    Page<ProjWorkoutVideoPageVO> selectWorkoutVideoPage(Page<ProjWorkoutVideoPageVO> page, @Param("myPage") ProjWorkoutVideoPageReq pageReq);

}
