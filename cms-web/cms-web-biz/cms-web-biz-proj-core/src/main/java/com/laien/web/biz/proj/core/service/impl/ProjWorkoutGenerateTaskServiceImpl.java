package com.laien.web.biz.proj.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask;
import com.laien.web.biz.proj.core.mapper.ProjWorkoutGenerateTaskMapper;
import com.laien.web.biz.proj.core.service.IProjWorkoutGenerateTaskService;
import com.laien.web.biz.proj.core.workout.IWorkoutGenerator;
import com.laien.web.biz.proj.core.workout.WorkoutGenerateBizEnum;
import com.laien.web.biz.proj.core.workout.WorkoutGenerateParam;
import com.laien.web.biz.proj.core.workout.WorkoutGeneratorTaskStatusEnum;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>workout业务接口实现</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 17:16
 */
@Getter
@Slf4j
@Service
public class ProjWorkoutGenerateTaskServiceImpl extends ServiceImpl<ProjWorkoutGenerateTaskMapper, ProjWorkoutGenerateTask> implements IProjWorkoutGenerateTaskService {

    @Resource
    private ApplicationContext applicationContext;
    private ThreadPoolTaskExecutor workoutGenerateTaskExecutor;
    private final Map<Integer,ProjWorkoutGenerateTask> runningTasks = new ConcurrentHashMap<>();
    @Resource
    private DiscoveryClient discoveryClient;

    @PostConstruct
    public void init() {

        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(1);
        taskExecutor.setMaxPoolSize(1);
        taskExecutor.setQueueCapacity(1 << 9);
        taskExecutor.setKeepAliveSeconds(10 * 60);
        taskExecutor.setThreadNamePrefix("workout-generator-thread-");
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(60);
        taskExecutor.initialize();
        this.workoutGenerateTaskExecutor = taskExecutor;
    }

    @PreDestroy
    public void shutDown() {

        getWorkoutGenerateTaskExecutor().shutdown();
    }

    /**
     * <p>获取正在当前节点运行的生成任务</p>
     *
     * @return java.util.Map<java.lang.Integer,com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask>
     * <AUTHOR>
     * @date 2025/1/14 10:11
     */
    @Override
    public Map<Integer,ProjWorkoutGenerateTask> getDeepCopyOfRunningTasks(){
        // 使用JSON深拷贝，避免被外部修改
        return JSON.parseObject(JSON.toJSONString(this.getRunningTasks()), new TypeReference<Map<Integer, ProjWorkoutGenerateTask>>() {});
    }

    /**
     * <p>提交workout生成</p>
     *
     * @param workoutGenerateParam 任务提交参数
     * @return com.laien.cms.workout.generate.WorkoutGeneratorTask 将任务返回给调用方，方便它获取状态信息等
     * <AUTHOR>
     * @date 2024/12/27 14:47
     */
    @Override
    public final <T> ProjWorkoutGenerateTask submit(WorkoutGenerateParam<T> workoutGenerateParam) {

        // 查找项目对应生成器
        getApplicationContext().getBeansOfType(IWorkoutGenerator.class).values().stream()
                .filter(item -> Objects.equals(item.workoutGenerateBiz(), workoutGenerateParam.getWorkoutGenerateBiz()))
                .findAny().orElseThrow(()->new BizException(String.format("generator of project %s is not exist!", workoutGenerateParam.getWorkoutGenerateBiz())));
        // 任务写入数据库
        ProjWorkoutGenerateTask taskEntity = new ProjWorkoutGenerateTask()
                .setProjId(workoutGenerateParam.getProjId())
                .setBizCode(workoutGenerateParam.getWorkoutGenerateBiz().getCode())
                .setExtendDataJson(JacksonUtil.toJsonString(workoutGenerateParam.getExtendData()))
                .setGroupId(workoutGenerateParam.getGroupId())
                .setStatus(WorkoutGeneratorTaskStatusEnum.PENDING.getCode());
        this.save(taskEntity);
        // 任务保存后，提交线程池执行
        getWorkoutGenerateTaskExecutor().execute(()->this.runTask(taskEntity));
        return taskEntity;
    }

    /**
     * <p>执行任务</p>
     *
     * @param taskEntity 任务实体
     * <AUTHOR>
     * @date 2025/1/14 14:04
     */
    private void runTask(ProjWorkoutGenerateTask taskEntity){

        // 将当前正在执行的任务，加入执行记录
        this.getRunningTasks().put(taskEntity.getId(), taskEntity);
        try {
            // 任务状态改为执行中
            this.updateStatus(taskEntity.getId(), WorkoutGeneratorTaskStatusEnum.RUNNING,"");
            WorkoutGenerateBizEnum workoutGenerateBiz = WorkoutGenerateBizEnum.getByCode(taskEntity.getBizCode()).orElseThrow(() -> new BizException(String.format("workout generator task %s biz is not exist!", taskEntity.getId())));
            // 查询项目对应的生成器
            IWorkoutGenerator workoutGenerator = getApplicationContext().getBeansOfType(IWorkoutGenerator.class).values().stream()
                    .filter(item -> Objects.equals(item.workoutGenerateBiz(), workoutGenerateBiz))
                    .findAny().orElseThrow(() -> new BizException(String.format("generator of project %s is not exist!", workoutGenerateBiz.name())));
            // 获取生成器对应的扩展数据类型
            Class<?> extendDataClass = Stream.of(workoutGenerator.getClass().getGenericInterfaces())
                    .filter(type -> type instanceof ParameterizedType)
                    .filter(type -> ((ParameterizedType) type).getRawType().equals(IWorkoutGenerator.class))
                    .map(type -> (Class) ((ParameterizedType) type).getActualTypeArguments()[0])
                    .findAny()
                    .orElseThrow(() -> new BizException(String.format("workout generator task %s type is not exist!", taskEntity.getId())));
            // 组装生成参数，提交生成器生成
            WorkoutGenerateParam param = new WorkoutGenerateParam<>()
                    .setWorkoutGenerateTaskId(taskEntity.getId())
                    .setWorkoutGenerateBiz(workoutGenerateBiz)
                    .setExtendData(JSON.parseObject(taskEntity.getExtendDataJson(), extendDataClass));
            workoutGenerator.generate(param);
            this.updateStatus(taskEntity.getId(), WorkoutGeneratorTaskStatusEnum.SUCCESS,null);
        } catch (Exception e) {
            log.error("workout generator task {} execute failed!",taskEntity.getId(), e);
            // 任务执行失败，修改任务状态为失败
            this.updateStatus(taskEntity.getId(), WorkoutGeneratorTaskStatusEnum.FAIL, e.getMessage());
         } finally {
            // 任务执行结束，移除从当前正在执行的任务中移除
            this.getRunningTasks().remove(taskEntity.getId());
        }
    }
    
    /**
     * <p>更新任务状态</p>
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @param messageForAppend 追加的执行信息
     * <AUTHOR>
     * @date 2025/1/14 16:02
     */
    private void updateStatus(Integer taskId,WorkoutGeneratorTaskStatusEnum status,String messageForAppend){

        LambdaUpdateWrapper<ProjWorkoutGenerateTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjWorkoutGenerateTask::getStatus, status.getCode());
        updateWrapper.set(WorkoutGeneratorTaskStatusEnum.RUNNING.equals(status),ProjWorkoutGenerateTask::getStartTime, LocalDateTime.now());
        updateWrapper.set(WorkoutGeneratorTaskStatusEnum.SUCCESS.equals(status),ProjWorkoutGenerateTask::getEndTime, LocalDateTime.now());
        updateWrapper.set(WorkoutGeneratorTaskStatusEnum.FAIL.equals(status),ProjWorkoutGenerateTask::getEndTime, LocalDateTime.now());
        if(StringUtils.isNotBlank(messageForAppend)){
            String message = getBaseMapper().selectById(taskId).getMessage() + "," + messageForAppend;
            updateWrapper.set(ProjWorkoutGenerateTask::getMessage, message);
        }

        updateWrapper.eq(ProjWorkoutGenerateTask::getId, taskId);
        getBaseMapper().update(new ProjWorkoutGenerateTask(), updateWrapper);
    }

    /**
     * <p>查询指定项目下，limit数量的已提交任务，按提交时间倒序</p>
     *
     * @param workoutGenerateBizEnum 项目编号,不可空
     * @param limit 限制数量，可空，为空默认查询1个
     * @param statusList 任务状态，可空，为空查询所有状态
     * @param groupIdList 分组ID，可空
     * @return com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask
     * <AUTHOR>
     * @date 2024/12/27 14:46
     */
    @Override
    public final List<ProjWorkoutGenerateTask> getSubmittedTasksWithLimit(WorkoutGenerateBizEnum workoutGenerateBiz, List<WorkoutGeneratorTaskStatusEnum> statusList, List<Integer> groupIdList, Integer limit) {
        LambdaQueryWrapper<ProjWorkoutGenerateTask> queryWrapper = new LambdaQueryWrapper<ProjWorkoutGenerateTask>()
                .eq(ProjWorkoutGenerateTask::getBizCode, workoutGenerateBiz.getCode())
                .in(CollUtil.isNotEmpty(statusList), ProjWorkoutGenerateTask::getStatus, CollUtil.isNotEmpty(statusList)? statusList.stream().map(WorkoutGeneratorTaskStatusEnum::getCode).collect(Collectors.toList()) : Collections.emptyList())
                .in(CollUtil.isNotEmpty(groupIdList), ProjWorkoutGenerateTask::getGroupId, CollUtil.isNotEmpty(groupIdList)? groupIdList : Collections.emptyList())
                .eq(ProjWorkoutGenerateTask::getDelFlag, 0)
                .orderByDesc(BaseModel::getId).last("limit " + (Objects.isNull(limit) ? 1 : limit));
        return this.list(queryWrapper);
    }
}
