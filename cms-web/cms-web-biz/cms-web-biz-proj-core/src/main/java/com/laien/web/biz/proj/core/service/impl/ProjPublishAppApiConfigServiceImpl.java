package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.entity.ProjPublishAppApiConfig;
import com.laien.web.biz.proj.core.mapper.ProjPublishAppApiConfigMapper;
import com.laien.web.biz.proj.core.service.IProjPublishAppApiConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布app api 配置 用于清缓存 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Service
public class ProjPublishAppApiConfigServiceImpl extends ServiceImpl<ProjPublishAppApiConfigMapper, ProjPublishAppApiConfig> implements IProjPublishAppApiConfigService {

}
