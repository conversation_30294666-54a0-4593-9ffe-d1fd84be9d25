package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.laien.web.biz.proj.core.entity.ProjReminder;
import com.laien.web.biz.proj.core.mapper.ProjReminderMapper;
import com.laien.web.biz.proj.core.response.ProjReminderListVO;
import com.laien.web.biz.proj.core.service.IProjReminderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 项目通知表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-20
 */
@Service
public class ProjReminderServiceImpl extends ServiceImpl<ProjReminderMapper, ProjReminder> implements IProjReminderService {

    @Override
    public List<ProjReminderListVO> getList(Integer projId) {
        return getBaseMapper().selectListByProjId(projId);
    }

    @Override
    public void delByReminderId(List<Integer> reminderIds) {
        LambdaUpdateWrapper<ProjReminder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjReminder::getResReminderId, reminderIds);
        wrapper.set(ProjReminder::getDelFlag, GlobalConstant.YES);
        getBaseMapper().update(new ProjReminder(), wrapper);
    }
}
