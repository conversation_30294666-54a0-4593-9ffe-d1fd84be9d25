package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.entity.ProjWorkoutScene;
import com.laien.web.frame.exception.BizException;
import com.laien.web.biz.proj.core.mapper.ProjWorkoutSceneMapper;
import com.laien.web.biz.proj.core.request.ProjWorkoutSceneAddReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutSceneUpdateReq;
import com.laien.web.biz.proj.core.response.ProjWorkoutSceneDetailVO;
import com.laien.web.biz.proj.core.response.ProjWorkoutSceneListVO;
import com.laien.web.biz.proj.core.service.IProjWorkoutSceneService;
import com.laien.web.biz.proj.core.service.IProjWorkoutVideoService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.common.user.utils.RequestContextUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * workout scene 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Service
public class ProjWorkoutSceneServiceImpl extends ServiceImpl<ProjWorkoutSceneMapper, ProjWorkoutScene> implements IProjWorkoutSceneService {

    @Resource
    private IProjWorkoutVideoService projWorkoutVideoService;

    @Override
    public List<ProjWorkoutSceneListVO> selectWorkoutSceneList() {
        LambdaQueryWrapper<ProjWorkoutScene> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkoutScene::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.orderByAsc(ProjWorkoutScene::getSortNo);
        queryWrapper.orderByAsc(ProjWorkoutScene::getId);
        List<ProjWorkoutScene> workoutSceneList = this.list(queryWrapper);

        List<ProjWorkoutSceneListVO> listVOList = new ArrayList<>(workoutSceneList.size());
        List<Integer> idList = new ArrayList<>();
        for (ProjWorkoutScene workoutScene : workoutSceneList) {
            ProjWorkoutSceneListVO listVO = new ProjWorkoutSceneListVO();
            BeanUtils.copyProperties(workoutScene, listVO);
            listVO.setCounts(GlobalConstant.ZERO);
            idList.add(workoutScene.getId());
            listVOList.add(listVO);
        }
        if (idList.size() > 0) {
            Map<Integer, Integer> counts = projWorkoutVideoService.getCounts(idList);
            for (ProjWorkoutSceneListVO listVO : listVOList) {
                listVO.setCounts(counts.get(listVO.getId()));
            }
        }


        return listVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWorkoutScene(ProjWorkoutSceneAddReq workoutSceneAddReq) {
        boolean exist = this.selectNameExists(workoutSceneAddReq.getSceneName(), null);
        if (exist) {
            throw new BizException("Scene name exists");
        }

        // 保存
        ProjWorkoutScene workoutScene = new ProjWorkoutScene();
        BeanUtils.copyProperties(workoutSceneAddReq, workoutScene);
        workoutScene.setStatus(GlobalConstant.STATUS_ENABLE);
        workoutScene.setSortNo(Integer.MAX_VALUE);
        workoutScene.setProjId(RequestContextUtils.getProjectId());
        this.save(workoutScene);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWorkoutScene(ProjWorkoutSceneUpdateReq workoutSceneUpdateReq) {
        Integer id = workoutSceneUpdateReq.getId();
        ProjWorkoutScene workoutSceneFind = this.getById(id);
        if (Objects.isNull(workoutSceneFind)) {
            throw new BizException("Data not found");
        }

        boolean exist = this.selectNameExists(workoutSceneUpdateReq.getSceneName(), id);
        if (exist) {
            throw new BizException("Scene name exists");
        }

        // 保存
        ProjWorkoutScene workoutScene = new ProjWorkoutScene();
        BeanUtils.copyProperties(workoutSceneUpdateReq, workoutScene);
        this.updateById(workoutScene);
    }

    /**
     * 验证名称是否重复
     *
     * @param sceneName  sceneName
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectNameExists(String sceneName, Integer excludeId) {
        LambdaQueryWrapper<ProjWorkoutScene> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkoutScene::getSceneName, sceneName);
        queryWrapper.eq(ProjWorkoutScene::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.ne(Objects.nonNull(excludeId), ProjWorkoutScene::getId, excludeId);
        int counts = this.count(queryWrapper);
        return counts > GlobalConstant.ZERO;
    }

    @Override
    public ProjWorkoutSceneDetailVO selectWorkoutSceneDetail(Integer id) {
        ProjWorkoutScene workoutSceneFind = this.getById(id);
        if (Objects.isNull(workoutSceneFind)) {
            throw new BizException("Data not found");
        }
        ProjWorkoutSceneDetailVO detailVO = new ProjWorkoutSceneDetailVO();
        BeanUtils.copyProperties(workoutSceneFind, detailVO);
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWorkoutScene> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkoutScene::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.eq(ProjWorkoutScene::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjWorkoutScene::getId, idList);
        this.update(new ProjWorkoutScene(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWorkoutScene> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkoutScene::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjWorkoutScene::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWorkoutScene::getId, idList);
        this.update(new ProjWorkoutScene(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        Map<Integer, Integer> counts = projWorkoutVideoService.getCounts(idList);
        List<Integer> idDelList = new ArrayList<>();
        for (Integer id : idList) {
            Integer count = counts.get(id);
            if (count == null || count == 0) {
                idDelList.add(id);
            }
        }
        if (idDelList.size() > 0) {
            LambdaUpdateWrapper<ProjWorkoutScene> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjWorkoutScene::getDelFlag, GlobalConstant.YES);
            wrapper.in(ProjWorkoutScene::getId, idDelList);
            this.update(new ProjWorkoutScene(), wrapper);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWorkoutSceneSort(List<Integer> idList) {
        if (CollectionUtils.isNotEmpty(idList)) {
            int sortNoIndex = 1;
            Integer projId = RequestContextUtils.getProjectId();
            for (Integer id : idList) {
                LambdaUpdateWrapper<ProjWorkoutScene> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProjWorkoutScene::getId, id);
                wrapper.eq(ProjWorkoutScene::getProjId, projId);
                wrapper.set(ProjWorkoutScene::getSortNo, sortNoIndex);
                this.update(new ProjWorkoutScene(), wrapper);
                sortNoIndex++;
            }
        }
    }

}
