package com.laien.web.biz.proj.core.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 通知分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="通知分页", description="通知分页")
public class ResReminderPageReq extends PageReq {

    @ApiModelProperty(value = "通知标题")
    private String title;

    @ApiModelProperty(value = "通知内容")
    private String content;

}
