package com.laien.web.biz.proj.core.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "播放列表保存请求-音乐列表", description = "播放列表保存请求-音乐列表")
public class ProjPlaylistMusicDetailVO {

    @ApiModelProperty(value = "音乐id")
    private Integer resMusicId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "音频")
    private String audio;

    @ApiModelProperty(value = "音频总时长")
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型（关联字典表）")
    private String musicType;

}
