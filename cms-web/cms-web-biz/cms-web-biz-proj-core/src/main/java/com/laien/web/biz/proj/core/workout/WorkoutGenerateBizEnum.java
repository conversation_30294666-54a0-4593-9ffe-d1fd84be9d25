/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.core.workout;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>workout生成相关业务枚举，主要是为了定义workout 生成业务</p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/1/6 14:15
 */
@Getter
@AllArgsConstructor
public enum WorkoutGenerateBizEnum {

    // 具体业务标识

    DEMO(1,"demo"),
    OOG_106(106,"OOG-106"),
    ;

    private final int code;
    private final String desc;

    /**
     * <p>依据code查找对应的枚举</p>
     *
     * @param code code
     * @return java.util.Optional<com.laien.cms.workout.generate.WorkoutAppProjectEnum>
     * <AUTHOR>
     * @date 2024/12/27 14:42
     */
    public static Optional<WorkoutGenerateBizEnum> getByCode(Integer code) {

        return Arrays.stream(WorkoutGenerateBizEnum.values())
                .filter(item -> Objects.equals(item.code , code))
                .findAny();
    }


}