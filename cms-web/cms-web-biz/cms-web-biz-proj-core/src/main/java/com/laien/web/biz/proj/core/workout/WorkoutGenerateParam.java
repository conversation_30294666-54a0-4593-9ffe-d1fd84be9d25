/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.core.workout;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>workout生成参数 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/1/6 14:18
 */
@Getter
@Setter
@Accessors(chain = true)
public class WorkoutGenerateParam<T> {

    /**  生成任务ID */
    private Integer workoutGenerateTaskId;
    /**  项目唯一标识，用于从spring容器中查找生成器实现 */
    private WorkoutGenerateBizEnum workoutGenerateBiz;
    /**  项目ID */
    private Integer projId;
    /**  分组ID，用于在同一个appProject下做任务区分，例如106项目不同的模板 */
    private Integer groupId;
    /**  扩展参数，由调用方自行定义 */
    private T extendData;

}