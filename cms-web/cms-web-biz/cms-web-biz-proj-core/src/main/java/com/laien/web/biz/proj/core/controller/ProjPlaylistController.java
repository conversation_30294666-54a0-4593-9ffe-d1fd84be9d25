package com.laien.web.biz.proj.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.laien.web.biz.proj.core.entity.ProjPlaylist;
import com.laien.web.biz.proj.core.entity.ProjPlaylistMusic;
import com.laien.web.biz.proj.core.request.ProjPlaylistAddReq;
import com.laien.web.biz.proj.core.request.ProjPlaylistMusicSaveReq;
import com.laien.web.biz.proj.core.request.ProjPlaylistUpdateReq;
import com.laien.web.biz.proj.core.response.ProjPlaylistDetailVO;
import com.laien.web.biz.proj.core.response.ProjPlaylistListVO;
import com.laien.web.biz.proj.core.response.ProjPlaylistMusicDetailVO;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.core.service.IProjPlaylistMusicService;
import com.laien.web.biz.proj.core.service.IProjPlaylistService;
import com.laien.web.biz.proj.core.util.ShortLinkGenerateUtil;
import com.laien.web.biz.resource.entity.ResMusic;
import com.laien.web.biz.resource.service.IResMusicService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseCode;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jodd.mutable.MutableInteger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_DISABLE;
import static com.laien.web.frame.constant.GlobalConstant.STATUS_ENABLE;

/**
 * <p>
 * 播放列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@Api(tags = "项目管理:播放列表")
@RestController
@RequestMapping("/proj/playlist")
public class ProjPlaylistController extends ResponseController {

    @Resource
    private IProjPlaylistService projPlaylistService;

    @Resource
    private IProjPlaylistMusicService projPlaylistMusicService;

    @Resource
    private IResMusicService resMusicService;

    @Resource
    private ShortLinkGenerateUtil shortLinkGenerateUtil;

    @Resource
    private IProjLmsI18nService lmsI18nService;

    private MutableInteger sortNoDefault = new MutableInteger(1000);


    @ApiOperation(value = "查询播放列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjPlaylistListVO>> list(@ApiParam(value = "状态 1 启用 2 禁用") @RequestParam(required = false) Integer status) {
//        projPlaylistListReq.setProjId(RequestContextUtils.getProjectId());
        return succ(projPlaylistService.getPlayLists(RequestContextUtils.getProjectId(), status));
    }

    @ApiOperation(value = "查询列表详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjPlaylistDetailVO> detail(@PathVariable Integer id) {
        if (id == null) {
            return fail("The playlist ID cannot be empty");
        }
        LambdaQueryWrapper<ProjPlaylist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjPlaylist::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.eq(ProjPlaylist::getId, id);
        ProjPlaylist projPlaylist = projPlaylistService.getOne(queryWrapper.last("limit 1"));
        if (projPlaylist == null) {
            return fail("Playlist does not exist");
        }
        //查询播放列表的基本信息
        ProjPlaylistDetailVO projPlaylistDetailVO = new ProjPlaylistDetailVO();
        BeanUtils.copyProperties(projPlaylist, projPlaylistDetailVO);
        projPlaylistDetailVO.setMusicList(Lists.newArrayList());
        //查询关联的音乐信息
        List<ProjPlaylistMusic> musicIdList = projPlaylistMusicService.getMusicIdList(projPlaylist.getId());
        if (CollectionUtils.isNotEmpty(musicIdList)) {
            List<Integer> musicIds = Lists.newArrayList();
            for (ProjPlaylistMusic projPlaylistMusic : musicIdList) {
                musicIds.add(projPlaylistMusic.getResMusicId());
                ProjPlaylistMusicDetailVO projPlaylistMusicDetailVO = new ProjPlaylistMusicDetailVO();
                projPlaylistMusicDetailVO.setResMusicId(projPlaylistMusic.getResMusicId());
                projPlaylistMusicDetailVO.setSubscription(projPlaylistMusic.getSubscription());
                projPlaylistMusicDetailVO.setShortLink(projPlaylistMusic.getShortLink());
                projPlaylistMusicDetailVO.setDisplayName(projPlaylistMusic.getDisplayName());
                projPlaylistDetailVO.getMusicList().add(projPlaylistMusicDetailVO);
            }
            //缓存为hashmap 方便后续回填音乐基本信息
            //可能存在重复的音乐 所以value为一个集合
            HashMap<Integer, List<ProjPlaylistMusicDetailVO>> musicDetaiMap = Maps.newHashMap();
            for (ProjPlaylistMusicDetailVO projPlaylistMusicDetailVO : projPlaylistDetailVO.getMusicList()) {
                List<ProjPlaylistMusicDetailVO> detailList = musicDetaiMap.get(projPlaylistMusicDetailVO.getResMusicId().intValue());
                if (detailList == null) {
                    detailList = Lists.newArrayList();
                    musicDetaiMap.put(projPlaylistMusicDetailVO.getResMusicId().intValue(), detailList);
                }
                detailList.add(projPlaylistMusicDetailVO);
            }
            //查询音乐的名称类型等信息
            List<ResMusic> musicByIds = resMusicService.getListByIds(musicIds);
            for (ResMusic music : musicByIds) {
                List<ProjPlaylistMusicDetailVO> detailList = musicDetaiMap.get(music.getId().intValue());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    for (ProjPlaylistMusicDetailVO projPlaylistMusicDetailVO : detailList) {
                        BeanUtils.copyProperties(music, projPlaylistMusicDetailVO);
                    }
                }
            }
        }
        return succ(projPlaylistDetailVO);
    }

    public ResponseResult<Void> useName(Integer projId, String playlistName, Integer playlistId, String playlistType) {
        ProjPlaylist playlist = projPlaylistService.getOne(new LambdaQueryWrapper<ProjPlaylist>().eq(ProjPlaylist::getPlaylistName, playlistName).eq(ProjPlaylist::getProjId, projId).eq(ProjPlaylist::getPlaylistType, playlistType).last("limit 1"));
        if (playlist == null || (playlistId != null && playlistId.intValue() == playlist.getId())) {
            return succ();
        } else {
            return fail("The playlist name already exists");
        }
    }

    @ApiOperation(value = "删除播放列表（支持批量）")
    @PostMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> del(@RequestBody IdListReq idListReq) {
        if (CollectionUtils.isNotEmpty(idListReq.getIdList())) {
            LambdaUpdateWrapper<ProjPlaylist> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjPlaylist::getDelFlag, GlobalConstant.YES);
            wrapper.in(ProjPlaylist::getId, idListReq.getIdList());
            wrapper.eq(ProjPlaylist::getProjId, RequestContextUtils.getProjectId());
            projPlaylistService.update(new ProjPlaylist(), wrapper);
            //删除关联表数据
            deleteRelation(idListReq.getIdList());
        }
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isNotEmpty(idList)) {
            int sortNoIndex = sortNoDefault.get();
            for (Integer id : idList) {
                LambdaUpdateWrapper<ProjPlaylist> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProjPlaylist::getId, id);
                wrapper.eq(ProjPlaylist::getProjId, RequestContextUtils.getProjectId());
                wrapper.set(ProjPlaylist::getSortNo, sortNoIndex);
                projPlaylistService.update(new ProjPlaylist(), wrapper);
                sortNoIndex--;
            }
        }
        return succ();
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@Validated(Group.class) @RequestBody ProjPlaylistAddReq projPlaylistAddReq) {
        ResponseResult<Void> useName = checkDataIntegrity(RequestContextUtils.getProjectId(), projPlaylistAddReq, null);
        if (!(useName.getCode() == ResponseCode.SUCCESS.getCode())) {
            return useName;
        }
        ProjPlaylist projPlaylist = new ProjPlaylist();
        projPlaylist.setProjId(RequestContextUtils.getProjectId());
        BeanUtils.copyProperties(projPlaylistAddReq, projPlaylist);
        projPlaylist.setStatus(STATUS_DISABLE);
        projPlaylist.setSortNo(sortNoDefault.get());
        projPlaylistService.save(projPlaylist);
        lmsI18nService.handleI18n(Collections.singletonList(projPlaylist));

        updateRelation(projPlaylistAddReq, projPlaylist);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@Validated(Group.class) @RequestBody ProjPlaylistUpdateReq projPlaylistUpdateReq) {
        Integer id = projPlaylistUpdateReq.getId();
        //查询原数据
        ProjPlaylist resMusic = projPlaylistService.getById(id);
        if (resMusic == null) {
            return fail("The playlist with id " + id + " does not exist");
        }
        //判断视频名称是否重复
        ResponseResult<Void> checkNameExist = checkDataIntegrity(RequestContextUtils.getProjectId(), projPlaylistUpdateReq, projPlaylistUpdateReq.getId());
        if (!(checkNameExist.getCode() == ResponseCode.SUCCESS.getCode())) {
            return checkNameExist;
        }
        BeanUtils.copyProperties(projPlaylistUpdateReq, resMusic);
        projPlaylistService.updateById(resMusic);
        lmsI18nService.handleI18n(Collections.singletonList(resMusic));

        updateRelation(projPlaylistUpdateReq, resMusic);
        return succ();
    }

    @ApiOperation(value = "启用/禁用（支持批量）")
    @PostMapping("/enable")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        if (CollectionUtils.isNotEmpty(idListReq.getIdList())) {
            LambdaUpdateWrapper<ProjPlaylist> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjPlaylist::getStatus, STATUS_ENABLE);
            updateWrapper.in(ProjPlaylist::getId, idListReq.getIdList());
            updateWrapper.eq(ProjPlaylist::getProjId, RequestContextUtils.getProjectId());
            updateWrapper.in(ProjPlaylist::getStatus, Lists.newArrayList(STATUS_DISABLE));
            projPlaylistService.update(new ProjPlaylist(), updateWrapper);
        }
        return succ();
    }

    @ApiOperation(value = "启用/禁用（支持批量）")
    @PostMapping("/disable")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        if (CollectionUtils.isNotEmpty(idListReq.getIdList())) {
            LambdaUpdateWrapper<ProjPlaylist> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjPlaylist::getStatus, STATUS_DISABLE);
            updateWrapper.in(ProjPlaylist::getId, idListReq.getIdList());
            updateWrapper.eq(ProjPlaylist::getProjId, RequestContextUtils.getProjectId());
            updateWrapper.in(ProjPlaylist::getStatus, Lists.newArrayList(STATUS_ENABLE));
            projPlaylistService.update(new ProjPlaylist(), updateWrapper);
        }
        return succ();
    }

    /**
     * 删除关联
     *
     * @param ids
     */
    private void deleteRelation(List<Integer> ids) {
        LambdaUpdateWrapper<ProjPlaylistMusic> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjPlaylistMusic::getDelFlag, GlobalConstant.YES);
        updateWrapper.in(ProjPlaylistMusic::getProjPlaylistId, ids);
        projPlaylistMusicService.update(new ProjPlaylistMusic(), updateWrapper);
    }

    /**
     * 更新关联
     *
     * @param projPlaylistAddReq
     * @param projPlaylist
     */
    private void updateRelation(ProjPlaylistAddReq projPlaylistAddReq, ProjPlaylist projPlaylist) {
        //先删除原来的关系 再增加 防止脏数据
        deleteRelation(Lists.newArrayList(projPlaylist.getId()));
        //增加关联关系
        Integer projId = RequestContextUtils.getProjectId();
        List<ProjPlaylistMusicSaveReq> musicList = projPlaylistAddReq.getMusicList();
        String playlistType = projPlaylistAddReq.getPlaylistType();
        List<ProjPlaylistMusic> playlistMusicList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(musicList)) {
            for (ProjPlaylistMusicSaveReq projPlaylistMusicSaveReq : musicList) {
                ProjPlaylistMusic projPlaylistMusic = new ProjPlaylistMusic();
                BeanUtils.copyProperties(projPlaylistMusicSaveReq, projPlaylistMusic);
                projPlaylistMusic.setProjPlaylistId(projPlaylist.getId());
                projPlaylistMusic.setProjId(projId);
                projPlaylistMusicService.save(projPlaylistMusic);
                playlistMusicList.add(projPlaylistMusic);
                if (Objects.equals(playlistType, "Soundscape")) {
                    ProjPlaylistMusic playlistMusicShortLink = new ProjPlaylistMusic();
                    playlistMusicShortLink.setId(projPlaylistMusic.getId());
                    playlistMusicShortLink.setShortLink(shortLinkGenerateUtil.getWorkShortLink("/music/" + projPlaylistMusic.getId(), projId));
                    projPlaylistMusicService.updateById(playlistMusicShortLink);
                }
            }
        }
        lmsI18nService.handleI18n(playlistMusicList);
    }


    private ResponseResult<Void> checkDataIntegrity(Integer projId, ProjPlaylistAddReq projPlaylistAddReq, Integer playListId) {
        if (projId == null) {
            return fail("The project ID is not empty");
        }
        ResponseResult<Void> checkNameExist = useName(projId, projPlaylistAddReq.getPlaylistName(), playListId, projPlaylistAddReq.getPlaylistType());
        if (!(checkNameExist.getCode() == ResponseCode.SUCCESS.getCode())) {
            return checkNameExist;
        }
        //video name
        projPlaylistAddReq.setPlaylistName(projPlaylistAddReq.getPlaylistName().trim());
        return succ();
    }
}
