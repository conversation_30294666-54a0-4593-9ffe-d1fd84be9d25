package com.laien.web.biz.proj.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.core.entity.ProjPlaylist;
import com.laien.web.biz.proj.core.response.ProjPlaylistListVO;

import java.util.List;

/**
 * <p>
 * 播放列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
public interface ProjPlaylistMapper extends BaseMapper<ProjPlaylist> {
    List<ProjPlaylistListVO> selectListByProjId(Integer projId, Integer status);
}
