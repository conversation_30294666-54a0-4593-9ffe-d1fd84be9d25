package com.laien.web.biz.proj.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.laien.web.biz.proj.core.entity.ProjMenu;
import com.laien.web.biz.proj.core.response.ProjMenuVO;
import com.laien.web.biz.proj.core.service.IProjMenuService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.validation.Group;
import com.laien.web.common.user.request.PermsUpdateByKeyReq;
import com.laien.web.common.user.service.ISysPermsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.laien.web.common.user.constant.UserConstant.PERMS_TYPE_MENU;
import static com.laien.web.frame.constant.GlobalConstant.THREE;

/**
 * <p>
 * 项目菜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Api(tags = "项目管理:项目菜单")
@RestController
@RequestMapping("/proj/menu")
public class ProjMenuController extends ResponseController {

    @Autowired
    private IProjMenuService projMenuService;

    @Autowired
    private ISysPermsService sysPermsService;

    @ApiOperation(value = "查询可选择的项目菜单")
    @GetMapping("/list")
    public ResponseResult<List<ProjMenuVO>> list() {
        LambdaQueryWrapper<ProjMenu> queryWrapper = new LambdaQueryWrapper<>();
        //状态为正常
        queryWrapper.eq(ProjMenu::getStatus, GlobalConstant.STATUS_ENABLE);
        //显示状态为显示
        queryWrapper.eq(ProjMenu::getVisible, GlobalConstant.SHOW_VISIBLE);
        //类型为菜单
        queryWrapper.eq(ProjMenu::getMenuType, PERMS_TYPE_MENU);
        //父级id为0
        queryWrapper.eq(ProjMenu::getParentId, 0);
        queryWrapper.orderByAsc(ProjMenu::getSortNo);

        List<ProjMenu> list = projMenuService.list(queryWrapper);
        List<ProjMenuVO> projMenuVOList = Lists.newArrayList();
        for (ProjMenu projMenu : list) {
            ProjMenuVO projMenuVO = new ProjMenuVO();
            BeanUtils.copyProperties(projMenu, projMenuVO);
            projMenuVOList.add(projMenuVO);
        }
        return succ(projMenuVOList);
    }

    @ApiOperation(value = "修改项目菜单")
    @PostMapping("/update")
    public ResponseResult<Void> update(@Validated(Group.class) @RequestBody ProjMenu projMenu) {
        Integer id = projMenu.getId();
        if (id == null) {
            return fail("The project menu ID cannot be empty");
        }
        //查询原数据
        ProjMenu resMusic = projMenuService.getById(id);
        if (resMusic == null) {
            return fail("The project menu with id " + id + " does not exist");
        }
        projMenuService.updateById(projMenu);
        //同步对应权限的相关信息
        PermsUpdateByKeyReq permsUpdateByKeyReq = new PermsUpdateByKeyReq();
        permsUpdateByKeyReq.setLikeType(THREE);
        permsUpdateByKeyReq.setPermsKey(resMusic.getMenuKey());
        permsUpdateByKeyReq.setComponent(projMenu.getComponent());
        permsUpdateByKeyReq.setIcon(projMenu.getIcon());
        permsUpdateByKeyReq.setPath(projMenu.getPath());
        permsUpdateByKeyReq.setRequired(projMenu.getRequired());
        permsUpdateByKeyReq.setSortNo(projMenu.getSortNo());
        sysPermsService.batchUpdateByPermsKey(permsUpdateByKeyReq);
        return succ();
    }

    @ApiOperation(value = "查询菜单详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjMenu> detail(@PathVariable Integer id) {
        if (id == null) {
            return fail("The project menu ID cannot be empty");
        }
        ProjMenu projMenu = projMenuService.getById(id);
        if (projMenu == null) {
            return fail("Project menu does not exist");
        }
        return succ(projMenu);
    }

}
