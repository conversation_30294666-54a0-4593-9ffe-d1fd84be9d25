package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.core.constant.BizConstant;
import com.laien.web.biz.proj.core.entity.ResRegularVideo;
import com.laien.web.biz.proj.core.mapper.ResRegularVideoMapper;
import com.laien.web.biz.proj.core.request.ResRegularVideoAddReq;
import com.laien.web.biz.proj.core.request.ResRegularVideoReq;
import com.laien.web.biz.proj.core.request.ResRegularVideoUpdateReq;
import com.laien.web.biz.proj.core.response.ResRegularVideoDetailVO;
import com.laien.web.biz.proj.core.response.ResRegularVideoPageVO;
import com.laien.web.biz.proj.core.service.IResRegularVideoService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * regular video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Service
public class ResRegularVideoServiceImpl extends ServiceImpl<ResRegularVideoMapper, ResRegularVideo> implements IResRegularVideoService {

    @Override
    public PageRes<ResRegularVideoPageVO> selectRegularVideoPage(ResRegularVideoReq pageReq) {
        LambdaQueryWrapper<ResRegularVideo> queryWrapper = new LambdaQueryWrapper<>();
        String exerciseName = pageReq.getExerciseName();
        queryWrapper.like(StringUtils.isNotBlank(exerciseName), ResRegularVideo::getExerciseName, exerciseName);
        Integer status = pageReq.getStatus();
        queryWrapper.eq(Objects.nonNull(status), ResRegularVideo::getStatus, status);
        // 目的
        String[] targetArr = pageReq.getTargetArr();
        if (Objects.nonNull(targetArr) && targetArr.length > GlobalConstant.ZERO) {
            queryWrapper.and(cust -> {
                int len = targetArr.length;
                for (int i = 0; i < len; i++) {
                    String targetItem = targetArr[i];
                    if (Objects.equals(targetItem, BizConstant.NONE)) {
                        cust.apply("target = {0}", GlobalConstant.EMPTY_STRING);
                    } else {
                        cust.apply("FIND_IN_SET({0}, target)", targetItem);
                    }
                    cust.or(i < len - 1);
                }
            });
        }

        // 配备
        String[] equipmentArr = pageReq.getEquipmentArr();
        if (Objects.nonNull(equipmentArr) && equipmentArr.length > GlobalConstant.ZERO) {
            queryWrapper.and(cust -> {
                int len = equipmentArr.length;
                for (int i = 0; i < len; i++) {
                    String equipmentItem = equipmentArr[i];
                    if (Objects.equals(equipmentItem, BizConstant.NONE)) {
                        cust.apply("equipment = {0}", GlobalConstant.EMPTY_STRING);
                    } else {
                        cust.apply("equipment = {0}", equipmentItem);
                    }
                    cust.or(i < len - 1);
                }
            });
        }


        queryWrapper.orderByDesc(ResRegularVideo::getId);

        // 查询
        Page<ResRegularVideo> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.page(page, queryWrapper);
        List<ResRegularVideo> list = page.getRecords();
        List<ResRegularVideoPageVO> copyList = new ArrayList<>(list.size());
        for (ResRegularVideo regularVideo : list) {
            ResRegularVideoPageVO pageVO = new ResRegularVideoPageVO();
            BeanUtils.copyProperties(regularVideo, pageVO);
            copyList.add(pageVO);
        }

        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), copyList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRegularVideo(ResRegularVideoAddReq regularVideoAddReq) {
        selectNameExists(regularVideoAddReq.getExerciseName(), null);
        // 保证主数据
        ResRegularVideo regularVideo = new ResRegularVideo();
        BeanUtils.copyProperties(regularVideoAddReq, regularVideo);
        regularVideo.setTarget(MyStringUtil.getJoinWithComma(regularVideoAddReq.getTargetArr()));
        this.save(regularVideo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRegularVideo(ResRegularVideoUpdateReq regularVideoUpdateReq) {
        Integer id = regularVideoUpdateReq.getId();
        selectNameExists(regularVideoUpdateReq.getExerciseName(), id);
        ResRegularVideo regularVideoFind = this.getById(id);
        if (Objects.isNull(regularVideoFind)) {
            throw new BizException("Data not found");
        }

        if (Objects.equals(regularVideoUpdateReq.getStatus(), GlobalConstant.STATUS_DRAFT)
                && !Objects.equals(regularVideoFind.getStatus(), GlobalConstant.STATUS_DRAFT)) {
            // 保存为草稿，原来的状态不是草稿，不能修改
            regularVideoUpdateReq.setStatus(regularVideoFind.getStatus());
        } else if(Objects.equals(regularVideoUpdateReq.getStatus(), GlobalConstant.STATUS_ENABLE)
                && Objects.equals(regularVideoFind.getStatus(), GlobalConstant.STATUS_DRAFT)) {
            // 保存，可以将草稿状态的启用
        } else {
            // 其他情况不处理状态
            regularVideoUpdateReq.setStatus(null);
        }

        ResRegularVideo regularVideo = new ResRegularVideo();
        BeanUtils.copyProperties(regularVideoUpdateReq, regularVideo);
        regularVideo.setTarget(MyStringUtil.getJoinWithComma(regularVideoUpdateReq.getTargetArr()));

        this.updateById(regularVideo);
    }

    /**
     * 验证名称是否重复
     *
     * @param exerciseName  exerciseName
     * @param excludeId 排除id
     * @return bool
     */
    private void selectNameExists(String exerciseName, Integer excludeId) {
        LambdaQueryWrapper<ResRegularVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResRegularVideo::getExerciseName, exerciseName);
        queryWrapper.ne(Objects.nonNull(excludeId), ResRegularVideo::getId, excludeId);
        int counts = this.count(queryWrapper);
        if (counts > 0) {
            throw new BizException("exerciseName already exists");
        }
    }

    @Override
    public ResRegularVideoDetailVO getRegularVideoDetail(Integer id) {
        ResRegularVideo regularVideoFind = this.getById(id);
        if (Objects.isNull(regularVideoFind)) {
            throw new BizException("Data not found");
        }
        ResRegularVideoDetailVO detailVO = new ResRegularVideoDetailVO();
        BeanUtils.copyProperties(regularVideoFind, detailVO);
        detailVO.setTargetArr(MyStringUtil.getSplitWithComa(regularVideoFind.getTarget()));
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResRegularVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResRegularVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResRegularVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ResRegularVideo::getId, idList);
        this.update(new ResRegularVideo(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResRegularVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResRegularVideo::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ResRegularVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ResRegularVideo::getId, idList);
        this.update(new ResRegularVideo(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ResRegularVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResRegularVideo::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ResRegularVideo::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ResRegularVideo::getId, idList);
        this.update(new ResRegularVideo(), wrapper);
    }

}
