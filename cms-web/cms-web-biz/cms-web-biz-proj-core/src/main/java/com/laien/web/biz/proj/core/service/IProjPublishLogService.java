package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.bo.ProjPublishProjParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamBO;
import com.laien.web.biz.proj.core.entity.ProjPublishCurrentVersion;
import com.laien.web.biz.proj.core.entity.ProjPublishLog;
import com.laien.web.biz.proj.core.request.ProjPublishPubOnlineReq;

import java.util.List;

/**
 * <p>
 * 发布日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
public interface IProjPublishLogService extends IService<ProjPublishLog> {

    /**
     * 查询项目版本号
     *
     * @param projId projId
     * @return ProjPublishCurrentVersion
     */
    ProjPublishCurrentVersion getVersionByProjId(Integer projId);

    /**
     * 最后一次是否是预发布
     *
     * @param projId projId
     * @return boolean
     */
    boolean getLastPubIsPre(Integer projId);

    /**
     * 更新版本号
     * @param projId projId
     * @param publishVersion publishVersion
     */
    void updateVersionByProjId (Integer projId, Integer publishVersion);

    /**
     * 发布
     *
     * @param projId projId
     * @return int
     */
    Integer savePublish(Integer projId, ProjPublishPubOnlineReq projPublishPubOnlineReq);

    /**
     * 清除历史发布数据
     *
     * @param projId projId
     * @param projParamBOList projParamBOList
     * @param projRelationParamBOList projRelationParamBOList
     */
    void deletePubHistory(Integer projId, List<ProjPublishProjParamBO> projParamBOList, List<ProjPublishProjRelationParamBO> projRelationParamBOList);

    /**
     * 清除预发布历史数据
     *
     * @param projId projId
     * @param projParamBOList projParamBOList
     * @param projRelationParamBOList projRelationParamBOList
     */
    void deletePrePublishHistory(Integer projId, List<ProjPublishProjParamBO> projParamBOList, List<ProjPublishProjRelationParamBO> projRelationParamBOList);

    /**
     * 清除Cloudflare缓存
     *
     * @param projId projId
     */
    void purgeCloudflareCache(Integer projId);

    /**
     * 保存清除缓存日志
     * @param projId
     */
    void savePurgeCacheLog(Integer projId);
}
