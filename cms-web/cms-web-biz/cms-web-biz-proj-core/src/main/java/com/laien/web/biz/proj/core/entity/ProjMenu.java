package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjMenu对象", description="项目菜单表")
public class ProjMenu extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "父级id")
    private Integer parentId;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "菜单标识，一旦使用 不得修改，否则容易造成权限错误")
    private String menuKey;

    @ApiModelProperty(value = "权限类型： 1 菜单 2操作类型 3 项目 4外链")
    private Integer menuType;

    @ApiModelProperty(value = "路由地址，链接地址")
    private String path;

    @ApiModelProperty(value = "组件路径")
    private String component;

    @ApiModelProperty(value = "随着父级联动，必须的（0 否 1是）")
    private Integer required;

    @ApiModelProperty(value = "菜单状态（0隐藏 1显示）")
    private Integer visible;

    @ApiModelProperty(value = "菜单状态（1正常 2停用）")
    private Integer status;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sortNo;


}
