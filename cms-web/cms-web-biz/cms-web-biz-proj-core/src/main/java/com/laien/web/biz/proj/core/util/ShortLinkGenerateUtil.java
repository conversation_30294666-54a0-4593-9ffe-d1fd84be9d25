package com.laien.web.biz.proj.core.util;

import com.laien.web.common.file.config.FirebaseShortLinkConfig;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.common.core.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * note: 短连接生成工具
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ShortLinkGenerateUtil {

    @Resource
    private FirebaseShortLinkConfig firebaseShortLinkConfig;
    @Resource
    private IProjInfoService iProjInfoService;
    @Resource
    private RestTemplate restTemplate;

    public String getWorkShortLink(String appendLink, Integer projId){
        if (!firebaseShortLinkConfig.isGenerate()) {
            return GlobalConstant.EMPTY_STRING;
        }

        ProjInfo projInfo = iProjInfoService.getById(projId);
        if (projInfo == null || Objects.equals(projInfo.getWorkoutShortLink(), GlobalConstant.ZERO)) {
            return GlobalConstant.EMPTY_STRING;
        }

        String link = projInfo.getDynamicLink();
        String slash = "/";
        if (!link.endsWith(slash)) {
            link = link + slash;
        }

        String domainUriPrefix = this.getUriPrefix(link);

        String androidPackageName = projInfo.getBundleId();
        String iosBundleId = projInfo.getBundleId();

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> dynamicLinkInfo = new HashMap<>();
        dynamicLinkInfo.put("domainUriPrefix", domainUriPrefix);
        dynamicLinkInfo.put("link", link + appendLink);

        Map<String, Object> androidInfo = new HashMap<>();
        androidInfo.put("androidPackageName", androidPackageName);
        dynamicLinkInfo.put("androidInfo", androidInfo);

        Map<String, Object> iosInfo = new HashMap<>();
        iosInfo.put("iosBundleId", iosBundleId);
        iosInfo.put("iosAppStoreId",projInfo.getAppleId());
        dynamicLinkInfo.put("iosInfo", iosInfo);

        // 不预览直接打开
        Map<String, Object> navigationInfo = new HashMap<>(1);
        navigationInfo.put("enableForcedRedirect", 1);
        dynamicLinkInfo.put("navigationInfo", navigationInfo);


        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dynamicLinkInfo", dynamicLinkInfo);

        String webApiKey = projInfo.getWebApiKey();
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(paramMap, httpHeaders);
        ResponseEntity<Map> responseEntity;
        try {
            responseEntity = restTemplate.postForEntity(firebaseShortLinkConfig.getApiUrl() + webApiKey, httpEntity, Map.class);
        } catch (Exception e) {
            throw new BizException("Get firebase short link error! please check project");
        }

        Map result = responseEntity.getBody();
        log.info("获取firebase短连接返回值: " + JacksonUtil.toJsonString(result));

        String shortLink = (String)result.get("shortLink");
        if (shortLink == null) {
            throw new BizException("Get firebase short link error!");
        }
        return shortLink;
    }

    private String getUriPrefix(String url) {
        String http = "http://";
        String https = "https://";
        String uriPrefix;
        if(url.startsWith(http)) {
            uriPrefix = url.substring(http.length());
            uriPrefix = uriPrefix.substring(0, uriPrefix.indexOf("/"));
        } else if(url.startsWith(https)) {
            uriPrefix = url.substring(https.length());
            uriPrefix = uriPrefix.substring(0, uriPrefix.indexOf("/"));
        } else {
            uriPrefix = url.substring(0, url.indexOf("/"));
        }
        return uriPrefix;
    }

}
