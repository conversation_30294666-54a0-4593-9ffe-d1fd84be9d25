package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjWorkoutScene;
import com.laien.web.biz.proj.core.request.ProjWorkoutSceneAddReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutSceneUpdateReq;
import com.laien.web.biz.proj.core.response.ProjWorkoutSceneDetailVO;
import com.laien.web.biz.proj.core.response.ProjWorkoutSceneListVO;

import java.util.List;

/**
 * <p>
 * workout scene 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
public interface IProjWorkoutSceneService extends IService<ProjWorkoutScene> {

    /**
     * 查询 workout scene 列表
     *
     * @return list
     */
    List<ProjWorkoutSceneListVO> selectWorkoutSceneList();

    /**
     * workout scene 新增
     *
     * @param workoutSceneAddReq workoutSceneAddReq
     */
    void saveWorkoutScene(ProjWorkoutSceneAddReq workoutSceneAddReq);

    /**
     * workout scene 修改
     *
     * @param workoutSceneUpdateReq workoutSceneUpdateReq
     */
    void updateWorkoutScene(ProjWorkoutSceneUpdateReq workoutSceneUpdateReq);

    /**
     * workout scene 详情
     *
     * @param id id
     * @return ProjWorkoutSceneDetailVO
     */
    ProjWorkoutSceneDetailVO selectWorkoutSceneDetail(Integer id);

    /**
     * workout scene 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * workout scene 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * workout scene 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * workout scene 保存排序
     *
     * @param idList idList
     */
    void saveWorkoutSceneSort(List<Integer> idList);
}
