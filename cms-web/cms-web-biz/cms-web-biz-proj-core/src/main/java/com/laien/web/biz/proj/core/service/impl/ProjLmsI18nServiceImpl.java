package com.laien.web.biz.proj.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  项目
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProjLmsI18nServiceImpl implements IProjLmsI18nService {

    private final ICoreTextTaskI18nService i18nService;
    private final IProjInfoService projInfoService;

    @Override
    public void handleI18n(List<? extends CoreI18nModel> i18nList, ProjInfo projInfo) {
        CreateTaskDTO createTaskDTO;
        if (projInfo != null) {
            createTaskDTO = new CreateTaskDTO(i18nList, projInfo.getTextLanguages(), projInfo.getLanguages(), projInfo.getAppCode());
        } else {
            //查询所有项目的
            List<String> allAppCode =  ProjCodeEnums.getAppCodeListExclude(ProjCodeEnums.COMMON);
            List<ProjInfo> projInfoList = projInfoService.getByAppCodeList(allAppCode);
            if (CollUtil.isNotEmpty(projInfoList)) {
                Set<String> textLangSet = new HashSet<>();
                Set<String> langSet = new HashSet<>();

                for (ProjInfo info : projInfoList) {
                    if (StrUtil.isNotBlank(info.getTextLanguages())) {
                        textLangSet.addAll(Arrays.asList(info.getTextLanguages().split(",")));
                    }
                    if (StrUtil.isNotBlank(info.getLanguages())) {
                        langSet.addAll(Arrays.asList(info.getLanguages().split(",")));
                    }
                }

                String textLanguages = String.join(",", textLangSet);
                String languages = String.join(",", langSet);
                createTaskDTO = new CreateTaskDTO(i18nList, textLanguages, languages, ProjCodeEnums.COMMON.getAppCode());
            } else {
                createTaskDTO = new CreateTaskDTO(i18nList);
            }
        }
        try {
            i18nService.batchSaveOrUpdate(createTaskDTO);
        } catch (Exception e) {
            log.error("handleI18n failed, createTaskDTO:{}", createTaskDTO, e);
            throw new BizException("handleI18n failed:"+ e.getMessage());
        }
    }

    @Override
    public void handleI18n(List<? extends CoreI18nModel> i18nList, Integer projId) {
        BizExceptionUtil.throwIf(projId == null, "projId is null");
        ProjInfo projInfo = projInfoService.getById(projId);
        BizExceptionUtil.throwIf(projInfo == null, "projInfo is null");
        handleI18n(i18nList, projInfo);
    }

    @Override
    public void handleI18n(List<? extends CoreI18nModel> i18nList) {
        handleI18n(i18nList, (ProjInfo) null);
    }

}
