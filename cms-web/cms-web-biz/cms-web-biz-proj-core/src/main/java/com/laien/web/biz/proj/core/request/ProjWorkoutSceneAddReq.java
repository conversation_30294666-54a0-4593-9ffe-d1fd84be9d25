package com.laien.web.biz.proj.core.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout scene 新增
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="workout scene 新增", description="workout scene 新增")
public class ProjWorkoutSceneAddReq {

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景类型")
    private String sceneType;

}
