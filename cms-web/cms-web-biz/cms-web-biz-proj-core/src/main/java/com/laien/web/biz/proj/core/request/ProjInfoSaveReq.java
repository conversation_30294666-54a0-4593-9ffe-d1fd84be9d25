package com.laien.web.biz.proj.core.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: 项目保存
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "项目保存", description = "项目保存")
public class ProjInfoSaveReq {

    @ApiModelProperty(value = "项目名称")
    private Integer id;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "项目图标")
    private String iconUrl;

    @ApiModelProperty(value = "app code")
    private String appCode;

    @ApiModelProperty(value = "apple id")
    private String appleId;

    @ApiModelProperty(value = "bundle id")
    private String bundleId;

    @ApiModelProperty(value = "app store name")
    private String appStoreName;

    @ApiModelProperty(value = "app subtitle")
    private String appSubtitle;

    @ApiModelProperty(value = "scheme")
    private String scheme;

    @ApiModelProperty(value = "web api key")
    private String webApiKey;

    @ApiModelProperty(value = "dynamic link")
    private String dynamicLink;

    @ApiModelProperty(value = "campaign link")
    private String campaignLink;

    @ApiModelProperty(value = "workout short link 开关，1开启 0关闭")
    private Integer workoutShortLink;

    @ApiModelProperty(value = "功能菜单")
    private List<Integer> menuIds;
}
