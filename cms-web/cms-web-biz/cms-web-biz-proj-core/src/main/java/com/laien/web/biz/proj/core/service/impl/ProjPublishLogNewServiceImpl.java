package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.core.constant.RedisKeyConstant;
import com.laien.web.biz.proj.core.bo.ProjPublishProjParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamBO;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.entity.ProjPublishLog;
import com.laien.web.biz.proj.core.handler.ProjPublishHandler;
import com.laien.web.biz.proj.core.mapper.ProjPublishLogMapper;
import com.laien.web.biz.proj.core.request.ProjPublishPubOnlineReq;
import com.laien.web.biz.proj.core.service.IProjPublishLogNewService;
import com.laien.web.biz.proj.core.service.IProjPublishLogService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 发布日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Service
@RefreshScope
@EnableAsync
public class ProjPublishLogNewServiceImpl extends ServiceImpl<ProjPublishLogMapper, ProjPublishLog> implements IProjPublishLogNewService {
    @Resource
    private ProjPublishLogMapper projPublishLogMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IProjPublishLogService oldProjPublishLogService;

    private enum PublishResult { Success, Fail }
    private static final int DB_MAX_ERROR_LENGTH = 3000;

    @Override
    public Integer savePublish(ProjInfo projInfo, ProjPublishPubOnlineReq projPublishPubOnlineReq, ProjPublishHandler handler) {
        final Integer projId = projInfo.getId();
        final RLock lock = redissonClient.getLock("LOCK:CMS:PUBLISH:" + projId);
        final boolean prePublish = Boolean.TRUE.equals(projPublishPubOnlineReq.getPrePublish());
        final List<ProjPublishProjParamBO> projParamBOList = new ArrayList<>();
        final List<ProjPublishProjRelationParamBO> projRelationParamBOList = new ArrayList<>();
        if (!lock.tryLock()) {
            throw new BizException("Someone is publishing in the current period, Please operate later");
        }
        Integer publishVersion = null;
        try {
            publishVersion = determinePublishVersion(projId, prePublish, handler);
            handler.beforePublishProcess(projId, projPublishPubOnlineReq);
            projParamBOList.addAll(handler.getProjParamBOList(projId, publishVersion));
            projRelationParamBOList.addAll(handler.getProjRelationParamBOList(projId, publishVersion));
            processPublishData(prePublish, projId, projParamBOList, projRelationParamBOList);
            handlePostPublishActions(projInfo, publishVersion, projPublishPubOnlineReq, prePublish);
            return publishVersion;
        } catch (Exception e) {
            e.printStackTrace();
            handlePublishFailure(publishVersion,projId, projPublishPubOnlineReq, e);
            if (e instanceof BizException) {
                throw (BizException)e;
            } else {
                throw new BizException("An error occurred in publishing");
            }
        } finally {
            lock.unlock();
            if (!prePublish) {
                oldProjPublishLogService.deletePubHistory(projId, projParamBOList, projRelationParamBOList);
            }
        }
    }

    private Integer determinePublishVersion(Integer projId, boolean prePublish, ProjPublishHandler handler) {
        if (prePublish) return GlobalConstant.VERSION_PRE_RELEASE;

        return oldProjPublishLogService.getVersionByProjId(projId).getCurrentVersion() + 1;
    }

    private void processPublishData(boolean prePublish, Integer projId,
                                   List<ProjPublishProjParamBO> projParams,
                                   List<ProjPublishProjRelationParamBO> relationParams) {
        if (prePublish) {
            oldProjPublishLogService.deletePrePublishHistory(projId, projParams, relationParams);
        }

        projParams.forEach(projPublishLogMapper::insertProjToCmsApp);
        relationParams.forEach(projPublishLogMapper::insertProjRelationToCmsApp);
    }

    private void handlePostPublishActions(ProjInfo projInfo, Integer version,
                                          ProjPublishPubOnlineReq req, boolean prePublish) {
        // 非预发布：更新项目版本（数据库操作）
        if (!prePublish) {
            oldProjPublishLogService.updateVersionByProjId(projInfo.getId(), version);
        }

        // 保存发布日志（数据库操作）
        savePublishLog(projInfo.getId(), req.getRemark(), version, prePublish, PublishResult.Success, null);

        // 非预发布：在确保数据库操作完成后，执行缓存清理
        if (!prePublish) {
            clearRedisCache(projInfo.getAppCode());
            oldProjPublishLogService.purgeCloudflareCache(projInfo.getId());
        }
    }

    private void savePublishLog(Integer projId, String remark, Integer version,
                               boolean prePublish, PublishResult result, String error) {
        ProjPublishLog log = new ProjPublishLog()
            .setProjId(projId)
            .setRemark(remark)
            .setVersion(version)
            .setPrePublish(prePublish ? GlobalConstant.ONE : GlobalConstant.ZERO)
            .setResult(result.name())
            .setFailReason(processErrorMessage(error));

        this.save(log);
    }

    private String processErrorMessage(String error) {
        return error != null && error.length() > DB_MAX_ERROR_LENGTH
               ? error.substring(0, DB_MAX_ERROR_LENGTH)
               : error;
    }

    private void clearRedisCache(String appCode) {
        String redisKey = RedisKeyConstant.APP_PROJECT_PUBLISH + appCode.toLowerCase();
        redissonClient.getBucket(redisKey).delete();
    }

    private void handlePublishFailure(Integer publishVersion,Integer projId, ProjPublishPubOnlineReq req, Exception e) throws BizException{
        String errorMessage = e.getMessage();
        if (publishVersion != null) {
            savePublishLog(projId, req.getRemark(), publishVersion, req.getPrePublish(),
                    PublishResult.Fail, errorMessage);
        }
    }
}
