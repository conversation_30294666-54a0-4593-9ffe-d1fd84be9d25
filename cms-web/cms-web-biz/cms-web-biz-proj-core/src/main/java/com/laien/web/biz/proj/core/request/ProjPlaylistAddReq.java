package com.laien.web.biz.proj.core.request;

import com.laien.web.frame.validation.Group5;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "播放列表新增请求", description = "播放列表新增请求")
public class ProjPlaylistAddReq extends ProjPlaylistSaveReq {

    @ApiModelProperty(value = "音乐列表")
    @NotEmpty(message = "The playlist contains at least one piece of music", groups = Group5.class)
    private List<ProjPlaylistMusicSaveReq> musicList;
}
