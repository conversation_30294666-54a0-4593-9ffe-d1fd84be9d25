package com.laien.web.biz.proj.core.controller;


import com.laien.web.biz.proj.core.entity.ResRegularVideo;
import com.laien.web.biz.proj.core.request.ResRegularVideoAddReq;
import com.laien.web.biz.proj.core.request.ResRegularVideoReq;
import com.laien.web.biz.proj.core.request.ResRegularVideoUpdateReq;
import com.laien.web.biz.proj.core.response.ResRegularVideoDetailVO;
import com.laien.web.biz.proj.core.response.ResRegularVideoPageVO;
import com.laien.web.biz.proj.core.service.IResRegularVideoService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * regular video 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Api(tags = "资源管理:Regular Video")
@RestController
@RequestMapping("/res/regularVideo")
public class ResRegularVideoController extends ResponseController {

    @Resource
    private IResRegularVideoService iResRegularVideoService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResRegularVideoPageVO>> page(ResRegularVideoReq pageReq) {
        PageRes<ResRegularVideoPageVO> pageRes = iResRegularVideoService.selectRegularVideoPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResRegularVideoAddReq regularVideoAddReq) {
        regularVideoAddReq.setStatus(GlobalConstant.STATUS_DRAFT);
        iResRegularVideoService.saveRegularVideo(regularVideoAddReq);
        return succ();
    }

//    @ApiOperation(value = "新增为草稿")
//    @PostMapping("/addAsDraft")
//    public ResponseResult<Void> addAsDraft(@RequestBody ResRegularVideoAddReq regularVideoAddReq) {
//        regularVideoAddReq.setStatus(GlobalConstant.STATUS_DRAFT);
//        iResRegularVideoService.saveRegularVideo(regularVideoAddReq);
//        return succ();
//    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResRegularVideoUpdateReq regularVideoUpdateReq) {
        regularVideoUpdateReq.setStatus(GlobalConstant.STATUS_ENABLE);
        iResRegularVideoService.updateRegularVideo(regularVideoUpdateReq);
        return succ();
    }

//    @ApiOperation(value = "修改为草稿")
//    @PostMapping("/updateAsDraft")
//    public ResponseResult<Void> updateAsDraft(@RequestBody ResRegularVideoUpdateReq regularVideoUpdateReq) {
//        regularVideoUpdateReq.setStatus(GlobalConstant.STATUS_DRAFT);
//        iResRegularVideoService.updateRegularVideo(regularVideoUpdateReq);
//        return succ();
//    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResRegularVideoDetailVO> detail(@PathVariable Integer id) {
        ResRegularVideoDetailVO detailVO = iResRegularVideoService.getRegularVideoDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        iResRegularVideoService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        iResRegularVideoService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        iResRegularVideoService.deleteByIds(idList);
        return succ();
    }

}
