package com.laien.web.biz.proj.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.core.entity.ResReminder;
import com.laien.web.biz.proj.core.request.ResReminderPageReq;
import com.laien.web.biz.proj.core.service.IProjReminderService;
import com.laien.web.biz.proj.core.service.IResReminderService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

;

/**
 * <p>
 * 通知表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Api(tags = "资源管理:通知")
@RestController
@RequestMapping("/res/reminder")
public class ResReminderController extends ResponseController {

    @Resource
    private IResReminderService resReminderService;

    @Resource
    private IProjReminderService projReminderService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public ResponseResult<PageRes<ResReminder>> page(ResReminderPageReq pageReq) {
        LambdaQueryWrapper<ResReminder> queryWrapper = new LambdaQueryWrapper<>();
        // 标题搜素
        String title = pageReq.getTitle();
        queryWrapper.like(StringUtils.isNotBlank(title), ResReminder::getTitle, title);

        // 内容搜索
        String content = pageReq.getContent();
        queryWrapper.like(StringUtils.isNotBlank(content), ResReminder::getContent, content);

        // 排序
        queryWrapper.orderByDesc(ResReminder::getId);

        // 查询
        Page<ResReminder> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        resReminderService.page(page, queryWrapper);
        PageRes<ResReminder> pageResult = PageConverter.convert(page);
        // 返回
        return succ(pageResult);
    }


    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ResReminder> detail(@PathVariable Integer id) {
        ResReminder resReminder = resReminderService.getById(id);
        if (Objects.isNull(resReminder)) {
            return fail("Data not found");
        }
        return succ(resReminder);
    }

    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ResReminder resReminder) {
        resReminder.setId(null);
        resReminder.setDelFlag(GlobalConstant.NO);
        resReminderService.save(resReminder);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ResReminder resReminder) {
        // 避免通过修改删除数据
        resReminder.setDelFlag(null);
        resReminderService.updateById(resReminder);
        return succ();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/del")
    public ResponseResult<Void> del(@RequestBody IdListReq listReq) {
        List<Integer> idList = listReq.getIdList();
        LambdaUpdateWrapper<ResReminder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ResReminder::getDelFlag, GlobalConstant.YES);
        wrapper.in(ResReminder::getId, idList);
        // 不能使用resReminderService.update(wrapper)，虽然能修改，但是mybatis-plus公共修改字段无效（MetaObjectHandler updateFill）
        // 需要传一个new ResReminder()，不能传null，update(wrapper)实际调用的就是update(null, wrapper)
        resReminderService.update(new ResReminder(), wrapper);
        projReminderService.delByReminderId(listReq.getIdList());
        return succ();
    }


}
