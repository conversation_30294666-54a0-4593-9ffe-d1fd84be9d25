package com.laien.web.biz.proj.core.request;

import com.laien.web.frame.validation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@ApiModel(value = "播放列表保存请求", description = "播放列表保存请求")
public class ProjPlaylistSaveReq {

    @ApiModelProperty(value = "列表名称", required = true)
    @NotBlank(message = "The playlist name cannot be empty", groups = Group1.class)
    @Size(max = 50, message = "The playlist name cannot exceed 50 characters", groups = Group1.class)
    private String playlistName;

    @ApiModelProperty(value = "playlist type", required = true)
    @NotBlank(message = "The playlist type cannot be empty")
    private String playlistType;

    //    @NotBlank(message = "The phone cover image cannot be empty", groups = Group2.class)
    @ApiModelProperty(value = "手机封面图", required = false)
    private String phoneCoverImgUrl;

    //    @NotBlank(message = "The tablet cover image cannot be empty", groups = Group3.class)
    @ApiModelProperty(value = "平板封面图", required = false)
    private String tabletCoverImgUrl;

    //    @NotBlank(message = "The phone detail image cannot be empty", groups = Group4.class)
    @ApiModelProperty(value = "手机详情图", required = false)
    private String phoneDetailImgUrl;

    //    @NotBlank(message = "The tablet detail image cannot be empty", groups = Group5.class)
    @ApiModelProperty(value = "平板详情图", required = false)
    private String tabletDetailImgUrl;

    @NotNull(message = "The subscription cannot be empty", groups = Group5.class)
    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;
}
