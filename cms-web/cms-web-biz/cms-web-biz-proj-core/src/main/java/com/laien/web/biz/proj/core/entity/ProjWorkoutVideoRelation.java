package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * workout video relation
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkoutVideoRelation对象", description="workout video relation")
public class ProjWorkoutVideoRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "workout id")
    private Integer workoutId;

    @ApiModelProperty(value = "exercise id")
    private Integer exerciseId;


}
