package com.laien.web.biz.proj.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.laien.common.core.enums.IEnumBaseService;
import com.laien.common.core.enums.response.ProjectEnumData;
import com.laien.web.biz.proj.core.service.IProjConstantEnumService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProjConstantEnumServiceImpl implements IProjConstantEnumService {

    private final List<IEnumBaseService> enumInterfaces;

    @Override
    public List<ProjectEnumData> getAllEnumByAppCode(String appCode) {
        return Optional.ofNullable(getServiceByAppCode(appCode)).map(service -> service.getAllEnumDataList()).orElse(Collections.emptyList());
    }

    private IEnumBaseService getServiceByAppCode(String appCode) {
        return Optional.ofNullable(appCode).map(code -> enumInterfaces.stream().filter(es -> StrUtil.isNotBlank(es.getAppCode()) && StrUtil.equals(code.toUpperCase(), es.getAppCode().toUpperCase())).findFirst().orElse(null)).orElse(null);
    }

}
