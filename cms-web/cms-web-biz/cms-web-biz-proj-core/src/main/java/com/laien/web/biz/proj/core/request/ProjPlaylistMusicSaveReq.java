package com.laien.web.biz.proj.core.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "播放列表保存请求-音乐列表", description = "播放列表保存请求-音乐列表")
public class ProjPlaylistMusicSaveReq {

    @ApiModelProperty(value = "音乐id")
    private Integer resMusicId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "displayName")
    private String displayName;

}
