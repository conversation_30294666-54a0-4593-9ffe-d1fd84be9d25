package com.laien.web.biz.proj.core.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 项目发布外部实现关联入参
 *
 * <AUTHOR>
 * @since 2024-01-20
 */
@Data
@ApiModel(value = "项目发布外部实现关联入参", description = "项目发布参数")
public class ProjPublishProjRelationParamOutBO {

    @ApiModelProperty(value = "表名")
    private String tableName;
    @ApiModelProperty(value = "关联id名称")
    private String relationIdName;
    @ApiModelProperty(value = "关联的主表名")
    private String mainTableName;

    public ProjPublishProjRelationParamOutBO(String tableEntity, String relationIdName, String mainTableEntity) {
        this.tableName = tableEntity;
        this.relationIdName = relationIdName;
        this.mainTableName = mainTableEntity;
    }
}
