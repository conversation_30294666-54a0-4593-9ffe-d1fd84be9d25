package com.laien.web.biz.proj.core.response;

import com.laien.web.biz.proj.core.request.ProjPlaylistSaveReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "播放列表修改请求", description = "播放列表修改请求")
public class ProjPlaylistDetailVO extends ProjPlaylistSaveReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "音乐列表")
    private List<ProjPlaylistMusicDetailVO> musicList;
}
