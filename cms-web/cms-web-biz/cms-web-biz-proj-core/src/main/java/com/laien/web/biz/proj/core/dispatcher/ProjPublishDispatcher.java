package com.laien.web.biz.proj.core.dispatcher;

import cn.hutool.core.util.StrUtil;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.handler.ProjPublishHandler;
import com.laien.web.biz.proj.core.request.ProjPublishPubOnlineReq;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjPublishLogNewService;
import com.laien.web.biz.proj.core.service.IProjPublishLogService;
import com.laien.web.frame.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class ProjPublishDispatcher {

    private final List<ProjPublishHandler> handlers;
    private final IProjPublishLogService projPublishLogService;
    private final IProjPublishLogNewService projPublishLogNewService;
    private final IProjInfoService projInfoService;

    public Integer dispatch(Integer projId, ProjPublishPubOnlineReq projPublishPubOnlineReq) {
        ProjInfo projInfo = projInfoService.getById(projId);
        if (projInfo == null) {
            throw new BizException("project not found");
        }
        String appCode = projInfo.getAppCode().toLowerCase();
        // 从List中根据appCode 获取对应的handler
        ProjPublishHandler handler = Optional.ofNullable(appCode).map(code ->
                        handlers.stream()
                                .filter(publishHandler ->
                                        StrUtil.isNotBlank(publishHandler.getAppCode()) && StrUtil.equals(code.toUpperCase(), publishHandler.getAppCode().toUpperCase()))
                                .findFirst().orElse(null)).orElse(null);
        if (handler == null) {
            log.info("No Publish handler found for app code:{}, use old publish process ", appCode);
            return projPublishLogService.savePublish(projId,projPublishPubOnlineReq);
        }
        return projPublishLogNewService.savePublish(projInfo,projPublishPubOnlineReq,handler);
    }

}