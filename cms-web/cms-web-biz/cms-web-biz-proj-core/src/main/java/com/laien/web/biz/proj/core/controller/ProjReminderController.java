package com.laien.web.biz.proj.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.web.biz.proj.core.entity.ProjReminder;
import com.laien.web.biz.proj.core.response.ProjReminderListVO;
import com.laien.web.biz.proj.core.service.IProjReminderService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseCode;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目通知表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-20
 */
@Api(tags = "项目管理:通知")
@RestController
@RequestMapping("/proj/reminder")
public class ProjReminderController extends ResponseController {


    @Resource
    private IProjReminderService projReminderService;

    @ApiOperation(value = "查询已使用的通知列表(分页)")
    @GetMapping("/page")
    public ResponseResult<List<ProjReminderListVO>> list() {
        Integer projId = RequestContextUtils.getProjectId();
        if (projId == null) {
            return fail("The project id cannot be empty");
        }
        return succ(projReminderService.getList(projId));
    }

    @ApiOperation(value = "删除通知（支持批量）")
    @PostMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> del(@RequestBody IdListReq idListReq) {
        if (CollectionUtils.isNotEmpty(idListReq.getIdList())) {
            LambdaUpdateWrapper<ProjReminder> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjReminder::getDelFlag, GlobalConstant.YES);
            wrapper.in(ProjReminder::getId, idListReq.getIdList());
            wrapper.eq(ProjReminder::getProjId, RequestContextUtils.getProjectId());
            projReminderService.update(new ProjReminder(), wrapper);
        }
        return succ();
    }


    @ApiOperation(value = "增加")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@Validated(Group.class) @RequestBody IdListReq idListReq) {
        ResponseResult<Void> useReminder = useReminder(RequestContextUtils.getProjectId());
        if (!(useReminder.getCode() == ResponseCode.SUCCESS.getCode())) {
            return useReminder;
        }
        LambdaQueryWrapper<ProjReminder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjReminder::getProjId, RequestContextUtils.getProjectId());
        List<ProjReminder> old = projReminderService.list(queryWrapper);
        Set<Integer> oldReminderIds = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(old)) {
            oldReminderIds = old.stream().map(ProjReminder::getResReminderId).collect(Collectors.toSet());
        }
        Set<Integer> newReminderIds = Sets.newHashSet(idListReq.getIdList());
        Set<Integer> addReminderIds = Sets.difference(newReminderIds, oldReminderIds).immutableCopy();
        Set<Integer> removeReminderIds = Sets.difference(oldReminderIds, newReminderIds).immutableCopy();

        // 删除老的
        if (!removeReminderIds.isEmpty()) {
            LambdaUpdateWrapper<ProjReminder> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(ProjReminder::getDelFlag, GlobalConstant.YES);
            wrapper.in(ProjReminder::getResReminderId, removeReminderIds);
            wrapper.in(ProjReminder::getProjId, RequestContextUtils.getProjectId());
            projReminderService.update(new ProjReminder(), wrapper);
        }
        // 保存新的
        if (!addReminderIds.isEmpty()) {
            List<ProjReminder> batchAddList = Lists.newArrayList();
            for (Integer reminderId : idListReq.getIdList()) {
                if (addReminderIds.contains(reminderId)) {
                    ProjReminder resMusic = new ProjReminder();
                    resMusic.setProjId(RequestContextUtils.getProjectId());
                    resMusic.setResReminderId(reminderId);
                    batchAddList.add(resMusic);
                }
            }
            projReminderService.saveBatch(batchAddList);
        }
        return succ();
    }


    public ResponseResult<Void> useReminder(Integer projId) {
        if (projId == null) {
            return fail("The project id is not empty");
        }
//        int count = projReminderService.count(new LambdaQueryWrapper<ProjReminder>().eq(ProjReminder::getProjId, projId).in(ProjReminder::getResReminderId, reminderId));
//        if (count > 0) {
//            return fail("This is already used by project and cannot be added repeatedly");
//        }
        return succ();
    }

}
