/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.core.workout;

import org.springframework.stereotype.Component;

/**
 * <p>示例workout生成器，仅用于演示 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/1/6 15:03
 */
@Component
public class DemoProjectWorkoutGenerator implements IWorkoutGenerator<String> {

    @Override
    public void generate(WorkoutGenerateParam<String> param) throws Exception{

        // 资源加载

        // workout生成

        // 数据保存

    }

    @Override
    public WorkoutGenerateBizEnum workoutGenerateBiz() {
        return WorkoutGenerateBizEnum.DEMO;
    }
}