package com.laien.web.biz.proj.core.controller;


import com.laien.web.biz.proj.core.entity.ProjWorkoutScene;
import com.laien.web.biz.proj.core.request.ProjWorkoutSceneAddReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutSceneUpdateReq;
import com.laien.web.biz.proj.core.response.ProjWorkoutSceneDetailVO;
import com.laien.web.biz.proj.core.response.ProjWorkoutSceneListVO;
import com.laien.web.biz.proj.core.service.IProjWorkoutSceneService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * workout scene 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Api(tags = "项目管理:workoutScene")
@RestController
@RequestMapping("/proj/workoutScene")
public class ProjWorkoutSceneController extends ResponseController {

    @Resource
    private IProjWorkoutSceneService projWorkoutSceneService;

    @ApiOperation(value = "workoutScene列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjWorkoutSceneListVO>> list() {
        List<ProjWorkoutSceneListVO> list = projWorkoutSceneService.selectWorkoutSceneList();
        return succ(list);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@RequestBody ProjWorkoutSceneAddReq workoutSceneAddReq) {
        projWorkoutSceneService.saveWorkoutScene(workoutSceneAddReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@RequestBody ProjWorkoutSceneUpdateReq workoutSceneUpdateReq) {
        projWorkoutSceneService.updateWorkoutScene(workoutSceneUpdateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjWorkoutSceneDetailVO> detail(@PathVariable Integer id) {
        ProjWorkoutSceneDetailVO detailVO = projWorkoutSceneService.selectWorkoutSceneDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkoutSceneService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkoutSceneService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkoutSceneService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projWorkoutSceneService.saveWorkoutSceneSort(idList);
        return succ();
    }


}
