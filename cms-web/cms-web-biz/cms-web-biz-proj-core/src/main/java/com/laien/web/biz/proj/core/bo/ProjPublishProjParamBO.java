package com.laien.web.biz.proj.core.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 项目发布参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "项目发布参数", description = "项目发布参数")
public class ProjPublishProjParamBO {

    @ApiModelProperty(value = "表名")
    private String tableName;
    @ApiModelProperty(value = "项目id")
    private Integer projId;
    @ApiModelProperty(value = "版本")
    private Integer version;

    public ProjPublishProjParamBO(String tableName, Integer projId, Integer version) {
        this.tableName = tableName;
        this.projId = projId;
        this.version = version;
    }

}
