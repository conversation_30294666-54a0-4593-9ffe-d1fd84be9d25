package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjPlaylist;
import com.laien.web.biz.proj.core.response.ProjPlaylistListVO;

import java.util.List;

/**
 * <p>
 * 播放列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
public interface IProjPlaylistService extends IService<ProjPlaylist> {

    List<ProjPlaylistListVO> getPlayLists(Integer projectId, Integer status);
}
