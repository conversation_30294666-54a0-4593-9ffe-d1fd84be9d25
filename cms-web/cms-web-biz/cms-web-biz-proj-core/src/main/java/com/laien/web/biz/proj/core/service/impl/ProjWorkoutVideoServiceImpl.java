package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.common.file.bo.AudioMergeM3u8BO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.VideoAudioMergeM3u8BO;
import com.laien.web.biz.proj.core.entity.ProjWorkoutVideo;
import com.laien.web.biz.proj.core.entity.ProjWorkoutVideoRelation;
import com.laien.web.biz.proj.core.entity.ResRegularVideo;
import com.laien.web.biz.proj.core.mapper.ProjWorkoutVideoMapper;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoAddReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoExerciseAddReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoPageReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoUpdateReq;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoDetailVO;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoExerciseDetailVO;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoPageVO;
import com.laien.web.common.file.service.FileService;
import com.laien.web.biz.proj.core.service.IProjWorkoutVideoRelationService;
import com.laien.web.biz.proj.core.service.IProjWorkoutVideoService;
import com.laien.web.biz.proj.core.service.IResRegularVideoService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.utils.PageConverter;
import com.laien.web.frame.response.PageRes;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

;

/**
 * <p>
 * workout video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Service
public class ProjWorkoutVideoServiceImpl extends ServiceImpl<ProjWorkoutVideoMapper, ProjWorkoutVideo> implements IProjWorkoutVideoService {

    @Resource
    private IProjWorkoutVideoRelationService projWorkoutVideoRelationService;
    @Resource
    private IResRegularVideoService resRegularVideoService;
    @Resource
    private FileService fileService;

    @Override
    public PageRes<ProjWorkoutVideoPageVO> selectWorkoutVideoPage(ProjWorkoutVideoPageReq pageReq) {
        pageReq.setProjId(RequestContextUtils.getProjectId());
        Page<ProjWorkoutVideoPageVO> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.baseMapper.selectWorkoutVideoPage(page, pageReq);
        List<ProjWorkoutVideoPageVO> records = page.getRecords();
        LocalDateTime now = LocalDateTime.now();
        for (ProjWorkoutVideoPageVO record : records) {
            LocalDateTime newStartTime = record.getNewStartTime();
            LocalDateTime newEndTime = record.getNewEndTime();
            record.setIsNew(GlobalConstant.NO);
            if (Objects.nonNull(newStartTime) && now.isAfter(newStartTime)) {
                record.setIsNew(GlobalConstant.YES);
            }
            if (Objects.nonNull(newEndTime) && now.isAfter(newEndTime)) {
                record.setIsNew(GlobalConstant.NO);
            }
        }
        return PageConverter.convert(page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWorkoutVideo(ProjWorkoutVideoAddReq workoutVideoAddReq) {
        selectNameExists(workoutVideoAddReq.getWorkoutName(), null);
        ProjWorkoutVideo workoutVideo = new ProjWorkoutVideo();
        BeanUtils.copyProperties(workoutVideoAddReq, workoutVideo);
        workoutVideo.setStatus(GlobalConstant.STATUS_DISABLE);
        workoutVideo.setProjId(RequestContextUtils.getProjectId());
        this.save(workoutVideo);

        Integer id = workoutVideo.getId();
        List<ProjWorkoutVideoExerciseAddReq> exerciseList = workoutVideoAddReq.getExerciseList();
        this.saveWorkoutVideoExerciseRelations(id, exerciseList);
        this.saveWorkoutVideoMerge(id, exerciseList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWorkoutVideo(ProjWorkoutVideoUpdateReq workoutVideoUpdateReq) {
        Integer id = workoutVideoUpdateReq.getId();
        selectNameExists(workoutVideoUpdateReq.getWorkoutName(), id);
        ProjWorkoutVideo workoutVideoFind = this.getById(id);
        if (Objects.isNull(workoutVideoFind)) {
            throw new BizException("Data not found");
        }
        ProjWorkoutVideo workoutVideo = new ProjWorkoutVideo();
        BeanUtils.copyProperties(workoutVideoUpdateReq, workoutVideo);
        this.updateById(workoutVideo);

        // 先删除，在新增
        LambdaUpdateWrapper<ProjWorkoutVideoRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkoutVideoRelation::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjWorkoutVideoRelation::getWorkoutId, id);
        projWorkoutVideoRelationService.update(new ProjWorkoutVideoRelation(), wrapper);

        List<ProjWorkoutVideoExerciseAddReq> exerciseList = workoutVideoUpdateReq.getExerciseList();
        this.saveWorkoutVideoExerciseRelations(id, exerciseList);
        this.saveWorkoutVideoMerge(id, exerciseList);
    }

    private void saveWorkoutVideoMerge(Integer id, List<ProjWorkoutVideoExerciseAddReq> exerciseList) {
        if (exerciseList == null || exerciseList.isEmpty()) {
            return;
        }

        List<Integer> exerciseIds = new ArrayList<>(exerciseList.size());
        for (ProjWorkoutVideoExerciseAddReq exerciseAddReq : exerciseList) {
            exerciseIds.add(exerciseAddReq.getId());
        }
        LambdaQueryWrapper<ResRegularVideo> videoWrapperQuery = new LambdaQueryWrapper<>();
        videoWrapperQuery.in(ResRegularVideo::getId, exerciseIds);
        List<ResRegularVideo> regularVideoList = resRegularVideoService.list(videoWrapperQuery);

        int size = regularVideoList.size();
        if (size == 0) {
            return;
        }

        List<TsMergeBO> videoList = new ArrayList<>(size);
        List<TsMergeBO> audioList = new ArrayList<>(size);
        // 按照页面顺序合并
        int durationTotal = 0;
        int mainDuration = 0;
        for (Integer exerciseId : exerciseIds) {
            for (ResRegularVideo regularVideo : regularVideoList) {
                if (Objects.equals(exerciseId, regularVideo.getId())) {
                    int previewDuration = regularVideo.getPreviewDuration();
                    int duration = regularVideo.getDuration();
                    durationTotal += previewDuration;
                    mainDuration += duration;
                    String audioPreviewUrl = fileService.getAbsoluteR2Url(regularVideo.getAudioPreviewUrl());
                    String audioUrl = fileService.getAbsoluteR2Url(regularVideo.getAudioUrl());
                    String videoPreviewUrl = fileService.getAbsoluteR2Url(regularVideo.getVideoPreviewUrl());
                    String videoUrl = fileService.getAbsoluteR2Url(regularVideo.getVideoUrl());
                    // 注意添加顺序，不能变动
                    audioList.add(new TsMergeBO(audioPreviewUrl, previewDuration));
                    audioList.add(new TsMergeBO(audioUrl, duration));
                    videoList.add(new TsMergeBO(videoPreviewUrl, previewDuration));
                    videoList.add(new TsMergeBO(videoUrl, duration));
                    break;
                }
            }
        }
        durationTotal += mainDuration;

        UploadFileInfoRes audioR2Info = fileService.uploadMergeTSForM3U8R2(audioList, "project-workoutVideo-audio");
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTSForM3U8R2(videoList, "project-workoutVideo-video");
        List<AudioMergeM3u8BO> audioI18nList = new ArrayList<>();
        audioI18nList.add(new AudioMergeM3u8BO("en", audioR2Info.getFileUrl()));
        VideoAudioMergeM3u8BO videoAudioMergeM3u8BO = new VideoAudioMergeM3u8BO(videoR2Info.getFileUrl(), durationTotal, audioI18nList);
        UploadFileInfoRes videoPlayInfo = fileService.uploadMergeVideoAudioM3U8R2(videoAudioMergeM3u8BO, "project-workoutVideo-video");

        ProjWorkoutVideo workoutVideo = new ProjWorkoutVideo();
        workoutVideo.setId(id);
        workoutVideo.setAudioUrl(audioR2Info.getFileRelativeUrl());
        workoutVideo.setVideoUrl(videoR2Info.getFileRelativeUrl());
        workoutVideo.setVideoPlayUrl(videoPlayInfo.getFileRelativeUrl());
        workoutVideo.setMainDuration(mainDuration);
        workoutVideo.setDuration(durationTotal);
        this.updateById(workoutVideo);
    }

    /**
     *
     * 保存Workout Video Relation
     *
     * @param id id
     * @param exerciseList exerciseList
     */
    private void saveWorkoutVideoExerciseRelations(Integer id, List<ProjWorkoutVideoExerciseAddReq> exerciseList) {
        if (exerciseList != null && !exerciseList.isEmpty()) {
            List<ProjWorkoutVideoRelation> workoutVideoRelationSaveList = new ArrayList<>();
            for (ProjWorkoutVideoExerciseAddReq workoutVideoExerciseAddReq : exerciseList) {
                ProjWorkoutVideoRelation workoutVideoRelationSave = new ProjWorkoutVideoRelation();
                workoutVideoRelationSave.setWorkoutId(id);
                workoutVideoRelationSave.setExerciseId(workoutVideoExerciseAddReq.getId());
                workoutVideoRelationSaveList.add(workoutVideoRelationSave);
            }
            projWorkoutVideoRelationService.saveBatch(workoutVideoRelationSaveList);
        }
    }

    @Override
    public ProjWorkoutVideoDetailVO selectWorkoutVideoDetail(Integer id) {
        ProjWorkoutVideo workoutVideoFind = this.getById(id);
        if (Objects.isNull(workoutVideoFind)) {
            throw new BizException("Data not found");
        }
        ProjWorkoutVideoDetailVO detailVO = new ProjWorkoutVideoDetailVO();
        BeanUtils.copyProperties(workoutVideoFind, detailVO);

        // 查询关系
        LambdaQueryWrapper<ProjWorkoutVideoRelation> wrapperQuery = new LambdaQueryWrapper<>();
        wrapperQuery.eq(ProjWorkoutVideoRelation::getWorkoutId, id);
        List<ProjWorkoutVideoRelation> relationList = projWorkoutVideoRelationService.list(wrapperQuery);
        List<Integer> exerciseIdList = new ArrayList<>();
        for (ProjWorkoutVideoRelation projWorkoutVideoRelation : relationList) {
            exerciseIdList.add(projWorkoutVideoRelation.getExerciseId());
        }

        if (exerciseIdList.isEmpty()) {
            detailVO.setExerciseList(new ArrayList<>());
            return detailVO;
        }

        // 查询exercise
        LambdaQueryWrapper<ResRegularVideo> videoWrapperQuery = new LambdaQueryWrapper<>();
        videoWrapperQuery.in(ResRegularVideo::getId, exerciseIdList);
        List<ResRegularVideo> regularVideoList = resRegularVideoService.list(videoWrapperQuery);

        // 查询结果拼装
        List<ProjWorkoutVideoExerciseDetailVO> exerciseList = new ArrayList<>(relationList.size());
        for (ProjWorkoutVideoRelation projWorkoutVideoRelation : relationList) {
            for (ResRegularVideo regularVideo : regularVideoList) {
                if (Objects.equals(projWorkoutVideoRelation.getExerciseId(), regularVideo.getId())) {
                    ProjWorkoutVideoExerciseDetailVO exerciseDetailVO = new ProjWorkoutVideoExerciseDetailVO();
                    BeanUtils.copyProperties(regularVideo, exerciseDetailVO);
                    exerciseList.add(exerciseDetailVO);
                    break;
                }
            }
        }
        detailVO.setExerciseList(exerciseList);

        return detailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWorkoutVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkoutVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.eq(ProjWorkoutVideo::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjWorkoutVideo::getId, idList);
        this.update(new ProjWorkoutVideo(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWorkoutVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkoutVideo::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjWorkoutVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjWorkoutVideo::getId, idList);
        this.update(new ProjWorkoutVideo(), wrapper);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjWorkoutVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjWorkoutVideo::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjWorkoutVideo::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjWorkoutVideo::getId, idList);
        this.update(new ProjWorkoutVideo(), wrapper);

        LambdaUpdateWrapper<ProjWorkoutVideoRelation> workoutVideoRelationWrapper = new LambdaUpdateWrapper<>();
        workoutVideoRelationWrapper.set(ProjWorkoutVideoRelation::getDelFlag, GlobalConstant.YES);
        workoutVideoRelationWrapper.in(ProjWorkoutVideoRelation::getWorkoutId, idList);
        projWorkoutVideoRelationService.update(new ProjWorkoutVideoRelation(), workoutVideoRelationWrapper);
    }

    @Override
    public Map<Integer, Integer> getCounts(List<Integer> sceneIdList) {
        QueryWrapper<ProjWorkoutVideo> wrapper = new QueryWrapper<>();
        wrapper.select("count(*) as id, scene_id as sceneId");
        if (sceneIdList != null) {
            wrapper.in("scene_id", sceneIdList);
        }
        wrapper.groupBy("scene_id");
        List<ProjWorkoutVideo> list = this.list(wrapper);

        Map<Integer, Integer> countsMap = new HashMap<>(sceneIdList.size());
        for (Integer sceneId : sceneIdList) {
            countsMap.put(sceneId, GlobalConstant.ZERO);
        }
        for (ProjWorkoutVideo workoutVideo : list) {
            countsMap.put(workoutVideo.getSceneId(), workoutVideo.getId());
        }

        return countsMap;
    }

    private void selectNameExists(String workoutName, Integer id) {
        LambdaQueryWrapper<ProjWorkoutVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkoutVideo::getWorkoutName, workoutName)
                .ne(null != id, ProjWorkoutVideo::getId, id)
                .eq(ProjWorkoutVideo::getProjId, RequestContextUtils.getProjectId());
        int counts = this.count(queryWrapper);
        if(counts > 0){
            throw new BizException("workoutName already exists");
        }
    }
}
