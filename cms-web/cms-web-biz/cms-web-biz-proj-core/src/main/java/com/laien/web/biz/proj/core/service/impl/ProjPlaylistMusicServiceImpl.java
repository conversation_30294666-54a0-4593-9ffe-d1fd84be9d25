package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.entity.ProjPlaylistMusic;
import com.laien.web.biz.proj.core.mapper.ProjPlaylistMusicMapper;
import com.laien.web.biz.proj.core.service.IProjPlaylistMusicService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 播放列表音乐关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@Service
public class ProjPlaylistMusicServiceImpl extends ServiceImpl<ProjPlaylistMusicMapper, ProjPlaylistMusic> implements IProjPlaylistMusicService {

    @Override
    public List<ProjPlaylistMusic> getMusicIdList(Integer playlistId) {
        return getBaseMapper().selectMusicIds(playlistId);
    }
}
