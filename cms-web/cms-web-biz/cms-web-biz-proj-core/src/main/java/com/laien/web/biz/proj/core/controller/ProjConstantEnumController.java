package com.laien.web.biz.proj.core.controller;

import com.laien.common.core.enums.response.ProjectEnumData;
import com.laien.web.biz.proj.core.service.IProjConstantEnumService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "项目管理:项目常量枚举")
@RestController
@RequestMapping("/proj/{appCode}")
@RequiredArgsConstructor
public class ProjConstantEnumController extends ResponseController {

    private final IProjConstantEnumService projConstantEnumService;

    @ApiOperation(value = "根据 appCode 查询所有的枚举信息")
    @GetMapping("/enum/list")
    public ResponseResult<List<ProjectEnumData>> queryAllEnumData(@PathVariable String appCode) {
        return succ(projConstantEnumService.getAllEnumByAppCode(appCode));
    }

}