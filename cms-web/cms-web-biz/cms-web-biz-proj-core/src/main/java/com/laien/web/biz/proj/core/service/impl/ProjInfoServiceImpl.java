package com.laien.web.biz.proj.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.entity.ProjMenu;
import com.laien.web.biz.proj.core.mapper.ProjInfoMapper;
import com.laien.web.biz.proj.core.request.ProjInfoSaveReq;
import com.laien.web.biz.proj.core.response.ProjInfoDetailRes;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjMenuService;
import com.laien.web.common.user.request.PermsCoverSubPerms;
import com.laien.web.common.user.request.PermsSaveOneReq;
import com.laien.web.common.user.request.PermsSaveReq;
import com.laien.web.common.user.service.ISysPermsService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.common.user.vo.PermsVO;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.setting.ResponseResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static com.laien.web.common.user.constant.UserConstant.PERMS_TYPE_MENU;
import static com.laien.web.common.user.constant.UserConstant.PERMS_TYPE_PROJECT;

/**
 * <p>
 * 项目信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Service
public class ProjInfoServiceImpl extends ServiceImpl<ProjInfoMapper, ProjInfo> implements IProjInfoService {

    @Resource
    private IProjMenuService projMenuService;

    @Resource
    private ISysPermsService sysPermsService;

    @Override
    public ProjInfoDetailRes selectProjDetail(Integer id) {
        // 查询项目基本信息
        ProjInfo projInfoSelect = this.getById(id);
        if (Objects.isNull(projInfoSelect)) {
            throw new BizException("Data not found");
        }
        ProjInfoDetailRes detailRes = new ProjInfoDetailRes();
        BeanUtils.copyProperties(projInfoSelect, detailRes);

        detailRes.setLanguages(this.getLanguages(projInfoSelect.getLanguages()));
        detailRes.setTextLanguages(StrUtil.split(projInfoSelect.getLanguages(), GlobalConstant.COMMA));

        // 权限查询
        fillMenuIds(projInfoSelect, detailRes);
        return detailRes;
    }

    private void fillMenuIds(ProjInfo projInfoSelect, ProjInfoDetailRes detailRes) {
        detailRes.setMenuIds(Lists.newArrayList());
        List<String> menuKeys = Lists.newArrayList();
        Integer permsId = projInfoSelect.getPermsId();
        List<PermsVO> subPerms = sysPermsService.getSubPerms(permsId);
        for (PermsVO subPerm : subPerms) {
            String permsKey = subPerm.getPermsKey();
            menuKeys.add(StringUtils.substringAfterLast(permsKey, "_"));
        }
        LambdaQueryWrapper<ProjMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjMenu::getMenuKey, menuKeys);
        List<ProjMenu> list = projMenuService.list(queryWrapper);
        for (ProjMenu projMenu : list) {
            detailRes.getMenuIds().add(projMenu.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveProj(ProjInfoSaveReq saveReq) {
        Integer permsId = 0;
        // 保存项目信息
        ProjInfo projInfoSave = new ProjInfo();
        boolean exist = this.selectAppCodeExists(saveReq.getAppCode(), null);
        if (exist) {
            throw new BizException("App Code exists");
        }
        exist = this.selectAppleIdExists(saveReq.getAppleId(), null);
        if (exist) {
            throw new BizException("Apple ID name exists");
        }
        BeanUtils.copyProperties(saveReq, projInfoSave);
        projInfoSave.setPermsId(permsId);
        this.save(projInfoSave);
        createPerms(saveReq, projInfoSave);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateProj(ProjInfoSaveReq saveReq) {
        // 保存项目信息
        ProjInfo projInfoSave = new ProjInfo();
        boolean exist = this.selectAppCodeExists(saveReq.getAppCode(), saveReq.getId());
        if (exist) {
            throw new BizException("App Code exists");
        }
        exist = this.selectAppleIdExists(saveReq.getAppleId(), saveReq.getId());
        if (exist) {
            throw new BizException("Apple ID name exists");
        }
        BeanUtils.copyProperties(saveReq, projInfoSave);
        this.updateById(projInfoSave);

        // 修改权限
        projInfoSave = getById(saveReq.getId());
        createPerms(saveReq, projInfoSave);
    }

    /**
     * 判断appCode重复
     *
     * @param appCode   appCode
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectAppCodeExists(String appCode, Integer excludeId) {
        LambdaQueryWrapper<ProjInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjInfo::getAppCode, appCode);
        queryWrapper.ne(Objects.nonNull(excludeId), ProjInfo::getId, excludeId);
        int counts = this.count(queryWrapper);
        return counts > GlobalConstant.ZERO;
    }

    /**
     * 判断appleId重复
     *
     * @param appleId   appleId
     * @param excludeId 排除id
     * @return bool
     */
    private boolean selectAppleIdExists(String appleId, Integer excludeId) {
        LambdaQueryWrapper<ProjInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjInfo::getAppleId, appleId);
        queryWrapper.ne(Objects.nonNull(excludeId), ProjInfo::getId, excludeId);
        int counts = this.count(queryWrapper);
        return counts > GlobalConstant.ZERO;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {
        // 删除项目
        LambdaUpdateWrapper<ProjInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjInfo::getDelFlag, GlobalConstant.YES);
        wrapper.in(ProjInfo::getId, idList);
        this.update(new ProjInfo(), wrapper);

        // 删除相关权限

    }

    @Override
    public ProjInfo find(String appCode) {
        LambdaQueryWrapper<ProjInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjInfo::getAppCode, appCode);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public List<ProjInfo> getByAppCodeList(List<String> allAppCode) {
        LambdaQueryWrapper<ProjInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjInfo::getAppCode, allAppCode);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<ProjInfo> find(Collection<String> appCodes) {
        LambdaQueryWrapper<ProjInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjInfo::getAppCode, appCodes);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<String> getLanguagesById(Integer id) {
        ProjInfo projInfo = this.getById(id);
        if (projInfo == null) {
            throw new BizException("Project not found");
        }
        String languages = projInfo.getLanguages();
        return this.getLanguages(languages);
    }

    /**
     * 根据输入的字符串获取语言列表
     * 如果输入的字符串为空或空白，则将默认语言添加到列表中
     * 如果输入的字符串不为空或空白，则将其按逗号分隔成列表，并将默认语言作为第一个元素添加到列表中
     *
     * @param languagesString 语言字符串，以逗号分隔
     * @return 语言列表，包含默认语言和输入字符串中的语言
     */
    private List<String> getLanguages(String languagesString) {
        // 创建一个用于存储语言的列表
        List<String> languages = new ArrayList<>();

        // 添加默认语言到列表
        languages.add(GlobalConstant.DEFAULT_LANGUAGE);

        // 检查输入的字符串是否为空或空白
        if (StringUtils.isNotBlank(languagesString)) {
            // 将输入的字符串按逗号分隔成列表
            String[] splitArray = languagesString.split(",");
            for (String language : splitArray) {
                String trimmedLanguage = language.trim();
                if (!trimmedLanguage.isEmpty() && !trimmedLanguage.equals(GlobalConstant.DEFAULT_LANGUAGE)) {
                    languages.add(trimmedLanguage);
                }
            }
        }

        // 返回最终的语言列表
        return languages;
    }

    private void createPerms(ProjInfoSaveReq saveReq, ProjInfo projInfoSave) {
        // 保存项目权限
        PermsSaveOneReq permsSaveOneReq = new PermsSaveOneReq();
        permsSaveOneReq.setParentId(3);
        permsSaveOneReq.setSubKey(projInfoSave.getId() + "");
        permsSaveOneReq.setComponent("");
        permsSaveOneReq.setIcon("");
        permsSaveOneReq.setPath("");
        permsSaveOneReq.setPermsName(projInfoSave.getAppCode());
        permsSaveOneReq.setPermsType(PERMS_TYPE_PROJECT);
        if (projInfoSave.getPermsId() != null && projInfoSave.getPermsId() > 0) {
            permsSaveOneReq.setId(projInfoSave.getPermsId());
        }
        ResponseResult<Integer> saveResult = sysPermsService.savePerms(permsSaveOneReq);
        //修改项目的permsId
        if (projInfoSave.getPermsId() != null && projInfoSave.getPermsId() == 0) {
            LambdaUpdateWrapper<ProjInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjInfo::getPermsId, saveResult.getData());
            updateWrapper.eq(ProjInfo::getId, projInfoSave.getId());
            update(updateWrapper);
        }
        //创建项目菜单权限
        List<Integer> menuIds = saveReq.getMenuIds();
        if (!menuIds.contains(1)) {
            menuIds.add(1);
        }
        LambdaQueryWrapper<ProjMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjMenu::getId, menuIds);
        List<ProjMenu> menus = projMenuService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(menus)) {
            PermsCoverSubPerms permsBatchAddSubReq = new PermsCoverSubPerms();
            permsBatchAddSubReq.setParentId(saveResult.getData());
            permsBatchAddSubReq.setSubPerms(Lists.newArrayList());
            for (ProjMenu menu : menus) {
                PermsSaveReq permsSaveReq = new PermsSaveReq();
                permsSaveReq.setSubKey(menu.getMenuKey());
                permsSaveReq.setComponent(menu.getComponent());
                permsSaveReq.setIcon(menu.getIcon());
                permsSaveReq.setPath(menu.getPath());
                permsSaveReq.setPermsType(PERMS_TYPE_MENU);
                permsSaveReq.setPermsName(menu.getMenuName());
                permsSaveReq.setSubPerms(Lists.newArrayList());
                permsBatchAddSubReq.getSubPerms().add(permsSaveReq);
                //查看菜单的子节点
                LambdaQueryWrapper<ProjMenu> queryWrapper1 = new LambdaQueryWrapper();
                queryWrapper1.eq(ProjMenu::getParentId, menu.getId());
                List<ProjMenu> subMenus = projMenuService.list(queryWrapper1);
                for (ProjMenu subMenu : subMenus) {
                    PermsSaveReq subSaveReq = new PermsSaveReq();
                    subSaveReq.setSubKey(subMenu.getMenuKey());
                    subSaveReq.setComponent(subMenu.getComponent());
                    subSaveReq.setIcon(subMenu.getIcon());
                    subSaveReq.setPath(subMenu.getPath());
                    subSaveReq.setPermsType(subMenu.getMenuType());
                    subSaveReq.setPermsName(subMenu.getMenuName());
                    permsSaveReq.getSubPerms().add(subSaveReq);
                }
            }
            sysPermsService.coverSubPerms(permsBatchAddSubReq);
        }
        sysPermsService.authorizationPermsByParent("proj\\_" + projInfoSave.getId() + "\\_", RequestContextUtils.getLoginUser().getUserId());
    }
}
