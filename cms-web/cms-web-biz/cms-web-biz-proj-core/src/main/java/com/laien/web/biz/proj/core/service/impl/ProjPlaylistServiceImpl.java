package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.entity.ProjPlaylist;
import com.laien.web.biz.proj.core.mapper.ProjPlaylistMapper;
import com.laien.web.biz.proj.core.response.ProjPlaylistListVO;
import com.laien.web.biz.proj.core.service.IProjPlaylistService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 播放列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@Service
public class ProjPlaylistServiceImpl extends ServiceImpl<ProjPlaylistMapper, ProjPlaylist> implements IProjPlaylistService {

    @Override
    public List<ProjPlaylistListVO> getPlayLists(Integer projId, Integer status) {
        return getBaseMapper().selectListByProjId(projId, status);
    }
}
