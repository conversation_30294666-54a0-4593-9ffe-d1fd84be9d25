package com.laien.web.biz.proj.core.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: regular video 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "regular video 分页", description = "regular video 分页")
public class ResRegularVideoReq extends PageReq {

    @ApiModelProperty(value = "动作名称")
    private String exerciseName;

    @ApiModelProperty(value = "动作名称")
    private String[] equipmentArr;

    @ApiModelProperty(value = "动作名称")
    private String[] targetArr;

    @ApiModelProperty(value = "状态")
    private Integer status;

}
