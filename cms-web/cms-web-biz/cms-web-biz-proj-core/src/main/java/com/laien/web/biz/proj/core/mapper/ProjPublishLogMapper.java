package com.laien.web.biz.proj.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.core.bo.ProjPublishProjParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishResParamBO;
import com.laien.web.biz.proj.core.entity.ProjPublishLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 发布日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
public interface ProjPublishLogMapper extends BaseMapper<ProjPublishLog> {

    /**
     * 发布资源表
     *
     * @param resParamBO resParamBO
     * @return int
     */
    int insertResToCmsApp(ProjPublishResParamBO resParamBO);

    /**
     * 发布项目主表
     *
     * @param projParamBO projParamBO
     * @return int
     */
    int insertProjToCmsApp(ProjPublishProjParamBO projParamBO);

    /**
     * 发布项目关联表
     *
     * @param publishParamBO publishParamBO
     * @return int
     */
    int insertProjRelationToCmsApp(ProjPublishProjRelationParamBO publishParamBO);

    /**
     * 删除项目主表数据
     *
     * @param projParamBO projParamBO
     * @param excludeVersionList excludeVersionList
     * @return int
     */
    int deleteProjToCmsApp(@Param("tableParam") ProjPublishProjParamBO projParamBO,
                           @Param("versionList") List<Integer> excludeVersionList, @Param("inOrNot") boolean inOrNot);

    /**
     * 删除项目关联表数据
     *
     * @param publishParamBO publishParamBO
     * @param excludeVersionList excludeVersionList
     * @return int
     */
    int deleteProjRelationToCmsApp(@Param("tableParam") ProjPublishProjRelationParamBO publishParamBO,
                                   @Param("versionList") List<Integer> excludeVersionList, @Param("inOrNot") boolean inOrNot);

    /**
     * 查询id列表
     *
     * @param tableName tableName
     * @param projId projId
     * @param version version
     * @return list
     */
    List<Integer> selectDetailIds(@Param("tableName") String tableName, @Param("projId") Integer projId, @Param("version") Integer version);

}
