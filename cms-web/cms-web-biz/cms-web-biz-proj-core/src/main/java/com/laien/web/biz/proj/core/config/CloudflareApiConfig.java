package com.laien.web.biz.proj.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * note:
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cloudflare.api")
public class CloudflareApiConfig {

    /**
     * 是否为正式环境
     */
    private Boolean prod;
    /**
     * 认证邮箱
     */
    private String xAuthEmail;
    /**
     * 认证key
     */
    private String xAuthKey;
    /**
     * zone id
     */
    private String zoneId;
    /**
     * 清缓存api地址
     */
    private String purgeCacheUrl;

}
