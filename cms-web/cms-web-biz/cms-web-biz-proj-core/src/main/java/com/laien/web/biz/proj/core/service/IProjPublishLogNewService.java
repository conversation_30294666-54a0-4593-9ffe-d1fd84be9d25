package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.bo.ProjPublishProjParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamBO;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.entity.ProjPublishCurrentVersion;
import com.laien.web.biz.proj.core.entity.ProjPublishLog;
import com.laien.web.biz.proj.core.handler.ProjPublishHandler;
import com.laien.web.biz.proj.core.request.ProjPublishPubOnlineReq;

import java.util.List;

/**
 * <p>
 * 发布日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface IProjPublishLogNewService extends IService<ProjPublishLog> {

    /**
     * 发布
     *
     * @param projInfo  projInfo
     * @param handler handler
     * @return int
     */
    Integer savePublish(ProjInfo projInfo, ProjPublishPubOnlineReq projPublishPubOnlineReq, ProjPublishHandler handler);


}
