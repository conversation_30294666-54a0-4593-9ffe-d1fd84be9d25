package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask;
import com.laien.web.biz.proj.core.workout.WorkoutGenerateBizEnum;
import com.laien.web.biz.proj.core.workout.WorkoutGenerateParam;
import com.laien.web.biz.proj.core.workout.WorkoutGeneratorTaskStatusEnum;

import java.util.List;
import java.util.Map;

/**
 * <p>workout生成任务业务接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 17:15
 */
public interface IProjWorkoutGenerateTaskService extends IService<ProjWorkoutGenerateTask> {

    /**
     * <p>获取当前节点上正在执行的生成任务</p>
     *
     * @return java.util.Map<java.lang.Integer,com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask>
     * <AUTHOR>
     * @date 2025/1/14 11:32
     */
    Map<Integer,ProjWorkoutGenerateTask> getDeepCopyOfRunningTasks();

    /**
     * <p>提交workout生成</p>
     *
     * @param workoutGenerateParam 任务提交参数
     * @return com.laien.cms.workout.generate.WorkoutGeneratorTask 将任务返回给调用方，方便它获取状态信息等
     * <AUTHOR>
     * @date 2024/12/27 14:47
     */
    <T> ProjWorkoutGenerateTask submit(WorkoutGenerateParam<T> workoutGenerateParam);

    /**
     * <p>查询指定项目下，limit数量的已提交任务，按提交时间倒序</p>
     *
     * @param appProject 项目编号,不可空
     * @param limit 限制数量，可空，为空默认查询1个
     * @param statusList 任务状态，可空，为空查询所有状态
     * @param groupIdList 任务分组id，可空，为空查询所有分组
     * @return com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask
     * <AUTHOR>
     * @date 2024/12/27 14:46
     */
    List<ProjWorkoutGenerateTask> getSubmittedTasksWithLimit(WorkoutGenerateBizEnum appProject, List<WorkoutGeneratorTaskStatusEnum> statusList, List<Integer> groupIdList, Integer limit);
}
