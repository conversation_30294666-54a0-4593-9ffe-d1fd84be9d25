package com.laien.web.biz.proj.core.workout;


import com.laien.common.core.enums.IEnumBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * <p>workout生成任务状态 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024/12/27 14:35
 */
@Getter
@AllArgsConstructor
public enum WorkoutGeneratorTaskStatusEnum implements IEnumBase {

    //待处理
    PENDING(1,"Pending","Pending"),
    RUNNING(2,"Running","Running"),
    FAIL(3,"Fail","Fail"),
    SUCCESS(4,"Success","Success"),

    ;
    private final Integer code;
    private final String name;
    private final String displayName;

    /**
     * <p>依据code查找对应的枚举</p>
     *
     * @param code code
     * @return java.util.Optional<com.laien.cms.workout.generate.WorkoutGeneratorTaskStatusEnum>
     * <AUTHOR>
     * @date 2024/12/27 14:42
     */
    public static Optional<WorkoutGeneratorTaskStatusEnum> getByCode(Integer code) {

        return Arrays.stream(WorkoutGeneratorTaskStatusEnum.values())
                .filter(item -> item.code.equals(code))
                .findAny();
    }
}
