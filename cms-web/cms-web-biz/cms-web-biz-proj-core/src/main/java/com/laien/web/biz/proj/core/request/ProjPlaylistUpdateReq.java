package com.laien.web.biz.proj.core.request;

import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "播放列表修改请求", description = "播放列表修改请求")
public class ProjPlaylistUpdateReq extends ProjPlaylistAddReq {

    @ApiModelProperty(value = "数据id")
    @NotNull(message = "The playlist ID cannot be empty", groups = Group1.class)
    private Integer id;
}
