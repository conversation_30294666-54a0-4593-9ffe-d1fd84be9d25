package com.laien.web.biz.proj.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.core.dispatcher.ProjPublishDispatcher;
import com.laien.web.biz.proj.core.entity.ProjPublishLog;
import com.laien.web.biz.proj.core.request.ProjPublishPubOnlineReq;
import com.laien.web.biz.proj.core.response.ProjPublishLogPageVO;
import com.laien.web.biz.proj.core.service.IProjPublishLogService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.response.PageRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 发布日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
@Api(tags = "项目管理: Publish")
@RestController
@RequestMapping("/proj/publish")
public class ProjPublishLogController extends ResponseController {

    @Resource
    private IProjPublishLogService projPublishLogService;
    @Resource
    private ProjPublishDispatcher dispatcher;

    @ApiOperation(value = "查询视频列表(分页)")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjPublishLogPageVO>> list(PageReq pageReq) {
        LambdaQueryWrapper<ProjPublishLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjPublishLog::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.orderByDesc(ProjPublishLog::getId);
        Page<ProjPublishLog> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        projPublishLogService.page(page, queryWrapper);

        List<ProjPublishLog> list = page.getRecords();
        List<ProjPublishLogPageVO> copyList = new ArrayList<>(list.size());
        for (ProjPublishLog projPublishLog : list) {
            ProjPublishLogPageVO pageVO = new ProjPublishLogPageVO();
            BeanUtils.copyProperties(projPublishLog, pageVO);
            copyList.add(pageVO);
        }

        PageRes<ProjPublishLogPageVO> pageRes = new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), copyList);
        return succ(pageRes);
    }

    @ApiOperation(value = "查看版本号")
    @GetMapping("/version")
    public ResponseResult<Integer> version() {
        return succ(projPublishLogService.getVersionByProjId(RequestContextUtils.getProjectId()).getCurrentVersion());
    }

    @ApiOperation(value = "最后发布是否是预发布")
    @GetMapping("/lastPubIsPre")
    public ResponseResult<Boolean> lastPubIsPre() {
        return succ(projPublishLogService.getLastPubIsPre(RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "发布")
    @PostMapping("/pubOnline")
    public ResponseResult<Void> pubOnline(@RequestBody ProjPublishPubOnlineReq projPublishPubOnlineReq) {
        Integer projId = RequestContextUtils.getProjectId();
        if (Objects.isNull(projId)) {
            return fail("Project not found");
        }
        dispatcher.dispatch(projId, projPublishPubOnlineReq);
        return succ();
    }

    @ApiOperation(value = "清理Cloudflare缓存")
    @GetMapping("/purgeCache")
    public ResponseResult<Void> purgeCache() {
        Integer projId = RequestContextUtils.getProjectId();
        if (Objects.isNull(projId)) {
            return fail("Project not found");
        }
        projPublishLogService.purgeCloudflareCache(projId);
        // app code对应项目未创建的projId为0
        projPublishLogService.savePurgeCacheLog(projId);
        return succ();
    }


}

