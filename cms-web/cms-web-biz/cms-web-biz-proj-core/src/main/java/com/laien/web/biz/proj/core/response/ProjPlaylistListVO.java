package com.laien.web.biz.proj.core.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "播放列表视图", description = "播放列表视图")
public class ProjPlaylistListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "playlist type")
    private String playlistType;

    @ApiModelProperty(value = "列表名称")
    private String playlistName;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "手机详情图")
    private String phoneDetailImgUrl;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "音乐数量")
    private Integer musicNum;

    @ApiModelProperty(value = "状态 1 启用 2 禁用")
    private Integer status;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

}
