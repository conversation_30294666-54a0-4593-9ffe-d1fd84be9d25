package com.laien.web.biz.proj.core.bo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * note: Cloudflare 清缓存返回值
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value="Cloudflare 清缓存返回值", description="Cloudflare 清缓存返回值")
public class CloudflarePurgeCacheResult {

    private Map<String, Object> result;
    private Boolean success;
    private List<Map<String,Object>> errors;
    private List<Map<String,Object>> messages;

}
