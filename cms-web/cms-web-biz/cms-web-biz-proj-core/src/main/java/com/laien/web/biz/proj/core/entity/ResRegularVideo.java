package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * regular video
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResRegularVideo对象", description="regular video")
public class ResRegularVideo extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作名称")
    private String exerciseName;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "met")
    private Integer met;

    @ApiModelProperty(value = "必备")
    private String equipment;

    @ApiModelProperty(value = "目的")
    private String target;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "视频预览")
    private String videoPreviewUrl;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "音频预览")
    private String audioPreviewUrl;

    @ApiModelProperty(value = "音频地址")
    private String audioUrl;

    @ApiModelProperty(value = "状态")
    private Integer status;


}
