package com.laien.web.biz.proj.core.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.laien.web.biz.proj.core.bo.ProjPublishProjParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamOutBO;
import com.laien.web.biz.proj.core.request.ProjPublishPubOnlineReq;
import com.laien.web.frame.exception.BizException;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  项目发布handler;
 *  <AUTHOR>
 *  @since 2025-01-20
 */
public interface ProjPublishHandler {

    /**
     * 获取项目特定的表配置
     *
     * @return 项目表的表名
     */
    List<String> getProjTables();

    /**
     * 获取项目特定的关联表配置
     *
     * @return 关联表的表名，以及关联表的外键字段，以及关联表的主表的表名
     */
    List<ProjPublishProjRelationParamOutBO> getProjRelationTables ();


    /**
     * 项目特定逻辑（例如发布前检查）
     * #按需要实现
     */
    default void beforePublishProcess(Integer projId, ProjPublishPubOnlineReq projPublishPubOnlineReq) throws BizException{};

    /**
     * 是否需要新翻译校验(默认true)
     * 按项目需求实现
     */
    default boolean requiresMiddleI18nTaskValidation(){
        return true;
    };

    /**
     * 是否需要旧翻译校验(默认false)
     * 按项目需求实现
     */
    default boolean requiresTranslationTaskValidation(){
        return false;
    };

    /**
     * 声明对应实现类的appCode
     * @return appCode
     */
    String getAppCode();

    /**
     * 获取对象的数据库表名
     */
    default String getTableName(Class clazz) {
        return SqlHelper.table(clazz).getTableName();
    }

    /**
     * 组装完整的ProjPublishProjParamBO
     */
    default List<ProjPublishProjParamBO> getProjParamBOList(Integer projId, Integer version) {
        //获取本身的项目表实体对象后加工
        List<String> projTableEntities = getProjTables();
        if (CollUtil.isEmpty(projTableEntities)) {
            return Collections.emptyList();
        }
        return projTableEntities.stream().map(table -> new ProjPublishProjParamBO(table, projId, version)).
                collect(Collectors.toList());
    }

    /**
     * 组装完整的ProjPublishProjRelationParamBO
     */
    default List<ProjPublishProjRelationParamBO> getProjRelationParamBOList(Integer projId, Integer version) {
        //获取关联表实体对象后加工
        List<ProjPublishProjRelationParamOutBO> projRelationTableEntities = getProjRelationTables();
        if (CollUtil.isEmpty(projRelationTableEntities)) {
            return Collections.emptyList();
        }
        return projRelationTableEntities.stream().map(outBO ->
                        new ProjPublishProjRelationParamBO(outBO.getTableName(), projId, version, outBO.getRelationIdName(), outBO.getMainTableName())
                ). collect(Collectors.toList());
    }
}