package com.laien.web.biz.proj.core.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 项目发布参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "项目发布参数", description = "项目发布参数")
public class ProjPublishProjRelationParamBO {

    @ApiModelProperty(value = "表名")
    private String tableName;
    @ApiModelProperty(value = "项目id")
    private Integer projId;
    @ApiModelProperty(value = "版本")
    private Integer version;
    @ApiModelProperty(value = "关联id名称")
    private String relationIdName;
    @ApiModelProperty(value = "关联的主表名称")
    private String mainTableName;

    public ProjPublishProjRelationParamBO(String tableName, Integer projId, Integer version, String relationIdName, String mainTableName) {
        this.tableName = tableName;
        this.projId = projId;
        this.version = version;
        this.relationIdName = relationIdName;
        this.mainTableName = mainTableName;
    }
}
