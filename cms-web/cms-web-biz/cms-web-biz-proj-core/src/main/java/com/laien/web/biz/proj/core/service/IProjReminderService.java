package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjReminder;
import com.laien.web.biz.proj.core.response.ProjReminderListVO;

import java.util.List;

/**
 * <p>
 * 项目通知表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-20
 */
public interface IProjReminderService extends IService<ProjReminder> {

    List<ProjReminderListVO> getList(Integer projId);

    void delByReminderId(List<Integer> reminderIds);
}
