package com.laien.web.biz.proj.core.controller;

import cn.hutool.core.bean.BeanUtil;
import com.laien.web.biz.proj.core.entity.ProjWorkoutGenerateTask;
import com.laien.web.biz.proj.core.response.ProjWorkoutGenerateTaskVO;
import com.laien.web.biz.proj.core.service.IProjWorkoutGenerateTaskService;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>workout生成任务</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 09:50
 */
@Api(tags = "项目管理:workout生成任务")
@RestController
@RequestMapping("/proj/workout/generate/task")
public class ProjWorkoutGenerateTaskController {

    @Resource
    private IProjWorkoutGenerateTaskService service;

    @ApiOperation(value = "获取当前节点上正在运行的生成任务")
    @GetMapping("/running")
    public ResponseResult<List<ProjWorkoutGenerateTaskVO>> getRunningTask() {
        Map<Integer, ProjWorkoutGenerateTask> deepCopyOfRunningTasks = service.getDeepCopyOfRunningTasks();
        List<ProjWorkoutGenerateTaskVO> taskVOList = BeanUtil.copyToList(deepCopyOfRunningTasks.values(), ProjWorkoutGenerateTaskVO.class);
        return ResponseResult.succ(taskVOList);
    }

}
