package com.laien.web.biz.proj.core.workout;


/**
 * <p>workout生成器 </p>
 * 具体实现请注册到spring容器中，任务执行器将从spring容器中依据项目ID查找对应的生成器执行任务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024/12/27 16:36
 */
public interface IWorkoutGenerator<T> {
    /**
     * <p>实际生成逻辑有子类实现</p>
     *
     * @param param 生成参数
     * @throws Exception 生成过程抛出异常则任务执行失败
     * <AUTHOR>
     * @date 2025/1/6 15:09
     */
    void generate(WorkoutGenerateParam<T> param) throws Exception;

    /**
     * <p>生成业务唯一标识，用于执行生成任务时将生成参数派发到具体的生成器实现上</p>
     *
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/12/27 16:41
     */
    WorkoutGenerateBizEnum workoutGenerateBiz();
}
