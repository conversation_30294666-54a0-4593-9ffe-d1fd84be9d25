package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.request.ProjInfoSaveReq;
import com.laien.web.biz.proj.core.response.ProjInfoDetailRes;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 项目信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
public interface IProjInfoService extends IService<ProjInfo> {

    /**
     * 项目详情
     *
     * @param id id
     * @return ProjInfoDetailRes
     */
    ProjInfoDetailRes selectProjDetail(Integer id);

    /**
     * 新增项目
     *
     * @param saveReq saveReq
     */
    void saveProj(ProjInfoSaveReq saveReq);

    /**
     * 修改项目
     *
     * @param saveReq saveReq
     */
    void updateProj(ProjInfoSaveReq saveReq);


    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIdList(List<Integer> idList);

    /**
     * 查询项目信息
     * @param appCodes appCodes
     * @return list
     */
    List<ProjInfo> find(Collection<String> appCodes);

    /**
     * 根据项目id查询项目支持的语言
     *
     * @param id id
     * @return list
     */
    List<String> getLanguagesById(Integer id);

    ProjInfo find(String appCode);

    List<ProjInfo> getByAppCodeList(List<String> allAppCode);
}
