package com.laien.web.biz.proj.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.enums.ProjAppRequestUriStatusEnums;
import com.laien.web.biz.proj.core.entity.ProjAppRequestUri;
import com.laien.web.biz.proj.core.mapper.ProjAppRequestUriMapper;
import com.laien.web.biz.proj.core.service.IProjAppRequestUriService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 项目请求地址 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service
public class ProjAppRequestUriServiceImpl extends ServiceImpl<ProjAppRequestUriMapper, ProjAppRequestUri> implements IProjAppRequestUriService {

    @Override
    public List<ProjAppRequestUri> selectForTask(Integer projId, Integer maxId, Integer limit, List<String> domain) {
        LambdaQueryWrapper<ProjAppRequestUri> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjAppRequestUri::getProjId, projId);
        queryWrapper.le(Objects.nonNull(maxId), ProjAppRequestUri::getId, maxId);
        queryWrapper.in(CollUtil.isNotEmpty(domain), ProjAppRequestUri::getRequestDomain, domain);
        queryWrapper.eq(ProjAppRequestUri::getStatus, ProjAppRequestUriStatusEnums.ENABLE.getCode());
        queryWrapper.last(Objects.nonNull(limit), " limit " + limit);
        return this.list(queryWrapper);
    }

    @Override
    public Integer getMaxIdForTask(Integer projId, List<String> domain) {
        QueryWrapper<ProjAppRequestUri> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("ifnull(max(id), 0) sortNo");
        queryWrapper.eq("proj_id", projId);
        queryWrapper.in(CollUtil.isNotEmpty(domain), "request_domain", domain);
        queryWrapper.eq("status", ProjAppRequestUriStatusEnums.ENABLE.getCode());
        return this.getObj(queryWrapper,o -> Integer.parseInt(o.toString()));

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteRealByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        for (Integer id : ids) {
            this.baseMapper.deleteRealById(id);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        LambdaUpdateWrapper<ProjAppRequestUri> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjAppRequestUri::getId, ids);
        updateWrapper.set(ProjAppRequestUri::getStatus, ProjAppRequestUriStatusEnums.DISABLE.getCode());
        updateWrapper.set(ProjAppRequestUri::getUpdateTime, LocalDateTime.now());
        this.update(updateWrapper);
    }

}
