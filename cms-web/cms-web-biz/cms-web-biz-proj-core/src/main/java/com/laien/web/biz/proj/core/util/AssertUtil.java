/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.core.util;

import com.laien.web.frame.exception.BizException;

/**
 * <p>断言工具，抛出异常 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class AssertUtil {

    private AssertUtil() {}

    public static void notNull(Object object, String message) {
        isFalse(null == object, message);
    }

    public static void isTrue(boolean isTrue, String message) {

        if(!isTrue) {
            throw new BizException(message);
        }
    }

    public static void isFalse(boolean isFalse, String message) {
        isTrue(!isFalse, message);
    }
}