package com.laien.web.biz.proj.core.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.web.biz.proj.core.workout.WorkoutGenerateBizEnum;
import com.laien.web.biz.proj.core.workout.WorkoutGeneratorTaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>workout生成任务</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 10:32
 */
@Data
@ApiModel(value = "workout生成任务", description = "workout生成任务")
public class ProjWorkoutGenerateTaskVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 项目唯一标识，{@link WorkoutGenerateBizEnum#getCode()}
     */
    private WorkoutGenerateBizEnum appProject;
    /**
     * 扩展参数，将具体app传入的扩展数据转为JSON保存
     */
    private String extendDataJson;
    /**
     * 任务执行状态, {@link WorkoutGeneratorTaskStatusEnum#getCode()}
     */
    private WorkoutGeneratorTaskStatusEnum status;
}
