package com.laien.web.biz.proj.core.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 播放列表音乐关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPlaylistMusic对象", description="播放列表音乐关联表")
public class ProjPlaylistMusic extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "播放列表id")
    private Integer projPlaylistId;

    @ApiModelProperty(value = "音乐id")
    private Integer resMusicId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "music展示名称")
    @TranslateField
    private String displayName;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;


}
