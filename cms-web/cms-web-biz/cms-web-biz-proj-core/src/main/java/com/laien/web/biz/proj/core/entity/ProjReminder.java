package com.laien.web.biz.proj.core.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjReminder对象", description="项目通知表")
public class ProjReminder extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "通知id")
    private Integer resReminderId;


}
