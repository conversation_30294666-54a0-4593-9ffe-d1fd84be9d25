package com.laien.web.biz.proj.core.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout video 分页
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="workout video 分页", description="workout video 分页")
public class ProjWorkoutVideoPageReq extends PageReq {

    @ApiModelProperty(value = "场景id")
    private Integer[] sceneIdArr;

    @ApiModelProperty(value = "锻炼名称")
    private String workoutName;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "焦点")
    private String[] focusArr;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "id")
    private Integer projId;

}
