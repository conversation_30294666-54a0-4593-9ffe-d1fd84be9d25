package com.laien.web.biz.proj.core.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout scene list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="workout scene list", description="workout scene list")
public class ProjWorkoutSceneListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景类型")
    private String sceneType;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "排序编号")
    private Integer counts;

}
