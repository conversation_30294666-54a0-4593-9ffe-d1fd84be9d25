package com.laien.web.biz.proj.core.request;

import com.laien.web.frame.validation.Group2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "新增项目通知请求", description = "新增项目通知请求")
public class ProjReminderAddReq {

    @NotNull(message = "The reminder id cannot be empty", groups = Group2.class)
    @ApiModelProperty(value = "通知id", required = true)
    private Integer resReminderId;
}
