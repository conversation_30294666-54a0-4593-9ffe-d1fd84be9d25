package com.laien.web.biz.proj.core.mapper;

import com.laien.web.biz.proj.core.entity.ProjPlaylistMusic;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 播放列表音乐关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-15
 */
public interface ProjPlaylistMusicMapper extends BaseMapper<ProjPlaylistMusic> {


    @Select("select proj_playlist_id,res_music_id,subscription,short_link,display_name from proj_playlist_music where  del_flag=0 and proj_playlist_id=#{playlistId}")
    List<ProjPlaylistMusic> selectMusicIds(@Param("playlistId") Integer playlistId);

    @Select("delete proj_playlist_music where proj_playlist_id=#{playlistId}")
    void deleteRelation();
}
