package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjWorkoutVideo;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoAddReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoPageReq;
import com.laien.web.biz.proj.core.request.ProjWorkoutVideoUpdateReq;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoDetailVO;
import com.laien.web.biz.proj.core.response.ProjWorkoutVideoPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * workout video 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
public interface IProjWorkoutVideoService extends IService<ProjWorkoutVideo> {

    /**
     * workout video分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjWorkoutVideoPageVO> selectWorkoutVideoPage(ProjWorkoutVideoPageReq pageReq);

    /**
     * workout video 保存
     *
     * @param workoutVideoAddReq workoutVideoAddReq
     */
    void saveWorkoutVideo(ProjWorkoutVideoAddReq workoutVideoAddReq);

    /**
     * workout video 修改
     *
     * @param workoutVideoUpdateReq workoutVideoUpdateReq
     */
    void updateWorkoutVideo(ProjWorkoutVideoUpdateReq workoutVideoUpdateReq);

    /**
     * workout video 详情
     *
     * @param id id
     * @return ProjWorkoutVideoDetailVO
     */
    ProjWorkoutVideoDetailVO selectWorkoutVideoDetail(Integer id);

    /**
     * workout video 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * workout video 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * workout video 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 根据sceneId查询统计数
     *
     * @param sceneIdList sceneIdList
     * @return list
     */
    Map<Integer, Integer> getCounts(List<Integer> sceneIdList);

}
