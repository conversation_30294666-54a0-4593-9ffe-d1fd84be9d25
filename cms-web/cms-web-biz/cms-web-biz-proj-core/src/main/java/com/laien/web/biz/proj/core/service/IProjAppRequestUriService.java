package com.laien.web.biz.proj.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjAppRequestUri;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 项目请求地址 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface IProjAppRequestUriService extends IService<ProjAppRequestUri> {

    /**
     * 查询需要清除缓存的请求地址
     *
     * @param projId 项目id 必传
     * @param maxId  清理缓存的最大id
     * @param limit 一次查询次数，可为空，空查询全部
     * @param domain 请求域名
     * @return list
     */
    List<ProjAppRequestUri> selectForTask(Integer projId, Integer maxId, Integer limit, List<String> domain);

    /**
     * 查询最大的id
     *
     * @param projId projId
     * @param requestUriStart requestUriStart
     * @return
     */
    Integer getMaxIdForTask(Integer projId, List<String> domain);

    /**
     * 根据id列表真删除
     * @param ids ids
     */
    void deleteRealByIds(List<Integer> ids);

    @Transactional(rollbackFor = Exception.class)
    void deleteByIds(List<Integer> ids);
}
