package com.laien.web.biz.proj.core.service.impl;

import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.ColumnCache;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.laien.web.biz.proj.core.mapper.LanguageMapper;
import com.laien.web.biz.proj.core.service.LanguageService;
import com.laien.web.frame.entity.BaseModel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * note:
 *
 * <AUTHOR>
 */
@Service
public class LanguageServiceImpl implements LanguageService {

    @Resource
    private LanguageMapper languageMapper;

    @Override
    public void saveLanguage(Map<String, Map<String, String>> lang, BaseModel entity) {
        if (lang == null) {
            return;
        }

        Class clz = entity.getClass();
        // 获取表名
        String tableName = SqlHelper.table(clz).getTableName();
        // 获取对应的实体对应的字段名
        Map<String, ColumnCache> columnMap = LambdaUtils.getColumnMap(clz);
        Integer id = entity.getId();
        Field[] fields = clz.getDeclaredFields();
        for (Map.Entry<String, Map<String, String>> mapEntry : lang.entrySet()) {
            String langKey = mapEntry.getKey();
            Map<String, String> map = mapEntry.getValue();
            Map<String, Object> column = new HashMap<>();
            for (Field field : fields) {
                String fieldName = field.getName();
                if (map.containsKey(fieldName)) {
                    ColumnCache columnCache = columnMap.get(LambdaUtils.formatKey(fieldName));
                    column.put(columnCache.getColumn(), map.get(fieldName));
                }
            }

            if (!column.isEmpty()) {
                column.put("id", id);
                column.put("language", langKey);
                languageMapper.insertLanguage(tableName, column);
            }

        }


    }

}
