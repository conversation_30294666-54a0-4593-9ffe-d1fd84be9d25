<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cms-web-biz</artifactId>
        <groupId>com.laien</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cms-web-biz-proj-core</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <artifactId>cms-web-biz-resource</artifactId>
            <groupId>com.laien</groupId>
        </dependency>

        <!-- openfeign 服务间访问-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-common-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-common-lms</artifactId>
        </dependency>
    </dependencies>
</project>
