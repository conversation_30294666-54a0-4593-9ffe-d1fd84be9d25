<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cms-web-biz</artifactId>
        <groupId>com.laien</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cms-web-biz-core</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <artifactId>cms-web-frame</artifactId>
            <groupId>com.laien</groupId>
        </dependency>
        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-common-m3u8-seq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-web-common-user</artifactId>
        </dependency>
        <dependency>
            <groupId>com.laien</groupId>
            <artifactId>cms-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- nacos 注册中心、配置中心-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
    </dependencies>
</project>
