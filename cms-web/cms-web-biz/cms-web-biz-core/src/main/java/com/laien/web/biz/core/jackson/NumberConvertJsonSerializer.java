package com.laien.web.biz.core.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.laien.web.biz.core.annotation.NumberConvert;
import com.laien.web.biz.core.enums.NumberConvertEnums;
import com.laien.web.biz.core.util.NumberUtils;

import java.io.IOException;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
public class NumberConvertJsonSerializer extends JsonSerializer<Number> implements ContextualSerializer {

    private NumberConvertEnums numberConvertEnums;
    private int scale;
    private RoundingMode roundingMode;

    @Override
    public void serialize(Number s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (null == s) {
            jsonGenerator.writeNumber(0);
            return;
        }
        if (NumberConvertEnums.MILLIS_TO_MINUTES == numberConvertEnums) {
            jsonGenerator.writeNumber(NumberUtils.millisToMinutes(s.longValue(), scale, roundingMode));
            return;
        } else if (NumberConvertEnums.MILLIS_TO_SECOND == numberConvertEnums) {
            jsonGenerator.writeNumber(NumberUtils.millisToSecond(s.longValue(), scale, roundingMode));
            return;
        }
        jsonGenerator.writeNumber(s.toString());
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        NumberConvert annotation = beanProperty.getAnnotation(NumberConvert.class);
        if (null != annotation && Number.class.isAssignableFrom(beanProperty.getType().getRawClass())) {
            this.numberConvertEnums = annotation.value();
            this.scale = annotation.scale();
            this.roundingMode = annotation.roundingMode();
            return this;
        }
        return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
    }
}
