package com.laien.web.biz.core.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
public final class NumberUtils {

    /**
     * 毫秒转分钟
     *
     * @param millis       毫秒
     * @param scale        保留几位小数
     * @param roundingMode 取整方式
     * @return 分钟
     */
    public static Long millisToMinutes(Long millis, int scale, RoundingMode roundingMode) {
        if (null == millis) {
            return 0L;
        }
        BigDecimal minutes = new BigDecimal(millis).divide(new BigDecimal(60 * 1000), scale, roundingMode);
        return minutes.longValue();
    }

    public static Long millisToSecond(Long millis, int scale, RoundingMode roundingMode) {
        if (null == millis) {
            return 0L;
        }
        BigDecimal minutes = new BigDecimal(millis).divide(new BigDecimal(1000), scale, roundingMode);
        return minutes.longValue();
    }


}
