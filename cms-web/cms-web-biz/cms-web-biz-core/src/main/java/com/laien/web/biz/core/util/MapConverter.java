package com.laien.web.biz.core.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.cglib.beans.BeanMap;

import java.util.HashMap;
import java.util.Map;

/**
 * note:
 *
 * <AUTHOR>
 */
public class MapConverter {

    /**
     * 将对象属性转化为map结合
     */
    public static <T> Map<Object, Object> beanToMap(T bean) {
        int initialCapacity = 16;
        Map<Object, Object> map = new HashMap<>(initialCapacity);
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                map.put(key, beanMap.get(key));
            }
        }

        return map;
    }

    /**
     * 将对象属性转化为map结合,忽略空字符
     */
    public static <T> Map<Object, Object> beanToMapIgnEmptyStr(T bean) {
        int initialCapacity = 16;
        Map<Object, Object> map = new HashMap<>(initialCapacity);
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                Object obj = beanMap.get(key);
                if (obj != null && !obj.toString().trim().equals(GlobalConstant.EMPTY_STRING)) {
                    map.put(key, obj);
                }
            }
        }

        return map;
    }

    /**
     * 将对象属性空字符设置为null，并返回新对象
     */
    public static <T> T newBeanIgnEmptyStr(T bean, Class<T> beanType) {
        Map<Object, Object> map = beanToMapIgnEmptyStr(bean);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String json = objectMapper.writeValueAsString(map);
            T t = objectMapper.readValue(json, beanType);
            return t;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return null;
    }


}
