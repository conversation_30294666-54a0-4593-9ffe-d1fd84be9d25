package com.laien.web.biz.core.converter;

import com.alibaba.excel.converters.string.StringStringConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;
import java.util.Optional;

public class StringStringTrimConverter extends StringStringConverter {
    @Override
    public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        String value;
        if (CellDataTypeEnum.NUMBER.equals(cellData.getType())) {
            BigDecimal bigDecimal = cellData.getNumberValue();
            if (bigDecimal == null) {
                return null;
            }
            value = bigDecimal.toString();
        } else {
            value = cellData.getStringValue();
        }
        return Optional.ofNullable(value).map(n -> n.trim()).get();
    }

    @Override
    public CellData convertToExcelData(String value, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration) {
        return new CellData(Optional.ofNullable(value).map(n -> n.trim()).get());
    }
}