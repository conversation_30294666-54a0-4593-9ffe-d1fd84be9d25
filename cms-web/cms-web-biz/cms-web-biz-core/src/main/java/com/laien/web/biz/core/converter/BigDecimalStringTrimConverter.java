package com.laien.web.biz.core.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 兼容 Excel 数值和字符串的 BigDecimal 转换器，支持保留3位小数
 */
@Slf4j
public class BigDecimalStringTrimConverter implements Converter<BigDecimal> {

    @Override
    public Class supportJavaTypeKey() {
        return BigDecimal.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public BigDecimal convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        if (cellData == null) {
            return null; // 处理空单元格
        }

        try {
            if (CellDataTypeEnum.NUMBER.equals(cellData.getType())) {
                // 处理 Excel 数字类型
                BigDecimal bigDecimal = cellData.getNumberValue();
                return (bigDecimal != null) ? bigDecimal.setScale(3, RoundingMode.HALF_UP) : null;
            } else if (CellDataTypeEnum.STRING.equals(cellData.getType())) {
                // 处理 Excel 字符串类型
                String value = cellData.getStringValue();
                if (value == null || value.trim().isEmpty()) {
                    return null; // 处理空字符串
                }
                return new BigDecimal(value.trim()).setScale(3, RoundingMode.HALF_UP); // 解析字符串为 BigDecimal
            }
        } catch (NumberFormatException e) {
            log.warn("BigDecimalStringTrimConverter: 无法解析 [{}] 为 BigDecimal", cellData.getStringValue(), e);
        } catch (Exception e) {
            log.error("BigDecimalStringTrimConverter: 解析异常 [{}]", cellData, e);
        }

        return null; // 解析失败返回 null
    }

    @Override
    public CellData<?> convertToExcelData(BigDecimal value, ExcelContentProperty contentProperty,
                                          GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new CellData<>("");
        }
        // 确保输出时保留3位小数
        return new CellData<>(value.setScale(3, RoundingMode.HALF_UP).toString());
    }
} 