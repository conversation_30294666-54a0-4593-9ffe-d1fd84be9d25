package com.laien.web.biz.core.intercept;//package com.laien.common.intercept;
//
//import com.laien.mybatisplus.config.BaseModel;
//import org.apache.ibatis.executor.resultset.ResultSetHandler;
//import org.apache.ibatis.plugin.*;
//
//import java.sql.Statement;
//import java.util.List;
//import java.util.Properties;
//
///**
// * 结果集拦截器
// */
//@Intercepts({
//        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class}),
//})
//public class ResultHandlerInterceptor implements Interceptor {
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        // 调用目标方法
//        List<Object> result = (List<Object>) invocation.proceed();
//        if (result != null && result.size() > 0) {
//            Class objClass = null;
//            for (Object o : result) {
//                if (o != null) {
//                    objClass = o.getClass();
//                }
//            }
//            if (objClass != null && BaseModel.class.isAssignableFrom(objClass)) {
//                //修改属性
//                System.out.println(objClass.getName());
//            }
//        }
//        return result;
//    }
//
//    @Override
//    public Object plugin(Object o) {
//        return Plugin.wrap(o, this);
//
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//
//    }
//}