package com.laien.web.biz.core.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.laien.common.core.enums.IEnumBase;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class GenericEnumListNameConverter implements Converter<List<IEnumBase>> {

    /**
     * 利用 contentProperty 反射获取 List 中实际的枚举类型
     */
    private Class<? extends IEnumBase> getEnumClass(ExcelContentProperty contentProperty) {
        if (contentProperty != null && contentProperty.getField() != null) {
            Type genericType = contentProperty.getField().getGenericType();
            if (genericType instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) genericType;
                Type actualType = pt.getActualTypeArguments()[0];
                return (Class<? extends IEnumBase>) actualType;
            }
        }
        return null;
    }

    @Override
    public Class supportJavaTypeKey() {
        return List.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public List<IEnumBase> convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                             GlobalConfiguration globalConfiguration){
        String cellValue = cellData.getStringValue();
        if (cellValue == null || cellValue.trim().isEmpty()) {
            return Collections.emptyList();
        }
        Class<? extends IEnumBase> enumClass = getEnumClass(contentProperty);
        if (enumClass == null) {
            log.error("无法从 contentProperty 中确定 List 中的枚举类型");
            throw new IllegalStateException("无法从 contentProperty 中确定 List 中的枚举类型");
        }
        return Arrays.stream(cellValue.split(StringPool.COMMA))
                .map(String::trim)
                .map(name -> Arrays.stream(enumClass.getEnumConstants())
                        .filter(e -> e.getName().equals(name))
                        .findFirst()
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public CellData convertToExcelData(List<IEnumBase> value, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration) {
        String result = "";
        if (value != null && !value.isEmpty()) {
            result = value.stream().map(IEnumBase::getName).collect(Collectors.joining(StringPool.COMMA));
        }
        return new CellData(result);
    }
}
