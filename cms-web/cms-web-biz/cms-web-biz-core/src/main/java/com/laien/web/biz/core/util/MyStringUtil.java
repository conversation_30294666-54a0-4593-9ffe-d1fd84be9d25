package com.laien.web.biz.core.util;

import com.laien.web.frame.constant.GlobalConstant;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * note:
 *
 * <AUTHOR>
 */
public final class MyStringUtil {

    /**
     * 拼接逗号
     *
     * @param strArr strArr
     * @return String
     */
    public static String getJoinWithComma(String[] strArr) {
        if (strArr == null || strArr.length == GlobalConstant.ZERO) {
            return GlobalConstant.EMPTY_STRING;
        }
        return String.join(GlobalConstant.COMMA, strArr);
    }

    /**
     * 拆分逗号
     *
     * @param str str
     * @return String[]
     */
    public static String[] getSplitWithComa(String str) {
        if (StringUtils.isBlank(str)) {
            return new String[]{};
        }
        return str.split(GlobalConstant.COMMA);
    }

    /**
     * 按照换行符拆分成数组
     *
     * @param string string
     * @return list
     */
    public static List<String> toListWithNewLine(String string) {
        List<String> stringList = new ArrayList<>();
        String[] arr = string.split("\n");
        for (String s : arr) {
            if (StringUtils.isNotBlank(s)) {
                stringList.add(s);
            }
        }
        return stringList;
    }

    /**
     * 删除结尾的(left),(right) 并去除空格
     *
     * @param str str
     * @return String
     */
    public static String delLeftRightTrim(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }

        String left = "(left)";
        if (str.toLowerCase().endsWith(left)) {
            return str.substring(0, str.length() - left.length()).trim();
        }

        String right = "(right)";
        if (str.toLowerCase().endsWith(right)) {
            return str.substring(0, str.length() - right.length()).trim();
        }
        return str.trim();
    }

}

