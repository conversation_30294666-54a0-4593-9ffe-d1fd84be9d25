package com.laien.web.biz.core.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 兼容 Excel 数值和字符串的 Integer 转换器
 */
@Slf4j
public class IntegerStringTrimConverter implements Converter<Object> {

    @Override
    public Class supportJavaTypeKey() {
        return Object.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Object convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        if (cellData == null) {
            return null; // 处理空单元格
        }

        try {
            if (CellDataTypeEnum.NUMBER.equals(cellData.getType())) {
                // 处理 Excel 数字类型
                BigDecimal bigDecimal = cellData.getNumberValue();
                return (bigDecimal != null) ? bigDecimal.intValue() : null;
            } else if (CellDataTypeEnum.STRING.equals(cellData.getType())) {
                // 处理 Excel 字符串类型
                String value = cellData.getStringValue();
                if (value == null || value.trim().isEmpty()) {
                    return null; // 处理空字符串
                }
                return Integer.parseInt(value.trim()); // 解析字符串为 Integer
            }
        } catch (NumberFormatException e) {
            log.warn("IntegerStringTrimConverter: 无法解析 [{}] 为 Integer", cellData.getStringValue(), e);
        } catch (Exception e) {
            log.error("IntegerStringTrimConverter: 解析异常 [{}]", cellData, e);
        }

        return null; // 解析失败返回 null
    }

    @Override
    public CellData<?> convertToExcelData(Object value, ExcelContentProperty contentProperty,
                                          GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new CellData<>("");
        }
        return new CellData<>(String.valueOf(value));
    }
}
