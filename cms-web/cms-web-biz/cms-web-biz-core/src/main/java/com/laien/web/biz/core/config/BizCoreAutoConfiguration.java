package com.laien.web.biz.core.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/9/26
 */
@Configuration
@ComponentScan(value = "com.laien.web.biz.core")
@ConditionalOnProperty(prefix = "laien.common", name = "enable", havingValue = "true", matchIfMissing = true)
public class BizCoreAutoConfiguration {
}
