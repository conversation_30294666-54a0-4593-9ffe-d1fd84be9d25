package com.laien.web.biz.core.sentinel;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.callback.BlockExceptionHandler;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.laien.web.frame.response.setting.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description 流控统一异常处理类（就不需要@SentinelResource注解）
 * @Date 2022-04-13 14:43
 * <AUTHOR>
 */
@Component
@Slf4j
public class MyBlockExceptionHandler implements BlockExceptionHandler {

    @Override
    public void handle(
            HttpServletRequest request,
            HttpServletResponse response,
            BlockException e) throws Exception {
        ResponseResult<Object> responseResult = null;
        if (e instanceof FlowException) {
            responseResult = ResponseResult.fail("接口限流了");
        } else if (e instanceof DegradeException) {
            responseResult = ResponseResult.fail("服务降级了");

        } else if (e instanceof ParamFlowException) {
            responseResult = ResponseResult.fail("热点参数限流了");
        } else if (e instanceof SystemBlockException) {
            responseResult = ResponseResult.fail("触发系统保护规则了");
        } else if (e instanceof AuthorityException) {
            responseResult = ResponseResult.fail("授权规则不通过");
        }
        //返回json数据
        response.setStatus(200);
        response.setCharacterEncoding("utf8");
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        new ObjectMapper().writeValue(response.getWriter(), responseResult);
    }

}