package com.laien.web.biz.core.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.laien.web.biz.core.enums.NumberConvertEnums;
import com.laien.web.biz.core.jackson.NumberConvertJsonSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.RoundingMode;

import static com.laien.web.biz.core.enums.NumberConvertEnums.MILLIS_TO_MINUTES;
import static java.math.RoundingMode.HALF_UP;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@JacksonAnnotationsInside
@JsonSerialize(using = NumberConvertJsonSerializer.class)
public @interface NumberConvert {

    NumberConvertEnums value() default MILLIS_TO_MINUTES;


    int scale() default 0;

    RoundingMode roundingMode() default HALF_UP;

}
