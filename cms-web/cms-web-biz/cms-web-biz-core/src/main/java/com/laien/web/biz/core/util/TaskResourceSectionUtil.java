package com.laien.web.biz.core.util;


import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
public class TaskResourceSectionUtil {

    public static List<Integer> getCompletedIdList(List<TaskResourceSection> taskList) {
        Map<Integer, List<TaskResourceSection>> taskMap = taskList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        List<Integer> completedIdList = taskMap.entrySet().stream()
                .filter(item -> {
                    List<TaskResourceSection> tasks = item.getValue();
                    if (tasks.size() != 2) {
                        return false;
                    }
                    for (TaskResourceSection taskItem : tasks) {
                        if (TaskResourceSectionStatusEnums.COMPLETED != taskItem.getStatus()) {
                            return false;
                        }
                    }
                    return true;
                }).map(Map.Entry::getKey).collect(Collectors.toList());
        return completedIdList;
    }

    public static Set<Integer> listNotCompletedId(List<TaskResourceSection> taskList) {
        Map<Integer, List<TaskResourceSection>> taskMap = taskList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        Set<Integer> completedIdList = taskMap.entrySet().stream()
                .filter(item -> {
                    for (TaskResourceSection taskItem : item.getValue()) {
                        if (TaskResourceSectionStatusEnums.COMPLETED != taskItem.getStatus()) {
                            return true;
                        }
                    }
                    return false;
                }).map(Map.Entry::getKey).collect(Collectors.toSet());
        return completedIdList;
    }
}
