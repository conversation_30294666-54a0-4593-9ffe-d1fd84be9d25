package com.laien.web.biz.core.util;

public class SqlUtils {

    public static String escapeLikeValue(String likeValue) {
        if (likeValue == null) {
            return null;
        }
        char[] chars = likeValue.toCharArray();
        int len = chars.length;
        int offset = 0;
        char[] result = new char[len * 2];
        for (int i = 0; i < len; i++) {
            char c = chars[i];
            // 特殊字符增加转义
            if (c == '\\' || c == '_' || c == '%') {
                result[offset++] = '\\';
            }
            result[offset++] = c;
        }
        return new String(result, 0, offset);
    }
}
