package com.laien.web.biz.core.constant;


import java.util.concurrent.TimeUnit;

/**
 * note:
 *
 * <AUTHOR>
 */
public interface RedisKeyConstant {



    /**
     * 移动端接口表达式
     */
    String APP_PROJECT_KEY_PATTERN = "cmsApp:*:p-{projId}:v-{version}";

    /**
     * 当期项目发布信息key
     */
    String APP_PROJECT_PUBLISH = "cmsApp:projectPublish:";

    /**
     * 移动端接口 项目相关接口需要符合 cmsApp:*:p-{projId}:v-{version}
     */
    String APP_DAILY_KEY = "daily";
    String APP_PLAYLIST_KEY = "playlist";
    String APP_REMINDER_KEY = "reminder";
    String APP_SOUND_FLOW_KEY = "soundFlow";
    String APP_WORKOUT_VIDEO_KEY = "workoutVideo";
    String APP_WORKOUT_VIDEO120_KEY = "workoutVideo120";

    /**
     * project的workout集合key模板
     */
    String APP_PROJECT_WORKOUT_KEY_TEMPLATE = "workout";
    String APP_PROJECT_WORKOUT_LOCK = "workout";
    /**
     * project的program集合key模板
     */
    String APP_PROJECT_PROGRAM_KEY_TEMPLATE = "program";
    String APP_PROJECT_PROGRAM_LOCK = "program";

    /**
     * project的program集合key模板
     */
    String APP_PROJECT_COLLECTION_KEY_TEMPLATE = "collection";
    String APP_PROJECT_COLLECTION_LOCK = "collection";

    /**
     * app project search缓存时间
     */
    long APP_PROJECT_SEARCH_CATCH_TIME = 3;
    /**
     * app project search缓存时间单位
     */
    TimeUnit APP_PROJECT_SEARCH_CATCH_TIME_UNIT = TimeUnit.MINUTES;

}
