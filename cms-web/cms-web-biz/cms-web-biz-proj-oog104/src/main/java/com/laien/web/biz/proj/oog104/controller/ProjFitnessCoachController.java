package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoach;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCoachService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Fitness Coach 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */

@Api(tags = "项目管理:fitnessCoach")
@RestController
@RequestMapping("/proj/fitnessCoach")
@RequiredArgsConstructor
public class ProjFitnessCoachController extends ResponseController {

    private final IProjFitnessCoachService service;

    @ApiOperation(value = "列表")
    @GetMapping( "/list")
    public ResponseResult<List<ProjFitnessCoachVO>> list(ProjFitnessCoachListReq listReq) {
        return succ(service.list(listReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessCoachAddReq req) {
        service.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessCoachUpdateReq req) {
        service.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessCoachVO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.deleteByIdList(idList);
        return succ();
    }

}
