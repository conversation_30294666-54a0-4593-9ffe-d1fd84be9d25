package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessMealPlan;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessMealPlanRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessMealPlanMapper;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDailyDishDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishListVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessMealPlanDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessMealPlanListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessMealPlanRelationService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessMealPlanService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/31 17:01
 */
@Slf4j
@Service
public class ProjFitnessMealPlanServiceImpl extends ServiceImpl<ProjFitnessMealPlanMapper, ProjFitnessMealPlan> implements IProjFitnessMealPlanService {

    @Resource
    IProjFitnessMealPlanRelationService planRelationService;

    @Resource
    IProjFitnessDishService dishService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessMealPlanListVO> selectMealPlanList(ProjFitnessMealPlanListReq listReq) {

        LambdaQueryWrapper<ProjFitnessMealPlan> queryWrapper = wrapQueryWrapper(listReq);
        List<ProjFitnessMealPlan> mealPlanList = list(queryWrapper);
        if (CollectionUtils.isEmpty(mealPlanList)) {
            return Collections.emptyList();
        }

        return mealPlanList.stream().map(mealPlan -> convert2ListVO(mealPlan)).collect(Collectors.toList());
    }

    private ProjFitnessMealPlanListVO convert2ListVO(ProjFitnessMealPlan mealPlan) {

        ProjFitnessMealPlanListVO listVO = new ProjFitnessMealPlanListVO();
        BeanUtils.copyProperties(mealPlan, listVO);
        return listVO;
    }

    private LambdaQueryWrapper<ProjFitnessMealPlan> wrapQueryWrapper(ProjFitnessMealPlanListReq pageReq) {

        LambdaQueryWrapper<ProjFitnessMealPlan> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjFitnessMealPlan::getName, pageReq.getName());
        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjFitnessMealPlan::getStatus, pageReq.getStatus());
        queryWrapper.orderByAsc(ProjFitnessMealPlan::getSorted);
        queryWrapper.orderByDesc(ProjFitnessMealPlan::getId);
        return queryWrapper;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveMealPlan(ProjFitnessMealPlanAddReq addReq) {

        checkMealPlan(addReq, null);
        ProjFitnessMealPlan mealPlan = new ProjFitnessMealPlan();
        BeanUtils.copyProperties(addReq, mealPlan);

        mealPlan.setStatus(GlobalConstant.STATUS_DRAFT);
        mealPlan.setProjId(RequestContextUtils.getProjectId());
        setDays(addReq, mealPlan);
        save(mealPlan);

        if (CollectionUtils.isEmpty(addReq.getDailyDishList())) {
            return;
        }
        List<ProjFitnessMealPlanRelation> relationList = addReq.getDailyDishList().stream().map(dailyDish -> wrapPlanRelation(mealPlan.getId(), mealPlan.getProjId(), dailyDish)).collect(Collectors.toList());
        planRelationService.saveBatch(relationList);

        projLmsI18nService.handleI18n(ListUtil.of(mealPlan), mealPlan.getProjId());
    }

    private void setDays(ProjFitnessMealPlanAddReq addReq, ProjFitnessMealPlan mealPlan) {

        if (CollectionUtils.isEmpty(addReq.getDailyDishList())) {
            mealPlan.setDays(GlobalConstant.ZERO);
        }

        Map<Integer, List<ProjFitnessDailyDishDetailVO>> dailyMap = addReq.getDailyDishList().stream().collect(Collectors.groupingBy(ProjFitnessDailyDishDetailVO::getDay));
        mealPlan.setDays(dailyMap.size());
    }

    private void checkMealPlan(ProjFitnessMealPlanAddReq mealPlanAddReq, Integer id) {

        LambdaQueryWrapper<ProjFitnessMealPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessMealPlan::getName, mealPlanAddReq.getName())
                .eq(ProjFitnessMealPlan::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjFitnessMealPlan::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Meal plan name exists.");
        }

        LambdaQueryWrapper<ProjFitnessMealPlan> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjFitnessMealPlan::getEventName, mealPlanAddReq.getEventName())
                .eq(ProjFitnessMealPlan::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjFitnessMealPlan::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Meal plan event name exists.");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateMealPlan(ProjFitnessMealPlanUpdateReq updateReq) {

        ProjFitnessMealPlan mealPlan = this.getById(updateReq.getId());
        if (Objects.isNull(mealPlan)) {
            throw new BizException("Can't find mealPlan.");
        }

        checkMealPlan(updateReq, updateReq.getId());
        BeanUtils.copyProperties(updateReq, mealPlan);
        setDays(updateReq, mealPlan);
        updateById(mealPlan);

        updateNewTime(updateReq);
        handlePlanRelation(updateReq);

        projLmsI18nService.handleI18n(ListUtil.of(mealPlan), mealPlan.getProjId());
    }

    private void handlePlanRelation(ProjFitnessMealPlanUpdateReq updateReq) {

        planRelationService.deleteByMealPlanId(updateReq.getId());
        if (CollectionUtils.isEmpty(updateReq.getDailyDishList())) {
            return;
        }

        List<ProjFitnessMealPlanRelation> relationList = updateReq.getDailyDishList().stream().map(dailyDish -> wrapPlanRelation(updateReq.getId(), RequestContextUtils.getProjectId(), dailyDish)).collect(Collectors.toList());
        planRelationService.saveBatch(relationList);
    }

    private ProjFitnessMealPlanRelation wrapPlanRelation(Integer mealPlanId, Integer projId, ProjFitnessDailyDishDetailVO dailyDish) {

        ProjFitnessMealPlanRelation relation = new ProjFitnessMealPlanRelation();
        relation.setProjFitnessMealPlanId(mealPlanId);
        relation.setProjId(projId);
        relation.setDay(dailyDish.getDay());
        relation.setProjFitnessDishId(dailyDish.getId());
        relation.setDishType(dailyDish.getDishType());
        return relation;
    }

    private void updateNewTime(ProjFitnessMealPlanUpdateReq updateReq) {

        LambdaUpdateWrapper<ProjFitnessMealPlan> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessMealPlan::getNewStartTime, updateReq.getNewStartTime());
        updateWrapper.set(ProjFitnessMealPlan::getNewEndTime, updateReq.getNewEndTime());
        updateWrapper.eq(ProjFitnessMealPlan::getId, updateReq.getId());
        this.update(updateWrapper);
    }

    @Override
    public ProjFitnessMealPlanDetailVO getDetailById(Integer mealPlanId) {

        ProjFitnessMealPlan mealPlan = getById(mealPlanId);
        if (Objects.isNull(mealPlan)) {
            return null;
        }

        ProjFitnessMealPlanDetailVO detailVO = new ProjFitnessMealPlanDetailVO();
        BeanUtils.copyProperties(mealPlan, detailVO);

        List<ProjFitnessDailyDishDetailVO> detailVOList = getDailyDishList(mealPlanId);
        detailVO.setDailyDishList(detailVOList);
        return detailVO;
    }

    public List<ProjFitnessDailyDishDetailVO> getDailyDishList(Integer mealPlanId) {

        List<ProjFitnessMealPlanRelation> mealPlanRelationList = planRelationService.listByMealPlanId(mealPlanId);
        if(CollectionUtils.isEmpty(mealPlanRelationList)) {
            return Collections.emptyList();
        }

        List<Integer> dishIds = mealPlanRelationList.stream().map(ProjFitnessMealPlanRelation::getProjFitnessDishId).collect(Collectors.toList());
        ProjFitnessDishListReq dishListReq = new ProjFitnessDishListReq();
        dishListReq.setDishIds(dishIds);

        List<ProjFitnessDishListVO> dishList = dishService.list(dishListReq, RequestContextUtils.getProjectId());
        if (CollectionUtils.isEmpty(dishList)) {
            return Collections.emptyList();
        }

        Map<Integer, ProjFitnessDishListVO> dishIdMap = dishList.stream().collect(Collectors.toMap(ProjFitnessDishListVO::getId, Function.identity(), (k1, k2) -> k1));
        return mealPlanRelationList.stream().map(relation -> wrapDailyDishDetail(relation, dishIdMap.get(relation.getProjFitnessDishId()))).collect(Collectors.toList());
    }

    private ProjFitnessDailyDishDetailVO wrapDailyDishDetail(ProjFitnessMealPlanRelation planRelation, ProjFitnessDishListVO dishList) {

        ProjFitnessDailyDishDetailVO detailVO = new ProjFitnessDailyDishDetailVO();
        BeanUtils.copyProperties(dishList, detailVO);
        detailVO.setDay(planRelation.getDay());
        detailVO.setDishType(planRelation.getDishType());
        return detailVO;
    }

    @Override
    public void sort(IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        List<ProjFitnessMealPlan> dishList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjFitnessMealPlan dishCollection = new ProjFitnessMealPlan();
            dishCollection.setSorted(i).setId(idList.get(i));
            dishList.add(dishCollection);
        }
        updateBatchById(dishList);
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjFitnessMealPlan> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessMealPlan::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessMealPlan::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjFitnessMealPlan::getId, idList);
        this.update(new ProjFitnessMealPlan(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjFitnessMealPlan> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessMealPlan::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFitnessMealPlan::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessMealPlan::getId, idList);
        this.update(new ProjFitnessMealPlan(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjFitnessMealPlan> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessMealPlan::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjFitnessMealPlan::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjFitnessMealPlan::getId, idList);
        this.update(new ProjFitnessMealPlan(), wrapper);
    }
}
