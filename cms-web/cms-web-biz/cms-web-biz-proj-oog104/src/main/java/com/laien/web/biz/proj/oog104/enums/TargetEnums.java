package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

/**
 * 部位枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum TargetEnums implements IEnumBase {
    FULL_BODY(1, "Full Body", "Full Body"),
    ARM(2, "Arm", "Arm"),
    BACK(3,"Back", "Back"),
    BUTT(4, "Butt", "Butt"),
    ABS(5, "Abs", "Abs"),
    LEGS(6, "Legs", "Legs"),
    CORE(7, "Core", "Core");

    private final Integer code;
    private final String name;
    private final String displayName;

    TargetEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }
    public static TargetEnums getBy(Integer code) {
        for (TargetEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
