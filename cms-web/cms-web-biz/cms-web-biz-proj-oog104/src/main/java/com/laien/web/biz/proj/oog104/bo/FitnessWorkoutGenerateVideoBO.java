package com.laien.web.biz.proj.oog104.bo;

import com.google.common.collect.Lists;
import com.laien.common.oog104.enums.manual.*;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessExerciseVideo;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/3/17 19:10
 */
@Data
public class FitnessWorkoutGenerateVideoBO {

    private List<ProjFitnessExerciseVideo> centralAndLeftVideoList;
    private Map<Integer, ProjFitnessExerciseVideo> videoIdMap;
    private Map<ManualTargetEnums, Set<ProjFitnessExerciseVideo>> videoTargetMap;

    private Map<ExerciseVideoSpecialLimitEnums, Set<ProjFitnessExerciseVideo>> videoLimitMap;
    private Map<String, Map<Integer, ProjFitnessExerciseVideo>> videoI18nMap;

    BiPredicate<ProjFitnessExerciseVideo, ManualTypeEnums> videoTypeMatch = (video, videoType) -> Objects.equals(video.getType(), videoType);
    BiPredicate<ProjFitnessExerciseVideo, List<ManualPositionEnums>> positionMatch = (video, position) -> position.contains(video.getPosition());
    //v8.3.0新增逻辑Pilates plan可挑选regular fitness & Pilates两种type的动作
    //Pilates workout先从Pilates中进行挑选，如果不够，则剩余动作用Regular Fitness数据补充
    BiPredicate<ProjFitnessExerciseVideo, ManualExerciseTypeEnums> exerciseTypeMatch = (video, exerciseType) -> {
        if (exerciseType == ManualExerciseTypeEnums.PILATES) {
            return video.getExerciseType() == ManualExerciseTypeEnums.PILATES
                    || video.getExerciseType() == ManualExerciseTypeEnums.REGULAR_FITNESS;
        } else {
            return Objects.equals(video.getExerciseType(), exerciseType);
        }
    };
    BiPredicate<ProjFitnessExerciseVideo, List<ManualDifficultyEnums>> difficultMatch = (video, difficulty) -> difficulty.contains(video.getDifficulty());
    BiPredicate<ProjFitnessExerciseVideo, ManualEquipmentEnums> equipmentMatch = (video, equipment) -> Objects.equals(video.getEquipment(), equipment);

    BiPredicate<ProjFitnessExerciseVideo, List<ManualIntensityEnums>> intensityMatch = (video, intensity) -> {
        if (CollectionUtils.isEmpty(intensity)) {
            return true;
        }
        return intensity.contains(video.getIntensity());
    };

    BiPredicate<ProjFitnessExerciseVideo, ManualTargetEnums> targetMatch = (video, target) -> {

        if (Objects.equals(target, ManualTargetEnums.FULL_BODY)) {
            return true;
        }
        if (videoTargetMap.containsKey(target) && videoTargetMap.get(target).contains(video)) {
            return true;
        }

//        if (videoTargetMap.containsKey(ManualTargetEnums.NONE) && videoTargetMap.get(ManualTargetEnums.NONE).contains(video)) {
//            return true;
//        }
        return false;
    };

    BiPredicate<ProjFitnessExerciseVideo, List<ExerciseVideoSpecialLimitEnums>> specialLimitMatch = (video, specialLimitEnums) -> {

        if (CollectionUtils.isEmpty(specialLimitEnums)) {
            return true;
        }

        Set<ProjFitnessExerciseVideo> videoSet = videoLimitMap.keySet().stream()
                .filter(limit -> specialLimitEnums.contains(limit))
                .map(videoLimitMap::get).flatMap(Collection::stream).collect(Collectors.toSet());

        return CollectionUtils.isEmpty(videoSet) || !videoSet.contains(video);
    };

    public LinkedList<ProjFitnessExerciseVideo> listByPriority(ManualTypeEnums videoType,
                                                               ManualExerciseTypeEnums exerciseType,
                                                               List<ManualPositionEnums> positions,
                                                               ManualEquipmentEnums equipmentEnums,
                                                               List<ExerciseVideoSpecialLimitEnums> specialLimitList,
                                                               List<ManualDifficultyEnums> difficulty,
                                                               List<ManualIntensityEnums> intensity,
                                                               ManualTargetEnums target) {

        List<ProjFitnessExerciseVideo> priority4One = centralAndLeftVideoList.stream()
                .filter(video -> videoTypeMatch.test(video, videoType))
                .filter(video -> exerciseTypeMatch.test(video, exerciseType))
                //.filter(video -> equipmentMatch.test(video, equipmentEnums))
                .collect(Collectors.toList());
        Collections.shuffle(priority4One);
        //当选择 106 fitness时,过滤包含器材的
        List<ProjFitnessExerciseVideo> priority4Two;
        if (!Objects.equals(ManualExerciseTypeEnums.FITNESS_106, exerciseType)) {
            priority4Two = priority4One.stream()
                    .filter(video -> specialLimitMatch.test(video, specialLimitList))
                    .collect(Collectors.toList());
        }
        else if(Objects.equals(videoType, ManualTypeEnums.MAIN)) {
            //需要包含含有Dumbbell
            priority4Two = priority4One.stream()
                    .filter(video -> equipmentMatch.test(video, equipmentEnums))
                    .collect(Collectors.toList());
        } else {
            //当不为 main 时,不考虑器械
            List<ManualEquipmentEnums> manualEquipmentEnumsList = Arrays.asList(
                    ManualEquipmentEnums.DUMBBELLS,
                    ManualEquipmentEnums.NONE
            );
            priority4Two = priority4One.stream()
                    .filter(video -> manualEquipmentEnumsList.stream().anyMatch(manualEquipmentEnums ->equipmentMatch.test(video, manualEquipmentEnums)))
                    .collect(Collectors.toList());
            Collections.shuffle(priority4Two);
        }
        Collections.shuffle(priority4Two);
        if (Objects.equals(videoType, ManualTypeEnums.WARM_UP) || Objects.equals(videoType, ManualTypeEnums.COOL_DOWN)) {
            //wall pilates,106fitness,pilates 优先级单独处理
            if (Objects.equals(exerciseType, ManualExerciseTypeEnums.FITNESS_106)
                    || Objects.equals(exerciseType, ManualExerciseTypeEnums.WALL_PILATES)
                    || Objects.equals(exerciseType, ManualExerciseTypeEnums.PILATES)) {
                //当Template Type为Wall Pilates和106 Fitness时，Arm和Back作为一个类型，都可以选择
                List<ProjFitnessExerciseVideo> priority4Three;
                if ((Objects.equals(ManualExerciseTypeEnums.FITNESS_106, exerciseType) || Objects.equals(ManualExerciseTypeEnums.WALL_PILATES, exerciseType))
                        && (Objects.equals(target, ManualTargetEnums.ARMS) || Objects.equals(target, ManualTargetEnums.BACK))) {
                    List<ManualTargetEnums> targetList = Arrays.asList(
                            ManualTargetEnums.ARMS,
                            ManualTargetEnums.BACK
                    );
                    priority4Three = priority4Two.stream()
                            .filter(video -> targetList.stream().anyMatch(targets -> targetMatch.test(video, targets)))
                            .collect(Collectors.toList());
                } else {
                    priority4Three = priority4Two.stream()
                            .filter(video -> targetMatch.test(video, target))
                            .collect(Collectors.toList());
                }
                Collections.shuffle(priority4Three);
                List<ProjFitnessExerciseVideo> priority4Six = priority4Three.stream()
                        .filter(video -> positionMatch.test(video, positions))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Six);
                //v3.8.0选择 pilates 时,优先从 pilates 取出
                List<ProjFitnessExerciseVideo> priority4Seven;
                if (Objects.equals(ManualExerciseTypeEnums.PILATES, exerciseType)) {
                    priority4Seven = priority4Six.stream()
                            .filter(video -> Objects.equals(video.getExerciseType(), exerciseType))
                            .collect(Collectors.toList());
                    Collections.shuffle(priority4Seven);
                } else {priority4Seven = Collections.emptyList();}
                return combineList(priority4One, priority4Two, priority4Three, Collections.emptyList(), Collections.emptyList(), priority4Six, priority4Seven);
            }else {
                List<ProjFitnessExerciseVideo> priority4Five;
                    priority4Five = priority4Two.stream()
                            .filter(video -> targetMatch.test(video, target))
                            .collect(Collectors.toList());
                Collections.shuffle(priority4Five);
                List<ProjFitnessExerciseVideo> priority4Six = priority4Five.stream()
                        .filter(video -> positionMatch.test(video, positions))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Six);
                //v3.8.0选择 pilates 时,优先从 pilates 取出
                List<ProjFitnessExerciseVideo> priority4Seven;
                if (Objects.equals(ManualExerciseTypeEnums.PILATES, exerciseType)) {
                    priority4Seven = priority4Six.stream()
                            .filter(video -> Objects.equals(video.getExerciseType(), exerciseType))
                            .collect(Collectors.toList());
                    Collections.shuffle(priority4Seven);
                } else {
                    priority4Seven = Collections.emptyList();
                }
                return combineList(priority4One, priority4Two, Collections.emptyList(), Collections.emptyList(), priority4Five, priority4Six, priority4Seven);
            }
        }
        if (Objects.equals(videoType, ManualTypeEnums.MAIN)) {
            if (Objects.equals(exerciseType, ManualExerciseTypeEnums.FITNESS_106)
                    || Objects.equals(exerciseType, ManualExerciseTypeEnums.WALL_PILATES)
                    || Objects.equals(exerciseType, ManualExerciseTypeEnums.PILATES)) {
                //当Template Type为Wall Pilates和106 Fitness时，Arm和Back作为一个类型，都可以选择
                List<ProjFitnessExerciseVideo> priority4Three;
                if ((Objects.equals(ManualExerciseTypeEnums.FITNESS_106, exerciseType) || Objects.equals(ManualExerciseTypeEnums.WALL_PILATES, exerciseType))
                        && (Objects.equals(target, ManualTargetEnums.ARMS) || Objects.equals(target, ManualTargetEnums.BACK))) {
                    List<ManualTargetEnums> targetList = Arrays.asList(
                            ManualTargetEnums.ARMS,
                            ManualTargetEnums.BACK
                    );
                    priority4Three = priority4Two.stream()
                            .filter(video -> targetList.stream().anyMatch(targets -> targetMatch.test(video, targets)))
                            .collect(Collectors.toList());
                } else {
                    priority4Three = priority4Two.stream()
                            .filter(video -> targetMatch.test(video, target))
                            .collect(Collectors.toList());
                }
                Collections.shuffle(priority4Three);
                List<ProjFitnessExerciseVideo> priority4Four = priority4Three.stream()
                        .filter(video -> difficultMatch.test(video, difficulty))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Four);

                List<ProjFitnessExerciseVideo> priority4Five = priority4Four.stream()
                        .filter(video -> intensityMatch.test(video, intensity))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Five);

                List<ProjFitnessExerciseVideo> priority4Six = priority4Five.stream()
                        .filter(video -> positionMatch.test(video, positions))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Six);
                //v3.8.0选择 pilates 时,优先从 pilates 取出
                List<ProjFitnessExerciseVideo> priority4Seven;
                if (Objects.equals(ManualExerciseTypeEnums.PILATES, exerciseType)) {
                    priority4Seven = priority4Six.stream()
                            .filter(video -> Objects.equals(video.getExerciseType(), exerciseType))
                            .collect(Collectors.toList());
                    Collections.shuffle(priority4Seven);
                } else {priority4Seven = Collections.emptyList();}
                return combineList(priority4One, priority4Two, priority4Three, priority4Four, priority4Five, priority4Six, priority4Seven);
            }else {

                List<ProjFitnessExerciseVideo> priority4Three = priority4Two.stream()
                        .filter(video -> difficultMatch.test(video, difficulty))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Three);

                List<ProjFitnessExerciseVideo> priority4Four = priority4Three.stream()
                        .filter(video -> intensityMatch.test(video, intensity))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Four);
                List<ProjFitnessExerciseVideo> priority4Five = priority4Four.stream()
                        .filter(video -> targetMatch.test(video, target))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Five);
                List<ProjFitnessExerciseVideo> priority4Six = priority4Five.stream()
                        .filter(video -> positionMatch.test(video, positions))
                        .collect(Collectors.toList());
                Collections.shuffle(priority4Six);
                //v3.8.0选择 pilates 时,优先从 pilates 取出
                List<ProjFitnessExerciseVideo> priority4Seven;
                if (Objects.equals(ManualExerciseTypeEnums.PILATES, exerciseType)) {
                    priority4Seven = priority4Six.stream()
                            .filter(video -> Objects.equals(video.getExerciseType(), exerciseType))
                            .collect(Collectors.toList());
                    Collections.shuffle(priority4Seven);
                } else {
                    priority4Seven = Collections.emptyList();
                }
                return combineList(priority4One, priority4Two, priority4Three, priority4Four, priority4Five, priority4Six, priority4Seven);
            }
        }

        return Lists.newLinkedList();
    }

    private LinkedList<ProjFitnessExerciseVideo> combineList(List<ProjFitnessExerciseVideo> priorityOne,
                                                             List<ProjFitnessExerciseVideo> priorityTwo,
                                                             List<ProjFitnessExerciseVideo> priorityThree,
                                                             List<ProjFitnessExerciseVideo> priorityFour,
                                                             List<ProjFitnessExerciseVideo> priorityFive,
                                                             List<ProjFitnessExerciseVideo> prioritySix,
                                                             List<ProjFitnessExerciseVideo> prioritySeven) {

        LinkedList<ProjFitnessExerciseVideo> exerciseVideoList = Lists.newLinkedList(prioritySeven);
        Consumer<List<ProjFitnessExerciseVideo>> combineConsume = videoList -> {
            videoList.stream().filter(video -> !exerciseVideoList.contains(video)).forEach(exerciseVideoList::addLast);
        };
        combineConsume.accept(prioritySix);
        combineConsume.accept(priorityFive);
        combineConsume.accept(priorityFour);
        combineConsume.accept(priorityThree);
        combineConsume.accept(priorityTwo);
        combineConsume.accept(priorityOne);
        //对 name 去重（保留首次出现的 name，移除后续重复的 name）
        Set<String> seenNames = new HashSet<>();
        Iterator<ProjFitnessExerciseVideo> iterator = exerciseVideoList.iterator();
        while (iterator.hasNext()) {
            ProjFitnessExerciseVideo video = iterator.next();
            if (!seenNames.add(video.getName())) {
                iterator.remove(); // 移除重复 name 的 video
            }
        }
        return exerciseVideoList;
    }

}
