package com.laien.web.biz.proj.oog104.request;

import com.laien.common.oog104.enums.FitnessGenderEnums;
import com.laien.common.oog104.enums.FitnessSoundSubTypeEnums;
import com.laien.common.oog104.enums.FitnessSoundTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "Sound 新增", description = "Sound 新增")
public class ProjFitnessSoundAddReq {

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private FitnessSoundTypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private FitnessSoundSubTypeEnums soundSubType;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "声音")
    private String url;

    @ApiModelProperty(value = "duration")
    private Integer duration;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private FitnessGenderEnums gender;

    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;
}
