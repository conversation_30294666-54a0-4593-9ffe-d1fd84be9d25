package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessCoachAddReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCoachAddReq", description="ProjFitnessCoachAddReq")
public class ProjFitnessCoachAddReq {

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "photoImgUrl")
    private String photoImgUrl;

    @ApiModelProperty(value = "introduction")
    private String introduction;

}
