package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessManualWorkout;
import com.laien.web.biz.proj.oog104.entity.WorkoutVideoCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * proj_fitness_manual_workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface ProjFitnessManualWorkoutMapper extends BaseMapper<ProjFitnessManualWorkout> {
    /**
     * 统计workout关联的视频数量
     *
     * @param workoutIds workout ID列表
     * @return 统计结果
     */
    @Select("<script>" +
            "SELECT " +
            "  w.id as workoutId, " +
            "  COUNT(DISTINCT v.id) as videoCount, " +
            "  v.status as videoStatus " +
            "FROM proj_fitness_manual_workout w " +
            "LEFT JOIN proj_fitness_manual_workout_exercise_video wv ON w.id = wv.proj_fitness_manual_workout_id " +
            "LEFT JOIN proj_fitness_exercise_video v ON wv.proj_fitness_exercise_video_id = v.id " +
            "WHERE w.id IN " +
            "  <foreach item='id' index='index' collection='workoutIds' open='(' separator=',' close=')'>#{id}</foreach> " +
            "GROUP BY w.id, v.status" +
            "</script>")
    List<WorkoutVideoCount> countVideosByWorkoutIds(@Param("workoutIds") List<Integer> workoutIds);
}
