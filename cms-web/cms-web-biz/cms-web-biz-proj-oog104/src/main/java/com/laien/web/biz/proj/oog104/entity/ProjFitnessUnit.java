package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Fitness Dish step
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_fitness_unit")
@ApiModel(value="ProjFitnessUnit对象", description="Fitness单位表")
public class ProjFitnessUnit extends BaseModel {

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_UNIT;

    @ApiModelProperty("单位名称")
    private String name;

    @ApiModelProperty("项目id")
    private Integer projId;

    @ApiModelProperty("启用状态 0草稿 1启用 2停用")
    private Integer status;
}
