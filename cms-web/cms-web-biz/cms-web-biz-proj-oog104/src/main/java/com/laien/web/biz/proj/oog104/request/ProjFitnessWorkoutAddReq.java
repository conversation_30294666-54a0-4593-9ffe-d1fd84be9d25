package com.laien.web.biz.proj.oog104.request;

import com.laien.web.biz.proj.oog104.enums.ExtraTagEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "workout 新增", description = "workout 新增")
public class ProjFitnessWorkoutAddReq {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "封面视频地址")
    private String coverVideoUrl;

    @ApiModelProperty(value = "封面视频时长")
    private Integer coverVideoDuration;

    @ApiModelProperty(value = "扩展标签")
    private List<ExtraTagEnums> extraTag;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "简介")
    private String description;

    @NotEmpty(message = "videoList cannot be empty")
    @ApiModelProperty(value = "video list")
    private List<ProjFitnessWorkoutAddVideoReq> videoList;

}
