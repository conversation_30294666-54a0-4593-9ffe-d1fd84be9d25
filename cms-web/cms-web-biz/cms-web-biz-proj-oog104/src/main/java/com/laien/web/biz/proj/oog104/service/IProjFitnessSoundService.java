package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessSound;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;


public interface IProjFitnessSoundService extends IService<ProjFitnessSound> {
    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    PageRes<ProjFitnessSoundPageVO> selectSoundPage(ProjFitnessSoundPageReq req);

    /**
     * 添加 sound
     *
     * @param req
     */
    void saveSound(ProjFitnessSoundAddReq req);

    /**
     * 修改 sound
     *
     * @param req
     */
    void updateSound(ProjFitnessSoundUpdateReq req);

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    ProjFitnessSoundDetailVO getDetail(Integer id);

    /**
     * 批量启用
     * @param idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     * @param idList
     */
    void updateDisableByIds(List<Integer> idList);
}