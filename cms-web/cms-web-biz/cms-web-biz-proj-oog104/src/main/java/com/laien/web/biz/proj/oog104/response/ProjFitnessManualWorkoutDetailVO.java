package com.laien.web.biz.proj.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualEquipmentEnums;
import com.laien.common.oog104.enums.manual.ManualIntensityEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.manual.ManualWorkoutTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Manual Workout Detail Response
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@ApiModel(value = "Manual Workout Detail Response", description = "Detailed information of a manual workout")
public class ProjFitnessManualWorkoutDetailVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "Workout ID")
    private Integer id;

    /**
     * Name
     */
    @ApiModelProperty(value = "Workout Name")
    private String name;

    /**
     * Event Name
     */
    @ApiModelProperty(value = "Event Name")
    private String eventName;

    /**
     * Cover Image
     */
    @ApiModelProperty(value = "Cover Image URL")
    private String coverImage;

    /**
     * Detail Image
     */
    @ApiModelProperty(value = "Detail Image URL")
    private String detailImage;

    /**
     * Categories
     */
    @ApiModelProperty(value = "Categories")
    private List<ManualCategoryEnums> category;

    /**
     * Workout Type
     */
    @ApiModelProperty(value = "Workout Type")
    private ManualWorkoutTypeEnums workoutType;

    /**
     * Age Groups
     */
    @ApiModelProperty(value = "Age Groups")
    private List<ManualAgeGroupEnums> ageGroup;

    /**
     * Target Areas
     */
    @ApiModelProperty(value = "Target Areas")
    private List<ManualTargetEnums> target;

    /**
     * Difficulty
     */
    @ApiModelProperty(value = "Difficulty Level")
    private ManualDifficultyEnums difficulty;

    /**
     * Equipment
     */
    @ApiModelProperty(value = "Equipment")
    private List<ManualEquipmentEnums> equipment;

    /**
     * Special Limits
     */
    @ApiModelProperty(value = "Special Limits")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    /**
     * Intensity
     */
    @ApiModelProperty(value = "Intensity")
    private ManualIntensityEnums intensity;

    /**
     * Description
     */
    @ApiModelProperty(value = "Description")
    private String description;

    /**
     * Duration
     */
    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    /**
     * Calories Burned
     */
    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    /**
     * Time Period Start
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    /**
     * Time Period End
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    /**
     * Subscription 0-No, 1-Yes
     */
    @ApiModelProperty(value = "Subscription (0-No, 1-Yes)")
    private Integer subscription;

    /**
     * Status
     */
    @ApiModelProperty(value = "Status (0-Draft, 1-Enabled, 2-Disabled)")
    private Integer status;

    /**
     * File Status
     */
    @ApiModelProperty(value = "File Status (0-Processing, 1-Success, 2-Failed)")
    private Integer fileStatus;

    /**
     * Failure Message
     */
    @ApiModelProperty(value = "Failure Message")
    private String failMessage;

    /**
     * Audio Languages
     */
    @ApiModelProperty(value = "Audio Languages, comma separated")
    private String audioLanguages;

    /**
     * Video List
     */
    @ApiModelProperty(value = "List of Exercise Videos")
    private List<ProjFitnessManualWorkoutDetailVideoVO> videoList;
}
