package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerate;
import com.laien.web.biz.proj.oog104.response.*;
import org.mapstruct.Mapper;

/**
 * <p>
 *  Workout generate MapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessWorkoutGenerateMapStruct {

    ProjFitnessWorkoutGeneratePageVO toPageVO(ProjFitnessWorkoutGenerate workout);

    ProjFitnessWorkoutGenerateDetailVO toDetailVO(ProjFitnessWorkoutGenerate workout);
}
