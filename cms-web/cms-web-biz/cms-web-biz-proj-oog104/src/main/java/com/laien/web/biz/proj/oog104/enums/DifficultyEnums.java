package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

/**
 * 难度枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum DifficultyEnums implements IEnumBase {
    BEGINNER(1, "Beginner", "Beginner"),
    INTERMEDIATE(2, "Intermediate", "Intermediate"),
    ADVANCED(3, "Advanced", "Advanced");

    private final Integer code;
    private final String name;
    private final String displayName;

    DifficultyEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static DifficultyEnums getBy(Integer code) {
        for (DifficultyEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
