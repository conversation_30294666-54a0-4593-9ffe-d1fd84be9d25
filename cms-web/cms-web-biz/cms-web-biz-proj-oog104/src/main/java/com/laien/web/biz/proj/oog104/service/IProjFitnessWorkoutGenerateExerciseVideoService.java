package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.dto.res.ProjFitnessWorkoutGenerateExerciseVideoVO;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerateExerciseVideo;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_fitness_workout_generate_exercise_video 服务接口
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface IProjFitnessWorkoutGenerateExerciseVideoService extends IService<ProjFitnessWorkoutGenerateExerciseVideo> {

    /**
     * 根据WorkoutGenerateId查询关联的ExerciseVideo列表
     *
     * @param workoutGenerateId workoutGenerateId
     * @return 关联的ExerciseVideo列表
     */
    List<ProjFitnessWorkoutGenerateExerciseVideo> listByWorkoutGenerateId(Integer workoutGenerateId);

    /**
     * 批量保存WorkoutGenerateExerciseVideo
     *
     * @param exerciseVideoList exerciseVideoList
     */
    void batchSave(List<ProjFitnessWorkoutGenerateExerciseVideo> exerciseVideoList);

    /**
     * 根据workoutIds删除关联的ExerciseVideo
     * @param workoutIds workoutIds
     */
    void deleteByWorkoutIds(Set<Integer> workoutIds);

    /**
     * 根据模板ID和任务ID查询关联的ExerciseVideo列表
     *
     * @param templateId 模板ID
     * @param taskId 任务ID
     * @return 关联的ExerciseVideo列表
     */
    List<ProjFitnessWorkoutGenerateExerciseVideo> listByTemplateIdAndTaskId(Integer templateId, Integer taskId);

    /**
     * 批量查询运动视频关联信息
     */
    List<ProjFitnessExerciseVideoPageVO> listByWorkoutGenerateIds(List<Integer> workoutGenerateIds);
}
