package com.laien.web.biz.proj.oog104.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.common.core.converter.GenericEnumNameConverter;
import com.laien.common.oog104.enums.manual.*;
import com.laien.web.biz.core.converter.BigDecimalStringTrimConverter;
import com.laien.web.biz.core.converter.GenericEnumListNameConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ExerciseVideo Excel 导出 VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExerciseVideo 详情", description = "ExerciseVideo 详情")
public class ProjFitnessExerciseVideoExportVO {

    @ExcelProperty("Id")
    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ExcelProperty("Name")
    @ApiModelProperty(value = "动作名称")
    private String name;

    @ExcelProperty("Event Name")
    @ApiModelProperty(value = "event name")
    private String eventName;

    @ExcelIgnore
    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ExcelProperty("English Voice Name")
    @ApiModelProperty(value = "coreVoiceConfigI18nName")
    private String coreVoiceConfigI18nName;

    @ExcelProperty("Image URL")
    @ApiModelProperty(value = "图片地址，支持webp,png")
    private String imageUrl;

    @ExcelProperty(value = "Type",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "类型code")
    private ManualTypeEnums type;

    @ExcelProperty(value = "Exercise Type",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "exerciseType")
    private ManualExerciseTypeEnums exerciseType;

    @ExcelProperty(value = "Difficulty",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "难度")
    private ManualDifficultyEnums difficulty;

    @ExcelProperty(value = "Target",converter = GenericEnumListNameConverter.class)
    @ApiModelProperty(value = "目标")
    private List<ManualTargetEnums> target;

    @ExcelProperty(value = "Special Limit",converter = GenericEnumListNameConverter.class)
    @ApiModelProperty(value = "特殊限制列表")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ExcelProperty(value = "Intensity",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "intensity")
    private ManualIntensityEnums intensity;

    @ExcelProperty(value = "Equipment",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "器械")
    private ManualEquipmentEnums equipment;

    @ExcelProperty(value = "Position",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "部位")
    private ManualPositionEnums position;

    @ExcelProperty(value = "Video Direction",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "direction")
    private ManualVideoDirectionEnums videoDirection;

    @ExcelProperty("Guidance")
    @ApiModelProperty(value = "指导文本 (500字符限制)")
    private String guidance;

    @ExcelProperty("How To Do")
    @ApiModelProperty(value = "如何做 (500字符限制)")
    private String howToDo;

    @ExcelProperty("Front Video URL")
    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ExcelProperty("Front Video Duration")
    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ExcelProperty("Side Video URL")
    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ExcelProperty("Side Video Duration")
    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ExcelProperty("Name Audio URL")
    @ApiModelProperty(value = "名称音频 (mp3格式)")
    private String nameAudioUrl;

    @ExcelProperty("Name Audio Duration")
    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ExcelProperty("Guidance Audio URL")
    @ApiModelProperty(value = "指导音频 (mp3格式)")
    private String guidanceAudioUrl;

    @ExcelProperty("Guidance Audio Duration")
    @ApiModelProperty(value = "指导音频时长")
    private Integer guidanceAudioDuration;

    @ExcelProperty("How To Do Audio URL")
    @ApiModelProperty(value = "如何做音频 (mp3格式)")
    private String howToDoAudioUrl;

    @ExcelProperty("How To Do Audio Duration")
    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ExcelProperty("MET")
    @ApiModelProperty(value = "MET 值")
    private Integer met;

    @ExcelProperty(value = "Calorie", converter = BigDecimalStringTrimConverter.class)
    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @ExcelProperty("Status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /*
    @ExcelProperty("Project Id")
    @ApiModelProperty(value = "项目id")
    private Integer projId;
    */

    @ExcelProperty("Is Used For Auto")
    @ApiModelProperty(value = "是否参与自动生成 1-是 0-否")
    private Integer usedForAuto;

    @ExcelProperty("Left-Right Video Id")
    @ApiModelProperty(value = "Left-Right 关联 Right 类型的动作 ID")
    private Integer leftRightVideoId;
}
