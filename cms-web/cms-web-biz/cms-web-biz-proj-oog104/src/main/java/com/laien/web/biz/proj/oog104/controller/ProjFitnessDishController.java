package com.laien.web.biz.proj.oog104.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDish;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Fitness Dish 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */

@Api(tags = "项目管理:fitnessDish")
@RestController
@RequestMapping("/proj/fitnessDish")
@RequiredArgsConstructor
public class ProjFitnessDishController extends ResponseController {

    private final IProjFitnessDishService service;

    @ApiOperation(value = "列表")
    @GetMapping( "/list")
    public ResponseResult<List<ProjFitnessDishListVO>> list(ProjFitnessDishListReq listReq) {
        return succ(service.list(listReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessDishAddReq req) {
        service.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessDishUpdateReq req) {
        service.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessDishDetailVO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        List<Integer> failedIdList = service.updateEnableByIds(idList);
        if (CollUtil.isNotEmpty(failedIdList)) {
            String ids = StrUtil.join(GlobalConstant.COMMA, failedIdList);
            return fail("The following data(id: " + ids +") has an incorrect value or status");
        }
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.deleteByIdList(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        service.sort(idListReq);
        return succ();
    }

}
