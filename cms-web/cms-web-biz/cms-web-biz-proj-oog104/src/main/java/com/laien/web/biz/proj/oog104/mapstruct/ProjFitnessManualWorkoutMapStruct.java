package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessManualWorkout;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutAddReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutDetailVideoVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutPageVO;
import org.mapstruct.Mapper;

/**
 * <p>
 *  Workout generate MapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessManualWorkoutMapStruct {

    ProjFitnessManualWorkout addReqToEntity(ProjFitnessManualWorkoutAddReq workoutReq);

    ProjFitnessManualWorkoutDetailVO entityToDetailVO(ProjFitnessManualWorkout workoutFind);

    ProjFitnessManualWorkoutDetailVideoVO entityToDetailVideoVO(ProjFitnessExerciseVideo video);

    ProjFitnessManualWorkoutPageVO entityToPageVO(ProjFitnessManualWorkout workout);
}
