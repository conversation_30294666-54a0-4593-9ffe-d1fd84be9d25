/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog104.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateIntensityEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>ProjFitnessWorkoutImagePage </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "模板 分页", description = "模板 分页")
public class ProjFitnessWorkoutImageVO {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("图片名称")
    private String name;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

    //v8.3.0新增templateType字段
    @ApiModelProperty("模版类型字段")
    private TemplateTypeEnums templateType;

    @ApiModelProperty("年龄段")
    @TableField(typeHandler = ManualAgeGroupEnums.TypeHandler.class)
    private List<ManualAgeGroupEnums> ageGroup;

    @ApiModelProperty("锻炼部位")
    private ManualTargetEnums target;

    @ApiModelProperty("难度")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty("排序")
    private TemplateIntensityEnums intensity;

    @ApiModelProperty("特殊限制")
    @TableField(typeHandler = ExerciseVideoSpecialLimitEnums.TypeHandler.class)
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty("封面图片地址")
    private String coverImage;

    @ApiModelProperty("详情图片地址")
    private String detailImage;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Integer projId;

    @ApiModelProperty("排序字段")
    private Integer sortNo;


}