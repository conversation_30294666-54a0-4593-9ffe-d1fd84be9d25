package com.laien.web.biz.proj.oog104.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>生成模板参数</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 17:42
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "生成 template 参数", description = "生成 template 参数")
public class ProjFitnessTemplateGenerateReq {

    @JsonIgnore
    private Integer projId;

    private TemplateTypeEnums templateType;

    private List<WorkoutDurationRangeEnums> durationRangeList;

    private List<ManualDifficultyEnums> levelList;

    private List<ExerciseVideoSpecialLimitEnums> specialLimitList;

    private ExclusiveTypeEnums exclusiveType;

}
