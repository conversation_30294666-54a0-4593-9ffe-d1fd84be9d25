package com.laien.web.biz.proj.oog104.controller;

import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessSound;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessSoundService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Api(tags = "项目管理:Fitness sound")
@RestController
@RequestMapping("/proj/fitnessSound")
public class ProjFitnessSoundController extends ResponseController {

    @Resource
    private IProjFitnessSoundService soundService;

    @ApiOperation(value = "Fitness Sound 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessSoundPageVO>> page(ProjFitnessSoundPageReq req) {
        req.setProjId(RequestContextUtils.getProjectId()) ;
        AssertUtil.notNull(RequestContextUtils.getProjectId(),"projId is null");
        PageRes<ProjFitnessSoundPageVO> pageRes = soundService.selectSoundPage(req);
        return succ(pageRes);
    }

    @ApiOperation(value = "Fitness Sound 增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessSoundAddReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.saveSound(req);
        return succ();
    }

    @ApiOperation(value = "Fitness Sound 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessSoundUpdateReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.updateSound(req);
        return succ();
    }

    @ApiOperation(value = "Fitness Sound 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessSoundDetailVO> detail(@PathVariable Integer id) {
        ProjFitnessSoundDetailVO detailVO = soundService.getDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "Fitness Sound 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Fitness Sound 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateDisableByIds(idList);
        return succ();
    }
}
