package com.laien.web.biz.proj.oog104.request;


import com.laien.common.oog104.enums.dish.FitnessDishStyleEnums;
import com.laien.common.oog104.enums.dish.FitnessDishTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * <p>
 * ProjFitnessDishListReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@ApiModel(value = "ProjFitnessDish列表筛选条件", description = "ProjFitnessDish列表筛选条件")
public class ProjFitnessDishListReq {

    @ApiModelProperty(value = "dish Id 集合")
    private Collection<Integer> dishIds;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "类型")
    private FitnessDishTypeEnums type;

    @ApiModelProperty(value = "类型")
    private FitnessDishStyleEnums style;

}
