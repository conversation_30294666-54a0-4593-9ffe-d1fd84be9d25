package com.laien.web.biz.proj.oog104.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * plan 类型枚举
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
public enum PlanTypeEnums implements IEnumBase {
    FULL_BODY(1, "Full body", "Full body"),
//    LOSE_WEIGHT(2, "Lose weight", "Lose weight"),
    WALL_PILATES(3, "Wall pilates", "Wall pilates"),
    KNEE_FRIENDLY(4, "Knee friendly", "Knee friendly"),
    ABS(5, "Abs", "Abs"),
    BUTT(6, "Butt", "Butt");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    PlanTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }
    public static PlanTypeEnums getBy(Integer code) {
        for (PlanTypeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
