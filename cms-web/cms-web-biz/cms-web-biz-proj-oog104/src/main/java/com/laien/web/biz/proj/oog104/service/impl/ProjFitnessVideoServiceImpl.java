package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.web.biz.core.util.TaskResourceSectionUtil;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideo;
import com.laien.web.biz.proj.oog104.enums.*;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessVideoMapper;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessVideoService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.web.biz.proj.oog104.enums.TaskResourceSectionQueryEnums.PROJ_FITNESS_VIDEO_FRONT;
import static com.laien.web.biz.proj.oog104.enums.TaskResourceSectionQueryEnums.PROJ_FITNESS_VIDEO_SIDE;
import static com.laien.web.frame.constant.GlobalConstant.STATUS_DRAFT;
import static com.laien.web.frame.constant.GlobalConstant.YES;

/**
 * <p>
 * proj_fitness_video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class ProjFitnessVideoServiceImpl extends ServiceImpl<ProjFitnessVideoMapper, ProjFitnessVideo> implements IProjFitnessVideoService {

    @Resource
    private ITaskResourceSectionService taskResourceSectionService;
    @Resource
    private IProjLmsI18nService projLmsI18nService;
    @Resource
    private ICoreVoiceConfigI18nService i18nConfigService;

    private static final String TYPE_CODES ="type_codes";
    private static final String FIT_TYPE_CODES ="fit_type_codes";
    private static final String TARGET_CODES ="target_codes";
    private static final String EQUIPMENT_CODES ="equipment_codes";
    private static final String SPECIAL_LIMIT_CODES ="special_limit_codes";

    @Override
    public PageRes<ProjFitnessVideoPageVO> selectVideoPage(ProjFitnessVideoPageReq pageReq) {
        Integer id = pageReq.getId();
        String name = pageReq.getName();
        List<TypeEnums> type = pageReq.getType();
        DifficultyEnums difficulty = pageReq.getDifficulty();
        PositionEnums position = pageReq.getPosition();
        List<FitTypeEnum> fitType = pageReq.getFitType();
        List<TargetEnums> target = pageReq.getTarget();
        List<EquipmentEnums> equipment = pageReq.getEquipment();
        List<SpecialLimitEnums> specialLimits = pageReq.getSpecialLimits();

        Integer status = pageReq.getStatus();
        LambdaQueryWrapper<ProjFitnessVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(id), ProjFitnessVideo::getId, id)
                .eq(ProjFitnessVideo::getProjId, RequestContextUtils.getProjectId())
                .like(StringUtils.isNotBlank(name), ProjFitnessVideo::getName, name)
                .eq(Objects.nonNull(status), ProjFitnessVideo::getStatus, status);
        if (Objects.nonNull(difficulty)) {
            wrapper.eq(ProjFitnessVideo::getDifficultyCode, difficulty.getCode());
        }
        if (Objects.nonNull(position)) {
            wrapper.eq(ProjFitnessVideo::getPositionCode, position.getCode());
        }

        this.multipleSearchContainsOne(wrapper, type, TYPE_CODES);
        this.multipleSearchContainsOne(wrapper, fitType, FIT_TYPE_CODES);
        this.multipleSearchContainsOne(wrapper, target, TARGET_CODES);
        this.multipleSearchContainsOne(wrapper, equipment, EQUIPMENT_CODES);
        // 包含所有选项才能匹配
        if(MatchTypeEnum.CONTAINS_ALL.equals(pageReq.getSpecialLimitsMatchType())) {
            this.multipleSearchContainsAll(wrapper, specialLimits, SPECIAL_LIMIT_CODES);
        }
        if(MatchTypeEnum.CONTAINS_ONE.equals(pageReq.getSpecialLimitsMatchType())) {
            this.multipleSearchContainsOne(wrapper, specialLimits, SPECIAL_LIMIT_CODES);
        }

        wrapper.orderByDesc(ProjFitnessVideo::getId);
        IPage<ProjFitnessVideo> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), wrapper);
        List<ProjFitnessVideo> records = page.getRecords();

        List<ProjFitnessVideoPageVO> list = records.stream().map(video -> {
            ProjFitnessVideoPageVO pageVO = new ProjFitnessVideoPageVO();
            BeanUtils.copyProperties(video, pageVO);
            IEnumBase.setEnumList(pageVO::setType, video.getTypeCodes(), TypeEnums::getBy);
            IEnumBase.setEnum(pageVO::setDifficulty, video.getDifficultyCode(), DifficultyEnums::getBy);
            IEnumBase.setEnum(pageVO::setPosition, video.getPositionCode(), PositionEnums::getBy);
            IEnumBase.setEnumList(pageVO::setFitType, video.getFitTypeCodes(), FitTypeEnum::getBy);
            IEnumBase.setEnumList(pageVO::setTarget, video.getTargetCodes(), TargetEnums::getBy);
            IEnumBase.setEnumList(pageVO::setEquipment, video.getEquipmentCodes(), EquipmentEnums::getBy);
            IEnumBase.setEnumList(pageVO::setSpecialLimits, video.getSpecialLimitCodes(), SpecialLimitEnums::getBy);

            return pageVO;
        }).collect(Collectors.toList());
        // 设置切片相关任务状态
        injectionTaskStatus(list);
        // 填充翻译配置信息
        fillI18nConfigInfo(records, list);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    private void injectionTaskStatus(List<ProjFitnessVideoPageVO> videoList) {
        if (CollUtil.isEmpty(videoList)) {
            return;
        }
        Set<Integer> idSet = videoList.stream().map(ProjFitnessVideoPageVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> sideStatusList = taskResourceSectionService.find(PROJ_FITNESS_VIDEO_SIDE.getTableName(), PROJ_FITNESS_VIDEO_SIDE.getEntityFieldName(), idSet);
        List<TaskResourceSection> frontStatusList = taskResourceSectionService.find(PROJ_FITNESS_VIDEO_FRONT.getTableName(), PROJ_FITNESS_VIDEO_FRONT.getEntityFieldName(), idSet);
        if (CollUtil.isEmpty(sideStatusList) && CollUtil.isEmpty(frontStatusList)) {
            return;
        }
        sideStatusList = null == sideStatusList ? new ArrayList<>() : sideStatusList;
        frontStatusList = null == frontStatusList ? new ArrayList<>() : frontStatusList;

        Map<Integer, List<TaskResourceSection>> sideMap = sideStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        Map<Integer, List<TaskResourceSection>> frontMap = frontStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        for (ProjFitnessVideoPageVO video : videoList) {
            List<TaskResourceSection> sideList = sideMap.get(video.getId());
            if (CollUtil.isNotEmpty(sideList)) {
                TaskResourceSection sideTask = sideList.get(0);
                video.setSideTaskStatus(sideTask.getStatus());
            }

            List<TaskResourceSection> frontList = frontMap.get(video.getId());
            if (CollUtil.isNotEmpty(frontList)) {
                TaskResourceSection frontTask = frontList.get(0);
                video.setFrontTaskStatus(frontTask.getStatus());
            }
        }

    }

    /**
     * 多选查询处理 有交集为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsOne(LambdaQueryWrapper<ProjFitnessVideo> wrapper, List<? extends IEnumBase> options, String tableField) {
        List<Integer> multipleCodeList = new ArrayList<>();
        IEnumBase.setCodeInteger(multipleCodeList::addAll, options);
        if (!multipleCodeList.isEmpty()) {
            wrapper.and(o -> {
                int len = multipleCodeList.size();
                for (int i = 0; i < len; i++) {
                    o.apply("FIND_IN_SET({0}, " + tableField +")", multipleCodeList.get(i));
                    o.or(i < len - 1);
                }
            });
        }
    }

    /**
     * 多选查询处理 包含为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsAll(LambdaQueryWrapper<ProjFitnessVideo> wrapper, List<? extends IEnumBase> options, String tableField) {

        // 所有 find in set 为真
        if(CollUtil.isEmpty(options)){
            return;
        }
        List<Integer> multipleCodeList = new ArrayList<>();
        IEnumBase.setCodeInteger(multipleCodeList::addAll, options);
        if(CollUtil.isEmpty(multipleCodeList)){
            return;
        }
        wrapper.and(o -> o.apply(multipleCodeList.stream().map(code -> String.format("FIND_IN_SET(%s,%s)", code, tableField)).collect(Collectors.joining(" and "))));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVideo(ProjFitnessVideoAddReq videoReq) {
        // 校验
        Integer projId = RequestContextUtils.getProjectId();
        this.check(videoReq, null, projId);

        ProjFitnessVideo video = this.covertToFitnessVideo(videoReq);
        video.setStatus(GlobalConstant.STATUS_DRAFT);
        video.setProjId(projId);
        this.check(videoReq, null, projId);
        this.save(video);
        projLmsI18nService.handleI18n(ListUtil.of( video), projId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateVideo(ProjFitnessVideoUpdateReq videoReq) {
        Integer id = videoReq.getId();
        ProjFitnessVideo videoFind = this.getById(id);
        if (Objects.isNull(videoFind)) {
            throw new BizException("Data not found");
        }
        // 校验
        Integer projId = videoFind.getProjId();
        this.check(videoReq, id, projId);

        ProjFitnessVideo video = this.covertToFitnessVideo(videoReq);
        this.updateById(video);
        projLmsI18nService.handleI18n(ListUtil.of(video), projId);
    }

    /**
     * 校验
     *
     * @param videoReq videoReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjFitnessVideoAddReq videoReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjFitnessVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessVideo::getEventName, videoReq.getEventName())
                .ne(Objects.nonNull(id), ProjFitnessVideo::getId, id)
                .eq(ProjFitnessVideo::getProjId, projId);
        int count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("event name already exists");
        }
    }

    /**
     * 转换为ProjFitnessVideo对象
     *
     * @param videoReq videoReq
     * @return ProjFitnessVideo
     */
    private ProjFitnessVideo covertToFitnessVideo(ProjFitnessVideoAddReq videoReq) {
        ProjFitnessVideo video = new ProjFitnessVideo();
        BeanUtils.copyProperties(videoReq, video);
        IEnumBase.setCodeString(video::setTypeCodes, videoReq.getType());
        IEnumBase.setCodeInteger(video::setDifficultyCode, videoReq.getDifficulty());
        IEnumBase.setCodeInteger(video::setPositionCode, videoReq.getPosition());
        IEnumBase.setCodeString(video::setFitTypeCodes, videoReq.getFitType());
        IEnumBase.setCodeString(video::setTargetCodes, videoReq.getTarget());
        IEnumBase.setCodeString(video::setEquipmentCodes, videoReq.getEquipment());
        IEnumBase.setCodeString(video::setSpecialLimitCodes, videoReq.getSpecialLimits());

        return video;
    }

    @Override
    public ProjFitnessVideoDetailVO getVideoDetail(Integer id) {
        ProjFitnessVideo video = this.getById(id);
        if (Objects.isNull(video)) {
            throw new BizException("Data not found");
        }

        ProjFitnessVideoDetailVO detailVO = new ProjFitnessVideoDetailVO();
        BeanUtils.copyProperties(video, detailVO);
        IEnumBase.setEnumList(detailVO::setType, video.getTypeCodes(), TypeEnums::getBy);
        IEnumBase.setEnum(detailVO::setDifficulty, video.getDifficultyCode(), DifficultyEnums::getBy);
        IEnumBase.setEnum(detailVO::setPosition, video.getPositionCode(), PositionEnums::getBy);
        IEnumBase.setEnumList(detailVO::setFitType, video.getFitTypeCodes(), FitTypeEnum::getBy);
        IEnumBase.setEnumList(detailVO::setTarget, video.getTargetCodes(), TargetEnums::getBy);
        IEnumBase.setEnumList(detailVO::setEquipment, video.getEquipmentCodes(), EquipmentEnums::getBy);
        IEnumBase.setEnumList(detailVO::setSpecialLimits, video.getSpecialLimitCodes(), SpecialLimitEnums::getBy);

        fillI18nConfigInfoForDetail(detailVO);
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(idList)) {
            return null;
        }

        List<TaskResourceSection> taskList = taskResourceSectionService.query(PROJ_FITNESS_VIDEO_FRONT.getTableName(), PROJ_FITNESS_VIDEO_FRONT.getEntityFieldName(), idList);
        if (CollUtil.isEmpty(taskList)) {
            return idList;
        }
        List<Integer> completedIdList = TaskResourceSectionUtil.getCompletedIdList(taskList);
        if (CollUtil.isEmpty(completedIdList)) {
            return idList;
        }

        this.update(new ProjFitnessVideo(), new LambdaUpdateWrapper<ProjFitnessVideo>()
                .set(ProjFitnessVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjFitnessVideo::getId, completedIdList));

        idList.removeAll(completedIdList);
        return idList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        this.update(new ProjFitnessVideo(), new LambdaUpdateWrapper<ProjFitnessVideo>()
                .set(ProjFitnessVideo::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessVideo::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        this.update(new ProjFitnessVideo(), new LambdaUpdateWrapper<ProjFitnessVideo>()
                .set(ProjFitnessVideo::getDelFlag, YES)
                .eq(ProjFitnessVideo::getStatus, STATUS_DRAFT)
                .in(ProjFitnessVideo::getId, idList));
    }

    @Override
    public List<ProjFitnessVideo> selectListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectListByIds(Joiner.on(StringPool.COMMA).join(ids));
    }

    private void fillI18nConfigInfo(List<ProjFitnessVideo> records, List<ProjFitnessVideoPageVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjFitnessVideoPageVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ProjFitnessVideo> records) {
        Set<Integer> configIds = records.stream().map(ProjFitnessVideo::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }


    private void fillI18nConfigInfoForDetail(ProjFitnessVideoDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config!= null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }
}
