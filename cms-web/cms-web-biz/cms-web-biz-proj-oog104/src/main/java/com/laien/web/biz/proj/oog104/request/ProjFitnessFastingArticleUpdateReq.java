package com.laien.web.biz.proj.oog104.request;

import com.laien.common.oog104.enums.FitnessFastingArticleEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * fasting article
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="FastingArticle对象", description="fasting article")
public class ProjFitnessFastingArticleUpdateReq extends ProjFitnessFastingArticleAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;
}
