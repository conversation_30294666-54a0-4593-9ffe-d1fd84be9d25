package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

/**
 * 器械枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum EquipmentEnums implements IEnumBase {

    NONE(0, "None", "None"),
    BED(1, "Bed", "Bed"),
    CHAIR(2, "Chair", "Chair"),
    RESISTANCE_BAND(3, "Resistance band", "Resistance band"),
    WALL(4, "Wall", "Wall"),
    DUMBB<PERSON><PERSON>(5, "Dumbbells", "Dumbbells"),
    YOGA_MAT(6, "Yoga mat", "Yoga mat"),
    KETTLEBELL(7, "Kettlebell", "Kettlebell");

    private final Integer code;
    private final String name;
    private final String displayName;

    EquipmentEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static EquipmentEnums getBy(Integer code) {
        for (EquipmentEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
