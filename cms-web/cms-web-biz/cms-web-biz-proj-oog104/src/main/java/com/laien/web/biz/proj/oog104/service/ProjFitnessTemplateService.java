package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplate;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplateGenerateReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplateGenerateWorkoutReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplatePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplateUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplateDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplatePageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_fitness_template(proj_fitness_template)】的数据库操作Service
* @createDate 2025-03-11 17:01:12
*/
public interface ProjFitnessTemplateService extends IService<ProjFitnessTemplate> {

    Integer generate(ProjFitnessTemplateGenerateReq req);

    PageRes<ProjFitnessTemplatePageVO> pageQuery(ProjFitnessTemplatePageReq req);

    ProjFitnessTemplateDetailVO detail(Integer id);

    void generateWorkout(ProjFitnessTemplateGenerateWorkoutReq req) throws Exception;

    void updateEnable(List<Integer> req,Boolean isEnable);

    void delete(List<Integer> idList);

    void update(ProjFitnessTemplateUpdateReq req);
}
