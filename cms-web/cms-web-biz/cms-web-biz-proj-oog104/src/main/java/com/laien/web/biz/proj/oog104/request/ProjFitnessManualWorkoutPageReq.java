package com.laien.web.biz.proj.oog104.request;

import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualEquipmentEnums;
import com.laien.common.oog104.enums.manual.ManualIntensityEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.manual.ManualWorkoutTypeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * Manual Workout Page Request
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Manual Workout Page Request", description = "Request parameters for manual workout pagination query")
public class ProjFitnessManualWorkoutPageReq extends PageReq {

    /**
     * ID
     */
    @ApiModelProperty(value = "Workout ID")
    private Integer id;

    /**
     * Project ID
     */
    @ApiModelProperty(value = "Project ID")
    private Integer projId;

    /**
     * Name
     */
    @ApiModelProperty(value = "Workout Name")
    private String name;

    /**
     * Status
     */
    @ApiModelProperty(value = "Status (0-Draft, 1-Enabled, 2-Disabled)")
    private Integer status;

    /**
     * File Status
     */
    @ApiModelProperty(value = "File Status (0-Success, 1-Processing, 2-Failed)")
    private Integer fileStatus;

    /**
     * Categories
     */
    @ApiModelProperty(value = "Categories 多选")
    private List<ManualCategoryEnums> category;

    /**
     * Workout Type
     */
    @ApiModelProperty(value = "Workout Type")
    private ManualWorkoutTypeEnums workoutType;

    /**
     * Age Groups
     */
    @ApiModelProperty(value = "Age Groups 多选")
    private List<ManualAgeGroupEnums> ageGroup;

    /**
     * Subscription 0-No, 1-Yes
     */
    @ApiModelProperty(value = "Subscription (0-No, 1-Yes)")
    private Integer subscription;

    /**
     * Target Areas
     */
    @ApiModelProperty(value = "Target 多选")
    private List<ManualTargetEnums> target;

    /**
     * Difficulty
     */
    @ApiModelProperty(value = "Difficulty Level")
    private ManualDifficultyEnums difficulty;

    /**
     * Equipment
     */
    @ApiModelProperty(value = "Equipment 多选")
    private List<ManualEquipmentEnums> equipment;

    /**
     * Special Limits
     */
    @ApiModelProperty(value = "Special Limits 多选")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    /**
     * Intensity
     */
    @ApiModelProperty(value = "Intensity")
    private ManualIntensityEnums intensity;

    /**
     * Duration in milliseconds - Minimum
     */
    @ApiModelProperty(value = "Minimum Duration (in milliseconds)")
    private Integer minDuration;

    /**
     * Duration in milliseconds - Maximum
     */
    @ApiModelProperty(value = "Maximum Duration (in milliseconds)")
    private Integer maxDuration;

    /**
     * Calories Burned - Minimum
     */
    @ApiModelProperty(value = "Minimum Calories Burned")
    private BigDecimal minCalorie;

    /**
     * Calories Burned - Maximum
     */
    @ApiModelProperty(value = "Maximum Calories Burned")
    private BigDecimal maxCalorie;

    @ApiModelProperty(value = "videoIdList")
    private List<Integer> videoIdList;
}
