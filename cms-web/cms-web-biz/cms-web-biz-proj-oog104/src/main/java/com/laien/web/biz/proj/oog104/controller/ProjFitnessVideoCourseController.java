package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideoCourse;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCoursePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessVideoCourseService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Fitness VideoCourse 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */

@Api(tags = "项目管理:fitnessVideoCourse")
@RestController
@RequestMapping("/proj/fitnessVideoCourse")
@RequiredArgsConstructor
public class ProjFitnessVideoCourseController extends ResponseController {

    private final IProjFitnessVideoCourseService service;

    @ApiOperation(value = "分页列表")
    @GetMapping( "/page")
    public ResponseResult<PageRes<ProjFitnessVideoCourseListVO>> page(ProjFitnessVideoCoursePageReq pageReq) {
        return succ(service.page(pageReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessVideoCourseAddReq req) {
        service.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessVideoCourseUpdateReq req) {
        service.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessVideoCourseDetailVO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.deleteByIdList(idList);
        return succ();
    }

}
