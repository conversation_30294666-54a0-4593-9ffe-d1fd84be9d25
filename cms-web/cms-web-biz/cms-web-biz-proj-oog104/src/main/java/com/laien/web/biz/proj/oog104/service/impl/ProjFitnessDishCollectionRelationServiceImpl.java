package com.laien.web.biz.proj.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishCollectionRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessDishCollectionRelationMapper;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishCollectionRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/3 11:05
 */
@Service
public class ProjFitnessDishCollectionRelationServiceImpl extends ServiceImpl<ProjFitnessDishCollectionRelationMapper, ProjFitnessDishCollectionRelation> implements IProjFitnessDishCollectionRelationService {


    @Override
    public void deleteByDishCollectionId(Integer dishCollectionId) {

        LambdaUpdateWrapper<ProjFitnessDishCollectionRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjFitnessDishCollectionRelation::getProjFitnessDishCollectionId, dishCollectionId);
        updateWrapper.set(ProjFitnessDishCollectionRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjFitnessDishCollectionRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjFitnessDishCollectionRelation> listByDishCollectionId(Integer dishCollectionId) {

        LambdaQueryWrapper<ProjFitnessDishCollectionRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessDishCollectionRelation::getProjFitnessDishCollectionId, dishCollectionId);
        return list(queryWrapper);
    }

}
