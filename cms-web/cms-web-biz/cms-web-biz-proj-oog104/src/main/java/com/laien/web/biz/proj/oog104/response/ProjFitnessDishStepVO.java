package com.laien.web.biz.proj.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * ProjFitnessDishStepVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@Accessors(chain = true)
@ApiModel(value="dish step对象", description="dish step")
public class ProjFitnessDishStepVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "tip list")
    private List<ProjFitnessDishStepTipVO> tipList;

}
