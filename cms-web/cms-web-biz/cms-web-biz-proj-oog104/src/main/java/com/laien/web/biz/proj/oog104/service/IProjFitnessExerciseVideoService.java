package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoExportVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * proj_fitness_exercise_video 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface IProjFitnessExerciseVideoService extends IService<ProjFitnessExerciseVideo> {


    List<ProjFitnessExerciseVideo> listEnable4AudoGenerate();

    /**
     * ExerciseVideo分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjFitnessExerciseVideoPageVO> selectVideoPage(ProjFitnessExerciseVideoPageReq pageReq);

    /**
     * 根据ids查询ExerciseVideo列表
     * @param ids
     * @return
     */
    List<ProjFitnessExerciseVideoPageVO> listVOByIds(List<Integer> ids);

    /**
     * ExerciseVideo导出(忽略分页)
     * @param pageReq pageReq
     * @return List
     */
    List<ProjFitnessExerciseVideoExportVO> exportVideos(ProjFitnessExerciseVideoPageReq pageReq);

    /**
     * video新增
     *
     * @param videoReq videoReq
     */
    void saveVideo(ProjFitnessExerciseVideoAddReq videoReq);

    /**
     * video修改
     *
     * @param videoReq videoReq
     */
    void updateVideo(ProjFitnessExerciseVideoUpdateReq videoReq);

    /**
     * video详情
     *
     * @param id id
     * @return ProjFitnessVideoDetailVO
     */
    ProjFitnessExerciseVideoDetailVO getVideoDetail(Integer id);

    /**
     * video启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * video禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * video删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 导入Excel
     * @param excelInputStream
     * @return
     */
    List<String> importByExcel(InputStream excelInputStream, Integer projectId);
}
