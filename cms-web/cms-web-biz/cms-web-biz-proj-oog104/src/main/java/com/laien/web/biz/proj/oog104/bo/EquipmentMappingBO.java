package com.laien.web.biz.proj.oog104.bo;

import com.google.common.collect.Lists;
import com.laien.common.oog104.enums.manual.ManualEquipmentEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import lombok.Data;

import java.util.List;
import java.util.Objects;
@Data
public class EquipmentMappingBO {
    private ManualEquipmentEnums equipment;
    private float percent;

    public static List<EquipmentMappingBO> getEquipmentMappingByWorkoutDurationRange(WorkoutDurationRangeEnums workoutDurationRangeEnums) {

        if (Objects.equals(workoutDurationRangeEnums, WorkoutDurationRangeEnums.FIVE_TO_TEN_MIN)) {
            EquipmentMappingBO mappingBO_1 = new EquipmentMappingBO();
            mappingBO_1.setEquipment(ManualEquipmentEnums.DUMBBELLS);
            mappingBO_1.setPercent(1.0f);
            return Lists.newArrayList(mappingBO_1);
        }
        else if (Objects.equals(workoutDurationRangeEnums, WorkoutDurationRangeEnums.TEN_TO_FIFTEEN_MIN)
        || Objects.equals(workoutDurationRangeEnums, WorkoutDurationRangeEnums.FIFTEEN_TO_TWENTY_MIN)) {
            EquipmentMappingBO mappingBO_1 = new EquipmentMappingBO();
            mappingBO_1.setEquipment(ManualEquipmentEnums.DUMBBELLS);
            mappingBO_1.setPercent(0.8f);
            EquipmentMappingBO mappingBO_2 = new EquipmentMappingBO();
            mappingBO_2.setEquipment(ManualEquipmentEnums.NONE);
            mappingBO_2.setPercent(0.2f);
            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        } else {
            EquipmentMappingBO mappingBO_1 = new EquipmentMappingBO();
            mappingBO_1.setEquipment(ManualEquipmentEnums.DUMBBELLS);
            mappingBO_1.setPercent(0.7f);
            EquipmentMappingBO mappingBO_2 = new EquipmentMappingBO();
            mappingBO_2.setEquipment(ManualEquipmentEnums.NONE);
            mappingBO_2.setPercent(0.3f);
            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }
    }
}
