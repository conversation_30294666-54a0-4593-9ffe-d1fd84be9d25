package com.laien.web.biz.proj.oog104.request;

import com.laien.common.oog104.enums.manual.*;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 健身训练生成记录分页查询请求
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProjFitnessWorkoutGenerate分页查询请求")
public class ProjFitnessWorkoutGeneratePageReq extends PageReq {

    @ApiModelProperty(value = "Workout Id")
    private Integer id;

    @ApiModelProperty(value = "模板ID")
    private Integer projFitnessTemplateId;

    @ApiModelProperty(value = "template启用状态 0草稿 1启用 2停用")
    private Integer templateStatus;

    @ApiModelProperty(value = "templateLevel")
    private ManualDifficultyEnums templateLevel;

    @ApiModelProperty(value = "任务ID")
    private Integer projFitnessTemplateTaskId;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "fileStatus")
    private Integer fileStatus;

    @ApiModelProperty(value = "目标")
    private ManualTargetEnums target;

    @ApiModelProperty(value = "难度")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "器材")
    private ManualEquipmentEnums equipment;

    @ApiModelProperty(value = "特殊限制(多选)")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "强度(多选)")
    private List<ManualIntensityEnums> intensity;

    @ApiModelProperty(value = "Time")
    private WorkoutDurationRangeEnums time;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "videoIdList")
    private List<Integer> videoIdList;

}
