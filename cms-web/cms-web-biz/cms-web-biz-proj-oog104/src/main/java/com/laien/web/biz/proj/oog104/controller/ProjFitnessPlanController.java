package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.entity.ProjFitnessPlan;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessPlanDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessPlanPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessPlanService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_fitness_plan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Api(tags = "项目管理:Fitness Plan")
@RestController
@RequestMapping("/proj/fitnessPlan")
public class ProjFitnessPlanController extends ResponseController {

    @Resource
    private IProjFitnessPlanService projFitnessPlanService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessPlanPageVO>> page(ProjFitnessPlanPageReq pageReq) {
        PageRes<ProjFitnessPlanPageVO> pageRes = projFitnessPlanService.selectPlanPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@Validated @RequestBody ProjFitnessPlanAddReq planReq) {
        projFitnessPlanService.savePlan(planReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> add(@Validated @RequestBody ProjFitnessPlanUpdateReq planReq) {
        projFitnessPlanService.updatePlan(planReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessPlanDetailVO> detail(@PathVariable Integer id) {
        ProjFitnessPlanDetailVO detailVO = projFitnessPlanService.getPlanDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessPlanService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessPlanService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessPlanService.deleteByIds(idList);
        return succ();
    }


}
