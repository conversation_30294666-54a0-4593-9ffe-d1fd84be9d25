package com.laien.web.biz.proj.oog104.request;

import com.laien.common.oog104.enums.manual.*;
import com.laien.web.biz.proj.oog104.enums.MatchTypeEnum;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * Fitness Exercise video 分页
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness Exercise video 分页", description = "Fitness Exercise video 分页")
public class ProjFitnessExerciseVideoPageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "ids")
    private List<Integer> ids;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "exerciseType")
    private ManualExerciseTypeEnums exerciseType;

    @ApiModelProperty(value = "类型 WARM_UP-Warm up MAIN-Main COOL_DOWN-Cool down")
    private ManualTypeEnums type;

    @ApiModelProperty(value = "intensity STRETCH-Stretch CARDIO-Cardio HIIT-Hiit POWER-Power")
    private ManualIntensityEnums intensity;

    @ApiModelProperty(value = "难度 BEGINNER-Beginner INTERMEDIATE-Intermediate ADVANCED-Advanced")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "部位 HEAD-Head BODY-Body LEGS-Legs")
    private ManualPositionEnums position;

    @ApiModelProperty(value = "特殊限制列表 多选 NONE-None WRIST-Wrist FOOT-Foot BACK-Back SHOULDER-Shooter ABS-Abs")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "目标 多选 ARM-Arm BACK-Back ABS-Abs LEGS-Legs BUTT-Butt")
    private List<ManualTargetEnums> target;

    @ApiModelProperty(value = "器械 DUMBBELLS-Dumbbells NONE-None")
    private ManualEquipmentEnums equipment;

    @ApiModelProperty(value = "特殊限制匹配规则，默认为包含所有为真")
    private MatchTypeEnum specialLimitMatchType = MatchTypeEnum.CONTAINS_ALL;

    @ApiModelProperty(value = "videoDirection CENTRAL-Central LEFT-Left RIGHT-Right")
    private ManualVideoDirectionEnums videoDirection;

    @ApiModelProperty(value = "是否参与自动生成 1-是 0-否")
    private Integer usedForAuto;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
