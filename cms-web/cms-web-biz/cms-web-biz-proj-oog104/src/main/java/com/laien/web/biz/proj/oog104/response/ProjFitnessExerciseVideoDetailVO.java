package com.laien.web.biz.proj.oog104.response;

import com.laien.common.oog104.enums.manual.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ExerciseVideo detail VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExerciseVideo 详情", description = "ExerciseVideo 详情")
public class ProjFitnessExerciseVideoDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "图片地址，支持webp,png")
    private String imageUrl;

    @ApiModelProperty(value = "类型code")
    private ManualTypeEnums type;

    @ApiModelProperty(value = "exerciseType")
    private ManualExerciseTypeEnums exerciseType;

    @ApiModelProperty(value = "intensity")
    private ManualIntensityEnums intensity;

    @ApiModelProperty(value = "难度")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "部位")
    private ManualPositionEnums position;

    @ApiModelProperty(value = "特殊限制列表")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "目标")
    private List<ManualTargetEnums> target;

    @ApiModelProperty(value = "器械")
    private ManualEquipmentEnums equipment;

    @ApiModelProperty(value = "direction")
    private ManualVideoDirectionEnums videoDirection;

    @ApiModelProperty(value = "Left-Right 关联 Right 类型的动作 ID")
    private Integer leftRightVideoId;

    @ApiModelProperty(value = "Left-Right 关联 Right 类型的动作")
    private ProjFitnessExerciseVideoDetailVO leftRightDetail;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "指导文本 (500字符限制)")
    private String guidance;

    @ApiModelProperty(value = "如何做 (500字符限制)")
    private String howToDo;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频 (mp3格式)")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "指导音频 (mp3格式)")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "指导音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "如何做音频 (mp3格式)")
    private String howToDoAudioUrl;

    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ApiModelProperty(value = "MET (1-12)")
    private Integer met;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否参与自动生成 1-是 0-否")
    private Integer usedForAuto;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "coreVoiceConfigI18nName")
    private String coreVoiceConfigI18nName;

}
