package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessAllergenRelation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * Fitness Allergen Relation 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
public interface IProjFitnessAllergenRelationService extends IService<ProjFitnessAllergenRelation> {

    void saveBatch(List<Integer> allergenIdList, Integer projId, Integer dataId,
                   FitnessAllergenRelationBusinessEnums businessType);

    List<ProjFitnessAllergenRelation> query(Integer dishId, FitnessAllergenRelationBusinessEnums businessType);
}
