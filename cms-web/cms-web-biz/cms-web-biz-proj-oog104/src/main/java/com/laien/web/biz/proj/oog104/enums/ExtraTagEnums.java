package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

@Getter
public enum ExtraTagEnums implements IEnumBase {

    HOME(1, "Home", "Home"),
    YOGA_MAT(2, "Yoga mat", "Yoga mat");

    private final Integer code;
    private final String name;
    private final String displayName;

    ExtraTagEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static ExtraTagEnums getBy(Integer code) {
        for (ExtraTagEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
