package com.laien.web.biz.proj.oog104.controller;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessManualWorkout;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessManualWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *     Manual Workout 控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Api(tags = "项目管理:Manual Workout")
@RestController
@RequestMapping("/proj/fitnessManualWorkout")
public class ProjFitnessManualWorkoutController extends ResponseController {

    @Resource
    private IProjFitnessManualWorkoutService manualWorkoutService;

    @ApiOperation(value = "Manual Workout 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessManualWorkoutPageVO>> page(ProjFitnessManualWorkoutPageReq pageReq) {
        pageReq.setProjId(RequestContextUtils.getProjectId());
        PageRes<ProjFitnessManualWorkoutPageVO> pageRes = manualWorkoutService.selectWorkoutPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "Manual Workout 增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessManualWorkoutAddReq workoutReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        workoutReq.setProjId(projectId);
        manualWorkoutService.saveWorkout(workoutReq);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessManualWorkoutUpdateReq workoutReq) {
        manualWorkoutService.updateWorkout(workoutReq);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessManualWorkoutDetailVO> detail(@PathVariable Integer id) {
        ProjFitnessManualWorkoutDetailVO detailVO = manualWorkoutService.getWorkoutDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "Manual Workout 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        manualWorkoutService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        manualWorkoutService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        manualWorkoutService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Manual Workout 生成M3U8文件")
    @PostMapping("/generateM3u8")
    public ResponseResult<Boolean> generateM3u8(@RequestBody ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        m3u8Req.setProjId(RequestContextUtils.getProjectId());
        Boolean result = manualWorkoutService.generateM3u8(m3u8Req);
        return succ(result);
    }
}
