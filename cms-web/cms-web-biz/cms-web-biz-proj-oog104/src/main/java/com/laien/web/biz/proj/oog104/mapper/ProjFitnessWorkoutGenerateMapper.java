package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerate;
import com.laien.web.biz.proj.oog104.entity.TemplateWorkoutNum;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGeneratePageReq;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_fitness_workout_generate Mapper
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface ProjFitnessWorkoutGenerateMapper extends BaseMapper<ProjFitnessWorkoutGenerate> {

    /**
     * <p>统计模板生成的workout数量</p>
     *
     * @param idsStr 模板ID集合
     * @return java.util.List<com.laien.web.biz.proj.oog104.entity.TemplateWorkoutNum>
     * <AUTHOR>
     * @date 2025/3/12 18:22
     */
    @Select("<script> " +
            "SELECT a.proj_fitness_template_id AS templateId, COUNT(a.id) AS workoutNum " +
            "FROM proj_fitness_workout_generate a " +
            "WHERE del_flag = 0 " +
            "AND proj_fitness_template_id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>#{id}</foreach> " +
            "GROUP BY a.proj_fitness_template_id " +
            "</script>")
    List<TemplateWorkoutNum> countTemplateWorkoutNum(@Param("ids") List<Integer> ids);

    IPage<Integer> page(@Param("page") Page<ProjFitnessWorkoutGenerate> page,
                        @Param("pageReq") ProjFitnessWorkoutGeneratePageReq pageReq,
                        @Param("templateIdSet") Set<Integer> templateIdSet,
                        @Param("minDuration") Integer minDuration,
                        @Param("maxDuration") Integer maxDuration);

    List<Integer> list(@Param("pageReq") ProjFitnessWorkoutGeneratePageReq pageReq,
                       @Param("templateIdSet") Set<Integer> templateIdSet,
                       @Param("minDuration") Integer minDuration,
                       @Param("maxDuration") Integer maxDuration);

}
