package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "proj_fitness_allergen_relation",autoResultMap = true)
@ApiModel(value="ProjFitnessAllergenRelation对象", description="fitness_allergen和业务表的关系")
public class ProjFitnessAllergenRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("业务表数据id")
    private Integer dataId;

    @ApiModelProperty("proj_fitness_allergen表数据id")
    private Integer projFitnessAllergenId;

    @ApiModelProperty("业务type，1000:dish")
    private FitnessAllergenRelationBusinessEnums businessType;

    @ApiModelProperty("项目id")
    private Integer projId;
}
