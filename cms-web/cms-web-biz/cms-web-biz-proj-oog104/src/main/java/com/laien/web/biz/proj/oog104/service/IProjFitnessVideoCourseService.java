package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideoCourse;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCoursePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseListVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * Fitness VideoCourse 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
public interface IProjFitnessVideoCourseService extends IService<ProjFitnessVideoCourse> {

    PageRes<ProjFitnessVideoCourseListVO> page(ProjFitnessVideoCoursePageReq listReq, Integer projId);

    List<ProjFitnessVideoCourseListVO> listVOByIds(List<Integer> ids);

    ProjFitnessVideoCourseDetailVO findDetailById(Integer id);

    void update(ProjFitnessVideoCourseUpdateReq req, Integer projectId);

    void save(ProjFitnessVideoCourseAddReq dishReq, Integer projId);

    List<Integer> updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);
}
