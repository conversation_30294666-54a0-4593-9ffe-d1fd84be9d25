package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.entity.ProjFitnessCollection;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCollectionDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCollectionPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCollectionService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_fitness_collection 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Api(tags = "项目管理:Fitness Collection")
@RestController
@RequestMapping("/proj/fitnessCollection")
public class ProjFitnessCollectionController extends ResponseController {

    @Resource
    private IProjFitnessCollectionService projFitnessCollectionService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjFitnessCollectionPageVO>> page(ProjFitnessCollectionPageReq pageReq) {
        List<ProjFitnessCollectionPageVO> list = projFitnessCollectionService.selectCollectionPage(pageReq);
        return succ(list);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@Validated @RequestBody ProjFitnessCollectionAddReq CollectionReq) {
        projFitnessCollectionService.saveCollection(CollectionReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> add(@Validated @RequestBody ProjFitnessCollectionUpdateReq CollectionReq) {
        projFitnessCollectionService.updateCollection(CollectionReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessCollectionDetailVO> detail(@PathVariable Integer id) {
        ProjFitnessCollectionDetailVO detailVO = projFitnessCollectionService.getCollectionDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessCollectionService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessCollectionService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessCollectionService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessCollectionService.saveSort(idList);
        return succ();
    }


}
