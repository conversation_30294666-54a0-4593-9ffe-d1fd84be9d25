package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.constant.LmsConstant;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.*;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * proj_fitness_exercise_video
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessExerciseVideo对象", description = "proj_fitness_exercise_video")
@TableName(autoResultMap = true)
public class ProjFitnessExerciseVideo extends BaseModel  implements CoreI18nModel {

    private static final long serialVersionUID = -3980786728453076656L;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId = LmsConstant.DEFAULT_VOICE_CONFIG_ID_FEMALE;

    @ApiModelProperty(value = "动作名称")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "nameAudioUrl")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_EXERCISE_VIDEO;

    @ApiModelProperty(value = "图片地址，支持webp,png")
    private String imageUrl;

    @ApiModelProperty(value = "类型code (1-Warm Up, 2-Cool Down, 3-Main)")
    private ManualTypeEnums type;

    @ApiModelProperty(value = "exerciseType")
    private ManualExerciseTypeEnums exerciseType;

    @ApiModelProperty(value = "难度code (1-Beginner, 2-Intermediate, 3-Advanced)")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "目标code (多选, 逗号分隔)")
    @TableField(typeHandler = ManualTargetEnums.TypeHandler.class)
    private List<ManualTargetEnums> target;

    @ApiModelProperty(value = "特殊限制code (多选, 逗号分隔)")
    @TableField(typeHandler = ExerciseVideoSpecialLimitEnums.TypeHandler.class)
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "强度code (1-Stretch, 2-Cardio, 3-Hiit, 4-Power)")
    private ManualIntensityEnums intensity;

    @ApiModelProperty(value = "器械code (1-Dumbbells, 2-Yoga Mat, 0-None)")
    private ManualEquipmentEnums equipment;

    @ApiModelProperty(value = "位置code (1-Standing, 2-Sitting, 3-Lying)")
    private ManualPositionEnums position;

    @ApiModelProperty(value = "视频方向code (1-Central, 2-Left, 3-Right)")
    private ManualVideoDirectionEnums videoDirection;

    @ApiModelProperty(value = "Left-Right 关联 Right 类型的动作 ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)//允许更新为null
    private Integer leftRightVideoId;

    @ApiModelProperty(value = "指导文本 (500字符限制)")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "guidanceAudioUrl",
            durationFieldName = "guidanceAudioDurationConfig")
    private String guidance;

    @ApiModelProperty(value = "如何做 (500字符限制)")
    private String howToDo;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频 (mp3格式)")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "指导音频 (mp3格式)")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "指导音频时长")
    private Integer guidanceAudioDuration;

    @JsonIgnore
    @TableField(exist = false)
    @ApiModelProperty(value = "指导音频时长配制时长",hidden = true)
    private Integer guidanceAudioDurationConfig = 14000;

    @ApiModelProperty(value = "如何做音频 (mp3格式)")
    private String howToDoAudioUrl;

    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ApiModelProperty(value = "MET (1-12)")
    private Integer met;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "是否参与自动生成 1-是 0-否")
    private Integer usedForAuto;
}
