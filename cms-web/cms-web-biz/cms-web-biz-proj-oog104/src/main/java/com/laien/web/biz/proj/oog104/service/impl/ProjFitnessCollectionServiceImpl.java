package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.CollectionWorkoutCount;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCollection;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCollectionProjFitnessWorkout;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkout;
import com.laien.web.biz.proj.oog104.enums.DifficultyEnums;
import com.laien.web.biz.proj.oog104.enums.EquipmentEnums;
import com.laien.web.biz.proj.oog104.enums.ExtraTagEnums;
import com.laien.web.biz.proj.oog104.enums.IEnumBase;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessCollectionMapper;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionAddWorkoutReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCollectionDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCollectionDetailWorkoutVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCollectionPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCollectionProjFitnessWorkoutService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCollectionService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_collection 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class ProjFitnessCollectionServiceImpl extends ServiceImpl<ProjFitnessCollectionMapper, ProjFitnessCollection> implements IProjFitnessCollectionService {

    @Resource
    private IProjFitnessCollectionProjFitnessWorkoutService projFitnessCollectionProjFitnessWorkoutService;
    @Resource
    private IProjFitnessWorkoutService projFitnessWorkoutService;
    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessCollectionPageVO> selectCollectionPage(ProjFitnessCollectionPageReq pageReq) {
        LambdaQueryWrapper<ProjFitnessCollection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessCollection::getProjId, RequestContextUtils.getProjectId())
                .orderByAsc(ProjFitnessCollection::getSortNo)
                .orderByDesc(ProjFitnessCollection::getId);

        List<ProjFitnessCollection> records = this.list(queryWrapper);
        if(CollUtil.isEmpty(records)){
            return Collections.emptyList();
        }
        String idStr = records.stream().map(c -> c.getId().toString()).collect(Collectors.joining(","));
        Map<Integer, Integer> workoutCountDisabledNumMap = baseMapper.countVideosByCollectionIds(idStr).stream()
                .filter(c -> c.getWorkoutStatus().equals(GlobalConstant.STATUS_DISABLE))
                .collect(Collectors.groupingBy(CollectionWorkoutCount::getCollectionId, Collectors.summingInt(CollectionWorkoutCount::getWorkoutCount)));

        return records.stream().map(collection -> {
            ProjFitnessCollectionPageVO pageVO = new ProjFitnessCollectionPageVO();
            BeanUtils.copyProperties(collection, pageVO);
            IEnumBase.setEnum(pageVO::setDifficulty, collection.getDifficultyCode(), DifficultyEnums::getBy);
            IEnumBase.setEnumList(pageVO::setEquipment, collection.getEquipmentCodes(), EquipmentEnums::getBy);
            pageVO.setWorkoutDisabledCount(workoutCountDisabledNumMap.getOrDefault(collection.getId(), 0));
            return pageVO;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCollection(ProjFitnessCollectionAddReq collectionReq) {
        // 校验
        Integer projId = RequestContextUtils.getProjectId();
        this.check(collectionReq, null, projId);

        ProjFitnessCollection collection = this.covertToFitnessCollection(collectionReq);
        collection.setStatus(GlobalConstant.STATUS_DRAFT);
        collection.setProjId(projId);
        collection.setSortNo(GlobalConstant.ZERO);
        this.save(collection);
        // 保存workout关系
        this.saveRelation(collection.getId(), projId, collectionReq.getWorkoutList());

        projLmsI18nService.handleI18n(ListUtil.of(collection), projId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCollection(ProjFitnessCollectionUpdateReq collectionReq) {
        Integer id = collectionReq.getId();
        ProjFitnessCollection collectionFind = this.getById(id);
        if (Objects.isNull(collectionFind)) {
            throw new BizException("Data not found");
        }

        Integer projId = collectionFind.getProjId();
        // 校验
        this.check(collectionReq, id, projId);

        ProjFitnessCollection collection = this.covertToFitnessCollection(collectionReq);
        if (Objects.isNull(collectionReq.getNewStartTime()) || Objects.isNull(collectionReq.getNewEndTime())) {
            this.baseMapper.updateNewTime(id, collectionReq.getNewStartTime(), collectionReq.getNewEndTime());
        }
        this.updateById(collection);
        // 删除workout关系
        this.deleteRelation(id);
        // 保存workout关系
        this.saveRelation(id, projId, collectionReq.getWorkoutList());

        projLmsI18nService.handleI18n(ListUtil.of(collection), projId);
    }

    /**
     * 校验
     *
     * @param collectionReq collectionReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjFitnessCollectionAddReq collectionReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjFitnessCollection> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessCollection::getName, collectionReq.getName())
                .ne(Objects.nonNull(id), ProjFitnessCollection::getId, id)
                .eq(ProjFitnessCollection::getProjId, projId);
        int count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("name already exists");
        }

        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessCollection::getEventName, collectionReq.getEventName())
                .ne(Objects.nonNull(id), ProjFitnessCollection::getId, id)
                .eq(ProjFitnessCollection::getProjId, projId);
        count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("event name already exists");
        }
    }

    /**
     * 转换为ProjFitnessCollection对象
     *
     * @param collectionReq collectionReq
     * @return ProjFitnessCollection
     */
    private ProjFitnessCollection covertToFitnessCollection(ProjFitnessCollectionAddReq collectionReq) {
        ProjFitnessCollection collection = new ProjFitnessCollection();
        BeanUtils.copyProperties(collectionReq, collection);
        IEnumBase.setCodeInteger(collection::setDifficultyCode, collectionReq.getDifficulty());
        IEnumBase.setCodeString(collection::setEquipmentCodes, collectionReq.getEquipment());

        List<Integer> workoutIds = collectionReq.getWorkoutList().stream()
                .map(ProjFitnessCollectionAddWorkoutReq::getId)
                .collect(Collectors.toList());
        List<ProjFitnessWorkout> workouts = projFitnessWorkoutService.selectListByIds(workoutIds);
        int duration = workouts.stream().mapToInt(ProjFitnessWorkout::getDuration).sum();
        collection.setWorkoutCount(workouts.size());
        collection.setDuration(duration);
        return collection;
    }

    @Override
    public ProjFitnessCollectionDetailVO getCollectionDetail(Integer id) {
        ProjFitnessCollection collectionFind = this.getById(id);
        if (Objects.isNull(collectionFind)) {
            throw new BizException("Data not found");
        }

        ProjFitnessCollectionDetailVO detailVO = new ProjFitnessCollectionDetailVO();
        BeanUtils.copyProperties(collectionFind, detailVO);
        IEnumBase.setEnum(detailVO::setDifficulty, collectionFind.getDifficultyCode(), DifficultyEnums::getBy);
        IEnumBase.setEnumList(detailVO::setEquipment, collectionFind.getEquipmentCodes(), EquipmentEnums::getBy);

        List<ProjFitnessCollectionDetailWorkoutVO> workoutList = this.getWorkoutList(id);
        detailVO.setWorkoutList(workoutList);

        return detailVO;
    }

    /**
     * 获取collection workout list
     *
     * @param id id
     * @return list
     */
    private List<ProjFitnessCollectionDetailWorkoutVO> getWorkoutList(Integer id) {
        List<ProjFitnessCollectionProjFitnessWorkout> fitnessCollectionProjFitnessWorkoutList =
                projFitnessCollectionProjFitnessWorkoutService.list(new LambdaQueryWrapper<ProjFitnessCollectionProjFitnessWorkout>()
                        .eq(ProjFitnessCollectionProjFitnessWorkout::getProjFitnessCollectionId, id));
        List<Integer> workoutIds = fitnessCollectionProjFitnessWorkoutList.stream()
                .map(ProjFitnessCollectionProjFitnessWorkout::getProjFitnessWorkoutId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, ProjFitnessWorkout> fitnessWorkoutMap = projFitnessWorkoutService.selectListByIds(workoutIds).stream()
                .collect(Collectors.toMap(ProjFitnessWorkout::getId, t -> t));

        List<ProjFitnessCollectionDetailWorkoutVO> workoutList = new ArrayList<>();
        for (ProjFitnessCollectionProjFitnessWorkout fitnessCollectionProjFitnessWorkout : fitnessCollectionProjFitnessWorkoutList) {
            ProjFitnessCollectionDetailWorkoutVO workoutVO = new ProjFitnessCollectionDetailWorkoutVO();
            ProjFitnessWorkout workout = fitnessWorkoutMap.get(fitnessCollectionProjFitnessWorkout.getProjFitnessWorkoutId());
            if (workout != null) {
                BeanUtils.copyProperties(workout, workoutVO);
                IEnumBase.setEnumList(workoutVO::setExtraTag, workout.getExtraTagCodes(), ExtraTagEnums::getBy);
            }
            workoutList.add(workoutVO);
        }

        return workoutList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        this.update(new ProjFitnessCollection(), new LambdaUpdateWrapper<ProjFitnessCollection>()
                .set(ProjFitnessCollection::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessCollection::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjFitnessCollection::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        this.update(new ProjFitnessCollection(), new LambdaUpdateWrapper<ProjFitnessCollection>()
                .set(ProjFitnessCollection::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessCollection::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessCollection::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            boolean flag = this.update(new ProjFitnessCollection(), new LambdaUpdateWrapper<ProjFitnessCollection>()
                    .set(ProjFitnessCollection::getDelFlag, GlobalConstant.YES)
                    .eq(ProjFitnessCollection::getStatus, GlobalConstant.STATUS_DRAFT)
                    .eq(ProjFitnessCollection::getId, id));
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveSort(List<Integer> idList) {
        if (CollectionUtils.isNotEmpty(idList)) {
            int sortNoIndex = 1;
            Integer projId = RequestContextUtils.getProjectId();
            for (Integer id : idList) {
                LambdaUpdateWrapper<ProjFitnessCollection> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProjFitnessCollection::getId, id);
                wrapper.eq(ProjFitnessCollection::getProjId, projId);
                wrapper.set(ProjFitnessCollection::getSortNo, sortNoIndex);
                this.update(new ProjFitnessCollection(), wrapper);
                sortNoIndex++;
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDurationByWorkoutId(Integer workoutId) {
        List<Integer> collectionIds = projFitnessCollectionProjFitnessWorkoutService.list(new LambdaQueryWrapper<ProjFitnessCollectionProjFitnessWorkout>()
                        .eq(ProjFitnessCollectionProjFitnessWorkout::getProjFitnessWorkoutId, workoutId))
                .stream().map(ProjFitnessCollectionProjFitnessWorkout::getProjFitnessCollectionId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collectionIds)) {
            return;
        }

        Set<Integer> workoutSet = new HashSet<>();
        Map<Integer, List<ProjFitnessCollectionProjFitnessWorkout>> collectionWorkoutListMap = projFitnessCollectionProjFitnessWorkoutService.list(new LambdaQueryWrapper<ProjFitnessCollectionProjFitnessWorkout>()
                        .in(ProjFitnessCollectionProjFitnessWorkout::getProjFitnessCollectionId, collectionIds))
                .stream().peek(item -> workoutSet.add(item.getProjFitnessWorkoutId()))
                .collect(Collectors.groupingBy(ProjFitnessCollectionProjFitnessWorkout::getProjFitnessCollectionId));

        Map<Integer, ProjFitnessWorkout> fitnessWorkoutMap = projFitnessWorkoutService.selectListByIds(new ArrayList<>(workoutSet)).stream()
                .collect(Collectors.toMap(ProjFitnessWorkout::getId, t -> t));
        for (Integer collectionId : collectionIds) {
            List<ProjFitnessCollectionProjFitnessWorkout> projFitnessWorkouts = collectionWorkoutListMap.get(collectionId);
            if (CollectionUtils.isNotEmpty(projFitnessWorkouts)) {
                int duration = 0;
                for (ProjFitnessCollectionProjFitnessWorkout collectionProjFitnessWorkout : projFitnessWorkouts) {
                    ProjFitnessWorkout workout = fitnessWorkoutMap.get(collectionProjFitnessWorkout.getProjFitnessWorkoutId());
                    if (workout != null) {
                        duration += workout.getDuration();
                    }
                }

                ProjFitnessCollection collection = new ProjFitnessCollection();
                collection.setId(collectionId);
                collection.setDuration(duration);
                this.updateById(collection);
            }
        }

    }

    /**
     * 保存workout关系
     *
     * @param id id
     * @param projId projId
     * @param workoutList workoutList
     */
    private void saveRelation(Integer id, Integer projId, List<ProjFitnessCollectionAddWorkoutReq> workoutList) {
        List<ProjFitnessCollectionProjFitnessWorkout> fitnessCollectionProjFitnessWorkoutList = new ArrayList<>();
        for (ProjFitnessCollectionAddWorkoutReq fitnessCollectionAddWorkoutReq : workoutList) {
            ProjFitnessCollectionProjFitnessWorkout fitnessCollectionProjFitnessWorkout = new ProjFitnessCollectionProjFitnessWorkout();
            fitnessCollectionProjFitnessWorkout.setProjFitnessCollectionId(id);
            fitnessCollectionProjFitnessWorkout.setProjId(projId);
            fitnessCollectionProjFitnessWorkout.setProjFitnessWorkoutId(fitnessCollectionAddWorkoutReq.getId());
            fitnessCollectionProjFitnessWorkoutList.add(fitnessCollectionProjFitnessWorkout);
        }

        projFitnessCollectionProjFitnessWorkoutService.saveBatch(fitnessCollectionProjFitnessWorkoutList);
    }

    /**
     * 删除workout关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        projFitnessCollectionProjFitnessWorkoutService.update(new ProjFitnessCollectionProjFitnessWorkout(),
                new LambdaUpdateWrapper<ProjFitnessCollectionProjFitnessWorkout>()
                        .set(ProjFitnessCollectionProjFitnessWorkout::getDelFlag, GlobalConstant.YES)
                        .eq(ProjFitnessCollectionProjFitnessWorkout::getProjFitnessCollectionId, id)
        );
    }

}
