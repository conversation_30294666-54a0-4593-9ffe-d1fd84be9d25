package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "workout video 添加", description = "workout video 添加")
public class ProjFitnessWorkoutAddVideoReq {

    @ApiModelProperty(value = "video id")
    private Integer id;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

}
