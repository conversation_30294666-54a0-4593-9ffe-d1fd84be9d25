package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessIngredient;
import com.laien.web.biz.proj.oog104.request.ProjFitnessIngredientReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessIngredientVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * Fitness ingredient 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
public interface IProjFitnessIngredientService extends IService<ProjFitnessIngredient> {
    @Transactional(rollbackFor = Exception.class)
    void saveBatch(List<ProjFitnessIngredientReq> ingredientReqList, Integer dishId, Integer projId);

    @Transactional(rollbackFor = Exception.class)
    void deleteBatch(List<Integer> dishIdList);

    List<ProjFitnessIngredientVO> query(Integer dishId);
}
