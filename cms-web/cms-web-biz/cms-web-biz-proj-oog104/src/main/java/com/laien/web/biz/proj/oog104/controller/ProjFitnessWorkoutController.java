package com.laien.web.biz.proj.oog104.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkout;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerateFileTask;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessWorkoutService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_fitness_workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Api(tags = "项目管理:Fitness Workout")
@RestController
@RequestMapping("/proj/fitnessWorkout")
public class ProjFitnessWorkoutController extends ResponseController {

    @Resource
    private IProjFitnessWorkoutService projFitnessWorkoutService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessWorkoutPageVO>> page(ProjFitnessWorkoutPageReq pageReq) {
        PageRes<ProjFitnessWorkoutPageVO> pageRes = projFitnessWorkoutService.selectWorkoutPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@Validated @RequestBody ProjFitnessWorkoutAddReq workoutReq) {
        projFitnessWorkoutService.saveWorkout(workoutReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> add(@Validated @RequestBody ProjFitnessWorkoutUpdateReq workoutReq) {
        projFitnessWorkoutService.updateWorkout(workoutReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessWorkoutDetailVO> detail(@PathVariable Integer id) {
        ProjFitnessWorkoutDetailVO detailVO = projFitnessWorkoutService.getWorkoutDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessWorkoutService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessWorkoutService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessWorkoutService.deleteByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量生成视频/音频m3u8文件")
    @PostMapping("/m3u8/generate")
    public ResponseResult<Boolean> generateM3u8(@RequestBody ProjFitnessWorkoutGenerateM3u8Req m3u8Req) {
        if (ObjUtil.equal(Boolean.FALSE, m3u8Req.getVideoFlag()) && ObjUtil.equal(Boolean.FALSE, m3u8Req.getAudioFlag())) {
            return fail("please select file type");
        }
        if (ObjUtil.equal(Boolean.TRUE, m3u8Req.getAudioFlag()) && CollUtil.isEmpty(m3u8Req.getLanguages())) {
            return fail("please select audio language");
        }
        if (CollectionUtil.isEmpty(m3u8Req.getWorkoutIds())) {
            return fail("please select workout");
        }

        return succ(projFitnessWorkoutService.generateM3u8(m3u8Req));
    }

}
