package com.laien.web.biz.proj.oog104.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.web.biz.proj.oog104.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "workout video 详情", description = "workout video 详情")
public class ProjFitnessWorkoutDetailVideoVO {

    @ApiModelProperty(value = "关联表id")
    private Integer relationId;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "类型")
    private List<TypeEnums> type;

    @ApiModelProperty(value = "难度")
    private DifficultyEnums difficulty;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "部位")
    private PositionEnums position;

    @ApiModelProperty(value = "类型")
    private List<FitTypeEnum> fitType;

    @ApiModelProperty(value = "目标c")
    private List<TargetEnums> target;

    @ApiModelProperty(value = "器械")
    private List<EquipmentEnums> equipment;

    @ApiModelProperty(value = "特殊限制")
    private List<SpecialLimitEnums> specialLimits;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "说明")
    private String instructions;

    @ApiModelProperty(value = "名称音频地址")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer instructionsAudioDuration;

    @ApiModelProperty(value = "met")
    private Integer met;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @JsonIgnore
    @ApiModelProperty(value = "front 2532对应的m3u8内容")
    private String frontM3u8Text2532;

    @JsonIgnore
    @ApiModelProperty(value = "front 2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @JsonIgnore
    @ApiModelProperty(value = "front 1080对应的m3u8内容")
    private String frontM3u8Text1080p;

    @JsonIgnore
    @ApiModelProperty(value = "front 720对应的m3u8内容")
    private String frontM3u8Text720p;

    @JsonIgnore
    @ApiModelProperty(value = "front 480对应的m3u8内容")
    private String frontM3u8Text480p;

    @JsonIgnore
    @ApiModelProperty(value = "front 360对应的m3u8内容")
    private String frontM3u8Text360p;

    @JsonIgnore
    @ApiModelProperty(value = "side 2532对应的m3u8内容")
    private String sideM3u8Text2532;

    @JsonIgnore
    @ApiModelProperty(value = "side 2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @JsonIgnore
    @ApiModelProperty(value = "side 1080对应的m3u8内容")
    private String sideM3u8Text1080p;

    @JsonIgnore
    @ApiModelProperty(value = "side 720对应的m3u8内容")
    private String sideM3u8Text720p;

    @JsonIgnore
    @ApiModelProperty(value = "side 480对应的m3u8内容")
    private String sideM3u8Text480p;

    @JsonIgnore
    @ApiModelProperty(value = "side 360对应的m3u8内容")
    private String sideM3u8Text360p;

    @JsonIgnore
    @ApiModelProperty(value = "coreVoiceConfigI18nId",hidden = true)
    private Integer coreVoiceConfigI18nId;

}
