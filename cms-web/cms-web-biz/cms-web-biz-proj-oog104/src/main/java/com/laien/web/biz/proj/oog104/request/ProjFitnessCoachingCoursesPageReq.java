package com.laien.web.biz.proj.oog104.request;


import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * ProjFitnessCoachingCoursesPageReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@ApiModel(value = "ProjFitnessCoachingCourses列表筛选条件", description = "ProjFitnessCoachingCourses列表筛选条件")
public class ProjFitnessCoachingCoursesPageReq extends PageReq {

    @ApiModelProperty(value = "Courses Id 集合",hidden = true)
    private Collection<Integer> ids;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "ageGroups")
    private List<ManualAgeGroupEnums> ageGroups;

    @ApiModelProperty(value = "types")
    private List<FitnessCourseTypeEnums> types;

    @ApiModelProperty(value = "difficulty")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;
}
