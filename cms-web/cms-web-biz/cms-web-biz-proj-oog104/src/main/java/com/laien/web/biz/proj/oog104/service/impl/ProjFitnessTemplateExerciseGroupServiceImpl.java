package com.laien.web.biz.proj.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateExerciseGroup;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessTemplateExerciseGroupMapper;
import com.laien.web.biz.proj.oog104.service.ProjFitnessTemplateExerciseGroupService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_fitness_template_exercise_group(proj_fitness_template_exercise_group)】的数据库操作Service实现
* @createDate 2025-03-11 17:01:12
*/
@Service
public class ProjFitnessTemplateExerciseGroupServiceImpl extends ServiceImpl<ProjFitnessTemplateExerciseGroupMapper, ProjFitnessTemplateExerciseGroup>
    implements ProjFitnessTemplateExerciseGroupService{

    @Override
    public List<ProjFitnessTemplateExerciseGroup> listByTemplateIds(Collection<Integer> templateIds) {

        LambdaQueryWrapper<ProjFitnessTemplateExerciseGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessTemplateExerciseGroup::getProjFitnessTemplateId, templateIds);
        queryWrapper.eq(ProjFitnessTemplateExerciseGroup::getDelFlag, GlobalConstant.NO);
        return list(queryWrapper);
    }
}




