package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessSound;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessSoundMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessSoundMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessSoundService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessSoundServiceImpl extends ServiceImpl<ProjFitnessSoundMapper, ProjFitnessSound>
        implements IProjFitnessSoundService {
    private final ProjFitnessSoundMapStruct mapStruct;
    private final ICoreVoiceConfigI18nService i18nConfigService;
    private final ICoreTextTaskI18nService i18nService;
    private final IProjInfoService projInfoService;


    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    @Override
    public PageRes<ProjFitnessSoundPageVO> selectSoundPage(ProjFitnessSoundPageReq req) {
        LambdaQueryWrapper<ProjFitnessSound> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(req.getId()), ProjFitnessSound::getId, req.getId())
                .like(StrUtil.isNotBlank(req.getSoundName()), ProjFitnessSound::getSoundName, req.getSoundName())
                .eq(ObjUtil.isNotNull(req.getStatus()), ProjFitnessSound::getStatus, req.getStatus())
                .eq(ObjUtil.isNotNull(req.getSoundType()), ProjFitnessSound::getSoundType, req.getSoundType())
                .eq(ObjUtil.isNotNull(req.getSoundSubType()), ProjFitnessSound::getSoundSubType, req.getSoundSubType())
                .eq(ObjUtil.isNotNull(req.getGender()), ProjFitnessSound::getGender, req.getGender())
                .in(CollUtil.isNotEmpty(req.getIds()), ProjFitnessSound::getId, req.getIds());
        query.orderByDesc(ProjFitnessSound::getId);
        IPage<ProjFitnessSound> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), query);
        List<ProjFitnessSoundPageVO> list = mapStruct.toPageList(page.getRecords());
        fillI18nConfigInfo(page.getRecords(),list);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    private void fillI18nConfigInfo(List<ProjFitnessSound> records, List<ProjFitnessSoundPageVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjFitnessSoundPageVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private void fillI18nConfigNameBySound(ProjFitnessSoundDetailVO detailVO) {
        Set<Integer> configIds = new HashSet<>();
        configIds.add(detailVO.getCoreVoiceConfigI18nId());
        Map<Integer, CoreVoiceConfigI18n> configMap = i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));

        CoreVoiceConfigI18n config = configMap.get(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }


    private Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ProjFitnessSound> records) {
        Set<Integer> configIds = records.stream().map(ProjFitnessSound::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }

    /**
     * 添加 sound
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveSound(ProjFitnessSoundAddReq req) {
        check(null, req);
        ProjFitnessSound projFitnessSound = mapStruct.toEntity(req);
        projFitnessSound.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(projFitnessSound);
        if (projFitnessSound.getNeedTranslation()) {
            handleI18n(ListUtil.of(projFitnessSound), projInfoService.getById(req.getProjId()));
        }
    }
    /**
     * 修改 sound
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSound(ProjFitnessSoundUpdateReq req) {
        check(req.getId(), req);
        ProjFitnessSound projSevenmSound = mapStruct.toEntity(req);
        this.updateById(projSevenmSound);
        if (projSevenmSound.getNeedTranslation()) {
            handleI18n(ListUtil.of(projSevenmSound), projInfoService.getById(req.getProjId()));
        }
    }

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    @Override
    public ProjFitnessSoundDetailVO getDetail(Integer id) {
        ProjFitnessSound sound = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(sound),"Data not found");
        ProjFitnessSoundDetailVO detailVO = mapStruct.toDetailVO(sound);
        fillI18nConfigNameBySound(detailVO);
        return detailVO;
    }
    /**
     * 批量启用
     * @param idList
     */
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessSound::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessSound::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjFitnessSound::getId, idList);
       this.update(new ProjFitnessSound(), wrapper);
    }

    /**
     * 批量禁用
     * @param idList
     */
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessSound::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessSound::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessSound::getId, idList);
        this.update(new ProjFitnessSound(), wrapper);
    }

    private void check(Integer id, ProjFitnessSoundAddReq req) {
        LambdaQueryWrapper<ProjFitnessSound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessSound::getSoundName, req.getSoundName())
                .eq(ProjFitnessSound::getProjId, req.getProjId())
                .ne(null != id, BaseModel::getId, id);
        List<ProjFitnessSound> soundList = baseMapper.selectList(wrapper);
        Set<String> nameSet = soundList.stream().map(ProjFitnessSound::getSoundName).collect(Collectors.toSet());
        if (nameSet.contains(req.getSoundName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
    }


    private void handleI18n(List<? extends CoreI18nModel> i18nList, ProjInfo projInfo) {
        CreateTaskDTO createTaskDTO = new CreateTaskDTO(i18nList, projInfo.getTextLanguages(), projInfo.getLanguages(), projInfo.getAppCode());
        try {
            i18nService.batchSaveOrUpdate(createTaskDTO);
        } catch (Exception e) {
            log.error("handleI18n failed, createTaskDTO:{}", createTaskDTO, e);
            throw new BizException("handleI18n failed");
        }
    }


}
