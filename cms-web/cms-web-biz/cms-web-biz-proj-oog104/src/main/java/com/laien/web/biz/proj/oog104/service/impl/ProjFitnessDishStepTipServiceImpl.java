package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishStepTip;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessDishStepTipMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessDishMapStruct;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishStepTipVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishStepTipService;
import com.laien.web.frame.entity.BaseModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Fitness Dish Step Tip 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@RequiredArgsConstructor
public class ProjFitnessDishStepTipServiceImpl extends ServiceImpl<ProjFitnessDishStepTipMapper, ProjFitnessDishStepTip> implements IProjFitnessDishStepTipService {

    private final ProjFitnessDishMapStruct mapStruct;
    @Override
    public void delete(Collection<Integer> dishIdCollection) {
        LambdaUpdateWrapper<ProjFitnessDishStepTip> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjFitnessDishStepTip::getProjFitnessDishId, dishIdCollection);
        baseMapper.delete(wrapper);
    }

    @Override
    public List<ProjFitnessDishStepTipVO> query(Integer dishId) {
        LambdaQueryWrapper<ProjFitnessDishStepTip> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessDishStepTip::getProjFitnessDishId, dishId)
                .orderByAsc(BaseModel::getId);
        List<ProjFitnessDishStepTip> tipList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(tipList)){
            return new ArrayList<>();
        }
        return mapStruct.toStepTipVOList(tipList);
    }
}
