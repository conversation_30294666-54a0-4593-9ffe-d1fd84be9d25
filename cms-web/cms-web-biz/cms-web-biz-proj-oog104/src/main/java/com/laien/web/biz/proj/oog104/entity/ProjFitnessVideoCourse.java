package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.course.FitnessCoursePlayTypeEnums;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.web.common.m3u8.seq.annotation.ResourceSection;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *  ProjFitnessVideoCourse
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "proj_fitness_video_course",autoResultMap = true)
@ApiModel(value = "ProjFitnessVideoCourse对象", description = "ProjFitnessVideoCourse")
public class ProjFitnessVideoCourse extends BaseModel implements CoreI18nModel {

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_VIDEO_COURSE;

    @ApiModelProperty(value = "name")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "playType")
    private FitnessCoursePlayTypeEnums playType;

    @ApiModelProperty(value = "type")
    private FitnessCourseTypeEnums type;

    @ApiModelProperty(value = "courseType")
    private ManualDifficultyEnums difficulty;

    @ResourceSection(
            tableName = "proj_fitness_video_course",
            m3u8UrlColumn = "video_url",
            m3u8Url2532Column = "video2532_url",
            durationColum = "duration",
            dirKey = "project-fitness-video-course-m3u8"
    )
    @ApiModelProperty(value = "源视频地址")
    private String resourceVideoUrl;

    @ApiModelProperty(value = "多分辨率m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "2532 的m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "视频时长（毫秒）")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;
}
