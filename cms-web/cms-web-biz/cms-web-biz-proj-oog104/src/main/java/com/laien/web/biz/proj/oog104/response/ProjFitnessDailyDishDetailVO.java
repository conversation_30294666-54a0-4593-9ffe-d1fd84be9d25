package com.laien.web.biz.proj.oog104.response;

import com.laien.common.oog104.enums.dish.FitnessDishTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/12/31 16:12
 */
@Data
public class ProjFitnessDailyDishDetailVO extends ProjFitnessDishListVO{

    @ApiModelProperty(value = "表示一个MealPlan中第几天")
    private Integer day;

    @ApiModelProperty(value = "表示一个Dish在当前的类型")
    private FitnessDishTypeEnums dishType;

}
