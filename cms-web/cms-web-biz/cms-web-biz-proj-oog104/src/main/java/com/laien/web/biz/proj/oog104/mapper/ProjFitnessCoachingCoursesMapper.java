package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoachingCourses;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * ProjFitnessCoachingCoursesMapper
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
public interface ProjFitnessCoachingCoursesMapper extends BaseMapper<ProjFitnessCoachingCourses> {

    /**
     * @param ids ids
     * @return List<IdAndStatusCountsRes>
     */
    @Select("<script>" +
            "SELECT " +
            "  cc.id as id, " +
            "  COUNT(DISTINCT vc.id) as counts, " +
            "  vc.status as status " +
            "FROM proj_fitness_coaching_courses cc " +
            "LEFT JOIN proj_fitness_coaching_courses_relation ccr ON cc.id = ccr.proj_fitness_coaching_courses_id " +
            "LEFT JOIN proj_fitness_video_course vc ON ccr.proj_fitness_video_course_id = vc.id " +
            "WHERE cc.id IN " +
            "  <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>#{id}</foreach> " +
            "GROUP BY cc.id, vc.status" +
            "</script>")
    List<IdAndStatusCountsRes> countVideoCourseByIds(@Param("ids") List<Integer> ids);
}
