package com.laien.web.biz.proj.oog104.enums;

import com.baomidou.mybatisplus.core.toolkit.StringPool;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface IEnumBase {

   Integer getCode();

   String getName();

   String getDisplayName();

   String getEnumName();

   /**
    * 将枚举列表中的code转换为字符串并用逗号分隔连接起来，通过消费者接口进行后续处理
    *
    * @param consumer 消费者接口，用于处理转换后的字符串
    * @param enumList 枚举列表
    * @param <T> 实现IEnumBase的枚举类型
    */
   static <T extends IEnumBase> void setCodeString(Consumer<String> consumer, List<T> enumList) {
      Optional<List<T>> optional = Optional.ofNullable(enumList);
      if (optional.isPresent()) {
         List<String> codeList = enumList.stream().filter(Objects::nonNull)
                 .map(o -> o.getCode().toString()).collect(Collectors.toList());
         String join = String.join(StringPool.COMMA, codeList);
         consumer.accept(join);
      } else {
         consumer.accept(StringPool.EMPTY);
      }
   }

   /**
    * 将枚举中转换为字符串，通过消费者接口进行后续处理
    *
    * @param consumer 消费者接口，用于处理转换后的字符串
    * @param enums 枚举
    * @param <T> 实现IEnumBase的枚举类型
    */
   static <T extends IEnumBase> void setCodeString(Consumer<String> consumer, T enums) {
      Optional<T> optional = Optional.ofNullable(enums);
      if (optional.isPresent()) {
         consumer.accept(optional.get().getCode().toString());
      } else {
         consumer.accept(StringPool.EMPTY);
      }
   }

   /**
    * 将枚举列表中的code提取到一个code列表中，通过消费者接口进行后续处理
    *
    * @param consumer 消费者接口，用于处理转换后的code列表
    * @param enumList 枚举列表
    * @param <T> 实现IEnumBase的枚举类型
    */
   static <T extends IEnumBase> void setCodeInteger(Consumer<List<Integer>> consumer, List<T> enumList) {
      Optional<List<T>> optional = Optional.ofNullable(enumList);
      if (optional.isPresent()) {
         List<Integer> codeList = enumList.stream().filter(Objects::nonNull)
                 .map(IEnumBase::getCode).collect(Collectors.toList());
         consumer.accept(codeList);
      } else {
         consumer.accept(new ArrayList<>());
      }
   }

   /**
    * 将枚举转换为code，通过消费者接口进行后续处理
    *
    * @param consumer 消费者接口，用于处理转换后的code
    * @param enums 枚举
    * @param <T> 实现IEnumBase的枚举类型
    */
   static <T extends IEnumBase> void setCodeInteger(Consumer<Integer> consumer, T enums) {
      Optional.ofNullable(enums).ifPresent(o -> consumer.accept(o.getCode()));
   }

   /**
    * 根据枚举字符串设置枚举列表
    *
    * @param consumer 消费者函数，用于处理转换后的枚举列表
    * @param enumString 包含枚举code的字符串，code之间用逗号分隔
    * @param getByFunction 根据code获取枚举对象的函数
    * @param <T> 实现IEnumBase的枚举类型
    */
   static <T extends IEnumBase> void setEnumList(Consumer<List<T>> consumer, String enumString, Function<Integer, T> getByFunction) {
      List<T> enumList = new ArrayList<>();
      if (enumString != null && !enumString.isEmpty()) {
         String[] split = enumString.split(StringPool.COMMA);
         for (String string : split) {
            Integer code = Integer.valueOf(string);
            T t = getByFunction.apply(code);
            Optional.ofNullable(t).ifPresent(enumList::add);
         }
      }
      consumer.accept(enumList);
   }

   /**
    * 根据枚举code列表设置枚举列表
    *
    * @param consumer 消费者函数，用于处理转换后的枚举列表
    * @param enumCodeList 包含枚举code的字符串，code之间用逗号分隔
    * @param getByFunction 根据code获取枚举对象的函数
    * @param <T> 实现IEnumBase的枚举类型
    */
   static <T extends IEnumBase> void setEnumList(Consumer<List<T>> consumer, List<Integer> enumCodeList, Function<Integer, T> getByFunction) {
      List<T> enumList = new ArrayList<>();
      if (enumCodeList != null && !enumCodeList.isEmpty()) {
         for (Integer code : enumCodeList) {
            T t = getByFunction.apply(code);
            Optional.ofNullable(t).ifPresent(enumList::add);
         }
      }
      consumer.accept(enumList);
   }

   /**
    * 根据枚举code设置枚举
    *
    * @param consumer 消费者函数，用于处理转换后的枚举
    * @param code 枚举code
    * @param getByFunction 根据code获取枚举对象的函数
    * @param <T> 实现IEnumBase的枚举类型
    */
   static <T extends IEnumBase> void setEnum(Consumer<T> consumer, Integer code, Function<Integer, T> getByFunction) {
      T t = getByFunction.apply(code);
      Optional.ofNullable(t).ifPresent(consumer);
   }

   /**
    * 通过 code 获取对应的 name
    *
    * @param code 枚举 code
    * @param getByFunction 根据 code 获取枚举对象的函数
    * @param <T> 实现 IEnumBase 的枚举类型
    * @return 对应的 name，找不到则返回 null
    */
   static <T extends IEnumBase> String getNameByCode(Integer code, Function<Integer, T> getByFunction) {
      return Optional.ofNullable(getByFunction.apply(code))
              .map(T::getName)
              .orElse(null);
   }

   /**
    * 通过 code 设置对应的 name
    *
    * @param consumer 用于接收 name 的 Consumer
    * @param code 枚举 code
    * @param getByFunction 根据 code 获取枚举对象的函数
    * @param <T> 实现 IEnumBase 的枚举类型
    */
   static <T extends IEnumBase> void setNameByCode(Consumer<String> consumer, Integer code, Function<Integer, T> getByFunction) {
      consumer.accept(getNameByCode(code, getByFunction));
   }

   /**
    * 将 code 逗号分隔的字符串转换为 name 逗号分隔的字符串
    *
    * @param codeString 包含枚举 code 的字符串，code 之间用逗号分隔
    * @param getByFunction 根据 code 获取枚举对象的函数
    * @param <T> 实现 IEnumBase 的枚举类型
    * @return 逗号分隔的 name 字符串，找不到的 code 将被忽略
    */
   static <T extends IEnumBase> String convertCodeStringToNameString(String codeString, Function<Integer, T> getByFunction) {
      if (codeString == null || codeString.trim().isEmpty()) {
         return "";
      }

      return Arrays.stream(codeString.split(StringPool.COMMA))
              .map(String::trim)
              .map(code -> {
                 try {
                    return Integer.valueOf(code);
                 } catch (NumberFormatException e) {
                    return null; // 解析失败，跳过该 code
                 }
              })
              .filter(Objects::nonNull)
              .map(code -> getNameByCode(code, getByFunction))
              .filter(Objects::nonNull)
              .collect(Collectors.joining(StringPool.COMMA));
   }

   /**
    * 通过 code 逗号分隔字符串设置 name 逗号分隔字符串
    *
    * @param consumer 用于接收 name 字符串的 Consumer
    * @param codeString 包含 code 的字符串
    * @param getByFunction 根据 code 获取枚举对象的函数
    * @param <T> 实现 IEnumBase 的枚举类型
    */
   static <T extends IEnumBase> void setNameStringByCodeString(Consumer<String> consumer, String codeString, Function<Integer, T> getByFunction) {
      consumer.accept(convertCodeStringToNameString(codeString, getByFunction));
   }

}
