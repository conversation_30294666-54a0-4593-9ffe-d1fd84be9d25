package com.laien.web.biz.proj.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessDishStepTipVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@Accessors(chain = true)
@ApiModel(value="dish step tip对象", description="dish step tip")
public class ProjFitnessDishStepTipVO {

    @ApiModelProperty(value = "proj_fitness_dish表数据id")
    private Integer projFitnessDishId;

    @ApiModelProperty(value = "proj_fitness_dish_step表数据id")
    private Integer projFitnessDishStepId;

    @ApiModelProperty(value = "图片路径")
    private String imgUrl;

    @ApiModelProperty(value = "介绍")
    private String intro;

}
