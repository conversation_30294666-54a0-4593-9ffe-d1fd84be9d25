package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *ProjFitnessDishUpdateReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjFitnessDishUpdateReq extends ProjFitnessDishAddReq{

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
