package com.laien.web.biz.proj.oog104.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplateExerciseGroupDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>生成workout参数</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 17:42
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "修改 template 参数", description = "修改 template 参数")
public class ProjFitnessTemplateUpdateReq {

    @JsonIgnore
    private Integer projId;

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty
    private TemplateTypeEnums templateType;

    @ApiModelProperty
    private WorkoutDurationRangeEnums durationRange;

    @ApiModelProperty("生成多少天的天数")
    private Integer days;

    @ApiModelProperty
    private ManualDifficultyEnums level;

    @ApiModelProperty
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

    @ApiModelProperty("warm up动作组")
    private ProjFitnessTemplateExerciseGroupDetailVO warmupExerciseGroup;

    @ApiModelProperty("main动作组")
    private ProjFitnessTemplateExerciseGroupDetailVO mainExerciseGroup;

    @ApiModelProperty("cool down动作组")
    private ProjFitnessTemplateExerciseGroupDetailVO coolDownExerciseGroup;

}
