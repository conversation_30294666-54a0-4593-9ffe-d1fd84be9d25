package com.laien.web.biz.proj.oog104.response;

import com.laien.web.biz.proj.oog104.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "video 详情", description = "video 详情")
public class ProjFitnessVideoDetailVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "类型")
    private List<TypeEnums> type;

    @ApiModelProperty(value = "难度")
    private DifficultyEnums difficulty;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "部位")
    private PositionEnums position;

    @ApiModelProperty(value = "类型")
    private List<FitTypeEnum> fitType;

    @ApiModelProperty(value = "目标c")
    private List<TargetEnums> target;

    @ApiModelProperty(value = "器械")
    private List<EquipmentEnums> equipment;

    @ApiModelProperty(value = "特殊限制列表")
    private List<SpecialLimitEnums> specialLimits;

    @ApiModelProperty(value = "正机位mp4视频地址")
    private String frontVideoMp4Url;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧机位mp4视频地址")
    private String sideVideoMp4Url;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "说明")
    private String instructions;

    @ApiModelProperty(value = "名称音频地址")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer instructionsAudioDuration;

    @ApiModelProperty(value = "如何做")
    private String howToDo;

    @ApiModelProperty(value = "如何做音频地址")
    private String howToDoAudioUrl;

    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ApiModelProperty(value = "met")
    private Integer met;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "coreVoiceConfigI18nName")
    private String coreVoiceConfigI18nName;

}
