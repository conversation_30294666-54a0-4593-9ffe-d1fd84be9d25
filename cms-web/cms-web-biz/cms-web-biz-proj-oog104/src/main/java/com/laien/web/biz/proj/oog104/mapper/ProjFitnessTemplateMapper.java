package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplate;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplatePageReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【proj_fitness_template(proj_fitness_template)】的数据库操作Mapper
 * @createDate 2025-03-11 17:01:12
 * @Entity com.laien.common.oog104.enums.entity.ProjFitnessTemplate
 */
public interface ProjFitnessTemplateMapper extends BaseMapper<ProjFitnessTemplate> {

    int insertBatchSomeColumn(@Param("list") List<ProjFitnessTemplate> batchList);

    IPage<ProjFitnessTemplate> pageWithTask(@Param("page") IPage<ProjFitnessTemplate> page,@Param("param") ProjFitnessTemplatePageReq param);
}




