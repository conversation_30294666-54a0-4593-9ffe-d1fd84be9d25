package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *  ProjFitnessWorkoutGenerateUpdateReq
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessWorkoutGenerateUpdateReq", description="ProjFitnessWorkoutGenerateUpdateReq")
public class ProjFitnessWorkoutGenerateUpdateReq extends  ProjFitnessWorkoutGenerateAddReq{

    private static final long serialVersionUID = -1108137034718062943L;

    @ApiModelProperty(value = "id")
    private Integer id;
}
