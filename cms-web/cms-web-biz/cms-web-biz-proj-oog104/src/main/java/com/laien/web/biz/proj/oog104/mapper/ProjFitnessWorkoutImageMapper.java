package com.laien.web.biz.proj.oog104.mapper;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutImage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【proj_fitness_workout_image(proj_fitness_workout_image)】的数据库操作Mapper
* @createDate 2025-03-17 17:33:55
* @Entity com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutImage
*/
public interface ProjFitnessWorkoutImageMapper extends BaseMapper<ProjFitnessWorkoutImage> {

    @Select("select max(sort_no) from proj_fitness_workout_image")
    Integer selectMaxSortNo();
}




