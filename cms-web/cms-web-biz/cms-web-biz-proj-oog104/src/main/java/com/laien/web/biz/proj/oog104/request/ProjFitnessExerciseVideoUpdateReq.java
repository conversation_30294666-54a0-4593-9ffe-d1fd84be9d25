package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessExerciseVideo 编辑
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessExerciseVideo 编辑", description = "ProjFitnessExerciseVideo 编辑")
public class ProjFitnessExerciseVideoUpdateReq  extends ProjFitnessExerciseVideoAddReq{

    @ApiModelProperty(value = "id")
    private Integer id;
}
