package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishStep;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishStepTip;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessDishStepMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessDishMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishStepReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishStepTipReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishStepTipVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishStepVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishStepService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishStepTipService;
import com.laien.web.frame.entity.BaseModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Fitness Dish Step 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@RequiredArgsConstructor
public class ProjFitnessDishStepServiceImpl extends ServiceImpl<ProjFitnessDishStepMapper, ProjFitnessDishStep> implements IProjFitnessDishStepService {

    private final IProjFitnessDishStepTipService tipService;
    private final ProjFitnessDishMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(Integer dishId, List<ProjFitnessDishStepReq> dishStepList, Integer projId) {
        deleteBatch(Collections.singletonList(dishId));
        if (CollUtil.isEmpty(dishStepList)) {
            return;
        }
        Map<ProjFitnessDishStep, ProjFitnessDishStepReq> stepMap = new LinkedHashMap<>();
        for (ProjFitnessDishStepReq stepReq : dishStepList) {
            ProjFitnessDishStep step = mapStruct.toStepEntity(stepReq);
            step.setProjFitnessDishId(dishId)
                    .setProjId(projId);
            stepMap.put(step, stepReq);
        }
        saveBatch(stepMap.keySet());
        projLmsI18nService.handleI18n(CollUtil.newArrayList(stepMap.keySet()), projId);

        List<ProjFitnessDishStepTip> stepTipList = new ArrayList<>();

        for (Map.Entry<ProjFitnessDishStep, ProjFitnessDishStepReq> entry : stepMap.entrySet()) {
            ProjFitnessDishStep step = entry.getKey();
            ProjFitnessDishStepReq stepReq = entry.getValue();
            List<ProjFitnessDishStepTipReq> tipList = stepReq.getTipList();
            if (CollUtil.isEmpty(tipList)) {
                continue;
            }
            for (ProjFitnessDishStepTipReq tipReq : tipList) {
                ProjFitnessDishStepTip stepTip = mapStruct.toStepTipEntity(tipReq);
                stepTip.setProjFitnessDishId(dishId)
                        .setProjFitnessDishStepId(step.getId())
                        .setProjId(projId);
                stepTipList.add(stepTip);
            }
        }
        if (CollUtil.isNotEmpty(stepTipList)) {
            tipService.saveBatch(stepTipList);
            projLmsI18nService.handleI18n(stepTipList, projId);
        }
    }

    @Override
    public List<ProjFitnessDishStepVO> query(Integer dishId) {
        LambdaQueryWrapper<ProjFitnessDishStep> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessDishStep::getProjFitnessDishId, dishId).orderByAsc(BaseModel::getId);
        List<ProjFitnessDishStep> stepList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(stepList)) {
            return new ArrayList<>();
        }
        List<ProjFitnessDishStepVO> stepVOList = new ArrayList<>(stepList.size());
        Map<Integer, List<ProjFitnessDishStepTipVO>> tipMap = tipService.query(dishId).stream().collect(Collectors.groupingBy(ProjFitnessDishStepTipVO::getProjFitnessDishStepId));
        for (ProjFitnessDishStep step : stepList) {
            ProjFitnessDishStepVO stepVO = mapStruct.toStepVO(step);
            List<ProjFitnessDishStepTipVO> tipList = tipMap.getOrDefault(step.getId(), new ArrayList<>());
            stepVO.setTipList(tipList);
            stepVOList.add(stepVO);
        }
        return stepVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBatch(Collection<Integer> dishIdCollection) {
        if(CollUtil.isEmpty(dishIdCollection)){
            return;
        }
        LambdaUpdateWrapper<ProjFitnessDishStep> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjFitnessDishStep::getProjFitnessDishId, dishIdCollection);
        baseMapper.delete(wrapper);
        tipService.delete(dishIdCollection);
    }
}
