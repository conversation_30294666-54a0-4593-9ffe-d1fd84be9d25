package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishStep;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishStepReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishStepVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Fitness Dish Step 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
public interface IProjFitnessDishStepService extends IService<ProjFitnessDishStep> {

    void saveBatch(Integer dishId, List<ProjFitnessDishStepReq> dishStepList, Integer projId);

    List<ProjFitnessDishStepVO> query(Integer dishId);

    void deleteBatch(Collection<Integer> dishIdCollection);
}
