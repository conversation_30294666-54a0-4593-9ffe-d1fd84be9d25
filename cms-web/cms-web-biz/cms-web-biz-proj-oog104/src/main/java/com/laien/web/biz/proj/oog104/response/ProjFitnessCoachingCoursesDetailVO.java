package com.laien.web.biz.proj.oog104.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Dish
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjFitnessCoachingCoursesDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "Home 竖版Image")
    private String courseImgUrl;

    @ApiModelProperty(value = "ageGroups")
    @TableField(typeHandler = ManualAgeGroupEnums.TypeHandler.class)
    private List<ManualAgeGroupEnums> ageGroups;

    @ApiModelProperty(value = "types")
    @TableField(typeHandler = FitnessCourseTypeEnums.TypeHandler.class)
    private List<FitnessCourseTypeEnums> types;

    @ApiModelProperty(value = "difficulty")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "projFitnessCoachId")
    private Integer projFitnessCoachId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    private List<ProjFitnessVideoCourseListVO> videoCourseList;
}
