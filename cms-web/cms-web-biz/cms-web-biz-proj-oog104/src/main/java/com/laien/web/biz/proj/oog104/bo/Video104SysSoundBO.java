package com.laien.web.biz.proj.oog104.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "video104 生成系统音配置", description = "video104 生成系统音配置")
public class Video104SysSoundBO {

    @ApiModelProperty(value = "first")
    private String first;
    @ApiModelProperty(value = "threeTwoOne")
    private String threeTwoOne;
    @ApiModelProperty(value = "go")
    private String go;
    @ApiModelProperty(value = "readyFor")
    private String readyFor;
    @ApiModelProperty(value = "last")
    private String last;
    @ApiModelProperty(value = "endThreeTwoOne")
    private String endThreeTwoOne;

    @ApiModelProperty(value = "halfWayList")
    private List<String> halfWayList;

}
