package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p> Fitness Allergen </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_fitness_allergen")
@ApiModel(value="ProjFitnessAllergen对象", description="Fitness Allergen")
public class ProjFitnessAllergen extends BaseModel  implements CoreI18nModel {

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_ALLERGEN;

    @ApiModelProperty("过敏源名称")
    @TranslateField
    private String name;

    @ApiModelProperty("项目id")
    private Integer projId;

    @ApiModelProperty("启用状态 0草稿 1启用 2停用")
    private Integer status;
}
