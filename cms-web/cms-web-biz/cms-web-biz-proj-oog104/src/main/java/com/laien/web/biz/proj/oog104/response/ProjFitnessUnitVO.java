package com.laien.web.biz.proj.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessUnitVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessUnit对象", description="unit")
public class ProjFitnessUnitVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "单位名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
