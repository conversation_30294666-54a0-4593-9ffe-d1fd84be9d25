package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

/**
 * 特殊限制枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum SpecialLimitEnums implements IEnumBase {

    NONE(0, "None", "None"),
    PREGNANT(1, "Pregnant", "Pregnant"),
    ELDERLY(2, "Elderly", "Elderly");

    private final Integer code;
    private final String name;
    private final String displayName;

    SpecialLimitEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static SpecialLimitEnums getBy(Integer code) {
        for (SpecialLimitEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
