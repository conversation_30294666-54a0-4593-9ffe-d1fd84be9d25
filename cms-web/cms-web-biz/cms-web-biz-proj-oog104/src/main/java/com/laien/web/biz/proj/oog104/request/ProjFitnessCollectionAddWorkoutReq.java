package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "collection workout 添加", description = "collection workout 添加")
public class ProjFitnessCollectionAddWorkoutReq {

    @ApiModelProperty(value = "workout id")
    private Integer id;
}
