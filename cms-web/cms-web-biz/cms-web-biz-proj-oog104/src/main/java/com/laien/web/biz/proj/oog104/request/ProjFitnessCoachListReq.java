package com.laien.web.biz.proj.oog104.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * ProjFitnessCoachListReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@ApiModel(value = "ProjFitnessCoachListReq", description = "ProjFitnessCoachListReq")
public class ProjFitnessCoachListReq {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

}
