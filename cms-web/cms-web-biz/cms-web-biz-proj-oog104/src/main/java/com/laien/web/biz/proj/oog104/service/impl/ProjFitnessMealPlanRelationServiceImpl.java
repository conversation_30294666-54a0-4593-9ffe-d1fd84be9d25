package com.laien.web.biz.proj.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessMealPlanRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessMealPlanRelationMapper;
import com.laien.web.biz.proj.oog104.service.IProjFitnessMealPlanRelationService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/31 18:11
 */
@Slf4j
@Service
public class ProjFitnessMealPlanRelationServiceImpl extends ServiceImpl<ProjFitnessMealPlanRelationMapper, ProjFitnessMealPlanRelation> implements IProjFitnessMealPlanRelationService {

    @Override
    public void deleteByMealPlanId(Integer mealPlanId) {

        LambdaUpdateWrapper<ProjFitnessMealPlanRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjFitnessMealPlanRelation::getProjFitnessMealPlanId, mealPlanId);
        updateWrapper.set(ProjFitnessMealPlanRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjFitnessMealPlanRelation::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public List<ProjFitnessMealPlanRelation> listByMealPlanId(Integer mealPlanId) {

        LambdaQueryWrapper<ProjFitnessMealPlanRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessMealPlanRelation::getProjFitnessMealPlanId, mealPlanId);
        return list(queryWrapper);
    }

}
