package com.laien.web.biz.proj.oog104.entity.i18n;

import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutDetailVideoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value="ProjFitnessVideo多语言", description="ProjFitnessVideo多语言")
public class ProjFitnessVideoI18n extends BaseAudioI18nModel {


    @ApiModelProperty(value = "声音脚本")
    @AppAudioTranslateField(resultFieldName = "result")
    private String name;

    @AppAudioTranslateField(resultFieldName = "instructionsResult")
    private String instructions;

    private List<AudioTranslateResultModel> instructionsResult;

    public ProjFitnessVideoI18n(ProjFitnessWorkoutDetailVideoVO video) {
        super.setUniqueKey(video.getId());
        super.setCoreVoiceConfigI18nId(video.getCoreVoiceConfigI18nId());
        this.name = video.getName();
        this.instructions = video.getInstructions();
    }
}
