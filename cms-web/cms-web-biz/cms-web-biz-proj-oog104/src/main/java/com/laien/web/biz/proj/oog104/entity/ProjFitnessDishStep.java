package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Fitness Dish step
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_fitness_dish_step")
@ApiModel(value="ProjFitnessDishStep对象", description="Fitness Dish step")
public class ProjFitnessDishStep extends BaseModel  implements CoreI18nModel {

    @ApiModelProperty("描述")
    @TranslateField
    private String description;

    @ApiModelProperty("proj_fitness_dish表数据id")
    private Integer projFitnessDishId;

    @ApiModelProperty("项目id")
    private Integer projId;
}
