package com.laien.web.biz.proj.oog104.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 教练表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCoach", description="ProjFitnessCoach表")
public class ProjFitnessCoach extends BaseModel implements CoreI18nModel {

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_COACH;

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    @TranslateField
    private String name;

    @ApiModelProperty(value = "photoImgUrl")
    private String photoImgUrl;

    @ApiModelProperty(value = "introduction")
    @TranslateField
    private String introduction;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;

}
