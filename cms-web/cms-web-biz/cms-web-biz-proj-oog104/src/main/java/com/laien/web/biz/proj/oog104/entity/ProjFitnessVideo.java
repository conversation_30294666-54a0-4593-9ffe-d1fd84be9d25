package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.constant.LmsConstant;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.web.common.m3u8.seq.annotation.ResourceSection;
import com.laien.web.common.m3u8.seq.enums.CompressionTsParamsEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_video
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessVideo对象", description="proj_fitness_video")
public class ProjFitnessVideo extends BaseModel implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId = LmsConstant.DEFAULT_VOICE_CONFIG_ID_FEMALE;

    @ApiModelProperty(value = "动作名称")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "nameAudioUrl",
            durationFieldName = "nameAudioDurationConfig")
    private String name;

    @JsonIgnore
    @TableField(exist = false)
    @ApiModelProperty(value = "名称音频时长配制时长",hidden = true)
    private Integer nameAudioDurationConfig = 4000;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "类型code")
    private String typeCodes;

    @ApiModelProperty(value = "难度code")
    private Integer difficultyCode;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "部位code")
    private Integer positionCode;

    @ApiModelProperty(value = "fit类型code")
    private String fitTypeCodes;

    @ApiModelProperty(value = "目标code")
    private String targetCodes;

    @ApiModelProperty(value = "器械code")
    private String equipmentCodes;

    @Deprecated
    @ApiModelProperty(value = "特殊限制code",notes = "此字段已经废弃，准备删除")
    private Integer specialLimitCode;

    @ApiModelProperty(value = "特殊限制列表code",notes = "此字段用于表示多个specialLimitCode")
    private String specialLimitCodes;

    @ResourceSection(
            tableName = "proj_fitness_video",
            m3u8UrlColumn = "video_dynamic_url",
            compressionTsColumn = "front_video_url",
            compressionM3u8Column = "video_url",
            furtherCompression = true,
            compressionParams = CompressionTsParamsEnums.VIDEO_1POINT5M,
            m3u8Text2532Column = "front_m3u8_text2532",
            m3u8Text2kColumn = "front_m3u8_text2k",
            m3u8Text1080pColumn = "front_m3u8_text1080p",
            m3u8Text720pColumn = "front_m3u8_text720p",
            m3u8Text480pColumn = "front_m3u8_text480p",
            m3u8Text360pColumn = "front_m3u8_text360p",
            durationColum = "front_video_duration",
            dirKey = "project-fitness-workout-m3u8"
    )
    @ApiModelProperty(value = "正机位mp4视频地址")
    private String frontVideoMp4Url;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ResourceSection(
            tableName = "proj_fitness_video",
            compressionTsColumn = "side_video_url",
            furtherCompression = true,
            compressionParams = CompressionTsParamsEnums.VIDEO_1POINT5M,
            m3u8Text2532Column = "side_m3u8_text2532",
            m3u8Text2kColumn = "side_m3u8_text2k",
            m3u8Text1080pColumn = "side_m3u8_text1080p",
            m3u8Text720pColumn = "side_m3u8_text720p",
            m3u8Text480pColumn = "side_m3u8_text480p",
            m3u8Text360pColumn = "side_m3u8_text360p",
            durationColum = "side_video_duration",
            dirKey = "project-fitness-workout-m3u8"
    )
    @ApiModelProperty(value = "侧机位mp4视频地址")
    private String sideVideoMp4Url;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "视频地址(正机位dynamic m3u8)")
    private String videoDynamicUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "说明")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "instructionsAudioUrl")
    private String instructions;

    @ApiModelProperty(value = "名称音频地址")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer instructionsAudioDuration;

    @ApiModelProperty(value = "如何做")
    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "howToDoAudioUrl")
    private String howToDo;

    @ApiModelProperty(value = "如何做音频地址")
    private String howToDoAudioUrl;

    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ApiModelProperty(value = "met")
    private Integer met;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "front 2532对应的m3u8内容")
    private String frontM3u8Text2532;

    @ApiModelProperty(value = "front 2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @ApiModelProperty(value = "front 1080对应的m3u8内容")
    private String frontM3u8Text1080p;

    @ApiModelProperty(value = "front 720对应的m3u8内容")
    private String frontM3u8Text720p;

    @ApiModelProperty(value = "front 480对应的m3u8内容")
    private String frontM3u8Text480p;

    @ApiModelProperty(value = "front 360对应的m3u8内容")
    private String frontM3u8Text360p;

    @ApiModelProperty(value = "side 2532对应的m3u8内容")
    private String sideM3u8Text2532;

    @ApiModelProperty(value = "side 2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @ApiModelProperty(value = "side 1080对应的m3u8内容")
    private String sideM3u8Text1080p;

    @ApiModelProperty(value = "side 720对应的m3u8内容")
    private String sideM3u8Text720p;

    @ApiModelProperty(value = "side 480对应的m3u8内容")
    private String sideM3u8Text480p;

    @ApiModelProperty(value = "side 360对应的m3u8内容")
    private String sideM3u8Text360p;


}
