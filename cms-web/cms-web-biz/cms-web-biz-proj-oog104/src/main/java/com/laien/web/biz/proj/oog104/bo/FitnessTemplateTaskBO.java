package com.laien.web.biz.proj.oog104.bo;

import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplate;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateExerciseGroup;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2025/3/17 17:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FitnessTemplateTaskBO {

    private ProjFitnessTemplateTask templateTask;

    private ProjFitnessTemplate template;

    private Map<ManualTypeEnums, ProjFitnessTemplateExerciseGroup> templateGroupMap;

    private List<String> languageList;

}
