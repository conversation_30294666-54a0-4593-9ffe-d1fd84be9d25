package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerate;
import com.laien.web.biz.proj.oog104.request.ProjFitnessManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGeneratePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGenerateUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGenerateDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGeneratePageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessWorkoutGenerateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Fitness Workout Generate 前端控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Api(tags = "项目管理:Fitness Workout Generate")
@RestController
@RequestMapping("/proj/fitnessWorkoutGenerate")
public class ProjFitnessGenerateController extends ResponseController {

    @Resource
    private IProjFitnessWorkoutGenerateService workoutGenerateService;

    @ApiOperation(value = "Fitness Workout Generate 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessWorkoutGeneratePageVO>> page(ProjFitnessWorkoutGeneratePageReq pageReq) {
        pageReq.setProjId(RequestContextUtils.getProjectId());
        PageRes<ProjFitnessWorkoutGeneratePageVO> pageRes = workoutGenerateService.page(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "Fitness Workout Generate 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessWorkoutGenerateDetailVO> detail(@PathVariable Integer id) {
        return succ(workoutGenerateService.findDetailById(id));
    }

    @ApiOperation(value = "Fitness Workout Generate 分页-workout-video列表")
    @GetMapping("/page/{id}/video")
    public ResponseResult<PageRes<ProjFitnessExerciseVideoPageVO>> pageWorkout(PageReq pageReq, @PathVariable Integer id) {
        return succ(workoutGenerateService.pageVideo(pageReq, id));
    }

    @ApiOperation(value = "Fitness Workout Generate 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessWorkoutGenerateUpdateReq workoutUpdateReq) {
        workoutGenerateService.update(workoutUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }


    @ApiOperation(value = "Fitness Workout Generate 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        workoutGenerateService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Fitness Workout Generate 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        workoutGenerateService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Fitness Workout Generate 批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        workoutGenerateService.deleteByIdList(idList);
        return succ();
    }

    @ApiOperation(value = "Fitness Workout Generate 生成M3U8文件")
    @PostMapping("/generateM3u8")
    public ResponseResult<Boolean> generateM3u8(@RequestBody ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        m3u8Req.setProjId(RequestContextUtils.getProjectId());
        Boolean result = workoutGenerateService.generateM3u8(m3u8Req);
        return succ(result);
    }


    @ApiOperation(value = "Fitness Workout Generate 生成M3U8文件,支持用分页条件")
    @PostMapping("/generateM3u8ByPageReq")
    public ResponseResult<Boolean> generateM3u8ByPageReq(@RequestBody ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        m3u8Req.setProjId(RequestContextUtils.getProjectId());
        Boolean result = workoutGenerateService.generateM3u8ByPageReq(m3u8Req);
        return succ(result);
    }

    @ApiOperation(value = "Fitness Workout Generate 生成M3U8文件,支持用分页条件")
    @PostMapping("/generateM3u8ByQuery")
    public ResponseResult<Integer> generateM3u8ByQuery(@RequestBody ProjFitnessManualWorkoutGenerateM3u8Req m3u8Req) {
        m3u8Req.setProjId(RequestContextUtils.getProjectId());
        return succ(workoutGenerateService.generateM3u8ByQuery(m3u8Req));
    }

    @ApiOperation(value = "Fitness Workout Generate 生成M3U8文件打断")
    @PostMapping("/generateM3u8Interrupt")
    public ResponseResult<Void> generateM3u8Interrupt() {
        workoutGenerateService.generateM3u8Interrupt();
        return succ();
    }
}
