package com.laien.web.biz.proj.oog104.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualEquipmentEnums;
import com.laien.common.oog104.enums.manual.ManualIntensityEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  ProjFitnessWorkoutGenerateAddReq
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessWorkoutGenerateAddReq", description="ProjFitnessWorkoutGenerateAddReq")
public class ProjFitnessWorkoutGenerateAddReq {

    private static final long serialVersionUID = -1108137034718062943L;

    @ApiModelProperty(value = "proj_fitness_template_id")
    private Integer projFitnessTemplateId;

    @ApiModelProperty(value = "proj_fitness_template_task_id")
    private Integer projFitnessTemplateTaskId;

    @ApiModelProperty(value = "target code")
    private ManualTargetEnums target;

    @ApiModelProperty(value = "difficulty code")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "equipment code")
    private ManualEquipmentEnums equipment;

    @ApiModelProperty(value = "特殊限制code (多选, 逗号分隔)")
    @TableField(typeHandler = ExerciseVideoSpecialLimitEnums.TypeHandler.class)
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "组成的Exercise的Intensity包含的合集 , 多个用英文逗号分隔")
    @TableField(typeHandler = ManualIntensityEnums.TypeHandler.class)
    private List<ManualIntensityEnums> intensity;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;

    @ApiModelProperty(value = "videoIdList")
    private List<Integer> videoIdList;
}
