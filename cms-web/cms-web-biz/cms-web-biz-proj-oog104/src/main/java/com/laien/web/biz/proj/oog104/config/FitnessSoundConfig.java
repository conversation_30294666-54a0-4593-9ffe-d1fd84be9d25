package com.laien.web.biz.proj.oog104.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/3/21 17:00
 */
@Data
public class FitnessSoundConfig {

    @ApiModelProperty(value = "first")
    private String first;

    @ApiModelProperty(value = "threeTwoOne")
    private String threeTwoOne;

    @ApiModelProperty(value = "go")
    private String go;

    @ApiModelProperty(value = "readyFor")
    private String readyFor;

    @ApiModelProperty(value = "last")
    private String last;

    @ApiModelProperty(value = "promptList")
    private List<String> promptList;

}
