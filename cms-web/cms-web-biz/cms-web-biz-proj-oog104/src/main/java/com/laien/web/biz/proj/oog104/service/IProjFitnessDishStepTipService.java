package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishStepTip;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishStepTipVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Fitness Dish Step Tip 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
public interface IProjFitnessDishStepTipService extends IService<ProjFitnessDishStepTip> {
    void delete(Collection<Integer> dishIdCollection);

    List<ProjFitnessDishStepTipVO> query(Integer dishId);
}
