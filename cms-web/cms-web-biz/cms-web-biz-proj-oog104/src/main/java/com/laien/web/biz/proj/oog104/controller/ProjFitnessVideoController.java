package com.laien.web.biz.proj.oog104.controller;


import cn.hutool.core.collection.CollUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideo;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessVideoService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * proj_fitness_video 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Api(tags = "项目管理:Fitness Video")
@RestController
@RequestMapping("/proj/fitnessVideo")
public class ProjFitnessVideoController extends ResponseController {

    @Resource
    private IProjFitnessVideoService projFitnessVideoService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessVideoPageVO>> page(ProjFitnessVideoPageReq pageReq) {
        PageRes<ProjFitnessVideoPageVO> pageRes = projFitnessVideoService.selectVideoPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessVideoAddReq videoReq) {
        projFitnessVideoService.saveVideo(videoReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessVideoUpdateReq videoReq) {
        projFitnessVideoService.updateVideo(videoReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessVideoDetailVO> detail(@PathVariable Integer id) {
        ProjFitnessVideoDetailVO detailVO = projFitnessVideoService.getVideoDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        List<Integer> failedIdList = projFitnessVideoService.updateEnableByIds(idList);
        if(CollUtil.isEmpty(failedIdList)){
            return succ();
        }
        return fail("It cannot be enabled because video processing is not complete.failed id list:" + failedIdList);
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessVideoService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        projFitnessVideoService.deleteByIds(idList);
        return succ();
    }

}
