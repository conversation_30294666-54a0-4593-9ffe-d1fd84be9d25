package com.laien.web.biz.proj.oog104.bo;

import com.google.common.collect.Lists;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualIntensityEnums;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2025/3/21 15:26
 */
@Data
public class DifficultyAndIntensityMappingBO {

    private List<ManualDifficultyEnums> difficultyEnums;

    private List<ManualIntensityEnums> intensityEnums;

    private float percent;

    public static List<DifficultyAndIntensityMappingBO> getMappingByDifficulty(ManualDifficultyEnums difficultyEnums) {

        if (Objects.equals(difficultyEnums, ManualDifficultyEnums.NEWBIE)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.8f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.BEGINNER));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(ManualIntensityEnums.STRETCH, ManualIntensityEnums.CARDIO));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.2f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.BEGINNER));
            mappingBO_2.setIntensityEnums(Lists.newArrayList(ManualIntensityEnums.HIIT));

            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }

        if (Objects.equals(difficultyEnums, ManualDifficultyEnums.BEGINNER)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.5f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.BEGINNER));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(ManualIntensityEnums.CARDIO));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.5f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.BEGINNER));
            mappingBO_2.setIntensityEnums(Lists.newArrayList(ManualIntensityEnums.HIIT));

            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }

        if (Objects.equals(difficultyEnums, ManualDifficultyEnums.INTERMEDIATE)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.3f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.INTERMEDIATE));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.3f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.BEGINNER));
            mappingBO_2.setIntensityEnums(Lists.newArrayList(ManualIntensityEnums.HIIT));

            DifficultyAndIntensityMappingBO mappingBO_3 = new DifficultyAndIntensityMappingBO();
            mappingBO_3.setPercent(0.4f);
            mappingBO_3.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.BEGINNER));
            mappingBO_3.setIntensityEnums(Lists.newArrayList(ManualIntensityEnums.CARDIO));

            return Lists.newArrayList(mappingBO_1, mappingBO_2, mappingBO_3);
        }

        if (Objects.equals(difficultyEnums, ManualDifficultyEnums.ADVANCED)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.8f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.INTERMEDIATE));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.2f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(ManualDifficultyEnums.BEGINNER, ManualDifficultyEnums.ADVANCED));
            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }

        return Collections.emptyList();
    }
}
