package com.laien.web.biz.proj.oog104.bo;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.Map;

/**
 * Author:  hhl
 * Date:  2025/3/19 17:38
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class FitnessWorkoutGenerateBO {

    private LinkedList<BaseGenerateVideoBO> workoutAndRelationList;

    private ProjFitnessWorkoutGenerate workoutGenerate;

    private Map<String, String> i18nAudioJsonMap;

}
