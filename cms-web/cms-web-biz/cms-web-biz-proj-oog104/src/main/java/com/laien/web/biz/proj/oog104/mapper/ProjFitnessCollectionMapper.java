package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog104.entity.CollectionWorkoutCount;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCollection;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * proj_fitness_collection Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface ProjFitnessCollectionMapper extends BaseMapper<ProjFitnessCollection> {

    /**
     * 修改new time
     *
     * @param id id
     * @param newStartTime newStartTime
     * @param newEndTime newEndTime
     * @return int
     */
    int updateNewTime(@Param("id") Integer id, @Param("newStartTime") LocalDateTime newStartTime, @Param("newEndTime")LocalDateTime newEndTime);

    /**
     * <p>统计指定collection下各个状态的workout数量</p>
     *
     * @param idsStr collection id集合逗号分隔
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Integer>>
     * <AUTHOR>
     * @date 2025/1/9 10:40
     */
    @Select("select a.proj_fitness_collection_id collectionId,b.status workoutStatus, COUNT(a.proj_fitness_workout_id) workoutCount" +
            " from proj_fitness_collection_proj_fitness_workout a" +
            "         left join proj_fitness_workout b on a.proj_fitness_workout_id = b.id where a.del_flag = 0 and a.proj_fitness_collection_id in(${idsStr})" +
            " group by a.proj_fitness_collection_id, b.status")
    List<CollectionWorkoutCount> countVideosByCollectionIds(@Param("idsStr") String idsStr);

}
