package com.laien.web.biz.proj.oog104.request;

import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * Manual Workout Add Video Request
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
public class ProjFitnessManualWorkoutAddVideoReq {

    /**
     * Video ID
     */
    @ApiModelProperty(value = "Video ID")
    private Integer id;

    /**
     * Exercise Circuit
     */
    @ApiModelProperty(value = "Exercise Circuit")
    private Integer exerciseCircuit;

    /**
     * Unit Name
     */
    @ApiModelProperty(value = "Unit Name")
    private ManualTypeEnums unitName;
}
