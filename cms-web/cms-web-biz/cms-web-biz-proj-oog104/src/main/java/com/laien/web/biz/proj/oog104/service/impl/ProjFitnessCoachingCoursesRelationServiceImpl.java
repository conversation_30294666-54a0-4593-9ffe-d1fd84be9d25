package com.laien.web.biz.proj.oog104.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoachingCoursesRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessCoachingCoursesRelationMapper;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCoachingCoursesRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * CoachingCoursesRelation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessCoachingCoursesRelationServiceImpl
        extends ServiceImpl<ProjFitnessCoachingCoursesRelationMapper, ProjFitnessCoachingCoursesRelation>
        implements IProjFitnessCoachingCoursesRelationService {

}
