package com.laien.web.biz.proj.oog104.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutImage;
import com.laien.web.biz.proj.oog104.request.IdListBodyReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutImageExportVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutImageVO;
import com.laien.web.biz.proj.oog104.service.ProjFitnessWorkoutImageService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * proj_fitness_workout_image controller
 *
 * <AUTHOR>
 */
@Api(tags = {"项目管理:Fitness ProjFitnessWorkoutImage"})
@RestController
@RequestMapping("/proj/fitnessWorkoutImage")
public class ProjFitnessWorkoutImageController {

    @Resource
    private ProjFitnessWorkoutImageService projFitnessWorkoutImageService;

    @ApiOperation(value = "查询workout image列表")
    @GetMapping("/list")
    public ResponseResult<Map<String,Object>> list(ProjFitnessWorkoutImageListReq req){

        List<ProjFitnessWorkoutImageVO> page = projFitnessWorkoutImageService.list(req);
        Map<String, Object> data = new HashMap<>();
        data.put("list",page);
        return ResponseResult.succ(data);
    }

    @ApiOperation(value = "添加workout image")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessWorkoutImageAddReq req){

        req.setProjId(RequestContextUtils.getProjectId());
        projFitnessWorkoutImageService.add(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "修改workout image")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessWorkoutImageUpdateReq req){

        // 排序涉及全局，此接口不支持修改排序
        req.setProjId(RequestContextUtils.getProjectId());
        projFitnessWorkoutImageService.updateImg(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "修改workout image排序")
    @PostMapping("/update_sort")
    public ResponseResult<Void> updateSort(@RequestBody IdListBodyReq req ){

        projFitnessWorkoutImageService.updateSort(req.getIdList());
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量删除workout image")
    @PostMapping("/del")
    public ResponseResult<Void> del(@RequestBody IdListBodyReq req){

        projFitnessWorkoutImageService.del(req.getIdList());
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量启用workout image")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListBodyReq req){

        projFitnessWorkoutImageService.updateEnable(req.getIdList(),true);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量停用workout image")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListBodyReq req){

        projFitnessWorkoutImageService.updateEnable(req.getIdList(),false);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量导入workout image")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importExcel(@RequestParam("file") MultipartFile excel){
        Integer projectId = RequestContextUtils.getProjectId();
        return ResponseResult.succ(projFitnessWorkoutImageService.importExcel(projectId,excel));
    }

    @SneakyThrows
    @ApiOperation(value = "批量导出")
    @GetMapping("/exportWithExcel")
    public void exportWithExcel(HttpServletResponse response,ProjFitnessWorkoutImageListReq req) {

        // 查询数据
        List<ProjFitnessWorkoutImageExportVO> list = projFitnessWorkoutImageService.listExportVO(req);
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''fitnessWorkoutImage.xlsx";
        String sheetName = "fitnessWorkoutImage";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ProjFitnessWorkoutImageExportVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(list);
    }

    @ApiOperation(value = "workout image详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessWorkoutImageVO> detail(@PathVariable("id") Integer id){
        return ResponseResult.succ(projFitnessWorkoutImageService.detail(id));
    }

}