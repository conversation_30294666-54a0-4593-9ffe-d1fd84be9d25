package com.laien.web.biz.proj.oog104.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>生成workout参数</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 17:42
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "生成 workout 参数", description = "生成 workout 参数")
public class ProjFitnessTemplateGenerateWorkoutReq {

    @JsonIgnore
    private Integer projId;

    @ApiModelProperty(value = "模板id列表")
    private List<Integer> templateIds;

    @ApiModelProperty(value = "是否清除数据,0否,1是")
    private Integer cleanUp;

}
