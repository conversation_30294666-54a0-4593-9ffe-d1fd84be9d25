package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

/**
 * <p>匹配类型</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 17:31
 */
@Getter
public enum MatchTypeEnum implements IEnumBase{
    // 匹配类型
    CONTAINS_ALL(1, "contains all", "contains all"),
    CONTAINS_ONE(2, "contains one", "contains one"),
    ;

    private final Integer code;
    private final String name;
    private final String displayName;

    MatchTypeEnum(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static MatchTypeEnum getBy(Integer code) {
        for (MatchTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
