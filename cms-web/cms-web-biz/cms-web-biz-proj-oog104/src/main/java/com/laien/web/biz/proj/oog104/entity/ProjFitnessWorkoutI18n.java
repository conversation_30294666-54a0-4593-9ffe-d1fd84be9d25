package com.laien.web.biz.proj.oog104.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_workout 多语言表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessWorkoutI18n对象", description="proj_fitness_workout 多语言表")
public class ProjFitnessWorkoutI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "workout id")
    private Integer projFitnessWorkoutId;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
