package com.laien.web.biz.proj.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessCoachVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCoachVO", description="ProjFitnessCoachVO")
public class ProjFitnessCoachVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "photoImgUrl")
    private String photoImgUrl;

    @ApiModelProperty(value = "introduction")
    private String introduction;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;
}
