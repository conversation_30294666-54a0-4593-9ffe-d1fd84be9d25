package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog104.enums.FitnessFastingArticleEnums;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessFastingArticle;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessFastingArticleMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessFastingArticleMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessFastingArticleService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * fasting article 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessFastingArticleServiceImpl extends ServiceImpl<ProjFitnessFastingArticleMapper, ProjFitnessFastingArticle> implements IProjFitnessFastingArticleService {

    private final ProjFitnessFastingArticleMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ProjFitnessFastingArticleAddReq articleAddReq, Integer projId) {
        check(articleAddReq, null, projId);
        ProjFitnessFastingArticle article = mapStruct.toEntity(articleAddReq);
        article.setProjId(projId);
        article.setStatus(GlobalConstant.STATUS_DRAFT);
        save(article);
        projLmsI18nService.handleI18n(Collections.singletonList( article), projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjFitnessFastingArticleUpdateReq articleUpdateReq, Integer projId) {
        Integer id = articleUpdateReq.getId();
        ProjFitnessFastingArticle article = baseMapper.selectById(id);
        if (null == article) {
            throw new BizException("article not found");
        }
        check(articleUpdateReq, id, projId);
        ProjFitnessFastingArticle updateVideo = mapStruct.toEntity(articleUpdateReq);
        updateVideo.setId(id);
        updateVideo.setProjId(projId);
        updateById(updateVideo);
        projLmsI18nService.handleI18n(Collections.singletonList( updateVideo), projId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjFitnessFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessFastingArticle::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessFastingArticle::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjFitnessFastingArticle::getId, idList);
        update(new ProjFitnessFastingArticle(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {

        LambdaUpdateWrapper<ProjFitnessFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessFastingArticle::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFitnessFastingArticle::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessFastingArticle::getId, idList);
        this.update(new ProjFitnessFastingArticle(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<ProjFitnessFastingArticle> articleList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjFitnessFastingArticle info = new ProjFitnessFastingArticle();
            info.setSorted(i)
                    .setId(idList.get(i));
            articleList.add(info);
        }
        updateBatchById(articleList);
    }

    @Override
    public List<ProjFitnessFastingArticleListVO> list(ProjFitnessFastingArticleListReq articleListReq, Integer projId) {
        String titleName = articleListReq.getTitleName();
        FitnessFastingArticleEnums type = articleListReq.getType();
        Integer status = articleListReq.getStatus();

        LambdaQueryWrapper<ProjFitnessFastingArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(titleName), ProjFitnessFastingArticle::getTitleName, titleName)
                .eq(null != type, ProjFitnessFastingArticle::getType, type)
                .eq(null != status, ProjFitnessFastingArticle::getStatus, status)
                .orderByAsc(ProjFitnessFastingArticle::getSorted)
                .orderByDesc(BaseModel::getId);
        List<ProjFitnessFastingArticle> articleList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(articleList)) {
            return new ArrayList<>();
        }
        return mapStruct.toVOList(articleList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjFitnessFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessFastingArticle::getDelFlag, GlobalConstant.YES)
                .eq(ProjFitnessFastingArticle::getStatus, GlobalConstant.STATUS_DRAFT)
                .in(ProjFitnessFastingArticle::getId, idList);
        this.update(new ProjFitnessFastingArticle(), wrapper);
    }

    @Override
    public ProjFitnessFastingArticleDetailVO findDetailById(Integer id) {
        ProjFitnessFastingArticle article = baseMapper.selectById(id);
        if (null == article) {
            return null;
        }
        return mapStruct.toDetailVO(article);
    }

    private void check(ProjFitnessFastingArticleAddReq articleReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjFitnessFastingArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessFastingArticle::getTitleName, articleReq.getTitleName())
                .eq(ProjFitnessFastingArticle::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjFitnessFastingArticle> fastingArticleList = baseMapper.selectList(wrapper);
        Set<String> nameSet = fastingArticleList.stream().map(ProjFitnessFastingArticle::getTitleName).collect(Collectors.toSet());
        if (nameSet.contains(articleReq.getTitleName())) {
            String error = "title name already exists";
            throw new BizException(error);
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessFastingArticle::getEventName, articleReq.getEventName())
                .eq(ProjFitnessFastingArticle::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        fastingArticleList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= fastingArticleList.stream().map(ProjFitnessFastingArticle::getEventName).collect(Collectors.toSet());
        if (eventNameSet.contains(articleReq.getEventName())) {
            String error = "eventName already exists";
            throw new BizException(error);
        }
    }
}
