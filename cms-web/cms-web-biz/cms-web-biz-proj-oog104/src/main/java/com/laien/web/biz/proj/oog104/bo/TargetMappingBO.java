package com.laien.web.biz.proj.oog104.bo;

import com.google.common.collect.Lists;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2025/3/25 13:45
 */
@Data
public class TargetMappingBO {

    private ManualTargetEnums target;

    private float percent;

    public static List<TargetMappingBO> getTargetMapping(ManualTargetEnums target) {

        if (Objects.equals(target, ManualTargetEnums.FULL_BODY)) {
            TargetMappingBO mappingBO_1 = new TargetMappingBO();
            mappingBO_1.setTarget(target);
            mappingBO_1.setPercent(1.0f);
            return Lists.newArrayList(mappingBO_1);
        }

        TargetMappingBO mappingBO_1 = new TargetMappingBO();
        mappingBO_1.setTarget(target);
        mappingBO_1.setPercent(0.6f);

        TargetMappingBO mappingBO_2 = new TargetMappingBO();
        mappingBO_2.setTarget(ManualTargetEnums.NONE);
        mappingBO_2.setPercent(0.4f);

        return Lists.newArrayList(mappingBO_1, mappingBO_2);
    }
}
