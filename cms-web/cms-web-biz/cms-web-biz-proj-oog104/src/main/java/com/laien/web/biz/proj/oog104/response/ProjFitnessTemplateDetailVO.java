package com.laien.web.biz.proj.oog104.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>template详情</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 18:07
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "template 详情", description = "template 详情")
public class ProjFitnessTemplateDetailVO {

    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty
    private TemplateTypeEnums templateType;

    @ApiModelProperty("时间范围：1 5-10min；2 10-15min；3 15-20min；4 20-30min；")
    private WorkoutDurationRangeEnums durationRange;

    @ApiModelProperty("生成多少天的天数")
    private Integer days;

    @ApiModelProperty("难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advanced；")
    private ManualDifficultyEnums level;

    @ApiModelProperty
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

    @ApiModelProperty("状态：0草稿，1启用；2禁用；")
    private Integer status;

    @ApiModelProperty("warm up动作组")
    private ProjFitnessTemplateExerciseGroupDetailVO warmupExerciseGroup;

    @ApiModelProperty("main动作组")
    private ProjFitnessTemplateExerciseGroupDetailVO mainExerciseGroup;

    @ApiModelProperty("cool down动作组")
    private ProjFitnessTemplateExerciseGroupDetailVO coolDownExerciseGroup;

}
