package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoachingCourses;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideoCourse;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseAddReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesListVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *     ProjFitnessCoachingCoursesMapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessCoachingCoursesMapStruct {
    List<ProjFitnessCoachingCoursesListVO> toVOList(List<ProjFitnessCoachingCourses> records);

    ProjFitnessCoachingCoursesDetailVO toDetailVO(ProjFitnessCoachingCourses entity);

    ProjFitnessCoachingCourses toEntity(ProjFitnessCoachingCoursesAddReq req);
}
