package com.laien.web.biz.proj.oog104.controller;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplate;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateTask;
import com.laien.web.biz.proj.oog104.request.*;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplateDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplatePageVO;
import com.laien.web.biz.proj.oog104.service.ProjFitnessTemplateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * proj_fitness_template controller
 *
 * <AUTHOR>
 */
@Api(tags = {"项目管理:Fitness ProjFitnessTemplate"})
@RestController
@RequestMapping("/proj/fitnessTemplate")
public class ProjFitnessTemplateController {

    @Resource
    private ProjFitnessTemplateService projFitnessTemplateService;

    @ApiOperation(value = "template生成workout")
    @PostMapping("/generateWorkout")
    public ResponseResult<Void> generateWorkout( @RequestBody ProjFitnessTemplateGenerateWorkoutReq req) throws Exception {

        req.setProjId(RequestContextUtils.getProjectId());
        projFitnessTemplateService.generateWorkout(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "生成template")
    @PostMapping("/generate")
    public ResponseResult<Void> generate( @RequestBody ProjFitnessTemplateGenerateReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        projFitnessTemplateService.generate(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjFitnessTemplatePageVO>> page(ProjFitnessTemplatePageReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        return ResponseResult.succ(projFitnessTemplateService.pageQuery(req));
    }

    @ApiOperation(value = "template详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessTemplateDetailVO> page(@ApiParam("id") @PathVariable Integer id) {

        return ResponseResult.succ(projFitnessTemplateService.detail(id));
    }

    @ApiOperation(value = "修改template")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessTemplateUpdateReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        projFitnessTemplateService.update(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListBodyReq req) {

        projFitnessTemplateService.updateEnable(req.getIdList(), true);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListBodyReq req) {

        projFitnessTemplateService.updateEnable(req.getIdList(), false);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template删除")
    @PostMapping("/del")
    public ResponseResult<Void> delete(@RequestBody IdListBodyReq req) {

        projFitnessTemplateService.delete(req.getIdList());
        return ResponseResult.succ();
    }

}
