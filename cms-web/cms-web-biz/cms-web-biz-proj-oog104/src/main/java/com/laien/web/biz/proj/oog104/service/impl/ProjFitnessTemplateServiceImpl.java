package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTaskStatusEnum;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplate;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateExerciseGroup;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateTask;
import com.laien.web.biz.proj.oog104.entity.TemplateWorkoutNum;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessTemplateExerciseGroupMapper;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessTemplateMapper;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessTemplateTaskMapper;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessWorkoutGenerateMapper;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplateGenerateReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplateGenerateWorkoutReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplatePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessTemplateUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplateDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplateExerciseGroupDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessTemplatePageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessWorkoutGenerateService;
import com.laien.web.biz.proj.oog104.service.ProjFitnessTemplateService;
import com.laien.web.biz.proj.oog104.service.ProjFitnessTemplateTaskService;
import com.laien.web.frame.async.service.IAsyncService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【proj_fitness_template(proj_fitness_template)】的数据库操作Service实现
 * @createDate 2025-03-11 17:01:12
 */
@Service
public class ProjFitnessTemplateServiceImpl extends ServiceImpl<ProjFitnessTemplateMapper, ProjFitnessTemplate>
        implements ProjFitnessTemplateService {

    @Resource
    private ProjFitnessTemplateTaskMapper projFitnessTemplateTaskMapper;

    @Resource
    private ProjFitnessTemplateExerciseGroupMapper projFitnessTemplateExerciseGroupMapper;

    @Resource
    private ProjFitnessWorkoutGenerateMapper projFitnessWorkoutGenerateMapper;

    @Resource
    @Lazy
    private IProjFitnessWorkoutGenerateService workoutGenerateService;

    @Resource
    IAsyncService asyncService;

    @Autowired
    private ProjFitnessTemplateTaskService projFitnessTemplateTaskService;

    @Autowired
    private IProjInfoService projInfoService;

    @Autowired
    private IProjLmsI18nService projLmsI18nService;

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer generate(ProjFitnessTemplateGenerateReq req) {

        List<List<ExerciseVideoSpecialLimitEnums>> allSubsets = new ArrayList<>();
        //V8.3.0选择 106 fitness 不考虑 special limit
        if (CollUtil.isNotEmpty(req.getSpecialLimitList())
                && !Objects.equals(TemplateTypeEnums.FITNESS_106,req.getTemplateType())) {
            allSubsets = getAllSubsets(req.getSpecialLimitList());
        } else {
            allSubsets.add(null);
        }

        List<ProjFitnessTemplate> generateTemplates = Lists.newArrayList();
        // special limits 取排列组合
        for (WorkoutDurationRangeEnums durationRange : req.getDurationRangeList()) {
            for (ManualDifficultyEnums level : req.getLevelList()) {
                for (List<ExerciseVideoSpecialLimitEnums> specialLimitList : allSubsets) {
                    // 使用参数创建一个ProjFitnessTemplate对象
                    ProjFitnessTemplate projFitnessTemplate = new ProjFitnessTemplate()
                            .setProjId(req.getProjId())
                            .setTemplateType(req.getTemplateType())
                            .setDurationRange(durationRange)
                            .setLevel(level)
                            .setSpecialLimit(specialLimitList)
                            .setExclusiveType(req.getExclusiveType())
                            .setDays(28)
                            .setStatus(GlobalConstant.STATUS_DRAFT);
                    projFitnessTemplate.setName(generateTemplateName(projFitnessTemplate));
                    generateTemplates.add(projFitnessTemplate);

                }
            }
        }
        // 数据库查重复，维度一致的模板不重新创建
        LambdaQueryWrapper<ProjFitnessTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessTemplate::getProjId, req.getProjId());
        queryWrapper.eq(ProjFitnessTemplate::getTemplateType, req.getTemplateType());
        queryWrapper.in(ProjFitnessTemplate::getDurationRange, req.getDurationRangeList());
        queryWrapper.in(ProjFitnessTemplate::getLevel, req.getLevelList());
        queryWrapper.in(ProjFitnessTemplate::getExclusiveType, req.getExclusiveType());

        // 由于 枚举指定的 type handler 会把null转为空集合，因此这里认为null和空集合是一样的,转为null方便下一步做判断
        List<ProjFitnessTemplate> maybeTemplates = this.list(queryWrapper);
        maybeTemplates.stream().filter(t -> CollUtil.isEmpty(t.getSpecialLimit())).forEach(t -> t.setSpecialLimit(null));
        // 过滤掉维度一致的
        List<ProjFitnessTemplate> forInsert = generateTemplates.stream()
                .filter(template -> maybeTemplates.stream().noneMatch(maybeTemplate -> isDuplicateTemplate(template, maybeTemplate))).collect(Collectors.toList());
        Lists.partition(forInsert, 1000).forEach(this.baseMapper::insertBatchSomeColumn);
        // 为模板生成动作组
        List<ProjFitnessTemplateExerciseGroup> allGroup = forInsert.stream().flatMap(template -> createExerciseGroup(req, template)).collect(Collectors.toList());
        List<List<ProjFitnessTemplateExerciseGroup>> partitions = Lists.partition(allGroup, 1000);
        for (List<ProjFitnessTemplateExerciseGroup> partition : partitions) {
            this.projFitnessTemplateExerciseGroupMapper.insertBatchSomeColumn(partition);
            projLmsI18nService.handleI18n(partition, req.getProjId());
        }

        return forInsert.size();
    }

    private static Stream<ProjFitnessTemplateExerciseGroup> createExerciseGroup(ProjFitnessTemplateGenerateReq req, ProjFitnessTemplate template) {

        // warm up
        ProjFitnessTemplateExerciseGroup warmUpGroup = new ProjFitnessTemplateExerciseGroup();
        warmUpGroup.setProjId(req.getProjId());
        warmUpGroup.setProjFitnessTemplateId(template.getId());
        warmUpGroup.setGroupType(ManualTypeEnums.WARM_UP);
        warmUpGroup.setGroupName(ManualTypeEnums.WARM_UP.getName());
        warmUpGroup.setCount(template.getDurationRange().getWarmUpGroupCount());
        warmUpGroup.setRounds(template.getDurationRange().getWarmUpGroupRounds());

        // main
        ProjFitnessTemplateExerciseGroup mainGroup = new ProjFitnessTemplateExerciseGroup();
        mainGroup.setProjId(req.getProjId());
        mainGroup.setProjFitnessTemplateId(template.getId());
        mainGroup.setGroupType(ManualTypeEnums.MAIN);
        mainGroup.setGroupName(ManualTypeEnums.MAIN.getName());
        mainGroup.setRounds(template.getDurationRange().getMainGroupRounds());

        // coll down
        ProjFitnessTemplateExerciseGroup coolDownGroup = new ProjFitnessTemplateExerciseGroup();
        coolDownGroup.setProjId(req.getProjId());
        coolDownGroup.setProjFitnessTemplateId(template.getId());
        coolDownGroup.setGroupType(ManualTypeEnums.COOL_DOWN);
        coolDownGroup.setGroupName(ManualTypeEnums.COOL_DOWN.getName());
        coolDownGroup.setCount(template.getDurationRange().getCoolDownGroupCount());
        coolDownGroup.setRounds(template.getDurationRange().getCoolDownGroupRounds());

        return Stream.of(warmUpGroup,mainGroup, coolDownGroup);
    }

    private static boolean isDuplicateTemplate(ProjFitnessTemplate template, ProjFitnessTemplate maybeTemplate) {

        boolean sameSpecialLimits = (Objects.isNull(template.getSpecialLimit()) && Objects.isNull(maybeTemplate.getSpecialLimit()))
                || (Objects.nonNull(template.getSpecialLimit())
                && Objects.nonNull(maybeTemplate.getSpecialLimit())
                && Objects.equals(template.getSpecialLimit().size(), maybeTemplate.getSpecialLimit().size())
                && new HashSet<>(template.getSpecialLimit()).containsAll(maybeTemplate.getSpecialLimit()));

        return template.getProjId().equals(maybeTemplate.getProjId())
                && template.getExclusiveType().equals(maybeTemplate.getExclusiveType())
                && template.getTemplateType().equals(maybeTemplate.getTemplateType())
                && template.getDurationRange().equals(maybeTemplate.getDurationRange())
                && template.getLevel().equals(maybeTemplate.getLevel())
                && sameSpecialLimits;
    }

    private String generateTemplateName(ProjFitnessTemplate fitnessTemplate) {

        return fitnessTemplate.getDurationRange().getName() + GlobalConstant.ENGLISH_DASH +
                fitnessTemplate.getLevel().getName() +
                Optional.ofNullable(fitnessTemplate.getSpecialLimit()).filter(CollUtil::isNotEmpty)
                        .map(list -> GlobalConstant.ENGLISH_DASH + list.stream().map(ExerciseVideoSpecialLimitEnums::getName).collect(Collectors.joining(GlobalConstant.COMMA))).orElse("");
    }


    private List<List<ExerciseVideoSpecialLimitEnums>> getAllSubsets(List<ExerciseVideoSpecialLimitEnums> specialLimitList) {

        List<List<ExerciseVideoSpecialLimitEnums>> result = new ArrayList<>();
        backtrack(result, new ArrayList<>(), specialLimitList, 0);
        return result.stream().filter(CollUtil::isNotEmpty).collect(Collectors.toList());
    }

    private static void backtrack(List<List<ExerciseVideoSpecialLimitEnums>> result, List<ExerciseVideoSpecialLimitEnums> temp, List<ExerciseVideoSpecialLimitEnums> nums, int start) {
        result.add(new ArrayList<>(temp));
        for (int i = start; i < nums.size(); i++) {
            temp.add(nums.get(i));
            backtrack(result, temp, nums, i + 1);
            temp.remove(temp.size() - 1);
        }
    }


    @Override
    public PageRes<ProjFitnessTemplatePageVO> pageQuery(ProjFitnessTemplatePageReq req) {

        Page<ProjFitnessTemplate> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<ProjFitnessTemplate> pageResult = this.baseMapper.pageWithTask(page,req);
        if (page.getRecords().isEmpty()) {
            return PageConverter.convert(page, ProjFitnessTemplatePageVO.class);
        }
        // workout 数量查询
        List<Integer> ids = pageResult.getRecords().stream().map(BaseModel::getId).collect(Collectors.toList());
        Map<Integer, Integer> workoutNumMap = projFitnessWorkoutGenerateMapper.countTemplateWorkoutNum(ids).stream().collect(Collectors.toMap(TemplateWorkoutNum::getTemplateId, TemplateWorkoutNum::getWorkoutNum, (v1, v2) -> v1));
        return PageConverter.convert(page, ProjFitnessTemplatePageVO.class, (template, templatePageVO) -> {
            templatePageVO.setWorkoutNum(workoutNumMap.getOrDefault(template.getId(), 0));
        });
    }

    @Override
    public ProjFitnessTemplateDetailVO detail(Integer id) {
        ProjFitnessTemplate projFitnessTemplate = this.baseMapper.selectById(id);
        AssertUtil.notNull(projFitnessTemplate, "This template is not exist!");
        // 动作组
        LambdaQueryWrapper<ProjFitnessTemplateExerciseGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessTemplateExerciseGroup::getProjFitnessTemplateId, id);
        queryWrapper.eq(BaseModel::getDelFlag, GlobalConstant.NO);
        Map<ManualTypeEnums, List<ProjFitnessTemplateExerciseGroup>> groupMap = projFitnessTemplateExerciseGroupMapper.selectList(queryWrapper).stream().collect(Collectors.groupingBy(ProjFitnessTemplateExerciseGroup::getGroupType));

        ProjFitnessTemplateDetailVO detailVO = BeanUtil.toBean(projFitnessTemplate, ProjFitnessTemplateDetailVO.class);
        detailVO.setWarmupExerciseGroup(BeanUtil.toBean(groupMap.get(ManualTypeEnums.WARM_UP).get(0), ProjFitnessTemplateExerciseGroupDetailVO.class));
        detailVO.setMainExerciseGroup(BeanUtil.toBean(groupMap.get(ManualTypeEnums.MAIN).get(0), ProjFitnessTemplateExerciseGroupDetailVO.class));
        detailVO.setCoolDownExerciseGroup(BeanUtil.toBean(groupMap.get(ManualTypeEnums.COOL_DOWN).get(0), ProjFitnessTemplateExerciseGroupDetailVO.class));

        return detailVO;
    }

    @Override
    public void generateWorkout(ProjFitnessTemplateGenerateWorkoutReq req) throws Exception {

        // 检验任务状态
        LambdaQueryWrapper<ProjFitnessTemplateTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessTemplateTask::getProjFitnessTemplateId, req.getTemplateIds());
        queryWrapper.in(ProjFitnessTemplateTask::getStatus, Lists.newArrayList(TemplateTaskStatusEnum.PENDING, TemplateTaskStatusEnum.RUNNING));
        queryWrapper.eq(BaseModel::getDelFlag, GlobalConstant.NO);
        AssertUtil.isFalse(projFitnessTemplateTaskMapper.selectCount(queryWrapper) > 0, "There are tasks that have not yet been completed.");

        List<ProjFitnessTemplateTask> templateTasks = req.getTemplateIds().stream().map(templateId ->
                new ProjFitnessTemplateTask()
                        .setProjFitnessTemplateId(templateId)
                        .setProjId(req.getProjId())
                        .setStatus(TemplateTaskStatusEnum.PENDING)
                        .setCleanUp(req.getCleanUp())).collect(Collectors.toList());
        projFitnessTemplateTaskService.saveBatch(templateTasks);

        ProjInfo projInfo = projInfoService.getById(req.getProjId());
        asyncService.doSomethings(() -> workoutGenerateService.generateWorkoutByTask(templateTasks, projInfo));
    }

    @Override
    public void updateEnable(List<Integer> req, Boolean isEnable) {

        LambdaUpdateWrapper<ProjFitnessTemplate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BaseModel::getId, req);
        updateWrapper.set(ProjFitnessTemplate::getStatus, isEnable ? GlobalConstant.STATUS_ENABLE : GlobalConstant.STATUS_DISABLE);
        this.update(updateWrapper);

    }

    @Override
    public void delete(List<Integer> idList) {

        this.baseMapper.selectBatchIds(idList).forEach(template -> {
            AssertUtil.notNull(template, "This template is not exist!");
            // 只有草稿状态的模板可删除
            AssertUtil.isTrue(template.getStatus() == GlobalConstant.STATUS_DRAFT, "Deleting this template is not allowed!");
        });

        this.baseMapper.deleteBatchIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(ProjFitnessTemplateUpdateReq req) {
        ProjFitnessTemplate template = this.getById(req.getId());
        AssertUtil.notNull(template, "This template is not exist!");

        // 模板基础信息修改
        template.setName(req.getName());
        template.setTemplateType(req.getTemplateType());
        template.setDurationRange(req.getDurationRange());
        template.setDays(req.getDays());
        template.setLevel(req.getLevel());
        template.setSpecialLimit(req.getSpecialLimit());
        template.setExclusiveType(req.getExclusiveType());

        this.updateById(template);
        // 动作组修改，先删除旧数据，再新增新数据
        LambdaQueryWrapper<ProjFitnessTemplateExerciseGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessTemplateExerciseGroup::getProjFitnessTemplateId, req.getId());
        projFitnessTemplateExerciseGroupMapper.delete(queryWrapper);
        // 动作组添加
        List<ProjFitnessTemplateExerciseGroup> insertGroups = covertExerciseGroup(req);
        projFitnessTemplateExerciseGroupMapper.insertBatchSomeColumn(insertGroups);

        projLmsI18nService.handleI18n(insertGroups, req.getProjId());

    }

    private List<ProjFitnessTemplateExerciseGroup> covertExerciseGroup(ProjFitnessTemplateUpdateReq req) {

        ProjFitnessTemplateExerciseGroupDetailVO warmupExerciseGroup = req.getWarmupExerciseGroup();
        ProjFitnessTemplateExerciseGroup warmup = BeanUtil.toBean(warmupExerciseGroup, ProjFitnessTemplateExerciseGroup.class);
        warmup.setGroupType(ManualTypeEnums.WARM_UP);
        warmup.setProjId(req.getProjId());
        warmup.setProjFitnessTemplateId(req.getId());

        ProjFitnessTemplateExerciseGroupDetailVO mainExerciseGroup = req.getMainExerciseGroup();
        ProjFitnessTemplateExerciseGroup main = BeanUtil.toBean(mainExerciseGroup, ProjFitnessTemplateExerciseGroup.class);
        main.setGroupType(ManualTypeEnums.MAIN);
        main.setProjId(req.getProjId());
        main.setProjFitnessTemplateId(req.getId());

        ProjFitnessTemplateExerciseGroupDetailVO coolDownExerciseGroup = req.getCoolDownExerciseGroup();
        ProjFitnessTemplateExerciseGroup coolDown = BeanUtil.toBean(coolDownExerciseGroup, ProjFitnessTemplateExerciseGroup.class);
        coolDown.setGroupType(ManualTypeEnums.COOL_DOWN);
        coolDown.setProjId(req.getProjId());
        coolDown.setProjFitnessTemplateId(req.getId());

        return Lists.newArrayList(warmup, main, coolDown);
    }

}




