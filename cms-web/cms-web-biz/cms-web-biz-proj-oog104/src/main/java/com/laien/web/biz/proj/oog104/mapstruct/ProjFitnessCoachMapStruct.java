package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoach;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachAddReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *     ProjFitnessCoachMapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessCoachMapStruct {

    List<ProjFitnessCoachVO> toVOList(List<ProjFitnessCoach> dishList);

    ProjFitnessCoachVO toVO(ProjFitnessCoach projFitnessCoach);

    ProjFitnessCoach toEntity(ProjFitnessCoachAddReq req);
}
