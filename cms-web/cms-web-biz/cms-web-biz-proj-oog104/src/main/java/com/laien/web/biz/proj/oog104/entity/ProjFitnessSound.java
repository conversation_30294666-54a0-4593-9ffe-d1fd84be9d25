package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.common.oog104.enums.FitnessGenderEnums;
import com.laien.common.oog104.enums.FitnessSoundSubTypeEnums;
import com.laien.common.oog104.enums.FitnessSoundTypeEnums;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_sound
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessSound对象", description = "proj_fitness_sound")
@TableName(autoResultMap = true)
public class ProjFitnessSound extends BaseModel  implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_SOUND;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private FitnessSoundTypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private FitnessSoundSubTypeEnums soundSubType;

    @TranslateField(
            type = TranslationTaskTypeEnums.SPEECH,
            audioUrlFieldName = "url")
    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "声音")
    private String url;

    @ApiModelProperty(value = "duration")
    private Integer duration;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private FitnessGenderEnums gender;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;


}
