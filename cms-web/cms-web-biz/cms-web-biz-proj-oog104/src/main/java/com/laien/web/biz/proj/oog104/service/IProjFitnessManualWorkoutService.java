package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessManualWorkout;
import com.laien.web.biz.proj.oog104.request.*;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessManualWorkoutPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * proj_fitness_manual_workout 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface IProjFitnessManualWorkoutService extends IService<ProjFitnessManualWorkout> {

    /**
     * 分页查询训练列表
     *
     * @param pageReq 分页查询参数
     * @return 分页结果
     */
    PageRes<ProjFitnessManualWorkoutPageVO> selectWorkoutPage(ProjFitnessManualWorkoutPageReq pageReq);

    /**
     * 保存训练
     *
     * @param workoutReq 训练信息
     */
    void saveWorkout(ProjFitnessManualWorkoutAddReq workoutReq);

    /**
     * 更新训练
     *
     * @param workoutReq 训练信息
     */
    void updateWorkout(ProjFitnessManualWorkoutUpdateReq workoutReq);


    Boolean generateM3u8(ProjFitnessManualWorkoutGenerateM3u8Req workoutReq);

    /**
     * 获取训练详情
     *
     * @param id 训练ID
     * @return 训练详情
     */
    ProjFitnessManualWorkoutDetailVO getWorkoutDetail(Integer id);

    /**
     * 启用训练
     *
     * @param idList 训练ID列表
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 禁用训练
     *
     * @param idList 训练ID列表
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 删除训练
     *
     * @param idList 训练ID列表
     */
    void deleteByIds(List<Integer> idList);
}
