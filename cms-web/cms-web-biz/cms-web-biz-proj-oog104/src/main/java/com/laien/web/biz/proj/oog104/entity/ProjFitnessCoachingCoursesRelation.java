package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  ProjFitnessCoachingCoursesRelation
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "proj_fitness_coaching_courses_relation",autoResultMap = true)
@ApiModel(value = "ProjFitnessCoachingCoursesRelation对象", description = "ProjFitnessCoachingCoursesRelation对象")
public class ProjFitnessCoachingCoursesRelation extends BaseModel {

    @ApiModelProperty(value = "关联VideoCourseId")
    private Integer projFitnessVideoCourseId;

    @ApiModelProperty(value = "关联CoachingCoursesId")
    private Integer projFitnessCoachingCoursesId;
}
