package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessPlan;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessPlanDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessPlanPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * proj_fitness_plan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IProjFitnessPlanService extends IService<ProjFitnessPlan> {

    /**
     * plan分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjFitnessPlanPageVO> selectPlanPage(ProjFitnessPlanPageReq pageReq);

    /**
     * plan新增
     *
     * @param planReq planReq
     */
    void savePlan(ProjFitnessPlanAddReq planReq);

    /**
     * plan修改
     *
     * @param planReq planReq
     */
    void updatePlan(ProjFitnessPlanUpdateReq planReq);

    /**
     * plan详情
     *
     * @param id id
     * @return ProjFitnessPlanDetailVO
     */
    ProjFitnessPlanDetailVO getPlanDetail(Integer id);

    /**
     * plan启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * plan禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * plan删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

}
