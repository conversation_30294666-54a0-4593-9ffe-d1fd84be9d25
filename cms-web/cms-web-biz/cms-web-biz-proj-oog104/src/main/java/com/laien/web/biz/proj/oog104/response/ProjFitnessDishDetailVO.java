package com.laien.web.biz.proj.oog104.response;

import com.laien.common.oog104.enums.dish.FitnessDishStyleEnums;
import com.laien.common.oog104.enums.dish.FitnessDishTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Dish
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjFitnessDishDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型")
    private List<FitnessDishTypeEnums> types;

    @ApiModelProperty(value = "风格")
    private List<FitnessDishStyleEnums> styles;

    @ApiModelProperty(value = "准备时间，单位分钟")
    private Integer prepareTime;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "碳水含量")
    private BigDecimal carb;

    @ApiModelProperty(value = "蛋白质含量")
    private BigDecimal protein;

    @ApiModelProperty(value = "脂肪含量")
    private BigDecimal fat;

    @ApiModelProperty(value = "源视频地址")
    private String resourceVideoUrl;

    @ApiModelProperty(value = "份数")
    private Integer serving;

    @ApiModelProperty(value = "dishStepList")
    private List<ProjFitnessDishStepVO> dishStepList;

    @ApiModelProperty(value = "ingredient")
    private List<ProjFitnessIngredientVO> ingredientList;

    @ApiModelProperty(value = "allergenIdList")
    private List<Integer> allergenIdList;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "多分辨率m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "2532 的m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "视频时长")
    private Integer duration;

}
