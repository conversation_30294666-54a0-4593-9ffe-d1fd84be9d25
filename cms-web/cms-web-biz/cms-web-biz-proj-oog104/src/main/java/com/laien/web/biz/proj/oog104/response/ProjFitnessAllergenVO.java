package com.laien.web.biz.proj.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessAllergenVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessAllergen对象", description="allergen")
public class ProjFitnessAllergenVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "过敏源名称")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
