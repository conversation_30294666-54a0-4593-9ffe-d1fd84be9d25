package com.laien.web.biz.proj.oog104.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateIntensityEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>workout image分页查询对象</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 18:03
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "workout image 列表查询参数", description = "workout image 列表查询")
public class ProjFitnessWorkoutImageListReq {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("图片名称")
    private String name;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

    @ApiModelProperty("年龄段")
    private List<ManualAgeGroupEnums> ageGroup;

    @ApiModelProperty("锻炼部位")
    private ManualTargetEnums target;

    @ApiModelProperty("难度")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty("排序")
    private TemplateIntensityEnums intensity;

    @ApiModelProperty("特殊限制")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;
    @ApiModelProperty("状态")
    private Integer status;

    //v8.3.0新增templateType字段
    @ApiModelProperty("模版类型字段")
    private TemplateTypeEnums templateType;

}
