package com.laien.web.biz.proj.oog104.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_collection_proj_fitness_workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCollectionProjFitnessWorkout对象", description="proj_fitness_collection_proj_fitness_workout")
public class ProjFitnessCollectionProjFitnessWorkout extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "collection id")
    private Integer projFitnessCollectionId;

    @ApiModelProperty(value = "workout id")
    private Integer projFitnessWorkoutId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
