package com.laien.web.biz.proj.oog104.controller;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessMealPlan;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessMealPlanDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessMealPlanListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessMealPlanService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:13
 */
@Api(tags = "项目管理 104: fitnessMealPlan")
@RestController
@RequestMapping("/proj/fitnessMealPlan")
public class ProjFitnessMealPlanController extends ResponseController {

    @Resource
    private IProjFitnessMealPlanService mealPlanService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjFitnessMealPlanListVO>> page(ProjFitnessMealPlanListReq pageReq) {

        List<ProjFitnessMealPlanListVO> pageRes = mealPlanService.selectMealPlanList(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessMealPlanAddReq addReq) {

        mealPlanService.saveMealPlan(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessMealPlanUpdateReq updateReq) {

        mealPlanService.updateMealPlan(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessMealPlanDetailVO> detail(@PathVariable Integer id) {

        ProjFitnessMealPlanDetailVO detailVO = mealPlanService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        mealPlanService.sort(idListReq);
        return succ();
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        mealPlanService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        mealPlanService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        mealPlanService.deleteByIds(idList);
        return succ();
    }

}
