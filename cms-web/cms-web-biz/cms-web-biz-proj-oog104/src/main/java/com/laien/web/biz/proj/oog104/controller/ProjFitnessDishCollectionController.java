package com.laien.web.biz.proj.oog104.controller;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishCollection;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishCollectionDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishCollectionListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishCollectionService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2024/12/17 17:13
 */
@Api(tags = "项目管理 104: fitnessDishCollection")
@RestController
@RequestMapping("/proj/fitnessDishCollection")
public class ProjFitnessDishCollectionController extends ResponseController {

    @Resource
    private IProjFitnessDishCollectionService dishCollectionService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjFitnessDishCollectionListVO>> list(ProjFitnessDishCollectionListReq listReq) {

        List<ProjFitnessDishCollectionListVO> collectionVOList = dishCollectionService.selectDishCollection(listReq);
        return succ(collectionVOList);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessDishCollectionAddReq addReq) {

        dishCollectionService.saveDishCollection(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessDishCollectionUpdateReq updateReq) {

        dishCollectionService.updateDishCollection(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessDishCollectionDetailVO> detail(@PathVariable Integer id) {

        ProjFitnessDishCollectionDetailVO detailVO = dishCollectionService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        dishCollectionService.sort(idListReq);
        return succ();
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        dishCollectionService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        dishCollectionService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        dishCollectionService.deleteByIds(idList);
        return succ();
    }

}
