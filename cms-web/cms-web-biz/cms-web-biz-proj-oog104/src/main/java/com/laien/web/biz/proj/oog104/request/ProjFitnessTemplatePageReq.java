package com.laien.web.biz.proj.oog104.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTaskStatusEnum;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>模板分页查询对象</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 18:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "template 分页查询参数", description = "template 分页查询")
public class ProjFitnessTemplatePageReq extends PageReq {

    @JsonIgnore
    private Integer projId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板类型")
    private TemplateTypeEnums templateType;

    @ApiModelProperty("时间范围")
    private WorkoutDurationRangeEnums durationRange;

    @ApiModelProperty("状态：0 草稿；1 启用；2 禁用；")
    private Integer status;

    @ApiModelProperty("任务状态 1待处理、2处理中、3处理失败、4 处理成功")
    private TemplateTaskStatusEnum taskStatus;

    @ApiModelProperty("难度等级")
    private ManualDifficultyEnums level;

    @ApiModelProperty("特殊限制")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty("针对的人群类型")
    private ExclusiveTypeEnums exclusiveType;


}
