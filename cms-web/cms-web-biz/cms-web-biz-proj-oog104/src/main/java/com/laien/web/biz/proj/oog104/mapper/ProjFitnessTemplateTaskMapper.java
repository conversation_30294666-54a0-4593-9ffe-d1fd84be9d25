package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_fitness_template_task(proj_fitness_template_task)】的数据库操作Mapper
* @createDate 2025-03-12 15:23:43
* @Entity com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplateTask
*/
public interface ProjFitnessTemplateTaskMapper extends BaseMapper<ProjFitnessTemplateTask> {

    int insertBatchSomeColumn(@Param("list") List<ProjFitnessTemplateTask> batchList);

}




