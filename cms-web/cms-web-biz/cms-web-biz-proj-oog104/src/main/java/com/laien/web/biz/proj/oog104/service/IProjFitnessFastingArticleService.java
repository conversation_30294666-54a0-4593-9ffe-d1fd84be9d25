package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessFastingArticle;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.List;

/**
 * <p>
 * IProjFitnessFastingArticleService
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
public interface IProjFitnessFastingArticleService extends IService<ProjFitnessFastingArticle> {

    void save(ProjFitnessFastingArticleAddReq articleAddReq, Integer projId);

    void update(ProjFitnessFastingArticleUpdateReq articleUpdateReq, Integer projId);

    ProjFitnessFastingArticleDetailVO findDetailById(Integer id);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIds(List<Integer> idList);

    List<ProjFitnessFastingArticleListVO> list(ProjFitnessFastingArticleListReq articleListReq, Integer projId);

    void sort(IdListReq idListReq);
}
