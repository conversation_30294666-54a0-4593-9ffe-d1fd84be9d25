package com.laien.web.biz.proj.oog104.config;

import com.laien.web.biz.proj.oog104.bo.Video104SysSoundBO;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Oog104BizConfig {

    @ConfigurationProperties(value = "cms.biz.oog104")
    @Bean
    public Video104SysSoundBO getOog104() {
        return new Video104SysSoundBO();
    }

    @ConfigurationProperties(value = "cms.biz.oog104.regular-fitness")
    @Bean(value = "regular-fitness")
    public FitnessSoundConfig getRegularFitnessSoundConfig() {

        return new FitnessSoundConfig();
    }
   //v8.3.0 添加 pilates 声音
    @ConfigurationProperties(value = "cms.biz.oog104.pilates")
    @Bean(value = "pilates")
    public FitnessSoundConfig getPilatesSoundConfig() {

        return new FitnessSoundConfig();
    }

    @ConfigurationProperties(value = "cms.biz.oog104.wall-pilates")
    @Bean(value = "wall-pilates")
    public FitnessSoundConfig getWallPilatesSoundConfig() {

        return new FitnessSoundConfig();
    }

    @ConfigurationProperties(value = "cms.biz.oog104.classic-yoga")
    @Bean(value = "classic-yoga")
    public FitnessSoundConfig getClassYogaSoundConfig() {

        return new FitnessSoundConfig();
    }

    @ConfigurationProperties(value = "cms.biz.oog104.chair-yoga")
    @Bean(value = "chair-yoga")
    public FitnessSoundConfig getChairYogaSoundConfig() {

        return new FitnessSoundConfig();
    }

    @ConfigurationProperties(value = "cms.biz.oog104.fitness106")
    @Bean(value = "fitness106")
    public FitnessSoundConfig get106FitnessSoundConfig() {

        return new FitnessSoundConfig();
    }

}
