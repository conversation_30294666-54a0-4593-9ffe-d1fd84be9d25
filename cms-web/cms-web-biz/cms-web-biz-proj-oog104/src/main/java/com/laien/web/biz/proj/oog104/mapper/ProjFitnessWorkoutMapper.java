package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkout;
import com.laien.web.biz.proj.oog104.entity.TemplateWorkoutNum;
import com.laien.web.biz.proj.oog104.entity.WorkoutVideoCount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * proj_fitness_workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface ProjFitnessWorkoutMapper extends BaseMapper<ProjFitnessWorkout> {

    /**
     * 根据ids查询
     *
     * @param idsStr id集合
     * @return list
     */
    @Select("select * from proj_fitness_workout where id in (${idsStr})")
    List<ProjFitnessWorkout> selectListByIds(@Param("idsStr") String idsStr);

    /**
     * <p>统计指定workout下各个状态的视频数量</p>
     *
     * @param idsStr workout id集合逗号分隔
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Integer>>
     * <AUTHOR>
     * @date 2025/1/9 10:40
     */
    @Select("select a.proj_fitness_workout_id workoutId,b.status videoStatus, COUNT(a.proj_fitness_video_id) AS videoCount " +
            "from proj_fitness_workout_proj_fitness_video a " +
            "         left join proj_fitness_video b on a.proj_fitness_video_id = b.id where a.del_flag = 0 and a.proj_fitness_workout_id in (${idsStr})" +
            " group by a.proj_fitness_workout_id, b.status")
    List<WorkoutVideoCount> countVideosByWorkoutIds(@Param("idsStr") String idsStr);
}
