package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.core.util.MyStringUtil;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.PlanWorkoutCount;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessPlan;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessPlanProjFitnessWorkout;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkout;
import com.laien.web.biz.proj.oog104.enums.ExtraTagEnums;
import com.laien.web.biz.proj.oog104.enums.IEnumBase;
import com.laien.web.biz.proj.oog104.enums.PlanTypeEnums;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessPlanMapper;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanAddWorkoutReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessPlanUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessPlanDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessPlanDetailWorkoutVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessPlanPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessPlanProjFitnessWorkoutService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessPlanService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessWorkoutService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_plan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class ProjFitnessPlanServiceImpl extends ServiceImpl<ProjFitnessPlanMapper, ProjFitnessPlan> implements IProjFitnessPlanService {

    @Resource
    private IProjFitnessPlanProjFitnessWorkoutService projFitnessPlanProjFitnessWorkoutService;
    @Resource
    private IProjFitnessWorkoutService projFitnessWorkoutService;
    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public PageRes<ProjFitnessPlanPageVO> selectPlanPage(ProjFitnessPlanPageReq pageReq) {
        Integer id = pageReq.getId();
        String name = pageReq.getName();
        Integer status = pageReq.getStatus();
        PlanTypeEnums type = pageReq.getType();
        LambdaQueryWrapper<ProjFitnessPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(id), ProjFitnessPlan::getId, id)
                .eq(ProjFitnessPlan::getProjId, RequestContextUtils.getProjectId())
                .like(StringUtils.isNotBlank(name), ProjFitnessPlan::getName, name)
                .eq(Objects.nonNull(status), ProjFitnessPlan::getStatus, status);
        if (Objects.nonNull(type)) {
            wrapper.eq(ProjFitnessPlan::getTypeCode, type.getCode());
        }

        wrapper.orderByDesc(ProjFitnessPlan::getId);
        IPage<ProjFitnessPlan> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), wrapper);
        List<ProjFitnessPlan> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
        }
        String idStr = records.stream().map(plan -> plan.getId().toString()).collect(Collectors.joining(","));
        // 查询plan下关联的不可用workout数量
        Map<Integer, Integer> workoutCountDisabledNumMap = baseMapper.countVideosByPlanIds(idStr).stream()
                .filter(c ->c.getWorkoutStatus().equals(GlobalConstant.STATUS_DISABLE))
                .collect(Collectors.groupingBy( PlanWorkoutCount::getPlanId, Collectors.summingInt(PlanWorkoutCount::getWorkoutCount)));
        List<ProjFitnessPlanPageVO> list = records.stream().map(plan -> {
            ProjFitnessPlanPageVO pageVO = new ProjFitnessPlanPageVO();
            BeanUtils.copyProperties(plan, pageVO);
            IEnumBase.setEnum(pageVO::setType, plan.getTypeCode(), PlanTypeEnums::getBy);
            pageVO.setWorkoutDisabledCount(workoutCountDisabledNumMap.getOrDefault(plan.getId(), 0));
            return pageVO;
        }).collect(Collectors.toList());
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePlan(ProjFitnessPlanAddReq planReq) {
        // 校验
        Integer projId = RequestContextUtils.getProjectId();
        this.check(planReq, null, projId);

        ProjFitnessPlan plan = this.covertToFitnessPlan(planReq);
        plan.setStatus(GlobalConstant.STATUS_DRAFT);
        plan.setProjId(projId);
        this.save(plan);
        // 保存workout关系
        this.saveRelation(plan.getId(), projId, planReq.getWorkoutList());

        projLmsI18nService.handleI18n(ListUtil.of(plan), projId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePlan(ProjFitnessPlanUpdateReq planReq) {
        Integer id = planReq.getId();
        ProjFitnessPlan planFind = this.getById(id);
        if (Objects.isNull(planFind)) {
            throw new BizException("Data not found");
        }

        Integer projId = planFind.getProjId();
        // 校验
        this.check(planReq, id, projId);

        ProjFitnessPlan plan = this.covertToFitnessPlan(planReq);
        this.updateById(plan);
        // 删除workout关系
        this.deleteRelation(id);
        // 保存workout关系
        this.saveRelation(id, projId, planReq.getWorkoutList());

        projLmsI18nService.handleI18n(ListUtil.of(plan), projId);
    }

    /**
     * 校验
     *
     * @param planReq planReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjFitnessPlanAddReq planReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjFitnessPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessPlan::getName, planReq.getName())
                .ne(Objects.nonNull(id), ProjFitnessPlan::getId, id)
                .eq(ProjFitnessPlan::getProjId, projId);
        int count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("name already exists");
        }

        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessPlan::getEventName, planReq.getEventName())
                .ne(Objects.nonNull(id), ProjFitnessPlan::getId, id)
                .eq(ProjFitnessPlan::getProjId, projId);
        count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("event name already exists");
        }
    }

    /**
     * 转换为ProjFitnessPlan对象
     *
     * @param planReq planReq
     * @return ProjFitnessPlan
     */
    private ProjFitnessPlan covertToFitnessPlan(ProjFitnessPlanAddReq planReq) {
        ProjFitnessPlan plan = new ProjFitnessPlan();
        BeanUtils.copyProperties(planReq, plan);
        IEnumBase.setCodeInteger(plan::setTypeCode, planReq.getType());

        List<Integer> stageCounts = planReq.getStageCounts();
        int sum = stageCounts.stream().mapToInt(Integer::intValue).sum();
        if (sum != planReq.getWorkoutList().size()) {
            throw new BizException("Stage count is not match workout");
        }
        List<String> stageStringCounts = stageCounts.stream().map(String::valueOf).collect(Collectors.toList());
        plan.setStageCounts(String.join(StringPool.COMMA, stageStringCounts));
        plan.setWorkoutCount(sum);
        plan.setTags(String.join(StringPool.NEWLINE, planReq.getTags()));
        plan.setExpectedResults(String.join(StringPool.NEWLINE, planReq.getExpectedResults()));

        return plan;
    }

    @Override
    public ProjFitnessPlanDetailVO getPlanDetail(Integer id) {
        ProjFitnessPlan planFind = this.getById(id);
        if (Objects.isNull(planFind)) {
            throw new BizException("Data not found");
        }

        ProjFitnessPlanDetailVO detailVO = new ProjFitnessPlanDetailVO();
        BeanUtils.copyProperties(planFind, detailVO);
        IEnumBase.setEnum(detailVO::setType, planFind.getTypeCode(), PlanTypeEnums::getBy);
        List<Integer> stageCounts = Arrays.stream(planFind.getStageCounts().split(StringPool.COMMA))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        detailVO.setStageCounts(stageCounts);
        detailVO.setTags(MyStringUtil.toListWithNewLine(planFind.getTags()));
        detailVO.setExpectedResults(MyStringUtil.toListWithNewLine(planFind.getExpectedResults()));

        List<ProjFitnessPlanDetailWorkoutVO> workoutList = this.getWorkoutList(id);
        detailVO.setWorkoutList(workoutList);

        return detailVO;
    }

    /**
     * 获取plan workout list
     *
     * @param id id
     * @return list
     */
    private List<ProjFitnessPlanDetailWorkoutVO> getWorkoutList(Integer id) {
        List<ProjFitnessPlanProjFitnessWorkout> fitnessPlanProjFitnessWorkoutList =
                projFitnessPlanProjFitnessWorkoutService.list(new LambdaQueryWrapper<ProjFitnessPlanProjFitnessWorkout>()
                .eq(ProjFitnessPlanProjFitnessWorkout::getProjFitnessPlanId, id));
        List<Integer> workoutIds = fitnessPlanProjFitnessWorkoutList.stream()
                .map(ProjFitnessPlanProjFitnessWorkout::getProjFitnessWorkoutId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, ProjFitnessWorkout> fitnessWorkoutMap = projFitnessWorkoutService.selectListByIds(workoutIds).stream()
                .collect(Collectors.toMap(ProjFitnessWorkout::getId, t -> t));

        List<ProjFitnessPlanDetailWorkoutVO> workoutList = new ArrayList<>();
        for (ProjFitnessPlanProjFitnessWorkout fitnessPlanProjFitnessWorkout : fitnessPlanProjFitnessWorkoutList) {
            ProjFitnessPlanDetailWorkoutVO workoutVO = new ProjFitnessPlanDetailWorkoutVO();
            ProjFitnessWorkout workout = fitnessWorkoutMap.get(fitnessPlanProjFitnessWorkout.getProjFitnessWorkoutId());
            if (workout != null) {
                BeanUtils.copyProperties(workout, workoutVO);
                IEnumBase.setEnumList(workoutVO::setExtraTag, workout.getExtraTagCodes(), ExtraTagEnums::getBy);
            }
            workoutList.add(workoutVO);
        }

        return workoutList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        this.update(new ProjFitnessPlan(), new LambdaUpdateWrapper<ProjFitnessPlan>()
                .set(ProjFitnessPlan::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessPlan::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjFitnessPlan::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        this.update(new ProjFitnessPlan(), new LambdaUpdateWrapper<ProjFitnessPlan>()
                .set(ProjFitnessPlan::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessPlan::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessPlan::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            boolean flag = this.update(new ProjFitnessPlan(), new LambdaUpdateWrapper<ProjFitnessPlan>()
                    .set(ProjFitnessPlan::getDelFlag, GlobalConstant.YES)
                    .eq(ProjFitnessPlan::getStatus, GlobalConstant.STATUS_DRAFT)
                    .eq(ProjFitnessPlan::getId, id));
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }

    /**
     * 保存workout关系
     *
     * @param id id
     * @param projId projId
     * @param workoutList workoutList
     */
    private void saveRelation(Integer id, Integer projId, List<ProjFitnessPlanAddWorkoutReq> workoutList) {
        List<ProjFitnessPlanProjFitnessWorkout> fitnessPlanProjFitnessWorkoutList = new ArrayList<>();
        for (ProjFitnessPlanAddWorkoutReq fitnessPlanAddWorkoutReq : workoutList) {
            ProjFitnessPlanProjFitnessWorkout fitnessPlanProjFitnessWorkout = new ProjFitnessPlanProjFitnessWorkout();
            fitnessPlanProjFitnessWorkout.setProjFitnessPlanId(id);
            fitnessPlanProjFitnessWorkout.setProjId(projId);
            fitnessPlanProjFitnessWorkout.setProjFitnessWorkoutId(fitnessPlanAddWorkoutReq.getId());
            fitnessPlanProjFitnessWorkoutList.add(fitnessPlanProjFitnessWorkout);
        }

        projFitnessPlanProjFitnessWorkoutService.saveBatch(fitnessPlanProjFitnessWorkoutList);
    }

    /**
     * 删除workout关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        projFitnessPlanProjFitnessWorkoutService.update(new ProjFitnessPlanProjFitnessWorkout(),
                new LambdaUpdateWrapper<ProjFitnessPlanProjFitnessWorkout>()
                        .set(ProjFitnessPlanProjFitnessWorkout::getDelFlag, GlobalConstant.YES)
                        .eq(ProjFitnessPlanProjFitnessWorkout::getProjFitnessPlanId, id)
        );
    }

}
