package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

/**
 * 部位枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum PositionEnums implements IEnumBase {
    STANDING(1, "Standing", "Standing"),
    SEATED(2, "Seated", "Seated"),
    LYING(3, "Lying", "Lying"),
    PRONE(4, "<PERSON>ne", "<PERSON>ne"),
    KNEELING(5, "<PERSON>nee<PERSON>", "<PERSON>neeling");

    private final Integer code;
    private final String name;
    private final String displayName;

    PositionEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static PositionEnums getBy(Integer code) {
        for (PositionEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
