package com.laien.web.biz.proj.oog104.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 类型枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum TypeEnums implements IEnumBase {
    WARM_UP(1, "Warm up", "Warm up"),
    MAIN(2, "Main", "Main"),
    COOL_DOWN(3, "Cool Down", "Cool Down");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    TypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }
    public static TypeEnums getBy(Integer code) {
        for (TypeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
