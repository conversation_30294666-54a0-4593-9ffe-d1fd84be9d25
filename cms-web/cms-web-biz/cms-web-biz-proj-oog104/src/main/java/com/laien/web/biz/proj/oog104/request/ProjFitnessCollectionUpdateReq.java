package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "collection 修改", description = "collection 修改")
public class ProjFitnessCollectionUpdateReq extends ProjFitnessCollectionAddReq {

    @ApiModelProperty(value = "id")
    private Integer id;

}
