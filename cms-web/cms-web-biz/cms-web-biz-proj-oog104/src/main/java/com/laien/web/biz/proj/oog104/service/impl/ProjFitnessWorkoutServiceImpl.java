package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.bo.Video104SysSoundBO;
import com.laien.web.biz.proj.oog104.config.Oog104BizConfig;
import com.laien.web.biz.proj.oog104.entity.*;
import com.laien.web.biz.proj.oog104.entity.i18n.ProjFitnessSoundI18n;
import com.laien.web.biz.proj.oog104.entity.i18n.ProjFitnessVideoI18n;
import com.laien.web.biz.proj.oog104.enums.*;
import com.laien.web.biz.proj.oog104.hts.HTSVideoSegment;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessWorkoutMapper;
import com.laien.web.biz.proj.oog104.request.*;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutDetailVideoVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutPageVO;
import com.laien.web.biz.proj.oog104.service.*;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.AudioJsonBO;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.bo.TsTextMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class ProjFitnessWorkoutServiceImpl extends ServiceImpl<ProjFitnessWorkoutMapper, ProjFitnessWorkout> implements IProjFitnessWorkoutService {

    @Resource
    private IProjFitnessVideoService projFitnessVideoService;
    @Resource
    private IProjFitnessWorkoutProjFitnessVideoService projFitnessWorkoutProjFitnessVideoService;
    @Resource
    private IProjFitnessWorkoutGenerateFileTaskService projFitnessWorkoutGenerateFileTaskService;
    @Resource
    private IProjInfoService projInfoService;
    @Resource
    private FileService fileService;
    @Resource
    private IProjFitnessSoundService fitnessSoundService;
    @Resource
    private IProjFitnessWorkoutI18nService projFitnessWorkoutI18nService;
    @Resource
    private IProjLmsI18nService projLmsI18nService;
    @Resource
    private ICoreSpeechTaskI18nPubService speechI18nPubService;

    @Resource
    private Oog104BizConfig oog104BizConfig;

    private static final String FIRST = "first";
    private static final String THREE_TWO_ONE = "threeTwoOne";
    private static final String GO = "go";
    private static final String READY_FOR = "readyFor";
    private static final String LAST = "last";
    private static final String END_THREE_TWO_ONE = "endThreeTwoOne";

    private final ExecutorService TASK_EXECUTOR_SERVICE = Executors.newFixedThreadPool(1);

    @Override
    public PageRes<ProjFitnessWorkoutPageVO> selectWorkoutPage(ProjFitnessWorkoutPageReq pageReq) {
        Integer id = pageReq.getId();
        String name = pageReq.getName();
        Integer status = pageReq.getStatus();
        Integer fileStatus = pageReq.getFileStatus();
        List<ExtraTagEnums> extraTag = pageReq.getExtraTag();
        LambdaQueryWrapper<ProjFitnessWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(id), ProjFitnessWorkout::getId, id)
                .eq(ProjFitnessWorkout::getProjId, RequestContextUtils.getProjectId())
                .like(StringUtils.isNotBlank(name), ProjFitnessWorkout::getName, name)
                .eq(Objects.nonNull(status), ProjFitnessWorkout::getStatus, status)
                .eq(Objects.nonNull(fileStatus), ProjFitnessWorkout::getFileStatus, fileStatus);
        List<Integer> extraTagCodeList = new ArrayList<>();
        IEnumBase.setCodeInteger(extraTagCodeList::addAll, extraTag);
        if (!extraTagCodeList.isEmpty()) {
            wrapper.and(o -> {
                int len = extraTagCodeList.size();
                for (int i = 0; i < len; i++) {
                    o.apply("FIND_IN_SET({0}, extra_tag_codes)", extraTagCodeList.get(i));
                }
            });
        }

        wrapper.orderByDesc(ProjFitnessWorkout::getId);
        IPage<ProjFitnessWorkout> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), wrapper);
        List<ProjFitnessWorkout> records = page.getRecords();
        if(CollUtil.isEmpty(records)){
            return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
        }
        // 各workout关联视频数量统计，应该按workout ID 和 禁用状态两个维度分组查询
        String idStr = records.stream().map(w -> w.getId().toString()).collect(Collectors.joining(","));
        List<WorkoutVideoCount> countVideosByWorkout = this.baseMapper.countVideosByWorkoutIds(idStr);
        Map<Integer, Integer> videoCountNumMap = countVideosByWorkout.stream()
                .collect(Collectors.groupingBy(WorkoutVideoCount::getWorkoutId, Collectors.summingInt(WorkoutVideoCount::getVideoCount)));
        Map<Integer, Integer> videoCountDisabledNumMap = countVideosByWorkout.stream()
                .filter(c -> c.getVideoStatus().equals(GlobalConstant.STATUS_DISABLE))
                .collect(Collectors.groupingBy(WorkoutVideoCount::getWorkoutId, Collectors.summingInt(WorkoutVideoCount::getVideoCount)));
        List<ProjFitnessWorkoutPageVO> list = records.stream().map(workout -> {
            ProjFitnessWorkoutPageVO pageVO = new ProjFitnessWorkoutPageVO();
            BeanUtils.copyProperties(workout, pageVO);
            IEnumBase.setEnumList(pageVO::setExtraTag, workout.getExtraTagCodes(), ExtraTagEnums::getBy);
            pageVO.setVideoNum(videoCountNumMap.getOrDefault(workout.getId(), 0));
            pageVO.setVideoDisabledNum(videoCountDisabledNumMap.getOrDefault(workout.getId(), 0));

            return pageVO;
        }).collect(Collectors.toList());
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWorkout(ProjFitnessWorkoutAddReq workoutReq) {
        // 校验
        Integer projId = RequestContextUtils.getProjectId();
        this.check(workoutReq, null, projId);

        ProjFitnessWorkout workout = new ProjFitnessWorkout();
        BeanUtils.copyProperties(workoutReq, workout);
        IEnumBase.setCodeString(workout::setExtraTagCodes, workoutReq.getExtraTag());
        workout.setStatus(GlobalConstant.STATUS_DRAFT);
        workout.setProjId(projId);
        this.save(workout);

        // 保存新的video关系
        this.saveRelation(workout.getId(), projId, workoutReq.getVideoList());

        // 保存m3u8
        this.saveWorkoutM3u8(workout);

        projLmsI18nService.handleI18n(ListUtil.of( workout), projId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWorkout(ProjFitnessWorkoutUpdateReq workoutReq) {
        Integer id = workoutReq.getId();
        ProjFitnessWorkout workoutFind = this.getById(id);
        if (Objects.isNull(workoutFind)) {
            throw new BizException("Data not found");
        }

        Integer projId = workoutFind.getProjId();
        // 校验
        this.check(workoutReq, id, projId);

        ProjFitnessWorkout workout = new ProjFitnessWorkout();
        BeanUtils.copyProperties(workoutReq, workout);
        IEnumBase.setCodeString(workout::setExtraTagCodes, workoutReq.getExtraTag());
        this.updateById(workout);
        // 删除video关系
        this.deleteRelation(id);

        // 保存新的video关系
        this.saveRelation(id, projId, workoutReq.getVideoList());
        // 保存m3u8
        workout.setProjId(projId);

        // 保存m3u8
        this.saveWorkoutM3u8(workout);

        // 更新关联collection的时长
        IProjFitnessCollectionService service = SpringUtil.getBean(IProjFitnessCollectionService.class);
        service.updateDurationByWorkoutId(id);

        projLmsI18nService.handleI18n(ListUtil.of( workout), projId);
    }

    /**
     * 校验
     *
     * @param workoutReq workoutReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjFitnessWorkoutAddReq workoutReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjFitnessWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkout::getName, workoutReq.getName())
                .ne(Objects.nonNull(id), ProjFitnessWorkout::getId, id)
                .eq(ProjFitnessWorkout::getProjId, projId);
        int count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("name already exists");
        }

        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkout::getEventName, workoutReq.getEventName())
                .ne(Objects.nonNull(id), ProjFitnessWorkout::getId, id)
                .eq(ProjFitnessWorkout::getProjId, projId);
        count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("event name already exists");
        }
    }


    /**
     * 删除workout 关系
     *
     * @param id id
     */
    private void saveRelation(Integer id, Integer projId, List<ProjFitnessWorkoutAddVideoReq> videoList) {
        List<ProjFitnessWorkoutProjFitnessVideo> fitnessVideoList = new ArrayList<>();
        LinkedHashMap<String, List<ProjFitnessWorkoutAddVideoReq>> videoListGroup = videoList.stream()
                .collect(Collectors.groupingBy(
                        ProjFitnessWorkoutAddVideoReq::getUnitName,
                        LinkedHashMap::new,
                        Collectors.toList())
                );

        for (Map.Entry<String, List<ProjFitnessWorkoutAddVideoReq>> entry : videoListGroup.entrySet()) {
            List<ProjFitnessWorkoutAddVideoReq> videoListByUnit = entry.getValue();
            for (ProjFitnessWorkoutAddVideoReq fitnessWorkoutAddVideoReq : videoListByUnit) {
                ProjFitnessWorkoutProjFitnessVideo fitnessVideo = new ProjFitnessWorkoutProjFitnessVideo();
                fitnessVideo.setProjFitnessWorkoutId(id);
                fitnessVideo.setUnitName(fitnessWorkoutAddVideoReq.getUnitName());
                fitnessVideo.setProjFitnessVideoId(fitnessWorkoutAddVideoReq.getId());
                fitnessVideo.setProjId(projId);
                fitnessVideoList.add(fitnessVideo);
            }
        }

        projFitnessWorkoutProjFitnessVideoService.saveBatch(fitnessVideoList);
        projLmsI18nService.handleI18n(fitnessVideoList, projId);
    }

    /**
     * 删除workout 关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        projFitnessWorkoutProjFitnessVideoService.update(new ProjFitnessWorkoutProjFitnessVideo(),
                new LambdaUpdateWrapper<ProjFitnessWorkoutProjFitnessVideo>()
                        .set(ProjFitnessWorkoutProjFitnessVideo::getDelFlag, GlobalConstant.YES)
                        .eq(ProjFitnessWorkoutProjFitnessVideo::getProjFitnessWorkoutId, id)
        );
    }

    @Override
    public ProjFitnessWorkoutDetailVO getWorkoutDetail(Integer id) {
        ProjFitnessWorkout workoutFind = this.getById(id);
        if (Objects.isNull(workoutFind)) {
            throw new BizException("Data not found");
        }
        ProjFitnessWorkoutDetailVO detailVO = new ProjFitnessWorkoutDetailVO();
        BeanUtils.copyProperties(workoutFind, detailVO);
        IEnumBase.setEnumList(detailVO::setExtraTag, workoutFind.getExtraTagCodes(), ExtraTagEnums::getBy);

        List<ProjFitnessWorkoutDetailVideoVO> videoList = this.selectVideoList(id);
        detailVO.setVideoList(videoList);

        return detailVO;
    }

    /**
     * 获取workout video list
     *
     * @param id id
     * @return list
     */
    private List<ProjFitnessWorkoutDetailVideoVO> selectVideoList(Integer id) {
        List<ProjFitnessWorkoutProjFitnessVideo> fitnessWorkoutProjFitnessVideoList =
                projFitnessWorkoutProjFitnessVideoService.list(new LambdaQueryWrapper<ProjFitnessWorkoutProjFitnessVideo>()
                .eq(ProjFitnessWorkoutProjFitnessVideo::getProjFitnessWorkoutId, id));

        List<Integer> videoIds = fitnessWorkoutProjFitnessVideoList.stream()
                .map(ProjFitnessWorkoutProjFitnessVideo::getProjFitnessVideoId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, ProjFitnessVideo> fitnessVideoListMap = projFitnessVideoService.selectListByIds(videoIds).stream()
                .collect(Collectors.toMap(ProjFitnessVideo::getId, t -> t));

        List<ProjFitnessWorkoutDetailVideoVO> videoList = new ArrayList<>();
        for (ProjFitnessWorkoutProjFitnessVideo fitnessWorkoutProjFitnessVideo : fitnessWorkoutProjFitnessVideoList) {
            ProjFitnessWorkoutDetailVideoVO videoVO = new ProjFitnessWorkoutDetailVideoVO();
            ProjFitnessVideo video = fitnessVideoListMap.get(fitnessWorkoutProjFitnessVideo.getProjFitnessVideoId());
            if (video != null) {
                videoVO.setRelationId(fitnessWorkoutProjFitnessVideo.getId());
                BeanUtils.copyProperties(video, videoVO);
                IEnumBase.setEnumList(videoVO::setType, video.getTypeCodes(), TypeEnums::getBy);
                IEnumBase.setEnum(videoVO::setDifficulty, video.getDifficultyCode(), DifficultyEnums::getBy);
                IEnumBase.setEnum(videoVO::setPosition, video.getPositionCode(), PositionEnums::getBy);
                IEnumBase.setEnumList(videoVO::setFitType, video.getFitTypeCodes(), FitTypeEnum::getBy);
                IEnumBase.setEnumList(videoVO::setTarget, video.getTargetCodes(), TargetEnums::getBy);
                IEnumBase.setEnumList(videoVO::setEquipment, video.getEquipmentCodes(), EquipmentEnums::getBy);
                IEnumBase.setEnumList(videoVO::setSpecialLimits, video.getSpecialLimitCodes(), SpecialLimitEnums::getBy);
            }
            videoVO.setUnitName(fitnessWorkoutProjFitnessVideo.getUnitName());
            videoList.add(videoVO);
        }

        return videoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        this.update(new ProjFitnessWorkout(), new LambdaUpdateWrapper<ProjFitnessWorkout>()
                .set(ProjFitnessWorkout::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjFitnessWorkout::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        this.update(new ProjFitnessWorkout(), new LambdaUpdateWrapper<ProjFitnessWorkout>()
                .set(ProjFitnessWorkout::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessWorkout::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessWorkout::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            boolean flag = this.update(new ProjFitnessWorkout(), new LambdaUpdateWrapper<ProjFitnessWorkout>()
                    .set(ProjFitnessWorkout::getDelFlag, GlobalConstant.YES)
                    .eq(ProjFitnessWorkout::getStatus, GlobalConstant.STATUS_DRAFT)
                    .eq(ProjFitnessWorkout::getId, id));
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }

    @Override
    public Boolean generateM3u8(ProjFitnessWorkoutGenerateM3u8Req m3u8Req) {
        List<ProjFitnessWorkout> workouts = this.list(new LambdaQueryWrapper<ProjFitnessWorkout>()
                .in(ProjFitnessWorkout::getId, m3u8Req.getWorkoutIds()));
        List<Integer> workoutIds = workouts.stream()
                .map(ProjFitnessWorkout::getId)
                .collect(Collectors.toList());

        List<String> languages = m3u8Req.getLanguages();
        // 获取视频系统音频的语言映射
        Map<String, Map<String, AudioJsonBO>> sysSoundMap = this.getSysSoundLanguageMap(languages);
        // 获取halfway系统音频的语言映射
        List<Map<String, AudioJsonBO>> halfWaySysSounds = this.getHalfwaySysSoundLanguageMap(languages);

        projFitnessWorkoutGenerateFileTaskService.save(new ProjFitnessWorkoutGenerateFileTask()
                .setProjId(workouts.get(GlobalConstant.ZERO).getProjId())
                .setVideoFlag(m3u8Req.getVideoFlag())
                .setAudioFlag(m3u8Req.getAudioFlag())
                .setLanguages(StringUtils.join(m3u8Req.getLanguages(), StringPool.COMMA))
                .setWorkoutIds(StringUtils.join(
                        m3u8Req.getWorkoutIds() == null ? new ArrayList<>() : m3u8Req.getWorkoutIds(),
                        StringPool.COMMA)
                )
        );

        //文件处理状态赋值
        this.update(new LambdaUpdateWrapper<ProjFitnessWorkout>().set(ProjFitnessWorkout::getFileStatus, GlobalConstant.ZERO)
                .in(ProjFitnessWorkout::getId, workoutIds));

        workouts.forEach(workout -> {
            TASK_EXECUTOR_SERVICE.execute(() -> {
                try {
                    this.generateFitnessWorkoutM3u8(workout, sysSoundMap, halfWaySysSounds, m3u8Req);
                } catch (BizException be) {
                    workout.setFailMessage("Lack of audio resources : " + StringUtils.substring(be.getMessage(), 0, 200))
                            .setFileStatus(GlobalConstant.TWO);
                    this.updateById(workout);
                    log.error("Lack of audio resources", be);
                } catch (Exception e) {
                    workout.setFailMessage("Generate m3u8 error : " + StringUtils.substring(e.getMessage(), 0, 200))
                            .setFileStatus(GlobalConstant.TWO);
                    this.updateById(workout);
                    log.error("Generate m3u8 error", e);
                }
            });
        });
        return Boolean.TRUE;
    }

    @Override
    public List<ProjFitnessWorkout> selectListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectListByIds(Joiner.on(StringPool.COMMA).join(ids));
    }


    /**
     * 保存M3u8文件
     *
     * @param workout workout
     */
    private void saveWorkoutM3u8(ProjFitnessWorkout workout) {
        // 根据项目ID获取支持的语言列表
        List<String> languages = projInfoService.getLanguagesById(workout.getProjId());
        // 获取视频系统音频的语言映射
        Map<String, Map<String, AudioJsonBO>> sysSoundMap = this.getSysSoundLanguageMap(languages);
        // 获取halfway系统音频的语言映射
        List<Map<String, AudioJsonBO>> halfWaySysSounds = this.getHalfwaySysSoundLanguageMap(languages);

        ProjFitnessWorkoutGenerateM3u8Req m3u8Req = new ProjFitnessWorkoutGenerateM3u8Req();
        m3u8Req.setAudioFlag(true);
        m3u8Req.setVideoFlag(true);
        m3u8Req.setLanguages(languages);

        this.generateFitnessWorkoutM3u8(workout, sysSoundMap, halfWaySysSounds, m3u8Req);
    }

    /**
     * 获取系统音
     *
     * @param languages languages
     * @return map
     */
    private Map<String, Map<String, AudioJsonBO>> getSysSoundLanguageMap(List<String> languages) {
        Video104SysSoundBO sysSoundBO = oog104BizConfig.getOog104();
        String first = sysSoundBO.getFirst();
        String threeTwoOne = sysSoundBO.getThreeTwoOne();
        String go = sysSoundBO.getGo();
        String readyFor = sysSoundBO.getReadyFor();
        String last = sysSoundBO.getLast();
        String endThreeTwoOne = sysSoundBO.getEndThreeTwoOne();

        // 对应idMap
        Map<String, String> soundIdMap = new LinkedHashMap<>();
        soundIdMap.put(first, FIRST);
        soundIdMap.put(threeTwoOne, THREE_TWO_ONE);
        soundIdMap.put(go, GO);
        soundIdMap.put(readyFor, READY_FOR);
        soundIdMap.put(last, LAST);
        soundIdMap.put(endThreeTwoOne, END_THREE_TWO_ONE);

        // 从数据库查询系统音，并以soundName 为key 转为map
        Collection<String> soundNames = soundIdMap.keySet();
        LambdaQueryWrapper<ProjFitnessSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessSound::getSoundName, soundNames);
        Map<String, ProjFitnessSound> soundListMap = fitnessSoundService.list(queryWrapper).stream().collect(
                Collectors.toMap(
                        ProjFitnessSound::getSoundName,
                        sound -> sound,
                        (existing, replacement) -> replacement
                )
        );

        // map<SoundName, Map<language, AudioJsonBO>>
        Map<String, Map<String, AudioJsonBO>> sysSoundLanguageMap = new HashMap<>();
        // 需要翻译的sound, key为name
        Map<String, ProjFitnessSound> soundMap = new HashMap<>();
        List<ProjFitnessSoundI18n> i18nModelList = new ArrayList<>();
        for (String name : soundNames) {
            String id = soundIdMap.get(name);
            ProjFitnessSound sound = soundListMap.get(name);
            if (sound == null || StringUtils.isBlank(sound.getUrl())) {
                throw new BizException("System sound '" + id + "' not find!");
            }
            String soundUrl = sound.getUrl();
            String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
            BigDecimal time = new BigDecimal(sound.getDuration().toString());
            AudioJsonBO audioJsonBO = new AudioJsonBO(id, fileService.getAbsoluteR2Url(soundUrl), soundName, time);
            Map<String, AudioJsonBO> audioJsonLanguagesMap = new LinkedHashMap<>();
            audioJsonLanguagesMap.put(GlobalConstant.DEFAULT_LANGUAGE, audioJsonBO);
            sysSoundLanguageMap.put(name, audioJsonLanguagesMap);

            soundMap.put(name, sound);
            // 如果该音需要翻译，则将其ID添加到列表中
            if (sound.getNeedTranslation()) {
                i18nModelList.add(new ProjFitnessSoundI18n(sound));
            }
        }

        // 英语以外的其他语种
        List<String> excludeEnLanguages = languages.stream().filter(o -> !Objects.equals(o, GlobalConstant.DEFAULT_LANGUAGE)).collect(Collectors.toList());
        if (excludeEnLanguages.isEmpty()) {
            return sysSoundLanguageMap;
        }

        Map<Object, Map<LanguageEnums, AudioTranslateResultModel>> soundI18nMap = new HashMap<>();
        if (!i18nModelList.isEmpty()) {
           soundI18nMap = speechI18nPubService
                    .getI18nResultGroupByKey(i18nModelList, languages, ProjCodeEnums.OOG104);
        }

        for (String name : soundNames) {
            String id = soundIdMap.get(name);
            ProjFitnessSound fitnessSound = soundMap.get(name);
            Map<LanguageEnums, AudioTranslateResultModel> fitnessSoundI18nMap = soundI18nMap.get(fitnessSound.getId());
            Map<String, AudioJsonBO> audioJsonLanguagesMap = sysSoundLanguageMap.get(name);
            for (String language : excludeEnLanguages) {
                String soundUrl;
                BigDecimal time;
                if (fitnessSound.getNeedTranslation()) {
                    AudioTranslateResultModel soundI18n = fitnessSoundI18nMap.get(LanguageEnums.getByNameIgnoreCase(language));
                    if (soundI18n == null || StringUtils.isBlank(soundI18n.getAudioUrl())) {
                        throw new BizException("System sound '" + id + "' language(" + language + ") not find!");
                    }
                    soundUrl = soundI18n.getAudioUrl();
                    time = new BigDecimal(soundI18n.getDuration().toString());
                } else {
                    soundUrl = fitnessSound.getUrl();
                    time = new BigDecimal(fitnessSound.getDuration().toString());
                }
                String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
                AudioJsonBO audioJsonBO = new AudioJsonBO(id, fileService.getAbsoluteR2Url(soundUrl), soundName, time);
                audioJsonLanguagesMap.put(language, audioJsonBO);
            }
        }
        return sysSoundLanguageMap;
    }

    /**
     * 获取halfway系统音
     *
     * @param languages languages
     * @return map
     */
    private List<Map<String, AudioJsonBO>> getHalfwaySysSoundLanguageMap(List<String> languages) {
        Video104SysSoundBO sysSoundBO = oog104BizConfig.getOog104();
        List<String> soundNames = sysSoundBO.getHalfWayList();
        LambdaQueryWrapper<ProjFitnessSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessSound::getSoundName, soundNames);
        Map<String, ProjFitnessSound> soundListMap = fitnessSoundService.list(queryWrapper).stream().collect(
                Collectors.toMap(
                        ProjFitnessSound::getSoundName,
                        sound -> sound,
                        (existing, replacement) -> replacement
                )
        );

        int size = soundNames.size();
        List<ProjFitnessSoundI18n> i18nModelList = new ArrayList<>();
        // 按照配置的顺序返回
        String previousSoundName = "halfWay";
        List<Map<String, AudioJsonBO>> audioJsonMapList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            String halfWayNode = soundNames.get(i);
            String id = previousSoundName + i;
            ProjFitnessSound sound = soundListMap.get(halfWayNode);
            if (sound == null || StringUtils.isBlank(sound.getUrl())) {
                throw new BizException("System sound '" + halfWayNode + "' not find!");
            }

            String soundUrl = sound.getUrl();
            String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
            BigDecimal time = new BigDecimal(sound.getDuration().toString());
            AudioJsonBO audioJsonBO = new AudioJsonBO(id, fileService.getAbsoluteR2Url(soundUrl), soundName, time);
            Map<String, AudioJsonBO> audioJsonLanguagesMap = new LinkedHashMap<>();
            audioJsonLanguagesMap.put(GlobalConstant.DEFAULT_LANGUAGE, audioJsonBO);
            audioJsonMapList.add(audioJsonLanguagesMap);

            // 如果该音需要翻译，则将其ID添加到列表中
            if (sound.getNeedTranslation()) {
                i18nModelList.add(new ProjFitnessSoundI18n(sound));
            }
        }

        // 英语以外的其他语种
        List<String> excludeEnLanguages = languages.stream().filter(o -> !Objects.equals(o, GlobalConstant.DEFAULT_LANGUAGE)).collect(Collectors.toList());
        if (excludeEnLanguages.isEmpty()) {
            return audioJsonMapList;
        }

        Map<Object, Map<LanguageEnums, AudioTranslateResultModel>> soundI18nMap = new HashMap<>();
        if (!i18nModelList.isEmpty()) {
            soundI18nMap = speechI18nPubService
                    .getI18nResultGroupByKey(i18nModelList, languages, ProjCodeEnums.OOG104);
        }


        for (int i = 0; i < size; i++) {
            String halfWayNodeNode = soundNames.get(i);
            String id = previousSoundName + i;
            ProjFitnessSound sound = soundListMap.get(halfWayNodeNode);
            Map<LanguageEnums, AudioTranslateResultModel> fitnessSoundI18nMap = soundI18nMap.get(sound.getId());
            Map<String, AudioJsonBO> audioJsonLanguagesMap = audioJsonMapList.get(i);
            for (String language : excludeEnLanguages) {
                String soundUrl;
                BigDecimal time;
                if (sound.getNeedTranslation()) {
                    AudioTranslateResultModel soundI18n = fitnessSoundI18nMap.get(LanguageEnums.getByNameIgnoreCase(language));
                    if (soundI18n == null || StringUtils.isBlank(soundI18n.getAudioUrl())) {
                        throw new BizException("System sound '" + id + "' language(" + language + ") not find!");
                    }
                    soundUrl = soundI18n.getAudioUrl();
                    time = new BigDecimal(soundI18n.getDuration().toString());
                } else {
                    soundUrl = sound.getUrl();
                    time = new BigDecimal(sound.getDuration().toString());
                }
                String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
                AudioJsonBO audioJsonBO = new AudioJsonBO(id, fileService.getAbsoluteR2Url(soundUrl), soundName, time);
                audioJsonLanguagesMap.put(language, audioJsonBO);
            }
        }
        return audioJsonMapList;
    }

    private void generateFitnessWorkoutM3u8(ProjFitnessWorkout workout, Map<String, Map<String, AudioJsonBO>> sysSoundMap, List<Map<String, AudioJsonBO>> halfWaySysSounds, ProjFitnessWorkoutGenerateM3u8Req m3u8Req) {
        // 系统音获取并验证是否完整(系统音不区分语言)
        Video104SysSoundBO sysSoundBO = oog104BizConfig.getOog104();
        List<String> languages = m3u8Req.getLanguages();

        Integer id = workout.getId();
        List<ProjFitnessWorkoutDetailVideoVO> relationList = this.selectVideoList(id);

        // Map<id, Map<language, ProjFitnessVideo> 主键+lang 作为key
        Map<Integer, Map<String, ProjFitnessVideo>> i18nContainer = this.getI18nContainer(relationList, languages);

        List<String> ruleIdList = new ArrayList<>();
        LinkedHashMap<String, List<ProjFitnessWorkoutDetailVideoVO>> relationGroup = relationList.stream()
                .peek(o -> {
                    String ruleId = o.getUnitName();
                    if (!ruleIdList.contains(ruleId)) {
                        ruleIdList.add(ruleId);
                    }
                }).collect(Collectors.groupingBy(
                        ProjFitnessWorkoutDetailVideoVO::getUnitName, LinkedHashMap::new, Collectors.toList()));
        // 视频列表
        int size = relationList.size();
        HTSVideoSegment htsVideoSegment = HTSVideoSegment.newInstance(size);
        TsTextMergeBO tsTextMergeBO = new TsTextMergeBO();
        // 音频列表
        Map<String, List<AudioJsonBO>> audioListI18nMap = new HashMap<>(size);
        for (String language : languages) {
            audioListI18nMap.put(language, new ArrayList<>(size));
        }

        // 开始生成
        BigDecimal startTime = new BigDecimal("0.1");
        int ruleSize = ruleIdList.size();
        int halfWaySize = halfWaySysSounds.size();
        Map<Integer, ProjFitnessWorkoutProjFitnessVideo> videoDurationMap = new LinkedHashMap<>();
        for (int i = 0; i < ruleSize; i++) {
            String ruleId = ruleIdList.get(i);
            List<ProjFitnessWorkoutDetailVideoVO> relations = relationGroup.get(ruleId);
            int relationSize = relations.size();
            // 视频音频生成
            int rounds = 1;
            for (int j = 0; j < rounds; j++) {
                for (int k = 0; k < relationSize; k++) {
                    BigDecimal startPlayTime = new BigDecimal(htsVideoSegment.getSegmentPointEnd() + "")
                            .divide(new BigDecimal("1000.0"), 1, RoundingMode.UP).add(startTime);
                    ProjFitnessWorkoutDetailVideoVO relationItem = relations.get(k);

                    Integer videoId = relationItem.getId();
                    int frontDuration = relationItem.getFrontVideoDuration();
                    int sideDuration = relationItem.getSideVideoDuration();
                    TsMergeBO frontTs = new TsMergeBO(
                            fileService.getAbsoluteR2Url(relationItem.getFrontVideoUrl()), frontDuration);
                    TsMergeBO sideTs = new TsMergeBO(
                            fileService.getAbsoluteR2Url(relationItem.getSideVideoUrl()), sideDuration);

                    ProjFitnessWorkoutProjFitnessVideo videoDuration;
                    Integer relationId = relationItem.getRelationId();
                    if (videoDurationMap.containsKey(relationId)) {
                        videoDuration = videoDurationMap.get(relationId);
                    } else {
                        videoDuration = new ProjFitnessWorkoutProjFitnessVideo();
                        videoDurationMap.put(relationId, videoDuration);
                    }

                    String halfwayOffSetTime = "26.0";
                    if (UnitNameEnums.WARM_UP.getName().equals(relationItem.getUnitName())) {
                        halfwayOffSetTime = "21.0";
                        // 处理正侧正
                        videoDuration.setPreviewDuration(frontDuration);
                        videoDuration.setMainDuration(sideDuration + frontDuration);
                        htsVideoSegment.addSegment(frontTs, sideTs, frontTs);
                        tsTextMergeBO.addM3u8Text(
                                relationItem.getFrontM3u8Text2k(),
                                relationItem.getFrontM3u8Text1080p(),
                                relationItem.getFrontM3u8Text720p(),
                                relationItem.getFrontM3u8Text480p(),
                                relationItem.getFrontM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                relationItem.getSideM3u8Text2k(),
                                relationItem.getSideM3u8Text1080p(),
                                relationItem.getSideM3u8Text720p(),
                                relationItem.getSideM3u8Text480p(),
                                relationItem.getSideM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                relationItem.getFrontM3u8Text2k(),
                                relationItem.getFrontM3u8Text1080p(),
                                relationItem.getFrontM3u8Text720p(),
                                relationItem.getFrontM3u8Text480p(),
                                relationItem.getFrontM3u8Text360p());
                    } else {
                        // 处理正正侧正
                        videoDuration.setPreviewDuration(frontDuration);
                        videoDuration.setMainDuration(frontDuration + sideDuration + frontDuration);
                        htsVideoSegment.addSegment(frontTs, frontTs, sideTs, frontTs);
                        tsTextMergeBO.addM3u8Text(
                                relationItem.getFrontM3u8Text2k(),
                                relationItem.getFrontM3u8Text1080p(),
                                relationItem.getFrontM3u8Text720p(),
                                relationItem.getFrontM3u8Text480p(),
                                relationItem.getFrontM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                relationItem.getFrontM3u8Text2k(),
                                relationItem.getFrontM3u8Text1080p(),
                                relationItem.getFrontM3u8Text720p(),
                                relationItem.getFrontM3u8Text480p(),
                                relationItem.getFrontM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                relationItem.getSideM3u8Text2k(),
                                relationItem.getSideM3u8Text1080p(),
                                relationItem.getSideM3u8Text720p(),
                                relationItem.getSideM3u8Text480p(),
                                relationItem.getSideM3u8Text360p());
                        tsTextMergeBO.addM3u8Text(
                                relationItem.getFrontM3u8Text2k(),
                                relationItem.getFrontM3u8Text1080p(),
                                relationItem.getFrontM3u8Text720p(),
                                relationItem.getFrontM3u8Text480p(),
                                relationItem.getFrontM3u8Text360p());
                    }

                    boolean isLast = i == ruleSize - 1 && j == rounds - 1 && k == relationSize - 1;
                    Map<String, AudioJsonBO> halfWayAudioMap = halfWaySysSounds.get(new Random().nextInt(halfWaySize));
                    for (String language : languages) {
                        BigDecimal playTime = startPlayTime;
                        if (i == 0 && j == 0 && k == 0) {
                            // first audio
                            Map<String, AudioJsonBO> soundFirstMap = sysSoundMap.get(sysSoundBO.getFirst());
                            this.addAudioJson(
                                    audioListI18nMap, language, soundFirstMap, playTime.add(new BigDecimal("1.0")));
                        } else if (isLast) {
                            // finish audio
                            Map<String, AudioJsonBO> soundFinishMap = sysSoundMap.get(sysSoundBO.getLast());
                            this.addAudioJson(
                                    audioListI18nMap, language, soundFinishMap, playTime.add(new BigDecimal("1.0")));
                        } else {
                            // next audio
                            Map<String, AudioJsonBO> soundNextMap = sysSoundMap.get(sysSoundBO.getReadyFor());
                            this.addAudioJson(
                                    audioListI18nMap, language, soundNextMap, playTime.add(new BigDecimal("1.0")));
                        }

                        // exercise sound name
                        ProjFitnessVideo videoI18n = i18nContainer.get(videoId).get(language);
                        String soundUrl = fileService.getAbsoluteR2Url(videoI18n.getNameAudioUrl());
                        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
                        AudioJsonBO soundBO = new AudioJsonBO(
                                "name:" + videoId,
                                soundUrl, soundName,
                                playTime.add(new BigDecimal("2.5")));
                        audioListI18nMap.get(language).add(soundBO);


                        // three two one audio
                        Map<String, AudioJsonBO> soundThreeTwoOneMap = sysSoundMap.get(sysSoundBO.getThreeTwoOne());
                        this.addAudioJson(
                                audioListI18nMap, language, soundThreeTwoOneMap, playTime.add(new BigDecimal("6.6")));

                        // go audio
                        Map<String, AudioJsonBO> goMap = sysSoundMap.get(sysSoundBO.getGo());
                        this.addAudioJson(
                                audioListI18nMap, language, goMap, playTime.add(new BigDecimal("10.0")));

                        // exercise guidance
                        String guidanceSoundUrl = fileService.getAbsoluteR2Url(videoI18n.getInstructionsAudioUrl());
                        String guidanceName = FireBaseUrlSubUtils.getFileName(guidanceSoundUrl);
                        AudioJsonBO guidanceSoundBO = new AudioJsonBO(
                                "guidance:" + videoId,
                                guidanceSoundUrl, guidanceName,
                                playTime.add(new BigDecimal("11.5")));
                        audioListI18nMap.get(language).add(guidanceSoundBO);

                        // halfway audio
                        this.addAudioJson(
                                audioListI18nMap, language, halfWayAudioMap, playTime.add(new BigDecimal(halfwayOffSetTime)));

                        // three two one audio
                        Map<String, AudioJsonBO> soundEndThreeTwoOneMap = sysSoundMap.get(sysSoundBO.getEndThreeTwoOne());

                        playTime = new BigDecimal(htsVideoSegment.getSegmentPointEnd() + "")
                                .subtract(soundEndThreeTwoOneMap.get(language).getTime())
                                .divide(new BigDecimal("1000.0"), 1, RoundingMode.UP);

                        this.addAudioJson(
                                audioListI18nMap, language, soundEndThreeTwoOneMap, playTime);

                    }

                }
            }

        }

        // 更新每段视频的时长
        for (ProjFitnessWorkoutDetailVideoVO videoVO : relationList) {
            ProjFitnessWorkoutProjFitnessVideo fitnessWorkoutVideo = videoDurationMap.get(videoVO.getRelationId());
            fitnessWorkoutVideo.setId(videoVO.getRelationId());
            projFitnessWorkoutProjFitnessVideoService.updateById(fitnessWorkoutVideo);
        }


        ProjFitnessWorkout workoutFileSave = new ProjFitnessWorkout();
        if (m3u8Req.getVideoFlag()) {
            UploadFileInfoRes videoR2Info = fileService.uploadMergeTSForM3U8R2(htsVideoSegment.getVideoList(), "project-fitness-workout-m3u8");
            UploadFileInfoRes videoDynamicR2Info = fileService.uploadMergeTsTextForM3u8V4(tsTextMergeBO, "project-fitness-workout-m3u8");
            workoutFileSave.setVideoUrl(videoR2Info.getFileRelativeUrl());
            workoutFileSave.setVideoDynamicUrl(videoDynamicR2Info.getFileRelativeUrl());
            workoutFileSave.setDuration(htsVideoSegment.getSegmentPointEnd());
        }

        // 上传音频json
        List<ProjFitnessWorkoutI18n> workoutI18nList = new ArrayList<>();
        if (m3u8Req.getAudioFlag()) {
            for (String language : languages) {
                List<AudioJsonBO> audioList = audioListI18nMap.get(language);
                UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(
                        JacksonUtil.toJsonString(audioList), "project-fitness-workout-json");

                ProjFitnessWorkoutI18n workoutI18n = new ProjFitnessWorkoutI18n();
                workoutI18n.setProjFitnessWorkoutId(id);
                workoutI18n.setLanguage(language);
                workoutI18n.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                workoutI18n.setProjId(workout.getProjId());
                workoutI18nList.add(workoutI18n);
                if (Objects.equals(language, GlobalConstant.DEFAULT_LANGUAGE)) {
                    workoutFileSave.setAudioJsonUrl(audioJsonR2Info.getFileRelativeUrl());
                }
            }
        }

        if (CollectionUtil.isNotEmpty(workoutI18nList)) {
            for (ProjFitnessWorkoutI18n workoutI18n : workoutI18nList) {
                LambdaUpdateWrapper<ProjFitnessWorkoutI18n> wrapper = new LambdaUpdateWrapper<>();
                wrapper.set(ProjFitnessWorkoutI18n::getAudioJsonUrl, workoutI18n.getAudioJsonUrl());
                wrapper.eq(ProjFitnessWorkoutI18n::getProjFitnessWorkoutId, id);
                wrapper.eq(ProjFitnessWorkoutI18n::getLanguage, workoutI18n.getLanguage());
                boolean flag = projFitnessWorkoutI18nService.update(new ProjFitnessWorkoutI18n(), wrapper);
                if (!flag) {
                    projFitnessWorkoutI18nService.save(workoutI18n);
                }
            }
        }

        // 保存最终生成结果
        workoutFileSave.setId(id);
        workoutFileSave.setFileStatus(GlobalConstant.ONE);
        LinkedHashSet<String> audioLanguageSet = new LinkedHashSet<>();
        String audioLanguages = workout.getAudioLanguages();
        if (StrUtil.isNotBlank(audioLanguages)){
            audioLanguageSet.addAll(
                    Arrays.stream(audioLanguages.split(StringPool.COMMA)).collect(Collectors.toList())
            );
        }
        audioLanguageSet.addAll(languages);
        workoutFileSave.setAudioLanguages(StringUtils.join(audioLanguageSet, StringPool.COMMA));
        this.updateById(workoutFileSave);
    }


    /**
     * 音频json添加
     *
     * @param audioListI18nMap audioListI18nMap
     * @param language language
     * @param audioJsonI18nMap audioJsonI18nMap
     * @param playTime playTime
     */
    private void addAudioJson(Map<String, List<AudioJsonBO>> audioListI18nMap, String language,
                                 Map<String, AudioJsonBO> audioJsonI18nMap, BigDecimal playTime) {
        AudioJsonBO sysAudioJson = audioJsonI18nMap.get(language);
        AudioJsonBO audioJsonBO = new AudioJsonBO(sysAudioJson.getId(),
                sysAudioJson.getUrl(), sysAudioJson.getName(), playTime);
        audioListI18nMap.get(language).add(audioJsonBO);
    }


    private Map<Integer, Map<String, ProjFitnessVideo>> getI18nContainer(List<ProjFitnessWorkoutDetailVideoVO> relationList, List<String> languages) {
        // Map<id, Map<language, ProjFitnessVideoI18n>>
        Map<Integer, Map<String, ProjFitnessVideo>> i18nContainer = new HashMap<>(relationList.size());

        for (ProjFitnessWorkoutDetailVideoVO videoVO : relationList) {
            Integer videoId = videoVO.getId();

            Map<String, ProjFitnessVideo> map = getVideoMap(videoVO);
            i18nContainer.put(videoId, map);
        }

        // 英语以外的其他语种
        List<String> excludeEnLanguages = languages.stream().filter(o -> !Objects.equals(o, GlobalConstant.DEFAULT_LANGUAGE)).collect(Collectors.toList());
        if (excludeEnLanguages.isEmpty()) {
            return i18nContainer;
        }

        List<ProjFitnessVideoI18n> videoI18ns = relationList.stream().map(
                ProjFitnessVideoI18n::new).collect(Collectors.toList());

        Map<Object, ProjFitnessVideoI18n> videoI18nMapMap = speechI18nPubService
                .getI18nModelGroupByKey(videoI18ns, excludeEnLanguages, ProjCodeEnums.OOG104);


        for (ProjFitnessVideoI18n i18n : videoI18ns) {
            ProjFitnessVideoI18n videoI18nMap = videoI18nMapMap.get(i18n.getUniqueKey());
            if (videoI18nMap == null) {
                throw new BizException("The Video (id: " + i18n + ") data translation language incomplete");
            }
            Map<String, ProjFitnessVideo> audioI18nMap = i18nContainer.get(i18n.getUniqueKey());
            Map<LanguageEnums, AudioTranslateResultModel> instructionsResultMap = CollUtil.emptyIfNull(i18n.getInstructionsResult())
                            .stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, Function.identity(),
                            (m, n) -> m));
            Map<LanguageEnums, AudioTranslateResultModel> nameResultMap = CollUtil.emptyIfNull(i18n.getResult())
                    .stream().collect( Collectors.toMap(AudioTranslateResultModel::getLanguage, Function.identity(),
                            (m, n) -> m));
            for (String language : excludeEnLanguages) {

                if (!nameResultMap.containsKey(LanguageEnums.getByNameIgnoreCase(language)) ||
                        !instructionsResultMap.containsKey(LanguageEnums.getByNameIgnoreCase(language))) {
                    throw new BizException("The Video (id: " + i18n + ") data translation language (" + language + ") not found");
                }
                AudioTranslateResultModel nameResult = nameResultMap.get(LanguageEnums.getByNameIgnoreCase(language));
                AudioTranslateResultModel instructionsResult = instructionsResultMap.get(LanguageEnums.getByNameIgnoreCase(language));

                if (StringUtils.isBlank(nameResult.getAudioUrl())
                        || Objects.isNull(nameResult.getDuration())
                        || StringUtils.isBlank(instructionsResult.getAudioUrl())
                        || Objects.isNull(instructionsResult.getDuration())
                ) {
                    throw new BizException("The Video (id: " + i18n + ") data translation language ("
                            + language + ") fields values has empty");
                }
                ProjFitnessVideo videoI18n = getVideoI18n(nameResult, instructionsResult);
                audioI18nMap.put(language, videoI18n);
            }
        }
        return i18nContainer;
    }

    private Map<String, ProjFitnessVideo> getVideoMap(ProjFitnessWorkoutDetailVideoVO videoVO) {
        Map<String, ProjFitnessVideo> map = new HashMap<>();
        ProjFitnessVideo videoI18n = new ProjFitnessVideo();
        videoI18n.setName(videoVO.getName());
        videoI18n.setNameAudioUrl(videoVO.getNameAudioUrl());
        videoI18n.setNameAudioDuration(videoVO.getNameAudioDuration());
        videoI18n.setInstructions(videoVO.getInstructions());
        videoI18n.setInstructionsAudioUrl(videoVO.getInstructionsAudioUrl());
        videoI18n.setInstructionsAudioDuration(videoVO.getInstructionsAudioDuration());

        map.put(GlobalConstant.DEFAULT_LANGUAGE, videoI18n);
        return map;
    }

    private ProjFitnessVideo getVideoI18n(AudioTranslateResultModel nameResult, AudioTranslateResultModel instructionsResult) {
        ProjFitnessVideo videoI18n = new ProjFitnessVideo();
        videoI18n.setName(nameResult.getText());
        videoI18n.setNameAudioUrl(nameResult.getAudioUrl());
        videoI18n.setNameAudioDuration(nameResult.getDuration());
        videoI18n.setInstructions(instructionsResult.getText());
        videoI18n.setInstructionsAudioUrl(instructionsResult.getAudioUrl());
        videoI18n.setInstructionsAudioDuration(instructionsResult.getDuration());
        return videoI18n;
    }

}
