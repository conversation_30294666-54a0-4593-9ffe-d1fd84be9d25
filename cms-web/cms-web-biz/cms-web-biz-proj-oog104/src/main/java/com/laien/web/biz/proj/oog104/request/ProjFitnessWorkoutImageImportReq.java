/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog104.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <p>ProjFitnessWorkoutImage 导入对象 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Accessors(chain = true)
public class ProjFitnessWorkoutImageImportReq {

    @ExcelProperty(index = 0,value = "id")
    private Integer id;

    @Length(max = 100,message = "name length cannot be more than 100 characters")
    @NotBlank(message = "name cannot be blank")
    @ExcelProperty(index = 1,value = "name")
    private String name;

    @NotBlank(message = "exclusive_type cannot be blank")
    @Pattern(regexp = "^(Normal|Pregnant|Postpartum|Injury|Menopause|Daily Stretch|Daily HIIT|Daily Abs|Daily Booty|Daily Fat Loss)$",message = "exclusive_type is invalid")
    @ExcelProperty(index = 2,value = "exclusive_type")
    private String exclusiveType;

    @Pattern(regexp = "^(<18|18~24|25~34|35~44|45~54|≥55)(,(<18|18~24|25~34|35~44|45~54|≥55))*$",message = "age_group is invalid")
    @ExcelProperty(index = 3,value = "age_group")
    private String ageGroup;

    @Pattern(regexp = "^(None|Arms|Back|Abs|Butt|Legs|Full Body)$",message = "target is invalid")
    @ExcelProperty(index = 4,value = "target")
    private String target;

    @Pattern(regexp = "^(Newbie|Beginner|Intermediate|Advanced)$",message = "difficulty is invalid")
    @ExcelProperty(index = 5,value = "difficulty")
    private String difficulty;

    @Pattern(regexp = "^(None|Stretch|Cardio|Hiit|Power)$",message = "intensity is invalid")
    @ExcelProperty(index = 6,value = "intensity")
    private String intensity;

    @Pattern(regexp = "^(None|Wrists|Feet|Back|Shoulders|Abs|Knees)(,(None|Wrists|Feet|Back|Shoulders|Abs|Knees))*$",message = "special_limit is invalid")
    @ExcelProperty(index = 7,value = "special_limit")
    private String specialLimit;

    @Length(max = 255,message = "cover_image length cannot be more than 255 characters")
    @Pattern(regexp = "^(?=.{1,255}$).*/[^/]+\\.(png|webp)(\\?.*)?$",message = "cover_image is invalid")
    @NotBlank(message = "cover_image cannot be blank")
    @ExcelProperty(index = 8,value = "cover_image")
    private String coverImage;

    @Length(max = 255,message = "detail_image length cannot be more than 255 characters")
    @Pattern(regexp = "^(?=.{1,255}$).*/[^/]+\\.(png|webp)(\\?.*)?$",message = "detail_image is invalid")
    @NotBlank(message = "cover_image cannot be blank")
    @ExcelProperty(index = 9,value = "detail_image")
    private String detailImage;

    @NotBlank(message = "template_type cannot be blank")
    @Pattern(regexp = "^(Regular Fitness|Wall Pilates|Classic Yoga|Chair Yoga|106 Fitness|Pilates)$",message = "template_type is invalid")
    @ExcelProperty(index = 10,value = "template_type")
    private String templateType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExclusiveType() {
        return exclusiveType;
    }

    public void setExclusiveType(String exclusiveType) {
        this.exclusiveType = exclusiveType;
    }

    public String getAgeGroup() {
        return ageGroup;
    }

    public void setAgeGroup(String ageGroup) {
        this.ageGroup = ageGroup;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public String getIntensity() {
        return intensity;
    }

    public void setIntensity(String intensity) {
        this.intensity = intensity;
    }

    public String getSpecialLimit() {
        return specialLimit;
    }

    public void setSpecialLimit(String specialLimit) {
        this.specialLimit = specialLimit;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public String getDetailImage() {
        return detailImage;
    }

    public void setDetailImage(String detailImage) {
        this.detailImage = detailImage;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }
}