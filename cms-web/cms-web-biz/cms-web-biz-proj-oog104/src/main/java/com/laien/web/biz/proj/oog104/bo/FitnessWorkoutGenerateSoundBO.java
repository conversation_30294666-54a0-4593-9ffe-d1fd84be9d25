package com.laien.web.biz.proj.oog104.bo;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessSound;
import com.laien.web.biz.proj.oog104.enums.AudioCategoryEnums;
import com.laien.web.biz.proj.oog104.service.IProjFitnessSoundService;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.exception.BizException;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/3/20 13:52
 */
@Data
@Accessors(chain = true)
public class FitnessWorkoutGenerateSoundBO {

    private AudioJson104BO first;

    private AudioJson104BO getReady;

    private AudioJson104BO last;

    private AudioJson104BO threeTwoOne;

    private AudioJson104BO go;

    private List<AudioJson104BO> promptList;

    private Map<String, FitnessWorkoutGenerateSoundBO> soundI18nMap;

    public List<AudioJson104BO> getAudioList(List<String> audioList, AudioCategoryEnums category) {

        return audioList.stream().map(audioName -> getSoundByName(category, audioName)).collect(Collectors.toList());

    }

    public AudioJson104BO getSoundByName(AudioCategoryEnums category, String name) {

        LambdaQueryWrapper<ProjFitnessSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessSound::getSoundName, name).last("limit 1");
        IProjFitnessSoundService fitnessSoundService = SpringUtil.getBean(IProjFitnessSoundService.class);
        FileService fileService = SpringUtil.getBean(FileService.class);

        ProjFitnessSound sound = fitnessSoundService.getOne(queryWrapper);
        if (sound == null) {
            throw new BizException("System sound '" + category + "' not find!");
        }

        String soundUrl = sound.getUrl();
        if (StringUtils.isBlank(soundUrl)) {
            throw new BizException("System sound '" + category + "' not set!");
        }

        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);
        return new AudioJson104BO(category, soundName, fileService.getAbsoluteR2Url(soundUrl), soundName,
                BigDecimal.ZERO, sound.getDuration(), false,
                sound.getId(), sound.getNeedTranslation(),
                sound.getCoreVoiceConfigI18nId(),sound.getSoundScript(),sound.getGender());

    }

}
