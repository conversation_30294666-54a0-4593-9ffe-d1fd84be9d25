package com.laien.web.biz.proj.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessIngredientVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessIngredientVO对象", description="ProjFitnessIngredientVO")
public class ProjFitnessIngredientVO {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private String amount;

    @ApiModelProperty(value = "projFitnessUnit表数据id")
    private Integer projFitnessUnitId;

}
