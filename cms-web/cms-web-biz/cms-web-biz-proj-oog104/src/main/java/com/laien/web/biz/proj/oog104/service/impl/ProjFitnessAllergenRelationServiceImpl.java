package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessAllergenRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessAllergenRelationMapper;
import com.laien.web.biz.proj.oog104.service.IProjFitnessAllergenRelationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Fitness Allergen Relation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@RequiredArgsConstructor
public class ProjFitnessAllergenRelationServiceImpl extends ServiceImpl<ProjFitnessAllergenRelationMapper, ProjFitnessAllergenRelation> implements IProjFitnessAllergenRelationService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<Integer> allergenIdList, Integer projId, Integer dataId, FitnessAllergenRelationBusinessEnums businessType) {
        LambdaUpdateWrapper<ProjFitnessAllergenRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjFitnessAllergenRelation::getDataId, dataId)
                .eq(ProjFitnessAllergenRelation::getBusinessType, businessType)
                .eq(ProjFitnessAllergenRelation::getProjId, projId);
        baseMapper.delete(wrapper);
        if(CollUtil.isEmpty(allergenIdList)){
            return;
        }
        List<ProjFitnessAllergenRelation> relationList = new ArrayList<>();
        for (Integer allergenId : allergenIdList) {
            ProjFitnessAllergenRelation relation = new ProjFitnessAllergenRelation();
            relation.setDataId(dataId)
                    .setProjFitnessAllergenId(allergenId)
                    .setBusinessType(businessType)
                    .setProjId(projId);
            relationList.add(relation);
        }
        saveBatch(relationList);
    }

    @Override
    public List<ProjFitnessAllergenRelation> query(Integer dishId, FitnessAllergenRelationBusinessEnums businessType) {
        LambdaQueryWrapper<ProjFitnessAllergenRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessAllergenRelation::getDataId, dishId)
                .eq(ProjFitnessAllergenRelation::getBusinessType, businessType);
        return baseMapper.selectList(wrapper);
    }
}
