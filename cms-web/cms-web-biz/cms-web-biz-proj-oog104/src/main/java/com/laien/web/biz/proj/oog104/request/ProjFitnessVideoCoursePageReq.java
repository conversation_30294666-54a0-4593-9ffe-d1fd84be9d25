package com.laien.web.biz.proj.oog104.request;


import com.laien.common.oog104.enums.course.FitnessCoursePlayTypeEnums;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * <p>
 * ProjFitnessVideoCourseListReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@ApiModel(value = "ProjFitnessVideoCourse列表筛选条件", description = "ProjFitnessVideoCourse列表筛选条件")
public class ProjFitnessVideoCoursePageReq extends PageReq {

    @ApiModelProperty(value = "dish Id 集合",hidden = true)
    private Collection<Integer> ids;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "playType")
    private FitnessCoursePlayTypeEnums playType;

    @ApiModelProperty(value = "type")
    private FitnessCourseTypeEnums type;

    @ApiModelProperty(value = "courseType")
    private ManualDifficultyEnums difficulty;

}
