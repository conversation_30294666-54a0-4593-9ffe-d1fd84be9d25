package com.laien.web.biz.proj.oog104.enums;

import lombok.Getter;

/**
 * fit类型枚举
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Getter
public enum FitTypeEnum implements IEnumBase {

    NONE(0, "None", "None"),
    HIIT(1, "HIIT", "HIIT"),
    CARD<PERSON>(2, "Cardio", "Cardio"),
    PILATES(3, "Pilates", "Pilates"),
    STRETCH(4, "Stretch", "Stretch");

    private final Integer code;
    private final String name;
    private final String displayName;

    FitTypeEnum(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static FitTypeEnum getBy(Integer code) {
        for (FitTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getEnumName() {
        return this.name();
    }
}
