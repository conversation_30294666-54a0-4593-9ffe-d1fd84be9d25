package com.laien.web.biz.proj.oog104.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_plan_proj_fitness_workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessPlanProjFitnessWorkout对象", description="proj_fitness_plan_proj_fitness_workout")
public class ProjFitnessPlanProjFitnessWorkout extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "plan id")
    private Integer projFitnessPlanId;

    @ApiModelProperty(value = "workout id")
    private Integer projFitnessWorkoutId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
