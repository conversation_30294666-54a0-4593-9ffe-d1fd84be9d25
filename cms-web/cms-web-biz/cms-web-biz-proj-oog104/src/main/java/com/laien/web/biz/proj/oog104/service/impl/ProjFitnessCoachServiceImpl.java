package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoach;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessCoachMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessCoachMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCoachService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * Fitness Dish 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessCoachServiceImpl extends ServiceImpl<ProjFitnessCoachMapper, ProjFitnessCoach> implements IProjFitnessCoachService {

    private final ProjFitnessCoachMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessCoachVO> list(ProjFitnessCoachListReq listReq, Integer projId) {
        LambdaQueryWrapper<ProjFitnessCoach> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(listReq.getName()), ProjFitnessCoach::getName, listReq.getName())
                .eq(ObjUtil.isNotNull(listReq.getStatus()), ProjFitnessCoach::getStatus, listReq.getStatus())
                .orderByDesc(BaseModel::getId);
        return mapStruct.toVOList(baseMapper.selectList(wrapper));
    }


    @Override
    public ProjFitnessCoachVO findDetailById(Integer id) {
        return mapStruct.toVO(baseMapper.selectById(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjFitnessCoachUpdateReq req, Integer projId) {
        Integer id = req.getId();
        this.check(id,req,projId);
        ProjFitnessCoach entity = mapStruct.toEntity(req);
        entity.setId(id);
        this.updateById(entity);
        projLmsI18nService.handleI18n(Collections.singletonList(entity),projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjFitnessCoachAddReq req, Integer projId) {
        check(null, req, projId);
        ProjFitnessCoach dish = mapStruct.toEntity(req);
        dish.setProjId(projId);
        save(dish);
        projLmsI18nService.handleI18n(Collections.singletonList(dish), projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessCoach> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessCoach::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjFitnessCoach::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjFitnessCoach::getId, idList);
        this.update(new ProjFitnessCoach(), updateWrapper);
        return idList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessCoach> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessCoach::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFitnessCoach::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessCoach::getId, idList);
        this.update(new ProjFitnessCoach(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessCoach> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessCoach::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjFitnessCoach::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjFitnessCoach::getId, idList);
        this.update(new ProjFitnessCoach(), wrapper);
    }

    private void check(Integer id, ProjFitnessCoachAddReq req, Integer projId) {
        LambdaQueryWrapper<ProjFitnessCoach> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessCoach::getName, req.getName())
                .eq(ProjFitnessCoach::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjFitnessCoach> dishList = baseMapper.selectList(wrapper);
        Set<String> nameSet = dishList.stream().map(ProjFitnessCoach::getName).collect(Collectors.toSet());
        if (nameSet.contains(req.getName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
    }
}
