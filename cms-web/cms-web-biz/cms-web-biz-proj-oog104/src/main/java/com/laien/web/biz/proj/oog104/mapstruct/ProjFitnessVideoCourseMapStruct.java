package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideoCourse;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseAddReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseListVO;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *     ProjFitnessVideoCourseMapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessVideoCourseMapStruct {
    List<ProjFitnessVideoCourseListVO> toVOList(Collection<ProjFitnessVideoCourse> records);

    ProjFitnessVideoCourseDetailVO toDetailVO(ProjFitnessVideoCourse dish);

    ProjFitnessVideoCourse toEntity(ProjFitnessVideoCourseAddReq req);
}
