package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *ProjFitnessVideoCourseUpdateReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="ProjVideoCourse对象", description="VideoCourse")
public class ProjFitnessVideoCourseUpdateReq extends ProjFitnessVideoCourseAddReq{

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
