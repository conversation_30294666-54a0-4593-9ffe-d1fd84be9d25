package com.laien.web.biz.proj.oog104.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_workout_generate_file_task
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessWorkoutGenerateFileTask对象", description="proj_fitness_workout_generate_file_task")
public class ProjFitnessWorkoutGenerateFileTask extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否生成视频")
    private Boolean videoFlag;

    @ApiModelProperty(value = "是否生成音频")
    private Boolean audioFlag;

    @ApiModelProperty(value = "选中生成的workout id list")
    private String workoutIds;

    @ApiModelProperty(value = "选中生成的语言，多个逗号分隔")
    private String languages;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
