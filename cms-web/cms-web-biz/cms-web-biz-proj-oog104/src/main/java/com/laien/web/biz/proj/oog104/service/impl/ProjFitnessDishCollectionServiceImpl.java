package com.laien.web.biz.proj.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog104.enums.dish.FitnessDishTypeEnums;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishCollection;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishCollectionRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessDishCollectionMapper;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionUpdateReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishListReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDailyDishDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishCollectionDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishCollectionListVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishCollectionRelationService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishCollectionService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/2 11:52
 */
@Slf4j
@Service
public class ProjFitnessDishCollectionServiceImpl extends ServiceImpl<ProjFitnessDishCollectionMapper, ProjFitnessDishCollection> implements IProjFitnessDishCollectionService {

    @Resource
    private IProjFitnessDishCollectionService dishCollectionService;

    @Resource
    private IProjFitnessDishCollectionRelationService collectionRelationService;

    @Resource
    private IProjFitnessDishService dishService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessDishCollectionListVO> selectDishCollection(ProjFitnessDishCollectionListReq listReq) {

        LambdaQueryWrapper<ProjFitnessDishCollection> queryWrapper = wrapQueryWrapper(listReq);
        List<ProjFitnessDishCollection> dishCollectionList = list(queryWrapper);
        if (CollectionUtils.isEmpty(dishCollectionList)) {
            return Collections.emptyList();
        }

        return dishCollectionList.stream().map(dishCollection -> convert2ListVO(dishCollection)).collect(Collectors.toList());
    }

    private ProjFitnessDishCollectionListVO convert2ListVO(ProjFitnessDishCollection ProjFitnessDishCollection) {

        ProjFitnessDishCollectionListVO listVO = new ProjFitnessDishCollectionListVO();
        BeanUtils.copyProperties(ProjFitnessDishCollection, listVO);
        return listVO;
    }

    private LambdaQueryWrapper<ProjFitnessDishCollection> wrapQueryWrapper(ProjFitnessDishCollectionListReq pageReq) {

        LambdaQueryWrapper<ProjFitnessDishCollection> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(!StringUtils.isEmpty(pageReq.getName()), ProjFitnessDishCollection::getName, pageReq.getName());
        queryWrapper.eq(Objects.nonNull(pageReq.getStatus()), ProjFitnessDishCollection::getStatus, pageReq.getStatus());
        queryWrapper.orderByAsc(ProjFitnessDishCollection::getSorted);
        queryWrapper.orderByDesc(ProjFitnessDishCollection::getId);
        return queryWrapper;
    }

    @Override
    public void saveDishCollection(ProjFitnessDishCollectionAddReq addReq) {

        ProjFitnessDishCollection dishCollection = new ProjFitnessDishCollection();
        BeanUtils.copyProperties(addReq, dishCollection);
        checkMealPlan(addReq, null);

        dishCollection.setStatus(GlobalConstant.STATUS_DRAFT);
        dishCollection.setProjId(RequestContextUtils.getProjectId());
        save(dishCollection);

        if (CollectionUtils.isEmpty(addReq.getDailyDishList())) {
            return;
        }
        List<ProjFitnessDishCollectionRelation> collectionRelations = addReq.getDailyDishList().stream().map(dish -> wrapCollRelation(dish, dishCollection.getId(), dishCollection.getProjId())).collect(Collectors.toList());
        collectionRelationService.saveBatch(collectionRelations);

        projLmsI18nService.handleI18n(Collections.singletonList(dishCollection), dishCollection.getProjId());
    }

    private void checkMealPlan(ProjFitnessDishCollectionAddReq dishCollectionAddReq, Integer id) {

        LambdaQueryWrapper<ProjFitnessDishCollection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessDishCollection::getName, dishCollectionAddReq.getName())
                .eq(ProjFitnessDishCollection::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjFitnessDishCollection::getId, id);
        boolean exist = this.count(queryWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Dish collection name exists.");
        }

        LambdaQueryWrapper<ProjFitnessDishCollection> queryEventWrapper = new LambdaQueryWrapper<>();
        queryEventWrapper.eq(ProjFitnessDishCollection::getEventName, dishCollectionAddReq.getEventName())
                .eq(ProjFitnessDishCollection::getProjId, RequestContextUtils.getProjectId())
                .ne(Objects.nonNull(id), ProjFitnessDishCollection::getId, id);
        exist = this.count(queryEventWrapper) > GlobalConstant.ZERO;
        if (exist) {
            throw new BizException("Dish collection event name exists.");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateDishCollection(ProjFitnessDishCollectionUpdateReq updateReq) {

        ProjFitnessDishCollection dishCollection = this.getById(updateReq.getId());
        if (Objects.isNull(dishCollection)) {
            throw new BizException("Can't find dish collection.");
        }

        BeanUtils.copyProperties(updateReq, dishCollection);
        updateById(dishCollection);
        checkMealPlan(updateReq, updateReq.getId());
        updateNewTime(updateReq);

        handleCollectionRelation(updateReq);

        projLmsI18nService.handleI18n(Collections.singletonList(dishCollection), dishCollection.getProjId());
    }

    private void handleCollectionRelation(ProjFitnessDishCollectionUpdateReq updateReq) {

        collectionRelationService.deleteByDishCollectionId(updateReq.getId());
        if (CollectionUtils.isEmpty(updateReq.getDailyDishList())) {
            return;
        }

        List<ProjFitnessDishCollectionRelation> collectionRelations = updateReq.getDailyDishList().stream().map(dish -> wrapCollRelation(dish, updateReq.getId(), RequestContextUtils.getProjectId())).collect(Collectors.toList());
        collectionRelationService.saveBatch(collectionRelations);
    }

    private ProjFitnessDishCollectionRelation wrapCollRelation(ProjFitnessDailyDishDetailVO dailyDish, Integer collectionId, Integer projId) {

        ProjFitnessDishCollectionRelation collectionRelation = new ProjFitnessDishCollectionRelation();
        collectionRelation.setProjFitnessDishId(dailyDish.getId());
        collectionRelation.setProjId(projId);
        collectionRelation.setDishType(dailyDish.getDishType());
        collectionRelation.setProjFitnessDishCollectionId(collectionId);
        return collectionRelation;
    }

    private void updateNewTime(ProjFitnessDishCollectionUpdateReq updateReq) {

        LambdaUpdateWrapper<ProjFitnessDishCollection> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessDishCollection::getNewStartTime, updateReq.getNewStartTime());
        updateWrapper.set(ProjFitnessDishCollection::getNewEndTime, updateReq.getNewEndTime());
        updateWrapper.eq(ProjFitnessDishCollection::getId, updateReq.getId());
        this.update(updateWrapper);
    }

    @Override
    public ProjFitnessDishCollectionDetailVO getDetailById(Integer dishCollectionId) {

        ProjFitnessDishCollection dishCollection = this.getById(dishCollectionId);
        if (Objects.isNull(dishCollection)) {
            throw new BizException("Can't find dish collection.");
        }

        ProjFitnessDishCollectionDetailVO collectionDetailVO = new ProjFitnessDishCollectionDetailVO();
        BeanUtils.copyProperties(dishCollection, collectionDetailVO);

        List<ProjFitnessDailyDishDetailVO> dishList = getDishList(dishCollectionId);
        collectionDetailVO.setDailyDishList(dishList);
        return collectionDetailVO;
    }

    private List<ProjFitnessDailyDishDetailVO> getDishList(Integer dishCollectionId) {

        List<ProjFitnessDishCollectionRelation> dishCollectionRelationList = collectionRelationService.listByDishCollectionId(dishCollectionId);
        if(CollectionUtils.isEmpty(dishCollectionRelationList)) {
            return Collections.emptyList();
        }

        List<Integer> dishIds = dishCollectionRelationList.stream().map(ProjFitnessDishCollectionRelation::getProjFitnessDishId).collect(Collectors.toList());
        ProjFitnessDishListReq dishListReq = new ProjFitnessDishListReq();
        dishListReq.setDishIds(dishIds);

        List<ProjFitnessDishListVO> dishList = dishService.list(dishListReq, RequestContextUtils.getProjectId());
        if (CollectionUtils.isEmpty(dishList)) {
            return Collections.emptyList();
        }

        Map<Integer, ProjFitnessDishListVO> dishIdMap = dishList.stream().collect(Collectors.toMap(ProjFitnessDishListVO::getId, Function.identity(), (k1, k2) -> k1));
        return dishCollectionRelationList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjFitnessDishId())).map(relation -> convert2DailyVO(relation.getDishType(), dishIdMap.get(relation.getProjFitnessDishId()))).collect(Collectors.toList());
    }

    private ProjFitnessDailyDishDetailVO convert2DailyVO(FitnessDishTypeEnums dishType, ProjFitnessDishListVO dishList) {

        ProjFitnessDailyDishDetailVO dailyDishDetailVO = new ProjFitnessDailyDishDetailVO();
        BeanUtils.copyProperties(dishList, dailyDishDetailVO);
        dailyDishDetailVO.setDishType(dishType);
        return dailyDishDetailVO;
    }

    @Override
    public void sort(IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        List<ProjFitnessDishCollection> dishCollections = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjFitnessDishCollection dishCollection = new ProjFitnessDishCollection();
            dishCollection.setSorted(i).setId(idList.get(i));
            dishCollections.add(dishCollection);
        }
        updateBatchById(dishCollections);
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjFitnessDishCollection> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessDishCollection::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessDishCollection::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjFitnessDishCollection::getId, idList);
        this.update(new ProjFitnessDishCollection(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjFitnessDishCollection> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessDishCollection::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFitnessDishCollection::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessDishCollection::getId, idList);
        this.update(new ProjFitnessDishCollection(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjFitnessDishCollection> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessDishCollection::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjFitnessDishCollection::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjFitnessDishCollection::getId, idList);
        this.update(new ProjFitnessDishCollection(), wrapper);
    }

}
