package com.laien.web.biz.proj.oog104.bo;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.web.frame.constant.GlobalConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author:  hhl
 * Date:  2025/3/17 21:31
 */
@NoArgsConstructor
@Data
public class BaseGenerateVideoBO {

    @ApiModelProperty(value = "单视频正侧循环次数")
    private Integer videoRound;

    @ApiModelProperty(value = "preview 循环次数")
    private Integer previewRound = Integer.valueOf(GlobalConstant.ONE);

    @ApiModelProperty(value = "所属规则组")
    private Integer groupId;

    @ApiModelProperty(value = "当前使用视频")
    private ProjFitnessExerciseVideo video;

    @ApiModelProperty(value = "preview_duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "video_duration")
    private Integer videoDuration;

    public BaseGenerateVideoBO(Integer videoRound, Integer groupId, ProjFitnessExerciseVideo video) {
        this.videoRound = videoRound;
        this.groupId = groupId;
        this.video = video;
    }
}
