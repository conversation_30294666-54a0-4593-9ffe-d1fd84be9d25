package com.laien.web.biz.proj.oog104.request;

import com.laien.web.biz.proj.oog104.enums.PlanTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "plan 新增", description = "plan 新增")
public class ProjFitnessPlanAddReq {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "plan类型")
    private PlanTypeEnums type;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "Stage Counts")
    private List<Integer> stageCounts;

    @ApiModelProperty(value = "字体颜色")
    private String fontColor;

    @ApiModelProperty(value = "背景颜色")
    private String bgColor;

    @NotEmpty(message = "tags cannot be empty")
    @ApiModelProperty(value = "tags")
    private List<String> tags;

    @ApiModelProperty(value = "描述")
    private String description;

    @NotEmpty(message = "expectedResults cannot be empty")
    @ApiModelProperty(value = "预期结果")
    private List<String> expectedResults;

    @NotEmpty(message = "workoutList cannot be empty")
    @ApiModelProperty(value = "workout list")
    private List<ProjFitnessPlanAddWorkoutReq> workoutList;

}
