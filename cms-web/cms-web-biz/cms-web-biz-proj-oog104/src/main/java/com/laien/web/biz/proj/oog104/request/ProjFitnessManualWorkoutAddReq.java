package com.laien.web.biz.proj.oog104.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualEquipmentEnums;
import com.laien.common.oog104.enums.manual.ManualIntensityEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.manual.ManualWorkoutTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Manual Workout Add Request
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
public class ProjFitnessManualWorkoutAddReq implements Serializable {

    private static final long serialVersionUID = -1928688074430936123L;

    @ApiModelProperty(value = "Exercise Name")
    private String name;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    private String coverImage;

    @ApiModelProperty(value = "Detail Image, supports png/webp formats")
    private String detailImage;

    @ApiModelProperty(value = "Workout Type")
    private ManualWorkoutTypeEnums workoutType;

    @ApiModelProperty(value = "Age Groups")
    private List<ManualAgeGroupEnums> ageGroup;

    @ApiModelProperty(value = "Target Areas")
    private List<ManualTargetEnums> target;

    @ApiModelProperty(value = "Difficulty")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "Equipment")
    private List<ManualEquipmentEnums> equipment;

    @ApiModelProperty(value = "Special Limits")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "Intensity")
    private ManualIntensityEnums intensity;

    @ApiModelProperty(value = "Categories")
    private List<ManualCategoryEnums> category;

    @ApiModelProperty(value = "Description, maximum 1000 characters")
    private String description;

    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;

    @ApiModelProperty(value = "Video List")
    private List<ProjFitnessManualWorkoutAddVideoReq> videoList;

}
