package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>修改模板的启用状态</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 18:14
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "video 新增", description = "video 新增")
public class ProjFitnessTemplateUpdateEnableReq {

    @ApiModelProperty(value = "模板id列表")
    private List<Integer> templateIds;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;
}
