package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoachingCourses;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesListVO;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * Fitness CoachingCourses 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
public interface IProjFitnessCoachingCoursesService extends IService<ProjFitnessCoachingCourses> {

    List<ProjFitnessCoachingCoursesListVO> list(ProjFitnessCoachingCoursesPageReq listReq, Integer projId);

    ProjFitnessCoachingCoursesDetailVO findDetailById(Integer id);

    void update(ProjFitnessCoachingCoursesUpdateReq req, Integer projectId);

    void save(ProjFitnessCoachingCoursesAddReq dishReq, Integer projId);

    List<Integer> updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);

    @Transactional(rollbackFor = Exception.class)
    void sort(IdListReq idListReq);
}
