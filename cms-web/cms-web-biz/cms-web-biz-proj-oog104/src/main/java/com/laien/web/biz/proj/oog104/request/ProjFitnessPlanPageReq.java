package com.laien.web.biz.proj.oog104.request;

import com.laien.web.biz.proj.oog104.enums.PlanTypeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "plan 分页", description = "plan 分页")
public class ProjFitnessPlanPageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "plan类型")
    private PlanTypeEnums type;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

}
