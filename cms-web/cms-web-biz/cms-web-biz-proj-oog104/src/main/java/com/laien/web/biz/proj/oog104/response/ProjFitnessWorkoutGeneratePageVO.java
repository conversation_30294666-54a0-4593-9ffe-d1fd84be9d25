package com.laien.web.biz.proj.oog104.response;

import com.laien.common.oog104.enums.manual.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 健身训练生成记录分页查询响应
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@ApiModel(value = "健身训练生成记录分页查询响应")
public class ProjFitnessWorkoutGeneratePageVO {

    @ApiModelProperty(value = "ID")
    private Integer id;

    @ApiModelProperty(value = "模板ID")
    private Integer projFitnessTemplateId;

    @ApiModelProperty(value = "任务ID")
    private Integer projFitnessTemplateTaskId;

    @ApiModelProperty(value = "目标")
    private ManualTargetEnums target;

    @ApiModelProperty(value = "难度")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "器材")
    private ManualEquipmentEnums equipment;

    @ApiModelProperty(value = "特殊限制(多选)")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "强度(多选)")
    private List<ManualIntensityEnums> intensity;

    @ApiModelProperty(value = "时长(秒)")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "视频URL")
    private String videoUrl;

    @ApiModelProperty(value = "音频JSON URL")
    private String audioJsonUrl;

    @ApiModelProperty(value = "音频语言")
    private String audioLanguages;

    @ApiModelProperty(value = "文件状态 0:运行中 1:成功 2:失败")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "生成数量")
    private Integer generateNum;

    @ApiModelProperty(value = "是否清空之前的workout 1是 0否")
    private Integer cleanUp;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "部位")
    private ManualPositionEnums position;

    @ApiModelProperty(value = "Workout Type")
    private ManualExerciseTypeEnums workoutType;

}
