package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDishCollection;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishCollectionUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishCollectionDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishCollectionListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/31 15:15
 */
public interface IProjFitnessDishCollectionService extends IService<ProjFitnessDishCollection> {

    /**
     * ProjFitnessDishCollection列表查询
     *
     * @param listReq listReq
     * @return ProjFitnessDishCollectionListVO
     */
    List<ProjFitnessDishCollectionListVO> selectDishCollection(ProjFitnessDishCollectionListReq listReq);

    /**
     * ProjFitnessDishCollection新增
     *
     * @param addReq
     */
    void saveDishCollection(ProjFitnessDishCollectionAddReq addReq);

    /**
     * ProjFitnessDishCollection修改
     *
     * @param updateReq
     */
    void updateDishCollection(ProjFitnessDishCollectionUpdateReq updateReq);

    /**
     * ProjMealPlan详情
     *
     * @param dishCollectionId
     * @return ProjFitnessDishCollectionDetailVO
     */
    ProjFitnessDishCollectionDetailVO getDetailById(Integer dishCollectionId);

    /**
     * 排序
     *
     * @param idListReq
     */
    void sort(IdListReq idListReq);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
