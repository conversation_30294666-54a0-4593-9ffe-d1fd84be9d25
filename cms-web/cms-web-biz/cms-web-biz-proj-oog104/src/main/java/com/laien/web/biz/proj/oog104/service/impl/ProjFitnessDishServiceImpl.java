package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;
import com.laien.common.oog104.enums.dish.FitnessDishStyleEnums;
import com.laien.common.oog104.enums.dish.FitnessDishTypeEnums;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessAllergenRelation;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDish;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessDishMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessDishMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishListVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishStepVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessIngredientVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessAllergenRelationService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessDishStepService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessIngredientService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_FITNESS_DISH;

/**
 * <p>
 * Fitness Dish 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessDishServiceImpl extends ServiceImpl<ProjFitnessDishMapper, ProjFitnessDish> implements IProjFitnessDishService {

    private final ProjFitnessDishMapStruct mapStruct;
    private final IProjFitnessDishStepService dishStepService;
    private final IProjFitnessAllergenRelationService allergenRelationService;
    private final IProjFitnessIngredientService ingredientService;
    private final ITaskResourceSectionService taskResourceSectionService;
    private final IProjLmsI18nService projLmsI18nService;


    @Override
    public List<ProjFitnessDishListVO> list(ProjFitnessDishListReq listReq, Integer projId) {
        FitnessDishTypeEnums type = listReq.getType();
        Integer typeCode = null;
        if (null != type) {
            typeCode = type.getCode();
        }
        FitnessDishStyleEnums style = listReq.getStyle();
        Integer styleCode = null;
        if(null != style){
            styleCode = style.getCode();
        }
        LambdaQueryWrapper<ProjFitnessDish> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(listReq.getName()), ProjFitnessDish::getName, listReq.getName())
                .in(CollectionUtils.isNotEmpty(listReq.getDishIds()), ProjFitnessDish::getId, listReq.getDishIds())
                .like(null != typeCode, ProjFitnessDish::getTypes, typeCode)
                .like(null != styleCode, ProjFitnessDish::getStyles, styleCode)
                .eq(null != listReq.getStatus(), ProjFitnessDish::getStatus, listReq.getStatus())
                .orderByAsc(ProjFitnessDish::getSorted)
                .orderByDesc(BaseModel::getId);
        List<ProjFitnessDish> dishList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(dishList)) {
            return new ArrayList<>();
        }
        List<ProjFitnessDishListVO> dishListVO = mapStruct.toVOList(dishList);
        injectionTaskStatus(dishListVO);
        return dishListVO;
    }


    @Override
    public ProjFitnessDishDetailVO findDetailById(Integer id) {
        ProjFitnessDish dish = baseMapper.selectById(id);
        ProjFitnessDishDetailVO detailVO = mapStruct.toDetailVO(dish);
        List<ProjFitnessDishStepVO> stepVOList = dishStepService.query(id);
        detailVO.setDishStepList(stepVOList);
        List<ProjFitnessIngredientVO> ingredientVOList = ingredientService.query(id);
        detailVO.setIngredientList(ingredientVOList);
        List<ProjFitnessAllergenRelation> relationList = allergenRelationService.query(id, FitnessAllergenRelationBusinessEnums.FITNESS_DISH);
        if(CollUtil.isNotEmpty(relationList)){
            List<Integer> allergenIdList = relationList.stream().map(ProjFitnessAllergenRelation::getProjFitnessAllergenId).collect(Collectors.toList());
            detailVO.setAllergenIdList(allergenIdList);
        }
        return detailVO;
    }

    private void injectionTaskStatus(List<ProjFitnessDishListVO> dishList) {
        if (CollUtil.isEmpty(dishList)) {
            return;
        }
        Set<Integer> idSet = dishList.stream().map(ProjFitnessDishListVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> statusList = taskResourceSectionService.find(PROJ_FITNESS_DISH.getTableName(), PROJ_FITNESS_DISH.getEntityFieldName(), idSet);
        Map<Integer, TaskResourceSection> taskMap = statusList.stream().collect(Collectors.toMap(TaskResourceSection::getTableId, item -> item));
        for (ProjFitnessDishListVO dish : dishList) {
            if (StrUtil.isBlank(dish.getResourceVideoUrl())) {
                dish.setResourceVideoTaskStatus(TaskResourceSectionStatusEnums.COMPLETED);
                continue;
            }
            TaskResourceSection task = taskMap.get(dish.getId());
            if (null != task) {
                dish.setResourceVideoTaskStatus(task.getStatus());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjFitnessDishUpdateReq req, Integer projId) {
        String resourceVideoUrl = req.getResourceVideoUrl();
        Integer id = req.getId();
        check(id,req,projId);
        LambdaUpdateWrapper<ProjFitnessDish> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BaseModel::getId, id);

        //枚举需要使用handler，需要set到对象上
        ProjFitnessDish dish = mapStruct.toEntity(req);
        if (StrUtil.isBlank(resourceVideoUrl)) {
            wrapper.set(ProjFitnessDish::getVideo2532Url, null)
                    .set(ProjFitnessDish::getResourceVideoUrl, null)
                    .set(ProjFitnessDish::getVideoUrl, null)
                    .set(ProjFitnessDish::getDuration, GlobalConstant.ZERO);
        } else {
            dish.setId(id);
            dish.setResourceVideoUrl(resourceVideoUrl);
        }

        baseMapper.update(dish, wrapper);
        allergenRelationService.saveBatch(req.getAllergenIdList(), projId, id, FitnessAllergenRelationBusinessEnums.FITNESS_DISH);
        ingredientService.saveBatch(req.getIngredientList(), id, projId);
        dishStepService.saveBatch(id, req.getDishStepList(), projId);

        projLmsI18nService.handleI18n(Collections.singletonList( this.getById(id)), projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjFitnessDishAddReq req, Integer projId) {
        check(null, req, projId);
        ProjFitnessDish dish = mapStruct.toEntity(req);
        dish.setProjId(projId);
        save(dish);
        Integer id = dish.getId();
        allergenRelationService.saveBatch(req.getAllergenIdList(), projId, id, FitnessAllergenRelationBusinessEnums.FITNESS_DISH);
        ingredientService.saveBatch(req.getIngredientList(), id, projId);
        dishStepService.saveBatch(id, req.getDishStepList(), projId);

        projLmsI18nService.handleI18n(Collections.singletonList(dish), projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        List<Integer> completedIdList = new ArrayList<>();
        LambdaQueryWrapper<ProjFitnessDish> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseModel::getId, idList);
        List<Integer> resourceUrlEmptyIdList = baseMapper.selectList(wrapper).stream()
                .filter(item -> StrUtil.isBlank(item.getResourceVideoUrl()))
                .map(BaseModel::getId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(resourceUrlEmptyIdList)) {
            completedIdList.addAll(resourceUrlEmptyIdList);
        }
        List<TaskResourceSection> taskList = taskResourceSectionService.query(PROJ_FITNESS_DISH.getTableName(), PROJ_FITNESS_DISH.getEntityFieldName(), idList);
        List<Integer> taskCompletedIdList = taskList.stream()
                .filter(item -> TaskResourceSectionStatusEnums.COMPLETED == item.getStatus())
                .map(TaskResourceSection::getTableId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskCompletedIdList)) {
            completedIdList.addAll(taskCompletedIdList);
        }
        if (CollUtil.isEmpty(completedIdList)) {
            return idList;
        }

        LambdaUpdateWrapper<ProjFitnessDish> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessDish::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjFitnessDish::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjFitnessDish::getId, completedIdList);
        this.update(new ProjFitnessDish(), updateWrapper);
        idList.removeAll(completedIdList);
        return idList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessDish> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessDish::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFitnessDish::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessDish::getId, idList);
        this.update(new ProjFitnessDish(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessDish> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessDish::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjFitnessDish::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjFitnessDish::getId, idList);
        this.update(new ProjFitnessDish(), wrapper);
        ingredientService.deleteBatch(idList);
        dishStepService.deleteBatch(idList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<ProjFitnessDish> dishList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjFitnessDish info = new ProjFitnessDish();
            info.setSorted(i)
                    .setId(idList.get(i));
            dishList.add(info);
        }
        updateBatchById(dishList);
    }

    private void check(Integer id, ProjFitnessDishAddReq req, Integer projId) {
        LambdaQueryWrapper<ProjFitnessDish> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessDish::getName, req.getName())
                .eq(ProjFitnessDish::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjFitnessDish> dishList = baseMapper.selectList(wrapper);
        Set<String> nameSet = dishList.stream().map(ProjFitnessDish::getName).collect(Collectors.toSet());
        BizExceptionUtil.throwIf(nameSet.contains(req.getName()), "name already exists");
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessDish::getEventName, req.getEventName())
                .eq(ProjFitnessDish::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        dishList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= dishList.stream().map(ProjFitnessDish::getName).collect(Collectors.toSet());
        BizExceptionUtil.throwIf(eventNameSet.contains(req.getEventName()), "eventName already exists");
        if (ObjUtil.isNull(id)) {
            return;
        }
        ProjFitnessDish dish = baseMapper.selectById(id);
        BizExceptionUtil.throwIf(ObjUtil.isNull(dish), "dish not exists");
        BizExceptionUtil.throwIf(ObjUtil.equals(dish.getStatus(),GlobalConstant.STATUS_ENABLE), "dish status is enable, can not update");
    }
}
