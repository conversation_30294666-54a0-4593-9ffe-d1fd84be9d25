package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_workout_generate_exercise_video
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessWorkoutGenerateExerciseVideo对象", description = "proj_fitness_workout_generate_exercise_video")
@TableName(autoResultMap = true)
public class ProjFitnessWorkoutGenerateExerciseVideo extends BaseModel {

    private static final long serialVersionUID = -3980786728453076658L;

    @ApiModelProperty(value = "proj_fitness_template_id")
    private Integer projFitnessTemplateId;

    @ApiModelProperty(value = "proj_fitness_template_task_id")
    private Integer projFitnessTemplateTaskId;

    @ApiModelProperty(value = "proj_workout_generate_id")
    private Integer projFitnessWorkoutGenerateId;

    @ApiModelProperty(value = "proj_fitness_template_exercise_group_id")
    private Integer projFitnessTemplateExerciseGroupId;

    @ApiModelProperty(value = "proj_fitness_exercise_video_id")
    private Integer projFitnessExerciseVideoId;

    @ApiModelProperty(value = "preview_duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "video_duration")
    private Integer videoDuration;
}
