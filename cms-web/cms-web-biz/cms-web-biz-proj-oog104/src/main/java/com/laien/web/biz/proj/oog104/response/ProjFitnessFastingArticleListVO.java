package com.laien.web.biz.proj.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog104.enums.FitnessFastingArticleEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * fasting article
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value="FastingArticle对象", description="fasting article")
public class ProjFitnessFastingArticleListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String titleName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型")
    private FitnessFastingArticleEnums type;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "参考文档")
    private String reference;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;
}
