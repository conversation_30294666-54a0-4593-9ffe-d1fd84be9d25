package com.laien.web.biz.proj.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.web.biz.proj.oog104.enums.DifficultyEnums;
import com.laien.web.biz.proj.oog104.enums.EquipmentEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "collection 分页", description = "collection 分页")
public class ProjFitnessCollectionPageVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "难度")
    private DifficultyEnums difficulty;

    @ApiModelProperty(value = "器械")
    private List<EquipmentEnums> equipment;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "workout 不可用数量")
    private Integer workoutDisabledCount;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

}
