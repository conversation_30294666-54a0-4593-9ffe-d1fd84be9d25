package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.template.TemplateTaskStatusEnum;
import com.laien.web.frame.entity.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

/**
 * proj_fitness_template_task
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="proj_fitness_template_task",autoResultMap = true)
@Data
@Accessors(chain = true)
public class ProjFitnessTemplateTask extends BaseModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_TEMPLATE_TASK;

   /**
    * 项目ID
    */
    @ApiModelProperty("项目ID")
    private Integer projId;


   /**
    * 生成的workout数量
    */
    @ApiModelProperty("生成的workout数量")
    private Integer workoutNum;


   /**
    * 模板ID
    */
    @ApiModelProperty("模板ID")
    private Integer projFitnessTemplateId;


   /**
    * 是否需要清理已生成的数据 0 否，1是
    */
    @ApiModelProperty("是否需要清理已生成的数据 0 否，1是")
    private Integer cleanUp;


   /**
    * 失败信息
    */
    @ApiModelProperty("失败信息")
    private String failureMessage;


   /**
    * 任务状态 1待处理、2处理中、3处理失败、4 处理成功
    */
    @ApiModelProperty("任务状态 1待处理、2处理中、3处理失败、4 处理成功")
    private TemplateTaskStatusEnum status;











}
