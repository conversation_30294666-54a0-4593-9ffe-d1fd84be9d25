package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessDish;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessDishListVO;
import com.laien.web.frame.request.IdListReq;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * Fitness Dish 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
public interface IProjFitnessDishService extends IService<ProjFitnessDish> {

    List<ProjFitnessDishListVO> list(ProjFitnessDishListReq listReq, Integer projId);

    ProjFitnessDishDetailVO findDetailById(Integer id);

    void update(ProjFitnessDishUpdateReq req, Integer projectId);

    void save(ProjFitnessDishAddReq dishReq, Integer projId);

    List<Integer> updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);

    void sort(IdListReq idListReq);
}
