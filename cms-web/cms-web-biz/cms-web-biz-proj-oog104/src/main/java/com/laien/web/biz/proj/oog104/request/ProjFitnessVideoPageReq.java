package com.laien.web.biz.proj.oog104.request;

import com.laien.web.biz.proj.oog104.enums.*;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "video 分页", description = "video 分页")
public class ProjFitnessVideoPageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private List<TypeEnums> type;

    @ApiModelProperty(value = "难度")
    private DifficultyEnums difficulty;

    @ApiModelProperty(value = "部位")
    private PositionEnums position;

    @ApiModelProperty(value = "类型")
    private List<FitTypeEnum> fitType;

    @ApiModelProperty(value = "目标c")
    private List<TargetEnums> target;

    @ApiModelProperty(value = "器械")
    private List<EquipmentEnums> equipment;

    @ApiModelProperty(value = "特殊限制列表")
    private List<SpecialLimitEnums> specialLimits;

    @ApiModelProperty(value = "特殊限制匹配规则，默认为包含所有为真")
    private MatchTypeEnum specialLimitsMatchType = MatchTypeEnum.CONTAINS_ALL;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

}
