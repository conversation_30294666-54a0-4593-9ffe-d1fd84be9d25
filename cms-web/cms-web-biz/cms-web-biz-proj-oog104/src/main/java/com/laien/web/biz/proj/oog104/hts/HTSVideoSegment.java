package com.laien.web.biz.proj.oog104.hts;

import com.laien.web.common.file.bo.TsMergeBO;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * HTS segment
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
public class HTSVideoSegment {

    private List<TsMergeBO> videoList;
    private int segmentPointBegin = 0;
    private int segmentPointEnd = 0;

    public static HTSVideoSegment newInstance(int initSize) {
        HTSVideoSegment htsVideoSegment = new HTSVideoSegment();
        htsVideoSegment.setVideoList(new ArrayList<>(initSize));
        return htsVideoSegment;
    }
    public static HTSVideoSegment newInstance() {
        return newInstance(16);
    }

    public void addSegment(TsMergeBO... TsMerges) {
        this.segmentPointBegin = this.segmentPointEnd;
        for (TsMergeBO tsMerge : TsMerges) {
            this.videoList.add(tsMerge);
            this.segmentPointEnd += tsMerge.getVideoDuration();
        }
    }
    public void addSegment(Collection<TsMergeBO> tsMerges) {
        this.segmentPointBegin = this.segmentPointEnd;
        for (TsMergeBO tsMerge : tsMerges) {
            this.videoList.add(tsMerge);
            this.segmentPointEnd += tsMerge.getVideoDuration();
        }
    }

}
