package com.laien.web.biz.proj.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * proj_fitness_video Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface ProjFitnessVideoMapper extends BaseMapper<ProjFitnessVideo> {

    /**
     * 根据ids查询
     *
     * @param idsStr id集合
     * @return list
     */
    @Select("select * from proj_fitness_video where id in (${idsStr})")
    List<ProjFitnessVideo> selectListByIds(@Param("idsStr") String idsStr);

}
