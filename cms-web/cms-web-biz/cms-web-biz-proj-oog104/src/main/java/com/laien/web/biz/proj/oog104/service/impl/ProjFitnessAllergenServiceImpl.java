package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessAllergen;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessAllergenMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessDishMapStruct;
import com.laien.web.biz.proj.oog104.response.ProjFitnessAllergenVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessAllergenService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Fitness Allergen 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@RequiredArgsConstructor
public class ProjFitnessAllergenServiceImpl extends ServiceImpl<ProjFitnessAllergenMapper, ProjFitnessAllergen> implements IProjFitnessAllergenService {

    private final ProjFitnessDishMapStruct mapStruct;

    @Override
    public List<ProjFitnessAllergenVO> query(Integer projId) {
        LambdaQueryWrapper<ProjFitnessAllergen> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessAllergen::getProjId, projId)
                .eq(ProjFitnessAllergen::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjFitnessAllergen> allergenList = baseMapper.selectList(wrapper);
        List<ProjFitnessAllergenVO> allergenVOList = new ArrayList<>();
        if (CollUtil.isEmpty(allergenList)) {
            return allergenVOList;
        }
        return mapStruct.toAllergenVOList(allergenList);
    }
}
