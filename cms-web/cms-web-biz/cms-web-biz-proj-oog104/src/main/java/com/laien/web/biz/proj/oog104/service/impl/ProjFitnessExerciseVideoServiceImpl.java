package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.manual.ManualVideoDirectionEnums;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.web.biz.proj.oog104.enums.MatchTypeEnum;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessExerciseVideoMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessExerciseVideoMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoImportReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoExportVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessExerciseVideoService;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import com.laien.web.frame.validation.Group;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_DRAFT;
import static com.laien.web.frame.constant.GlobalConstant.YES;

/**
 * <p>
 * proj_fitness_exercise_video 服务类实现
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessExerciseVideoServiceImpl extends ServiceImpl<ProjFitnessExerciseVideoMapper, ProjFitnessExerciseVideo>
        implements IProjFitnessExerciseVideoService {

    private static final String SPECIAL_LIMIT ="special_limit";
    private static final String TARGET ="target";
    private static final String EVENT_NAME ="event_name";
    private static final String FITNESS_EXERCISE_VIDEO_M3U8 = "project-fitness-exercise-video-m3u8";

    private final FileService fileService;
    private final Validator validator;
    private final ProjFitnessExerciseVideoMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;
    private final ICoreVoiceConfigI18nService i18nConfigService;

    @Override
    public List<ProjFitnessExerciseVideo> listEnable4AudoGenerate() {

        LambdaQueryWrapper<ProjFitnessExerciseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessExerciseVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ProjFitnessExerciseVideo::getUsedForAuto, GlobalConstant.ONE);
        return list(queryWrapper);
    }

    @Override
    public PageRes<ProjFitnessExerciseVideoPageVO> selectVideoPage(ProjFitnessExerciseVideoPageReq pageReq) {
        LambdaQueryWrapper<ProjFitnessExerciseVideo> query = getLambdaQueryWrapper(pageReq);
        IPage<ProjFitnessExerciseVideo> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), query);
        List<ProjFitnessExerciseVideoPageVO> list = mapStruct.toPageList(page.getRecords());
        fillI18nConfigInfo(page.getRecords(), list);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    @Override
    public List<ProjFitnessExerciseVideoPageVO> listVOByIds(List<Integer> ids) {
        LambdaQueryWrapper<ProjFitnessExerciseVideo> query = new LambdaQueryWrapper<>();
        query.in(ProjFitnessExerciseVideo::getId, ids);
        return mapStruct.toPageList(this.list(query));
    }

    private LambdaQueryWrapper<ProjFitnessExerciseVideo> getLambdaQueryWrapper(ProjFitnessExerciseVideoPageReq pageReq) {
        LambdaQueryWrapper<ProjFitnessExerciseVideo> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(pageReq.getId()), ProjFitnessExerciseVideo::getId, pageReq.getId())
                .like(StrUtil.isNotBlank(pageReq.getName()), ProjFitnessExerciseVideo::getName, pageReq.getName())
                .eq(ObjUtil.isNotNull(pageReq.getStatus()), ProjFitnessExerciseVideo::getStatus, pageReq.getStatus())
                .eq(ObjUtil.isNotNull(pageReq.getExerciseType()),ProjFitnessExerciseVideo::getExerciseType, pageReq.getExerciseType())
                .eq(ObjUtil.isNotNull(pageReq.getType()),ProjFitnessExerciseVideo::getType, pageReq.getType())
                .eq(ObjUtil.isNotNull(pageReq.getIntensity()),ProjFitnessExerciseVideo::getIntensity, pageReq.getIntensity())
                .eq(ObjUtil.isNotNull(pageReq.getDifficulty()),ProjFitnessExerciseVideo::getDifficulty, pageReq.getDifficulty())
                .eq(ObjUtil.isNotNull(pageReq.getPosition()),ProjFitnessExerciseVideo::getPosition, pageReq.getPosition())
                .eq(ObjUtil.isNotNull(pageReq.getEquipment()),ProjFitnessExerciseVideo::getEquipment, pageReq.getEquipment())
                .eq(ObjUtil.isNotNull(pageReq.getVideoDirection()),ProjFitnessExerciseVideo::getVideoDirection, pageReq.getVideoDirection())
                .eq(ObjUtil.isNotNull(pageReq.getUsedForAuto()),ProjFitnessExerciseVideo::getUsedForAuto, pageReq.getUsedForAuto())
                .eq(ObjUtil.isNotNull(pageReq.getProjId()),ProjFitnessExerciseVideo::getProjId, pageReq.getProjId())
                .in(CollUtil.isNotEmpty(pageReq.getIds()), ProjFitnessExerciseVideo::getId, pageReq.getIds());

        query.orderByDesc(ProjFitnessExerciseVideo::getId);

        this.multipleSearchContainsOne(query, pageReq.getTarget(), TARGET);
        if(MatchTypeEnum.CONTAINS_ALL.equals(pageReq.getSpecialLimitMatchType())) {
            this.multipleSearchContainsAll(query, pageReq.getSpecialLimit(), SPECIAL_LIMIT);
        }
        if(MatchTypeEnum.CONTAINS_ONE.equals(pageReq.getSpecialLimitMatchType())) {
            this.multipleSearchContainsOne(query, pageReq.getSpecialLimit(), SPECIAL_LIMIT);
        }
        return query;
    }

    @Override
    public List<ProjFitnessExerciseVideoExportVO> exportVideos(ProjFitnessExerciseVideoPageReq pageReq) {
        LambdaQueryWrapper<ProjFitnessExerciseVideo> queryWrapper = this.getLambdaQueryWrapper(pageReq);
        List<ProjFitnessExerciseVideo> videoList = this.list(queryWrapper);
        List<ProjFitnessExerciseVideoExportVO> exportList = mapStruct.toExportList(videoList);
        fillI18nConfigInfo4Export(videoList, exportList);
        return exportList;
    }


    /**
     * 多选查询处理 有交集为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsOne(LambdaQueryWrapper<ProjFitnessExerciseVideo> wrapper, List<? extends com.laien.common.core.enums.IEnumBase> options, String tableField) {
        if(CollUtil.isEmpty(options)){
            return;
        }
        wrapper.and(orWrapper ->
                options.forEach(equipment ->
                orWrapper.or().apply("FIND_IN_SET({0},"+tableField+")", equipment.getCode())));
    }

    /**
     * 多选查询处理 包含为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsAll(LambdaQueryWrapper<ProjFitnessExerciseVideo> wrapper, List<? extends com.laien.common.core.enums.IEnumBase> options, String tableField) {
        // 所有 find in set 为真
        if(CollUtil.isEmpty(options)){
            return;
        }
        wrapper.and(o ->
                options.forEach(equipmentEnums -> o.apply("FIND_IN_SET({0},"+tableField+")",
                equipmentEnums.getCode())));
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVideo(ProjFitnessExerciseVideoAddReq videoReq) {
        ProjFitnessExerciseVideo entity = mapStruct.toEntity(videoReq);
        check(videoReq,null,videoReq.getProjId());
        entity.setStatus(GlobalConstant.STATUS_DRAFT);
        entity.setEventName("");
        if(CollUtil.isEmpty(entity.getSpecialLimit())){
            entity.setSpecialLimit(CollUtil.newArrayList(ExerciseVideoSpecialLimitEnums.NONE));
        }
        this.save(entity);
        //update eventName,调用生成videoUrl生成m3u
        LambdaUpdateWrapper<ProjFitnessExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessExerciseVideo::getEventName, entity.getId()+"+"+entity.getName())
                .set(ProjFitnessExerciseVideo::getVideoUrl, this.videoForM3u8(entity.getFrontVideoUrl(), entity.getFrontVideoDuration()))
                .eq(ProjFitnessExerciseVideo::getId, entity.getId());
        update(new ProjFitnessExerciseVideo(), wrapper);

        //调用lms生成i18n
        projLmsI18nService.handleI18n(Collections.singletonList(entity), videoReq.getProjId());
    }

    private String videoForM3u8(String videoUrl, Integer videoDuration) {
        List<TsMergeBO> videoList = new ArrayList<>(GlobalConstant.ONE);
        videoList.add(new TsMergeBO(fileService.getAbsoluteR2Url(videoUrl), videoDuration));
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTSForM3U8R2(videoList, FITNESS_EXERCISE_VIDEO_M3U8);
        return videoR2Info.getFileRelativeUrl();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateVideo(ProjFitnessExerciseVideoUpdateReq videoReq) {
        Integer id = videoReq.getId();
        ProjFitnessExerciseVideo videoFind = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(videoFind),"Data not found");
        Integer projId = videoFind.getProjId();
        check(videoReq,id,projId);
        ProjFitnessExerciseVideo video = mapStruct.toEntity(videoReq);
        video.setId(videoReq.getId());
        if(CollUtil.isEmpty(video.getSpecialLimit())) {
            video.setSpecialLimit(CollUtil.newArrayList(ExerciseVideoSpecialLimitEnums.NONE));
        }
        //校验frontVideoUrl是否修改
        if(StrUtil.isNotBlank(videoReq.getFrontVideoUrl()) && !Objects.equals(videoReq.getFrontVideoUrl(), videoFind.getFrontVideoUrl())){
            video.setVideoUrl(this.videoForM3u8(video.getFrontVideoUrl(), video.getFrontVideoDuration()));
        }
        this.updateById(video);

        //调用lms生成i18n
        projLmsI18nService.handleI18n(Collections.singletonList(video), projId);
    }

    @Override
    public ProjFitnessExerciseVideoDetailVO getVideoDetail(Integer id) {
        ProjFitnessExerciseVideo video = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(video),"Data not found");
        ProjFitnessExerciseVideoDetailVO videoDetailVO = mapStruct.toDetailVO(video);
        //组装leftRightDetail
        Integer leftRightId = video.getLeftRightVideoId();
        if (ManualVideoDirectionEnums.LEFT.equals(video.getVideoDirection()) && null != leftRightId) {
            ProjFitnessExerciseVideo leftRightVideo = baseMapper.selectById(leftRightId);
            if(null != leftRightVideo){
                ProjFitnessExerciseVideoDetailVO leftRightDetailVO = mapStruct.toDetailVO(leftRightVideo);
                videoDetailVO.setLeftRightDetail(leftRightDetailVO);
            }
        }
        fillI18nConfigInfoForDetail(videoDetailVO);
        return videoDetailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessExerciseVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessExerciseVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjFitnessExerciseVideo::getId, idList);
        boolean update = this.update(new ProjFitnessExerciseVideo(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessExerciseVideo::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjFitnessExerciseVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessExerciseVideo::getId, idList);
        boolean update = this.update(new ProjFitnessExerciseVideo(), wrapper);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        this.update(new ProjFitnessExerciseVideo(), new LambdaUpdateWrapper<ProjFitnessExerciseVideo>()
                .set(ProjFitnessExerciseVideo::getDelFlag, YES)
                .eq(ProjFitnessExerciseVideo::getStatus, STATUS_DRAFT)
                .in(ProjFitnessExerciseVideo::getId, idList));
    }


    /**
     * @param excelInputStream
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importByExcel(InputStream excelInputStream, Integer projectId) {
        log.info("ProjFitnessExerciseVideo importByExcel Start-----------------");
        List<ProjFitnessExerciseVideoImportReq> importReqs = CollUtil.newArrayList();
        EasyExcel.read(excelInputStream, ProjFitnessExerciseVideoImportReq.class, new AnalysisEventListener<ProjFitnessExerciseVideoImportReq>() {
            @Override
            public void invoke(ProjFitnessExerciseVideoImportReq row, AnalysisContext analysisContext) {
                importReqs.add(row);
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        //2、过滤不符合输入规范的数据
        List<String> failMessage = CollUtil.newArrayList();
        List<ProjFitnessExerciseVideo> exerciseVideos = filterDirtyData(importReqs, failMessage);
        //3、通过判断exerciseVideos对象中id分成新增和修改两个list
        List<ProjFitnessExerciseVideo> insertExerciseVideoList = new ArrayList<>();
        List<ProjFitnessExerciseVideo> updateExerciseVideoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(exerciseVideos)) {
            for (ProjFitnessExerciseVideo video : exerciseVideos) {
                if (null == video.getId()) {
                    video.setProjId(projectId);
                    video.setStatus(GlobalConstant.STATUS_DRAFT);
                    video.setEventName("");
                    insertExerciseVideoList.add(video);
                } else {
                    updateExerciseVideoList.add(video);
                }
            }
        }
        //4、校验名称重复的数据
        insertExerciseVideoList = filterRepeatData(insertExerciseVideoList, failMessage,false);
        updateExerciseVideoList = filterRepeatData(updateExerciseVideoList, failMessage,true);

        //有错误信息直接返回，不执行后续逻辑
        if (CollUtil.isNotEmpty(failMessage)) {
            StringBuilder strBuilder = new StringBuilder();
            failMessage.forEach(s -> strBuilder.append("failMessage:").append(s).append("\r\n"));
            log.error(strBuilder.toString());
            log.info("projFitnessExerciseVideo importByExcel Finish-----------------");
            return failMessage;
        }
        //5、新增
        if (CollUtil.isNotEmpty(insertExerciseVideoList)) {
            //设置insertExerciseVideoList的videourl
            insertExerciseVideoList.forEach(video -> video.setVideoUrl(this.videoForM3u8(video.getFrontVideoUrl(), video.getFrontVideoDuration())));
            this.saveBatch(insertExerciseVideoList);
            //批量插入后才可以获取到ID，再来处理eventName=id+name
            LambdaUpdateWrapper<ProjFitnessExerciseVideo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(ProjFitnessExerciseVideo::getId, insertExerciseVideoList.stream().map(ProjFitnessExerciseVideo::getId).collect(Collectors.toList()));
            updateWrapper.setSql(EVENT_NAME+" = CONCAT(id,'+',name)");
            this.update(updateWrapper);

            //调用lms生成i18n
            projLmsI18nService.handleI18n(insertExerciseVideoList, projectId);
        }
        //6.更新
        if (CollUtil.isNotEmpty(updateExerciseVideoList)) {
            //获取更新数据的原数据
            List<Integer> updateIds = updateExerciseVideoList.stream().map(ProjFitnessExerciseVideo::getId).collect(Collectors.toList());
            // 构建 Map，key 为 id，value 为原数据
            Map<Integer, ProjFitnessExerciseVideo> existMap = this.listByIds(updateIds).stream()
                    .collect(Collectors.toMap(ProjFitnessExerciseVideo::getId, video -> video));
            //批量设置videoUrl
            updateExerciseVideoList.forEach(video -> {
                ProjFitnessExerciseVideo exerciseVideo = existMap.get(video.getId());
                //校验frontVideoUrl是否修改
                if(StrUtil.isNotBlank(video.getFrontVideoUrl()) && !Objects.equals(video.getFrontVideoUrl(), exerciseVideo.getFrontVideoUrl())){
                    video.setVideoUrl(this.videoForM3u8(video.getFrontVideoUrl(), video.getFrontVideoDuration()));
                }
                //处理leftRightVideoId
                if (ObjUtil.equal(exerciseVideo.getVideoDirection(), ManualVideoDirectionEnums.LEFT)) {
                    video.setLeftRightVideoId(exerciseVideo.getLeftRightVideoId());
                }
            });
            //批量保存
            updateBatchById(updateExerciseVideoList);

            //调用lms生成i18n
            projLmsI18nService.handleI18n(updateExerciseVideoList, projectId);
        }
        return failMessage;
    }

    /**
     * 过滤与数据库规则重复的数据
     * @param exerciseVideos
     * @param failMessage
     * @param updateFlag
     * @return
     */
    private List<ProjFitnessExerciseVideo> filterRepeatData(List<ProjFitnessExerciseVideo> exerciseVideos, List<String> failMessage,
                                                            boolean updateFlag) {
        if (CollUtil.isEmpty(exerciseVideos)) {
            return Collections.emptyList();
        }
        // 提取唯一 name 并去重
        Set<String> nameSet = exerciseVideos.stream()
                .map(ProjFitnessExerciseVideo::getName)
                .collect(Collectors.toSet());
        // 查询数据库中已存在的 name
        LambdaQueryWrapper<ProjFitnessExerciseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessExerciseVideo::getName, nameSet);
        // 根据 updateFlag 决定是查询已存在的还是未存在的
        if (updateFlag) {
            queryWrapper.notIn(ProjFitnessExerciseVideo::getId, exerciseVideos.stream().map(ProjFitnessExerciseVideo::getId).collect(Collectors.toList()));
        }
        // 转换为 Set 提高查找性能
        Set<String> existNames = this.list(queryWrapper).stream()
                .map(this::getVideoUniqueKey).collect(Collectors.toSet());
        // 过滤掉数据库已存在的 name
        return exerciseVideos.stream().filter(video -> {
                    if (existNames.contains(this.getVideoUniqueKey(video))) {
                        failMessage.add(this.getVideoUniqueKey(video) + ": name duplicated in database");
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    private String getVideoUniqueKey(ProjFitnessExerciseVideo video) {
        return video.getName() + ":" + video.getExerciseType().getName();
    }

    private String getVideoUniqueKey(ProjFitnessExerciseVideoImportReq video) {
        return video.getName() + ":" + video.getExerciseType().getName();
    }
    /**
     * 过滤不符合业务规则的数据
     *
     * @param dataList
     * @param failMessage
     * @return
     */
    private List<ProjFitnessExerciseVideo> filterDirtyData(List<ProjFitnessExerciseVideoImportReq> dataList, List<String> failMessage) {
        List<ProjFitnessExerciseVideo> meetsCondiData = CollUtil.newArrayList();
        Optional.ofNullable(dataList).filter(CollUtil::isNotEmpty).ifPresent(data -> {
            Set<String> nameSet = new HashSet<>(); // 用于存储唯一的 name
            Set<String> i18nConfigNameSet = data.stream().map(ProjFitnessExerciseVideoImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
            Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                    .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));

            for (ProjFitnessExerciseVideoImportReq req : data) {
                try {
                    Optional.ofNullable(validator.validate(req, Group.class)).ifPresent(result -> {
                        Optional<ConstraintViolation<ProjFitnessExerciseVideoImportReq>> firstError = result.stream().findFirst();
                        if (firstError.isPresent()) {
                            //校验失败，只记录第一条失败原因
                            failMessage.add(req.getName() + ":" + firstError.get().getMessage());
                        }
                        else if (!nameSet.add(this.getVideoUniqueKey(req))){
                            // 检查导入数据中是否有重复的 `name`
                            failMessage.add(req.getName() + ": name duplicated in import data");
                        }
                        else if (!i18nConfigNameIdMap.containsKey(req.getCoreVoiceConfigI18nName())) {
                            failMessage.add(req.getName() + ": English Voice Name Not Found in TTS config");
                        }
                        else if (!validateTarget(req)) {
                            failMessage.add(req.getName() + ": target contains None and other options");
                        }
                        else {
                            assembleVideos(req, meetsCondiData,i18nConfigNameIdMap);
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    failMessage.add(req.getName() + ":" + e.getMessage());
                }
            }
        });
        return meetsCondiData;
    }

    private boolean validateTarget(ProjFitnessExerciseVideoImportReq req) {
        List<ManualTargetEnums> targets = req.getTarget();
        // 如果 targets 为空，允许
        if (CollUtil.isEmpty(targets)) {
            return true;
        }
        // 检查是否包含 None
        boolean hasNone = targets.contains(ManualTargetEnums.NONE);
        // 如果包含 None，不能与其他选项一起选择
        return !hasNone || targets.size() == 1;  // 返回校验失败
    }

    private void assembleVideos(ProjFitnessExerciseVideoImportReq req, List<ProjFitnessExerciseVideo> meetsCondiData, Map<String, Integer> i18nConfigNameIdMap) {
        //生成video对象
        ProjFitnessExerciseVideo exerciseVideo = mapStruct.toEntity(req);
        //设置coreVoiceConfigI18nId
        exerciseVideo.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(req.getCoreVoiceConfigI18nName()));
        //如果Calorie为空或为0，计算Calorie
        if (null == exerciseVideo.getCalorie() || exerciseVideo.getCalorie().compareTo(BigDecimal.ZERO) == 0) {
            Integer met = exerciseVideo.getMet();
            Integer frontVideoDuration = exerciseVideo.getFrontVideoDuration();
            //Calorie = 「met * 75 / 3600 * 视频时长s」，保留3位小数,使用Bigdecimal；
            BigDecimal result = new BigDecimal(met).multiply(new BigDecimal(75)).multiply(BigDecimal.valueOf(frontVideoDuration))
                    .divide(new BigDecimal(3600*1000), 3, RoundingMode.HALF_UP);
            exerciseVideo.setCalorie(result);
        }
        if(CollUtil.isEmpty(exerciseVideo.getSpecialLimit())) {
            exerciseVideo.setSpecialLimit(CollUtil.newArrayList(ExerciseVideoSpecialLimitEnums.NONE));
        }
        meetsCondiData.add(exerciseVideo);
    }


    /**
     * 判断路径是否已经包含参数，追加时间戳参数
     * @param path 文件路径
     * @param timeStamp 当前时间戳
     * @return 拼接后的路径
     */
    private String appendTimeStamp(String path, Long timeStamp) {
        if (StrUtil.isBlank(path)) {
            return path;
        }
        String connector = path.contains("?") ? "&" : "?";
        return path + connector + timeStamp;
    }
    /**
     * 通过分隔符分隔的字段值获取枚举列表，过滤无效值
     *
     * @param condition 条件字段
     * @param values    分隔符分隔的字段值
     * @param separator 分隔符
     * @param <E>       枚举类型
     * @param <C>       字段类型
     * @return 匹配的枚举列表（不会为null）
     */
    public <E extends Enum<E>, C> List<E> getListBy(Func1<E, C> condition, String values, String separator) {
        if (StrUtil.isBlank(values)) {
            return Collections.emptyList();
        }
        return StrUtil.split(values, separator)
                .stream()
                .map(String::trim)
                .map(v -> EnumUtil.getBy(condition, (C) v)) // 保持原方法类型转换逻辑
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 校验
     *
     * @param videoReq videoReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjFitnessExerciseVideoAddReq videoReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjFitnessExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessExerciseVideo::getName, videoReq.getName())
                .eq(ProjFitnessExerciseVideo::getExerciseType, videoReq.getExerciseType())
                .ne(Objects.nonNull(id), ProjFitnessExerciseVideo::getId, id)
                .eq(ProjFitnessExerciseVideo::getProjId, projId);
        BizExceptionUtil.throwIf(this.count(wrapper) > 0, "name+exerciseType already exists");
        Integer leftRightVideoId = videoReq.getLeftRightVideoId();
        if (leftRightVideoId != null) {
            LambdaQueryWrapper<ProjFitnessExerciseVideo> leftRightWrapper = new LambdaQueryWrapper<>();
            leftRightWrapper.eq(ProjFitnessExerciseVideo::getId, leftRightVideoId)
                    .eq(ProjFitnessExerciseVideo::getProjId, projId)
                    .eq(ProjFitnessExerciseVideo::getDelFlag, GlobalConstant.NO);
            ProjFitnessExerciseVideo video = this.getOne(leftRightWrapper);
            BizExceptionUtil.throwIf(video == null, "leftRightVideo not exists");
            BizExceptionUtil.throwIf(GlobalConstant.STATUS_ENABLE != video.getStatus(), "leftRightVideo not enable");
            BizExceptionUtil.throwIf(!ManualVideoDirectionEnums.RIGHT.equals(video.getVideoDirection()), "leftRightVideo not right");
        }
    }

    private void fillI18nConfigInfo(List<ProjFitnessExerciseVideo> records, List<ProjFitnessExerciseVideoPageVO> list) {
        if (CollUtil.isEmpty(records) || CollUtil.isEmpty(list)) {
            return;
        }
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjFitnessExerciseVideoPageVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private void fillI18nConfigInfoForDetail(ProjFitnessExerciseVideoDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config!= null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }

    private void fillI18nConfigInfo4Export(List<ProjFitnessExerciseVideo> records, List<ProjFitnessExerciseVideoExportVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjFitnessExerciseVideoExportVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ProjFitnessExerciseVideo> records) {
        Set<Integer> configIds = records.stream().map(ProjFitnessExerciseVideo::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }

}
