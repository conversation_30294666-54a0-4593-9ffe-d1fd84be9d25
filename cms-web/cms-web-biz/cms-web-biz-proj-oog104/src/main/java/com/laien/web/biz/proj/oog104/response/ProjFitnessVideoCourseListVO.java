package com.laien.web.biz.proj.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.oog104.enums.course.FitnessCoursePlayTypeEnums;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * VideoCourse
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjVideoCourse对象", description="VideoCourse")
public class ProjFitnessVideoCourseListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "playType")
    private FitnessCoursePlayTypeEnums playType;

    @ApiModelProperty(value = "type")
    private FitnessCourseTypeEnums type;

    @ApiModelProperty(value = "courseType")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "视频时长（毫秒）")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "切片任务状态")
    private TaskResourceSectionStatusEnums taskStatus;


}
