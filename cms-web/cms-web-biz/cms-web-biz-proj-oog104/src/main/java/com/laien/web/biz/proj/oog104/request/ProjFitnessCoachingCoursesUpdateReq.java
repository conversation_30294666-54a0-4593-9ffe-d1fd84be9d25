package com.laien.web.biz.proj.oog104.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * ProjFitnessCoachingCoursesUpdateReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCoachingCoursesUpdateReq", description="ProjFitnessCoachingCoursesUpdateReq")
public class ProjFitnessCoachingCoursesUpdateReq extends ProjFitnessCoachingCoursesAddReq {

    @ApiModelProperty(value = "id")
    private Integer id;
}
