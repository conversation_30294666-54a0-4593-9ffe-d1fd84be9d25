package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessCoachUpdateReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCoachUpdateReq", description="ProjFitnessCoachUpdateReq")
public class ProjFitnessCoachUpdateReq extends ProjFitnessCoachAddReq {

    @ApiModelProperty(value = "id")
    private Integer id;
}
