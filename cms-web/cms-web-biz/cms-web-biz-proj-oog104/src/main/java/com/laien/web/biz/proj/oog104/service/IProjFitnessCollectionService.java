package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCollection;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCollectionUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCollectionDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCollectionPageVO;

import java.util.List;

/**
 * <p>
 * proj_fitness_collection 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface IProjFitnessCollectionService extends IService<ProjFitnessCollection> {

    /**
     * collection分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    List<ProjFitnessCollectionPageVO> selectCollectionPage(ProjFitnessCollectionPageReq pageReq);

    /**
     * collection新增
     *
     * @param collectionReq collectionReq
     */
    void saveCollection(ProjFitnessCollectionAddReq collectionReq);

    /**
     * collection修改
     *
     * @param collectionReq collectionReq
     */
    void updateCollection(ProjFitnessCollectionUpdateReq collectionReq);

    /**
     * collection详情
     *
     * @param id id
     * @return ProjFitnessCollectionDetailVO
     */
    ProjFitnessCollectionDetailVO getCollectionDetail(Integer id);

    /**
     * collection启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * collection禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * collection删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * collection 排序
     * @param idList idList
     */
    void saveSort(List<Integer> idList);

    /**
     * 根据workoutId更新时长
     *
     * @param workoutId workoutId
     */
    void updateDurationByWorkoutId(Integer workoutId);

}
