package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessSound;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessSoundUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessSoundPageVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessSoundMapStruct {

    List<ProjFitnessSoundPageVO> toPageList(List<ProjFitnessSound> entities);

    /**
     * 添加 request 转 entity
     * @param req
     * @return
     */
    ProjFitnessSound toEntity(ProjFitnessSoundAddReq req);

    /**
     * 修改 request 转 entity
     * @param req
     * @return
     */
    ProjFitnessSound toEntity(ProjFitnessSoundUpdateReq req);

    /**
     * entity 转详情
     * @param entity
     * @return
     */
    ProjFitnessSoundDetailVO toDetailVO(ProjFitnessSound entity);
}