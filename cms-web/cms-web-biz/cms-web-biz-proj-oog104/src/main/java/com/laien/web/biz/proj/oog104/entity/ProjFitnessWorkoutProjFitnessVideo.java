package com.laien.web.biz.proj.oog104.entity;

import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_workout_proj_fitness_video
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessWorkoutProjFitnessVideo对象", description="proj_fitness_workout_proj_fitness_video")
public class ProjFitnessWorkoutProjFitnessVideo extends BaseModel  implements CoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "workout id")
    private Integer projFitnessWorkoutId;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

    @ApiModelProperty(value = "正式时长")
    private Integer mainDuration;

    @ApiModelProperty(value = "单元名称")
    @TranslateField
    private String unitName;

    @ApiModelProperty(value = "exercise id")
    private Integer projFitnessVideoId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
