package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoach;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachVO;

import java.util.List;

/**
 * <p>
 * Fitness Coach 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
public interface IProjFitnessCoachService extends IService<ProjFitnessCoach> {

    List<ProjFitnessCoachVO> list(ProjFitnessCoachListReq listReq, Integer projId);

    ProjFitnessCoachVO findDetailById(Integer id);

    void update(ProjFitnessCoachUpdateReq req, Integer projectId);

    void save(ProjFitnessCoachAddReq dishReq, Integer projId);

    List<Integer> updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIdList(List<Integer> idList);
}
