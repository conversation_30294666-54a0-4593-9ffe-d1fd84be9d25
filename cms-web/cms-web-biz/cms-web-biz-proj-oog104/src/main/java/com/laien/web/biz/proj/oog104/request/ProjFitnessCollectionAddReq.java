package com.laien.web.biz.proj.oog104.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.web.biz.proj.oog104.enums.DifficultyEnums;
import com.laien.web.biz.proj.oog104.enums.EquipmentEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "collection 新增", description = "collection 新增")
public class ProjFitnessCollectionAddReq {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "难度")
    private DifficultyEnums difficulty;

    @ApiModelProperty(value = "器械")
    private List<EquipmentEnums> equipment;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "简介")
    private String description;

    @NotEmpty(message = "workoutList cannot be empty")
    @ApiModelProperty(value = "workout list")
    private List<ProjFitnessCollectionAddWorkoutReq> workoutList;

}
