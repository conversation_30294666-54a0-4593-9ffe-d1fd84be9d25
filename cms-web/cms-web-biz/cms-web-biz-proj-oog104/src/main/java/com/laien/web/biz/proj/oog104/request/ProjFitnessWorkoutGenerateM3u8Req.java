package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "workout 批量生成m3u8请求参数", description = "workout 批量生成m3u8请求参数")
public class ProjFitnessWorkoutGenerateM3u8Req {

    @ApiModelProperty(value = "是否生成视频")
    private Boolean videoFlag;

    @ApiModelProperty(value = "是否生成音频")
    private Boolean audioFlag;

    @ApiModelProperty(value = "音频需生成的语言")
    private List<String> languages = new ArrayList<>();

    @ApiModelProperty(value = "workout ids")
    private List<Integer> workoutIds = new ArrayList<>();

}
