package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.*;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishStepReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessDishStepTipReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessIngredientReq;
import com.laien.web.biz.proj.oog104.response.*;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *     ProjFitnessDishMapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/06
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessDishMapStruct {

    ProjFitnessDish toEntity(ProjFitnessDishAddReq req);

    List<ProjFitnessIngredient> toIngredientEntityList(List<ProjFitnessIngredientReq> ingredientReqList);

    List<ProjFitnessIngredientVO> toIngredientVOList(List<ProjFitnessIngredient> ingredientList);

    List<ProjFitnessDishStepTipVO> toStepTipVOList(List<ProjFitnessDishStepTip> tipList);

    ProjFitnessDishStep toStepEntity(ProjFitnessDishStepReq stepReq);

    ProjFitnessDishStepTip toStepTipEntity(ProjFitnessDishStepTipReq tipReq);

    ProjFitnessDishStepVO toStepVO(ProjFitnessDishStep step);

    List<ProjFitnessDishListVO> toVOList(List<ProjFitnessDish> dishList);

    ProjFitnessDishDetailVO toDetailVO(ProjFitnessDish dish);

    List<ProjFitnessAllergenVO> toAllergenVOList(List<ProjFitnessAllergen> allergenList);

    List<ProjFitnessUnitVO> toUnitVOList(List<ProjFitnessUnit> unitList);
}
