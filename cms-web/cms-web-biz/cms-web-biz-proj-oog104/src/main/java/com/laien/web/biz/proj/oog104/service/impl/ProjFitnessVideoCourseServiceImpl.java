package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideoCourse;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessVideoCourseMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessVideoCourseMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCoursePageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoCourseUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessVideoCourseService;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.laien.web.common.m3u8.seq.enums.TaskResourceSectionQueryEnums.PROJ_FITNESS_VIDEO_COURSE;

/**
 * <p>
 * Fitness VideoCourse 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessVideoCourseServiceImpl extends ServiceImpl<ProjFitnessVideoCourseMapper, ProjFitnessVideoCourse> implements IProjFitnessVideoCourseService {

    private final ProjFitnessVideoCourseMapStruct mapStruct;
    private final ITaskResourceSectionService taskResourceSectionService;
    private final IProjLmsI18nService projLmsI18nService;

    @Override
    public PageRes<ProjFitnessVideoCourseListVO> page(ProjFitnessVideoCoursePageReq req, Integer projId) {
        LambdaQueryWrapper<ProjFitnessVideoCourse> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(req.getName()), ProjFitnessVideoCourse::getName, req.getName())
                .in(CollectionUtils.isNotEmpty(req.getIds()), ProjFitnessVideoCourse::getId, req.getIds())
                .eq(null != req.getPlayType(), ProjFitnessVideoCourse::getPlayType, req.getPlayType())
                .eq(null != req.getType(), ProjFitnessVideoCourse::getType, req.getType())
                .eq(null != req.getStatus(), ProjFitnessVideoCourse::getStatus, req.getStatus())
                .eq(null != projId , ProjFitnessVideoCourse::getProjId, projId)
                .eq(null != req.getDifficulty(), ProjFitnessVideoCourse::getDifficulty, req.getDifficulty())
                .orderByDesc(BaseModel::getId);
        IPage<ProjFitnessVideoCourse> pageResult = this.page(new Page<>(req.getPageNum(), req.getPageSize()),wrapper);
        List<ProjFitnessVideoCourseListVO> voList = mapStruct.toVOList(pageResult.getRecords());
        injectionTaskStatus(voList);
        return new PageRes<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal(), pageResult.getPages(),voList);
    }

    @Override
    public List<ProjFitnessVideoCourseListVO> listVOByIds(List<Integer> ids) {
        return mapStruct.toVOList(this.listByIds(ids));
    }


    @Override
    public ProjFitnessVideoCourseDetailVO findDetailById(Integer id) {
        ProjFitnessVideoCourse dish = this.getById(id);
        return mapStruct.toDetailVO(dish);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjFitnessVideoCourseUpdateReq req, Integer projId) {
        Integer id = req.getId();
        this.check(id, req, projId);
        ProjFitnessVideoCourse entity = mapStruct.toEntity(req);
        entity.setId(id);
        this.updateById(entity);
        projLmsI18nService.handleI18n(Collections.singletonList(entity),projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjFitnessVideoCourseAddReq req, Integer projId) {
        check(null, req, projId);
        ProjFitnessVideoCourse dish = mapStruct.toEntity(req);
        dish.setProjId(projId);
        save(dish);
        projLmsI18nService.handleI18n(Collections.singletonList(dish),projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        List<Integer> completedIdList = new ArrayList<>();
        LambdaQueryWrapper<ProjFitnessVideoCourse> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseModel::getId, idList);
        List<Integer> resourceUrlEmptyIdList = baseMapper.selectList(wrapper).stream()
                .filter(item -> StrUtil.isBlank(item.getResourceVideoUrl()))
                .map(BaseModel::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(resourceUrlEmptyIdList)) {
            completedIdList.addAll(resourceUrlEmptyIdList);
        }
        List<TaskResourceSection> taskList = taskResourceSectionService.query(PROJ_FITNESS_VIDEO_COURSE.getTableName(), PROJ_FITNESS_VIDEO_COURSE.getEntityFieldName(), idList);
        List<Integer> taskCompletedIdList = taskList.stream()
                .filter(item -> TaskResourceSectionStatusEnums.COMPLETED == item.getStatus())
                .map(TaskResourceSection::getTableId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskCompletedIdList)) {
            completedIdList.addAll(taskCompletedIdList);
        }
        if (CollUtil.isEmpty(completedIdList)) {
            return idList;
        }
        LambdaUpdateWrapper<ProjFitnessVideoCourse> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessVideoCourse::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjFitnessVideoCourse::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjFitnessVideoCourse::getId, completedIdList);
        this.update(new ProjFitnessVideoCourse(), updateWrapper);
        idList.removeAll(completedIdList);
        return idList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessVideoCourse> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessVideoCourse::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFitnessVideoCourse::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessVideoCourse::getId, idList);
        this.update(new ProjFitnessVideoCourse(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessVideoCourse> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessVideoCourse::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjFitnessVideoCourse::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjFitnessVideoCourse::getId, idList);
        this.update(new ProjFitnessVideoCourse(), wrapper);
    }

    private void check(Integer id, ProjFitnessVideoCourseAddReq req, Integer projId) {
        LambdaQueryWrapper<ProjFitnessVideoCourse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessVideoCourse::getName, req.getName())
                .eq(ProjFitnessVideoCourse::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjFitnessVideoCourse> dishList = baseMapper.selectList(wrapper);
        Set<String> nameSet = dishList.stream().map(ProjFitnessVideoCourse::getName).collect(Collectors.toSet());
        BizExceptionUtil.throwIf(nameSet.contains(req.getName()), "name already exists");
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessVideoCourse::getEventName, req.getEventName())
                .eq(ProjFitnessVideoCourse::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        dishList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= dishList.stream().map(ProjFitnessVideoCourse::getName).collect(Collectors.toSet());
        BizExceptionUtil.throwIf(eventNameSet.contains(req.getEventName()), "eventName already exists");
        if (ObjUtil.isNull(id)) {
            return;
        }
        ProjFitnessVideoCourse course = baseMapper.selectById(id);
        BizExceptionUtil.throwIf(ObjUtil.isNull(course), "course not exists");
        BizExceptionUtil.throwIf(ObjUtil.equals(course.getStatus(),GlobalConstant.STATUS_ENABLE), "course status is enable, can not update");
    }


    private void injectionTaskStatus(List<ProjFitnessVideoCourseListVO> videoList) {
        if (CollUtil.isEmpty(videoList)) {
            return;
        }

        Set<Integer> idSet = videoList.stream().map(ProjFitnessVideoCourseListVO::getId).collect(Collectors.toSet());
        List<TaskResourceSection> taskStatusList = taskResourceSectionService.find(PROJ_FITNESS_VIDEO_COURSE.getTableName(), PROJ_FITNESS_VIDEO_COURSE.getEntityFieldName(), idSet);
        if (CollUtil.isEmpty(taskStatusList)) {
            return;
        }

        Map<Integer, List<TaskResourceSection>> taskStatusMap = taskStatusList.stream().collect(Collectors.groupingBy(TaskResourceSection::getTableId));
        for (ProjFitnessVideoCourseListVO video : videoList) {
            List<TaskResourceSection> taskList = taskStatusMap.get(video.getId());
            if (CollUtil.isNotEmpty(taskList)) {
                TaskResourceSection sideTask = taskList.get(0);
                video.setTaskStatus(sideTask.getStatus());
            }
        }
    }
}
