package com.laien.web.biz.proj.oog104.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTaskStatusEnum;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>模板分页数据</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11 18:00
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "模板 分页", description = "模板 分页")
public class ProjFitnessTemplatePageVO {


    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

    /**
     * 类型：1 Regular Workout；
     */
    @ApiModelProperty
    private TemplateTypeEnums templateType;

    /**
     * 时间范围：1 5-10min；2 10-15min；3 15-20min；4 20-30min；
     */
    @ApiModelProperty("时间范围：1 5-10min；2 10-15min；3 15-20min；4 20-30min；")
    private WorkoutDurationRangeEnums durationRange;

    /**
     * 生成多少天的天数
     */
    @ApiModelProperty("生成多少天的天数")
    private Integer days;

    /**
     * 难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；
     */
    @ApiModelProperty
    private ManualDifficultyEnums level;

    /**
     * 特殊限制：1 Back；2 Knee；3 None；
     */
    @ApiModelProperty
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty("状态：0草稿，1启用；2禁用；")
    private Integer status;

    @ApiModelProperty("workout数量")
    private Integer workoutNum;

    @ApiModelProperty("最近一个任务的id")
    private Integer taskId;

    @ApiModelProperty("最近一个任务的状态")
    private TemplateTaskStatusEnum taskStatus;
}
