package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutGenerateExerciseVideo;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessWorkoutGenerateExerciseVideoMapper;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessExerciseVideoService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessWorkoutGenerateExerciseVideoService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_workout_generate_exercise_video 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessWorkoutGenerateExerciseVideoServiceImpl extends ServiceImpl<ProjFitnessWorkoutGenerateExerciseVideoMapper, ProjFitnessWorkoutGenerateExerciseVideo>
        implements IProjFitnessWorkoutGenerateExerciseVideoService {


    private final IProjFitnessExerciseVideoService projFitnessExerciseVideoService;

    @Override
    public List<ProjFitnessWorkoutGenerateExerciseVideo> listByWorkoutGenerateId(Integer workoutGenerateId) {
        if (workoutGenerateId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, workoutGenerateId)
                .eq(ProjFitnessWorkoutGenerateExerciseVideo::getDelFlag, 0)
                .orderByAsc(ProjFitnessWorkoutGenerateExerciseVideo::getId);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<ProjFitnessWorkoutGenerateExerciseVideo> exerciseVideoList) {
        if (CollectionUtils.isEmpty(exerciseVideoList)) {
            return;
        }
        saveBatch(exerciseVideoList);
    }


    @Override
    public void deleteByWorkoutIds(Set<Integer> workoutIds) {
        if(CollUtil.isEmpty(workoutIds)){
            return;
        }
        LambdaUpdateWrapper<ProjFitnessWorkoutGenerateExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, workoutIds)
                .set(BaseModel::getDelFlag, GlobalConstant.YES);
        baseMapper.update(new ProjFitnessWorkoutGenerateExerciseVideo(), wrapper);
    }

    @Override
    public List<ProjFitnessWorkoutGenerateExerciseVideo> listByTemplateIdAndTaskId(Integer templateId, Integer taskId) {
        if (templateId == null || taskId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateId, templateId)
                .eq(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateTaskId, taskId)
                .eq(ProjFitnessWorkoutGenerateExerciseVideo::getDelFlag, 0);
        return list(wrapper);
    }

    @Override
    public List<ProjFitnessExerciseVideoPageVO> listByWorkoutGenerateIds(List<Integer> workoutGenerateIds) {
        if (CollUtil.isEmpty(workoutGenerateIds)) {
            return Collections.emptyList();
        }
        // 查询关联表
        LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, workoutGenerateIds)
                .eq(ProjFitnessWorkoutGenerateExerciseVideo::getDelFlag, 0)
                .orderByAsc(ProjFitnessWorkoutGenerateExerciseVideo::getId);
        List<ProjFitnessWorkoutGenerateExerciseVideo> exerciseVideos = list(wrapper);

        if (CollUtil.isEmpty(exerciseVideos)) {
            return Collections.emptyList();
        }

        // 获取所有视频ID
        List<Integer> videoIds = exerciseVideos.stream()
                .map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId)
                .collect(Collectors.toList());

        // 批量查询视频信息并转换为VO
        return projFitnessExerciseVideoService.listVOByIds(videoIds);
    }
}
