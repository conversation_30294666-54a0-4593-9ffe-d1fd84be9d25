// New implementation class
package com.laien.web.biz.proj.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessUnit;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessUnitMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessDishMapStruct;
import com.laien.web.biz.proj.oog104.response.ProjFitnessUnitVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessUnitService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * Fitness Unit 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessUnitServiceImpl extends ServiceImpl<ProjFitnessUnitMapper, ProjFitnessUnit> implements IProjFitnessUnitService {

    private final ProjFitnessDishMapStruct projFitnessDishMapStruct;
    @Override
    public List<ProjFitnessUnitVO> query(Integer projId) {
        LambdaQueryWrapper<ProjFitnessUnit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessUnit::getProjId, projId)
                .eq(ProjFitnessUnit::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjFitnessUnit> unitList = baseMapper.selectList(wrapper);
        return projFitnessDishMapStruct.toUnitVOList(unitList);
    }
}
