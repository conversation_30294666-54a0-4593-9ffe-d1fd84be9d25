package com.laien.web.biz.proj.oog104.request;

import com.laien.common.oog104.enums.FitnessFastingArticleEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * fasting article对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@Accessors(chain = true)
@ApiModel(value="fasting article对象", description="fasting article对象")
public class ProjFitnessFastingArticleListReq {

    @ApiModelProperty(value = "动作展示名称")
    private String titleName;

    @ApiModelProperty(value = "类型")
    private FitnessFastingArticleEnums type;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
