/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog104.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>ID列表 Body </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ID列表 Body", description = "ID列表 Body")
public class IdListBodyReq {

    private List<Integer> idList;
}