package com.laien.web.biz.proj.oog104.service;

import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkoutImage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutImageUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutImageExportVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutImageVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_fitness_workout_image(proj_fitness_workout_image)】的数据库操作Service
* @createDate 2025-03-17 17:33:55
*/
public interface ProjFitnessWorkoutImageService extends IService<ProjFitnessWorkoutImage> {

    void add(ProjFitnessWorkoutImageAddReq req);

    void updateImg(ProjFitnessWorkoutImageUpdateReq req);

    void del(List<Integer> ids);

    void updateEnable(List<Integer> idList, boolean b);

    List<ProjFitnessWorkoutImageVO> list(ProjFitnessWorkoutImageListReq req);

    List<ProjFitnessWorkoutImageExportVO> listExportVO(ProjFitnessWorkoutImageListReq req);

    List<String> importExcel(Integer projId,MultipartFile excel);

    void updateSort(List<Integer> idList);

    ProjFitnessWorkoutImageVO detail(Integer id);
}
