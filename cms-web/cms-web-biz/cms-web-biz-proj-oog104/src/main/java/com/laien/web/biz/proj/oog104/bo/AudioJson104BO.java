package com.laien.web.biz.proj.oog104.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog104.enums.FitnessGenderEnums;
import com.laien.web.biz.proj.oog104.enums.AudioCategoryEnums;
import com.laien.web.common.file.bo.AudioJsonBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2025/3/20 13:57
 */
@Data
@NoArgsConstructor
//@AllArgsConstructor
public class AudioJson104BO extends AudioJsonBO {

    private boolean close = false;

    @JsonIgnore
    @ApiModelProperty(value = "当前音频的时长, 默认为female音频")
    private Integer duration;

    @JsonIgnore
    @ApiModelProperty(value = "系统音表原始id")
    private Integer soundId;

    @JsonIgnore
    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;

    @ApiModelProperty(value = "用于表明音频类型")
    private AudioCategoryEnums category;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    @JsonIgnore
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "soundScript")
    @JsonIgnore
    private String soundScript;

    @ApiModelProperty(value = "性别")
    @JsonIgnore
    private FitnessGenderEnums gender;


    public AudioJson104BO(AudioCategoryEnums category, String id, String url, String name, BigDecimal time, Integer duration, boolean close) {
        super(id, url, name, time);
        this.category = category;
        this.close = close;
        this.duration = duration;
    }

    public AudioJson104BO(AudioCategoryEnums category, String id, String url, String name,
                          BigDecimal time, Integer duration, boolean close,
                          Integer soundId, Boolean needTranslation,
                          Integer coreVoiceConfigI18nId, String soundScript, FitnessGenderEnums gender) {
        super(id, url, name, time);
        this.category = category;
        this.close = close;
        this.duration = duration;
        this.soundId = soundId;
        this.needTranslation = needTranslation;
        this.coreVoiceConfigI18nId = coreVoiceConfigI18nId;
        this.soundScript = soundScript;
        this.gender = gender;
    }
}
