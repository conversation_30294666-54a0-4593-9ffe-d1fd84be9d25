package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessMealPlan;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessMealPlanUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessMealPlanDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessMealPlanListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/31 15:15
 */
public interface IProjFitnessMealPlanService extends IService<ProjFitnessMealPlan> {

    /**
     * ProjFitnessMealPlan列表查询
     *
     * @param listReq listReq
     * @return ProjFitnessMealPlanListVO
     */
    List<ProjFitnessMealPlanListVO> selectMealPlanList(ProjFitnessMealPlanListReq listReq);

    /**
     * ProjFitnessMealPlan新增
     *
     * @param addReq
     */
    void saveMealPlan(ProjFitnessMealPlanAddReq addReq);

    /**
     * ProjFitnessMealPlan修改
     *
     * @param updateReq
     */
    void updateMealPlan(ProjFitnessMealPlanUpdateReq updateReq);

    /**
     * ProjFitnessMealPlan详情
     *
     * @param videoId
     * @return ProjFitnessMealPlanDetailVO
     */
    ProjFitnessMealPlanDetailVO getDetailById(Integer mealPlanId);

    /**
     * 排序
     *
     * @param idListReq
     */
    void sort(IdListReq idListReq);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);

}
