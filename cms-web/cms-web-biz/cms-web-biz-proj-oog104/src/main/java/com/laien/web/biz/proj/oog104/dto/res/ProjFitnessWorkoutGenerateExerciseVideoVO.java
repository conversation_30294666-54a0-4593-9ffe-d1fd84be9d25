package com.laien.web.biz.proj.oog104.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "健身训练生成的运动视频VO")
public class ProjFitnessWorkoutGenerateExerciseVideoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "健身训练生成ID")
    private Integer projFitnessWorkoutGenerateId;

    @ApiModelProperty(value = "运动视频ID")
    private Integer exerciseVideoId;

    @ApiModelProperty(value = "运动视频名称")
    private String exerciseVideoName;

    @ApiModelProperty(value = "运动视频时长(秒)")
    private Integer duration;

    @ApiModelProperty(value = "运动视频URL")
    private String videoUrl;

    @ApiModelProperty(value = "运动视频缩略图URL")
    private String thumbnailUrl;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}