package com.laien.web.biz.proj.oog104.response;

import com.laien.web.biz.proj.oog104.enums.ExtraTagEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "workout 详情", description = "workout 详情")
public class ProjFitnessWorkoutDetailVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "封面视频地址")
    private String coverVideoUrl;

    @ApiModelProperty(value = "封面视频时长")
    private Integer coverVideoDuration;

    @ApiModelProperty(value = "扩展标签")
    private List<ExtraTagEnums> extraTag;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "video list")
    private List<ProjFitnessWorkoutDetailVideoVO> videoList;

}
