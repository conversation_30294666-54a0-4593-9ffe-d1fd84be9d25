package com.laien.web.biz.proj.oog104.entity;

import com.laien.common.oog104.enums.dish.FitnessDishTypeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2024/12/31 14:57
 */
@Data
public class ProjFitnessMealPlanRelation extends BaseModel {

    private Integer projFitnessMealPlanId;

    private Integer day;

    private FitnessDishTypeEnums dishType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    private Integer projFitnessDishId;

}
