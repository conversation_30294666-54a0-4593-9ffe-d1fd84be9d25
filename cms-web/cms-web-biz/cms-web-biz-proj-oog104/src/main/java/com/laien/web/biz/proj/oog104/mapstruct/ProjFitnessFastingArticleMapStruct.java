package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessFastingArticle;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleAddReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessFastingArticleMapStruct {

    ProjFitnessFastingArticle toEntity(ProjFitnessFastingArticleAddReq articleAddReq);

    List<ProjFitnessFastingArticleListVO> toVOList(List<ProjFitnessFastingArticle> articleList);

    ProjFitnessFastingArticleDetailVO toDetailVO(ProjFitnessFastingArticle article);
}
