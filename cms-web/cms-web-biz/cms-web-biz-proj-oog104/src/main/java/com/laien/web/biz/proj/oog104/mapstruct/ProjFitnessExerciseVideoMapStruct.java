package com.laien.web.biz.proj.oog104.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessExerciseVideoImportReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoExportVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessExerciseVideoPageVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutGenerateVideoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *  ProjFitnessExerciseVideo 对象转换
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/06
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjFitnessExerciseVideoMapStruct {

    ProjFitnessExerciseVideo toEntity(ProjFitnessExerciseVideoAddReq entity);

    ProjFitnessExerciseVideo toEntity(ProjFitnessExerciseVideoImportReq req);

    List<ProjFitnessExerciseVideoPageVO> toPageList(List<ProjFitnessExerciseVideo> entities);

    ProjFitnessExerciseVideoDetailVO toDetailVO(ProjFitnessExerciseVideo entity);

    List<ProjFitnessExerciseVideoExportVO> toExportList(List<ProjFitnessExerciseVideo> list);

    ProjFitnessWorkoutGenerateVideoVO toGenerateVideoVO(ProjFitnessExerciseVideoPageVO v);
}
