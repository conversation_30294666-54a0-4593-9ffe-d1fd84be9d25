package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessIngredient;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessIngredientMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessDishMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessIngredientReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessIngredientVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessIngredientService;
import com.laien.web.frame.entity.BaseModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Fitness Ingredient 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Service
@RequiredArgsConstructor
public class ProjFitnessIngredientServiceImpl extends ServiceImpl<ProjFitnessIngredientMapper, ProjFitnessIngredient> implements IProjFitnessIngredientService {

    private final ProjFitnessDishMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatch(List<ProjFitnessIngredientReq> ingredientReqList, Integer dishId, Integer projId) {
        LambdaUpdateWrapper<ProjFitnessIngredient> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProjFitnessIngredient::getProjFitnessDishId, dishId);
        baseMapper.delete(wrapper);
        if(CollUtil.isEmpty(ingredientReqList)){
            return;
        }
        List<ProjFitnessIngredient> ingredientList = mapStruct.toIngredientEntityList(ingredientReqList);
        for (ProjFitnessIngredient ingredient : ingredientList) {
            ingredient.setProjId(projId)
                    .setProjFitnessDishId(dishId);
        }
        saveBatch(ingredientList);
        projLmsI18nService.handleI18n(ingredientList, projId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBatch(List<Integer> dishIdList) {
        if(CollUtil.isEmpty(dishIdList)){
            return;
        }
        LambdaUpdateWrapper<ProjFitnessIngredient> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjFitnessIngredient::getProjFitnessDishId, dishIdList);
        baseMapper.delete(wrapper);
    }

    @Override
    public List<ProjFitnessIngredientVO> query(Integer dishId) {
        if(null == dishId){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjFitnessIngredient> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessIngredient::getProjFitnessDishId, dishId)
                .orderByAsc(BaseModel::getId);
        List<ProjFitnessIngredient> ingredientList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(ingredientList)){
            return new ArrayList<>();
        }
        return mapStruct.toIngredientVOList(ingredientList);
    }
}
