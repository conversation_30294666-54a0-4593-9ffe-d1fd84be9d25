package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.response.ProjFitnessUnitVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessUnitService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * ProjFitnessUnitController
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */

@Api(tags = "项目管理:fitness Unit")
@RestController
@RequestMapping("/proj/fitnessUnit")
public class ProjFitnessUnitController extends ResponseController {

    @Resource
    private IProjFitnessUnitService projUnitService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjFitnessUnitVO>> list() {
        return succ(projUnitService.query(RequestContextUtils.getProjectId()));
    }
}
