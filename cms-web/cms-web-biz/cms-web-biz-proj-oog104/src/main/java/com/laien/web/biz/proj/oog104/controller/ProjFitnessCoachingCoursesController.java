package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoachingCourses;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCoachingCoursesService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Fitness CoachingCourses 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */

@Api(tags = "项目管理:fitnessCoachingCourses")
@RestController
@RequestMapping("/proj/fitnessCoachingCourses")
@RequiredArgsConstructor
public class ProjFitnessCoachingCoursesController extends ResponseController {

    private final IProjFitnessCoachingCoursesService service;

    @ApiOperation(value = "分页列表")
    @GetMapping( "/list")
    public ResponseResult<List<ProjFitnessCoachingCoursesListVO>> list(ProjFitnessCoachingCoursesPageReq pageReq) {
        return succ(service.list(pageReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessCoachingCoursesAddReq req) {
        service.save(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessCoachingCoursesUpdateReq req) {
        if (req == null || req.getId() == null) {
            return fail("ID cannot be null");
        }
        service.update(req, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessCoachingCoursesDetailVO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.deleteByIdList(idList);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        service.sort(idListReq);
        return succ();
    }
}
