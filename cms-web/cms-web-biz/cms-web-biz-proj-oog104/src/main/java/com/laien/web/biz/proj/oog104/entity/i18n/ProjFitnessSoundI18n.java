package com.laien.web.biz.proj.oog104.entity.i18n;

import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.web.biz.proj.oog104.bo.AudioJson104BO;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessSound;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessSoundI18n 实体类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessSoundI18n", description = "ProjFitnessSoundI18n")
public class ProjFitnessSoundI18n extends BaseAudioI18nModel {

    @ApiModelProperty(value = "声音脚本")
    @AppAudioTranslateField(resultFieldName = "result")
    private String soundScript;

    public ProjFitnessSoundI18n(AudioJson104BO json104BO) {
        super.setUniqueKey(json104BO.getSoundId());
        super.setCoreVoiceConfigI18nId(json104BO.getCoreVoiceConfigI18nId());
        this.soundScript = json104BO.getSoundScript();
    }

    public ProjFitnessSoundI18n(ProjFitnessSound sound) {
        super.setUniqueKey(sound.getId());
        super.setCoreVoiceConfigI18nId(sound.getCoreVoiceConfigI18nId());
        this.soundScript = sound.getSoundScript();
    }
}
