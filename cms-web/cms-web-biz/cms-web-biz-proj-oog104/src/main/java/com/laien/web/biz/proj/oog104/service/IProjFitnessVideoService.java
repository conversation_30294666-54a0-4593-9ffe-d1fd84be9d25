package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessVideo;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessVideoUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * proj_fitness_video 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IProjFitnessVideoService extends IService<ProjFitnessVideo> {

    /**
     * video分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjFitnessVideoPageVO> selectVideoPage(ProjFitnessVideoPageReq pageReq);

    /**
     * video新增
     *
     * @param videoReq videoReq
     */
    void saveVideo(ProjFitnessVideoAddReq videoReq);

    /**
     * video修改
     *
     * @param videoReq videoReq
     */
    void updateVideo(ProjFitnessVideoUpdateReq videoReq);

    /**
     * video详情
     *
     * @param id id
     * @return ProjFitnessVideoDetailVO
     */
    ProjFitnessVideoDetailVO getVideoDetail(Integer id);

    /**
     * video启用
     *
     * @param idList idList
     * @return list
     */
    List<Integer> updateEnableByIds(List<Integer> idList);

    /**
     * video禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * video删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 根据id 列表查询
     *
     * @param ids ids
     * @return list
     */
    List<ProjFitnessVideo> selectListByIds(List<Integer> ids);

}
