package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.response.ProjFitnessAllergenVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessAllergenService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * ProjFitnessAllergenController
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Api(tags = "项目管理:fitness allergen")
@RestController
@RequestMapping("/proj/fitnessAllergen")
public class ProjFitnessAllergenController extends ResponseController {

    @Resource
    private IProjFitnessAllergenService projAllergenService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjFitnessAllergenVO>> list() {
        return succ(projAllergenService.query(RequestContextUtils.getProjectId()));
    }
}
