package com.laien.web.biz.proj.oog104.mapper;

import com.laien.web.biz.proj.oog104.entity.PlanWorkoutCount;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * proj_fitness_plan Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface ProjFitnessPlanMapper extends BaseMapper<ProjFitnessPlan> {

    /**
     * <p>统计指定plan下各个状态的workout数量</p>
     *
     * @param idsStr paln id集合逗号分隔
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Integer>>
     * <AUTHOR>
     * @date 2025/1/9 10:40
     */
    @Select("select a.proj_fitness_plan_id planId,b.status workoutStatus, COUNT(a.proj_fitness_workout_id) workoutCount" +
            " from proj_fitness_plan_proj_fitness_workout a" +
            "         left join proj_fitness_workout b on a.proj_fitness_workout_id = b.id where a.del_flag = 0 and a.proj_fitness_plan_id in(${idsStr})" +
            " group by a.proj_fitness_plan_id, b.status")
    List<PlanWorkoutCount> countVideosByPlanIds(@Param("idsStr") String idsStr);
}
