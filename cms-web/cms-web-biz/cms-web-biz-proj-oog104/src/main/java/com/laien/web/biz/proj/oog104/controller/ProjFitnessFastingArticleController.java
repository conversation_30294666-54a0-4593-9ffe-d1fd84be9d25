package com.laien.web.biz.proj.oog104.controller;


import com.laien.web.biz.proj.oog104.entity.ProjFitnessFastingArticle;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleListReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessFastingArticleListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessFastingArticleService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * fitness fasting article 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Api(tags = "项目管理:fitness fasting article")
@RestController
@RequestMapping("/proj/fitnessFastingArticle")
public class ProjFitnessFastingArticleController extends ResponseController {

    @Resource
    private IProjFitnessFastingArticleService fitnessFastingArticleService;

    @ApiOperation(value = "分页列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjFitnessFastingArticleListVO>> list(ProjFitnessFastingArticleListReq articleListReq) {
        return succ(fitnessFastingArticleService.list(articleListReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjFitnessFastingArticleAddReq articleAddReq) {
        fitnessFastingArticleService.save(articleAddReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjFitnessFastingArticleUpdateReq articleUpdateReq) {
        fitnessFastingArticleService.update(articleUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjFitnessFastingArticleDetailVO> detail(@PathVariable Integer id) {
        return succ(fitnessFastingArticleService.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        fitnessFastingArticleService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        fitnessFastingArticleService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list can not be empty");
        }
        fitnessFastingArticleService.deleteByIds(idList);
        return succ();
    }


    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        fitnessFastingArticleService.sort(idListReq);
        return succ();
    }

}
