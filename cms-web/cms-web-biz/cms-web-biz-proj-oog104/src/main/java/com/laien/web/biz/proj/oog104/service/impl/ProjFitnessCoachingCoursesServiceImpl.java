package com.laien.web.biz.proj.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoachingCourses;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessCoachingCoursesRelation;
import com.laien.web.biz.proj.oog104.mapper.ProjFitnessCoachingCoursesMapper;
import com.laien.web.biz.proj.oog104.mapstruct.ProjFitnessCoachingCoursesMapStruct;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessCoachingCoursesUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessCoachingCoursesListVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessVideoCourseListVO;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCoachingCoursesRelationService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessCoachingCoursesService;
import com.laien.web.biz.proj.oog104.service.IProjFitnessVideoCourseService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.request.IdReq;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Fitness CoachingCourses 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessCoachingCoursesServiceImpl extends ServiceImpl<ProjFitnessCoachingCoursesMapper, ProjFitnessCoachingCourses> implements IProjFitnessCoachingCoursesService {

    private final ProjFitnessCoachingCoursesMapStruct mapStruct;
    private final IProjFitnessVideoCourseService videoCourseService;
    private final IProjFitnessCoachingCoursesRelationService relationService;
    private final IProjLmsI18nService projLmsI18nService;
    private static final String AGE_GROUPS ="age_groups";
    private static final String TYPES ="types";

    @Override
    public List<ProjFitnessCoachingCoursesListVO> list(ProjFitnessCoachingCoursesPageReq req, Integer projId) {
        LambdaQueryWrapper<ProjFitnessCoachingCourses> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(req.getName()), ProjFitnessCoachingCourses::getName, req.getName())
                .in(CollectionUtils.isNotEmpty(req.getIds()), ProjFitnessCoachingCourses::getId, req.getIds())
                .eq(null != req.getDifficulty(), ProjFitnessCoachingCourses::getDifficulty, req.getDifficulty())
                .eq(null != req.getStatus(), ProjFitnessCoachingCourses::getStatus, req.getStatus())
                .eq(null != projId , ProjFitnessCoachingCourses::getProjId, projId)
                .orderByAsc(ProjFitnessCoachingCourses::getSorted)
                .orderByDesc(BaseModel::getId);
        this.multipleSearchContainsOne(wrapper, req.getAgeGroups(), AGE_GROUPS);
        this.multipleSearchContainsOne(wrapper, req.getTypes(), TYPES);
        List<ProjFitnessCoachingCourses> list = this.list(wrapper);
        List<ProjFitnessCoachingCoursesListVO> voList = mapStruct.toVOList(list);
        if (CollUtil.isEmpty(voList)) {
            return voList;
        }
        //关联查询videoCourse统计信息
        List<IdAndStatusCountsRes> countsRes = baseMapper.countVideoCourseByIds(voList.stream().map(ProjFitnessCoachingCoursesListVO::getId).collect(Collectors.toList()));
        Map<Integer, List<IdAndStatusCountsRes>>  idStatusCountsMap = countsRes.stream().collect(Collectors.groupingBy(IdAndStatusCountsRes::getId));
        for (ProjFitnessCoachingCoursesListVO vo : voList) {
            if (!idStatusCountsMap.containsKey(vo.getId())) {
               continue;
            }
            List<IdAndStatusCountsRes> statusCounts = idStatusCountsMap.get(vo.getId());
            //get all count and disabled count
            int totalCount = statusCounts.stream().mapToInt(IdAndStatusCountsRes::getCounts).sum();
            int enabledCount = statusCounts.stream().filter(statusCount ->
                            Objects.equals(statusCount.getStatus(), GlobalConstant.STATUS_ENABLE))
                    .mapToInt(IdAndStatusCountsRes::getCounts).sum();
            vo.setVideoCourseTotalNum(totalCount);
            vo.setVideoCourseEnabledNum(enabledCount);
        }
        return voList;
    }


    @Override
    public ProjFitnessCoachingCoursesDetailVO findDetailById(Integer id) {
        ProjFitnessCoachingCourses entity = this.getById(id);
        ProjFitnessCoachingCoursesDetailVO detailVO = mapStruct.toDetailVO(entity);
        //query relation
        LambdaQueryWrapper<ProjFitnessCoachingCoursesRelation> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.eq(ProjFitnessCoachingCoursesRelation::getProjFitnessCoachingCoursesId, id)
                .eq(BaseModel::getDelFlag, GlobalConstant.NO);
        List<ProjFitnessCoachingCoursesRelation> relationList = relationService.list(relationWrapper);
        List<Integer> videoCourseIds = relationList.stream().map(ProjFitnessCoachingCoursesRelation::getProjFitnessVideoCourseId).collect(Collectors.toList());
        //query videoCourse
        List<ProjFitnessVideoCourseListVO> videoCourseList = new ArrayList<>();
        if (CollUtil.isNotEmpty(videoCourseIds)) {
            videoCourseList = videoCourseService.listVOByIds(videoCourseIds);
            //按videoCourseIds的顺序排序
            videoCourseList.sort(Comparator.comparingInt(videoCourse -> videoCourseIds.indexOf(videoCourse.getId())));
        }
        detailVO.setVideoCourseList(videoCourseList);
        return detailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjFitnessCoachingCoursesUpdateReq req, Integer projId) {
        Integer id = req.getId();
        this.check(id, req, projId);
        ProjFitnessCoachingCourses entity = mapStruct.toEntity(req);
        LambdaUpdateWrapper<ProjFitnessCoachingCourses> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BaseModel::getId, id);
        //需要更新为null的字段单独处理
        wrapper.set(ProjFitnessCoachingCourses::getNewStartTime, entity.getNewStartTime());
        wrapper.set(ProjFitnessCoachingCourses::getNewEndTime, entity.getNewEndTime());
        entity.setId(id);
        this.update(entity, wrapper);
        this.removeRelation(id);
        this.saveRelation(req.getVideoCourseList(), id);
        projLmsI18nService.handleI18n(Collections.singletonList(entity), projId);
    }

    private void removeRelation(Integer id) {
        LambdaUpdateWrapper<ProjFitnessCoachingCoursesRelation> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessCoachingCoursesRelation::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjFitnessCoachingCoursesRelation::getProjFitnessCoachingCoursesId, id);
        relationService.update(new ProjFitnessCoachingCoursesRelation(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(ProjFitnessCoachingCoursesAddReq req, Integer projId) {
        check(null, req, projId);
        ProjFitnessCoachingCourses entity = mapStruct.toEntity(req);
        entity.setProjId(projId);
        save(entity);
        //save relation
        this.saveRelation(req.getVideoCourseList(), entity.getId());
        projLmsI18nService.handleI18n(Collections.singletonList(entity), projId);
    }

    private void saveRelation(List<IdReq> videoCourseList, Integer id) {
        if (CollUtil.isNotEmpty(videoCourseList)) {
            List<ProjFitnessCoachingCoursesRelation> relationList = new ArrayList<>();
            for (IdReq req : videoCourseList) {
                ProjFitnessCoachingCoursesRelation relation = new ProjFitnessCoachingCoursesRelation();
                relation.setProjFitnessCoachingCoursesId(id);
                relation.setProjFitnessVideoCourseId(req.getId());
                relationList.add(relation);
            }
            relationService.saveBatch(relationList);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessCoachingCourses> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProjFitnessCoachingCourses::getStatus, GlobalConstant.STATUS_ENABLE);
        updateWrapper.in(ProjFitnessCoachingCourses::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        updateWrapper.in(ProjFitnessCoachingCourses::getId, idList);
        this.update(new ProjFitnessCoachingCourses(), updateWrapper);
        return idList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessCoachingCourses> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessCoachingCourses::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjFitnessCoachingCourses::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjFitnessCoachingCourses::getId, idList);
        this.update(new ProjFitnessCoachingCourses(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Integer> idList) {
        LambdaUpdateWrapper<ProjFitnessCoachingCourses> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjFitnessCoachingCourses::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjFitnessCoachingCourses::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjFitnessCoachingCourses::getId, idList);
        this.update(new ProjFitnessCoachingCourses(), wrapper);
    }

    private void check(Integer id, ProjFitnessCoachingCoursesAddReq req, Integer projId) {
        LambdaQueryWrapper<ProjFitnessCoachingCourses> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessCoachingCourses::getName, req.getName())
                .eq(ProjFitnessCoachingCourses::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjFitnessCoachingCourses> dishList = baseMapper.selectList(wrapper);
        Set<String> nameSet = dishList.stream().map(ProjFitnessCoachingCourses::getName).collect(Collectors.toSet());
        if (nameSet.contains(req.getName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessCoachingCourses::getEventName, req.getEventName())
                .eq(ProjFitnessCoachingCourses::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        dishList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= dishList.stream().map(ProjFitnessCoachingCourses::getName).collect(Collectors.toSet());
        if (eventNameSet.contains(req.getEventName())) {
            String error = "eventName already exists";
            throw new BizException(error);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<ProjFitnessCoachingCourses> dishList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjFitnessCoachingCourses info = new ProjFitnessCoachingCourses();
            info.setSorted(i)
                    .setId(idList.get(i));
            dishList.add(info);
        }
        updateBatchById(dishList);
    }

    /**
     * 多选查询处理 有交集为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsOne(LambdaQueryWrapper<ProjFitnessCoachingCourses> wrapper, List<? extends com.laien.common.core.enums.IEnumBase> options, String tableField) {
        if(CollUtil.isEmpty(options)){
            return;
        }
        wrapper.and(orWrapper ->
                options.forEach(equipment ->
                        orWrapper.or().apply("FIND_IN_SET({0},"+tableField+")", equipment.getCode())));
    }

    /**
     * 多选查询处理 包含为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsAll(LambdaQueryWrapper<ProjFitnessCoachingCourses> wrapper, List<? extends com.laien.common.core.enums.IEnumBase> options, String tableField) {
        // 所有 find in set 为真
        if(CollUtil.isEmpty(options)){
            return;
        }
        wrapper.and(o ->
                options.forEach(equipmentEnums -> o.apply("FIND_IN_SET({0},"+tableField+")",
                        equipmentEnums.getCode())));
    }
}
