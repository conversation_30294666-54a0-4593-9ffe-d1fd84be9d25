package com.laien.web.biz.proj.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog104.entity.ProjFitnessWorkout;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutAddReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutPageReq;
import com.laien.web.biz.proj.oog104.request.ProjFitnessWorkoutUpdateReq;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutDetailVO;
import com.laien.web.biz.proj.oog104.response.ProjFitnessWorkoutPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * proj_fitness_workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IProjFitnessWorkoutService extends IService<ProjFitnessWorkout> {

    /**
     * workout分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjFitnessWorkoutPageVO> selectWorkoutPage(ProjFitnessWorkoutPageReq pageReq);

    /**
     * workout新增
     *
     * @param workoutReq workoutReq
     */
    void saveWorkout(ProjFitnessWorkoutAddReq workoutReq);

    /**
     * workout修改
     *
     * @param workoutReq workoutReq
     */
    void updateWorkout(ProjFitnessWorkoutUpdateReq workoutReq);

    /**
     * workout详情
     *
     * @param id id
     * @return ProjFitnessWorkoutDetailVO
     */
    ProjFitnessWorkoutDetailVO getWorkoutDetail(Integer id);

    /**
     * workout启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * workout禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * workout删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 生成m3u8文件
     *
     * @param m3u8Req m3u8Req
     * @return Boolean
     */
    Boolean generateM3u8(ProjFitnessWorkoutGenerateM3u8Req m3u8Req);

    /**
     * 根据id 列表查询
     *
     * @param ids ids
     * @return list
     */
    List<ProjFitnessWorkout> selectListByIds(List<Integer> ids);
    
}
