package com.laien.web.biz.proj.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.common.oog104.enums.FitnessFastingArticleEnums;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *     ProjFitnessFastingArticle
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "proj_fitness_fasting_article",autoResultMap = true)
@ApiModel(value="FitnessFastingArticle对象", description="fitness fasting article")
public class ProjFitnessFastingArticle extends BaseModel implements CoreI18nModel {

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_FASTING_ARTICLE;

    @ApiModelProperty(value = "动作展示名称")
    @TranslateField
    private String titleName;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型")
    private FitnessFastingArticleEnums type;

    @ApiModelProperty(value = "内容")
    @TranslateField(type = TranslationTaskTypeEnums.HTML)
    private String content;

    @ApiModelProperty(value = "参考文档")
    private String reference;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer sorted;
}
