<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog104.mapper.ProjFitnessTemplateMapper">

    <!-- 多个字段中某个字段的类型处理器配置 -->
    <resultMap id="resultMap" type="com.laien.web.biz.proj.oog104.entity.ProjFitnessTemplate">
        <id property="id" column="id"/>
        <result property="delFlag" column="de_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="name" column="name"/>
        <result property="exclusiveType" column="exclusive_type"/>
        <result property="templateType" column="template_type"/>
        <result property="durationRange" column="duration_range"/>
        <result property="days" column="days"/>
        <result property="level" column="level"/>
        <result property="specialLimit" column="special_limit"
                typeHandler="com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnumsTypeHandler"/>
        <result property="status" column="status"/>
        <result property="taskId" column="taskId"/>
        <result property="taskStatus" column="taskStatus"/>
        <result property="projId" column="proj_id"/>
    </resultMap>
    <select id="pageWithTask" resultMap="resultMap">

        SELECT
        pft.*,
        latest_task.status AS taskStatus,
        latest_task.id AS taskId
        FROM proj_fitness_template pft
        LEFT JOIN (
        SELECT pftt1.*
        FROM proj_fitness_template_task pftt1
        INNER JOIN (
        SELECT proj_fitness_template_id, MAX(id) AS max_id
        FROM proj_fitness_template_task
        GROUP BY proj_fitness_template_id
        ) latest
        ON pftt1.proj_fitness_template_id = latest.proj_fitness_template_id
        AND pftt1.id = latest.max_id
        ) latest_task ON pft.id = latest_task.proj_fitness_template_id
        <where>
            pft.del_flag = 0
            <if test="param.name != null and param.name != ''">
                AND pft.name LIKE CONCAT('%', #{param.name}, '%')
            </if>
            <if test="param.templateType != null">
                AND pft.template_type = #{param.templateType}
            </if>
            <if test="param.durationRange!= null">
                AND pft.duration_range = #{param.durationRange}
            </if>
            <if test="param.status!= null">
                AND pft.status = #{param.status}
            </if>
            <if test="param.taskStatus!= null">
                AND latest_task.status = #{param.taskStatus}
            </if>
            <if test="param.level!= null">
                AND pft.level = #{param.level}
            </if>
            <if test="param.specialLimit != null and !param.specialLimit.isEmpty()">
                <foreach collection="param.specialLimit" item="item">
                    AND FIND_IN_SET(#{item}, pft.special_limit)
                </foreach>
            </if>
            <if test="param.exclusiveType!=null">
                AND pft.exclusive_type = #{param.exclusiveType}
            </if>
        </where>
        ORDER BY pft.id DESC
    </select>
</mapper>