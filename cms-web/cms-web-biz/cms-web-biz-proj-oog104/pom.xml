<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cms-web-biz</artifactId>
        <groupId>com.laien</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cms-web-biz-proj-oog104</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <artifactId>cms-web-biz-proj-core</artifactId>
            <groupId>com.laien</groupId>
        </dependency>
        <dependency>
            <artifactId>cms-common-oog104</artifactId>
            <groupId>com.laien</groupId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <artifactId>cms-common-lms-client</artifactId>
            <groupId>com.laien</groupId>
        </dependency>
        <dependency>
            <artifactId>cms-common-lms</artifactId>
            <groupId>com.laien</groupId>
        </dependency>
        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
