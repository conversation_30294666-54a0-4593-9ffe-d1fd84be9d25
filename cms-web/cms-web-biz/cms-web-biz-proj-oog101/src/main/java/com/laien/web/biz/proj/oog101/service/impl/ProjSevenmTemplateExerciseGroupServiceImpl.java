package com.laien.web.biz.proj.oog101.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplateExerciseGroup;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmTemplateExerciseGroupMapper;
import com.laien.web.biz.proj.oog101.service.IProjSevenmTemplateExerciseGroupService;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
public class ProjSevenmTemplateExerciseGroupServiceImpl extends ServiceImpl<ProjSevenmTemplateExerciseGroupMapper, ProjSevenmTemplateExerciseGroup>
    implements IProjSevenmTemplateExerciseGroupService {

    @Override
    public List<ProjSevenmTemplateExerciseGroup> listByTemplateIds(Collection<Integer> templateIds) {
        LambdaQueryWrapper<ProjSevenmTemplateExerciseGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjSevenmTemplateExerciseGroup::getProjSevenmTemplateId, templateIds);
        queryWrapper.eq(ProjSevenmTemplateExerciseGroup::getDelFlag, GlobalConstant.NO);
        return list(queryWrapper);
    }
}




