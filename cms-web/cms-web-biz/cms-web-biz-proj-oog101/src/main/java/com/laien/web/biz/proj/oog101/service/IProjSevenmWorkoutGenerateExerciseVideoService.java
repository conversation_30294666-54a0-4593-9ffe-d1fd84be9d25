package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutGenerateExerciseVideo;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_sevenm_workout_generate_exercise_video 服务接口
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
public interface IProjSevenmWorkoutGenerateExerciseVideoService extends IService<ProjSevenmWorkoutGenerateExerciseVideo> {

    /**
     * 根据WorkoutGenerateId查询关联的ExerciseVideo列表
     *
     * @param workoutGenerateId workoutGenerateId
     * @return 关联的ExerciseVideo列表
     */
    List<ProjSevenmWorkoutGenerateExerciseVideo> listByWorkoutGenerateId(Integer workoutGenerateId);

    /**
     * 批量保存WorkoutGenerateExerciseVideo
     *
     * @param exerciseVideoList exerciseVideoList
     */
    void batchSave(List<ProjSevenmWorkoutGenerateExerciseVideo> exerciseVideoList);

    /**
     * 根据workoutIds删除关联的ExerciseVideo
     * @param workoutIds workoutIds
     */
    void deleteByWorkoutIds(Set<Integer> workoutIds);

    /**
     * 根据模板ID和任务ID查询关联的ExerciseVideo列表
     *
     * @param templateId 模板ID
     * @param taskId 任务ID
     * @return 关联的ExerciseVideo列表
     */
    List<ProjSevenmWorkoutGenerateExerciseVideo> listByTemplateIdAndTaskId(Integer templateId, Integer taskId);

    /**
     * 批量查询运动视频关联信息
     */
    List<ProjSevenmExerciseVideoPageVO> listByWorkoutGenerateIds(List<Integer> workoutGenerateIds);
}
