package com.laien.web.biz.proj.oog101.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog101.enums.SevenmDifficultyEnums;
import com.laien.common.oog101.enums.SevenmSpecialLimitEnums;
import com.laien.common.oog101.enums.SevenmTemplateTypeEnums;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p> 生成 template 参数 </p>
 *
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "生成 template 参数", description = "生成 template 参数")
public class ProjSevenmTemplateGenerateReq {
    @JsonIgnore
    private Integer projId;

    private SevenmTemplateTypeEnums templateType;

    private List<SevenmDifficultyEnums> levelList;

    private List<SevenmSpecialLimitEnums> specialLimitList;

    private Integer days;
}
