package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmSound;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundPageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;


public interface IProjSevenmSoundService extends IService<ProjSevenmSound> {
    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    PageRes<ProjSevenmSoundPageVO> selectSoundPage(ProjSevenmSoundPageReq req);

    /**
     * 添加 sound
     *
     * @param req
     */
    void saveSound(ProjSevenmSoundAddReq req);

    /**
     * 修改 sound
     *
     * @param req
     */
    void updateSound(ProjSevenmSoundUpdateReq req);

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    ProjSevenmSoundDetailVO getDetail(Integer id);

    /**
     * 批量启用
     * @param idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用
     * @param idList
     */
    void updateDisableByIds(List<Integer> idList);
}