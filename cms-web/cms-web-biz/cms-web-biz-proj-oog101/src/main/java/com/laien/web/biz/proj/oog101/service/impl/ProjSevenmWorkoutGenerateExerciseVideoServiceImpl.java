package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutGenerateExerciseVideo;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmWorkoutGenerateExerciseVideoMapper;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmExerciseVideoService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmWorkoutGenerateExerciseVideoService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_sevenm_workout_generate_exercise_video 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjSevenmWorkoutGenerateExerciseVideoServiceImpl extends ServiceImpl<ProjSevenmWorkoutGenerateExerciseVideoMapper, ProjSevenmWorkoutGenerateExerciseVideo>
        implements IProjSevenmWorkoutGenerateExerciseVideoService {


    private final IProjSevenmExerciseVideoService service;

    @Override
    public List<ProjSevenmWorkoutGenerateExerciseVideo> listByWorkoutGenerateId(Integer workoutGenerateId) {
        if (workoutGenerateId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ProjSevenmWorkoutGenerateExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId, workoutGenerateId)
                .eq(ProjSevenmWorkoutGenerateExerciseVideo::getDelFlag, 0)
                .orderByAsc(ProjSevenmWorkoutGenerateExerciseVideo::getId);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<ProjSevenmWorkoutGenerateExerciseVideo> exerciseVideoList) {
        if (CollectionUtils.isEmpty(exerciseVideoList)) {
            return;
        }
        saveBatch(exerciseVideoList);
    }


    @Override
    public void deleteByWorkoutIds(Set<Integer> workoutIds) {
        if(CollUtil.isEmpty(workoutIds)){
            return;
        }
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerateExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId, workoutIds)
                .set(BaseModel::getDelFlag, GlobalConstant.YES);
        baseMapper.update(new ProjSevenmWorkoutGenerateExerciseVideo(), wrapper);
    }

    @Override
    public List<ProjSevenmWorkoutGenerateExerciseVideo> listByTemplateIdAndTaskId(Integer templateId, Integer taskId) {
        if (templateId == null || taskId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ProjSevenmWorkoutGenerateExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmTemplateId, templateId)
                .eq(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmTemplateTaskId, taskId)
                .eq(ProjSevenmWorkoutGenerateExerciseVideo::getDelFlag, 0);
        return list(wrapper);
    }

    @Override
    public List<ProjSevenmExerciseVideoPageVO> listByWorkoutGenerateIds(List<Integer> workoutGenerateIds) {
        if (CollUtil.isEmpty(workoutGenerateIds)) {
            return Collections.emptyList();
        }
        // 查询关联表
        LambdaQueryWrapper<ProjSevenmWorkoutGenerateExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId, workoutGenerateIds)
                .eq(ProjSevenmWorkoutGenerateExerciseVideo::getDelFlag, 0)
                .orderByAsc(ProjSevenmWorkoutGenerateExerciseVideo::getId);
        List<ProjSevenmWorkoutGenerateExerciseVideo> exerciseVideos = list(wrapper);

        if (CollUtil.isEmpty(exerciseVideos)) {
            return Collections.emptyList();
        }

        // 获取所有视频ID
        List<Integer> videoIds = exerciseVideos.stream()
                .map(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmExerciseVideoId)
                .collect(Collectors.toList());

        // 批量查询视频信息并转换为VO
        return service.listVOByIds(videoIds);
    }
}
