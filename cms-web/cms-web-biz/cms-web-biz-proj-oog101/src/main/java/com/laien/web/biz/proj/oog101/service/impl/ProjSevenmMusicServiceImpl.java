package com.laien.web.biz.proj.oog101.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmMusic;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmMusicMapper;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmMusicDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmMusicPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmMusicService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_ENABLE;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Service
public class ProjSevenmMusicServiceImpl extends ServiceImpl<ProjSevenmMusicMapper, ProjSevenmMusic>
        implements IProjSevenmMusicService {

    @Override
    public PageRes<ProjSevenmMusicPageVO> pageQuery(ProjSevenmMusicPageReq musicPageReq) {

        LambdaQueryWrapper<ProjSevenmMusic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmMusic::getProjId, musicPageReq.getProjId());
        queryWrapper.eq(Objects.nonNull(musicPageReq.getId()), ProjSevenmMusic::getId, musicPageReq.getId());
        queryWrapper.like(!StringUtils.isEmpty(musicPageReq.getMusicName()), ProjSevenmMusic::getMusicName, musicPageReq.getMusicName());
        queryWrapper.eq(Objects.nonNull(musicPageReq.getMusicType()), ProjSevenmMusic::getMusicType, musicPageReq.getMusicType());
        queryWrapper.orderByDesc(ProjSevenmMusic::getId);

        Page<ProjSevenmMusic> musicPage = new Page<>(musicPageReq.getPageNum(), musicPageReq.getPageSize());
        IPage<ProjSevenmMusic> iPage = this.page(musicPage, queryWrapper);
        PageRes<ProjSevenmMusicPageVO> pageRes = PageConverter.convert(iPage, ProjSevenmMusicPageVO.class);
        return pageRes;
    }

    @Override
    public void insert(ProjSevenmMusicAddReq musicAddReq) {

        bizCheck(musicAddReq, null);
        ProjSevenmMusic yogaMusic = new ProjSevenmMusic();
        BeanUtils.copyProperties(musicAddReq, yogaMusic);

        yogaMusic.setStatus(STATUS_ENABLE);
        yogaMusic.setProjId(RequestContextUtils.getProjectId());
        save(yogaMusic);
    }

    private void bizCheck(ProjSevenmMusicAddReq addReq, Integer id) {

        LambdaQueryWrapper<ProjSevenmMusic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmMusic::getMusicName, addReq.getMusicName());
        queryWrapper.ne(Objects.nonNull(id), ProjSevenmMusic::getId, id);

        ProjSevenmMusic yogaMusic = getOne(queryWrapper);
        if (Objects.nonNull(yogaMusic)) {
            throw new BizException("Music name is existed.");
        }
    }

    @Override
    public void update(ProjSevenmMusicUpdateReq musicUpdateReq) {

        bizCheck(musicUpdateReq, musicUpdateReq.getId());
        ProjSevenmMusic yogaMusic = getById(musicUpdateReq.getId());

        BeanUtils.copyProperties(musicUpdateReq, yogaMusic);
        updateById(yogaMusic);
    }

    @Override
    public ProjSevenmMusicDetailVO getDetailById(Integer musicId) {

        ProjSevenmMusic yogaMusic = getById(musicId);
        if (Objects.isNull(yogaMusic)) {
            return null;
        }

        ProjSevenmMusicDetailVO musicDetailVO = new ProjSevenmMusicDetailVO();
        BeanUtils.copyProperties(yogaMusic, musicDetailVO);
        return musicDetailVO;
    }
}
