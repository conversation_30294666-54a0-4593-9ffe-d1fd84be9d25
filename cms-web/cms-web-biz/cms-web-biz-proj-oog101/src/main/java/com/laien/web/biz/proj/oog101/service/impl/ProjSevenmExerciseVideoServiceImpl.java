package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import com.laien.common.oog101.enums.SevenmVideoDirectionEnums;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmExerciseVideoMapper;
import com.laien.web.biz.proj.oog101.mapstruct.ProjSevenmExerciseVideoMapStruct;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoImportReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmExerciseVideoService;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import com.laien.web.frame.validation.Group;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_DRAFT;
import static com.laien.web.frame.constant.GlobalConstant.YES;

/**
 * <p>
 * proj_7m_exercise_video 服务类实现
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjSevenmExerciseVideoServiceImpl extends ServiceImpl<ProjSevenmExerciseVideoMapper, ProjSevenmExerciseVideo>
        implements IProjSevenmExerciseVideoService {

    private static final String EVENT_NAME ="event_name";
    private static final String EXERCISE_VIDEO_SEVENM_M3U8 = "project-sevenm-exercise-video-m3u8";

    private final FileService fileService;
    private final Validator validator;
    private final ProjSevenmExerciseVideoMapStruct mapStruct;
    private final IProjInfoService projInfoService;
    private final ICoreVoiceConfigI18nService i18nConfigService;
    private final IProjLmsI18nService lmsI18nService;

    @Override
    public List<ProjSevenmExerciseVideo> listEnable4AudoGenerate() {

        LambdaQueryWrapper<ProjSevenmExerciseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmExerciseVideo::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ProjSevenmExerciseVideo::getUsedForAuto, GlobalConstant.ONE);
        return list(queryWrapper);
    }

    @Override
    public PageRes<ProjSevenmExerciseVideoPageVO> selectVideoPage(ProjSevenmExerciseVideoPageReq pageReq) {
        LambdaQueryWrapper<ProjSevenmExerciseVideo> query = getLambdaQueryWrapper(pageReq);
        IPage<ProjSevenmExerciseVideo> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), query);
        List<ProjSevenmExerciseVideoPageVO> list = mapStruct.toPageList(page.getRecords());
        fillI18nConfigInfo(page.getRecords(),list);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    private void fillI18nConfigInfo(List<ProjSevenmExerciseVideo> records, List<ProjSevenmExerciseVideoPageVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjSevenmExerciseVideoPageVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private void fillI18nConfigInfoForDetail(ProjSevenmExerciseVideoDetailVO detailVO) {
        CoreVoiceConfigI18n config = i18nConfigService.getById(detailVO.getCoreVoiceConfigI18nId());
        if (config!= null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }

    private Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ProjSevenmExerciseVideo> records) {
        Set<Integer> configIds = records.stream().map(ProjSevenmExerciseVideo::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }

    @Override
    public List<ProjSevenmExerciseVideoPageVO> listVOByIds(List<Integer> ids) {
        LambdaQueryWrapper<ProjSevenmExerciseVideo> query = new LambdaQueryWrapper<>();
        query.in(ProjSevenmExerciseVideo::getId, ids);
        return mapStruct.toPageList(this.list(query));
    }

    private LambdaQueryWrapper<ProjSevenmExerciseVideo> getLambdaQueryWrapper(ProjSevenmExerciseVideoPageReq pageReq) {
        LambdaQueryWrapper<ProjSevenmExerciseVideo> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(pageReq.getId()), ProjSevenmExerciseVideo::getId, pageReq.getId())
                .like(StrUtil.isNotBlank(pageReq.getName()), ProjSevenmExerciseVideo::getName, pageReq.getName())
                .eq(ObjUtil.isNotNull(pageReq.getStatus()), ProjSevenmExerciseVideo::getStatus, pageReq.getStatus())
                .eq(ObjUtil.isNotNull(pageReq.getGender()), ProjSevenmExerciseVideo::getGender, pageReq.getGender())
                .eq(ObjUtil.isNotNull(pageReq.getExerciseType()), ProjSevenmExerciseVideo::getExerciseType, pageReq.getExerciseType())
                .eq(ObjUtil.isNotNull(pageReq.getType()), ProjSevenmExerciseVideo::getType, pageReq.getType())
                .eq(ObjUtil.isNotNull(pageReq.getIntensity()), ProjSevenmExerciseVideo::getIntensity, pageReq.getIntensity())
                .eq(ObjUtil.isNotNull(pageReq.getDifficulty()), ProjSevenmExerciseVideo::getDifficulty, pageReq.getDifficulty())
                .eq(ObjUtil.isNotNull(pageReq.getPosition()), ProjSevenmExerciseVideo::getPosition, pageReq.getPosition())
                .eq(ObjUtil.isNotNull(pageReq.getEquipment()), ProjSevenmExerciseVideo::getEquipment, pageReq.getEquipment())
                .eq(ObjUtil.isNotNull(pageReq.getVideoDirection()), ProjSevenmExerciseVideo::getVideoDirection, pageReq.getVideoDirection())
                .eq(ObjUtil.isNotNull(pageReq.getUsedForAuto()), ProjSevenmExerciseVideo::getUsedForAuto, pageReq.getUsedForAuto())
                .eq(ObjUtil.isNotNull(pageReq.getProjId()), ProjSevenmExerciseVideo::getProjId, pageReq.getProjId())
                .in(CollUtil.isNotEmpty(pageReq.getIds()), ProjSevenmExerciseVideo::getId, pageReq.getIds());
        query.orderByDesc(ProjSevenmExerciseVideo::getId);
        BitmaskEnumUtil.addBitmaskCondition(query, ProjSevenmExerciseVideo::getSpecialLimit, pageReq.getSpecialLimit(), true);
        BitmaskEnumUtil.addBitmaskCondition(query, ProjSevenmExerciseVideo::getTarget, pageReq.getTarget(), false);
        return query;
    }

    @Override
    public List<ProjSevenmExerciseVideoExportVO> exportVideos(ProjSevenmExerciseVideoPageReq pageReq) {
        LambdaQueryWrapper<ProjSevenmExerciseVideo> queryWrapper = this.getLambdaQueryWrapper(pageReq);
        List<ProjSevenmExerciseVideo> entityList = this.list(queryWrapper);
        List<ProjSevenmExerciseVideoExportVO> voList = mapStruct.toExportList(entityList);
        fillI18nConfigInfo4Export(entityList,voList);
        return voList;
    }

    private void fillI18nConfigInfo4Export(List<ProjSevenmExerciseVideo> records, List<ProjSevenmExerciseVideoExportVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjSevenmExerciseVideoExportVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveVideo(ProjSevenmExerciseVideoAddReq videoReq) {
        ProjSevenmExerciseVideo entity = mapStruct.toEntity(videoReq);
        check(videoReq,null,videoReq.getProjId());
        entity.setStatus(GlobalConstant.STATUS_DRAFT);
        entity.setEventName("");
        this.calcCalorie(entity.getCalorie(), entity);
        this.save(entity);
        //update eventName,调用生成videoUrl生成m3u
        LambdaUpdateWrapper<ProjSevenmExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmExerciseVideo::getEventName, entity.getId()+"+"+entity.getName())
                .set(ProjSevenmExerciseVideo::getVideoUrl, this.videoForM3u8(entity.getFrontVideoUrl(), entity.getFrontVideoDuration()))
                .eq(ProjSevenmExerciseVideo::getId, entity.getId());
        update(entity, wrapper);
        lmsI18nService.handleI18n(ListUtil.of(entity), projInfoService.getById(videoReq.getProjId()));
    }

    private String videoForM3u8(String videoUrl, Integer videoDuration) {
        List<TsMergeBO> videoList = new ArrayList<>(GlobalConstant.ONE);
        videoList.add(new TsMergeBO(fileService.getAbsoluteR2Url(videoUrl), videoDuration));
        UploadFileInfoRes videoR2Info = fileService.uploadMergeTSForM3U8R2(videoList, EXERCISE_VIDEO_SEVENM_M3U8);
        return videoR2Info.getFileRelativeUrl();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateVideo(ProjSevenmExerciseVideoUpdateReq videoReq) {
        Integer id = videoReq.getId();
        ProjSevenmExerciseVideo videoFind = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(videoFind),"Data not found");
        Integer projId = videoFind.getProjId();
        check(videoReq,id,projId);
        ProjSevenmExerciseVideo video = mapStruct.toEntity(videoReq);
        video.setId(videoReq.getId());
        this.calcCalorie(video.getCalorie(), video);
        //校验frontVideoUrl是否修改
        if(StrUtil.isNotBlank(videoReq.getFrontVideoUrl()) && !Objects.equals(videoReq.getFrontVideoUrl(), videoFind.getFrontVideoUrl())){
            video.setVideoUrl(this.videoForM3u8(video.getFrontVideoUrl(), video.getFrontVideoDuration()));
        }
        this.updateById(video);
        lmsI18nService.handleI18n(ListUtil.of(video), projInfoService.getById(projId));
    }

    @Override
    public ProjSevenmExerciseVideoDetailVO getVideoDetail(Integer id) {
        ProjSevenmExerciseVideo video = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(video),"Data not found");
        ProjSevenmExerciseVideoDetailVO videoDetailVO = mapStruct.toDetailVO(video);
        //组装leftRightDetail
        Integer leftRightId = video.getLeftRightVideoId();
        if (SevenmVideoDirectionEnums.LEFT.equals(video.getVideoDirection()) && null != leftRightId) {
            ProjSevenmExerciseVideo leftRightVideo = baseMapper.selectById(leftRightId);
            if(null != leftRightVideo){
                ProjSevenmExerciseVideoDetailVO leftRightDetailVO = mapStruct.toDetailVO(leftRightVideo);
                videoDetailVO.setLeftRightDetail(leftRightDetailVO);
            }
        }
        fillI18nConfigInfoForDetail(videoDetailVO);
        return videoDetailVO;
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSevenmExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmExerciseVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmExerciseVideo::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjSevenmExerciseVideo::getId, idList);
        boolean update = this.update(new ProjSevenmExerciseVideo(), wrapper);
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSevenmExerciseVideo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmExerciseVideo::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjSevenmExerciseVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmExerciseVideo::getId, idList);
        boolean update = this.update(new ProjSevenmExerciseVideo(), wrapper);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        this.update(new ProjSevenmExerciseVideo(), new LambdaUpdateWrapper<ProjSevenmExerciseVideo>()
                .set(ProjSevenmExerciseVideo::getDelFlag, YES)
                .eq(ProjSevenmExerciseVideo::getStatus, STATUS_DRAFT)
                .in(ProjSevenmExerciseVideo::getId, idList));
    }

    @Override
    public List<ProjSevenmExerciseVideo> query(Set<Integer> idSet) {
        LambdaQueryWrapper<ProjSevenmExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjSevenmExerciseVideo::getId, idSet);
        return this.list(wrapper);
    }

    /**
     * @param excelInputStream
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> importByExcel(InputStream excelInputStream, Integer projectId) {
        log.info("Proj7MExerciseVideo importByExcel Start-----------------");
        List<ProjSevenmExerciseVideoImportReq> importReqs = CollUtil.newArrayList();
        EasyExcel.read(excelInputStream, ProjSevenmExerciseVideoImportReq.class, new AnalysisEventListener<ProjSevenmExerciseVideoImportReq>() {
            @Override
            public void invoke(ProjSevenmExerciseVideoImportReq row, AnalysisContext analysisContext) {
                importReqs.add(row);
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();
        //2、过滤不符合输入规范的数据
        List<String> failMessage = CollUtil.newArrayList();
        List<ProjSevenmExerciseVideo> exerciseVideos = filterDirtyData(importReqs, failMessage);
        //3、通过判断exerciseVideos对象中id分成新增和修改两个list
        List<ProjSevenmExerciseVideo> insertExerciseVideoList = new ArrayList<>();
        List<ProjSevenmExerciseVideo> updateExerciseVideoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(exerciseVideos)) {
            for (ProjSevenmExerciseVideo video : exerciseVideos) {
                if (null == video.getId()) {
                    video.setProjId(projectId);
                    video.setStatus(GlobalConstant.STATUS_DRAFT);
                    video.setEventName("");
                    insertExerciseVideoList.add(video);
                } else {
                    updateExerciseVideoList.add(video);
                }
            }
        }
        //4、校验名称重复的数据
        insertExerciseVideoList = filterRepeatData(insertExerciseVideoList, failMessage,false);
        updateExerciseVideoList = filterRepeatData(updateExerciseVideoList, failMessage,true);

        //有错误信息直接返回，不执行后续逻辑
        if (CollUtil.isNotEmpty(failMessage)) {
            StringBuilder strBuilder = new StringBuilder();
            failMessage.forEach(s -> strBuilder.append("failMessage:").append(s).append("\r\n"));
            log.error(strBuilder.toString());
            log.info("proj7MExerciseVideo importByExcel Finish-----------------");
            return failMessage;
        }
        //5、新增
        if (CollUtil.isNotEmpty(insertExerciseVideoList)) {
            //设置insertExerciseVideoList的videourl
            insertExerciseVideoList.forEach(video -> video.setVideoUrl(this.videoForM3u8(video.getFrontVideoUrl(), video.getFrontVideoDuration())));
            this.saveBatch(insertExerciseVideoList);
            //批量插入后才可以获取到ID，再来处理eventName=id+name
            LambdaUpdateWrapper<ProjSevenmExerciseVideo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(ProjSevenmExerciseVideo::getId, insertExerciseVideoList.stream().map(ProjSevenmExerciseVideo::getId).collect(Collectors.toList()));
            updateWrapper.setSql(EVENT_NAME+" = CONCAT(id,'+',name)");
            this.update(updateWrapper);
        }
        //6.更新
        if (CollUtil.isNotEmpty(updateExerciseVideoList)) {
            //获取更新数据的原数据
            List<Integer> updateIds = updateExerciseVideoList.stream().map(ProjSevenmExerciseVideo::getId).collect(Collectors.toList());
            // 构建 Map，key 为 id，value 为原数据
            Map<Integer, ProjSevenmExerciseVideo> existMap = this.listByIds(updateIds).stream()
                    .collect(Collectors.toMap(ProjSevenmExerciseVideo::getId, video -> video));
            //批量设置videoUrl
            updateExerciseVideoList.forEach(video -> {
                ProjSevenmExerciseVideo exerciseVideo = existMap.get(video.getId());
                //校验frontVideoUrl是否修改
                if(StrUtil.isNotBlank(video.getFrontVideoUrl()) && !Objects.equals(video.getFrontVideoUrl(), exerciseVideo.getFrontVideoUrl())){
                    video.setVideoUrl(this.videoForM3u8(video.getFrontVideoUrl(), video.getFrontVideoDuration()));
                }
                //处理leftRightVideoId
                if (ObjUtil.equal(exerciseVideo.getVideoDirection(), SevenmVideoDirectionEnums.LEFT)) {
                    video.setLeftRightVideoId(exerciseVideo.getLeftRightVideoId());
                }
            });
            //批量保存
            updateBatchById(updateExerciseVideoList);
        }
        insertExerciseVideoList.addAll(updateExerciseVideoList);
        lmsI18nService.handleI18n(insertExerciseVideoList, projInfoService.getById(projectId));
        return failMessage;
    }

    /**
     * 过滤与数据库规则重复的数据
     * @param exerciseVideos
     * @param failMessage
     * @param updateFlag
     * @return
     */
    private List<ProjSevenmExerciseVideo> filterRepeatData(List<ProjSevenmExerciseVideo> exerciseVideos, List<String> failMessage,
                                                           boolean updateFlag) {
        if (CollUtil.isEmpty(exerciseVideos)) {
            return CollUtil.newArrayList();
        }
        // 提取唯一 name 并去重
        Set<String> nameSet = exerciseVideos.stream()
                .map(ProjSevenmExerciseVideo::getName)
                .collect(Collectors.toSet());
        // 查询数据库中已存在的 name
        LambdaQueryWrapper<ProjSevenmExerciseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjSevenmExerciseVideo::getName, nameSet);
        // 根据 updateFlag 决定是查询已存在的还是未存在的
        if (updateFlag) {
            queryWrapper.notIn(ProjSevenmExerciseVideo::getId, exerciseVideos.stream().map(ProjSevenmExerciseVideo::getId).collect(Collectors.toList()));
        }
        // 转换为 Set 提高查找性能
        Set<String> existNames = this.list(queryWrapper).stream()
                .map(this::getVideoUniqueKey).collect(Collectors.toSet());
        // 过滤掉数据库已存在的 name
        return exerciseVideos.stream().filter(video -> {
                    if (existNames.contains(this.getVideoUniqueKey(video))) {
                        failMessage.add(this.getVideoUniqueKey(video) + ": name duplicated in database");
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    private String getVideoUniqueKey(ProjSevenmExerciseVideo video) {
        return video.getName() + ":" + video.getExerciseType().getName();
    }

    private String getVideoUniqueKey(ProjSevenmExerciseVideoImportReq video) {
        return video.getName() + ":" + video.getExerciseType().getName();
    }
    /**
     * 过滤不符合业务规则的数据
     *
     * @param dataList
     * @param failMessage
     * @return
     */
    private List<ProjSevenmExerciseVideo> filterDirtyData(List<ProjSevenmExerciseVideoImportReq> dataList, List<String> failMessage) {
        List<ProjSevenmExerciseVideo> meetsCondiData = CollUtil.newArrayList();
        Optional.ofNullable(dataList).filter(CollUtil::isNotEmpty).ifPresent(data -> {
            Set<String> nameSet = new HashSet<>(); // 用于存储唯一的 name
            Set<String> i18nConfigNameSet = data.stream().map(ProjSevenmExerciseVideoImportReq::getCoreVoiceConfigI18nName).collect(Collectors.toSet());
            Map<String,Integer> i18nConfigNameIdMap =  i18nConfigService.listConfigByNames(i18nConfigNameSet).stream()
                    .collect(Collectors.toMap(CoreVoiceConfigI18n::getName, CoreVoiceConfigI18n::getId));
            for (ProjSevenmExerciseVideoImportReq req : data) {
                try {
                    Optional.ofNullable(validator.validate(req, Group.class)).ifPresent(result -> {
                        Optional<ConstraintViolation<ProjSevenmExerciseVideoImportReq>> firstError = result.stream().findFirst();
                        if (firstError.isPresent()) {
                            //校验失败，只记录第一条失败原因
                            failMessage.add(req.getName() + ":" + firstError.get().getMessage());
                        }
                        else if (!nameSet.add(this.getVideoUniqueKey(req))){
                            // 检查导入数据中是否有重复的 `name`
                            failMessage.add(req.getName() + ": name duplicated in import data");
                        }
                        else if (!validateTarget(req)) {
                            failMessage.add(req.getName() + ": target contains None and other options");
                        }
                        else if (!i18nConfigNameIdMap.containsKey(req.getCoreVoiceConfigI18nName())) {
                            failMessage.add(req.getName() + ": English Voice Name Not Found in TTS config");
                        }
                        else {
                            assembleVideos(req, meetsCondiData,i18nConfigNameIdMap);
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    failMessage.add(req.getName() + ":" + e.getMessage());
                }
            }
        });
        return meetsCondiData;
    }

    private boolean validateTarget(ProjSevenmExerciseVideoImportReq req) {
        List<SevenmTargetEnums> targets = req.getTarget();
        // 如果 targets 为空，允许
        if (CollUtil.isEmpty(targets)) {
            return true;
        }
        // 检查是否包含 None
        boolean hasNone = targets.contains(SevenmTargetEnums.NONE);
        // 如果包含 None，不能与其他选项一起选择
        return !hasNone || targets.size() == 1;  // 返回校验失败
    }

    private void assembleVideos(ProjSevenmExerciseVideoImportReq req, List<ProjSevenmExerciseVideo> meetsCondiData, Map<String, Integer> i18nConfigNameIdMap) {
        //生成video对象
        ProjSevenmExerciseVideo exerciseVideo = mapStruct.toEntity(req);
        //设置coreVoiceConfigI18nId
        exerciseVideo.setCoreVoiceConfigI18nId(i18nConfigNameIdMap.get(req.getCoreVoiceConfigI18nName()));
        //如果Calorie为空或为0，计算Calorie
        BigDecimal calorie = exerciseVideo.getCalorie();
        calcCalorie(calorie, exerciseVideo);
        meetsCondiData.add(exerciseVideo);
    }

    private void calcCalorie(BigDecimal calorie, ProjSevenmExerciseVideo exerciseVideo) {
        if (null == calorie || calorie.compareTo(BigDecimal.ZERO) == 0) {
            Integer met = exerciseVideo.getMet();
            Integer frontVideoDuration = exerciseVideo.getFrontVideoDuration();
            Integer sideVideoDuration = exerciseVideo.getSideVideoDuration();
            //Calorie = 「met * 75 / 3600 * 视频时长s * 3」，保留3位小数,使用Bigdecimal；
            BigDecimal totalDuration = new BigDecimal(frontVideoDuration).multiply(new BigDecimal(2)).add(new BigDecimal(sideVideoDuration));
            BigDecimal result = new BigDecimal(met).multiply(new BigDecimal("1.5")).multiply(new BigDecimal(75)).multiply(totalDuration).divide(new BigDecimal(3600*1000), 3, RoundingMode.HALF_UP);
            exerciseVideo.setCalorie(result);
        }
    }


    /**
     * 判断路径是否已经包含参数，追加时间戳参数
     * @param path 文件路径
     * @param timeStamp 当前时间戳
     * @return 拼接后的路径
     */
    private String appendTimeStamp(String path, Long timeStamp) {
        if (StrUtil.isBlank(path)) {
            return path;
        }
        String connector = path.contains("?") ? "&" : "?";
        return path + connector + timeStamp;
    }
    /**
     * 通过分隔符分隔的字段值获取枚举列表，过滤无效值
     *
     * @param condition 条件字段
     * @param values    分隔符分隔的字段值
     * @param separator 分隔符
     * @param <E>       枚举类型
     * @param <C>       字段类型
     * @return 匹配的枚举列表（不会为null）
     */
    public <E extends Enum<E>, C> List<E> getListBy(Func1<E, C> condition, String values, String separator) {
        if (StrUtil.isBlank(values)) {
            return Collections.emptyList();
        }
        return StrUtil.split(values, separator)
                .stream()
                .map(String::trim)
                .map(v -> EnumUtil.getBy(condition, (C) v)) // 保持原方法类型转换逻辑
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 校验
     *
     * @param videoReq videoReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjSevenmExerciseVideoAddReq videoReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjSevenmExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmExerciseVideo::getName, videoReq.getName())
                .eq(ProjSevenmExerciseVideo::getExerciseType, videoReq.getExerciseType())
                .ne(Objects.nonNull(id), ProjSevenmExerciseVideo::getId, id)
                .eq(ProjSevenmExerciseVideo::getProjId, projId);
        BizExceptionUtil.throwIf(this.count(wrapper) > 0, "name+exerciseType already exists");
        Integer leftRightVideoId = videoReq.getLeftRightVideoId();
        if (leftRightVideoId != null) {
            LambdaQueryWrapper<ProjSevenmExerciseVideo> leftRightWrapper = new LambdaQueryWrapper<>();
            leftRightWrapper.eq(ProjSevenmExerciseVideo::getId, leftRightVideoId)
                    .eq(ProjSevenmExerciseVideo::getProjId, projId)
                    .eq(ProjSevenmExerciseVideo::getDelFlag, GlobalConstant.NO);
            ProjSevenmExerciseVideo video = this.getOne(leftRightWrapper);
            BizExceptionUtil.throwIf(video == null, "leftRightVideo not exists");
            BizExceptionUtil.throwIf(GlobalConstant.STATUS_ENABLE != video.getStatus(), "leftRightVideo not enable");
            BizExceptionUtil.throwIf(!SevenmVideoDirectionEnums.RIGHT.equals(video.getVideoDirection()), "leftRightVideo not right");
        }
    }



}
