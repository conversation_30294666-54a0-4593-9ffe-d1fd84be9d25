package com.laien.web.biz.proj.oog101.response;

import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmSoundSubTypeEnums;
import com.laien.common.oog101.enums.SevenmSoundTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "Sound 分页", description = "Sound 分页")
public class ProjSevenmSoundPageVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "duration")
    private Integer duration;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "coreVoiceConfigI18nName")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private SevenmSoundTypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private SevenmSoundSubTypeEnums soundSubType;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "声音")
    private String url;

    @ApiModelProperty(value = "机器声音")
    private String robotUrl;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;
}
