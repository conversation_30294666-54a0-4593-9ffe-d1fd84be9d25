package com.laien.web.biz.proj.oog101.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.laien.common.oog101.enums.SevenmDifficultyEnums;
import com.laien.common.oog101.enums.SevenmSpecialLimitEnums;
import com.laien.common.oog101.enums.SevenmTemplateTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * template 详情
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "template 详情", description = "template 详情")
public class ProjSevenmTemplateDetailVO {

    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("template版本")
    private Integer dataVersion;

    @ApiModelProperty
    private SevenmTemplateTypeEnums templateType;

    @ApiModelProperty("生成多少天的天数")
    private Integer days;

    @ApiModelProperty("难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advanced；")
    private SevenmDifficultyEnums level;

    @ApiModelProperty
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty("状态：0草稿，1启用；2禁用；")
    private Integer status;

    @ApiModelProperty("warm up动作组")
    private ProjSevenmTemplateExerciseGroupDetailVO warmupExerciseGroup;

    @ApiModelProperty("main动作组")
    private ProjSevenmTemplateExerciseGroupDetailVO mainExerciseGroup;

    @ApiModelProperty("cool down动作组")
    private ProjSevenmTemplateExerciseGroupDetailVO coolDownExerciseGroup;

}
