package com.laien.web.biz.proj.oog101.bo;

import com.google.common.collect.Lists;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import com.laien.common.oog101.enums.SevenmWorkoutTargetEnums;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * TargetMappingBO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@Data
public class TargetMappingBO {

    private List<SevenmTargetEnums> target;

    private float percent;

    public static List<TargetMappingBO> getTargetMapping(SevenmWorkoutTargetEnums target) {
        List<TargetMappingBO> targetMappingBOList = Lists.newArrayList();
        List<SevenmWorkoutTargetEnums.TargetMapping> relatedTargets = target.getRelatedTargets();
        float totalPercent = 1.0f;
        for (int i = 0, relatedTargetsSize = relatedTargets.size(); i < relatedTargetsSize; i++) {
            SevenmWorkoutTargetEnums.TargetMapping mapping = relatedTargets.get(i);
            TargetMappingBO mappingBO = new TargetMappingBO();
            mappingBO.setTarget(mapping.getTarget());
            mappingBO.setPercent(mapping.getPercent());
            targetMappingBOList.add(mappingBO);
            totalPercent -= mapping.getPercent();
            if (i == relatedTargetsSize - 1 && totalPercent > 0) {
                TargetMappingBO mappingLast = new TargetMappingBO();
                mappingLast.setTarget(Collections.singletonList(SevenmTargetEnums.NONE));
                mappingLast.setPercent(totalPercent);
                targetMappingBOList.add(mappingLast);
            }
        }
        return targetMappingBOList;
    }
}
