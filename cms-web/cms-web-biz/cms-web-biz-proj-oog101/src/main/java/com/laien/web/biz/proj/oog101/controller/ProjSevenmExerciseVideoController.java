package com.laien.web.biz.proj.oog101.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmExerciseVideoService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 7M Exercise Video 前端控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Api(tags = "项目管理:7M Exercise Video")
@RestController
@RequestMapping("/proj/sevenmExerciseVideo")
public class ProjSevenmExerciseVideoController extends ResponseController {

    @Resource
    private IProjSevenmExerciseVideoService exerciseVideoService;

    @ApiOperation(value = "7M Exercise Video 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSevenmExerciseVideoPageVO>> page(ProjSevenmExerciseVideoPageReq pageReq) {
        pageReq.setProjId(RequestContextUtils.getProjectId());
        PageRes<ProjSevenmExerciseVideoPageVO> pageRes = exerciseVideoService.selectVideoPage(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "7M Exercise Video 带查询条件导出")
    @GetMapping("/exportExcel")
    @SneakyThrows
    public void exportExcel(ProjSevenmExerciseVideoPageReq pageReq, HttpServletResponse response) {
        List<ProjSevenmExerciseVideoExportVO> list = exerciseVideoService.exportVideos(pageReq);
        // 确保有数据导出
        if (CollUtil.isEmpty(list)) {
            response.setContentType("application/json");
            response.getWriter().write("{\"message\": \"No data found for export.\"}");
            return;
        }

        // 设置响应头
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''7MExerciseVideo.xlsx";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        // 使用 EasyExcel 进行数据导出
        try (OutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, ProjSevenmExerciseVideoExportVO.class)
                    .inMemory(true)
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("Exercise Video")
                    .doWrite(list);
        }
    }

    @ApiOperation(value = "7M Exercise Video 增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSevenmExerciseVideoAddReq videoReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        videoReq.setProjId(projectId);
        exerciseVideoService.saveVideo(videoReq);
        return succ();
    }

    @ApiOperation(value = "7M Exercise Video 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmExerciseVideoUpdateReq videoReq) {
        exerciseVideoService.updateVideo(videoReq);
        return succ();
    }

    @ApiOperation(value = "7M Exercise Video 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmExerciseVideoDetailVO> detail(@PathVariable Integer id) {
        ProjSevenmExerciseVideoDetailVO detailVO = exerciseVideoService.getVideoDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "7M Exercise Video 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        exerciseVideoService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "7M Exercise Video 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        exerciseVideoService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "7M Exercise Video 批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        exerciseVideoService.deleteByIds(idList);
        return succ();
    }

    @SneakyThrows
    @ApiOperation(value = "7M Exercise Video 批量导入")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importByExcel(@RequestParam("file") MultipartFile excel) {
        return ResponseResult.succ(exerciseVideoService.importByExcel(excel.getInputStream(),RequestContextUtils.getProjectId()));
    }

}
