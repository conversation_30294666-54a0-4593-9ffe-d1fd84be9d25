/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog101.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import com.laien.web.biz.core.converter.GenericEnumListNameConverter;
import com.laien.common.core.converter.GenericEnumNameConverter;
import com.laien.web.biz.core.converter.IntegerStringTrimConverter;
import com.laien.web.biz.core.converter.StringStringTrimConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <p> workout image 导入 <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
public class ProjSevenmWorkoutImageImportReq {

    @ExcelProperty(value = "id",converter = IntegerStringTrimConverter.class)
    private Integer id;

    @Length(max = 100,message = "name length cannot be more than 100 characters")
    @NotBlank(message = "name cannot be blank")
    @ExcelProperty(value = "name", converter = StringStringTrimConverter.class)
    private String name;

    @NotNull(message = "target cannot be empty or wrong value")
    @ExcelProperty(value = "target", converter = GenericEnumListNameConverter.class)
    private List<SevenmTargetEnums> target;

    @NotNull(message = "gender cannot be empty or wrong value")
    @ApiModelProperty(value = "性别 1-female 2-male")
    @ExcelProperty(value = "gender",converter = GenericEnumNameConverter.class)
    private SevenmGenderEnums gender;

    @Length(max = 255,message = "cover_image length cannot be more than 255 characters")
    @Pattern(regexp = "^(?=.{1,255}$).*/[^/]+\\.(png|webp)(\\?.*)?$",message = "cover_image is invalid")
    @NotBlank(message = "cover_image cannot be blank")
    @ExcelProperty(value = "cover_image", converter = StringStringTrimConverter.class)
    private String coverImage;

    @Length(max = 255,message = "detail_image length cannot be more than 255 characters")
    @Pattern(regexp = "^(?=.{1,255}$).*/[^/]+\\.(png|webp)(\\?.*)?$",message = "detail_image is invalid")
    @NotBlank(message = "cover_image cannot be blank")
    @ExcelProperty(value = "detail_image", converter = StringStringTrimConverter.class)
    private String detailImage;
}
