package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * template 启用禁用 请求
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "video 新增", description = "video 新增")
public class ProjSevenmTemplateUpdateEnableReq {

    @ApiModelProperty(value = "模板id列表")
    private List<Integer> templateIds;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;
}
