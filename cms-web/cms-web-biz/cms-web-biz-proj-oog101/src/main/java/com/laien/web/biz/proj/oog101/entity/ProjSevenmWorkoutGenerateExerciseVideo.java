package com.laien.web.biz.proj.oog101.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_sevenm_workout_generate_exercise_video
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmWorkoutGenerateExerciseVideo对象", description = "proj_sevenm_workout_generate_exercise_video")
@TableName(autoResultMap = true)
public class ProjSevenmWorkoutGenerateExerciseVideo extends BaseModel {

    @ApiModelProperty(value = "proj_sevenm_template_id")
    private Integer projSevenmTemplateId;

    @ApiModelProperty(value = "proj_sevenm_template_task_id")
    private Integer projSevenmTemplateTaskId;

    @ApiModelProperty(value = "proj_workout_generate_id")
    private Integer projSevenmWorkoutGenerateId;

    @ApiModelProperty(value = "proj_sevenm_template_exercise_group_id")
    private Integer projSevenmTemplateExerciseGroupId;

    @ApiModelProperty(value = "proj_sevenm_exercise_video_id")
    private Integer projSevenmExerciseVideoId;

    @ApiModelProperty(value = "preview_duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "video_duration")
    private Integer videoDuration;
}
