package com.laien.web.biz.proj.oog101.controller;

import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmMusic;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmMusicDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmMusicPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmMusicService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Api(tags = "项目管理:Sevenm Music")
@RestController
@RequestMapping("/proj/sevenmMusic")
public class ProjSevenmMusicController extends ResponseController {

    @Resource
    IProjSevenmMusicService sevenmMusicService;

    @ApiOperation(value = "分页接口")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSevenmMusicPageVO>> list(ProjSevenmMusicPageReq pageReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        AssertUtil.notNull(projectId,"projId is null");
        pageReq.setProjId(projectId);
        PageRes<ProjSevenmMusicPageVO> pageRes = sevenmMusicService.pageQuery(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSevenmMusicAddReq addReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        AssertUtil.notNull(projectId,"projId is null");
        addReq.setProjId(projectId);
        sevenmMusicService.insert(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmMusicUpdateReq updateReq) {
        sevenmMusicService.update(updateReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmMusicDetailVO> detail(@PathVariable Integer id) {
        ProjSevenmMusicDetailVO detailVO = sevenmMusicService.getDetailById(id);
        return succ(detailVO);
    }

}
