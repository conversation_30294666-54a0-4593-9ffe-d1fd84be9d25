package com.laien.web.biz.proj.oog101.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog101.enums.SevenmMusicTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *ProjSevenmMusicAddReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjSevenmMusic对象", description="音乐表")
public class ProjSevenmMusicAddReq {

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "音频")
    private String audio;

    @ApiModelProperty(value = "音频总时长, 毫秒")
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型")
    private SevenmMusicTypeEnums musicType;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "讲述者，用于Meditation类型")
    private String instructor;

    @JsonIgnore
    private Integer projId;

}
