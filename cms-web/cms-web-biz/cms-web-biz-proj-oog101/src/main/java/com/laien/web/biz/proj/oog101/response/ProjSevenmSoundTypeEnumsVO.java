package com.laien.web.biz.proj.oog101.response;

import cn.hutool.core.collection.CollUtil;
import com.laien.common.oog101.enums.SevenmSoundSubTypeEnums;
import com.laien.common.oog101.enums.SevenmSoundTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel(value = "Sound VO", description = "Sound VO")
public class ProjSevenmSoundTypeEnumsVO {

    @ApiModelProperty(value = "soundType")
    private String soundType;

    @ApiModelProperty(value = "soundTypeName")
    private String soundTypeName;

    @ApiModelProperty(value = "subSoundTypes")
    List<ProjSevenmSoundTypeEnumsVO> subSoundTypes;

    public ProjSevenmSoundTypeEnumsVO(SevenmSoundTypeEnums enums) {
        this.soundType = enums.getName();
        this.soundTypeName = enums.name();
        if (CollUtil.isNotEmpty(enums.getSoundSubTypes())) {
            this.subSoundTypes = enums.getSoundSubTypes().stream().map(ProjSevenmSoundTypeEnumsVO::new).collect(Collectors.toList());
        }
    }

    public ProjSevenmSoundTypeEnumsVO(SevenmSoundSubTypeEnums enums) {
        this.soundType = enums.getName();
        this.soundTypeName = enums.name();
    }
}
