package com.laien.web.biz.proj.oog101.response;

import com.laien.common.oog101.enums.SevenmMusicTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
public class ProjSevenmPlaylistRelationVO {

    @ApiModelProperty(value = "播放列表id")
    private Integer projSevenmPlaylistId;

    @ApiModelProperty(value = "音乐id")
    private Integer projSevenmMusicId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "music 名字")
    private String musicName;

    @ApiModelProperty(value = "music 类型")
    private SevenmMusicTypeEnums musicType;

    @ApiModelProperty(value = "music展示名称")
    private String displayName;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

}
