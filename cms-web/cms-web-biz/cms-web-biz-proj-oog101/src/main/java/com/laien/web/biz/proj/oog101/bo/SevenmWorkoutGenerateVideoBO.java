package com.laien.web.biz.proj.oog101.bo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import com.laien.common.oog101.enums.*;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.entity.i18n.ProjSevenmExerciseVideoI18n;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@Data
@Slf4j
public class SevenmWorkoutGenerateVideoBO {

    private List<ProjSevenmExerciseVideo> centralAndLeftVideoList;
    private Map<Integer, ProjSevenmExerciseVideo> videoIdMap;
    private Map<SevenmTargetEnums, Set<ProjSevenmExerciseVideo>> videoTargetMap;

    private Map<SevenmSpecialLimitEnums, Set<ProjSevenmExerciseVideo>> videoLimitMap;
    private Map<String, Map<Integer, ProjSevenmExerciseVideoI18n>> videoI18nMap;

    BiPredicate<ProjSevenmExerciseVideo, SevenmTypeEnums> videoTypeMatch =
            (video, videoType) -> Objects.equals(video.getType(), videoType);
    BiPredicate<ProjSevenmExerciseVideo, List<SevenmPositionEnums>> positionMatch =
            (video, position) -> position.contains(video.getPosition());
    BiPredicate<ProjSevenmExerciseVideo, SevenmExerciseTypeEnums> exerciseTypeMatch =
            (video, exerciseType) -> Objects.equals(video.getExerciseType(), exerciseType);
    BiPredicate<ProjSevenmExerciseVideo, List<SevenmDifficultyEnums>> difficultMatch =
            (video, difficulty) -> difficulty.contains(video.getDifficulty());
    BiPredicate<ProjSevenmExerciseVideo, SevenmEquipmentEnums> equipmentMatch =
            (video, equipment) -> Objects.equals(video.getEquipment(), equipment);
    BiPredicate<ProjSevenmExerciseVideo, SevenmGenderEnums> genderMatch =
            (video, gender) -> Objects.equals(video.getGender(), gender);

    BiPredicate<ProjSevenmExerciseVideo, List<SevenmIntensityEnums>> intensityMatch =
            (video, intensity) -> {
        if (CollectionUtils.isEmpty(intensity)) {
            return true;
        }
        return intensity.contains(video.getIntensity());
    };

    Predicate<List<SevenmTargetEnums>> targetFullBodyMatch = (targets) -> {
        return targets.contains(SevenmTargetEnums.FULL_BODY) && targets.size() == 1;
    };

    BiPredicate<ProjSevenmExerciseVideo, List<SevenmTargetEnums>> targetMatch = (video, targets) -> {

        if (targetFullBodyMatch.test(targets) ) {
            return true;
        }
        return CollUtil.containsAny(targets,video.getTarget());
    };

    BiPredicate<ProjSevenmExerciseVideo, List<SevenmSpecialLimitEnums>> specialLimitMatch = (video, specialLimitEnums) -> {

        if (CollectionUtils.isEmpty(specialLimitEnums)) {
            return true;
        }

        Set<ProjSevenmExerciseVideo> videoSet = videoLimitMap.keySet().stream()
                .filter(limit -> specialLimitEnums.contains(limit))
                .map(videoLimitMap::get).flatMap(Collection::stream).collect(Collectors.toSet());

        return CollectionUtils.isEmpty(videoSet) || !videoSet.contains(video);
    };

    public LinkedList<ProjSevenmExerciseVideo> listByPriority(SevenmTypeEnums videoType,
                                                              SevenmExerciseTypeEnums exerciseType,
                                                              List<SevenmPositionEnums> position,
                                                              SevenmEquipmentEnums equipmentEnums,
                                                              List<SevenmSpecialLimitEnums> specialLimitList,
                                                              List<SevenmDifficultyEnums> difficulty,
                                                              List<SevenmIntensityEnums> intensity,
                                                              List<SevenmTargetEnums> target,
                                                              SevenmGenderEnums gender) {

        List<ProjSevenmExerciseVideo> priority4One = centralAndLeftVideoList.stream()
                .filter(video -> videoTypeMatch.test(video, videoType))
                .filter(video -> genderMatch.test(video, gender))
                .filter(video -> exerciseTypeMatch.test(video, exerciseType))
                .filter(video -> equipmentMatch.test(video, equipmentEnums))
                .collect(Collectors.toList());
        Collections.shuffle(priority4One);

        List<ProjSevenmExerciseVideo> priority4Two = priority4One.stream()
                .filter(video -> specialLimitMatch.test(video, specialLimitList))
                .collect(Collectors.toList());

        //不是fullBody的情况下，把none先排前面
        List<ProjSevenmExerciseVideo> priority4Three;
        if (!targetFullBodyMatch.test(target)) {
            priority4Three = priority4Two.stream()
                    .sorted(Comparator.comparing(v -> {
                        List<SevenmTargetEnums> videoTarget = v.getTarget();
                        return !(CollUtil.isNotEmpty(videoTarget) && videoTarget.size() == 1 && ObjUtil.equal(videoTarget.get(0), SevenmTargetEnums.NONE));
                    }))
                    .collect(Collectors.toList());
        } else {
            priority4Three = priority4Two;
        }

        List<ProjSevenmExerciseVideo> priority4Four = priority4Three.stream()
                .filter(video -> targetMatch.test(video, target))
                .collect(Collectors.toList());

        if (Objects.equals(videoType, SevenmTypeEnums.MAIN)) {

            List<ProjSevenmExerciseVideo> priority4Five = priority4Four.stream()
                    .filter(video -> difficultMatch.test(video, difficulty))
                    .collect(Collectors.toList());

            List<ProjSevenmExerciseVideo> priority4Six = priority4Five.stream()
                    .filter(video -> intensityMatch.test(video, intensity))
                    .collect(Collectors.toList());

            List<ProjSevenmExerciseVideo> priority4Seven = priority4Six.stream()
                    .filter(video -> positionMatch.test(video, position))
                    .collect(Collectors.toList());

            return combineListNew(priority4One, priority4Two, priority4Three, priority4Four,priority4Five,priority4Six,priority4Seven);
        }

        if (Objects.equals(videoType, SevenmTypeEnums.COOL_DOWN) || Objects.equals(videoType, SevenmTypeEnums.WARM_UP)) {

            List<ProjSevenmExerciseVideo> priority4Five = priority4Four.stream()
                    .filter(video -> positionMatch.test(video, position))
                    .collect(Collectors.toList());

            return combineListNew(priority4One, priority4Two, priority4Three, priority4Four,priority4Five);
        }
        return Lists.newLinkedList();
    }

    private LinkedList<ProjSevenmExerciseVideo> combineList(List<ProjSevenmExerciseVideo>... priorities) {
        int length = priorities.length;
        LinkedList<ProjSevenmExerciseVideo> exerciseVideoList = Lists.newLinkedList(priorities[length-1]);
        Consumer<List<ProjSevenmExerciseVideo>> combineConsume = videoList -> {
            videoList.stream().filter(video -> !exerciseVideoList.contains(video)).forEach(exerciseVideoList::addLast);
        };
        for (int i = length - 2; i >= 0; i--) {
            combineConsume.accept(priorities[i]);
        }
        return exerciseVideoList;
    }

    /**
     * 将多个视频列表合并为一个 LinkedList，同时保持优先级并移除重复项。
     * 优先级较高的列表（在 'priorities' 可变参数数组中靠后）中的视频会先出现。
     * 此版本使用 LinkedHashSet 来高效移除重复项，同时根据优先级保留插入顺序。
     *
     * @param priorities 可变参数数组，包含多个视频列表。约定是此数组中的最后一个列表
     * （例如 priorities[priorities.length-1]）具有最高优先级，而第一个列表
     * （priorities[0]）具有最低优先级。
     * @return 一个包含唯一视频的 LinkedList，按优先级排序。
     */
    private LinkedList<ProjSevenmExerciseVideo> combineListNew(List<ProjSevenmExerciseVideo>... priorities) {
        // LinkedHashSet 保留插入顺序并确保唯一性。
        LinkedHashSet<ProjSevenmExerciseVideo> uniqueVideos = new LinkedHashSet<>();

        // 从最高优先级的列表（可变参数数组中的最后一个）开始迭代
        // 到最低优先级的列表（可变参数数组中的第一个）。
        // 这确保了来自较高优先级列表的视频首先被添加到 LinkedHashSet 中，
        // 从而确立它们在顺序中的位置。
        for (int i = priorities.length - 1; i >= 0; i--) {
            List<ProjSevenmExerciseVideo> currentPriorityList = priorities[i];
            if (currentPriorityList != null && !currentPriorityList.isEmpty()) {
                // addAll 到 LinkedHashSet 只会添加尚不存在的元素，
                // 并保持首次出现的顺序。
                uniqueVideos.addAll(currentPriorityList);
            }
        }
        // 将 LinkedHashSet 转换为 LinkedList 以匹配原始返回类型。
        return new LinkedList<>(uniqueVideos);
    }

}
