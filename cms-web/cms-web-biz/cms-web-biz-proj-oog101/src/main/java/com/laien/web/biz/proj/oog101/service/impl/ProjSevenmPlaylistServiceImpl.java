package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.core.util.ShortLinkGenerateUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmPlaylist;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmPlaylistRelation;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmPlaylistMapper;
import com.laien.web.biz.proj.oog101.request.ProjSevenmPlaylistAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmPlaylistUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistPageVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistRelationVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmPlaylistRelationService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmPlaylistService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import jodd.mutable.MutableInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_DISABLE;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Service
@Slf4j
public class ProjSevenmPlaylistServiceImpl
        extends ServiceImpl<ProjSevenmPlaylistMapper, ProjSevenmPlaylist>
        implements IProjSevenmPlaylistService {

    @Resource
    private IProjSevenmPlaylistRelationService playlistRelationService;
    @Resource
    private ICoreTextTaskI18nService i18nService;
    @Resource
    private IProjInfoService projInfoService;
    @Resource
    private IProjLmsI18nService lmsI18nService;

    @Resource
    private ShortLinkGenerateUtil shortLinkGenerateUtil;

    private MutableInteger sortNoDefault = new MutableInteger(1000);

    @Override
    public List<ProjSevenmPlaylistPageVO> pageQuery(Integer projId) {

        LambdaQueryWrapper<ProjSevenmPlaylist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmPlaylist::getProjId, projId);
        queryWrapper.orderByDesc(ProjSevenmPlaylist::getSortNo);
        queryWrapper.orderByDesc(ProjSevenmPlaylist::getId);

        List<ProjSevenmPlaylist> playlists = list(queryWrapper);
        if (CollectionUtils.isEmpty(playlists)) {
            return Collections.emptyList();
        }

        List<Integer> playlistIds = playlists.stream().map(ProjSevenmPlaylist::getId).collect(Collectors.toList());
        List<ProjSevenmPlaylistRelation> relationList = playlistRelationService.listByPlaylistIds(playlistIds);
        Map<Integer, List<ProjSevenmPlaylistRelation>> playlistSumMap = relationList.stream().collect(Collectors.groupingBy(ProjSevenmPlaylistRelation::getProjSevenmPlaylistId));

        List<ProjSevenmPlaylistPageVO> pageVOList = playlists.stream().map(playlist -> {
            ProjSevenmPlaylistPageVO playlistPageVO = new ProjSevenmPlaylistPageVO();
            BeanUtils.copyProperties(playlist, playlistPageVO);

            if (playlistSumMap.containsKey(playlist.getId())) {
                playlistPageVO.setMusicNum(playlistSumMap.get(playlist.getId()).size());
            }
            return playlistPageVO;
        }).collect(Collectors.toList());
        return pageVOList;
    }

    @Override
    public ProjSevenmPlaylistDetailVO getDetailById(Integer id) {

        ProjSevenmPlaylist playlist = getById(id);
        if (Objects.isNull(playlist)) {
            return null;
        }

        ProjSevenmPlaylistDetailVO detailVO = new ProjSevenmPlaylistDetailVO();
        BeanUtils.copyProperties(playlist, detailVO);

        List<ProjSevenmPlaylistRelationVO> playlistRelationVOList = playlistRelationService.listByPlaylistId(playlist.getId());
        detailVO.setPlaylistRelationVOList(playlistRelationVOList);
        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void insert(ProjSevenmPlaylistAddReq playlistAddReq) {
        bizCheck(playlistAddReq, null);
        ProjSevenmPlaylist playlist = new ProjSevenmPlaylist();
        BeanUtils.copyProperties(playlistAddReq, playlist);

        playlist.setStatus(STATUS_DISABLE);
        playlist.setSortNo(sortNoDefault.get());
        playlist.setProjId(RequestContextUtils.getProjectId());

        save(playlist);
        handleRelation(playlistAddReq.getPlaylistRelationVOList(), playlist);
        lmsI18nService.handleI18n(ListUtil.of(playlist), projInfoService.getById(playlistAddReq.getProjId()));
    }

    private void handleRelation(List<ProjSevenmPlaylistRelationVO> relationVOList, ProjSevenmPlaylist playlist) {

        // 删除已有的关系
        playlistRelationService.deleteByPlaylistId(Lists.newArrayList(playlist.getId()));

        if (CollectionUtils.isEmpty(relationVOList)) {
            return;
        }

        // 新增关系
        List<ProjSevenmPlaylistRelation> relationList = relationVOList.stream().map(relation -> {
            ProjSevenmPlaylistRelation playlistRelation = new ProjSevenmPlaylistRelation();
            BeanUtils.copyProperties(relation, playlistRelation);
            playlistRelation.setProjId(playlist.getProjId());
            playlistRelation.setProjSevenmPlaylistId(playlist.getId());
            return playlistRelation;
        }).collect(Collectors.toList());
        playlistRelationService.saveBatch(relationList);
    }

    private void bizCheck(ProjSevenmPlaylistAddReq playlistAddReq, Integer id) {



        LambdaQueryWrapper<ProjSevenmPlaylist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmPlaylist::getPlaylistName, playlistAddReq.getPlaylistName());
        queryWrapper.eq(ProjSevenmPlaylist::getPlaylistType, playlistAddReq.getPlaylistType());

        queryWrapper.eq(ProjSevenmPlaylist::getProjId, RequestContextUtils.getProjectId());
        queryWrapper.ne(Objects.nonNull(id), ProjSevenmPlaylist::getId, id);
        queryWrapper.last("limit 1");

        ProjSevenmPlaylist playlist = getOne(queryWrapper);
        if (Objects.nonNull(playlist)) {
            throw new BizException("Playlist name is existed.");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void sort(IdListReq idListReq) {

        List<Integer> idList = idListReq.getIdList();
        if (!CollectionUtils.isEmpty(idList)) {
            int sortNoIndex = sortNoDefault.get();
            for (Integer id : idList) {
                LambdaUpdateWrapper<ProjSevenmPlaylist> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProjSevenmPlaylist::getId, id);
                wrapper.eq(ProjSevenmPlaylist::getProjId, RequestContextUtils.getProjectId());
                wrapper.set(ProjSevenmPlaylist::getSortNo, sortNoIndex);
                wrapper.set(ProjSevenmPlaylist::getUpdateTime, LocalDateTime.now());
                wrapper.set(ProjSevenmPlaylist::getUpdateUser, RequestContextUtils.getLoginUserName());
                update(new ProjSevenmPlaylist(), wrapper);
                sortNoIndex--;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(ProjSevenmPlaylistUpdateReq playlistUpdateReq) {

        bizCheck(playlistUpdateReq, playlistUpdateReq.getId());
        ProjSevenmPlaylist playlist = getById(playlistUpdateReq.getId());

        BeanUtils.copyProperties(playlistUpdateReq, playlist);
        updateById(playlist);
        handleRelation(playlistUpdateReq.getPlaylistRelationVOList(), playlist);
        lmsI18nService.handleI18n(ListUtil.of(playlist), projInfoService.getById(playlistUpdateReq.getProjId()));
    }

    @Override
    public void updateEnableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjSevenmPlaylist> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmPlaylist::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjSevenmPlaylist::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjSevenmPlaylist::getId, idList);
        this.update(new ProjSevenmPlaylist(), wrapper);
    }

    @Override
    public void updateDisableByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjSevenmPlaylist> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmPlaylist::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjSevenmPlaylist::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjSevenmPlaylist::getId, idList);
        this.update(new ProjSevenmPlaylist(), wrapper);
    }

    @Override
    public void deleteByIds(Collection<Integer> idList) {

        LambdaUpdateWrapper<ProjSevenmPlaylist> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmPlaylist::getDelFlag, GlobalConstant.YES);
        wrapper.eq(ProjSevenmPlaylist::getStatus, GlobalConstant.STATUS_DRAFT);
        wrapper.in(ProjSevenmPlaylist::getId, idList);
        this.update(new ProjSevenmPlaylist(), wrapper);
    }
}
