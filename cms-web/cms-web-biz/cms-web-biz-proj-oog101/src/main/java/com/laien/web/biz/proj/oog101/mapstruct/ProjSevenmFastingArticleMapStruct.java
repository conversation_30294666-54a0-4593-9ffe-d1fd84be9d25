package com.laien.web.biz.proj.oog101.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmFastingArticle;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleAddReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 * <p>
 *
 * <AUTHOR>
 * @since sylar
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSevenmFastingArticleMapStruct {

    ProjSevenmFastingArticle toEntity(ProjSevenmFastingArticleAddReq articleAddReq);

    List<ProjSevenmFastingArticleListVO> toVOList(List<ProjSevenmFastingArticle> articleList);

    ProjSevenmFastingArticleDetailVO toDetailVO(ProjSevenmFastingArticle article);
}
