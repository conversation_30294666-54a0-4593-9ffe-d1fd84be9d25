package com.laien.web.biz.proj.oog101.controller;

import cn.hutool.core.collection.ListUtil;
import com.laien.web.biz.proj.oog101.bo.SevenmWorkoutGenerateVideoBO;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplate;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplateTask;
import com.laien.web.biz.proj.oog101.request.*;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplateDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplatePageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmTemplateService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmWorkoutGenerateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.LinkedList;

/**
 * proj_sevenm_template controller
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Api(tags = {"项目管理:Sevenm ProjSevenmTemplate"})
@RestController
@RequestMapping("/proj/sevenmTemplate")
public class ProjSevenmTemplateController {

    @Resource
    private IProjSevenmTemplateService projSevenmTemplateService;
    @Resource
    private IProjSevenmWorkoutGenerateService workoutGenerateService;
    private static SevenmWorkoutGenerateVideoBO bo = null;

    @ApiOperation(value = "测试生成筛选逻辑")
    @GetMapping("/testgenerateWorkout")
    public ResponseResult<LinkedList<ProjSevenmExerciseVideo>> testgenerateWorkout(SevenmVideoSelectReq req) throws Exception {

        if (bo == null) {
            bo = workoutGenerateService.createWorkoutGenerateVideoBO(ListUtil.of("en"));
        }
        return ResponseResult.succ( bo.listByPriority(req.getVideoType(),req.getExerciseType(),
                req.getPosition(), req.getEquipmentEnums(),
                req.getSpecialLimitList(), req.getDifficulty(),
                req.getIntensity(), req.getTarget(),
                req.getGender()));
    }


    @ApiOperation(value = "template生成workout")
    @PostMapping("/generateWorkout")
    public ResponseResult<Void> generateWorkout( @RequestBody ProjSevenmTemplateGenerateWorkoutReq req) throws Exception {
        req.setProjId(RequestContextUtils.getProjectId());
        projSevenmTemplateService.generateWorkout(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "生成template")
    @PostMapping("/generate")
    public ResponseResult<Void> generate( @RequestBody ProjSevenmTemplateGenerateReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        projSevenmTemplateService.generate(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSevenmTemplatePageVO>> page(ProjSevenmTemplatePageReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        return ResponseResult.succ(projSevenmTemplateService.pageQuery(req));
    }

    @ApiOperation(value = "template详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmTemplateDetailVO> page(@ApiParam("id") @PathVariable Integer id) {

        return ResponseResult.succ(projSevenmTemplateService.detail(id));
    }

    @ApiOperation(value = "修改template")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmTemplateUpdateReq req) {

        projSevenmTemplateService.update(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq req) {

        projSevenmTemplateService.updateEnable(req.getIdList(), true);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq req) {

        projSevenmTemplateService.updateEnable(req.getIdList(), false);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "template删除")
    @PostMapping("/del")
    public ResponseResult<Void> delete(@RequestBody IdListReq req) {

        projSevenmTemplateService.delete(req.getIdList());
        return ResponseResult.succ();
    }


}
