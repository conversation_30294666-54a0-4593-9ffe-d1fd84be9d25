package com.laien.web.biz.proj.oog101.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutGenerate;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGenerateDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGeneratePageVO;
import org.mapstruct.Mapper;

/**
 * <p>
 *  Workout generate MapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSevenmWorkoutGenerateMapStruct {

    ProjSevenmWorkoutGeneratePageVO toPageVO(ProjSevenmWorkoutGenerate workout);

    ProjSevenmWorkoutGenerateDetailVO toDetailVO(ProjSevenmWorkoutGenerate workout);
}
