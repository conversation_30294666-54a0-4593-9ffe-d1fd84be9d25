/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog101.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * workout image 新增
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "workout image 新增", description = "workout image 新增")
public class ProjSevenmWorkoutImageAddReq {

    @ApiModelProperty("图片名称")
    private String name;

    @ApiModelProperty(value = "Target Areas")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty("封面图片地址")
    private String coverImage;

    @ApiModelProperty("详情图片地址")
    private String detailImage;

    @JsonIgnore
    private Integer projId;

}
