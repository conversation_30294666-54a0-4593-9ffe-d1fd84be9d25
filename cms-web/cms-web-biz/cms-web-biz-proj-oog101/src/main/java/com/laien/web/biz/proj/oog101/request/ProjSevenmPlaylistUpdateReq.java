package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Proj7MPlaylist 新增
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjSevenmPlaylistUpdateReq对象", description="项目播放列表")
public class ProjSevenmPlaylistUpdateReq extends ProjSevenmPlaylistAddReq {

    private Integer id;

}
