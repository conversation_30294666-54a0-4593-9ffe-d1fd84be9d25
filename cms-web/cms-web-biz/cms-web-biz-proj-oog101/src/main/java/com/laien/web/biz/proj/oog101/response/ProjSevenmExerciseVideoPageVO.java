package com.laien.web.biz.proj.oog101.response;

import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ExerciseVideo 分页
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExerciseVideo 分页", description = "ExerciseVideo 分页")
public class ProjSevenmExerciseVideoPageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "English Voice Name")
    private String coreVoiceConfigI18nName;

    @ApiModelProperty(value = "图片地址，支持webp,png")
    private String imageUrl;

    @ApiModelProperty(value = "类型code")
    private SevenmTypeEnums type;

    @ApiModelProperty(value = "exerciseType")
    private SevenmExerciseTypeEnums exerciseType;

    @ApiModelProperty(value = "intensity")
    private SevenmIntensityEnums intensity;

    @ApiModelProperty(value = "direction")
    private SevenmVideoDirectionEnums videoDirection;

    @ApiModelProperty(value = "Left-Right 关联 Right 类型的动作 ID")
    private Integer leftRightVideoId;

    @ApiModelProperty(value = "难度")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "position STANDING-Standing SEATED-Seated LYING-Lying PRONE-Prone KNEELING-Kneeling SITTING-Sitting")
    private SevenmPositionEnums position;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制列表 多选 NONE-None WRIST-Wrist FOOT-Foot BACK-Back SHOULDER-Shooter ABS-Abs")
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "目标")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "器械")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "MET (1-12)")
    private Integer met;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否参与自动生成 1-是 0-否")
    private Integer usedForAuto;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;
}
