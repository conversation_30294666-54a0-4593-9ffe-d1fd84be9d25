package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 7M Manual Workout M3U8 Generation Request
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@ApiModel(value = "Manual Workout M3U8 Generation Request", description = "Request parameters for generating M3U8 files for manual workouts")
public class ProjSevenmManualWorkoutGenerateM3u8Req {

    /**
     * 训练ID列表
     */
    @ApiModelProperty(value = "List of Workout IDs to generate M3U8 files for")
    private List<Integer> workoutIds;

    /**
     * 语言列表
     */
    @ApiModelProperty(value = "List of languages to generate audio for")
    private List<String> languages;

    /**
     * 是否生成视频
     */
    @ApiModelProperty(value = "Whether to generate video files")
    private Boolean videoFlag;

    /**
     * 是否生成音频
     */
    @ApiModelProperty(value = "Whether to generate audio files")
    private Boolean audioFlag;

    @ApiModelProperty(value = "项目ID",hidden = true)
    private Integer projId;

    @ApiModelProperty(value = "分页参数")
    private ProjSevenmWorkoutGeneratePageReq pageReq;

    @ApiModelProperty(value = "分页参数",hidden = true)
    private String updateUser;
}
