package com.laien.web.biz.proj.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog101.enums.*;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * proj_sevenm_template
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Accessors(chain = true)
public class ProjSevenmTemplate extends BaseModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_TEMPLATE;

    @ApiModelProperty("template版本")
    private Integer dataVersion;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("类型")
    private SevenmTemplateTypeEnums templateType;

    @ApiModelProperty("生成多少天的天数")
    private Integer days;

    @ApiModelProperty("难度等级")
    private SevenmDifficultyEnums level;

    @ApiModelProperty("特殊限制")
    @TableField(typeHandler = SevenmSpecialLimitEnums.TypeHandler.class)
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("最近一个任务的id")
    @TableField(exist = false)
    private Integer taskId;

    @ApiModelProperty("最近一个任务的状态")
    @TableField(exist = false)
    private SevenmTemplateTaskStatusEnum taskStatus;

    @ApiModelProperty("项目id")
    private Integer projId;

}
