package com.laien.web.biz.proj.oog101.bo;

import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutGenerate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.Map;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SevenmWorkoutGenerateBO {

    private LinkedList<BaseGenerateVideoBO> workoutAndRelationList;

    private ProjSevenmWorkoutGenerate workoutGenerate;

    private Map<String, String> i18nAudioJsonMap;

    private Object UniqueKey;
}
