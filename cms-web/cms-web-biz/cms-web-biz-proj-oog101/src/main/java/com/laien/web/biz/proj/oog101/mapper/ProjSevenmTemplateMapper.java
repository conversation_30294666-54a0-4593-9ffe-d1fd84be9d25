package com.laien.web.biz.proj.oog101.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplate;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplatePageReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * ProjSevenmTemplateMapper 接口
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
public interface ProjSevenmTemplateMapper extends BaseMapper<ProjSevenmTemplate> {

    int insertBatchSomeColumn(@Param("list") List<ProjSevenmTemplate> batchList);

    IPage<ProjSevenmTemplate> pageWithTask(@Param("page") IPage<ProjSevenmTemplate> page,@Param("param") ProjSevenmTemplatePageReq param);
}




