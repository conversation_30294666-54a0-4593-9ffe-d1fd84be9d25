package com.laien.web.biz.proj.oog101.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog101.enums.SevenmMusicTypeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *ProjSevenmMusicPageReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjSevenmMusic对象", description="音乐表")
public class ProjSevenmMusicPageReq extends PageReq {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Music Id")
    private Integer id;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "音乐类型")
    private SevenmMusicTypeEnums musicType;

    @JsonIgnore
    private Integer projId;

}
