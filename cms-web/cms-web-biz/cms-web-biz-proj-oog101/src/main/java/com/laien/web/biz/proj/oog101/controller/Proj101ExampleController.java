package com.laien.web.biz.proj.oog101.controller;


import com.laien.common.oog101.enums.ExampleEnums;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * Proj101ExampleController
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/06
 */

@Api(tags = "项目管理:example")
@RestController
@RequestMapping("/proj/example")
@RequiredArgsConstructor
public class Proj101ExampleController extends ResponseController {

    @ApiOperation(value = "example")
    @GetMapping( "/example")
    public ResponseResult<Object> list() {
        return succ(ExampleEnums.EXAMPLE_ENUMS.getName()+"-oog101web");
    }

}
