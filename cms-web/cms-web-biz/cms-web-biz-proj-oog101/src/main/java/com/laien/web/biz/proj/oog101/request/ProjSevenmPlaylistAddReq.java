package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.SevenmPlaylistTypeEnums;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistRelationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjSevenmPlaylist对象", description="项目播放列表")
public class ProjSevenmPlaylistAddReq {

    @ApiModelProperty(value = "playlist type", required = true)
    private SevenmPlaylistTypeEnums playlistType;

    @ApiModelProperty(value = "列表名称", required = true)
    private String playlistName;

    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "手机详情图")
    private String phoneDetailImgUrl;

    @ApiModelProperty(value = "平板详情图")
    private String tabletDetailImgUrl;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "music relation")
    private List<ProjSevenmPlaylistRelationVO> playlistRelationVOList;

}
