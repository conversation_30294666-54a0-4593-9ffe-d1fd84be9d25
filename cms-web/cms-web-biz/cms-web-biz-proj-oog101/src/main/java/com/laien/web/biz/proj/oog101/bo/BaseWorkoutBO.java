package com.laien.web.biz.proj.oog101.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@Data
public class BaseWorkoutBO {

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "video的 2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json, 英语")
    private String audioJsonUrl;

    @ApiModelProperty(value = "多语言音频json，包含英语，<language,audioUrl>")
    private Map<String, String> audioI18nUrl;

}
