package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * Proj7MExerciseVideo 编辑
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Proj7MExerciseVideo 编辑", description = "Proj7MExerciseVideo 编辑")
public class ProjSevenmExerciseVideoUpdateReq extends ProjSevenmExerciseVideoAddReq {

    @ApiModelProperty(value = "id")
    private Integer id;
}
