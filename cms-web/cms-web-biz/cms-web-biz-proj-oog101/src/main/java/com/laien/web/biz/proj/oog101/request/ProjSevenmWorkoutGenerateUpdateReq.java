package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *  ProjSevenmWorkoutGenerateUpdateReq
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjSevenmWorkoutGenerateUpdateReq", description="ProjSevenmWorkoutGenerateUpdateReq")
public class ProjSevenmWorkoutGenerateUpdateReq extends  ProjSevenmWorkoutGenerateAddReq{

    @ApiModelProperty(value = "id")
    private Integer id;
}
