package com.laien.web.biz.proj.oog101.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class Oog101BizConfig {


    @ConfigurationProperties(value = "cms.biz.oog101.sevenm")
    @Bean(value = "sevenm")
    public SevenmSoundConfigWrapper get106FitnessSoundConfig() {
        return new SevenmSoundConfigWrapper();
    }

    @ConfigurationProperties(value = "cms.biz.oog101.tabata")
    @Bean(value = "tabata")
    public SevenmSoundConfigWrapper getTabataSoundConfig() {
        return new SevenmSoundConfigWrapper();
    }

}
