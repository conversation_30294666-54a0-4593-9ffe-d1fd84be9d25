package com.laien.web.biz.proj.oog101.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmPlaylistRelation;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmPlaylistRelationMapper;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistRelationVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmPlaylistRelationService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Service
public class ProjSevenmPlaylistRelationServiceImpl extends
        ServiceImpl<ProjSevenmPlaylistRelationMapper, ProjSevenmPlaylistRelation> implements IProjSevenmPlaylistRelationService {

    @Resource
    ProjSevenmPlaylistRelationMapper relationMapper;

    @Override
    public void deleteByPlaylistId(List<Integer> playlistIds) {

        LambdaUpdateWrapper<ProjSevenmPlaylistRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjSevenmPlaylistRelation::getProjSevenmPlaylistId, playlistIds);
        updateWrapper.set(ProjSevenmPlaylistRelation::getDelFlag, GlobalConstant.YES);
        updateWrapper.set(ProjSevenmPlaylistRelation::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ProjSevenmPlaylistRelation::getUpdateUser, RequestContextUtils.getLoginUserName());
        update(new ProjSevenmPlaylistRelation(), updateWrapper);
    }

    @Override
    public List<ProjSevenmPlaylistRelationVO> listByPlaylistId(Integer playlistId) {

        List<ProjSevenmPlaylistRelationVO> relationVOList = relationMapper.listByPlaylistId(playlistId);
        return relationVOList;
    }

    @Override
    public List<ProjSevenmPlaylistRelation> listByPlaylistIds(List<Integer> playlistIds) {

        LambdaQueryWrapper<ProjSevenmPlaylistRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjSevenmPlaylistRelation::getProjSevenmPlaylistId, playlistIds);
        List<ProjSevenmPlaylistRelation> relationList = list(queryWrapper);

        if (CollectionUtils.isEmpty(relationList)) {
            return Collections.emptyList();
        }
        return relationList;
    }
}
