package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.oog101.enums.*;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.oog101.bo.*;
import com.laien.web.biz.proj.oog101.entity.*;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmTemplateExerciseGroupMapper;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmWorkoutGenerateMapper;
import com.laien.web.biz.proj.oog101.mapstruct.ProjSevenmExerciseVideoMapStruct;
import com.laien.web.biz.proj.oog101.mapstruct.ProjSevenmWorkoutGenerateMapStruct;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutGeneratePageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutGenerateUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGenerateDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGeneratePageVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGenerateVideoVO;
import com.laien.web.biz.proj.oog101.service.*;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_sevenm_workout_generate 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjSevenmWorkoutGenerateServiceImpl extends ServiceImpl<ProjSevenmWorkoutGenerateMapper, ProjSevenmWorkoutGenerate>
        implements IProjSevenmWorkoutGenerateService {

    private final ProjSevenmWorkoutGenerateMapStruct mapStruct;
    private final ProjSevenmExerciseVideoMapStruct videoMapStruct;
    private final IProjSevenmWorkoutGenerateExerciseVideoService generateVideoService;
    private final IProjSevenmManualWorkoutService manualWorkoutService;
    private final IProjSevenmTemplateService templateService;
    private final IProjSevenmTemplateTaskService taskService;
    private final IProjSevenmExerciseVideoService videoService;
    private final IProjSevenmTemplateExerciseGroupService exerciseGroupService;
    private final TransactionTemplate transactionTemplate;
    private final IProjSevenmWorkoutGenerateI18nService workoutGenerateI18nService;
    private final IProjInfoService projInfoService;
    private final ProjSevenmTemplateExerciseGroupMapper groupMapper;

    private static final ThreadPoolTaskExecutor poolTaskExecutor = createTaskExecutor4Generate();
    private final AtomicBoolean generateM3u8ReqIsRunning = new AtomicBoolean(false);
    private final LinkedBlockingQueue<ProjSevenmManualWorkoutGenerateM3u8Req> generateM3u8Queue = new LinkedBlockingQueue<>(GlobalConstant.THOUSAND);


    private static ThreadPoolTaskExecutor createTaskExecutor4Generate() {

        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(3);
        threadPoolTaskExecutor.setMaxPoolSize(3);
        threadPoolTaskExecutor.setQueueCapacity(2000);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        // 允许回收核心线程
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setThreadNamePrefix("sevenm-workout-generate-thread-");
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    private IPage<Integer> pageWorkoutIds(ProjSevenmWorkoutGeneratePageReq req, Page<ProjSevenmWorkoutGenerate> page) {
        Set<Integer> templateIdSet = getPageReqResult(req);
        return  baseMapper.page(page, req, templateIdSet,
                BitmaskEnumUtil.sumBitmaskEnumList(req.getSpecialLimit()),
                BitmaskEnumUtil.sumBitmaskEnumList(req.getTarget()));
    }

    private Set<Integer> getPageReqResult(ProjSevenmWorkoutGeneratePageReq req) {
        Integer templateStatus = req.getTemplateStatus();
        Integer projSevenmTemplateId = req.getProjSevenmTemplateId();
        SevenmDifficultyEnums templateLevel = req.getTemplateLevel();
        Set<Integer> templateIdSet;

        if (projSevenmTemplateId == null && (templateStatus != null || templateLevel != null)) {
            LambdaQueryWrapper<ProjSevenmTemplate> templateWrapper = new LambdaQueryWrapper<>();
            templateWrapper.eq(templateStatus != null, ProjSevenmTemplate::getStatus, templateStatus)
                    .eq(templateLevel != null, ProjSevenmTemplate::getLevel, templateLevel)
                    .eq(ProjSevenmTemplate::getDelFlag, 0);
            List<ProjSevenmTemplate> templates = templateService.list(templateWrapper);
            templateIdSet = templates.stream().map(BaseModel::getId).collect(Collectors.toSet());
        } else {
            templateIdSet = null;
        }
        return templateIdSet;
    }

    private List<Integer> listWorkoutIds(ProjSevenmWorkoutGeneratePageReq req) {
        Set<Integer> templateIdSet = getPageReqResult(req);
        return baseMapper.list(req, templateIdSet,
                BitmaskEnumUtil.sumBitmaskEnumList(req.getSpecialLimit()),
                BitmaskEnumUtil.sumBitmaskEnumList(req.getTarget()));
    }


    @Override
    public PageRes<ProjSevenmWorkoutGeneratePageVO> page(ProjSevenmWorkoutGeneratePageReq req) {
        IPage<Integer> page = this.pageWorkoutIds(req, new Page<>(req.getPageNum(), req.getPageSize()));
        // 为空直接返回
        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), Collections.emptyList());
        }
        //查询数据
        LambdaQueryWrapper<ProjSevenmWorkoutGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjSevenmWorkoutGenerate::getId, page.getRecords()).orderByDesc(ProjSevenmWorkoutGenerate::getId);
        List<ProjSevenmWorkoutGenerate> workoutList = baseMapper.selectList(queryWrapper);
        //补充查询任务信息
        //1.获取taskIdSet
        Set<Integer> taskIds = workoutList.stream().map(ProjSevenmWorkoutGenerate::getProjSevenmTemplateTaskId).collect(Collectors.toSet());
        //2.查询任务信息
        Map<Integer,ProjSevenmTemplateTask> taskMap = taskService.listByIds(taskIds).stream().collect(Collectors.toMap(ProjSevenmTemplateTask::getId, v -> v));
        //3.补充任务信息
        List<ProjSevenmWorkoutGeneratePageVO> vos = workoutList.stream().map(w -> {
            ProjSevenmWorkoutGeneratePageVO vo = mapStruct.toPageVO(w);
            ProjSevenmTemplateTask task = taskMap.get(vo.getProjSevenmTemplateTaskId());
            if (task != null) {
                vo.setGenerateNum(task.getWorkoutNum());
                vo.setCreateUser(task.getCreateUser());
                vo.setCleanUp(task.getCleanUp());
                vo.setUpdateStatus(w.getFileStatus());
            }
            return vo;
        }).collect(Collectors.toList());
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), vos);
    }

    private LambdaQueryWrapper<ProjSevenmWorkoutGenerate> getProjSevenmWorkoutGenerateLambdaQueryWrapper(ProjSevenmWorkoutGeneratePageReq req) {
        LambdaQueryWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        buildCommonConditions(req).accept(wrapper);
        return wrapper.orderByDesc(ProjSevenmWorkoutGenerate::getId);
    }

    private LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> getProjSevenmWorkoutGenerateLambdaUpdateWrapper(ProjSevenmWorkoutGeneratePageReq req, List<Integer> workoutIds) {
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaUpdateWrapper<>();
        if (CollUtil.isNotEmpty(workoutIds)) {
            wrapper.in(ProjSevenmWorkoutGenerate::getId, workoutIds);
        } else {
            buildCommonConditions(req).accept(wrapper);
        }
        return wrapper;
    }

    // ✅ 只写一份条件拼接逻辑，返回 Consumer
    private Consumer<AbstractLambdaWrapper<ProjSevenmWorkoutGenerate, ?>> buildCommonConditions(ProjSevenmWorkoutGeneratePageReq req) {
        Integer projSevenmTemplateId = req.getProjSevenmTemplateId();
        Set<Integer> templateIdSet = this.getPageReqResult(req);
        return wrapper -> {
            wrapper.eq(projSevenmTemplateId != null, ProjSevenmWorkoutGenerate::getProjSevenmTemplateId, projSevenmTemplateId)
                    .eq(req.getProjSevenmTemplateTaskId() != null, ProjSevenmWorkoutGenerate::getProjSevenmTemplateTaskId, req.getProjSevenmTemplateTaskId())
                    .eq(req.getId() != null, ProjSevenmWorkoutGenerate::getId, req.getId())
                    .eq(req.getStatus() != null, ProjSevenmWorkoutGenerate::getStatus, req.getStatus())
                    .eq(req.getFileStatus() != null, ProjSevenmWorkoutGenerate::getFileStatus, req.getFileStatus())
                    .in(CollUtil.isNotEmpty(templateIdSet), ProjSevenmWorkoutGenerate::getProjSevenmTemplateId, templateIdSet)
                    .eq(req.getDifficulty() != null, ProjSevenmWorkoutGenerate::getDifficulty, req.getDifficulty())
                    .eq(req.getEquipment() != null, ProjSevenmWorkoutGenerate::getEquipment, req.getEquipment())
                    .eq(req.getProjId() != null, ProjSevenmWorkoutGenerate::getProjId, req.getProjId())
                    .eq(req.getGender() != null, ProjSevenmWorkoutGenerate::getGender, req.getGender())
                    .eq(ProjSevenmWorkoutGenerate::getDelFlag, 0);
            BitmaskEnumUtil.addBitmaskCondition(wrapper, ProjSevenmWorkoutGenerate::getSpecialLimit, req.getSpecialLimit(), true);
            BitmaskEnumUtil.addBitmaskCondition(wrapper, ProjSevenmWorkoutGenerate::getTarget, req.getTarget(), false);
        };
    }


    @Override
    public ProjSevenmWorkoutGenerateDetailVO findDetailById(Integer id) {
        ProjSevenmWorkoutGenerate workout = baseMapper.selectById(id);
        BizExceptionUtil.throwIf(workout == null, "Workout not found");
        ProjSevenmWorkoutGenerateDetailVO vo = mapStruct.toDetailVO(workout);
        List<ProjSevenmWorkoutGenerateVideoVO> videoVOs = new ArrayList<>();
        //查询关联的视频
        List<ProjSevenmWorkoutGenerateExerciseVideo> relations = generateVideoService.listByWorkoutGenerateId(id);
        if (CollUtil.isNotEmpty(relations)) {
            Map<Integer, Integer> relationMap = relations.stream()
                .collect(Collectors.toMap(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmExerciseVideoId,
                                          ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmTemplateExerciseGroupId));
            List<Integer> relationVideoIds = relations.stream().map(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmExerciseVideoId).collect(Collectors.toList());
            Set<Integer> videoIds = relationMap.keySet();
            List<ProjSevenmExerciseVideoPageVO> videos = videoService.listVOByIds(new ArrayList<>(videoIds));

            Set<Integer> groupIds = new HashSet<>(relationMap.values());
            Collection<ProjSevenmTemplateExerciseGroup> groups = exerciseGroupService.listByIds(groupIds);
            Map<Integer, Integer> groupIdMap = groups.stream()
                .collect(Collectors.toMap(ProjSevenmTemplateExerciseGroup::getId,
                                          ProjSevenmTemplateExerciseGroup::getRounds,
                                          (v1, v2) -> v1));

            videoVOs = videos.stream().map(v -> {
                ProjSevenmWorkoutGenerateVideoVO videoVO = videoMapStruct.toGenerateVideoVO(v);
                Integer groupId = relationMap.get(v.getId());
                Integer rounds = groupId != null ? groupIdMap.getOrDefault(groupId, 1) : 1;
                videoVO.setExerciseCircuit(rounds);
                return videoVO;
            }).sorted(Comparator.comparingInt(v-> relationVideoIds.indexOf(v.getId())))
            .collect(Collectors.toList());
        }
        vo.setVideoList(videoVOs);
        //查询temp详情
        vo.setTemplateDetail(templateService.detail(workout.getProjSevenmTemplateId()));
        return vo;
    }

    @Transactional
    @Override
    public void update(ProjSevenmWorkoutGenerateUpdateReq workoutUpdateReq, Integer projId) {
        Integer id = workoutUpdateReq.getId();
        ProjSevenmWorkoutGenerate workoutGenerate = baseMapper.selectById(id);
        if (null == workoutGenerate) {
            throw new BizException("workout not found");
        }
        List<Integer> videoIdList = workoutUpdateReq.getVideoIdList();
        List<ProjSevenmExerciseVideo> videos = videoService.listByIds(videoIdList)
                .stream().sorted(Comparator.comparingInt(v -> videoIdList.indexOf(v.getId())))
                .collect(Collectors.toList());
        checkLeftRight(videos);
        Set<String> languages = CollUtil.newLinkedHashSet(GlobalConstant.DEFAULT_LANGUAGE);
        ProjInfo projInfo = projInfoService.getById(projId);
        ProjSevenmManualWorkoutGenerateM3u8Req generateM3u8Req = new ProjSevenmManualWorkoutGenerateM3u8Req();
        generateM3u8Req.setWorkoutIds(ListUtil.of(workoutUpdateReq.getId()));
        generateM3u8Req.setLanguages(this.getLanguageList(projInfo));
        generateM3u8Req.setProjId(projId);
        generateM3u8Req.setVideoFlag(true);
        generateM3u8Req.setAudioFlag(true);
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        generateM3u8Req.setUpdateUser(userName);
        M3u8GenerateContext context = assembleM3u8GenerateBo(generateM3u8Req,workoutGenerate,videos);
        List<GenerateSevenmWorkoutFileContextBO> fileContextBOs = context.getFileContextBOs();
        for (GenerateSevenmWorkoutFileContextBO fileContextBO : fileContextBOs) {
            manualWorkoutService.generateWorkoutFile(fileContextBO);
            processGenerateM3u8Update(projInfo,languages,context,fileContextBO,this,true);
        }
    }

    private void checkLeftRight(Collection<ProjSevenmExerciseVideo> videoList) {
        // key：right video id,value:相同id的个数
        ProjSevenmExerciseVideo preLeftVideo = null;
        for (ProjSevenmExerciseVideo video : videoList) {
            if (null != preLeftVideo && !Objects.equals(preLeftVideo.getLeftRightVideoId(), video.getId())) {
                throw new BizException(String.format("Left and right must be adjacent, left video name: %s", preLeftVideo.getName()));
            }
            if (SevenmVideoDirectionEnums.LEFT.equals(video.getVideoDirection())) {
                preLeftVideo = video;
            } else {
                preLeftVideo = null;
            }
        }
        //增加最后一个视频的校验
        if (null != preLeftVideo) {
            throw new BizException(String.format("Left and right must be adjacent, left video name: %s", preLeftVideo.getName()));
        }
    }

    @Override
    public PageRes<ProjSevenmExerciseVideoPageVO> pageVideo(PageReq pageReq, Integer id) {
        LambdaQueryWrapper<ProjSevenmWorkoutGenerateExerciseVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId, id)
                .eq(ProjSevenmWorkoutGenerateExerciseVideo::getDelFlag, 0)
                .orderByAsc(ProjSevenmWorkoutGenerateExerciseVideo::getId);
        IPage<ProjSevenmWorkoutGenerateExerciseVideo> page = generateVideoService.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), queryWrapper);
        List<ProjSevenmWorkoutGenerateExerciseVideo> relations = page.getRecords();
        if (CollUtil.isEmpty(relations)) {
            return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
        }
        //获取videoIdSet
        List<Integer> videoIds = relations.stream().map(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmExerciseVideoId).collect(Collectors.toList());
        //查询video信息
        List<ProjSevenmExerciseVideoPageVO> videos = videoService.listVOByIds(videoIds);
        // 按 videoIds 顺序排序
        Map<Integer, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < videoIds.size(); i++) {
            orderMap.put(videoIds.get(i), i);
        }
        videos.sort(Comparator.comparingInt(v -> orderMap.getOrDefault(v.getId(), Integer.MAX_VALUE)));
        return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), page.getTotal(), page.getPages(), videos);
    }

    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.set(ProjSevenmWorkoutGenerate::getUpdateTime, LocalDateTime.now());
        wrapper.set(ProjSevenmWorkoutGenerate::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.in(ProjSevenmWorkoutGenerate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjSevenmWorkoutGenerate::getId, idList);
        update(new ProjSevenmWorkoutGenerate(), wrapper);
    }


    @Override
    public void updateDisableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        Set<Integer> idSet = new HashSet<>(idList);
        //TODO 补充校验逻辑
        List<Object> workoutRelations = new ArrayList<>();
        BizExceptionUtil.throwIf(CollUtil.isNotEmpty(workoutRelations), "This workout cannot be disabled because it is used in the following images");
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmWorkoutGenerate::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.set(ProjSevenmWorkoutGenerate::getUpdateTime, LocalDateTime.now());
        wrapper.set(ProjSevenmWorkoutGenerate::getUpdateUser, RequestContextUtils.getLoginUserName());
        wrapper.eq(ProjSevenmWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjSevenmWorkoutGenerate::getId, idSet);
        this.update(new ProjSevenmWorkoutGenerate(), wrapper);
    }

    @Override
    public void deleteByIdList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        //TODO 补充校验逻辑
        List<Object> workoutRelations = new ArrayList<>();
        BizExceptionUtil.throwIf(CollUtil.isNotEmpty(workoutRelations), "This workout cannot be deleted because it is used in the following images");
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmWorkoutGenerate::getDelFlag, GlobalConstant.YES)
                .set(ProjSevenmWorkoutGenerate::getUpdateTime, LocalDateTime.now())
                .set(ProjSevenmWorkoutGenerate::getUpdateUser, RequestContextUtils.getLoginUserName())
                .in(ProjSevenmWorkoutGenerate::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_NOT_READY)
                .in(ProjSevenmWorkoutGenerate::getId, idList);
        this.update(new ProjSevenmWorkoutGenerate(), wrapper);
    }

    @Override
    public ProjSevenmWorkoutGenerate getByTemplateIdAndTaskId(Integer templateId, Integer taskId) {
        LambdaQueryWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmWorkoutGenerate::getProjSevenmTemplateId, templateId)
                .eq(ProjSevenmWorkoutGenerate::getProjSevenmTemplateTaskId, taskId)
                .eq(ProjSevenmWorkoutGenerate::getDelFlag, 0);
        return getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveWorkoutGenerate(ProjSevenmWorkoutGenerate workoutGenerate) {
        if (workoutGenerate == null) {
            throw new RuntimeException("参数不能为空");
        }
        save(workoutGenerate);
        return workoutGenerate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkoutGenerate(ProjSevenmWorkoutGenerate workoutGenerate) {
        if (workoutGenerate == null || workoutGenerate.getId() == null) {
            throw new RuntimeException("参数不能为空");
        }
        updateById(workoutGenerate);
    }

    @Override
    public void updateFileStatus(Integer id, Integer fileStatus, String failMessage) {
        if (id == null || fileStatus == null) {
            throw new RuntimeException("参数不能为空");
        }
        ProjSevenmWorkoutGenerate workoutGenerate = new ProjSevenmWorkoutGenerate();
        workoutGenerate.setId(id);
        workoutGenerate.setFileStatus(fileStatus);
        workoutGenerate.setFailMessage(failMessage);
        updateById(workoutGenerate);
    }


    @Override
    public void generateM3u8Interrupt() {
        generateM3u8Queue.clear();
        //update all fileStatus to 0
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjSevenmWorkoutGenerate::getFileStatus, GlobalConstant.ONE)
                .set(ProjSevenmWorkoutGenerate::getFileStatus, GlobalConstant.ZERO);
        baseMapper.update(new ProjSevenmWorkoutGenerate(), updateWrapper);
    }

    private Set<String> getLanguages(List<String> req, ProjInfo projInfo) {
        Set<String> languages = new HashSet<>();
        if (CollUtil.isEmpty(req)) {
            languages.add(GlobalConstant.DEFAULT_LANGUAGE);
            languages.addAll(StrUtil.split(projInfo.getLanguages(), GlobalConstant.COMMA, true, true));
        } else {
            languages.addAll(req);
        }
        return languages;
    }



    private List<ProjSevenmManualWorkoutGenerateM3u8Req> assembleReqList(ProjSevenmManualWorkoutGenerateM3u8Req m3u8Req, List<Integer> workoutIds, String userName) {
        List<List<Integer>> batches = ListUtil.split(workoutIds, GlobalConstant.FIVE_HUNDRED);
        return batches.stream().map(ids -> {
            ProjSevenmManualWorkoutGenerateM3u8Req req = new ProjSevenmManualWorkoutGenerateM3u8Req();
            BeanUtil.copyProperties(m3u8Req, req);
            req.setWorkoutIds(ids);
            req.setUpdateUser(userName);
            return req;
        }).collect(Collectors.toList());
    }

    private List<Integer> getAllWorkoutIds(ProjSevenmManualWorkoutGenerateM3u8Req m3u8Req) {
        List<ProjSevenmWorkoutGenerate> workouts;
        if (CollUtil.isNotEmpty(m3u8Req.getWorkoutIds())) {
             workouts = this.list(new LambdaQueryWrapper<ProjSevenmWorkoutGenerate>()
                    .in(ProjSevenmWorkoutGenerate::getId, m3u8Req.getWorkoutIds())
                    .select(ProjSevenmWorkoutGenerate::getId));
             return workouts.stream().map(ProjSevenmWorkoutGenerate::getId).collect(Collectors.toList());
        }else {
             return this.listWorkoutIds(m3u8Req.getPageReq());
        }
    }

    private void updateWorkoutFileStatus(int fileStatus, String userName, List<Object> workoutsDetails) {
        this.updateWorkoutFileStatus(fileStatus, userName, workoutsDetails, null);
    }

    private void updateWorkoutFileStatus(int fileStatus, String userName, LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> updateWrapper) {
        this.updateWorkoutFileStatus(fileStatus, userName, null, updateWrapper);
    }

    private void updateWorkoutFileStatus(int fileStatus, String userName, List<Object> workoutsDetails,
                                         LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> updateWrapper) {
        if (updateWrapper == null) {
            updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(ProjSevenmWorkoutGenerate::getId, workoutsDetails);
        }
        //update workout's fileStatus
        updateWrapper.set(ProjSevenmWorkoutGenerate::getFileStatus, fileStatus)
                .set(ProjSevenmWorkoutGenerate::getUpdateTime, LocalDateTime.now())
                .set(ProjSevenmWorkoutGenerate::getUpdateUser, userName);
        this.update(new ProjSevenmWorkoutGenerate(), updateWrapper);
    }

    @Override
    public Integer generateM3u8ByQuery(ProjSevenmManualWorkoutGenerateM3u8Req m3u8Req) {
        //query all worout ids
        List<Integer> workoutIds = getAllWorkoutIds(m3u8Req);
        if(CollUtil.isEmpty(workoutIds)) return 0;
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        String userName = Objects.isNull(userInfo) ? GlobalConstant.UNKNOWN_USER : userInfo.getUserName();
        //assemble req List
        List<ProjSevenmManualWorkoutGenerateM3u8Req> reqList = assembleReqList(m3u8Req, workoutIds, userName);
        //add to queue
        generateM3u8Queue.addAll(reqList);
        //update workout's fileStatus to 1-processing
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjSevenmWorkoutGenerate::getId, workoutIds);
        this.updateWorkoutFileStatus(GlobalConstant.ONE, userName, updateWrapper);
        //async running generateM3u8
        ProjInfo projInfo = projInfoService.getById(m3u8Req.getProjId());
        CompletableFuture.runAsync(() -> runQueueGenerateM3u8(projInfo, userName));
        return workoutIds.size();
    }

    private void runQueueGenerateM3u8(ProjInfo projInfo, String userName) {
        if (!generateM3u8ReqIsRunning.compareAndSet(false, true)) {
            log.warn("SevenmWorkoutGenerate QueueGenerateM3u8: is running, already add req to queue");
            return;
        }
        log.info("start running SevenmWorkoutGenerate QueueGenerateM3u8:");
        ProjSevenmManualWorkoutGenerateM3u8Req[] reqArray = new ProjSevenmManualWorkoutGenerateM3u8Req[1];
        ProjSevenmWorkoutGenerateServiceImpl bean = SpringUtil.getBean(ProjSevenmWorkoutGenerateServiceImpl.class);

        try {
            while ((reqArray[0] = generateM3u8Queue.poll()) != null) {
                final ProjSevenmManualWorkoutGenerateM3u8Req req = reqArray[0];
                log.info("SevenmWorkoutGenerate QueueGenerateM3u8 left pool size:{} , req:{}", generateM3u8Queue.size(),req);
                Set<String> languages = getLanguages(req.getLanguages(), projInfo);
                M3u8GenerateContext context = assembleM3u8GenerateBo(req, null, null);
                log.info("SevenmWorkoutGenerate QueueGenerateM3u8 start file generate:");
                List<GenerateSevenmWorkoutFileContextBO> fileContextBOs = context.getFileContextBOs();
                for (GenerateSevenmWorkoutFileContextBO fileContextBO : fileContextBOs) {
                    try {
                        manualWorkoutService.generateWorkoutFile(fileContextBO);
                        transactionTemplate.executeWithoutResult(status ->
                                processGenerateM3u8Update(projInfo,languages,context,fileContextBO,bean, false));
                    } catch (Exception e) {
                        log.error("batch update Sevenm generate workout error", e);
                        updateWorkoutFileStatus(GlobalConstant.TWO, userName, fileContextBO.getWorkoutList().stream().map(SevenmWorkoutWrapperBO::getWorkoutUniqueKey).collect(Collectors.toList()));
                    }
                }
            }
            log.info("end running SevenmWorkoutGenerate QueueGenerateM3u8");
        } catch (Exception e) {
            log.error("Failed to generate M3U8", e);
        } finally {
            generateM3u8ReqIsRunning.set(false);
        }
    }

    private void processGenerateM3u8Update(ProjInfo projInfo, Set<String> languages, M3u8GenerateContext context,
                                           GenerateSevenmWorkoutFileContextBO fileContextBO, ProjSevenmWorkoutGenerateServiceImpl bean,
                                           boolean newRelationFlag) {

        List<String> workoutIds = fileContextBO.getWorkoutList().stream().map(sevenmWorkoutWrapperBO -> sevenmWorkoutWrapperBO.getWorkoutUniqueKey().toString()).collect(Collectors.toList());
        log.info("batch update database Sevenm generate workout:{}", workoutIds);

        Collection<ProjSevenmWorkoutGenerateI18n> allI18nWorkoutList = new ArrayList<>();
        Collection<ProjSevenmWorkoutGenerate> updateWorkoutList = new ArrayList<>();
        Collection<ProjSevenmWorkoutGenerateExerciseVideo> updateRelationList = new ArrayList<>();

        for (SevenmWorkoutWrapperBO sevenmWorkoutWrapperBO : fileContextBO.getWorkoutList()) {
            ProjSevenmWorkoutGenerate workoutGenerate = context.getWorkoutMap().get(sevenmWorkoutWrapperBO.getWorkoutUniqueKey());
            if (Objects.isNull(workoutGenerate)) {
                continue;
            }
            WorkoutFileUploadInfoBO uploadInfoBO = sevenmWorkoutWrapperBO.getUploadInfoBO();
            workoutGenerate.setFileStatus(GlobalConstant.ZERO);
            workoutGenerate.setDuration(uploadInfoBO.getDuration());
            workoutGenerate.setVideoUrl(uploadInfoBO.getVideoUrl());
            workoutGenerate.setCalorie(uploadInfoBO.getCalorie());
            workoutGenerate.setAudioLanguages(CollUtil.join(languages, GlobalConstant.COMMA));
            updateWorkoutList.add(workoutGenerate);

            List<ProjSevenmWorkoutGenerateExerciseVideo> relations = context.getRelationMap().get(workoutGenerate.getId());
            Map<Integer, BaseGenerateVideoBO> relationMap = sevenmWorkoutWrapperBO.getVideoList().stream().collect(
                    Collectors.toMap(
                            b -> b.getVideo().getId(),
                            Function.identity(),
                            (existing, replacement) -> existing
                    )
            );
            for (ProjSevenmWorkoutGenerateExerciseVideo relation : relations) {
                BaseGenerateVideoBO bo = relationMap.get(relation.getProjSevenmExerciseVideoId());
                if(Objects.nonNull(bo)){
                    relation.setVideoDuration(bo.getVideoDuration());
                    relation.setPreviewDuration(bo.getPreviewDuration());
                }
                updateRelationList.add(relation);
            }

            Map<String, String> i18nMap = MapUtil.defaultIfEmpty(uploadInfoBO.getAudioI18nUrl(), new HashMap<>());
            List<ProjSevenmWorkoutGenerateI18n> i18nWorkoutList = i18nMap.entrySet().stream().map(entry -> {
                ProjSevenmWorkoutGenerateI18n workoutI18n = new ProjSevenmWorkoutGenerateI18n();
                workoutI18n.setProjSevenmWorkoutGenerateId(workoutGenerate.getId());
                workoutI18n.setLanguage(entry.getKey());
                workoutI18n.setAudioJsonUrl(entry.getValue());
                workoutI18n.setProjId(projInfo.getId());
                return  workoutI18n;
            }).collect(Collectors.toList());
            allI18nWorkoutList.addAll(i18nWorkoutList);
        }

        //delete old i18nData
        LambdaUpdateWrapper<ProjSevenmWorkoutGenerateI18n> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.in(ProjSevenmWorkoutGenerateI18n::getProjSevenmWorkoutGenerateId, workoutIds);
        deleteWrapper.eq(ProjSevenmWorkoutGenerateI18n::getProjId, projInfo.getId());
        deleteWrapper.eq(ProjSevenmWorkoutGenerateI18n::getDelFlag, 0);
        deleteWrapper.in(CollUtil.isNotEmpty(languages), ProjSevenmWorkoutGenerateI18n::getLanguage,languages);
        workoutGenerateI18nService.remove(deleteWrapper);
        //save new i18nData
        if (CollUtil.isNotEmpty(allI18nWorkoutList)) workoutGenerateI18nService.saveBatch(allI18nWorkoutList);
        if (newRelationFlag) {
            //delete old workoutGenerateExerciseVideo
            generateVideoService.deleteByWorkoutIds(updateWorkoutList.stream().map(ProjSevenmWorkoutGenerate::getId).collect(Collectors.toSet()));
            //save new workoutGenerateExerciseVideo
            generateVideoService.saveBatch(updateRelationList);
        } else {
            //update workoutGenerateExerciseVideo
            generateVideoService.updateBatchById(updateRelationList);
        }
        //update workoutGenerate
        bean.updateBatchById(updateWorkoutList);
    }

    private Map<SevenmTypeEnums, ProjSevenmTemplateExerciseGroup> getGroupMap(Integer id) {
        LambdaQueryWrapper<ProjSevenmTemplateExerciseGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmTemplateExerciseGroup::getDelFlag, 0)
                .eq(ProjSevenmTemplateExerciseGroup::getProjSevenmTemplateId, id);
        List<ProjSevenmTemplateExerciseGroup> groups = groupMapper.selectList(wrapper);
        return groups.stream().collect(
                Collectors.toMap(ProjSevenmTemplateExerciseGroup::getGroupType, v -> v));
    }

    private M3u8GenerateContext assembleM3u8GenerateBo(ProjSevenmManualWorkoutGenerateM3u8Req req,
                                                       ProjSevenmWorkoutGenerate workoutGenerate,
                                                       List<ProjSevenmExerciseVideo> videos) {
        M3u8GenerateContext context = new M3u8GenerateContext();
        List<ProjSevenmWorkoutGenerate> workouts;
        if (workoutGenerate == null) {
            workouts = this.list(new LambdaQueryWrapper<ProjSevenmWorkoutGenerate>()
                    .in(ProjSevenmWorkoutGenerate::getId, req.getWorkoutIds()));
            context.setWorkoutsDetails(workouts);
            Map<Integer,ProjSevenmWorkoutGenerate> wMap =
                    workouts.stream().collect(Collectors.toMap(ProjSevenmWorkoutGenerate::getId, v -> v));
            context.setWorkoutMap(wMap);
        }else{
            workouts = ListUtil.of(workoutGenerate);
            context.setWorkoutsDetails(workouts);
            context.setWorkoutMap(MapUtil.of(workoutGenerate.getId(), workoutGenerate));
        }

        if (CollUtil.isEmpty(workouts)) {
            return context;
        }

        if (CollUtil.isEmpty(videos)) {
            List<ProjSevenmWorkoutGenerateExerciseVideo> relations = generateVideoService.list(new LambdaQueryWrapper<ProjSevenmWorkoutGenerateExerciseVideo>()
                    .in(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId, req.getWorkoutIds())
                    .orderByAsc(ProjSevenmWorkoutGenerateExerciseVideo::getId));
            context.setRelationVideos(relations);
            Map<Integer, List<ProjSevenmWorkoutGenerateExerciseVideo>> relMap = relations.stream()
                    .collect(Collectors.groupingBy(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId));
            context.setRelationMap(relMap);

            Set<Integer> videoIds = relations.stream()
                    .map(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmExerciseVideoId)
                    .collect(Collectors.toSet());
            Map<Integer, ProjSevenmExerciseVideo> vMap = videoService.list(new LambdaQueryWrapper<ProjSevenmExerciseVideo>()
                            .in(ProjSevenmExerciseVideo::getId, videoIds))
                    .stream().collect(Collectors.toMap(ProjSevenmExerciseVideo::getId, v -> v));
            context.setVideoMap(vMap);

            Set<Integer> groupIds = relations.stream()
                    .map(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmTemplateExerciseGroupId)
                    .collect(Collectors.toSet());
            Map<Integer, ProjSevenmTemplateExerciseGroup> gMap = exerciseGroupService.list(new LambdaQueryWrapper<ProjSevenmTemplateExerciseGroup>()
                            .in(ProjSevenmTemplateExerciseGroup::getId, groupIds))
                    .stream().collect(Collectors.toMap(ProjSevenmTemplateExerciseGroup::getId, g -> g));
            context.setGroupMap(gMap);
        } else {
            Map<SevenmTypeEnums, ProjSevenmTemplateExerciseGroup> groupTypeMap = this.getGroupMap(Objects.requireNonNull(workoutGenerate).getProjSevenmTemplateId());
            List<ProjSevenmWorkoutGenerateExerciseVideo> videoRelationList = new ArrayList<>(videos.size());
            videos.forEach(item -> {
                ProjSevenmTemplateExerciseGroup group = groupTypeMap.get(item.getType());
                ProjSevenmWorkoutGenerateExerciseVideo videoRelation = new ProjSevenmWorkoutGenerateExerciseVideo();
                videoRelation.setProjSevenmExerciseVideoId(item.getId())
                        .setProjSevenmWorkoutGenerateId(workoutGenerate.getId())
                        .setProjSevenmTemplateId(workoutGenerate.getProjSevenmTemplateId())
                        .setProjSevenmTemplateExerciseGroupId(group.getId());
                videoRelationList.add(videoRelation);
            });
            context.setRelationVideos(videoRelationList);
            context.setRelationMap(MapUtil.of(workoutGenerate.getId(), videoRelationList));
            context.setVideoMap(videos.stream().collect(Collectors.toMap(ProjSevenmExerciseVideo::getId, v -> v)));
            context.setGroupMap(groupTypeMap.values().stream().collect(Collectors.toMap(
                    ProjSevenmTemplateExerciseGroup::getId, v -> v)));
        }

        List<SevenmWorkoutWrapperBO> workoutWrapperList = new ArrayList<>();
        for (ProjSevenmWorkoutGenerate workout : workouts) {
            Integer workoutId = workout.getId();
            List<ProjSevenmWorkoutGenerateExerciseVideo> relationList = context.getRelationMap().get(workoutId);
            //video拉平
            List<BaseGenerateVideoBO> baseGenerateVideoList = new ArrayList<>();
            Map<Integer, BaseGenerateVideoBO> baseGenerateVideoMap = new HashMap<>();
            for (ProjSevenmWorkoutGenerateExerciseVideo relationVideo : relationList) {
                Integer videoId = relationVideo.getProjSevenmExerciseVideoId();
                BaseGenerateVideoBO baseVideoBO = baseGenerateVideoMap.get(videoId);
                Integer circuit = context.getGroupMap().get(relationVideo.getProjSevenmTemplateExerciseGroupId()).getRounds();
                if(null != baseVideoBO){
                    for (int i = 0; i < circuit; i++) {
                        baseGenerateVideoList.add(baseVideoBO);
                    }
                    continue;
                }
                BaseGenerateVideoBO videoBO = new BaseGenerateVideoBO();
                ProjSevenmExerciseVideo video = context.getVideoMap().get(videoId);
                videoBO.setVideo(video);
                for (int i = 0; i < circuit; i++) {
                    baseGenerateVideoList.add(videoBO);
                }
                baseGenerateVideoMap.put(videoId, videoBO);
            }
            SevenmWorkoutWrapperBO workoutWrapper = new SevenmWorkoutWrapperBO();
            workoutWrapper.setVideoList(baseGenerateVideoList);
            workoutWrapper.setGender(workout.getGender());
            workoutWrapper.setWorkoutUniqueKey(workout.getId());
            workoutWrapper.setWorkoutType(workout.getWorkoutType());
            workoutWrapperList.add(workoutWrapper);
        }
        context.setFileContextBOs(new ArrayList<>());
        CollUtil.split(workoutWrapperList, GlobalConstant.FIVE_HUNDRED).forEach(subList -> {
            GenerateSevenmWorkoutFileContextBO fileContextBO = new GenerateSevenmWorkoutFileContextBO(subList, req.getLanguages());
            fileContextBO.setGenerateAudioJson(req.getAudioFlag());
            fileContextBO.setGenerateM3u8(req.getVideoFlag());
            context.getFileContextBOs().add(fileContextBO);
        });
        return context;
    }


    @Override
    public void generateWorkoutByTask(List<ProjSevenmTemplateTask> templateTasks, ProjInfo projInfo) {

        try{
            // 1. check task and template
            List<String> languageList = getLanguageList(projInfo);
            List<SevenmTemplateTaskBO> templateTaskBOList = checkTemplateTask4Generate(templateTasks, languageList);

            // 2. check video and create BO
            SevenmWorkoutGenerateVideoBO generateVideoBO = createWorkoutGenerateVideoBO(languageList);

            // 3. putVideoInfo to context
            templateTaskBOList.get(0).getFileContextBO().setVideoSoundI18nMap(
                    manualWorkoutService.listVideoI18n(languageList, new ArrayList<>(generateVideoBO.getVideoIdMap().values())));

            templateTaskBOList.forEach(taskBO -> {
                generateSingleAndUpdateStatus(taskBO, generateVideoBO);
            });

        } catch (Exception e) {
            log.warn("Generate sevenm workout failed.");
            log.warn(e.getMessage(), e);
            templateTasks.forEach(task -> task.setStatus(SevenmTemplateTaskStatusEnum.FAIL).setFailureMessage(StringUtils.substring(e.getMessage(), 0, 250)));
            taskService.saveOrUpdateBatch(templateTasks);
        }
    }

    private void generateSingleAndUpdateStatus(SevenmTemplateTaskBO taskBO, SevenmWorkoutGenerateVideoBO generateVideoBO) {

        try{
            updateStatus4Task(taskBO.getTemplateTask(), SevenmTemplateTaskStatusEnum.RUNNING, null);
            generateWorkout(taskBO, generateVideoBO);
            success4Task(taskBO.getTemplateTask());
            log.debug("Generate succeed, templateId : {}", taskBO.getTemplate().getId());
        } catch (Exception e) {
            log.warn("Generate Sevenm workout failed.");
            log.warn(e.getMessage(), e);
            updateStatus4Task(taskBO.getTemplateTask(), SevenmTemplateTaskStatusEnum.FAIL, StringUtils.substring(e.getMessage(), 0, 250));
        }
    }


    private void success4Task(ProjSevenmTemplateTask templateTask) {

        templateTask.setStatus(SevenmTemplateTaskStatusEnum.SUCCESS);
        taskService.updateById(templateTask);

        if (templateTask.getCleanUp() == GlobalConstant.ONE) {
            LambdaUpdateWrapper<ProjSevenmWorkoutGenerate> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(ProjSevenmWorkoutGenerate::getDelFlag, GlobalConstant.ONE);
            updateWrapper.set(ProjSevenmWorkoutGenerate::getUpdateTime, LocalDateTime.now());
            updateWrapper.eq(ProjSevenmWorkoutGenerate::getProjSevenmTemplateId, templateTask.getProjSevenmTemplateId());
            updateWrapper.ne(ProjSevenmWorkoutGenerate::getProjSevenmTemplateTaskId, templateTask.getId());
            update(updateWrapper);

            LambdaUpdateWrapper<ProjSevenmWorkoutGenerateExerciseVideo> relationUpdateWrapper = new LambdaUpdateWrapper<>();
            relationUpdateWrapper.set(ProjSevenmWorkoutGenerateExerciseVideo::getDelFlag, GlobalConstant.ONE);
            relationUpdateWrapper.set(ProjSevenmWorkoutGenerateExerciseVideo::getUpdateTime, LocalDateTime.now());
            relationUpdateWrapper.eq(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmTemplateId, templateTask.getProjSevenmTemplateId());
            relationUpdateWrapper.ne(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmTemplateTaskId, templateTask.getId());
            generateVideoService.update(relationUpdateWrapper);
        }
    }

    private void generateWorkout(SevenmTemplateTaskBO templateTaskBO, SevenmWorkoutGenerateVideoBO generateVideoBO) {

        ProjSevenmTemplate sevenmTemplate = templateTaskBO.getTemplate();
        Integer workoutCountPerday = sevenmTemplate.getTemplateType().getWorkoutCountPerday();
        Map<SevenmTypeEnums, ProjSevenmTemplateExerciseGroup> typeAndGroupMap = templateTaskBO.getTemplateGroupMap();
        List<SevenmWorkoutTargetEnums> targetEnumsList = Arrays.stream(SevenmWorkoutTargetEnums.values()).collect(Collectors.toList());
        List<SevenmExerciseTypeEnums> exerciseTypeEnumsList = Arrays.stream(SevenmExerciseTypeEnums.values()).collect(Collectors.toList());
        List<SevenmWorkoutGenerateBO> workoutGenerateList = Lists.newArrayListWithExpectedSize(sevenmTemplate.getDays() * targetEnumsList.size());

        int totalCount = workoutCountPerday * sevenmTemplate.getDays();
        for (int count = 0; count < totalCount; count++) {
            for (SevenmExerciseTypeEnums exerciseType : exerciseTypeEnumsList) {
                SevenmEquipmentEnums equipment = exerciseType.getEquipment();
                SevenmGenderEnums gender = exerciseType.getGender();
                for (SevenmWorkoutTargetEnums workoutTargetEnums : targetEnumsList) {

                    LinkedList<SevenmPositionEnums> positionEnum4Main = randomSelectPosition(2,true);
                    LinkedList<SevenmPositionEnums> positionEnum4Warmup = selectPosition4WarmUp(positionEnum4Main);
                    LinkedList<SevenmPositionEnums> positionEnum4CoolDown = randomSelectPosition(2,true);

                    ProjSevenmTemplateExerciseGroup mainGroup = typeAndGroupMap.get(SevenmTypeEnums.MAIN);
                    List<BaseGenerateVideoBO> mainVideos = Collections.emptyList();
                    if (mainGroup != null && mainGroup.getCount() != null && mainGroup.getCount() > 0) {
                        mainVideos = selectVideo4Main(positionEnum4Main, sevenmTemplate, exerciseType, equipment, workoutTargetEnums, generateVideoBO, mainGroup,gender);
                        SevenmPositionEnums position = mainVideos.get(0).getVideo().getPosition();
                        if (ObjUtil.notEqual(positionEnum4Warmup.getFirst(), position)) {
                            //重新获取warmup的position
                            positionEnum4Warmup = selectPosition4WarmUp(CollUtil.newLinkedList(position));
                        }
                    }

                    ProjSevenmTemplateExerciseGroup warmUpGroup = typeAndGroupMap.get(SevenmTypeEnums.WARM_UP);
                    List<BaseGenerateVideoBO> warmupVideos = Collections.emptyList();
                    if (warmUpGroup != null && warmUpGroup.getCount()!= null && warmUpGroup.getCount()> 0) {
                        warmupVideos = selectVideo4WarmAndCoolDown(positionEnum4Warmup, sevenmTemplate, exerciseType,workoutTargetEnums, equipment, generateVideoBO, warmUpGroup,gender);
                    }

                    ProjSevenmTemplateExerciseGroup cooldownGroup = typeAndGroupMap.get(SevenmTypeEnums.COOL_DOWN);
                    List<BaseGenerateVideoBO> cooldownVideos = Collections.emptyList();
                    if (cooldownGroup != null && cooldownGroup.getCount() != null && cooldownGroup.getCount() > 0 ) {
                        cooldownVideos = selectVideo4WarmAndCoolDown(positionEnum4CoolDown, sevenmTemplate, exerciseType, workoutTargetEnums, equipment, generateVideoBO, cooldownGroup, gender);
                    }

                    LinkedList<BaseGenerateVideoBO> selectedVideos = Lists.newLinkedList(warmupVideos);
                    selectedVideos.addAll(mainVideos);
                    selectedVideos.addAll(cooldownVideos);

                    ProjSevenmWorkoutGenerate workoutGenerate = wrapWorkoutGenerate(templateTaskBO.getTemplateTask(), equipment, sevenmTemplate, workoutTargetEnums,gender);
                    SevenmWorkoutGenerateBO workoutGenerateBO = new SevenmWorkoutGenerateBO(selectedVideos, workoutGenerate, null, UUID.randomUUID().toString());
                    workoutGenerateList.add(workoutGenerateBO);
                }
            }
        }
        List<String> languageList = templateTaskBO.getLanguageList();
        GenerateSevenmWorkoutFileContextBO contextBO = batchGenerateResource(workoutGenerateList, languageList, generateVideoBO,templateTaskBO.getFileContextBO());
        saveWorkoutAndRelation(workoutGenerateList,contextBO);
        templateTaskBO.getTemplateTask().setWorkoutNum(workoutGenerateList.size());
    }

    public GenerateSevenmWorkoutFileContextBO batchGenerateResource(List<SevenmWorkoutGenerateBO> workoutGenerateList, List<String> languageList,
                                                                    SevenmWorkoutGenerateVideoBO generateVideoBO,
                                                                    GenerateSevenmWorkoutFileContextBO fileContextBO) {
        List<SevenmWorkoutWrapperBO> workoutWrapperList = new ArrayList<>();
        Set<ProjSevenmExerciseVideo> videoSet = new HashSet<>();
        workoutGenerateList.forEach(bo -> {
            LinkedList<BaseGenerateVideoBO> relationList = bo.getWorkoutAndRelationList();
            //video拉平
            List<BaseGenerateVideoBO> baseGenerateVideoList = new ArrayList<>();
            relationList.forEach(relation -> {
                Integer videoRound = relation.getVideoRound();
                for (int i = 0; i < videoRound; i++) {
                    baseGenerateVideoList.add(relation);
                }
                videoSet.add(relation.getVideo());
            });
            SevenmWorkoutWrapperBO workoutWrapper = new SevenmWorkoutWrapperBO();
            workoutWrapper.setVideoList(baseGenerateVideoList);
            workoutWrapper.setGender(bo.getWorkoutGenerate().getGender());
            workoutWrapper.setWorkoutUniqueKey(bo.getUniqueKey());
            workoutWrapper.setWorkoutType(bo.getWorkoutGenerate().getWorkoutType());
            workoutWrapperList.add(workoutWrapper);
        });
        fileContextBO.setWorkoutList(workoutWrapperList);
        manualWorkoutService.generateWorkoutFile(fileContextBO);
        return fileContextBO;
    }

    private ProjSevenmWorkoutGenerateExerciseVideo wrapGenerateExerciseVideo(ProjSevenmWorkoutGenerate workout,
                                                                              BaseGenerateVideoBO relation) {

        ProjSevenmWorkoutGenerateExerciseVideo workoutVideo = new ProjSevenmWorkoutGenerateExerciseVideo();
        workoutVideo.setProjSevenmTemplateId(workout.getProjSevenmTemplateId());
        workoutVideo.setProjSevenmTemplateTaskId(workout.getProjSevenmTemplateTaskId());
        workoutVideo.setProjSevenmWorkoutGenerateId(workout.getId());
        workoutVideo.setProjSevenmTemplateExerciseGroupId(relation.getGroupId());
        workoutVideo.setVideoDuration(relation.getVideoDuration());
        workoutVideo.setPreviewDuration(relation.getPreviewDuration());
        workoutVideo.setProjSevenmExerciseVideoId(relation.getVideo().getId());
        return workoutVideo;
    }

    private void saveWorkoutAndRelation(List<SevenmWorkoutGenerateBO> workoutGenerateList, GenerateSevenmWorkoutFileContextBO contextBO) {
        //将workoutList转为key，value，key为uniquekey
        Map<Object, SevenmWorkoutWrapperBO> resultMap = contextBO.getWorkoutList().stream().collect(Collectors.toMap(
                SevenmWorkoutWrapperBO::getWorkoutUniqueKey, item -> item));

        // workout
        List<ProjSevenmWorkoutGenerate> workoutList = workoutGenerateList.stream().map(
                sevenmWorkoutGenerateBO -> {
                    ProjSevenmWorkoutGenerate workoutGenerate = sevenmWorkoutGenerateBO.getWorkoutGenerate();
                    SevenmWorkoutWrapperBO wrapperBO = resultMap.get(sevenmWorkoutGenerateBO.getUniqueKey());
                    if (wrapperBO == null) {
                        throw new BizException("workout generateFile Result not found");
                    }
                    workoutGenerate.setCalorie(wrapperBO.getUploadInfoBO().getCalorie());
                    workoutGenerate.setDuration(wrapperBO.getUploadInfoBO().getDuration());
                    workoutGenerate.setVideoUrl(wrapperBO.getUploadInfoBO().getVideoUrl());
                    workoutGenerate.setAudioLanguages(CollUtil.join(wrapperBO.getUploadInfoBO().getAudioI18nUrl().keySet(),GlobalConstant.COMMA));
                    workoutGenerate.setFileStatus(GlobalConstant.ZERO);
                    return workoutGenerate;
                }).collect(Collectors.toList());
        super.saveBatch(workoutList);

        // relation
        List<ProjSevenmWorkoutGenerateExerciseVideo> relationList = workoutGenerateList.stream().map(generateBO -> {
            ProjSevenmWorkoutGenerate workout = generateBO.getWorkoutGenerate();
            return generateBO.getWorkoutAndRelationList().stream().map(
                    relation -> wrapGenerateExerciseVideo(workout, relation)).collect(Collectors.toList());
        }).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        generateVideoService.saveBatch(relationList);

        // workout i18n
        List<ProjSevenmWorkoutGenerateI18n> i18nWorkoutList = workoutGenerateList.stream().map(generateBO -> {
            ProjSevenmWorkoutGenerate workout = generateBO.getWorkoutGenerate();
            SevenmWorkoutWrapperBO sevenmWorkoutWrapperBO = resultMap.get(generateBO.getUniqueKey());
            return sevenmWorkoutWrapperBO.getUploadInfoBO().getAudioI18nUrl().entrySet().stream().map(audioEntry -> {
                ProjSevenmWorkoutGenerateI18n generateI18n = new ProjSevenmWorkoutGenerateI18n();
                generateI18n.setLanguage(audioEntry.getKey());
                generateI18n.setAudioJsonUrl(audioEntry.getValue());
                generateI18n.setProjSevenmWorkoutGenerateId(workout.getId());
                generateI18n.setProjId(workout.getProjId());
                return generateI18n;
            }).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
        workoutGenerateI18nService.saveBatch(i18nWorkoutList);
    }

    private ProjSevenmWorkoutGenerate wrapWorkoutGenerate(ProjSevenmTemplateTask templateTask, SevenmEquipmentEnums equipmentEnums,
                                                          ProjSevenmTemplate SevenmTemplate, SevenmWorkoutTargetEnums targetEnum, SevenmGenderEnums gender) {

        ProjSevenmWorkoutGenerate workoutGenerate = new ProjSevenmWorkoutGenerate();
        workoutGenerate.setStatus(GlobalConstant.ONE);
        workoutGenerate.setProjId(SevenmTemplate.getProjId());
        workoutGenerate.setAudioLanguages(GlobalConstant.DEFAULT_LANGUAGE);
        workoutGenerate.setProjSevenmTemplateId(SevenmTemplate.getId());
        workoutGenerate.setProjSevenmTemplateTaskId(templateTask.getId());

        workoutGenerate.setWorkoutType(SevenmTemplate.getTemplateType());
        workoutGenerate.setDifficulty(SevenmTemplate.getLevel());
        workoutGenerate.setSpecialLimit(SevenmTemplate.getSpecialLimit());

        workoutGenerate.setTarget(targetEnum.getRelatedTargetsEnumsList());
        workoutGenerate.setEquipment(equipmentEnums);
        workoutGenerate.setGender(gender);
        return workoutGenerate;
    }

    private List<BaseGenerateVideoBO> selectVideo4Main(LinkedList<SevenmPositionEnums> positionEnumList,
                                                       ProjSevenmTemplate SevenmTemplate,
                                                       SevenmExerciseTypeEnums exerciseType,
                                                       SevenmEquipmentEnums equipmentEnums,
                                                       SevenmWorkoutTargetEnums targetEnum,
                                                       SevenmWorkoutGenerateVideoBO generateVideoBO,
                                                       ProjSevenmTemplateExerciseGroup group,
                                                       SevenmGenderEnums gender) {

        List<SevenmSpecialLimitEnums> specialLimitList = SevenmTemplate.getSpecialLimit();
        SevenmTypeEnums videoType = group.getGroupType();

        LinkedList<DifficultyAndIntensityMappingBO> difficultyBOList = Lists.newLinkedList(DifficultyAndIntensityMappingBO.getMappingByDifficulty(SevenmTemplate.getLevel()));
        LinkedList<DifficultyAndIntensityMappingBO> nodeDifficultyBOList = Lists.newLinkedList(DifficultyAndIntensityMappingBO.getNoneMappingByDifficuty(SevenmTemplate.getLevel()));
        LinkedList<TargetMappingBO> targetMappingBOList = Lists.newLinkedList(TargetMappingBO.getTargetMapping(targetEnum));
        List<ProjSevenmExerciseVideo> selectedVideoList = Lists.newLinkedList();

        int combineCount = GlobalConstant.ZERO;
        int mainCount = group.getCount();

        for (TargetMappingBO targetMapping : targetMappingBOList) {
            int targetCount;
            if (Objects.equals(targetMapping, targetMappingBOList.getLast())) {
                targetCount = mainCount - combineCount;
            } else {
                targetCount = Math.round(mainCount * targetMapping.getPercent());
            }

            if (targetCount < 1) {
                continue;
            } else {
                combineCount += targetCount;
            }

            //None和符合的Target处理逻辑不通
            if (targetMapping.getTarget().size() == 1 && ObjectUtil.equals(targetMapping.getTarget().get(0), SevenmTargetEnums.NONE)) {
                List<SevenmDifficultyEnums> difficultyEnums = nodeDifficultyBOList.get(0).getDifficultyEnums();
                List<SevenmIntensityEnums> intensityEnums = nodeDifficultyBOList.get(0).getIntensityEnums();
                List<ProjSevenmExerciseVideo> videoList = listVideo4Main(videoType, exerciseType, positionEnumList, equipmentEnums, specialLimitList,
                        difficultyEnums, intensityEnums, targetMapping.getTarget(),gender, generateVideoBO, selectedVideoList);
                List<ProjSevenmExerciseVideo> matchVideoList = selectVideo4Count(videoList, targetCount, generateVideoBO.getVideoIdMap());
                if (!Objects.equals(matchVideoList.size(), targetCount)) {
                    String errorMessage = String.format("No enough main video, mapping is %s, exercise type is %s, position is %s, " +
                                    "specialLimits is %s, difficulty is %s, target is %s. gender is %s",
                            JacksonUtil.toJsonString(nodeDifficultyBOList.get(0)), exerciseType, positionEnumList, specialLimitList, SevenmTemplate.getLevel(), targetEnum,gender);
                    log.warn(errorMessage);
                    throw new BizException(errorMessage);
                }
                selectedVideoList.addAll(matchVideoList);
            } else {
                for (DifficultyAndIntensityMappingBO difficultyMapping : difficultyBOList) {
                    int difficultyCount;
                    if (Objects.equals(difficultyMapping, difficultyBOList.getLast())) {
                        difficultyCount = targetCount;
                    } else {
                        difficultyCount = (int) Math.ceil(targetCount * difficultyMapping.getPercent());
                    }

                    if (difficultyCount < 1) {
                        continue;
                    } else {
                        targetCount -= difficultyCount ;
                    }
                    List<ProjSevenmExerciseVideo> videoList = listVideo4Main(videoType, exerciseType, positionEnumList, equipmentEnums, specialLimitList,
                            difficultyMapping.getDifficultyEnums(), difficultyMapping.getIntensityEnums(), targetMapping.getTarget(),gender, generateVideoBO, selectedVideoList);
                    List<ProjSevenmExerciseVideo> matchVideoList = selectVideo4Count(videoList, difficultyCount, generateVideoBO.getVideoIdMap());
                    if (!Objects.equals(matchVideoList.size(), difficultyCount)) {
                        String errorMessage = String.format("No enough main video, mapping is %s, exercise type is %s, position is %s, " +
                                        "specialLimits is %s, difficulty is %s, target is %s. gender is %s",
                                JacksonUtil.toJsonString(difficultyMapping), exerciseType, positionEnumList, specialLimitList, SevenmTemplate.getLevel(), targetEnum,gender);
                        log.warn(errorMessage);
                        throw new BizException(errorMessage);
                    }
                    selectedVideoList.addAll(matchVideoList);
                }
            }
        }
        //注释掉过滤Position的逻辑，postion不够使用兜底
        /*Map<SevenmPositionEnums, List<ProjSevenmExerciseVideo>> positionMap = selectedVideoList.stream().collect(Collectors.groupingBy(ProjSevenmExerciseVideo::getPosition));
        selectedVideoList = positionEnumList.stream().filter(positionMap::containsKey).map(positionMap::get).flatMap(Collection::stream).collect(Collectors.toList());*/

        return selectedVideoList.stream()
                .map(video -> new BaseGenerateVideoBO(group.getRounds(), group.getId(), video))
                .sorted(Comparator.comparingInt(video -> video.getVideo().getPosition().ordinal()))
                .collect(Collectors.toCollection(LinkedList::new));
    }


    private List<ProjSevenmExerciseVideo> listVideo4Main(SevenmTypeEnums videoType,
                                                         SevenmExerciseTypeEnums exerciseType,
                                                         List<SevenmPositionEnums> positionEnumList,
                                                         SevenmEquipmentEnums equipmentEnums,
                                                         List<SevenmSpecialLimitEnums> specialLimitList,
                                                         List<SevenmDifficultyEnums> difficulty,
                                                         List<SevenmIntensityEnums> intensity,
                                                         List<SevenmTargetEnums> target,
                                                         SevenmGenderEnums gender,
                                                         SevenmWorkoutGenerateVideoBO generateVideoBO,
                                                         List<ProjSevenmExerciseVideo> selectedVideoList) {
        LinkedList<ProjSevenmExerciseVideo> videoList = generateVideoBO.listByPriority(videoType, exerciseType, positionEnumList, equipmentEnums, specialLimitList, difficulty, intensity,
                target,gender);
        return videoList.stream().filter(video -> !selectedVideoList.contains(video)).collect(Collectors.toList());
    }

    private List<BaseGenerateVideoBO> selectVideo4WarmAndCoolDown(LinkedList<SevenmPositionEnums> positionEnumList,
                                                                  ProjSevenmTemplate SevenmTemplate,
                                                                  SevenmExerciseTypeEnums exerciseType,
                                                                  SevenmWorkoutTargetEnums targetEnum,
                                                                  SevenmEquipmentEnums equipmentEnums,
                                                                  SevenmWorkoutGenerateVideoBO generateVideoBO,
                                                                  ProjSevenmTemplateExerciseGroup exerciseGroup,
                                                                  SevenmGenderEnums gender) {

        List<SevenmSpecialLimitEnums> specialLimitList = SevenmTemplate.getSpecialLimit();
        Integer count = exerciseGroup.getCount();
        List<SevenmTargetEnums> targetList = targetEnum.getRelatedTargetsEnumsList();
        List<ProjSevenmExerciseVideo> selectedVideoList = Lists.newLinkedList();

        List<ProjSevenmExerciseVideo> videoList = listVideo4Main(exerciseGroup.getGroupType(), exerciseType, positionEnumList, equipmentEnums, specialLimitList,
                null, null, targetList ,gender, generateVideoBO, selectedVideoList);
        List<ProjSevenmExerciseVideo> matchVideoList = selectVideo4Count(videoList, count, generateVideoBO.getVideoIdMap());

        if (matchVideoList.size() < count) {
            String errorMessage = String.format("No enough warmUp/cooldown video, exercise type is %s, position is %s, " +
                            "specialLimits is %s, target is %s. gender is %s",
                    exerciseType, positionEnumList, specialLimitList,targetEnum,gender);
            log.warn(errorMessage);
            throw new BizException(errorMessage);
        }
        selectedVideoList.addAll(matchVideoList);

        return selectedVideoList.stream()
                .map(video -> new BaseGenerateVideoBO(exerciseGroup.getRounds(), exerciseGroup.getId(), video))
                .sorted(Comparator.comparingInt(video -> video.getVideo().getPosition().ordinal()))
                .collect(Collectors.toList());
    }


    private List<ProjSevenmExerciseVideo> selectVideo4Count(List<ProjSevenmExerciseVideo> videoList,
                                                             Integer requireCount,
                                                             Map<Integer, ProjSevenmExerciseVideo> videoIdMap) {

        int matchCount = videoList.stream().mapToInt(video -> Objects.equals(video.getVideoDirection(), SevenmVideoDirectionEnums.CENTRAL) ? 1 : 2).sum();
        int videoCount = requireCount;
        if (matchCount < videoCount) {
            return Collections.emptyList();
        }

        List<ProjSevenmExerciseVideo> selectedVideoList = Lists.newLinkedList();
        for (ProjSevenmExerciseVideo video : videoList) {
            if (videoCount <= 0) {
                break;
            }

            if (Objects.equals(video.getVideoDirection(), SevenmVideoDirectionEnums.LEFT)) {
                if (videoCount < 2) {
                    Optional<ProjSevenmExerciseVideo> centralVideo = selectedVideoList.stream().filter(exerciseVideo ->
                            Objects.equals(exerciseVideo.getVideoDirection(), SevenmVideoDirectionEnums.CENTRAL)).findAny();
                    if (!centralVideo.isPresent()) {
                        //增加warmUp的的特殊逻辑，如果只有一个left，则补充right
                        if (ObjectUtil.equals(SevenmTypeEnums.WARM_UP,video.getType())) {
                            selectedVideoList.add(video);
                            selectedVideoList.add(videoIdMap.get(video.getLeftRightVideoId()));
                            videoCount -= 2;
                        }
                        continue;
                    }
                    selectedVideoList.remove(centralVideo.get());
                    videoCount += 1;
                }
                selectedVideoList.add(video);
                selectedVideoList.add(videoIdMap.get(video.getLeftRightVideoId()));
                videoCount -= 2;
            }

            if (Objects.equals(video.getVideoDirection(), SevenmVideoDirectionEnums.CENTRAL)) {
                selectedVideoList.add(video);
                videoCount -= 1;
            }
        }
        return selectedVideoList;
    }
    private LinkedList<SevenmPositionEnums> randomSelectPosition(int bound,boolean needSort) {

        List<SevenmPositionEnums> positionEnumsList = Arrays.stream(SevenmPositionEnums.values()).collect(Collectors.toList());
        Collections.shuffle(positionEnumsList);
        if (bound > 0) {
            Random random = new Random();
            int positionCount = random.nextInt(bound) + 1;
            positionEnumsList = positionEnumsList.subList(0, positionCount);
        }
        if(needSort) positionEnumsList.sort(Comparator.comparing(SevenmPositionEnums::getCode));
        return Lists.newLinkedList(positionEnumsList);
    }


    private LinkedList<SevenmPositionEnums> selectPosition4WarmUp(LinkedList<SevenmPositionEnums> positionEnums4Main) {

        List<SevenmPositionEnums> allPositionEnumList = Arrays.stream(SevenmPositionEnums.values()).collect(Collectors.toList());
        SevenmPositionEnums selectPosition = positionEnums4Main.getFirst();

        return allPositionEnumList.stream().filter(
                position -> position.getCode() <= selectPosition.getCode())
                .sorted(Comparator.comparing(SevenmPositionEnums::getCode).reversed())
                .collect(Collectors.toCollection(LinkedList::new));
    }

    private void updateStatus4Task(ProjSevenmTemplateTask templateTask, SevenmTemplateTaskStatusEnum status, String failMessage) {
        templateTask.setStatus(status);
        templateTask.setFailureMessage(failMessage);
        taskService.updateById(templateTask);
    }

    private List<String> getLanguageList(ProjInfo projInfo) {

        if (Objects.isNull(projInfo) || StringUtils.isBlank(projInfo.getLanguages())) {
            return Lists.newArrayList(GlobalConstant.DEFAULT_LANGUAGE);
        }

        // proj_info 可能不存在en配置
        List<String> languageList = Arrays.stream(projInfo.getLanguages().split(GlobalConstant.COMMA)).sorted().collect(Collectors.toList());
        if (!languageList.contains(GlobalConstant.DEFAULT_LANGUAGE)) {
            languageList.add(GlobalConstant.DEFAULT_LANGUAGE);
        }
        return languageList;
    }

    private List<SevenmTemplateTaskBO> checkTemplateTask4Generate(List<ProjSevenmTemplateTask> templateTaskList, List<String> languageList) {

        Set<Integer> templateIds = templateTaskList.stream().map(ProjSevenmTemplateTask::getProjSevenmTemplateId).collect(Collectors.toSet());
        Collection<ProjSevenmTemplate> templateList = templateService.listByIds(templateIds);
        Map<Integer, ProjSevenmTemplate> templateIdMap = templateList.stream().collect(Collectors.toMap(ProjSevenmTemplate::getId, Function.identity()));

        List<ProjSevenmTemplateExerciseGroup> exerciseGroupList = exerciseGroupService.listByTemplateIds(templateIds);
        Map<Integer, List<ProjSevenmTemplateExerciseGroup>> templateAndGroupMap = exerciseGroupList.stream().collect(
                Collectors.groupingBy(ProjSevenmTemplateExerciseGroup::getProjSevenmTemplateId));


        GenerateSevenmWorkoutFileContextBO fileContextBO = new GenerateSevenmWorkoutFileContextBO(null, languageList);
        fileContextBO.setFemaleSoundBoMap(manualWorkoutService.getSoundI18n(languageList, SevenmGenderEnums.FEMALE));
        fileContextBO.setMaleSoundBoMap(manualWorkoutService.getSoundI18n(languageList, SevenmGenderEnums.MALE));

        return templateTaskList.stream().map(task -> {
            ProjSevenmTemplate template = templateIdMap.get(task.getProjSevenmTemplateId());
            List<ProjSevenmTemplateExerciseGroup> exerciseGroup = templateAndGroupMap.get(task.getProjSevenmTemplateId());

            Map<SevenmTypeEnums, ProjSevenmTemplateExerciseGroup> typeAndGroupMap = exerciseGroup.stream().collect(Collectors.toMap(ProjSevenmTemplateExerciseGroup::getGroupType, Function.identity(), (k1, k2) -> k1));
            return new SevenmTemplateTaskBO(task, template, typeAndGroupMap, languageList,fileContextBO);
        }).collect(Collectors.toList());
    }

    @Override
    public SevenmWorkoutGenerateVideoBO createWorkoutGenerateVideoBO(List<String> languageList) {

        SevenmWorkoutGenerateVideoBO workoutGenerateBO = new SevenmWorkoutGenerateVideoBO();
        List<ProjSevenmExerciseVideo> allVideos = videoService.listEnable4AudoGenerate();
        List<ProjSevenmExerciseVideo> validVideoList = allVideos.stream()
                .filter(video -> Objects.nonNull(video.getFrontVideoDuration()))
                .filter(video -> Objects.nonNull(video.getSideVideoDuration()))
                .collect(Collectors.toList());

        Map<Integer, ProjSevenmExerciseVideo> videoIdMap = validVideoList.stream().collect(Collectors.toMap(ProjSevenmExerciseVideo::getId, Function.identity()));
        workoutGenerateBO.setVideoIdMap(videoIdMap);

        List<ProjSevenmExerciseVideo> centralAndLeftVideos = validVideoList.stream()
                .filter(video -> {
                    if (Objects.equals(video.getVideoDirection(), SevenmVideoDirectionEnums.CENTRAL)) {
                        return true;
                    }
                    if (Objects.equals(video.getVideoDirection(), SevenmVideoDirectionEnums.LEFT) && Objects.nonNull(video.getLeftRightVideoId())) {
                        return videoIdMap.containsKey(video.getLeftRightVideoId());
                    }
                    return false;
                })
                .collect(Collectors.toList());
        workoutGenerateBO.setCentralAndLeftVideoList(centralAndLeftVideos);

        Map<SevenmSpecialLimitEnums, Set<ProjSevenmExerciseVideo>> videoLimitMap = Maps.newHashMap();
        workoutGenerateBO.setVideoLimitMap(videoLimitMap);
        Map<SevenmTargetEnums, Set<ProjSevenmExerciseVideo>> videoTargetMap = Maps.newHashMap();
        workoutGenerateBO.setVideoTargetMap(videoTargetMap);

        for (ProjSevenmExerciseVideo video : centralAndLeftVideos) {

            video.getSpecialLimit().forEach(specialLimitEnum -> {
                if (videoLimitMap.containsKey(specialLimitEnum)) {
                    videoLimitMap.get(specialLimitEnum).add(video);
                    return;
                }

                Set videoSet = Sets.newHashSet();
                videoSet.add(video);
                videoLimitMap.put(specialLimitEnum, videoSet);
            });

            video.getTarget().forEach(targetEnum -> {
                if (videoTargetMap.containsKey(targetEnum)) {
                    videoTargetMap.get(targetEnum).add(video);
                    return;
                }

                Set videoSet = Sets.newHashSet();
                videoSet.add(video);
                videoTargetMap.put(targetEnum, videoSet);
            });
        }

        return workoutGenerateBO;
    }

    @Data
    private static class M3u8GenerateContext {
        private List<ProjSevenmWorkoutGenerate> workoutsDetails;
        private Map<Integer,ProjSevenmWorkoutGenerate> workoutMap;
        private List<ProjSevenmWorkoutGenerateExerciseVideo> relationVideos;
        private Map<Integer, List<ProjSevenmWorkoutGenerateExerciseVideo>> relationMap;
        private Map<Integer, ProjSevenmExerciseVideo> videoMap;
        private Map<Integer, ProjSevenmTemplateExerciseGroup> groupMap;
        private List<GenerateSevenmWorkoutFileContextBO> fileContextBOs;
    }
}
