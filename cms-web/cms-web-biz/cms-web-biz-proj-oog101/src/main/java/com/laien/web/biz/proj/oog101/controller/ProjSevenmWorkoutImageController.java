package com.laien.web.biz.proj.oog101.controller;

import com.alibaba.excel.EasyExcel;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutImage;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageListReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmWorkoutImageService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * workout image 管理
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Api(tags = {"项目管理:Sevenm ProjSevenmWorkoutImage"})
@RestController
@RequestMapping("/proj/sevenmWorkoutImage")
@RequiredArgsConstructor
public class ProjSevenmWorkoutImageController {

    private final IProjSevenmWorkoutImageService service;

    @ApiOperation(value = "查询workout image列表")
    @GetMapping("/list")
    public ResponseResult<Map<String,Object>> list(ProjSevenmWorkoutImageListReq req){
        List<ProjSevenmWorkoutImageVO> page = service.list(req);
        Map<String, Object> data = new HashMap<>();
        data.put("list",page);
        return ResponseResult.succ(data);
    }

    @ApiOperation(value = "添加workout image")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSevenmWorkoutImageAddReq req){
        req.setProjId(RequestContextUtils.getProjectId());
        service.add(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "修改workout image")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmWorkoutImageUpdateReq req){
        // 排序涉及全局，此接口不支持修改排序
        req.setProjId(RequestContextUtils.getProjectId());
        service.updateImg(req);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "修改workout image排序")
    @PostMapping("/update_sort")
    public ResponseResult<Void> updateSort(@RequestBody IdListReq req ){
        service.updateSort(req.getIdList());
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量删除workout image")
    @PostMapping("/del")
    public ResponseResult<Void> del(@RequestBody IdListReq req){
        service.del(req.getIdList());
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量启用workout image")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq req){

        service.updateEnable(req.getIdList(),true);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量停用workout image")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq req){

        service.updateEnable(req.getIdList(),false);
        return ResponseResult.succ();
    }

    @ApiOperation(value = "批量导入workout image")
    @PostMapping("/importByExcel")
    public ResponseResult<List<String>> importExcel(@RequestParam("file") MultipartFile excel){
        Integer projectId = RequestContextUtils.getProjectId();
        return ResponseResult.succ(service.importExcel(projectId,excel));
    }

    @SneakyThrows
    @ApiOperation(value = "批量导出")
    @GetMapping("/exportWithExcel")
    public void exportWithExcel(HttpServletResponse response,ProjSevenmWorkoutImageListReq req) {

        // 查询数据
        List<ProjSevenmWorkoutImageExportVO> list = service.listExportVO(req);
        String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        String characterEncoding = "utf-8";
        String contentDispositionHeader = "Content-disposition";
        String contentDispositionHeaderValue = "attachment;filename*=utf-8''7MWorkoutImage.xlsx";
        String sheetName = "7MWorkoutImage";
        response.setContentType(contentType);
        response.setCharacterEncoding(characterEncoding);
        response.setHeader(contentDispositionHeader, contentDispositionHeaderValue);
        EasyExcel.write(response.getOutputStream(), ProjSevenmWorkoutImageExportVO.class).inMemory(Boolean.TRUE).sheet(sheetName).doWrite(list);
    }

    @ApiOperation(value = "workout image详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmWorkoutImageVO> detail(@PathVariable("id") Integer id){
        return ResponseResult.succ(service.detail(id));
    }

}
