package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmPlaylistRelation;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistRelationVO;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public interface IProjSevenmPlaylistRelationService extends IService<ProjSevenmPlaylistRelation> {

    void deleteByPlaylistId(List<Integer> playlistId);

    List<ProjSevenmPlaylistRelationVO> listByPlaylistId(Integer playlistId);

    List<ProjSevenmPlaylistRelation> listByPlaylistIds(List<Integer> playlistIds);
}
