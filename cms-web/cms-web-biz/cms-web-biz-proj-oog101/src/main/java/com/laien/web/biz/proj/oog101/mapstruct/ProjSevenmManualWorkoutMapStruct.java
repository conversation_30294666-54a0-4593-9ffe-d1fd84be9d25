package com.laien.web.biz.proj.oog101.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmManualWorkout;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutAddReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutDetailVideoVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutPageVO;
import org.mapstruct.Mapper;

/**
 * <p>
 *  7M Manual Workout MapStruct
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSevenmManualWorkoutMapStruct {

    ProjSevenmManualWorkout addReqToEntity(ProjSevenmManualWorkoutAddReq workoutReq);

    ProjSevenmManualWorkoutDetailVO entityToDetailVO(ProjSevenmManualWorkout workoutFind);

    ProjSevenmManualWorkoutDetailVideoVO entityToDetailVideoVO(ProjSevenmExerciseVideo video);

    ProjSevenmManualWorkoutPageVO entityToPageVO(ProjSevenmManualWorkout workout);
}
