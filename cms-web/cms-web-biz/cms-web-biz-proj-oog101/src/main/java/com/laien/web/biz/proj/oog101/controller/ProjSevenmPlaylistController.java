package com.laien.web.biz.proj.oog101.controller;

import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmPlaylist;
import com.laien.web.biz.proj.oog101.request.ProjSevenmPlaylistAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmPlaylistUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmPlaylistService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Api(tags = "项目管理:Sevenm Playlist")
@RestController
@RequestMapping("/proj/sevenmPlaylist")
public class ProjSevenmPlaylistController extends ResponseController {

    @Resource
    private IProjSevenmPlaylistService playlistService;

    @ApiOperation(value = "列表接口")
    @GetMapping("/list")
    public ResponseResult<List<ProjSevenmPlaylistPageVO>> list() {

        Integer projectId = RequestContextUtils.getProjectId();
        AssertUtil.notNull(projectId,"projId is null");
        List<ProjSevenmPlaylistPageVO> pageRes = playlistService.pageQuery(projectId);
        return succ(pageRes);
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSevenmPlaylistAddReq addReq) {
        Integer projectId = RequestContextUtils.getProjectId();
        AssertUtil.notNull(projectId,"projId is null");
        addReq.setProjId(projectId);
        playlistService.insert(addReq);
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmPlaylistUpdateReq updateReq) {

        playlistService.update(updateReq);
        return succ();
    }

    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {

        playlistService.sort(idListReq);
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmPlaylistDetailVO> detail(@PathVariable Integer id) {

        ProjSevenmPlaylistDetailVO detailVO = playlistService.getDetailById(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }

        playlistService.updateEnableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }

        playlistService.updateDisableByIds(idListReq.getIdList());
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {

        if (Objects.isNull(idListReq) || CollectionUtils.isEmpty(idListReq.getIdList())) {
            return fail("ID list cannot be empty");
        }

        playlistService.deleteByIds(idListReq.getIdList());
        return succ();
    }

}
