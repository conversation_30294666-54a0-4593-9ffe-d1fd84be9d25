package com.laien.web.biz.proj.oog101.bo;

import com.google.common.collect.Lists;
import com.laien.common.oog101.enums.SevenmDifficultyEnums;
import com.laien.common.oog101.enums.SevenmIntensityEnums;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@Data
public class DifficultyAndIntensityMappingBO {

    private List<SevenmDifficultyEnums> difficultyEnums;

    private List<SevenmIntensityEnums> intensityEnums;

    private float percent;

    public static List<DifficultyAndIntensityMappingBO> getMappingByDifficulty(SevenmDifficultyEnums difficultyEnums) {

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.NEWBIE)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.8f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.STRETCH,SevenmIntensityEnums.CARDIO));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.2f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_2.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.HIIT));

            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.BEGINNER)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.5f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.CARDIO));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.5f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_2.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.HIIT));

            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.INTERMEDIATE)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.5f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.INTERMEDIATE));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.25f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_2.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.HIIT));

            DifficultyAndIntensityMappingBO mappingBO_3 = new DifficultyAndIntensityMappingBO();
            mappingBO_3.setPercent(0.25f);
            mappingBO_3.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_3.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.CARDIO));

            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.ADVANCED)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setPercent(0.8f);
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.INTERMEDIATE));

            DifficultyAndIntensityMappingBO mappingBO_2 = new DifficultyAndIntensityMappingBO();
            mappingBO_2.setPercent(0.2f);
            mappingBO_2.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER, SevenmDifficultyEnums.ADVANCED));
            return Lists.newArrayList(mappingBO_1, mappingBO_2);
        }

        return Collections.emptyList();
    }



    public static List<DifficultyAndIntensityMappingBO> getNoneMappingByDifficuty(SevenmDifficultyEnums difficultyEnums) {

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.NEWBIE)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.STRETCH,SevenmIntensityEnums.CARDIO));
            return Lists.newArrayList(mappingBO_1);
        }

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.BEGINNER)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.CARDIO,SevenmIntensityEnums.HIIT));
            return Lists.newArrayList(mappingBO_1);
        }

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.INTERMEDIATE)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER, SevenmDifficultyEnums.INTERMEDIATE));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.CARDIO,SevenmIntensityEnums.HIIT));
            return Lists.newArrayList(mappingBO_1);
        }

        if (Objects.equals(difficultyEnums, SevenmDifficultyEnums.ADVANCED)) {

            DifficultyAndIntensityMappingBO mappingBO_1 = new DifficultyAndIntensityMappingBO();
            mappingBO_1.setDifficultyEnums(Lists.newArrayList(SevenmDifficultyEnums.BEGINNER, SevenmDifficultyEnums.INTERMEDIATE));
            mappingBO_1.setIntensityEnums(Lists.newArrayList(SevenmIntensityEnums.HIIT,SevenmIntensityEnums.POWER));
            return Lists.newArrayList(mappingBO_1);
        }
        return Collections.emptyList();
    }
}
