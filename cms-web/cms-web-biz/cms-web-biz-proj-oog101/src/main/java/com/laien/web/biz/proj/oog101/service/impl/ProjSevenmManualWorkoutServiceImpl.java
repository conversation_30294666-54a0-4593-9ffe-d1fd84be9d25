package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.common.oog101.enums.*;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog101.bo.*;
import com.laien.web.biz.proj.oog101.config.Oog101BizConfig;
import com.laien.web.biz.proj.oog101.config.SevenmSoundConfig;
import com.laien.web.biz.proj.oog101.config.SevenmSoundConfigWrapper;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmManualWorkout;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmManualWorkoutExerciseVideo;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmManualWorkoutI18n;
import com.laien.web.biz.proj.oog101.entity.i18n.ProjSevenmExerciseVideoI18n;
import com.laien.web.biz.proj.oog101.entity.i18n.ProjSevenmSoundI18n;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmManualWorkoutMapper;
import com.laien.web.biz.proj.oog101.mapstruct.ProjSevenmManualWorkoutMapStruct;
import com.laien.web.biz.proj.oog101.request.*;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutDetailVideoVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmManualWorkoutPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmExerciseVideoService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmManualWorkoutExerciseVideoService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmManualWorkoutI18nService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmManualWorkoutService;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.bo.TsMergeBO;
import com.laien.web.common.file.dto.UploadFileInfoRes;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import com.laien.web.frame.response.PageRes;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog101.enums.SevenmTemplateTypeEnums.SEVENM_TABATA_WORKOUT;
import static com.laien.web.frame.constant.GlobalConstant.SECOND_MILL;

/**
 * <p>
 * proj_sevenm_manual_workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjSevenmManualWorkoutServiceImpl extends ServiceImpl<ProjSevenmManualWorkoutMapper, ProjSevenmManualWorkout> implements IProjSevenmManualWorkoutService {

    private final ProjSevenmManualWorkoutMapStruct mapStruct;
    private final IProjSevenmManualWorkoutExerciseVideoService projSevenmManualWorkoutExerciseVideoService;
    private final IProjSevenmManualWorkoutI18nService i18nService;
    private final IProjSevenmExerciseVideoService projSevenmExerciseVideoService;
    private final IProjInfoService projInfoService;
    private final FileService fileService;
    private final Oog101BizConfig oog101BizConfig;
    private final ICoreTextTaskI18nService taskI18nService;
    private final ICoreSpeechTaskI18nPubService speechI18nPubService;
    private final IProjLmsI18nService projLmsI18nService;

    private static final String SEVENM_WORKOUT_M3U8_DIR_KEY = "project-seven-workout-m3u8";
    private static final String SEVENM_WORKOUT_JSON_DIR_KEY = "project-seven-workout-json";

    @Override
    public PageRes<ProjSevenmManualWorkoutPageVO> selectWorkoutPage(ProjSevenmManualWorkoutPageReq pageReq) {
        Integer id = pageReq.getId();
        String name = pageReq.getName();
        Integer status = pageReq.getStatus();
        Integer fileStatus = pageReq.getFileStatus();
        Boolean common = pageReq.getCommon();
        List<SevenmWorkoutCategoryEnums> category = pageReq.getCategory();
        Integer subscription = pageReq.getSubscription();
        Integer projId = pageReq.getProjId();
        if (projId == null) {
            projId = RequestContextUtils.getProjectId();
            pageReq.setProjId(projId);
        }

        // 获取新增的查询条件
        List<SevenmTargetEnums> target = pageReq.getTarget();
        SevenmDifficultyEnums difficulty = pageReq.getDifficulty();
        SevenmGenderEnums gender = pageReq.getGender();
        SevenmTemplateTypeEnums workoutType = pageReq.getWorkoutType();

        // 区间查询条件
        Integer minDuration = pageReq.getMinDuration();
        Integer maxDuration = pageReq.getMaxDuration();
        BigDecimal minCalorie = pageReq.getMinCalorie();
        BigDecimal maxCalorie = pageReq.getMaxCalorie();

        // 外部表的查询条件
        List<Integer> videoIdList = pageReq.getVideoIdList();
        Set<Integer> workoutIdSet = Collections.emptySet();
        if (CollUtil.isNotEmpty(videoIdList)) {
            LambdaQueryWrapper<ProjSevenmManualWorkoutExerciseVideo> videoWrapper = new LambdaQueryWrapper<>();
            videoWrapper.select(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmManualWorkoutId);
            videoWrapper.eq(ProjSevenmManualWorkoutExerciseVideo::getProjId, projId)
                    .in(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmExerciseVideoId, videoIdList);
            workoutIdSet = projSevenmManualWorkoutExerciseVideoService.list(videoWrapper).stream()
                    .map(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmManualWorkoutId).collect(Collectors.toSet());
            if (CollUtil.isEmpty(workoutIdSet)) {
                return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
            }
        }



        LambdaQueryWrapper<ProjSevenmManualWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(id), ProjSevenmManualWorkout::getId, id)
                .in(CollectionUtils.isNotEmpty(workoutIdSet), ProjSevenmManualWorkout::getId, workoutIdSet)
                .eq(ProjSevenmManualWorkout::getProjId, projId)
                .like(StringUtils.isNotBlank(name), ProjSevenmManualWorkout::getName, name)
                .eq(Objects.nonNull(status), ProjSevenmManualWorkout::getStatus, status)
                .eq(Objects.nonNull(fileStatus), ProjSevenmManualWorkout::getFileStatus, fileStatus)
                .eq(Objects.nonNull(subscription), ProjSevenmManualWorkout::getSubscription, subscription)
                .eq(ObjUtil.isNotNull(gender), ProjSevenmManualWorkout::getGender, gender)
                .eq(ObjUtil.isNotNull(common), ProjSevenmManualWorkout::isCommon, common)
                .eq(ObjUtil.isNotNull(workoutType), ProjSevenmManualWorkout::getWorkoutType, workoutType)
                .eq(Objects.nonNull(difficulty), ProjSevenmManualWorkout::getDifficulty, difficulty)
                .ge(Objects.nonNull(minDuration), ProjSevenmManualWorkout::getDuration, minDuration)
                .le(Objects.nonNull(maxDuration), ProjSevenmManualWorkout::getDuration, maxDuration)
                .ge(Objects.nonNull(minCalorie), ProjSevenmManualWorkout::getCalorie, minCalorie)
                .le(Objects.nonNull(maxCalorie), ProjSevenmManualWorkout::getCalorie, maxCalorie);

        // 特殊处理位运算类型枚举
        BitmaskEnumUtil.addBitmaskCondition(wrapper, ProjSevenmManualWorkout::getCategory, category, false);
        BitmaskEnumUtil.addBitmaskCondition(wrapper, ProjSevenmManualWorkout::getTarget, target, false);

        // HITT_ZONE 类型的workout，需要按照sort字段升序排列
        if (CollUtil.isNotEmpty(category) && category.size() == 1
                && ObjUtil.equal(category.get(0), SevenmWorkoutCategoryEnums.HITT_ZONE)) {
            wrapper.orderByAsc(ProjSevenmManualWorkout::getSort);
        }
        wrapper.orderByDesc(ProjSevenmManualWorkout::getId);
        IPage<ProjSevenmManualWorkout> page = this.page(new Page<>(pageReq.getPageNum(), pageReq.getPageSize()), wrapper);
        List<ProjSevenmManualWorkout> records = page.getRecords();
        if(CollUtil.isEmpty(records)){
            return new PageRes<>(pageReq.getPageNum(), pageReq.getPageSize(), 0L, 0L, Collections.emptyList());
        }

        // 各workout关联视频数量统计
        List<Integer> ids = records.stream().map(ProjSevenmManualWorkout::getId).collect(Collectors.toList());
        List<IdAndStatusCountsRes> countVideosByWorkout = this.baseMapper.countVideosByWorkoutIds(ids);
        Map<Integer, Integer> videoCountNumMap = CollectionUtils.isEmpty(countVideosByWorkout) ? new HashMap<>() :
                countVideosByWorkout.stream()
                        .collect(Collectors.groupingBy(IdAndStatusCountsRes::getId,
                                Collectors.summingInt(IdAndStatusCountsRes::getCounts)));
        Map<Integer, Integer> videoCountDisabledNumMap = CollectionUtils.isEmpty(countVideosByWorkout) ? new HashMap<>() :
                countVideosByWorkout.stream()
                        .filter(c -> Objects.nonNull(c.getStatus()) && c.getStatus().equals(GlobalConstant.STATUS_DISABLE))
                        .collect(Collectors.groupingBy(IdAndStatusCountsRes::getId,
                                Collectors.summingInt(IdAndStatusCountsRes::getCounts)));

        List<ProjSevenmManualWorkoutPageVO> list = records.stream().map(workout -> {
            ProjSevenmManualWorkoutPageVO pageVO = mapStruct.entityToPageVO(workout);
            pageVO.setVideoNum(videoCountNumMap.getOrDefault(workout.getId(), 0));
            pageVO.setVideoDisabledNum(videoCountDisabledNumMap.getOrDefault(workout.getId(), 0));
            return pageVO;
        }).collect(Collectors.toList());
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }


    @Override
    public void generateWorkoutFile(GenerateSevenmWorkoutFileContextBO contextBO) {
        List<String> languageList = contextBO.getLanguageList();

        //取系统音i18n
        assembleSoundBo(contextBO, languageList);

        //video上的音频（name、guidance）i18n
        Map<String, Map<Integer, SevenmVideoSoundI18nBO>> videoSoundI18nBOMap = contextBO.getVideoSoundI18nMap();
        if (CollUtil.isEmpty(videoSoundI18nBOMap)) {
            List<SevenmWorkoutWrapperBO> workoutList = contextBO.getWorkoutList();
            Set<Integer> videoIdSet = new HashSet<>();
            for (SevenmWorkoutWrapperBO workoutWrapperBO : workoutList) {
                for (BaseGenerateVideoBO videoBO : workoutWrapperBO.getVideoList()) {
                    videoIdSet.add(videoBO.getVideo().getId());
                }
            }
            List<ProjSevenmExerciseVideo> videoList = projSevenmExerciseVideoService.query(videoIdSet);
            Map<String, Map<Integer, SevenmVideoSoundI18nBO>> videoSoundI18nMap = listVideoI18n(languageList, videoList);
            contextBO.setVideoSoundI18nMap(videoSoundI18nMap);
        }

        calculate(contextBO);
        if(contextBO.getGenerateM3u8()){
            generateM3u8(contextBO);
        }
        if(contextBO.getGenerateAudioJson()){
            generateAudio(contextBO);
        }
        contextBO.waitUploadSuccess();
    }

    private void assembleSoundBo(GenerateSevenmWorkoutFileContextBO contextBO, List<String> languageList) {
        //取系统音i18n
        Map<String, SevenmSoundBO> femaleSoundBoMap = contextBO.getFemaleSoundBoMap();
        if (CollUtil.isEmpty(femaleSoundBoMap)) {
            contextBO.setFemaleSoundBoMap(getSoundI18n(languageList, SevenmGenderEnums.FEMALE));

        }
        Map<String, SevenmSoundBO> maleSoundBoMap = contextBO.getMaleSoundBoMap();
        if (CollUtil.isEmpty(maleSoundBoMap)) {
            contextBO.setMaleSoundBoMap(getSoundI18n(languageList, SevenmGenderEnums.MALE));
        }
        //取其他类型系统音
        Map<SevenmTemplateTypeEnums, Map<String, SevenmSoundBO>> typeFemaleSoundBoMap = contextBO.getTypeFemaleSoundBoMap();
        if (!typeFemaleSoundBoMap.containsKey(SEVENM_TABATA_WORKOUT)) {
            typeFemaleSoundBoMap.put(SEVENM_TABATA_WORKOUT, getSoundI18n(languageList, SevenmGenderEnums.FEMALE, SEVENM_TABATA_WORKOUT));
        }
        Map<SevenmTemplateTypeEnums, Map<String, SevenmSoundBO>> typeMaleSoundBoMap = contextBO.getTypeMaleSoundBoMap();
        if (!typeMaleSoundBoMap.containsKey(SEVENM_TABATA_WORKOUT)) {
            typeMaleSoundBoMap.put(SEVENM_TABATA_WORKOUT, getSoundI18n(languageList, SevenmGenderEnums.MALE, SEVENM_TABATA_WORKOUT));
        }

    }


    @Override
    public Map<String, SevenmSoundBO> getSoundI18n(List<String> languages, SevenmGenderEnums gender) {
        return getSoundI18n(languages, gender, null);
    }


    @Override
    public Map<String, SevenmSoundBO> getSoundI18n(List<String> languages, SevenmGenderEnums gender, SevenmTemplateTypeEnums workoutType) {
        Map<String, SevenmSoundBO> soundMap = new HashMap<>();
        SevenmSoundBO soundBO;
        if (ObjUtil.equal(SEVENM_TABATA_WORKOUT, workoutType)) {
            soundBO = getTabataEnSound(gender);
        } else {
            soundBO = getEnSound(gender);
        }
        soundMap.put(GlobalConstant.DEFAULT_LANGUAGE, soundBO);
        if(CollUtil.isEmpty(languages)){
            return soundMap;
        }
        List<AudioJson101BO> soundList;
        if (ObjUtil.equal(SEVENM_TABATA_WORKOUT, workoutType)) {
            soundList = new ArrayList<>(Arrays.asList( soundBO.getFiveFourThreeTwoOne(), soundBO.getGo(), soundBO.getBeeBeeBee()));
            soundList.addAll(soundBO.getPromptList());
        } else {
            soundList = new ArrayList<>(Arrays.asList(soundBO.getFirst(), soundBO.getNext(), soundBO.getLast(), soundBO.getThreeTwoOne(), soundBO.getGo(), soundBO.getBeeBeeBee()));
            soundList.addAll(soundBO.getPromptList());
        }
        // 不处理英语
        Set<String> languageList = languages.stream()
                .filter(e -> ObjUtil.notEqual(GlobalConstant.DEFAULT_LANGUAGE, e))
                .collect(Collectors.toSet());
        if (languageList.isEmpty()) {
            return soundMap;
        }
        //组装翻译任务
        List<ProjSevenmSoundI18n> i18nModelList = new ArrayList<>();
        for (AudioJson101BO json101BO : soundList) {
            if (json101BO.getNeedTranslation()) {
                ProjSevenmSoundI18n i18n = new ProjSevenmSoundI18n(json101BO);
                i18nModelList.add(i18n);
            }
        }
        Map<Object, Map<LanguageEnums,AudioTranslateResultModel>> soundI18nMap = speechI18nPubService.
                getI18nResultGroupByKey(i18nModelList, languageList, ProjCodeEnums.OOG101);
        for (AudioJson101BO audioJson101BO : soundList) {
            Integer soundId = audioJson101BO.getSoundId();
            Boolean needTranslation = audioJson101BO.getNeedTranslation();
            Map<LanguageEnums, AudioTranslateResultModel> soundLangMap = soundI18nMap.get(soundId);
            if (needTranslation && soundLangMap == null) {
                throw new BizException("The System sound " + audioJson101BO.getId() + " translation language incomplete");
            }
            for (String language : languageList) {
                getEnSound(audioJson101BO, soundI18nMap, language, soundMap, audioJson101BO.getCategory());
            }
        }
        return soundMap;
    }


    @Override
    public void generateM3u8(GenerateSevenmWorkoutFileContextBO contextBO) {
        List<SevenmWorkoutWrapperBO> workoutList = contextBO.getWorkoutList();
        for (SevenmWorkoutWrapperBO workout : workoutList) {
            WorkoutFileUploadInfoBO uploadInfoBO = workout.getUploadInfoBO();
            if (null == uploadInfoBO) {
                throw new BizException("uploadInfoBO not can be null");
            }
            List<TsMergeBO> tsTextMergeBO = new ArrayList<>(workoutList.size() * 4);
            for (BaseGenerateVideoBO videoBO : workout.getVideoList()) {
                ProjSevenmExerciseVideo video = videoBO.getVideo();
                TsMergeBO frontTsBO = new TsMergeBO(fileService.getAbsoluteR2Url(video.getFrontVideoUrl()), video.getFrontVideoDuration());
                TsMergeBO sideTsBO = new TsMergeBO(fileService.getAbsoluteR2Url(video.getSideVideoUrl()), video.getSideVideoDuration());
                if (ObjUtil.equal(SEVENM_TABATA_WORKOUT, workout.getWorkoutType())) {
                    tsTextMergeBO.add(frontTsBO);
                    tsTextMergeBO.add(frontTsBO);
                    tsTextMergeBO.add(frontTsBO);
                } else {
                    tsTextMergeBO.add(frontTsBO);
                    tsTextMergeBO.add(frontTsBO);
                    tsTextMergeBO.add(sideTsBO);
                    tsTextMergeBO.add(frontTsBO);
                }
            }
            contextBO.submit(() -> {
                String videoUrl = fileService.uploadMergeTSForM3U8R2WithDiscontinuity(tsTextMergeBO, SEVENM_WORKOUT_M3U8_DIR_KEY).getFileRelativeUrl();
                uploadInfoBO.setVideoUrl(videoUrl);
            });

        }
    }


    @Override
    public void generateAudio(GenerateSevenmWorkoutFileContextBO contextBO) {
        List<SevenmWorkoutWrapperBO> workoutList = contextBO.getWorkoutList();

        for (SevenmWorkoutWrapperBO workout : workoutList) {
            SevenmGenderEnums gender = workout.getGender();
            WorkoutFileUploadInfoBO uploadInfo = workout.getUploadInfoBO();
            if (null == uploadInfo) {
                throw new BizException("uploadInfo not can be null");
            }
            List<BaseGenerateVideoBO> videoList = workout.getVideoList();
            int promptIndex = NumberUtil.round(videoList.size()/2.0, 0,RoundingMode.UP).intValue();
            promptIndex--;
            if (CollectionUtils.isEmpty(videoList)) {
                throw new BizException("generateAudio videoList is empty");
            }

            Map<String, List<AudioJson101BO>> audioListMap = new HashMap<>();
            for (String language : contextBO.getLanguageList()) {
                List<AudioJson101BO> audioJsonList = new ArrayList<>();
                audioListMap.put(language, audioJsonList);
                if (ObjUtil.equal(SEVENM_TABATA_WORKOUT, workout.getWorkoutType())) {
                    assembleTabataAudioJson(contextBO, language, gender, videoList, audioJsonList, promptIndex);
                } else {
                    assembleNormalAudioJson(contextBO, language, gender, videoList, audioJsonList, promptIndex);
                }
            }
            contextBO.submit(() -> {
                uploadAudioJson(audioListMap, workout);
            });
        }
    }

    private void assembleNormalAudioJson(GenerateSevenmWorkoutFileContextBO contextBO, String language,
                                         SevenmGenderEnums gender, List<BaseGenerateVideoBO> videoList,
                                         List<AudioJson101BO> audioJsonList, int promptIndex) {
        SevenmSoundBO sevenmSoundBO = contextBO.getFemaleSoundBoMap().get(language);
        if (SevenmGenderEnums.MALE == gender) {
            sevenmSoundBO = contextBO.getMaleSoundBoMap().get(language);
        }
        if (null == sevenmSoundBO) {
            throw new BizException("generateAudio find by language, sevenmSoundBO is null");
        }
        int duration = 0;
        AudioJson101BO prompt = null;
        for (int i = 0; i < videoList.size(); i++) {
            BaseGenerateVideoBO videoBO = videoList.get(i);
            ProjSevenmExerciseVideo video = videoBO.getVideo();
            Integer previewDuration = videoBO.getPreviewDuration();
            Integer videoDuration = videoBO.getVideoDuration();
            AudioJson101BO videoFirstSound;
            if (0 == i) {
                videoFirstSound = sevenmSoundBO.getFirst();
            } else if (i == videoList.size() - 1) {
                videoFirstSound = sevenmSoundBO.getLast();
            } else {
                videoFirstSound = sevenmSoundBO.getNext();
            }
            addAudioJson(audioJsonList, videoFirstSound, duration + 100, gender);
            Integer videoId = video.getId();
            SevenmVideoSoundI18nBO videoSoundI18n = contextBO.getVideoSoundI18nMap().get(language).get(videoId);
            AudioJson101BO name = videoSoundI18n.nameAudioJson101BO();
            addAudioJson(audioJsonList, name, duration + videoFirstSound.getDuration() + 1000, gender);
            AudioJson101BO threeTwoOne = sevenmSoundBO.getThreeTwoOne();
            addAudioJson(audioJsonList, threeTwoOne, duration + previewDuration - threeTwoOne.getDuration(), gender);

            AudioJson101BO go = sevenmSoundBO.getGo();
            addAudioJson(audioJsonList, go, duration + previewDuration, gender);
            AudioJson101BO guidance = videoSoundI18n.guidanceAudioJson101BO();
            addAudioJson(audioJsonList, guidance, duration + previewDuration + go.getDuration() + 1000, gender);
            List<AudioJson101BO> promptList = sevenmSoundBO.getPromptList();
            List<AudioJson101BO> promptCopyList = new ArrayList<>(promptList);
            if (null != prompt && promptCopyList.size() > 1) {
                promptCopyList.remove(prompt);
            }
            Collections.shuffle(promptCopyList);
            prompt = promptCopyList.get(0);
            int promptTime = duration + previewDuration + (videoDuration - prompt.getDuration())/2;
            if(i == promptIndex){
                addAudioJson(audioJsonList, prompt, promptTime, gender);
            }
            AudioJson101BO beeBeeBee = sevenmSoundBO.getBeeBeeBee();
            addAudioJson(audioJsonList, beeBeeBee, duration + previewDuration + videoDuration - beeBeeBee.getDuration(), gender);
            duration = duration + previewDuration + videoDuration;
        }
    }

    private void assembleTabataAudioJson(GenerateSevenmWorkoutFileContextBO contextBO, String language,
                                         SevenmGenderEnums gender, List<BaseGenerateVideoBO> videoList,
                                         List<AudioJson101BO> audioJsonList, int promptIndex) {
        SevenmSoundBO sevenmSoundBO = contextBO.getTypeFemaleSoundBoMap().get(SEVENM_TABATA_WORKOUT).get(language);
        if (SevenmGenderEnums.MALE == gender) {
            sevenmSoundBO = contextBO.getTypeMaleSoundBoMap().get(SEVENM_TABATA_WORKOUT).get(language);
        }
        int duration = 0;
        AudioJson101BO prompt = null;
        for (int i = 0; i < videoList.size(); i++) {
            BaseGenerateVideoBO videoBO = videoList.get(i);
            ProjSevenmExerciseVideo video = videoBO.getVideo();
            Integer previewDuration = videoBO.getPreviewDuration();
            Integer videoDuration = videoBO.getVideoDuration();
            AudioJson101BO fiveFourThreeTwoOne = sevenmSoundBO.getFiveFourThreeTwoOne();
            addAudioJson(audioJsonList, fiveFourThreeTwoOne, duration + previewDuration - fiveFourThreeTwoOne.getDuration(), gender);
            AudioJson101BO go = sevenmSoundBO.getGo();
            addAudioJson(audioJsonList, go, duration + previewDuration, gender);
            List<AudioJson101BO> promptList = sevenmSoundBO.getPromptList();
            List<AudioJson101BO> promptCopyList = new ArrayList<>(promptList);
            if (null != prompt && promptCopyList.size() > 1) {
                promptCopyList.remove(prompt);
            }
            Collections.shuffle(promptCopyList);
            prompt = promptCopyList.get(0);
            int promptTime = duration + previewDuration + (videoDuration - prompt.getDuration())/2;
            if(i == promptIndex){
                addAudioJson(audioJsonList, prompt, promptTime, gender);
            }
            AudioJson101BO beeBeeBee = sevenmSoundBO.getBeeBeeBee();
            addAudioJson(audioJsonList, beeBeeBee, duration + previewDuration + videoDuration - beeBeeBee.getDuration(), gender);
            duration = duration + previewDuration + videoDuration;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWorkout(ProjSevenmManualWorkoutAddReq workoutReq) {
        // 校验
        Integer projId = workoutReq.getProjId();
        if (projId == null) {
            projId = RequestContextUtils.getProjectId();
            workoutReq.setProjId(projId);
        }
        this.check(workoutReq, null, projId);
        ProjSevenmManualWorkout workout = mapStruct.addReqToEntity(workoutReq);
        workout.setStatus(GlobalConstant.STATUS_DRAFT);
        workout.setProjId(projId);
        workout.setEventName("");
        this.save(workout);
        // 保存新的video关系
        List<ProjSevenmManualWorkoutExerciseVideo> relationVideos = this.saveRelation(workout.getId(), projId, workoutReq.getVideoList());
        //获取id，生成eventName
        Integer id = workout.getId();
        workout.setEventName(id+"+"+workout.getName());
        Map<ProjSevenmManualWorkout, Pair<List<ProjSevenmManualWorkoutExerciseVideo>, List<ProjSevenmManualWorkoutI18n>>> resultMap
                = MapUtil.of(workout, Pair.of(relationVideos, new ArrayList<>()));
        // 处理音视频
        Set<String> languages = this.getLanguages(projId, null);
        processM3u8Info(resultMap,projId, languages);
        // 保存结果
        this.updateM3u8InfoBatch(resultMap, projId, languages);
        projLmsI18nService.handleI18n(ListUtil.of(workout), projInfoService.getById(projId));
    }



    private void processM3u8Info(Map<ProjSevenmManualWorkout, Pair<List<ProjSevenmManualWorkoutExerciseVideo>,List<ProjSevenmManualWorkoutI18n>>> map,
                                 Integer projId, Set<String> languages) {
        this.processM3u8Info(map, projId, languages, true, true);
    }
    private void processM3u8Info(Map<ProjSevenmManualWorkout, Pair<List<ProjSevenmManualWorkoutExerciseVideo>,List<ProjSevenmManualWorkoutI18n>>> map,
                                 Integer projId, Set<String> languages,boolean videoFlag,boolean audioFlag) {
        Set<Integer> videoIdSet = map.values().stream().flatMap(v -> v.getLeft().stream()).map(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmExerciseVideoId).collect(Collectors.toSet());
        List<ProjSevenmExerciseVideo> videoList = projSevenmExerciseVideoService.query(videoIdSet);
        Map<Integer, ProjSevenmExerciseVideo> videoMap = videoList.stream().collect(Collectors.toMap(BaseModel::getId, item -> item));

        List<SevenmWorkoutWrapperBO> workoutWrapperList = new ArrayList<>();
        map.forEach((workout,pair) -> {
            List<ProjSevenmManualWorkoutExerciseVideo> relationVideos = pair.getLeft();
            //video拉平
            List<BaseGenerateVideoBO> baseGenerateVideoList = new ArrayList<>();
            Map<Integer, BaseGenerateVideoBO> baseGenerateVideoMap = new HashMap<>();
            for (ProjSevenmManualWorkoutExerciseVideo relationVideo : relationVideos) {
                Integer videoId = relationVideo.getProjSevenmExerciseVideoId();
                BaseGenerateVideoBO baseVideoBO = baseGenerateVideoMap.get(videoId);
                if(null != baseVideoBO){
                    for (int i = 0; i < relationVideo.getExerciseCircuit(); i++) {
                        baseGenerateVideoList.add(baseVideoBO);
                    }
                    continue;
                }
                BaseGenerateVideoBO videoBO = new BaseGenerateVideoBO();
                ProjSevenmExerciseVideo video = videoMap.get(videoId);
                videoBO.setVideo(video);
                for (int i = 0; i < relationVideo.getExerciseCircuit(); i++) {
                    baseGenerateVideoList.add(videoBO);
                }
                baseGenerateVideoMap.put(videoId, videoBO);
            }
            SevenmWorkoutWrapperBO workoutWrapper = new SevenmWorkoutWrapperBO();
            workoutWrapper.setVideoList(baseGenerateVideoList);
            workoutWrapper.setGender(workout.getGender());
            workoutWrapper.setWorkoutUniqueKey(workout.getId());
            workoutWrapper.setWorkoutType(workout.getWorkoutType());
            workoutWrapperList.add(workoutWrapper);
        });
        GenerateSevenmWorkoutFileContextBO contextBO = new GenerateSevenmWorkoutFileContextBO(workoutWrapperList, new ArrayList<>(languages));
        contextBO.setGenerateAudioJson(audioFlag);
        contextBO.setGenerateM3u8(videoFlag);
        generateWorkoutFile(contextBO);

        //将workoutList转为key，value，key为uniquekey
        Map<Object, SevenmWorkoutWrapperBO> resultMap = contextBO.getWorkoutList().stream().collect(Collectors.toMap(item -> item.getWorkoutUniqueKey(), item -> item));
        map.forEach((workout,pair) -> {
            SevenmWorkoutWrapperBO workoutWrapper = resultMap.get(workout.getId());
            WorkoutFileUploadInfoBO uploadInfoBO = workoutWrapper.getUploadInfoBO();
            if (null != uploadInfoBO) {
                workout.setVideoUrl(uploadInfoBO.getVideoUrl());
                workout.setDuration(uploadInfoBO.getDuration());
                workout.setCalorie(uploadInfoBO.getCalorie());
                workout.setFileStatus(GlobalConstant.ZERO);//返回了结果默认就是1-成功
                workout.setAudioLanguages(CollUtil.join(languages, GlobalConstant.COMMA));
            }

            List<BaseGenerateVideoBO> generateVideoBOS = workoutWrapper.getVideoList();
            //update relationDuration
            List<ProjSevenmManualWorkoutExerciseVideo> relationVideos = pair.getLeft();
            Map<Integer, List<BaseGenerateVideoBO>> relationDurationMap = generateVideoBOS.stream().collect(
                    Collectors.groupingBy(item -> item.getVideo().getId()));
            for (ProjSevenmManualWorkoutExerciseVideo relation : relationVideos) {
                BaseGenerateVideoBO bo = relationDurationMap.get(relation.getProjSevenmExerciseVideoId()).get(0);
                if(Objects.nonNull(bo)){
                    relation.setVideoDuration(bo.getVideoDuration());
                    relation.setPreviewDuration(bo.getPreviewDuration());
                }
            }

            Map<String, String> i18nMap = MapUtil.defaultIfEmpty(uploadInfoBO.getAudioI18nUrl(), new HashMap<>());
            List<ProjSevenmManualWorkoutI18n> i18nWorkoutList = i18nMap.entrySet().stream().map(entry -> {
                ProjSevenmManualWorkoutI18n workoutI18n = new ProjSevenmManualWorkoutI18n();
                workoutI18n.setProjSevenmManualWorkoutId(workout.getId());
                workoutI18n.setLanguage(entry.getKey());
                workoutI18n.setAudioJsonUrl(entry.getValue());
                workoutI18n.setProjId(projId);
                return  workoutI18n;
            }).collect(Collectors.toList());
            pair.getRight().addAll(i18nWorkoutList);
        });
    }

    private Set<String> getLanguages(Integer projId, List<String> selectLanguages) {
        Set<String> languages = new HashSet<>();
        if (CollUtil.isEmpty(selectLanguages)) {
            languages.add(GlobalConstant.DEFAULT_LANGUAGE);
            ProjInfo projInfo = projInfoService.getById(projId);
            languages.addAll(StrUtil.split(projInfo.getLanguages(), GlobalConstant.COMMA, true, true));
        } else {
            languages.addAll(selectLanguages);
        }
        return languages;
    }

    private void updateM3u8InfoBatch(Map<ProjSevenmManualWorkout, Pair<List<ProjSevenmManualWorkoutExerciseVideo>,List<ProjSevenmManualWorkoutI18n>>> map
                                     , Integer projId, Set<String> selectLanguages) {
        if(MapUtil.isEmpty(map)) return;
        map.forEach((workout, pair) -> {
            this.updateById(workout);
            List<ProjSevenmManualWorkoutExerciseVideo> relationVideos = pair.getLeft();
            projSevenmManualWorkoutExerciseVideoService.updateBatchById(relationVideos);
            List<ProjSevenmManualWorkoutI18n> i18nWorkoutList = pair.getRight();
            //delete old i18nData
            LambdaUpdateWrapper<ProjSevenmManualWorkoutI18n> deleteWrapper = new LambdaUpdateWrapper<>();
            deleteWrapper.eq(ProjSevenmManualWorkoutI18n::getProjSevenmManualWorkoutId, workout.getId());
            deleteWrapper.eq(ProjSevenmManualWorkoutI18n::getProjId, projId);
            deleteWrapper.eq(ProjSevenmManualWorkoutI18n::getDelFlag, 0);
            deleteWrapper.in(CollUtil.isNotEmpty(selectLanguages), ProjSevenmManualWorkoutI18n::getLanguage, selectLanguages);
            i18nService.remove(deleteWrapper);
            if (CollUtil.isNotEmpty(i18nWorkoutList)) i18nService.saveBatch(i18nWorkoutList);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWorkout(ProjSevenmManualWorkoutUpdateReq workoutReq) {
        Integer id = workoutReq.getId();
        ProjSevenmManualWorkout workoutFind = this.getById(id);
        if (Objects.isNull(workoutFind)) {
            throw new BizException("Data not found");
        }
        Integer projId = workoutFind.getProjId();
        // 校验
        this.check(workoutReq, id, projId);
        ProjSevenmManualWorkout workout = mapStruct.addReqToEntity(workoutReq);
        workout.setId(id);
        // 删除video关系
        this.deleteRelation(id);
        // 保存新的video关系
        List<ProjSevenmManualWorkoutExerciseVideo> relationVideos = this.saveRelation(id, projId, workoutReq.getVideoList());
        // 处理音视频
        Map<ProjSevenmManualWorkout, Pair<List<ProjSevenmManualWorkoutExerciseVideo>, List<ProjSevenmManualWorkoutI18n>>> resultMap
                = MapUtil.of(workout, Pair.of(relationVideos, new ArrayList<>()));
        Set<String> languages = this.getLanguages(projId, null);
        processM3u8Info(resultMap,projId, languages);
        // 保存结果
        this.updateM3u8InfoBatch(resultMap, projId, languages);
        projLmsI18nService.handleI18n(ListUtil.of(workout), projInfoService.getById(projId));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean generateM3u8(ProjSevenmManualWorkoutGenerateM3u8Req m3u8Req) {
        //query workouts
        List<ProjSevenmManualWorkout> workouts = this.list(new LambdaQueryWrapper<ProjSevenmManualWorkout>()
                .in(ProjSevenmManualWorkout::getId, m3u8Req.getWorkoutIds()));
        if(CollectionUtils.isEmpty(workouts)){
            return null;
        }
        //query relation
        List<Integer> workoutIds = workouts.stream().map(ProjSevenmManualWorkout::getId).collect(Collectors.toList());
        List<ProjSevenmManualWorkoutExerciseVideo> relationVideos = projSevenmManualWorkoutExerciseVideoService.list(new LambdaQueryWrapper<ProjSevenmManualWorkoutExerciseVideo>()
               .in(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmManualWorkoutId, workoutIds));
        Map<Integer, List<ProjSevenmManualWorkoutExerciseVideo>> relationMap = relationVideos.stream()
                .collect(Collectors.groupingBy(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmManualWorkoutId));
        Map<ProjSevenmManualWorkout, Pair<List<ProjSevenmManualWorkoutExerciseVideo>, List<ProjSevenmManualWorkoutI18n>>> resultMap = new HashMap<>();
        workouts.forEach(workout -> {
            List<ProjSevenmManualWorkoutExerciseVideo> videos = relationMap.get(workout.getId());
            resultMap.put(workout, Pair.of(videos, new ArrayList<>()));
        });
        // 处理音视频
        Integer projId = m3u8Req.getProjId();
        Set<String> languages = this.getLanguages(projId, m3u8Req.getLanguages());
        processM3u8Info(resultMap,projId, languages, m3u8Req.getVideoFlag(),m3u8Req.getAudioFlag());
        // 保存结果
        this.updateM3u8InfoBatch(resultMap, projId, languages);
        return true;
    }

    /**
     * 校验
     *
     * @param workoutReq workoutReq
     * @param id id
     * @param projId 项目id
     */
    private void check(ProjSevenmManualWorkoutAddReq workoutReq, Integer id, Integer projId) {
        String name = workoutReq.getName();
        LambdaQueryWrapper<ProjSevenmManualWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmManualWorkout::getName, name)
                .ne(Objects.nonNull(id), ProjSevenmManualWorkout::getId, id)
                .eq(ProjSevenmManualWorkout::getProjId, projId);
        int count = this.count(wrapper);
        if (count > 0) {
            throw new BizException("name already exists");
        }
    }

    /**
     * 保存workout 关系
     *
     * @param id        id
     * @param projId    projId
     * @param videoList videoList
     * @return
     */
    private List<ProjSevenmManualWorkoutExerciseVideo> saveRelation(Integer id, Integer projId, List<ProjSevenmManualWorkoutAddVideoReq> videoList) {
        List<ProjSevenmManualWorkoutExerciseVideo> sevenmVideoList = new ArrayList<>();
        for (ProjSevenmManualWorkoutAddVideoReq videoReq : videoList) {
            ProjSevenmManualWorkoutExerciseVideo sevenmVideo = new ProjSevenmManualWorkoutExerciseVideo();
            sevenmVideo.setProjId(projId);
            sevenmVideo.setProjSevenmManualWorkoutId(id);
            sevenmVideo.setProjSevenmExerciseVideoId(videoReq.getId());
            sevenmVideo.setExerciseCircuit(videoReq.getExerciseCircuit());
            sevenmVideo.setUnitName(videoReq.getUnitName());
            sevenmVideoList.add(sevenmVideo);
        }
        projSevenmManualWorkoutExerciseVideoService.saveBatch(sevenmVideoList);
        return sevenmVideoList;
    }

    /**
     * 删除workout 关系
     *
     * @param id id
     */
    private void deleteRelation(Integer id) {
        projSevenmManualWorkoutExerciseVideoService.update(new ProjSevenmManualWorkoutExerciseVideo(),
                new LambdaUpdateWrapper<ProjSevenmManualWorkoutExerciseVideo>()
                        .set(ProjSevenmManualWorkoutExerciseVideo::getDelFlag, GlobalConstant.YES)
                        .eq(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmManualWorkoutId, id)
        );
    }

    @Override
    public ProjSevenmManualWorkoutDetailVO getWorkoutDetail(Integer id) {
        ProjSevenmManualWorkout workoutFind = this.getById(id);
        if (Objects.isNull(workoutFind)) {
            throw new BizException("Data not found");
        }
        ProjSevenmManualWorkoutDetailVO detailVO = mapStruct.entityToDetailVO(workoutFind);

        List<ProjSevenmManualWorkoutDetailVideoVO> videoList = this.selectVideoList(id);
        detailVO.setVideoList(videoList);
        return detailVO;
    }

    /**
     * 获取workout video list
     *
     * @param id id
     * @return list
     */
    private List<ProjSevenmManualWorkoutDetailVideoVO> selectVideoList(Integer id) {
        List<ProjSevenmManualWorkoutExerciseVideo> sevenmWorkoutProjSevenmVideoList =
                projSevenmManualWorkoutExerciseVideoService.list(new LambdaQueryWrapper<ProjSevenmManualWorkoutExerciseVideo>()
                .eq(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmManualWorkoutId, id));

        List<Integer> videoIds = sevenmWorkoutProjSevenmVideoList.stream()
                .map(ProjSevenmManualWorkoutExerciseVideo::getProjSevenmExerciseVideoId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, ProjSevenmExerciseVideo> sevenmVideoListMap = projSevenmExerciseVideoService.listByIds(videoIds).stream()
                .collect(Collectors.toMap(ProjSevenmExerciseVideo::getId, t -> t));

        List<ProjSevenmManualWorkoutDetailVideoVO> videoList = new ArrayList<>();
        for (ProjSevenmManualWorkoutExerciseVideo sevenmWorkoutProjSevenmVideo : sevenmWorkoutProjSevenmVideoList) {
            ProjSevenmExerciseVideo video = sevenmVideoListMap.get(sevenmWorkoutProjSevenmVideo.getProjSevenmExerciseVideoId());
            if (video != null) {
                ProjSevenmManualWorkoutDetailVideoVO videoVO = mapStruct.entityToDetailVideoVO(video);
                videoVO.setExerciseCircuit(sevenmWorkoutProjSevenmVideo.getExerciseCircuit());
                videoVO.setUnitName(sevenmWorkoutProjSevenmVideo.getUnitName());
                videoList.add(videoVO );
            }
        }
        return videoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        this.update(new ProjSevenmManualWorkout(), new LambdaUpdateWrapper<ProjSevenmManualWorkout>()
                .set(ProjSevenmManualWorkout::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmManualWorkout::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjSevenmManualWorkout::getId, idList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        this.update(new ProjSevenmManualWorkout(), new LambdaUpdateWrapper<ProjSevenmManualWorkout>()
                .set(ProjSevenmManualWorkout::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjSevenmManualWorkout::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmManualWorkout::getId, idList));
    }


    @Override
    public Map<String, Map<Integer, SevenmVideoSoundI18nBO>> listVideoI18n(List<String> languages,
                                                                           List<ProjSevenmExerciseVideo> videoList) {

        // ---------- 预备数据 ----------
        // (1) 过滤语言，保留非空且非默认
        Set<String> translateLangStr = Optional.ofNullable(languages)
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(StringUtils::isNotBlank)
                .filter(l -> !GlobalConstant.DEFAULT_LANGUAGE.equals(l))
                .collect(Collectors.toSet());

        // (2) 生成基础 Map，默认语言先放进去
        Map<String, Map<Integer, SevenmVideoSoundI18nBO>> result = new HashMap<>(translateLangStr.size() + 1);
        Map<Integer, SevenmVideoSoundI18nBO> defaultLangMap = result.computeIfAbsent(
                GlobalConstant.DEFAULT_LANGUAGE, k -> new HashMap<>(videoList.size())
        );

        // ---------- Step 1：先校验翻译完整性 ----------
        // 把 video → i18n 结果一次拉取
        List<ProjSevenmExerciseVideoI18n> i18nModelList = videoList.stream()
                .map(ProjSevenmExerciseVideoI18n::new)
                .collect(Collectors.toList());

        Map<Object, ProjSevenmExerciseVideoI18n> i18nResult =
                speechI18nPubService.getI18nModelGroupByKey(i18nModelList, languages,ProjCodeEnums.OOG101);

        List<String> missingIdList  = new ArrayList<>();
        List<String> incompleteList = new ArrayList<>();

        for (ProjSevenmExerciseVideo v : videoList) {
            ProjSevenmExerciseVideoI18n i18n = i18nResult.get(v.getId());
            if (i18n == null) {
                missingIdList.add(String.valueOf(v.getId()));
                continue;
            }

            // 检查每种目标语言，是否 name/guidance 都完整
            for (String lang : translateLangStr) {
                LanguageEnums le = LanguageEnums.getByNameIgnoreCase(lang);
                boolean ok = i18n.getResult()!=null && i18n.getResult().stream().anyMatch(r -> le == r.getLanguage() && StringUtils.isNotBlank(r.getText()))
                        && i18n.getResult()!=null && i18n.getGuidanceResult().stream().anyMatch(r -> le == r.getLanguage() && StringUtils.isNotBlank(r.getText()));
                if (!ok) {
                    incompleteList.add(String.format("id=%s, lang=%s", v.getId(), lang));
                }
            }
        }

        if (!missingIdList.isEmpty() || !incompleteList.isEmpty()) {
            StringBuilder sb = new StringBuilder(128);
            if (!missingIdList.isEmpty()) {
                sb.append("missing:").append(missingIdList);
            }
            if (!incompleteList.isEmpty()) {
                if (sb.length() > 0) sb.append("; ");
                sb.append("incomplete:").append(incompleteList);
            }
            throw new BizException("Sevenm exercise video translation incomplete: " + sb);
        }

        // ---------- Step 2：组装默认语言 ----------
        videoList.forEach(v -> {
            SevenmVideoSoundI18nBO bo = new SevenmVideoSoundI18nBO()
                    .setId(v.getId())
                    .setName(v.getName())
                    .setNameUrl(v.getNameAudioUrl())
                    .setDuration(v.getNameAudioDuration())
                    .setGuidance(v.getGuidance())
                    .setGuidanceUrl(v.getGuidanceAudioUrl())
                    .setGuidanceDuration(v.getGuidanceAudioDuration())
                    .setGender(v.getGender());
            defaultLangMap.put(v.getId(), bo);
        });

        // ---------- Step 3：组装翻译语言 ----------
        for (ProjSevenmExerciseVideo v : videoList) {
            ProjSevenmExerciseVideoI18n i18n = i18nResult.get(v.getId());

            Map<LanguageEnums, AudioTranslateResultModel> nameMap = i18n.getResult()==null ? MapUtil.empty() :
                    i18n.getResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));
            Map<LanguageEnums, AudioTranslateResultModel> guidanceMap = i18n.getGuidanceResult()==null ? MapUtil.empty() :
                    i18n.getGuidanceResult().stream().collect(Collectors.toMap(AudioTranslateResultModel::getLanguage, r -> r));

            for (String lang : translateLangStr) {
                LanguageEnums le = LanguageEnums.getByNameIgnoreCase(lang);

                Map<Integer, SevenmVideoSoundI18nBO> langMap = result.computeIfAbsent(lang, k -> new HashMap<>(videoList.size()));

                SevenmVideoSoundI18nBO bo = new SevenmVideoSoundI18nBO()
                        .setId(v.getId())
                        .setName(nameMap.get(le).getText())
                        .setNameUrl(nameMap.get(le).getAudioUrl())
                        .setDuration(nameMap.get(le).getDuration())
                        .setGuidance(guidanceMap.get(le).getText())
                        .setGuidanceUrl(guidanceMap.get(le).getAudioUrl())
                        .setGuidanceDuration(guidanceMap.get(le).getDuration())
                        .setGender(v.getGender());

                langMap.put(v.getId(), bo);
            }
        }
        return result;
    }

    private void getEnSound(AudioJson101BO audioJson101BO, Map<Object, Map<LanguageEnums, AudioTranslateResultModel>> soundI18nMap,
                            String language, Map<String, SevenmSoundBO> soundMap, AudioCategoryEnums audioCategory) {
        Boolean needTranslation = audioJson101BO.getNeedTranslation();
        Integer soundId = audioJson101BO.getSoundId();
        Map<LanguageEnums, AudioTranslateResultModel> resVideo101Map = soundI18nMap.get(soundId);
        AudioJson101BO json101BO;
        if (needTranslation) {
            LanguageEnums languageEnums = LanguageEnums.getByNameIgnoreCase(language);
            AudioTranslateResultModel soundI18n = resVideo101Map.get(languageEnums);
            if (soundI18n == null) {
                throw new BizException("The System sound " + audioJson101BO.getId() + " translation language incomplete");
            }
            String fileName = FireBaseUrlSubUtils.getFileName(soundI18n.getAudioUrl());
            json101BO = new AudioJson101BO(
                    fileName,
                    fileService.getAbsoluteR2Url(soundI18n.getAudioUrl()),
                    fileName,
                    soundI18n.getText(),
                    soundI18n.getDuration(),
                    false,
                    audioJson101BO.getGender(),
                    soundId,
                    true,
                    audioJson101BO.getCoreVoiceConfigI18nId(),
                    null);
        } else {
            json101BO = audioJson101BO;
        }
        json101BO.setCategory(audioCategory);
        SevenmSoundBO soundI18nBO = soundMap.getOrDefault(language, new SevenmSoundBO());
        soundMap.put(language, soundI18nBO);
        switch (audioCategory) {
            case FIRST:
                soundI18nBO.setFirst(json101BO);
                break;
            case NEXT:
                soundI18nBO.setNext(json101BO);
                break;
            case LAST:
                soundI18nBO.setLast(json101BO);
                break;
            case THREE_TWO_ONE:
                soundI18nBO.setThreeTwoOne(json101BO);
                break;
            case GO:
                soundI18nBO.setGo(json101BO);
                break;
            case BEE_BEE_BEE:
                soundI18nBO.setBeeBeeBee(json101BO);
                break;
            case PROMPT:
                soundI18nBO.getPromptList().add(json101BO);
                break;
            case FIVE_FOUR_THREE_TWO_ONE:
                soundI18nBO.setFiveFourThreeTwoOne(json101BO);
                break;
        }
    }

    private SevenmSoundBO getEnSound(SevenmGenderEnums gender){
        SevenmSoundConfigWrapper soundConfigWrapper = oog101BizConfig.get106FitnessSoundConfig();
        SevenmSoundConfig soundConfig = soundConfigWrapper.getFemaleSoundConfig();
        if (gender == SevenmGenderEnums.MALE) {
            soundConfig = soundConfigWrapper.getMaleSoundConfig();
        }
        AudioJson101BO firstAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.FIRST, soundConfig.getFirst(), gender);
        AudioJson101BO nextAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.NEXT, soundConfig.getNext(), gender);
        AudioJson101BO lastAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.LAST, soundConfig.getLast(), gender);
        AudioJson101BO threeTwoOneAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.THREE_TWO_ONE, soundConfig.getThreeTwoOne(), gender);
        AudioJson101BO goAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.GO, soundConfig.getGo(), gender);
        AudioJson101BO beeBeeBeeAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.BEE_BEE_BEE, soundConfig.getBeeBeeBee(), gender);
        List<AudioJson101BO> promptAudioList = AudioJson101BO.getAudioList(soundConfig.getPromptList(),AudioCategoryEnums.PROMPT, gender);
        return new SevenmSoundBO(firstAudio, nextAudio,lastAudio,threeTwoOneAudio,null,goAudio,beeBeeBeeAudio,promptAudioList);
    }


    private SevenmSoundBO getTabataEnSound(SevenmGenderEnums gender){
        SevenmSoundConfigWrapper soundConfigWrapper = oog101BizConfig.getTabataSoundConfig();
        SevenmSoundConfig soundConfig = soundConfigWrapper.getFemaleSoundConfig();
        if (gender == SevenmGenderEnums.MALE) {
            soundConfig = soundConfigWrapper.getMaleSoundConfig();
        }
        AudioJson101BO fiveFourThreeTwoOneAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.FIVE_FOUR_THREE_TWO_ONE, soundConfig.getFiveFourThreeTwoOne(), gender);
        AudioJson101BO goAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.GO, soundConfig.getGo(), gender);
        AudioJson101BO beeBeeBeeAudio = AudioJson101BO.getSoundByName(AudioCategoryEnums.BEE_BEE_BEE, soundConfig.getBeeBeeBee(), gender);
        List<AudioJson101BO> promptAudioList = AudioJson101BO.getAudioList(soundConfig.getPromptList(),AudioCategoryEnums.PROMPT, gender);
        SevenmSoundBO tabataSoundBo = new SevenmSoundBO();
        tabataSoundBo.setFiveFourThreeTwoOne(fiveFourThreeTwoOneAudio);
        tabataSoundBo.setGo(goAudio);
        tabataSoundBo.setBeeBeeBee(beeBeeBeeAudio);
        tabataSoundBo.setPromptList(promptAudioList);
        return tabataSoundBo;
    }

    /**
     * 计算video的preview duration和video duration
     * 计算workout的duration和calorie
     */
    private void calculate(GenerateSevenmWorkoutFileContextBO context){
        List<SevenmWorkoutWrapperBO> workoutList = context.getWorkoutList();
        for (SevenmWorkoutWrapperBO workout : workoutList) {
            List<BaseGenerateVideoBO> videoList = workout.getVideoList();
            if(CollectionUtils.isEmpty(videoList)) {
                throw new BizException("calculate videoList is empty");
            }
            BigDecimal calorie = new BigDecimal(0);
            int duration = 0;
            for (BaseGenerateVideoBO videoBO : videoList) {
                ProjSevenmExerciseVideo video = videoBO.getVideo();
                Integer frontDuration = video.getFrontVideoDuration();
                Integer sideDuration = video.getSideVideoDuration();
                int videoDuration;
                if (ObjUtil.equal(SEVENM_TABATA_WORKOUT, workout.getWorkoutType())) {
                    videoDuration = frontDuration * 2;
                } else {
                    videoDuration = frontDuration * 2 + sideDuration;
                }
                duration = duration + frontDuration + videoDuration;
                calorie = calorie.add(video.getCalorie());
                videoBO.setPreviewDuration(frontDuration)
                        .setVideoDuration(videoDuration);
            }
            WorkoutFileUploadInfoBO uploadInfoBO = new WorkoutFileUploadInfoBO();
            uploadInfoBO.setCalorie(calorie)
                    .setDuration(duration);
            workout.setUploadInfoBO(uploadInfoBO);
        }
    }



    private WorkoutFileUploadInfoBO uploadAudioJson(Map<String, List<AudioJson101BO>> audioListMap, SevenmWorkoutWrapperBO workout) {
        WorkoutFileUploadInfoBO uploadInfoBO = workout.getUploadInfoBO();
        try {
            // 保存英语以外的语种
            Map<String, String> audioI18nUrl = new HashMap<>();
            for (String language : audioListMap.keySet()) {
                List<AudioJson101BO> audioList = audioListMap.get(language);
                // 上传音频json
                UploadFileInfoRes audioJsonR2Info = fileService.uploadJsonR2(
                        JacksonUtil.toJsonString(audioList), SEVENM_WORKOUT_JSON_DIR_KEY);
                audioI18nUrl.put(language, audioJsonR2Info.getFileRelativeUrl());
            }
            uploadInfoBO.setAudioI18nUrl(audioI18nUrl);
        } catch (Exception e) {
            log.error("oog101 upload failed", e);
            throw new BizException("oog101 upload failed: " + e.getMessage());
        }
        return uploadInfoBO;
    }

    private void addAudioJson(List<AudioJson101BO> audioList, AudioJson101BO audio, int playtime, SevenmGenderEnums gender) {
        BigDecimal time = new BigDecimal(playtime).divide(new BigDecimal(SECOND_MILL), GlobalConstant.ONE, RoundingMode.HALF_UP);
        AudioJson101BO newAudio = audio.createByGender(gender);
        newAudio.setTime(time);
        audioList.add(newAudio);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        for (Integer id : idList) {
            boolean flag = this.update(new ProjSevenmManualWorkout(), new LambdaUpdateWrapper<ProjSevenmManualWorkout>()
                    .set(ProjSevenmManualWorkout::getDelFlag, GlobalConstant.YES)
                    .eq(ProjSevenmManualWorkout::getStatus, GlobalConstant.STATUS_DRAFT)
                    .eq(ProjSevenmManualWorkout::getId, id));
            // 删除关系
            if (flag) {
                this.deleteRelation(id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        List<ProjSevenmManualWorkout> entities = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjSevenmManualWorkout entity = new ProjSevenmManualWorkout();
            entity.setSort(i).setId(idList.get(i));
            entities.add(entity);
        }
        updateBatchById(entities);
    }

}
