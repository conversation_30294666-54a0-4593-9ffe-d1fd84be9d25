package com.laien.web.biz.proj.oog101.bo;

import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.frame.constant.GlobalConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class BaseGenerateVideoBO {

    @ApiModelProperty(value = "单视频正侧循环次数")
    private Integer videoRound;

    @ApiModelProperty(value = "preview 循环次数")
    private Integer previewRound = GlobalConstant.ONE;

    @ApiModelProperty(value = "所属规则组")
    private Integer groupId;

    @ApiModelProperty(value = "当前使用视频")
    private ProjSevenmExerciseVideo video;

    @ApiModelProperty(value = "preview_duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "video_duration")
    private Integer videoDuration;

    public BaseGenerateVideoBO(Integer videoRound, Integer groupId, ProjSevenmExerciseVideo video) {
        this.videoRound = videoRound;
        this.groupId = groupId;
        this.video = video;
    }
}
