package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjSevenmMusicUpdateReq对象")
public class ProjSevenmMusicUpdateReq extends ProjSevenmMusicAddReq {

    private Integer id;

}
