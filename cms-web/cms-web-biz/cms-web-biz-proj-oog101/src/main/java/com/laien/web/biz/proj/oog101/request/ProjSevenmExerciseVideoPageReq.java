package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.*;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 7M Exercise video 分页
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "7M Exercise video 分页", description = "7M Exercise video 分页")
public class ProjSevenmExerciseVideoPageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "ids")
    private List<Integer> ids;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "exerciseType")
    private SevenmExerciseTypeEnums exerciseType;

    @ApiModelProperty(value = "类型 WARM_UP-Warm up MAIN-Main COOL_DOWN-Cool down")
    private SevenmTypeEnums type;

    @ApiModelProperty(value = "intensity STRETCH-Stretch CARDIO-Cardio HIIT-Hiit POWER-Power")
    private SevenmIntensityEnums intensity;

    @ApiModelProperty(value = "难度 BEGINNER-Beginner INTERMEDIATE-Intermediate ADVANCED-Advanced")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "部位 HEAD-Head BODY-Body LEGS-Legs")
    private SevenmPositionEnums position;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制列表 多选 NONE-None WRIST-Wrist FOOT-Foot BACK-Back SHOULDER-Shooter ABS-Abs")
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "目标 多选 ARM-Arm BACK-Back ABS-Abs LEGS-Legs BUTT-Butt")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "器械 DUMBBELLS-Dumbbells NONE-None")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "videoDirection CENTRAL-Central LEFT-Left RIGHT-Right")
    private SevenmVideoDirectionEnums videoDirection;

    @ApiModelProperty(value = "是否参与自动生成 1-是 0-否")
    private Integer usedForAuto;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
