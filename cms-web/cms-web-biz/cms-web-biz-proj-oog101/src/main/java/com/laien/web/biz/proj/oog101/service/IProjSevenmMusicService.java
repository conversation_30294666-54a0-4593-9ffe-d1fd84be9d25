package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmMusic;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmMusicUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmMusicDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmMusicPageVO;
import com.laien.web.frame.response.PageRes;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
public interface IProjSevenmMusicService extends IService<ProjSevenmMusic> {

    PageRes<ProjSevenmMusicPageVO> pageQuery(ProjSevenmMusicPageReq musicPageReq);

    void insert(ProjSevenmMusicAddReq musicAddReq);

    void update(ProjSevenmMusicUpdateReq musicUpdateReq);

    ProjSevenmMusicDetailVO getDetailById(Integer musicId);

}
