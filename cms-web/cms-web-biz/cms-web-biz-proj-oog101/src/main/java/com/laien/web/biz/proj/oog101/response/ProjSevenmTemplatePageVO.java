package com.laien.web.biz.proj.oog101.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.laien.common.oog101.enums.SevenmDifficultyEnums;
import com.laien.common.oog101.enums.SevenmSpecialLimitEnums;
import com.laien.common.oog101.enums.SevenmTemplateTaskStatusEnum;
import com.laien.common.oog101.enums.SevenmTemplateTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 模板 分页
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "模板 分页", description = "模板 分页")
public class ProjSevenmTemplatePageVO {


    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("template版本")
    private Integer dataVersion;

    /**
     * 类型：1 Regular Workout；
     */
    @ApiModelProperty
    private SevenmTemplateTypeEnums templateType;


    /**
     * 生成多少天的天数
     */
    @ApiModelProperty("生成多少天的天数")
    private Integer days;

    /**
     * 难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；
     */
    @ApiModelProperty
    private SevenmDifficultyEnums level;

    /**
     * 特殊限制：1 Back；2 Knee；3 None；
     */
    @ApiModelProperty
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty("状态：0草稿，1启用；2禁用；")
    private Integer status;

    @ApiModelProperty("workout数量")
    private Integer workoutNum;

    @ApiModelProperty("最近一个任务的id")
    private Integer taskId;

    @ApiModelProperty("最近一个任务的状态")
    private SevenmTemplateTaskStatusEnum taskStatus;
}
