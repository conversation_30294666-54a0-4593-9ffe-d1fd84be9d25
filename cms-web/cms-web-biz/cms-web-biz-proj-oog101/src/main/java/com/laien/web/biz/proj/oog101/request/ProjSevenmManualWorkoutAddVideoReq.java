package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.SevenmTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 7M Manual Workout Add Video Request
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
public class ProjSevenmManualWorkoutAddVideoReq {

    /**
     * Video ID
     */
    @ApiModelProperty(value = "Video ID")
    private Integer id;

    /**
     * Exercise Circuit
     */
    @ApiModelProperty(value = "Exercise Circuit")
    private Integer exerciseCircuit;

    /**
     * Unit Name
     */
    @ApiModelProperty(value = "Unit Name")
    private SevenmTypeEnums unitName;
}
