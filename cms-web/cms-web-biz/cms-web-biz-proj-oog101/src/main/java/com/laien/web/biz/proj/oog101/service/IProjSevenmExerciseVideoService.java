package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.frame.response.PageRes;

import java.io.InputStream;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_7m_exercise_video 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface IProjSevenmExerciseVideoService extends IService<ProjSevenmExerciseVideo> {


    List<ProjSevenmExerciseVideo> listEnable4AudoGenerate();

    /**
     * ExerciseVideo分页
     *
     * @param pageReq pageReq
     * @return PageRes
     */
    PageRes<ProjSevenmExerciseVideoPageVO> selectVideoPage(ProjSevenmExerciseVideoPageReq pageReq);

    /**
     * 根据ids查询ExerciseVideo列表
     * @param ids
     * @return
     */
    List<ProjSevenmExerciseVideoPageVO> listVOByIds(List<Integer> ids);

    /**
     * ExerciseVideo导出(忽略分页)
     * @param pageReq pageReq
     * @return List
     */
    List<ProjSevenmExerciseVideoExportVO> exportVideos(ProjSevenmExerciseVideoPageReq pageReq);

    /**
     * video新增
     *
     * @param videoReq videoReq
     */
    void saveVideo(ProjSevenmExerciseVideoAddReq videoReq);

    /**
     * video修改
     *
     * @param videoReq videoReq
     */
    void updateVideo(ProjSevenmExerciseVideoUpdateReq videoReq);

    /**
     * video详情
     *
     * @param id id
     * @return Proj7MVideoDetailVO
     */
    ProjSevenmExerciseVideoDetailVO getVideoDetail(Integer id);

    /**
     * video启用
     *
     * @param idList idList
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * video禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * video删除
     *
     * @param idList idList
     */
    void deleteByIds(List<Integer> idList);

    /**
     * 导入Excel
     * @param excelInputStream
     * @return
     */
    List<String> importByExcel(InputStream excelInputStream, Integer projectId);


    List<ProjSevenmExerciseVideo> query(Set<Integer> idSet);
}
