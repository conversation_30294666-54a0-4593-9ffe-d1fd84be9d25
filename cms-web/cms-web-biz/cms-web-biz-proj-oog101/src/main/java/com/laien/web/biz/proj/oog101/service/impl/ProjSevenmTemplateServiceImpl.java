package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.core.enums.ProjAppRequestUriStatusEnums;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.oog101.enums.*;
import com.laien.web.biz.proj.core.entity.ProjAppRequestUri;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplate;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplateExerciseGroup;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplateTask;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmTemplateExerciseGroupMapper;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmTemplateMapper;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmTemplateTaskMapper;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmWorkoutGenerateMapper;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplateGenerateReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplateGenerateWorkoutReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplatePageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplateUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplateDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplateExerciseGroupDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplatePageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmTemplateService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmTemplateTaskService;
import com.laien.web.biz.proj.oog101.service.IProjSevenmWorkoutGenerateService;
import com.laien.web.frame.async.service.IAsyncService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.response.IdAndCountsRes;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSevenmTemplateServiceImpl extends ServiceImpl<ProjSevenmTemplateMapper, ProjSevenmTemplate>
        implements IProjSevenmTemplateService {

    private final ProjSevenmTemplateTaskMapper projSevenmTemplateTaskMapper;
    private final ProjSevenmTemplateExerciseGroupMapper projSevenmTemplateExerciseGroupMapper;
    private final IAsyncService asyncService;
    private final IProjSevenmTemplateTaskService projSevenmTemplateTaskService;
    private final IProjInfoService projInfoService;
    private final ProjSevenmWorkoutGenerateMapper projSevenmWorkoutGenerateMapper;
    @Lazy
    @Autowired
    private IProjSevenmWorkoutGenerateService workoutGenerateService;

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer generate(ProjSevenmTemplateGenerateReq req) {
        List<List<SevenmSpecialLimitEnums>> allSubsets = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getSpecialLimitList())) {
            allSubsets = SevenmSpecialLimitEnums.getAllCombination(req.getSpecialLimitList());
        } else {
            allSubsets.add(null);
        }
        //获取最大版本号
        Integer maxDataVersion = this.getMaxDataVersion(req.getProjId());

        List<ProjSevenmTemplate> generateTemplates = Lists.newArrayList();
        // 取排列组合
        for (SevenmDifficultyEnums level : req.getLevelList()) {
            for (List<SevenmSpecialLimitEnums> specialLimitList : allSubsets) {
                // 使用参数创建一个ProjSevenmTemplate对象
                ProjSevenmTemplate projSevenmTemplate = new ProjSevenmTemplate()
                        .setProjId(req.getProjId())
                        .setTemplateType(req.getTemplateType())
                        .setLevel(level)
                        .setSpecialLimit(specialLimitList)
                        .setDays(req.getDays())
                        .setStatus(GlobalConstant.STATUS_DRAFT)
                        .setDataVersion(maxDataVersion+1);
                projSevenmTemplate.setName(generateTemplateName(projSevenmTemplate));
                generateTemplates.add(projSevenmTemplate);
            }
        }
        //因为增加了版本号，不存在重复的情况，注释掉验重逻辑
        /*// 数据库查重复，维度一致的模板不重新创建
        LambdaQueryWrapper<ProjSevenmTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmTemplate::getProjId, req.getProjId());
        queryWrapper.eq(ProjSevenmTemplate::getTemplateType, req.getTemplateType());
        queryWrapper.in(ProjSevenmTemplate::getLevel, req.getLevelList());

        List<ProjSevenmTemplate> maybeTemplates = this.list(queryWrapper);
        // 过滤掉维度一致的
        List<ProjSevenmTemplate> forInsert = generateTemplates.stream()
                .filter(template -> maybeTemplates.stream().noneMatch(maybeTemplate -> isDuplicateTemplate(template, maybeTemplate))).collect(Collectors.toList());*/
        Lists.partition(generateTemplates, 1000).forEach(this.baseMapper::insertBatchSomeColumn);
        // 为模板生成动作组
        List<ProjSevenmTemplateExerciseGroup> allGroup = generateTemplates.stream().flatMap(template -> createExerciseGroup(req, template)).collect(Collectors.toList());
        Lists.partition(allGroup, 1000).forEach(this.projSevenmTemplateExerciseGroupMapper::insertBatchSomeColumn);
        return generateTemplates.size();
    }

    private Integer getMaxDataVersion(Integer projId) {
        QueryWrapper<ProjSevenmTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("ifnull(max(data_version), 0) maxDataVersion");
        queryWrapper.eq("proj_id", projId);
        return this.getObj(queryWrapper,o -> Integer.parseInt(o.toString()));
    }

    private static Stream<ProjSevenmTemplateExerciseGroup> createExerciseGroup(ProjSevenmTemplateGenerateReq req, ProjSevenmTemplate template) {
        return template.getTemplateType().getGroupConfigs().stream()
                .map(groupConfig -> {
                    ProjSevenmTemplateExerciseGroup group = new ProjSevenmTemplateExerciseGroup();
                    group.setProjId(req.getProjId());
                    group.setProjSevenmTemplateId(template.getId());
                    group.setGroupType(groupConfig.getType());
                    group.setGroupName(groupConfig.getType().getName());
                    group.setCount(groupConfig.getCount());
                    group.setRounds(groupConfig.getRound());
                    return group;
                });
    }

    private static boolean isDuplicateTemplate(ProjSevenmTemplate template, ProjSevenmTemplate maybeTemplate) {

        boolean sameSpecialLimits = BitmaskEnumUtil.sumBitmaskEnumList(template.getSpecialLimit())
                == BitmaskEnumUtil.sumBitmaskEnumList(maybeTemplate.getSpecialLimit()) ;

        return template.getProjId().equals(maybeTemplate.getProjId())
                && template.getTemplateType().equals(maybeTemplate.getTemplateType())
                && template.getLevel().equals(maybeTemplate.getLevel())
                && sameSpecialLimits;
    }

    private String generateTemplateName(ProjSevenmTemplate fitnessTemplate) {

        return fitnessTemplate.getTemplateType().getName() + GlobalConstant.ENGLISH_DASH +
                fitnessTemplate.getLevel().getName() +
                Optional.ofNullable(fitnessTemplate.getSpecialLimit()).filter(CollUtil::isNotEmpty)
                        .map(list -> GlobalConstant.ENGLISH_DASH + list.stream().map(SevenmSpecialLimitEnums::getName).collect(Collectors.joining(GlobalConstant.COMMA))).orElse("");
    }

    @Override
    public PageRes<ProjSevenmTemplatePageVO> pageQuery(ProjSevenmTemplatePageReq req) {

        Page<ProjSevenmTemplate> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<ProjSevenmTemplate> pageResult = this.baseMapper.pageWithTask(page,req);
        if (page.getRecords().isEmpty()) {
            return PageConverter.convert(page, ProjSevenmTemplatePageVO.class);
        }
        // workout 数量查询
        List<Integer> ids = pageResult.getRecords().stream().map(BaseModel::getId).collect(Collectors.toList());
        Map<Integer, Integer> workoutNumMap = projSevenmWorkoutGenerateMapper.countTemplateWorkoutNum(ids).stream().
                collect(Collectors.toMap(IdAndCountsRes::getId, IdAndCountsRes::getCounts, (v1, v2) -> v1));
        return PageConverter.convert(page, ProjSevenmTemplatePageVO.class, (template, templatePageVO) -> {
            templatePageVO.setWorkoutNum(workoutNumMap.getOrDefault(template.getId(), 0));
        });
    }

    @Override
    public ProjSevenmTemplateDetailVO detail(Integer id) {
        ProjSevenmTemplate projSevenmTemplate = this.baseMapper.selectById(id);
        AssertUtil.notNull(projSevenmTemplate, "This template is not exist!");
        // 动作组
        LambdaQueryWrapper<ProjSevenmTemplateExerciseGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmTemplateExerciseGroup::getProjSevenmTemplateId, id);
        queryWrapper.eq(BaseModel::getDelFlag, GlobalConstant.NO);
        Map<SevenmTypeEnums, List<ProjSevenmTemplateExerciseGroup>> groupMap = projSevenmTemplateExerciseGroupMapper.selectList(queryWrapper).stream().collect(Collectors.groupingBy(ProjSevenmTemplateExerciseGroup::getGroupType));

        ProjSevenmTemplateDetailVO detailVO = BeanUtil.toBean(projSevenmTemplate, ProjSevenmTemplateDetailVO.class);
        if (groupMap.containsKey(SevenmTypeEnums.WARM_UP)) {
            detailVO.setWarmupExerciseGroup(BeanUtil.toBean(groupMap.get(SevenmTypeEnums.WARM_UP).get(0), ProjSevenmTemplateExerciseGroupDetailVO.class));
        }
        if (groupMap.containsKey(SevenmTypeEnums.MAIN)) {
            detailVO.setMainExerciseGroup(BeanUtil.toBean(groupMap.get(SevenmTypeEnums.MAIN).get(0), ProjSevenmTemplateExerciseGroupDetailVO.class));
        }
        if (groupMap.containsKey(SevenmTypeEnums.COOL_DOWN)) {
            detailVO.setCoolDownExerciseGroup(BeanUtil.toBean(groupMap.get(SevenmTypeEnums.COOL_DOWN).get(0), ProjSevenmTemplateExerciseGroupDetailVO.class));
        }
        return detailVO;
    }

    @Override
    public void generateWorkout(ProjSevenmTemplateGenerateWorkoutReq req) throws Exception {
        // 检验任务状态
        LambdaQueryWrapper<ProjSevenmTemplateTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjSevenmTemplateTask::getProjSevenmTemplateId, req.getTemplateIds());
        queryWrapper.in(ProjSevenmTemplateTask::getStatus, Lists.newArrayList(SevenmTemplateTaskStatusEnum.PENDING, SevenmTemplateTaskStatusEnum.RUNNING));
        queryWrapper.eq(BaseModel::getDelFlag, GlobalConstant.NO);
        AssertUtil.isFalse(projSevenmTemplateTaskMapper.selectCount(queryWrapper) > 0, "There are tasks that have not yet been completed.");

        List<ProjSevenmTemplateTask> templateTasks = req.getTemplateIds().stream().map(templateId ->
                new ProjSevenmTemplateTask()
                        .setProjSevenmTemplateId(templateId)
                        .setProjId(req.getProjId())
                        .setStatus(SevenmTemplateTaskStatusEnum.PENDING)
                        .setCleanUp(req.getCleanUp())).collect(Collectors.toList());
        projSevenmTemplateTaskService.saveBatch(templateTasks);

        ProjInfo projInfo = projInfoService.getById(req.getProjId());
        asyncService.doSomethings(() -> workoutGenerateService.generateWorkoutByTask(templateTasks, projInfo));
    }

    @Override
    public void updateEnable(List<Integer> req, Boolean isEnable) {

        LambdaUpdateWrapper<ProjSevenmTemplate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BaseModel::getId, req);
        updateWrapper.set(ProjSevenmTemplate::getStatus, isEnable ? GlobalConstant.STATUS_ENABLE : GlobalConstant.STATUS_DISABLE);
        this.update(updateWrapper);

    }

    @Override
    public void delete(List<Integer> idList) {

        this.baseMapper.selectBatchIds(idList).forEach(template -> {
            AssertUtil.notNull(template, "This template is not exist!");
            // 只有草稿状态的模板可删除
            AssertUtil.isTrue(template.getStatus() == GlobalConstant.STATUS_DRAFT, "Deleting this template is not allowed!");
        });

        this.baseMapper.deleteBatchIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(ProjSevenmTemplateUpdateReq req) {
        ProjSevenmTemplate template = this.getById(req.getId());
        AssertUtil.notNull(template, "This template is not exist!");

        // 模板基础信息修改
        template.setName(req.getName());
        template.setTemplateType(req.getTemplateType());
        template.setDays(req.getDays());
        template.setLevel(req.getLevel());
        template.setSpecialLimit(req.getSpecialLimit());

        this.updateById(template);
        // 动作组修改，先删除旧数据，再新增新数据
        LambdaQueryWrapper<ProjSevenmTemplateExerciseGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmTemplateExerciseGroup::getProjSevenmTemplateId, req.getId());
        projSevenmTemplateExerciseGroupMapper.delete(queryWrapper);
        // 动作组添加
        projSevenmTemplateExerciseGroupMapper.insertBatchSomeColumn(covertExerciseGroup(req));
    }

    private List<ProjSevenmTemplateExerciseGroup> covertExerciseGroup(ProjSevenmTemplateUpdateReq req) {

        List<ProjSevenmTemplateExerciseGroup> groups = Lists.newArrayList();

        ProjSevenmTemplateExerciseGroupDetailVO warmupExerciseGroup = req.getWarmupExerciseGroup();
        if (warmupExerciseGroup != null) {
            ProjSevenmTemplateExerciseGroup warmup = BeanUtil.toBean(warmupExerciseGroup, ProjSevenmTemplateExerciseGroup.class);
            warmup.setGroupType(SevenmTypeEnums.WARM_UP);
            warmup.setProjId(req.getProjId());
            warmup.setProjSevenmTemplateId(req.getId());
            groups.add(warmup);
        }

        ProjSevenmTemplateExerciseGroupDetailVO mainExerciseGroup = req.getMainExerciseGroup();
        if (mainExerciseGroup != null) {
            ProjSevenmTemplateExerciseGroup main = BeanUtil.toBean(mainExerciseGroup, ProjSevenmTemplateExerciseGroup.class);
            main.setGroupType(SevenmTypeEnums.MAIN);
            main.setProjId(req.getProjId());
            main.setProjSevenmTemplateId(req.getId());
            groups.add(main);
        }

        ProjSevenmTemplateExerciseGroupDetailVO coolDownExerciseGroup = req.getCoolDownExerciseGroup();
        if (coolDownExerciseGroup != null) {
            ProjSevenmTemplateExerciseGroup coolDown = BeanUtil.toBean(coolDownExerciseGroup, ProjSevenmTemplateExerciseGroup.class);
            coolDown.setGroupType(SevenmTypeEnums.COOL_DOWN);
            coolDown.setProjId(req.getProjId());
            coolDown.setProjSevenmTemplateId(req.getId());
            groups.add(coolDown);
        }

        return groups;
    }

}




