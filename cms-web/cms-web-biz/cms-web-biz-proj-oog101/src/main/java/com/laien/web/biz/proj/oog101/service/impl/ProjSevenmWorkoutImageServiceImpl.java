package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutImage;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmWorkoutImageMapper;
import com.laien.web.biz.proj.oog101.mapstruct.ProjSevenmWorkoutImageMapStruct;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageImportReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageListReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmWorkoutImageService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Validator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * proj_sevenm_workout_image 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Service
@RequiredArgsConstructor
public class ProjSevenmWorkoutImageServiceImpl extends ServiceImpl<ProjSevenmWorkoutImageMapper, ProjSevenmWorkoutImage>
        implements IProjSevenmWorkoutImageService {

    private final Validator validator;
    private final ProjSevenmWorkoutImageMapStruct mapStruct;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(ProjSevenmWorkoutImageAddReq req) {

        LambdaQueryWrapper<ProjSevenmWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ProjSevenmWorkoutImage::getTarget, req.getTarget(),true );
        queryWrapper.eq(ProjSevenmWorkoutImage::getName, req.getName());

        // 不能存在target和name相同的记录
        AssertUtil.isTrue(this.count(queryWrapper) == 0, "name and target cannot be duplicated.");
        // 数据转换
        ProjSevenmWorkoutImage workoutImage = mapStruct.toEntity(req);
        workoutImage.setStatus(GlobalConstant.STATUS_DRAFT);
        // 新增数据要排到最前面，先保存，再全量更新排序
        workoutImage.setSortNo(1);
        // 数据库中其他数据的排序字段全部加1，然后把新增数据的排序字段设置为1
        LambdaUpdateWrapper<ProjSevenmWorkoutImage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.setSql("sort_no = sort_no + 1");
        updateWrapper.isNotNull(ProjSevenmWorkoutImage::getSortNo);
        this.update(updateWrapper);
        this.save(workoutImage);
    }

    @Override
    public void updateImg(ProjSevenmWorkoutImageUpdateReq req) {
        ProjSevenmWorkoutImage workoutImage = this.getById(req.getId());
        AssertUtil.notNull(workoutImage, "workoutImage not exist");

        LambdaQueryWrapper<ProjSevenmWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ProjSevenmWorkoutImage::getTarget, req.getTarget(),true );
        queryWrapper.eq(ProjSevenmWorkoutImage::getName, req.getName());
        queryWrapper.ne(ProjSevenmWorkoutImage::getId, req.getId());
        // 不能存在target和name相同的记录
        AssertUtil.isTrue(this.count(queryWrapper) == 0, "name and target cannot be duplicated.");
        // 数据转换
        ProjSevenmWorkoutImage entity = mapStruct.toEntity(req);
        entity.setId(req.getId());
        this.updateById(entity);
    }

    @Override
    public void del(List<Integer> ids) {

        this.baseMapper.selectBatchIds(ids).forEach(workoutImage -> {
            AssertUtil.notNull(workoutImage, "workoutImage not exist");
            AssertUtil.isFalse(
                    !Objects.equals(workoutImage.getStatus(), GlobalConstant.STATUS_DRAFT),
                    "workoutImage status is enable");
        });
        this.removeByIds(ids);
    }

    @Override
    public void updateEnable(List<Integer> idList, boolean isEnable) {

        LambdaUpdateWrapper<ProjSevenmWorkoutImage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjSevenmWorkoutImage::getId, idList);
        updateWrapper.set(ProjSevenmWorkoutImage::getStatus, isEnable ? GlobalConstant.STATUS_ENABLE : GlobalConstant.STATUS_DISABLE);
        updateWrapper.set(ProjSevenmWorkoutImage::getUpdateUser, RequestContextUtils.getLoginUserName());
        updateWrapper.set(ProjSevenmWorkoutImage::getUpdateTime, LocalDateTime.now());
        this.update(updateWrapper);
    }

    @Override
    public List<ProjSevenmWorkoutImageVO> list(ProjSevenmWorkoutImageListReq req) {

        LambdaQueryWrapper<ProjSevenmWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(req.getId()), ProjSevenmWorkoutImage::getId, req.getId());
        queryWrapper.like(StringUtils.isNotBlank(req.getName()), ProjSevenmWorkoutImage::getName, req.getName());
        queryWrapper.eq(Objects.nonNull(req.getStatus()), ProjSevenmWorkoutImage::getStatus, req.getStatus());
        queryWrapper.eq(ObjUtil.isNotNull(req.getGender()), ProjSevenmWorkoutImage::getGender, req.getGender());
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper, ProjSevenmWorkoutImage::getTarget, req.getTarget(),false );
        queryWrapper.orderByAsc(ProjSevenmWorkoutImage::getSortNo);

        return mapStruct.toVOList(this.list(queryWrapper));
    }

    @Override
    public List<ProjSevenmWorkoutImageExportVO> listExportVO(ProjSevenmWorkoutImageListReq req) {
        return mapStruct.toExportVOList(this.list(req));
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Throwable.class)
    public List<String> importExcel(Integer projId, MultipartFile excel) {

        List<ProjSevenmWorkoutImageImportReq> list = Lists.newArrayList();
        EasyExcel.read(excel.getInputStream(), ProjSevenmWorkoutImageImportReq.class, new AnalysisEventListener<ProjSevenmWorkoutImageImportReq>() {
            @Override
            public void invoke(ProjSevenmWorkoutImageImportReq req, AnalysisContext analysisContext) {
                list.add(req);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {

            }
        }).sheet(0).doRead();

        // 校验name和target不能重复
        LambdaQueryWrapper<ProjSevenmWorkoutImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BaseModel::getId, ProjSevenmWorkoutImage::getTarget, ProjSevenmWorkoutImage::getName);

        queryWrapper.and(wrapper -> {
            list.forEach(data -> {
                wrapper.or(orWrapper -> {
                    orWrapper.eq(ProjSevenmWorkoutImage::getName, data.getName());
                    BitmaskEnumUtil.addBitmaskCondition(orWrapper, ProjSevenmWorkoutImage::getTarget, data.getTarget(), true);
                });
            });
        });
        queryWrapper.eq(ProjSevenmWorkoutImage::getDelFlag, 0);
        // 将请求数据和数据库中必然重复的数据一次性查询出来做判断，避免在循环中查询数据库
        List<ProjSevenmWorkoutImage> nameAndTargetImages = this.baseMapper.selectList(queryWrapper);
        // 逐一校验
        List<String> messages = list.stream()
                .flatMap(req -> Stream.concat(
                        // 校验器基本校验
                        validator.validate(req).stream()
                                .map(v -> String.format("%s:%s", req.getName(), v.getMessage())),
                        // name和target与数据库数据重复
                        nameAndTargetImages.stream()
                                .filter(image ->
                                        req.getName().equals(image.getName())
                                                && BitmaskEnumUtil.sumBitmaskEnumList(req.getTarget()) == BitmaskEnumUtil.sumBitmaskEnumList((image.getTarget()))
                                                && !Objects.equals(req.getId(), image.getId()))
                                .limit(1).map(image -> String.format("%s:name and target cannot be duplicated", req.getName()))
                ))
                .collect(Collectors.toList());
        messages.addAll(validateNameAndTarget(list));
        if (!messages.isEmpty()) {
            return messages;
        }

        // 区分新增和修改
        Map<Boolean, List<ProjSevenmWorkoutImageImportReq>> groupByInsert = list.stream().collect(Collectors.groupingBy(req -> Objects.isNull(req.getId())));
        if (CollUtil.isNotEmpty(groupByInsert.get(true))) {
            // 先将其他数据的排序统一向后位移，然后再插入数据
            AtomicInteger sortNo = new AtomicInteger(groupByInsert.get(true).size());
            List<ProjSevenmWorkoutImage> forInsert = groupByInsert.get(true).stream().map(mapStruct::toEntity)
                    .peek(entity -> entity.setProjId(projId).setSortNo(sortNo.getAndDecrement())).collect(Collectors.toList());
            LambdaUpdateWrapper<ProjSevenmWorkoutImage> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.setSql("sort_no = sort_no + " + forInsert.size());
            updateWrapper.isNotNull(ProjSevenmWorkoutImage::getSortNo);
            this.update(updateWrapper);
            this.saveBatch(forInsert);
        }
        if (CollUtil.isNotEmpty(groupByInsert.get(false))) {
            this.updateBatchById(groupByInsert.get(false).stream().map(req -> {
                ProjSevenmWorkoutImage entity = mapStruct.toEntity(req);
                entity.setId(req.getId());
                return entity;
            }).collect(Collectors.toList()));
        }
        return messages;
    }

    private List<String> validateNameAndTarget(List<ProjSevenmWorkoutImageImportReq> reqList) {
        List<String> result = new ArrayList<>();
        Set<String> processedPairs = new HashSet<>();
        for (ProjSevenmWorkoutImageImportReq req : reqList) {
            String pairKey = req.getName() + "|" + BitmaskEnumUtil.sumBitmaskEnumList(req.getTarget());
            if (processedPairs.contains(pairKey)) {
                result.add(String.format("%s:name and target cannot be duplicated", req.getName()));
            } else {
                processedPairs.add(pairKey);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateSort(List<Integer> idList) {

        // 按传入顺序指定排序
        AtomicInteger sortNo = new AtomicInteger(1);
        List<ProjSevenmWorkoutImage> batchImage = idList.stream().map(id -> {
            ProjSevenmWorkoutImage workoutImage = new ProjSevenmWorkoutImage();
            workoutImage.setId(id);
            workoutImage.setSortNo(sortNo.getAndIncrement());
            return workoutImage;
        }).collect(Collectors.toList());
        this.updateBatchById(batchImage);
    }

    @Override
    public ProjSevenmWorkoutImageVO detail(Integer id) {

        ProjSevenmWorkoutImage workoutImage = this.getById(id);
        AssertUtil.notNull(workoutImage, "workoutImage not exist");
        return BeanUtil.toBean(workoutImage, ProjSevenmWorkoutImageVO.class);
    }
}




