package com.laien.web.biz.proj.oog101.bo;

import com.laien.common.oog101.enums.SevenmTemplateTypeEnums;
import com.laien.web.frame.exception.BizException;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Author:  hhl
 * Date:  2025/3/20 13:52
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class GenerateSevenmWorkoutFileContextBO {

    private List<SevenmWorkoutWrapperBO> workoutList;
    private List<String> languageList;
    private Map<String, SevenmSoundBO> femaleSoundBoMap;
    private Map<String, SevenmSoundBO> maleSoundBoMap;
    private Map<SevenmTemplateTypeEnums,Map<String, SevenmSoundBO>> typeFemaleSoundBoMap = new HashMap<>();
    private Map<SevenmTemplateTypeEnums,Map<String, SevenmSoundBO>> typeMaleSoundBoMap = new HashMap<>();
    private Map<String, Map<Integer, SevenmVideoSoundI18nBO>> videoSoundI18nMap;

    private AtomicBoolean uploadSuccess = new AtomicBoolean(true);
    /**
     * 如果executor为null，那么使用当前线程进行上传
     * 如果executor不为null，那么使用executor进行异步文件上传
     */
    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(100, 100, 3 * 60 * 1000, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<Runnable>(500), Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    private BlockingQueue<CompletableFuture<Void>> futureQueue = new ArrayBlockingQueue<>(300000);

    private Boolean generateM3u8 = true;
    private Boolean generateAudioJson = true;


    public GenerateSevenmWorkoutFileContextBO(List<SevenmWorkoutWrapperBO> workoutList,
                                              List<String> languageList) {
        this.workoutList = workoutList;
        this.languageList = languageList;
    }

    /**
     * executor执行任务必须使用这个方法
     */
    public CompletableFuture<Void> submit(Runnable task) {
        CompletableFuture<Void> future = CompletableFuture.runAsync(task, EXECUTOR);
        try {
            futureQueue.put(future);
        } catch (InterruptedException e) {
            uploadSuccess.set(false);
            throw new BizException("oog101 upload file interrupted," + e.getMessage());
        }
        return future;
    }

    public void waitUploadSuccess() {
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureQueue.toArray(new CompletableFuture[0]));
        allFutures.join();
        futureQueue.clear();
        if (!uploadSuccess.get()) {
            throw new BizException("oog101 upload m3u8 and audio json failed");
        }
    }

}
