package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.*;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 7M Workout Generate 分页查询请求
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProjSevenmWorkoutGenerate分页查询请求")
public class ProjSevenmWorkoutGeneratePageReq extends PageReq {

    @ApiModelProperty(value = "Workout Id")
    private Integer id;

    @ApiModelProperty(value = "模板ID")
    private Integer projSevenmTemplateId;

    @ApiModelProperty(value = "template启用状态 0草稿 1启用 2停用")
    private Integer templateStatus;

    @ApiModelProperty(value = "任务ID")
    private Integer projSevenmTemplateTaskId;

    @ApiModelProperty(value = "templateLevel")
    private SevenmDifficultyEnums templateLevel;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "fileStatus")
    private Integer fileStatus;

    @ApiModelProperty(value = "目标")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "难度")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "器材")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制(多选)")
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "videoIdList")
    private List<Integer> videoIdList;

}
