package com.laien.web.biz.proj.oog101.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog101.enums.SevenmTypeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 7M Manual Workout Exercise Video 关联表
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmManualWorkoutExerciseVideo对象", description = "proj_sevenm_manual_workout_exercise_video")
@TableName(autoResultMap = true)
public class ProjSevenmManualWorkoutExerciseVideo extends BaseModel {

    @ApiModelProperty(value = "proj_sevenm_manual_workout_id")
    private Integer projSevenmManualWorkoutId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "proj_sevenm_exercise_video_id")
    private Integer projSevenmExerciseVideoId;

    @ApiModelProperty(value = "Exercise Circuit")
    private Integer exerciseCircuit;

    @ApiModelProperty(value = "Unit Name")
    private SevenmTypeEnums unitName;

    @ApiModelProperty(value = "preview_duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "video_duration")
    private Integer videoDuration;
}
