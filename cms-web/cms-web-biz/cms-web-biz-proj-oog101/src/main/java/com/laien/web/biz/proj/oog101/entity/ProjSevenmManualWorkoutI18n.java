package com.laien.web.biz.proj.oog101.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_sevenm_manual_workout 多语言表
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjSevenmManualWorkoutI18n对象", description="proj_sevenm_manual_workout 多语言表")
public class ProjSevenmManualWorkoutI18n extends BaseModel {

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "workout id")
    private Integer projSevenmManualWorkoutId;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "项目id")
    private Integer projId;
}
