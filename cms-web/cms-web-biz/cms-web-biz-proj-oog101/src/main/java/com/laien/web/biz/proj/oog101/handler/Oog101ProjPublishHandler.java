package com.laien.web.biz.proj.oog101.handler;

import com.laien.common.oog101.constant.ProjectConstant;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamOutBO;
import com.laien.web.biz.proj.core.handler.ProjPublishHandler;
import com.laien.web.biz.proj.oog101.entity.*;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * oog101项目发布handler;
 * <AUTHOR>
 * @since 2025/05/12
 */
@Service
public class Oog101ProjPublishHandler implements ProjPublishHandler{

    @Override
    public List<String> getProjTables () {
        return Arrays.asList(
                getTableName(ProjSevenmFastingArticle.class),
                getTableName(ProjSevenmManualWorkout.class),
                getTableName(ProjSevenmManualWorkoutI18n.class),
                getTableName(ProjSevenmManualWorkoutExerciseVideo.class),
                getTableName(ProjSevenmMusic.class),
                getTableName(ProjSevenmPlaylist.class),
                getTableName(ProjSevenmPlaylistRelation.class),
                getTableName(ProjSevenmWorkoutImage.class),
                getTableName(ProjSevenmTemplate.class)
        );
    }

    @Override
    public List<ProjPublishProjRelationParamOutBO> getProjRelationTables () {
        return Collections.emptyList();
    }

    @Override
    public String getAppCode () {
        return ProjectConstant.APP_CODE;
    }

    @Override
    public boolean requiresMiddleI18nTaskValidation() {
        return false;
    }
}
