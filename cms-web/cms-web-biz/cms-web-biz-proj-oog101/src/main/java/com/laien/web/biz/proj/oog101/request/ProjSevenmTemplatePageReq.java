package com.laien.web.biz.proj.oog101.request;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.oog101.enums.SevenmDifficultyEnums;
import com.laien.common.oog101.enums.SevenmSpecialLimitEnums;
import com.laien.common.oog101.enums.SevenmTemplateTaskStatusEnum;
import com.laien.common.oog101.enums.SevenmTemplateTypeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p> template 分页查询参数 <p>
 *
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "template 分页查询参数", description = "template 分页查询")
public class ProjSevenmTemplatePageReq extends PageReq {

    @JsonIgnore
    private Integer projId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("template版本")
    private Integer dataVersion;

    @ApiModelProperty("模板类型")
    private SevenmTemplateTypeEnums templateType;

    @ApiModelProperty("状态：0 草稿；1 启用；2 禁用；")
    private Integer status;

    @ApiModelProperty("任务状态 1待处理、2处理中、3处理失败、4 处理成功")
    private SevenmTemplateTaskStatusEnum taskStatus;

    @ApiModelProperty("难度等级")
    private SevenmDifficultyEnums level;

    @ApiModelProperty("特殊限制")
    private List<SevenmSpecialLimitEnums> specialLimit;

    /**
     * 用于手写sqlXml的位运算使用
     * @return specialLimit sum
     */
    @JsonIgnore
    public Integer getSpecialLimitSum(){
        return BitmaskEnumUtil.sumBitmaskEnumList(this.getSpecialLimit());
    }

}
