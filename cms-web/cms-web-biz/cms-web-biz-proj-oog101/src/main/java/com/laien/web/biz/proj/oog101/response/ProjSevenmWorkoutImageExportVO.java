/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog101.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import com.laien.web.biz.core.converter.GenericEnumListNameConverter;
import com.laien.common.core.converter.GenericEnumNameConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * Image导出VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@Accessors(chain = true)
public class ProjSevenmWorkoutImageExportVO {

    @ExcelProperty(value = "id")
    private Integer id;

    @ExcelProperty(value = "name")
    private String name;

    @ExcelProperty(value = "target",converter = GenericEnumListNameConverter.class)
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "性别 1-female 2-male")
    @ExcelProperty(value = "gender",converter = GenericEnumNameConverter.class)
    private SevenmGenderEnums gender;

    @ExcelProperty(value = "cover_image")
    private String coverImage;

    @ExcelProperty(value = "detail_image")
    private String detailImage;

}
