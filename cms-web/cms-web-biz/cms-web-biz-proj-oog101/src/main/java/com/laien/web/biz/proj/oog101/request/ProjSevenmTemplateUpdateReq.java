package com.laien.web.biz.proj.oog101.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog101.enums.SevenmDifficultyEnums;
import com.laien.common.oog101.enums.SevenmSpecialLimitEnums;
import com.laien.common.oog101.enums.SevenmTemplateTypeEnums;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplateExerciseGroupDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 修改 template 参数
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "修改 template 参数", description = "修改 template 参数")
public class ProjSevenmTemplateUpdateReq {

    @JsonIgnore
    private Integer projId;

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("template版本")
    private Integer dataVersion;

    @ApiModelProperty
    private SevenmTemplateTypeEnums templateType;

    @ApiModelProperty("生成多少天的天数")
    private Integer days;

    @ApiModelProperty
    private SevenmDifficultyEnums level;

    @ApiModelProperty
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty("warm up动作组")
    private ProjSevenmTemplateExerciseGroupDetailVO warmupExerciseGroup;

    @ApiModelProperty("main动作组")
    private ProjSevenmTemplateExerciseGroupDetailVO mainExerciseGroup;

    @ApiModelProperty("cool down动作组")
    private ProjSevenmTemplateExerciseGroupDetailVO coolDownExerciseGroup;

}
