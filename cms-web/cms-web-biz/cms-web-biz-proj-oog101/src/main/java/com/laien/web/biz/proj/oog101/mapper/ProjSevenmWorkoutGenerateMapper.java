package com.laien.web.biz.proj.oog101.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutGenerate;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutGeneratePageReq;
import com.laien.web.frame.response.IdAndCountsRes;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_sevenm_workout_generate Mapper
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
public interface ProjSevenmWorkoutGenerateMapper extends BaseMapper<ProjSevenmWorkoutGenerate> {

    /**
     * <p>统计模板生成的workout数量</p>
     *
     * @param ids 模板ID集合
     * @return 模板生成的workout数量集合
     */
    @Select("<script> " +
            "SELECT a.proj_sevenm_template_id AS id, COUNT(a.id) AS counts " +
            "FROM proj_sevenm_workout_generate a " +
            "WHERE del_flag = 0 " +
            "AND proj_sevenm_template_id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>#{id}</foreach> " +
            "GROUP BY a.proj_sevenm_template_id " +
            "</script>")
    List<IdAndCountsRes> countTemplateWorkoutNum(@Param("ids") List<Integer> ids);


    IPage<Integer> page(@Param("page") Page<ProjSevenmWorkoutGenerate> page,
                        @Param("pageReq") ProjSevenmWorkoutGeneratePageReq pageReq,
                        @Param("templateIdSet") Set<Integer> templateIdSet,
                        @Param("specialLimitSum") Integer specialLimitSum,
                        @Param("targetSum") Integer targetSum);

    List<Integer> list(@Param("pageReq") ProjSevenmWorkoutGeneratePageReq pageReq,
                       @Param("templateIdSet") Set<Integer> templateIdSet,
                       @Param("specialLimitSum") Integer specialLimitSum,
                       @Param("targetSum") Integer targetSum);

}
