package com.laien.web.biz.proj.oog101.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmSound;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundPageVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSevenmSoundMapStruct {

    List<ProjSevenmSoundPageVO> toPageList(List<ProjSevenmSound> entities);

    /**
     * 添加 request 转 entity
     * @param req
     * @return
     */
    ProjSevenmSound toEntity(ProjSevenmSoundAddReq req);

    /**
     * 修改 request 转 entity
     * @param req
     * @return
     */
    ProjSevenmSound toEntity(ProjSevenmSoundUpdateReq req);

    /**
     * entity 转详情
     * @param entity
     * @return
     */
    ProjSevenmSoundDetailVO toDetailVO(ProjSevenmSound entity);
}