package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  ProjSevenmWorkoutGenerateAddReq
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjSevenmWorkoutGenerateAddReq", description="ProjSevenmWorkoutGenerateAddReq")
public class ProjSevenmWorkoutGenerateAddReq {

    @ApiModelProperty(value = "proj_sevenm_template_id")
    private Integer projSevenmTemplateId;

    @ApiModelProperty(value = "proj_sevenm_template_task_id")
    private Integer projSevenmTemplateTaskId;

    @ApiModelProperty(value = "目标")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "难度")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "器材")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制(多选)")
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;

    @ApiModelProperty(value = "videoIdList")
    private List<Integer> videoIdList;
}
