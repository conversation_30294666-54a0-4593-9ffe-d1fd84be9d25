package com.laien.web.biz.proj.oog101.response;

import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 7M Workout Generate 分页查询响应
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@ApiModel(value = "健身训练生成记录分页查询响应")
public class ProjSevenmWorkoutGeneratePageVO {

    @ApiModelProperty(value = "ID")
    private Integer id;

    @ApiModelProperty(value = "模板ID")
    private Integer projSevenmTemplateId;

    @ApiModelProperty(value = "任务ID")
    private Integer projSevenmTemplateTaskId;

    @ApiModelProperty(value = "目标")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "难度")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "器材")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制(多选)")
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "时长(秒)")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "视频URL")
    private String videoUrl;

    @ApiModelProperty(value = "音频语言")
    private String audioLanguages;

    @ApiModelProperty(value = "文件状态 0:运行中 1:成功 2:失败")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "生成数量")
    private Integer generateNum;

    @ApiModelProperty(value = "是否清空之前的workout 1是 0否")
    private Integer cleanUp;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

}
