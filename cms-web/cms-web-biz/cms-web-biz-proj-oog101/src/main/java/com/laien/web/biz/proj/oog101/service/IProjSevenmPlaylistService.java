package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmPlaylist;
import com.laien.web.biz.proj.oog101.request.ProjSevenmPlaylistAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmPlaylistUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistPageVO;
import com.laien.web.frame.request.IdListReq;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public interface IProjSevenmPlaylistService extends IService<ProjSevenmPlaylist> {

    /**
     * 查询项目下所有playlist
     *
     * @return
     */
    List<ProjSevenmPlaylistPageVO> pageQuery(Integer projId);

    ProjSevenmPlaylistDetailVO getDetailById(Integer id);

    void insert(ProjSevenmPlaylistAddReq playlistAddReq);

    void sort(IdListReq idListReq);

    void update(ProjSevenmPlaylistUpdateReq playlistUpdateReq);

    /**
     * 批量启用
     *
     * @param idList idList
     */
    void updateEnableByIds(Collection<Integer> idList);

    /**
     * 批量禁用
     *
     * @param idList idList
     */
    void updateDisableByIds(Collection<Integer> idList);

    /**
     * 批量删除
     *
     * @param idList idList
     */
    void deleteByIds(Collection<Integer> idList);
}
