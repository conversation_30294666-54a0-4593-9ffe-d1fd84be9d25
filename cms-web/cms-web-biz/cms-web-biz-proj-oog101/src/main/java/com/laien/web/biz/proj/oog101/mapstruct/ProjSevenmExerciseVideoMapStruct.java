package com.laien.web.biz.proj.oog101.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmExerciseVideoImportReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGenerateVideoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *  Proj7MExerciseVideo 对象转换
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSevenmExerciseVideoMapStruct {

    ProjSevenmExerciseVideo toEntity(ProjSevenmExerciseVideoAddReq entity);

    ProjSevenmExerciseVideo toEntity(ProjSevenmExerciseVideoImportReq req);

    List<ProjSevenmExerciseVideoPageVO> toPageList(List<ProjSevenmExerciseVideo> entities);

    ProjSevenmExerciseVideoDetailVO toDetailVO(ProjSevenmExerciseVideo entity);

    List<ProjSevenmExerciseVideoExportVO> toExportList(List<ProjSevenmExerciseVideo> list);

    ProjSevenmWorkoutGenerateVideoVO toGenerateVideoVO(ProjSevenmExerciseVideoPageVO v);
}
