package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmSound;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmSoundMapper;
import com.laien.web.biz.proj.oog101.mapstruct.ProjSevenmSoundMapStruct;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundPageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmSoundService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.BizExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSevenmSoundServiceImpl extends ServiceImpl<ProjSevenmSoundMapper, ProjSevenmSound>
        implements IProjSevenmSoundService {
    private final ProjSevenmSoundMapStruct mapStruct;
    private final ICoreVoiceConfigI18nService i18nConfigService;
    private final ICoreTextTaskI18nService i18nService;
    private final IProjInfoService projInfoService;
    private final IProjLmsI18nService projLmsI18nService;

    /**
     * 分页查看 sound
     *
     * @param req
     * @return
     */
    @Override
    public PageRes<ProjSevenmSoundPageVO> selectSoundPage(ProjSevenmSoundPageReq req) {
        LambdaQueryWrapper<ProjSevenmSound> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(req.getId()), ProjSevenmSound::getId, req.getId())
                .like(StrUtil.isNotBlank(req.getSoundName()), ProjSevenmSound::getSoundName, req.getSoundName())
                .eq(ObjUtil.isNotNull(req.getStatus()), ProjSevenmSound::getStatus, req.getStatus())
                .eq(ObjUtil.isNotNull(req.getSoundType()), ProjSevenmSound::getSoundType, req.getSoundType())
                .eq(ObjUtil.isNotNull(req.getSoundSubType()), ProjSevenmSound::getSoundSubType, req.getSoundSubType())
                .eq(ObjUtil.isNotNull(req.getGender()), ProjSevenmSound::getGender, req.getGender())
                .in(CollUtil.isNotEmpty(req.getIds()), ProjSevenmSound::getId, req.getIds());
        query.orderByDesc(ProjSevenmSound::getId);
        IPage<ProjSevenmSound> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), query);
        List<ProjSevenmSoundPageVO> list = mapStruct.toPageList(page.getRecords());
        fillI18nConfigInfo(page.getRecords(),list);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }

    private void fillI18nConfigInfo(List<ProjSevenmSound> records, List<ProjSevenmSoundPageVO> list) {
        Map<Integer, CoreVoiceConfigI18n> configMap = getVoiceConfigIdMap(records);
        for (ProjSevenmSoundPageVO vo : list) {
            CoreVoiceConfigI18n config = configMap.get(vo.getCoreVoiceConfigI18nId());
            if (config != null) {
                vo.setCoreVoiceConfigI18nName(config.getName());
            }
        }
    }

    private void fillI18nConfigNameBySound(ProjSevenmSoundDetailVO detailVO) {
        Set<Integer> configIds = new HashSet<>();
        configIds.add(detailVO.getCoreVoiceConfigI18nId());
        Map<Integer, CoreVoiceConfigI18n> configMap = i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));

        CoreVoiceConfigI18n config = configMap.get(detailVO.getCoreVoiceConfigI18nId());
        if (config != null) {
            detailVO.setCoreVoiceConfigI18nName(config.getName());
        }
    }


    private Map<Integer, CoreVoiceConfigI18n> getVoiceConfigIdMap(List<ProjSevenmSound> records) {
        Set<Integer> configIds = records.stream().map(ProjSevenmSound::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        return i18nConfigService.listConfigByIds(configIds).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, Function.identity()));
    }

    /**
     * 添加 sound
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveSound(ProjSevenmSoundAddReq req) {
        check(null, req);
        ProjSevenmSound projSevenmSound = mapStruct.toEntity(req);
        projSevenmSound.setStatus(GlobalConstant.STATUS_DRAFT);
        this.save(projSevenmSound);
        if (projSevenmSound.getNeedTranslation()) {
            projLmsI18nService.handleI18n(ListUtil.of(projSevenmSound), projInfoService.getById(req.getProjId()));
        }
    }
    /**
     * 修改 sound
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSound(ProjSevenmSoundUpdateReq req) {
        check(req.getId(), req);
        ProjSevenmSound projSevenmSound = mapStruct.toEntity(req);
        this.updateById(projSevenmSound);
        if (projSevenmSound.getNeedTranslation()) {
            projLmsI18nService.handleI18n(ListUtil.of(projSevenmSound), projInfoService.getById(req.getProjId()));
        }
    }

    /**
     * 获取 sound 详情
     * @param id
     * @return
     */
    @Override
    public ProjSevenmSoundDetailVO getDetail(Integer id) {
        ProjSevenmSound sound = this.getById(id);
        BizExceptionUtil.throwIf(Objects.isNull(sound),"Data not found");
        ProjSevenmSoundDetailVO detailVO = mapStruct.toDetailVO(sound);
        fillI18nConfigNameBySound(detailVO);
        return detailVO;
    }
    /**
     * 批量启用
     * @param idList
     */
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSevenmSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmSound::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmSound::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE)
                .in(ProjSevenmSound::getId, idList);
       this.update(new ProjSevenmSound(), wrapper);
    }

    /**
     * 批量禁用
     * @param idList
     */
    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<ProjSevenmSound> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmSound::getStatus, GlobalConstant.STATUS_DISABLE)
                .eq(ProjSevenmSound::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmSound::getId, idList);
        this.update(new ProjSevenmSound(), wrapper);
    }

    private void check(Integer id, ProjSevenmSoundAddReq req) {
        LambdaQueryWrapper<ProjSevenmSound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmSound::getSoundName, req.getSoundName())
                .eq(ProjSevenmSound::getProjId, req.getProjId())
                .ne(null != id, BaseModel::getId, id);
        List<ProjSevenmSound> soundList = baseMapper.selectList(wrapper);
        Set<String> nameSet = soundList.stream().map(ProjSevenmSound::getSoundName).collect(Collectors.toSet());
        if (nameSet.contains(req.getSoundName())) {
            String error = "name already exists";
            throw new BizException(error);
        }
    }


}
