/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog101.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 模板动作组 详情
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "模板动作组 详情", description = "模板动作组 详情")
public class ProjSevenmTemplateExerciseGroupDetailVO {

    @ApiModelProperty("exercise组名称")
    private String groupName;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("播放循环次数")
    private Integer rounds;
}
