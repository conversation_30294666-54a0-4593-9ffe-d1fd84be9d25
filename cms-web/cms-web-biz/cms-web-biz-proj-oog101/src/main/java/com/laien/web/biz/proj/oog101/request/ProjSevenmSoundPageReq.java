package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmSoundSubTypeEnums;
import com.laien.common.oog101.enums.SevenmSoundTypeEnums;
import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * Sevenm Sound 分页
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Sevenm Sound 分页", description = "Sevenm Sound 分页")
public class ProjSevenmSoundPageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "ids")
    private List<Integer> ids;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "soundType")
    private SevenmSoundTypeEnums soundType;

    @ApiModelProperty(value = "soundSubType")
    private SevenmSoundSubTypeEnums soundSubType;

    @ApiModelProperty(value = "性别")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "项目id")
    private Integer projId;
}

