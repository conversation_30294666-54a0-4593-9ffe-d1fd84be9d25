package com.laien.web.biz.proj.oog101.bo;

import com.laien.common.oog101.enums.SevenmTypeEnums;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplate;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplateExerciseGroup;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplateTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * SevenmTemplateTaskBO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SevenmTemplateTaskBO {

    private ProjSevenmTemplateTask templateTask;

    private ProjSevenmTemplate template;

    private Map<SevenmTypeEnums, ProjSevenmTemplateExerciseGroup> templateGroupMap;

    private List<String> languageList;

    private GenerateSevenmWorkoutFileContextBO fileContextBO;

}
