package com.laien.web.biz.proj.oog101.controller;


import com.laien.web.biz.proj.oog101.entity.ProjSevenmFastingArticle;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleListReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleListVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmFastingArticleService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 7M fasting article 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Api(tags = "项目管理:7M fasting article")
@RestController
@RequestMapping("/proj/sevenmFastingArticle")
public class ProjSevenmFastingArticleController extends ResponseController {

    @Resource
    private IProjSevenmFastingArticleService service;

    @ApiOperation(value = "分页列表")
    @GetMapping("/list")
    public ResponseResult<List<ProjSevenmFastingArticleListVO>> list(ProjSevenmFastingArticleListReq articleListReq) {
        return succ(service.list(articleListReq, RequestContextUtils.getProjectId()));
    }

    @ApiOperation(value = "增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSevenmFastingArticleAddReq articleAddReq) {
        service.save(articleAddReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmFastingArticleUpdateReq articleUpdateReq) {
        service.update(articleUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmFastingArticleDetailVO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    @ApiOperation(value = "批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }

        service.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        service.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list can not be empty");
        }
        service.deleteByIds(idList);
        return succ();
    }


    @ApiOperation(value = "排序")
    @PostMapping("/sort")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> sort(@RequestBody IdListReq idListReq) {
        service.sort(idListReq);
        return succ();
    }

}
