package com.laien.web.biz.proj.oog101.bo;

import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTemplateTypeEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@Data
@Accessors(chain = true)
public class SevenmWorkoutWrapperBO {
    private List<BaseGenerateVideoBO> videoList;
    private WorkoutFileUploadInfoBO uploadInfoBO;
    private Object workoutUniqueKey;
    private SevenmGenderEnums gender;
    private SevenmTemplateTypeEnums workoutType;
}
