package com.laien.web.biz.proj.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import com.laien.common.oog101.enums.TableCodeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Accessors(chain = true)
public class ProjSevenmWorkoutImage extends BaseModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_WORKOUT_IMAGE;

    @ApiModelProperty("图片名称")
    private String name;

    @ApiModelProperty(value = "Target Areas")
    @TableField(typeHandler = SevenmTargetEnums.TypeHandler.class)
    private List<SevenmTargetEnums> target;

    @ApiModelProperty("封面图片地址")
    private String coverImage;

    @ApiModelProperty("详情图片地址")
    private String detailImage;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Integer projId;

    @ApiModelProperty("排序字段")
    private Integer sortNo;

}
