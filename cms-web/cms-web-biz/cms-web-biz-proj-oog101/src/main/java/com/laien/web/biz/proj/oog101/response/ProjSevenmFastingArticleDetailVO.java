package com.laien.web.biz.proj.oog101.response;

import com.laien.common.oog101.enums.SevenmFastingArticleEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * Proj7MFastingArticleDetailVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value="FastingArticle对象", description="fasting article")
public class ProjSevenmFastingArticleDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String titleName;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型")
    private SevenmFastingArticleEnums type;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "参考文档")
    private String reference;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
