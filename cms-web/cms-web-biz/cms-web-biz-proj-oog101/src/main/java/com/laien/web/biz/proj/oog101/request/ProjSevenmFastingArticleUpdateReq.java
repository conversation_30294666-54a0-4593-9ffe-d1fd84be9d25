package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * fasting article
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value="FastingArticle对象", description="fasting article")
public class ProjSevenmFastingArticleUpdateReq extends ProjSevenmFastingArticleAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;
}
