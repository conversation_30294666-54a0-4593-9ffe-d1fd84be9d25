package com.laien.web.biz.proj.oog101.entity.i18n;

import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmExerciseVideo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * ProjSevenmExerciseVideoI18n
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmExerciseVideoI18n", description = "ProjSevenmExerciseVideoI18n")
public class ProjSevenmExerciseVideoI18n extends BaseAudioI18nModel {

    @ApiModelProperty(value = "声音脚本")
    @AppAudioTranslateField(resultFieldName = "result")
    private String name;

    @AppAudioTranslateField(resultFieldName = "guidanceResult")
    private String guidance;

    private List<AudioTranslateResultModel> guidanceResult;



    public ProjSevenmExerciseVideoI18n(ProjSevenmExerciseVideo video) {
        super.setUniqueKey(video.getId());
        super.setCoreVoiceConfigI18nId(video.getCoreVoiceConfigI18nId());
        this.name = video.getName();
        this.guidance = video.getGuidance();
    }
}
