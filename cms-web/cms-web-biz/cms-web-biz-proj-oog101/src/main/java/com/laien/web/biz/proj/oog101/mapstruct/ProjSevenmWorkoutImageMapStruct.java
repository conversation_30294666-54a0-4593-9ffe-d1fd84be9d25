package com.laien.web.biz.proj.oog101.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutImage;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageImportReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 * <p>
 *
 * <AUTHOR>
 * @since sylar
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface ProjSevenmWorkoutImageMapStruct {

    ProjSevenmWorkoutImage toEntity(ProjSevenmWorkoutImageAddReq req);

    List<ProjSevenmWorkoutImageVO> toVOList(List<ProjSevenmWorkoutImage> list);

    List<ProjSevenmWorkoutImageExportVO> toExportVOList(List<ProjSevenmWorkoutImageVO> list);

    ProjSevenmWorkoutImage toEntity(ProjSevenmWorkoutImageImportReq req);
}
