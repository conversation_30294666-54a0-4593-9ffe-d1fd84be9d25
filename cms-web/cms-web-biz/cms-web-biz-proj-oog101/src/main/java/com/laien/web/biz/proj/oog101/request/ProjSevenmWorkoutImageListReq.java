package com.laien.web.biz.proj.oog101.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "workout image 列表查询参数", description = "workout image 列表查询")
public class ProjSevenmWorkoutImageListReq {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("图片名称")
    private String name;

    @ApiModelProperty(value = "Target Areas")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty("状态")
    private Integer status;

}
