package com.laien.web.biz.proj.oog101.controller;


import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutGenerate;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutGeneratePageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutGenerateUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGenerateDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGeneratePageVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmWorkoutGenerateService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Sevenm Workout Generate 前端控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/14
 */
@Api(tags = "项目管理:Sevenm Workout Generate")
@RestController
@RequestMapping("/proj/sevenmWorkoutGenerate")
public class ProjSevenmWorkoutGenerateController extends ResponseController {

    @Resource
    private IProjSevenmWorkoutGenerateService workoutGenerateService;

    @ApiOperation(value = "Sevenm Workout Generate 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSevenmWorkoutGeneratePageVO>> page(ProjSevenmWorkoutGeneratePageReq pageReq) {
        pageReq.setProjId(RequestContextUtils.getProjectId());
        PageRes<ProjSevenmWorkoutGeneratePageVO> pageRes = workoutGenerateService.page(pageReq);
        return succ(pageRes);
    }

    @ApiOperation(value = "Sevenm Workout Generate 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmWorkoutGenerateDetailVO> detail(@PathVariable Integer id) {
        return succ(workoutGenerateService.findDetailById(id));
    }

    @ApiOperation(value = "Sevenm Workout Generate 分页-workout-video列表")
    @GetMapping("/page/{id}/video")
    public ResponseResult<PageRes<ProjSevenmExerciseVideoPageVO>> pageWorkout(PageReq pageReq, @PathVariable Integer id) {
        return succ(workoutGenerateService.pageVideo(pageReq, id));
    }

    @ApiOperation(value = "Sevenm Workout Generate 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmWorkoutGenerateUpdateReq workoutUpdateReq) {
        workoutGenerateService.update(workoutUpdateReq, RequestContextUtils.getProjectId());
        return succ();
    }


    @ApiOperation(value = "Sevenm Workout Generate 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        workoutGenerateService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Sevenm Workout Generate 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        workoutGenerateService.updateDisableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "Sevenm Workout Generate 批量删除")
    @PostMapping("/del")
    public ResponseResult<Void> delBatch(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        workoutGenerateService.deleteByIdList(idList);
        return succ();
    }

    @ApiOperation(value = "Sevenm Workout Generate 生成M3U8文件,支持用分页条件")
    @PostMapping("/generateM3u8ByQuery")
    public ResponseResult<Integer> generateM3u8ByQuery(@RequestBody ProjSevenmManualWorkoutGenerateM3u8Req m3u8Req) {
        m3u8Req.setProjId(RequestContextUtils.getProjectId());
        return succ(workoutGenerateService.generateM3u8ByQuery(m3u8Req));
    }

    @ApiOperation(value = "Sevenm Workout Generate 生成M3U8文件打断")
    @PostMapping("/generateM3u8Interrupt")
    public ResponseResult<Void> generateM3u8Interrupt() {
        workoutGenerateService.generateM3u8Interrupt();
        return succ();
    }
}
