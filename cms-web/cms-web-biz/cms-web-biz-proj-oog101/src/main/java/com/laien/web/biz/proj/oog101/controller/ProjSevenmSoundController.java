package com.laien.web.biz.proj.oog101.controller;

import cn.hutool.core.collection.CollUtil;
import com.laien.common.oog101.enums.SevenmSoundTypeEnums;
import com.laien.web.biz.proj.core.util.AssertUtil;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmSound;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundPageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmSoundUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundPageVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmSoundTypeEnumsVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmSoundService;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Api(tags = "项目管理:Sevenm sound")
@RestController
@RequestMapping("/proj/sevenmSound")
public class ProjSevenmSoundController extends ResponseController {

    @Resource
    private IProjSevenmSoundService soundService;

    @ApiOperation(value = "7m sound 分页列表")
    @GetMapping("/page")
    public ResponseResult<PageRes<ProjSevenmSoundPageVO>> page(ProjSevenmSoundPageReq req) {
        req.setProjId(RequestContextUtils.getProjectId()) ;
        AssertUtil.notNull(RequestContextUtils.getProjectId(),"projId is null");
        PageRes<ProjSevenmSoundPageVO> pageRes = soundService.selectSoundPage(req);
        return succ(pageRes);
    }

    @ApiOperation(value = "获取所有soundType")
    @GetMapping("/getAllSoundType")
    public ResponseResult<List<ProjSevenmSoundTypeEnumsVO>> getAllSoundType() {
        return succ(CollUtil.newArrayList(SevenmSoundTypeEnums.values()).stream().map(ProjSevenmSoundTypeEnumsVO::new).collect(Collectors.toList()));
    }


    @ApiOperation(value = "7m Sound 增加")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody ProjSevenmSoundAddReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.saveSound(req);
        return succ();
    }

    @ApiOperation(value = "Sevenm Sound 修改")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody ProjSevenmSoundUpdateReq req) {
        req.setProjId(RequestContextUtils.getProjectId());
        soundService.updateSound(req);
        return succ();
    }

    @ApiOperation(value = "Sevenm Sound 详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<ProjSevenmSoundDetailVO> detail(@PathVariable Integer id) {
        ProjSevenmSoundDetailVO detailVO = soundService.getDetail(id);
        return succ(detailVO);
    }

    @ApiOperation(value = "7m Sound 批量启用")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateEnableByIds(idList);
        return succ();
    }

    @ApiOperation(value = "7m Sound 批量禁用")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (Objects.isNull(idList) || idList.isEmpty()) {
            return fail("ID list cannot be empty");
        }
        soundService.updateDisableByIds(idList);
        return succ();
    }
}
