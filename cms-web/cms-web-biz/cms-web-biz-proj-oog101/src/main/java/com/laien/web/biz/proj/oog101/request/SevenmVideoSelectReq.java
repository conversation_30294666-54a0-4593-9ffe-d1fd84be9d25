package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.*;
import lombok.Data;

import java.util.List;

/**
 * 封装用于 Sevenm 视频筛选的请求参数
 */
@Data
public class SevenmVideoSelectReq {
    private SevenmTypeEnums videoType;
    private SevenmExerciseTypeEnums exerciseType;
    private List<SevenmPositionEnums> position;
    private SevenmEquipmentEnums equipmentEnums;
    private List<SevenmSpecialLimitEnums> specialLimitList;
    private List<SevenmDifficultyEnums> difficulty;
    private List<SevenmIntensityEnums> intensity;
    private List<SevenmTargetEnums> target;
    private SevenmGenderEnums gender;
}
