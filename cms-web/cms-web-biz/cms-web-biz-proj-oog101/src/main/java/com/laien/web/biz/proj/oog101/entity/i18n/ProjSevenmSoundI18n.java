package com.laien.web.biz.proj.oog101.entity.i18n;

import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.web.biz.proj.oog101.bo.AudioJson101BO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_sevenm_sound
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmSoundI18n", description = "ProjSevenmSoundI18n")
public class ProjSevenmSoundI18n extends BaseAudioI18nModel {

    @ApiModelProperty(value = "声音脚本")
    @AppAudioTranslateField(resultFieldName = "result")
    private String soundScript;

    public ProjSevenmSoundI18n(AudioJson101BO json101BO) {
        super.setUniqueKey(json101BO.getSoundId());
        super.setCoreVoiceConfigI18nId(json101BO.getCoreVoiceConfigI18nId());
        this.soundScript = json101BO.getSoundScript();
    }
}
