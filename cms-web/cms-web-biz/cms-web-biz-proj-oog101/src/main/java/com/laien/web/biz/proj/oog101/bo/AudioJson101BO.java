package com.laien.web.biz.proj.oog101.bo;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.oog101.enums.AudioCategoryEnums;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmSound;
import com.laien.web.biz.proj.oog101.service.IProjSevenmSoundService;
import com.laien.web.common.file.service.FileService;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.utils.BizExceptionUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/3/20 13:57
 */
@Data
@NoArgsConstructor
public class AudioJson101BO {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "url")
    private String url;
    @ApiModelProperty(value = "name")
    private String name;
    @ApiModelProperty(value = "soundScript")
    private String soundScript;
    @ApiModelProperty(value = "播放时间点")
    private BigDecimal time;
    @ApiModelProperty(value = "性别")
    private SevenmGenderEnums gender;
    @JsonIgnore
    @ApiModelProperty(value = "时长")
    private Integer duration;
    @JsonIgnore
    @ApiModelProperty(value = "系统音表原始id")
    private Integer soundId;
    @JsonIgnore
    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;
    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;
    @ApiModelProperty(value = "用于表明音频类型")
    private AudioCategoryEnums category;
    // 是否支持关闭，false不支持关闭，true支持关闭
    private boolean close = false;

    public AudioJson101BO(String id,
                          String url,
                          String name,
                          String soundScript,
                          Integer duration,
                          boolean close,
                          SevenmGenderEnums gender,
                          Integer soundId,
                          Boolean needTranslation,
                          Integer coreVoiceConfigI18nId,
                          AudioCategoryEnums category) {
        this.id = id;
        this.url = url;
        this.name = name;
        this.soundScript = soundScript;
        this.duration = duration;
        this.close = close;
        this.gender = gender;
        this.soundId = soundId;
        this.needTranslation = needTranslation;
        this.coreVoiceConfigI18nId = coreVoiceConfigI18nId;
        this.category = category;
    }

    public static AudioJson101BO getSoundByName(AudioCategoryEnums category, String name, SevenmGenderEnums gender) {

        LambdaQueryWrapper<ProjSevenmSound> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjSevenmSound::getSoundName, name).last("limit 1");
        IProjSevenmSoundService sevenmSoundService = SpringUtil.getBean(IProjSevenmSoundService.class);
        FileService fileService = SpringUtil.getBean(FileService.class);

        ProjSevenmSound sound = sevenmSoundService.getOne(queryWrapper);
        if (sound == null) {
            throw new BizException("System sound '" + category + "' not find!");
        }

        String soundUrl = sound.getUrl();
        BizExceptionUtil.throwIf(StringUtils.isBlank(soundUrl),
                "System {} sound '{}' not set",gender.getName(),category);
        String soundName = FireBaseUrlSubUtils.getFileName(soundUrl);

        return new AudioJson101BO(soundName,
                fileService.getAbsoluteR2Url(soundUrl),
                soundName,
                sound.getSoundScript(),
                sound.getDuration(),
                false,
                gender,
                sound.getId(),
                sound.getNeedTranslation(),
                sound.getCoreVoiceConfigI18nId(),
                category);

    }

    public static List<AudioJson101BO> getAudioList(List<String> audioList, AudioCategoryEnums category, SevenmGenderEnums gender) {
        return audioList.stream().map(audioName -> getSoundByName(category, audioName, gender)).collect(Collectors.toList());
    }

    public AudioJson101BO createByGender(SevenmGenderEnums gender){
        return new AudioJson101BO(category, id, gender, url, name, close, time);
    }

    public AudioJson101BO(AudioCategoryEnums category, String id, SevenmGenderEnums gender, String url,
                          String name, boolean close, BigDecimal time) {
        this.category = category;
        this.id = id;
        this.gender = gender;
        this.url = url;
        this.name = name;
        this.close = close;
        this.time = time;
    }
}
