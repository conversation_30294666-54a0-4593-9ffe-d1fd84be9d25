package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutImage;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageListReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutImageUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageExportVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutImageVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IProjSevenmWorkoutImageService extends IService<ProjSevenmWorkoutImage> {

    void add(ProjSevenmWorkoutImageAddReq req);

    void updateImg(ProjSevenmWorkoutImageUpdateReq req);

    void del(List<Integer> ids);

    void updateEnable(List<Integer> idList, boolean b);

    List<ProjSevenmWorkoutImageVO> list(ProjSevenmWorkoutImageListReq req);

    List<ProjSevenmWorkoutImageExportVO> listExportVO(ProjSevenmWorkoutImageListReq req);

    List<String> importExcel(Integer projId,MultipartFile excel);

    void updateSort(List<Integer> idList);

    ProjSevenmWorkoutImageVO detail(Integer id);
}
