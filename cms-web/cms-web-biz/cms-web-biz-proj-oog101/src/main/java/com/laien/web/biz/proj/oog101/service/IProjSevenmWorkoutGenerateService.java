package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.oog101.bo.SevenmWorkoutGenerateVideoBO;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplateTask;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmWorkoutGenerate;
import com.laien.web.biz.proj.oog101.request.ProjSevenmManualWorkoutGenerateM3u8Req;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutGeneratePageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmWorkoutGenerateUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmExerciseVideoPageVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGenerateDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmWorkoutGeneratePageVO;
import com.laien.web.frame.request.PageReq;
import com.laien.web.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * proj_sevenm_workout_generate 服务接口
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
public interface IProjSevenmWorkoutGenerateService extends IService<ProjSevenmWorkoutGenerate> {

    void generateWorkoutByTask(List<ProjSevenmTemplateTask> templateTasks, ProjInfo projInfo);

    //TODO 待接触
    /**
     *
     * @param workoutAndVideoList
     * @param languageList
     * @param soundBO 可为空
     * @return
     *//*
    BaseWorkoutBO generateBaseWorkout(List<BaseGenerateVideoBO> workoutAndVideoList, List<String> languageList, SevenmWorkoutGenerateVideoBO generateVideoBO, SevenmWorkoutGenerateSoundBO soundBO);
*/
    /**
     * 分页查询WorkoutGenerate列表
     *
     * @param req 查询条件
     * @return IPage<ProjSevenmWorkoutGeneratePageRes>
     */
    PageRes<ProjSevenmWorkoutGeneratePageVO> page(ProjSevenmWorkoutGeneratePageReq req);

    /**
     * 查询WorkoutGenerate详情
     * @param id WorkoutGenerate ID
     * @return
     */
    ProjSevenmWorkoutGenerateDetailVO findDetailById(Integer id);

    void update(ProjSevenmWorkoutGenerateUpdateReq workoutUpdateReq, Integer projId);

    /**
     * 分页查询健身视频列表
     *
     * @param pageReq 分页查询条件
     * @param id WorkoutGenerate ID
     * @return 分页结果
     */
    PageRes<ProjSevenmExerciseVideoPageVO> pageVideo(PageReq pageReq, Integer id);

    /**
     * 批量启用WorkoutGenerate
     *
     * @param idList 需要启用的ID列表
     */
    void updateEnableByIds(List<Integer> idList);

    /**
     * 批量禁用WorkoutGenerate
     *
     * @param idList 需要禁用的ID列表
     */
    void updateDisableByIds(List<Integer> idList);

    /**
     * 批量删除WorkoutGenerate
     *
     * @param idList 需要删除的ID列表
     */
    void deleteByIdList(List<Integer> idList);

    /**
     * 根据模板ID和任务ID查询WorkoutGenerate
     *
     * @param templateId 模板ID
     * @param taskId 任务ID
     * @return ProjSevenmWorkoutGenerate
     */
    ProjSevenmWorkoutGenerate getByTemplateIdAndTaskId(Integer templateId, Integer taskId);

    /**
     * 保存WorkoutGenerate
     *
     * @param workoutGenerate workoutGenerate
     * @return 保存后的ID
     */
    Integer saveWorkoutGenerate(ProjSevenmWorkoutGenerate workoutGenerate);

    /**
     * 更新WorkoutGenerate
     *
     * @param workoutGenerate workoutGenerate
     */
    void updateWorkoutGenerate(ProjSevenmWorkoutGenerate workoutGenerate);

    /**
     * 更新文件状态
     *
     * @param id ID
     * @param fileStatus 文件状态
     * @param failMessage 失败信息
     */
    void updateFileStatus(Integer id, Integer fileStatus, String failMessage);

    void generateM3u8Interrupt();

    /**
     * 生成m3u8异步
     * @param m3u8Req
     * @return
     */
    Integer generateM3u8ByQuery(ProjSevenmManualWorkoutGenerateM3u8Req m3u8Req);


    SevenmWorkoutGenerateVideoBO createWorkoutGenerateVideoBO(List<String> languageList);
}
