package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmFastingArticle;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleListReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleListVO;
import com.laien.web.frame.request.IdListReq;

import java.util.List;

/**
 * <p>
 * IProj7MFastingArticleService
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
public interface IProjSevenmFastingArticleService extends IService<ProjSevenmFastingArticle> {

    void save(ProjSevenmFastingArticleAddReq articleAddReq, Integer projId);

    void update(ProjSevenmFastingArticleUpdateReq articleUpdateReq, Integer projId);

    ProjSevenmFastingArticleDetailVO findDetailById(Integer id);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    void deleteByIds(List<Integer> idList);

    List<ProjSevenmFastingArticleListVO> list(ProjSevenmFastingArticleListReq articleListReq, Integer projId);

    void sort(IdListReq idListReq);
}
