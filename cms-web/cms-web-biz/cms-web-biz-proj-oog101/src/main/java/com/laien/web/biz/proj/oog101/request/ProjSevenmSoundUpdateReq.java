package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "SevenmSound 编辑", description = "SevenmSound 编辑")
public class ProjSevenmSoundUpdateReq extends ProjSevenmSoundAddReq{

    @ApiModelProperty(value = "id")
    private Integer id;
}
