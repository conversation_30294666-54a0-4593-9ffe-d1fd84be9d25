package com.laien.web.biz.proj.oog101.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmPlaylistRelation;
import com.laien.web.biz.proj.oog101.response.ProjSevenmPlaylistRelationVO;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public interface ProjSevenmPlaylistRelationMapper extends BaseMapper<ProjSevenmPlaylistRelation> {


    @Select(value = "select relation.proj_sevenm_playlist_id, relation.proj_sevenm_music_id, relation.proj_id, relation.display_name, " +
            "relation.subscription, relation.short_link, music.music_name, music.music_type \n" +
            "from proj_sevenm_playlist_relation relation \n" +
            "inner join proj_sevenm_music music on relation.proj_sevenm_music_id = music.id \n" +
            "where relation.proj_sevenm_playlist_id = #{playlistId} and relation.del_flag = 0")
    List<ProjSevenmPlaylistRelationVO> listByPlaylistId(Integer playlistId);

}
