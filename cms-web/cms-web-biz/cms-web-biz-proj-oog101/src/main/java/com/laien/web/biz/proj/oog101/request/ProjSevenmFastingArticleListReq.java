package com.laien.web.biz.proj.oog101.request;

import com.laien.common.oog101.enums.SevenmFastingArticleEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * fasting article对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value="fasting article对象", description="fasting article对象")
public class ProjSevenmFastingArticleListReq {

    @ApiModelProperty(value = "动作展示名称")
    private String titleName;

    @ApiModelProperty(value = "类型")
    private SevenmFastingArticleEnums type;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
