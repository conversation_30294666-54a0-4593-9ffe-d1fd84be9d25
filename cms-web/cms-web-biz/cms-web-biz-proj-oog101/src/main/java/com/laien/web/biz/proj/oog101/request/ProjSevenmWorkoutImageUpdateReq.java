/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog101.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 描述: workout image 修改
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "workout image 修改", description = "workout image 修改")
public class ProjSevenmWorkoutImageUpdateReq extends ProjSevenmWorkoutImageAddReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;
}
