package com.laien.web.biz.proj.oog101.bo;

import cn.hutool.extra.spring.SpringUtil;
import com.laien.common.oog101.enums.AudioCategoryEnums;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.web.biz.core.util.FireBaseUrlSubUtils;
import com.laien.web.common.file.service.FileService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2025/3/20 13:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class SevenmVideoSoundI18nBO {

    private Integer id;
    private String name;
    private String nameUrl;
    private Integer duration;

    private AudioJson101BO nameAudioJson;
    private AudioJson101BO guidanceAudioJson;

    private String guidance;
    private String guidanceUrl;
    private Integer guidanceDuration;

    private SevenmGenderEnums gender;
    private Integer coreVoiceConfigI18nId;


    public AudioJson101BO nameAudioJson101BO() {
        return createAudioJson101BO(name,nameUrl,duration,AudioCategoryEnums.NAME,gender,coreVoiceConfigI18nId);
    }

    public AudioJson101BO guidanceAudioJson101BO() {
        return createAudioJson101BO(guidance,guidanceUrl,guidanceDuration,AudioCategoryEnums.GUIDANCE,gender,coreVoiceConfigI18nId,true);
    }

    private AudioJson101BO createAudioJson101BO(String name, String url, Integer duration, AudioCategoryEnums category,
                                                SevenmGenderEnums gender, Integer coreVoiceConfigI18nId) {
        FileService fileService = SpringUtil.getBean(FileService.class);
        String soundName = FireBaseUrlSubUtils.getFileName(url);
        return new AudioJson101BO(soundName,
                fileService.getAbsoluteR2Url(url),
                soundName,
                name,
                duration,
                false,
                gender,
        null,
                true,
                coreVoiceConfigI18nId,
                category);
    }

    private AudioJson101BO createAudioJson101BO(String name, String url, Integer duration, AudioCategoryEnums category,
                                                SevenmGenderEnums gender, Integer coreVoiceConfigI18nId, boolean close) {
        AudioJson101BO audioJson101BO = createAudioJson101BO(name, url, duration, category,gender,coreVoiceConfigI18nId);
        audioJson101BO.setClose(close);
        return audioJson101BO;
    }

}
