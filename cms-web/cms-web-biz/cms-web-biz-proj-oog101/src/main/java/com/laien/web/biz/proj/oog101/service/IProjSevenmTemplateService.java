package com.laien.web.biz.proj.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmTemplate;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplateGenerateReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplateGenerateWorkoutReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplatePageReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmTemplateUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplateDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmTemplatePageVO;
import com.laien.web.frame.response.PageRes;

import java.util.List;

public interface IProjSevenmTemplateService extends IService<ProjSevenmTemplate> {

    Integer generate(ProjSevenmTemplateGenerateReq req);

    PageRes<ProjSevenmTemplatePageVO> pageQuery(ProjSevenmTemplatePageReq req);

    ProjSevenmTemplateDetailVO detail(Integer id);

    void generateWorkout(ProjSevenmTemplateGenerateWorkoutReq req) throws Exception;

    void updateEnable(List<Integer> req,Boolean isEnable);

    void delete(List<Integer> idList);

    void update(ProjSevenmTemplateUpdateReq req);
}
