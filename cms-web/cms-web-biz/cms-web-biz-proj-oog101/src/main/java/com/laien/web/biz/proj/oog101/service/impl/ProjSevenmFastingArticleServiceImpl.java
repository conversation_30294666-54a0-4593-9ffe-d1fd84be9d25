package com.laien.web.biz.proj.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.common.oog101.enums.SevenmFastingArticleEnums;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjLmsI18nService;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmFastingArticle;
import com.laien.web.biz.proj.oog101.mapper.ProjSevenmFastingArticleMapper;
import com.laien.web.biz.proj.oog101.mapstruct.ProjSevenmFastingArticleMapStruct;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleAddReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleListReq;
import com.laien.web.biz.proj.oog101.request.ProjSevenmFastingArticleUpdateReq;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.web.biz.proj.oog101.response.ProjSevenmFastingArticleListVO;
import com.laien.web.biz.proj.oog101.service.IProjSevenmFastingArticleService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import com.laien.web.frame.exception.BizException;
import com.laien.web.frame.request.IdListReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * fasting article 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSevenmFastingArticleServiceImpl extends
        ServiceImpl<ProjSevenmFastingArticleMapper, ProjSevenmFastingArticle>
        implements IProjSevenmFastingArticleService {

    private final ProjSevenmFastingArticleMapStruct mapStruct;
    private final ICoreTextTaskI18nService i18nService;
    private final IProjInfoService projInfoService;
    private final IProjLmsI18nService projLmsI18nService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ProjSevenmFastingArticleAddReq articleAddReq, Integer projId) {
        check(articleAddReq, null, projId);
        ProjSevenmFastingArticle article = mapStruct.toEntity(articleAddReq);
        article.setProjId(projId);
        article.setStatus(GlobalConstant.STATUS_DRAFT);
        save(article);
        projLmsI18nService.handleI18n(ListUtil.of(article), projInfoService.getById(projId));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ProjSevenmFastingArticleUpdateReq articleUpdateReq, Integer projId) {
        Integer id = articleUpdateReq.getId();
        ProjSevenmFastingArticle article = baseMapper.selectById(id);
        if (null == article) {
            throw new BizException("article not found");
        }
        check(articleUpdateReq, id, projId);
        ProjSevenmFastingArticle updateArticle = mapStruct.toEntity(articleUpdateReq);
        updateArticle.setId(id);
        updateArticle.setProjId(projId);
        updateById(updateArticle);
        projLmsI18nService.handleI18n(ListUtil.of(updateArticle), projInfoService.getById(projId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjSevenmFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmFastingArticle::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjSevenmFastingArticle::getStatus, GlobalConstant.STATUS_DRAFT, GlobalConstant.STATUS_DISABLE);
        wrapper.in(ProjSevenmFastingArticle::getId, idList);
        update(new ProjSevenmFastingArticle(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDisableByIds(List<Integer> idList) {

        LambdaUpdateWrapper<ProjSevenmFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmFastingArticle::getStatus, GlobalConstant.STATUS_DISABLE);
        wrapper.eq(ProjSevenmFastingArticle::getStatus, GlobalConstant.STATUS_ENABLE);
        wrapper.in(ProjSevenmFastingArticle::getId, idList);
        this.update(new ProjSevenmFastingArticle(), wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sort(IdListReq idListReq) {
        List<Integer> idList = idListReq.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<ProjSevenmFastingArticle> articleList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            ProjSevenmFastingArticle info = new ProjSevenmFastingArticle();
            info.setSorted(i)
                    .setId(idList.get(i));
            articleList.add(info);
        }
        updateBatchById(articleList);
    }

    @Override
    public List<ProjSevenmFastingArticleListVO> list(ProjSevenmFastingArticleListReq articleListReq, Integer projId) {
        String titleName = articleListReq.getTitleName();
        SevenmFastingArticleEnums type = articleListReq.getType();
        Integer status = articleListReq.getStatus();

        LambdaQueryWrapper<ProjSevenmFastingArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(titleName), ProjSevenmFastingArticle::getTitleName, titleName)
                .eq(null != type, ProjSevenmFastingArticle::getType, type)
                .eq(null != status, ProjSevenmFastingArticle::getStatus, status)
                .orderByAsc(ProjSevenmFastingArticle::getSorted)
                .orderByDesc(BaseModel::getId);
        List<ProjSevenmFastingArticle> articleList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(articleList)) {
            return new ArrayList<>();
        }
        return mapStruct.toVOList(articleList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LambdaUpdateWrapper<ProjSevenmFastingArticle> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjSevenmFastingArticle::getDelFlag, GlobalConstant.YES)
                .eq(ProjSevenmFastingArticle::getStatus, GlobalConstant.STATUS_DRAFT)
                .in(ProjSevenmFastingArticle::getId, idList);
        this.update(new ProjSevenmFastingArticle(), wrapper);
    }

    @Override
    public ProjSevenmFastingArticleDetailVO findDetailById(Integer id) {
        ProjSevenmFastingArticle article = baseMapper.selectById(id);
        if (null == article) {
            return null;
        }
        return mapStruct.toDetailVO(article);
    }

    private void check(ProjSevenmFastingArticleAddReq articleReq, Integer id, Integer projId) {
        LambdaQueryWrapper<ProjSevenmFastingArticle> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmFastingArticle::getTitleName, articleReq.getTitleName())
                .eq(ProjSevenmFastingArticle::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        List<ProjSevenmFastingArticle> fastingArticleList = baseMapper.selectList(wrapper);
        Set<String> nameSet = fastingArticleList.stream().map(ProjSevenmFastingArticle::getTitleName).collect(Collectors.toSet());
        if (nameSet.contains(articleReq.getTitleName())) {
            String error = "title name already exists";
            throw new BizException(error);
        }
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmFastingArticle::getEventName, articleReq.getEventName())
                .eq(ProjSevenmFastingArticle::getProjId, projId)
                .ne(null != id, BaseModel::getId, id);
        fastingArticleList = baseMapper.selectList(wrapper);
        Set<String> eventNameSet= fastingArticleList.stream().map(ProjSevenmFastingArticle::getEventName).collect(Collectors.toSet());
        if (eventNameSet.contains(articleReq.getEventName())) {
            String error = "eventName already exists";
            throw new BizException(error);
        }
    }
}
