package com.laien.web.biz.proj.oog101.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.common.core.converter.GenericEnumNameConverter;
import com.laien.common.oog101.enums.*;
import com.laien.web.biz.core.converter.*;
import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  7M Exercise video导入
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@ApiModel(value = "7M Exercise video导入", description = "7M Exercise video导入")
public class ProjSevenmExerciseVideoImportReq {

    @ExcelProperty(value = "Id",converter = IntegerStringTrimConverter.class)
    @ApiModelProperty(value = "数据id")
    private Integer id;

    @NotEmpty(message = "video Name cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Name", converter = StringStringTrimConverter.class)
    @Length(max = 100, message = "name length must under 100", groups = Group1.class)
    @ApiModelProperty(value = "动作名称")
    private String name;

    @NotEmpty(message = "English Voice Name cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "English Voice Name", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "coreVoiceConfigI18nName")
    private String coreVoiceConfigI18nName;

    @NotEmpty(message = "imageUrl cannot be empty", groups = Group1.class)
    @ExcelProperty(value = "Image URL", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @NotNull(message = "Type cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Type", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "动作类型 Warm up,Main,Cool Down")
    private SevenmTypeEnums type;

    @NotNull(message = "Exercise Type cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Exercise Type", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "exerciseType")
    private SevenmExerciseTypeEnums exerciseType;

    @NotNull(message = "Difficulty cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Difficulty", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "动作难度 Beginner,Intermediate,Advanced")
    private SevenmDifficultyEnums difficulty;

    @NotEmpty(message = "Target cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Target", converter = GenericEnumListNameConverter.class)
    @ApiModelProperty(value = "目标 多选逗号分隔 Full Body、Arm、Back、Butt、Abs、Legs、Core")
    private List<SevenmTargetEnums> target;

    @ExcelProperty(value = "Special Limit",converter = GenericEnumListNameConverter.class)
    @ApiModelProperty(value = "特殊人群不可使用的 数组 None、Shoulder、Back、Wrist、Knee、Ankle、Hip、Belly")
    private List<SevenmSpecialLimitEnums> specialLimit;


    @NotNull(message = "Intensity cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Intensity", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "Intensity: None,Stretch,Cardio,Hiit,Power")
    private SevenmIntensityEnums intensity;

    @NotNull(message = "equipment cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Equipment",converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "equipment: None,Dumbbells,Yoga mat")
    private SevenmEquipmentEnums equipment;

    @NotNull(message = "Position cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Position", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "动作体位 Standing、Seated、Lying、Prone、Kneeling、Sitting")
    private SevenmPositionEnums position;


    @NotNull(message = "Gender cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Gender", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "性别 female male")
    private SevenmGenderEnums gender;

    @NotNull(message = "videoDirection cannot be empty or wrong value", groups = Group1.class)
    @ExcelProperty(value = "Video Direction", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "videoDirection：Central、Left、Right")
    private SevenmVideoDirectionEnums videoDirection;

    @ExcelProperty(value = "Guidance", converter = StringStringTrimConverter.class)
    @NotEmpty(message = "Guidance cannot be empty", groups = Group1.class)
    @Length(max = 1000, message = "Guidance length must under 1000", groups = Group1.class)
    @ApiModelProperty(value = "指导文本 (500字符限制)")
    private String guidance;

    @ExcelProperty(value = "How To Do", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "如何做 (1000字符限制)")
    @Length(max = 1000, message = "howToDo length must under 1000", groups = Group1.class)
    private String howToDo;

    @ExcelProperty(value = "Front Video URL", converter = StringStringTrimConverter.class)
    @NotEmpty(message = "Front Video URL cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "正机位TS视频地址")
    private String frontVideoUrl;

    @ExcelProperty(value = "Front Video Duration",converter = IntegerStringTrimConverter.class)
    @NotNull(message = "Front Video Duration cannot be null", groups = Group1.class)
    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ExcelProperty(value = "Side Video URL", converter = StringStringTrimConverter.class)
    @NotEmpty(message = "Side Video URL cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "侧机位TS视频地址")
    private String sideVideoUrl;

    @ExcelProperty(value = "Side Video Duration",converter = IntegerStringTrimConverter.class)
    @NotNull(message = "Side Video Duration cannot be null", groups = Group1.class)
    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ExcelProperty(value = "Name Audio URL", converter = StringStringTrimConverter.class)
    @NotEmpty(message = "Name Audio URL cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "名称音频 (mp3格式)")
    private String nameAudioUrl;

    @ExcelProperty(value = "Name Audio Duration",converter = IntegerStringTrimConverter.class)
    @NotNull(message = "Name Audio Duration cannot be null", groups = Group1.class)
    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ExcelProperty(value = "Guidance Audio URL", converter = StringStringTrimConverter.class)
    @NotEmpty(message = "Guidance Audio URL cannot be empty", groups = Group1.class)
    @ApiModelProperty(value = "指导音频 (mp3格式)")
    private String guidanceAudioUrl;

    @ExcelProperty(value = "Guidance Audio Duration",converter = IntegerStringTrimConverter.class)
    @NotNull(message = "Guidance Audio Duration cannot be null", groups = Group1.class)
    @ApiModelProperty(value = "指导音频时长")
    private Integer guidanceAudioDuration;

    @ExcelProperty(value = "How To Do Audio URL", converter = StringStringTrimConverter.class)
    @ApiModelProperty(value = "如何做音频 (mp3格式)")
    private String howToDoAudioUrl;

    @ExcelProperty(value = "How To Do Audio Duration",converter = IntegerStringTrimConverter.class)
    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ExcelProperty(value = "MET", converter = IntegerStringTrimConverter.class)
    @NotNull(message = "MET code cannot be null", groups = Group1.class)
    @ApiModelProperty(value = "MET code (1-15)")
    private Integer met;

    @ExcelProperty(value = "Calorie", converter = BigDecimalStringTrimConverter.class)
    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @ExcelProperty(value = "Is Used For Auto", converter = IntegerStringTrimConverter.class)
    @NotNull(message = "Is Used For Auto code cannot be null", groups = Group1.class)
    @Min(value = 0, message = "Is Used For Auto 必须在 0-1 之间", groups = Group1.class)
    @Max(value = 1, message = "Is Used For Auto 必须在 0-1 之间", groups = Group1.class)
    @ApiModelProperty(value = "是否参与自动生成 1-是 0-否")
    private Integer usedForAuto;

}
