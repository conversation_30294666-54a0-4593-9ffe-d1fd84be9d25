/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.web.biz.proj.oog101.response;

import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *  sevenm_workout_image VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "imageVo", description = "imageVo")
public class ProjSevenmWorkoutImageVO {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("图片名称")
    private String name;

    @ApiModelProperty("锻炼部位")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty("封面图片地址")
    private String coverImage;

    @ApiModelProperty("详情图片地址")
    private String detailImage;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Integer projId;

    @ApiModelProperty("排序字段")
    private Integer sortNo;


}
