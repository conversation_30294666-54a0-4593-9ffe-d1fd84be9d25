package com.laien.web.biz.proj.oog101.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.biz.proj.oog101.entity.ProjSevenmManualWorkout;
import com.laien.web.frame.response.IdAndStatusCountsRes;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ProjSevenmManualWorkoutMapper extends BaseMapper<ProjSevenmManualWorkout> {
    /**
     * 统计workout关联的视频数量
     *
     * @param workoutIds workout ID列表
     * @return 统计结果
     */
    @Select("<script>" +
            "SELECT " +
            "  w.id as id, " +
            "  COUNT(DISTINCT v.id) as counts, " +
            "  v.status as status " +
            "FROM proj_sevenm_manual_workout w " +
            "LEFT JOIN proj_sevenm_manual_workout_exercise_video wv ON w.id = wv.proj_sevenm_manual_workout_id " +
            "LEFT JOIN proj_sevenm_exercise_video v ON wv.proj_sevenm_exercise_video_id = v.id " +
            "WHERE w.id IN " +
            "  <foreach item='id' index='index' collection='workoutIds' open='(' separator=',' close=')'>#{id}</foreach> " +
            "GROUP BY w.id, v.status" +
            "</script>")
    List<IdAndStatusCountsRes> countVideosByWorkoutIds(@Param("workoutIds") List<Integer> workoutIds);
}
