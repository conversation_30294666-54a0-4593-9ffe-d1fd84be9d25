package com.laien.web.biz.proj.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.oog101.enums.SevenmMusicTypeEnums;
import com.laien.common.oog101.enums.TableCodeEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音乐表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjSevenmMusic对象", description="音乐表")
public class ProjSevenmMusic extends BaseModel {

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_MUSIC;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "音频")
    private String audio;

    @ApiModelProperty(value = "音频总时长,毫秒")
    private Integer audioDuration;

    @ApiModelProperty(value = "音乐类型（关联字典表）")
    private SevenmMusicTypeEnums musicType;

    @ApiModelProperty(value = "讲述者，用于Meditation类型")
    private String instructor;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "Music状态 0草稿 1启用 2停用")
    private Integer status;

    private Integer projId;

}
