<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cms-web</artifactId>
        <groupId>com.laien</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cms-web-common</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>cms-web-common-core</module>
        <module>cms-web-common-file</module>
        <module>cms-web-common-user</module>
        <module>cms-web-common-m3u8-seq</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-frame</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-common-file</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-common-user</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.laien</groupId>
                <artifactId>cms-web-common-m3u8-seq</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
