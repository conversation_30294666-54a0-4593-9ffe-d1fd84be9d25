package com.laien.web.common.m3u8.seq.dto;

import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
@Data
public class TaskResourceSectionCallBackDTO {
    private Integer delayTime;
    private Integer executionTime;
    private String error;
    /**
     * run pod的jobId
     */
    private String id;
    private TaskResourceSectionRunPodDTO input;
    private Output output;
    private TaskResourceSectionStatusEnums status;
    private String webhook;


    @Data
    public static class Output {
        private Integer id;
        private String m3u8Text2K;
        private String m3u8Text1080P;
        private String m3u8Text720P;
        private String m3u8Text480P;
        private String m3u8Text360P;
        private String m3u8Url;
        private Integer duration;

        private String m3u8TextOriginal;
        private String m3u8UrlOriginal;
        private String compressionTs;
        private String compressionM3u8;
    }
}
