package com.laien.web.common.m3u8.seq.enums;

import com.laien.web.common.m3u8.seq.callback.IM3u8SeqCallback;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@Getter
@AllArgsConstructor
public enum M3u8CallbackTypeEnums {

    COMPLETED(TaskResourceSectionStatusEnums.COMPLETED, (callback, b) -> {
        callback.completed(b);
        return null;
    }),

    FAILED(TaskResourceSectionStatusEnums.NOT_RETRY, (callback, b) -> {
        callback.failed(b);
        return null;
    });

    private TaskResourceSectionStatusEnums status;

    private BiFunction<IM3u8SeqCallback, TaskResourceSection, Void> function;

}
