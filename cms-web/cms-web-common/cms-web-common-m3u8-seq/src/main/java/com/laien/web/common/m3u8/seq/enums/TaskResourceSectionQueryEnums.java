package com.laien.web.common.m3u8.seq.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Getter
public enum TaskResourceSectionQueryEnums {

    PROJ_FITNESS_VIDEO_SIDE("proj_fitness_video", "sideVideoMp4Url"),
    PROJ_FITNESS_VIDEO_FRONT("proj_fitness_video", "frontVideoMp4Url"),

    RESVIDEO_120S_SIDE("res_video120s", "sideVideoUrl"),
    RESVIDEO_120S_FRONT("res_video120s", "frontVideoUrl"),

    RESVIDEO_116_SIDE("res_video116", "sideVideoUrl"),
    RESVIDEO_116_FRONT("res_video116", "frontVideoUrl"),

    RES_VIDEO_106_SIDE("res_video106", "sideVideoUrl"),
    RES_VIDEO_106_FRONT("res_video106", "frontVideoUrl"),

    RES_YOGA_VIDEO_SIDE("res_yoga_video", "sideVideoUrl"),
    RES_YOGA_VIDEO_FRONT("res_yoga_video", "frontVideoUrl"),

    RES_TRANSITION_SIDE("res_transition", "sideVideoUrl"),
    RES_TRANSITION_FRONT("res_transition", "frontVideoUrl"),

    PROJ_YOGA_POSE_VIDEO_SIDE("proj_yoga_pose_video", "sideVideoUrl"),
    PROJ_YOGA_POSE_VIDEO_FRONT("proj_yoga_pose_video", "frontVideoUrl"),

    PROJ_WALL_PILATES_VIDEO_RESOURCE_FRONT("proj_wall_pilates_video_resource", "frontVideoUrl"),
    PROJ_WALL_PILATES_VIDEO_RESOURCE_SIDE("proj_wall_pilates_video_resource", "sideVideoUrl"),

    PROJ_CHAIR_YOGA_VIDEO_SIDE("proj_chair_yoga_video_slice", "sideVideoUrl"),
    PROJ_CHAIR_YOGA_VIDEO_FRONT("proj_chair_yoga_video_slice", "frontVideoUrl"),

    PROJ_YOGA_POSE_TRANSITION_FRONT("proj_yoga_pose_transition", "frontVideoUrl"),

    RES_VIDEO116_SLICE_SIDE("res_video116_slice", "sideVideoUrl"),
    RES_VIDEO116_SLICE_FRONT("res_video116_slice", "frontVideoUrl"),

    PROJ_DISH("proj_dish", "resourceVideoUrl"),
    PROJ_FITNESS_DISH("proj_fitness_dish", "resourceVideoUrl"),
    PROJ_FITNESS_VIDEO_COURSE("proj_fitness_video_course", "resourceVideoUrl"),

    ;

    private final String tableName;

    private final String entityFieldName;

    TaskResourceSectionQueryEnums(String tableName, String entityFieldName) {
        this.tableName = tableName;
        this.entityFieldName = entityFieldName;
    }
}
