package com.laien.web.common.m3u8.seq.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import com.laien.web.common.core.util.JacksonUtil;
import com.laien.web.common.file.config.FileUploadConfig;
import com.laien.web.common.file.config.R2Config;
import com.laien.web.common.file.service.FileService;
import com.laien.web.common.m3u8.seq.annotation.ResourceSection;
import com.laien.web.common.m3u8.seq.callback.IM3u8SeqCallback;
import com.laien.web.common.m3u8.seq.config.RunPodConfig;
import com.laien.web.common.m3u8.seq.dto.*;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;
import com.laien.web.common.m3u8.seq.enums.CompressionTsParamsEnums;
import com.laien.web.common.m3u8.seq.enums.M3u8CallbackTypeEnums;
import com.laien.web.common.m3u8.seq.enums.SqlOperateEnums;
import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.common.m3u8.seq.mapper.TaskResourceSectionMapper;
import com.laien.web.common.m3u8.seq.service.ITaskResourceSectionService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.entity.BaseModel;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源切片任务表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Slf4j
@Service
public class TaskResourceSectionServiceImpl extends ServiceImpl<TaskResourceSectionMapper, TaskResourceSection> implements ITaskResourceSectionService {


    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private RunPodConfig runPodConfig;

    @Resource
    private FileUploadConfig fileUploadConfig;

    @Resource
    private FileService fileService;

    @Value("${firebase.project}")
    private String projectDirectory;

    @Resource
    private R2Config r2Config;

    private static final int maxRetryCount = 3;

    @Resource
    private OkHttpClient client;

    private final Set<Class<?>> ignoreClasses = new ConcurrentHashSet<>();

    @Override
    public <T> boolean oldResourceSection(Class<T> tClass) {
        final Map<String, IService> beansOfType = applicationContext.getBeansOfType(IService.class);
        if (CollUtil.isNotEmpty(beansOfType)) {
            IService targetService = null;
            for (IService cService : beansOfType.values()) {
                final Class genericSuperclass = (Class) (cService.getClass().getGenericSuperclass());
                final Class genericInterfaceClass = (Class) genericSuperclass.getGenericInterfaces()[0];
                final ParameterizedType genericInterface = (ParameterizedType) genericInterfaceClass.getGenericInterfaces()[0];
                final Class actualTypeArguments0Class = (Class) genericInterface.getActualTypeArguments()[0];
                if (actualTypeArguments0Class == tClass) {
                    targetService = cService;
                    break;
                }
            }
            if (null != targetService) {
                LambdaQueryWrapper<BaseModel> wrapper = new LambdaQueryWrapper<>();
                wrapper.last("and 1 = 1 or del_flag = 1");
                List<BaseModel> list = targetService.list(wrapper);
                if (CollectionUtil.isEmpty(list)) {
                    return true;
                }
                for (BaseModel entity : list) {
                    EntityEventDTO eventDTO = new EntityEventDTO(entity, SqlOperateEnums.OLD_RESOURCE_SECTION);
                    sendTask(eventDTO);
                }
                return true;
            }
        }
        return false;
    }

    @Override
    public void sendTask(EntityEventDTO event) {
        BaseModel entity = event.getEntity();
        if (ignoreClasses.contains(entity.getClass())) {
            return;
        }
        boolean ignore = true;
        // 通过反射获取被自定义注解标记的字段
        for (Field field : entity.getClass().getDeclaredFields()) {
            ResourceSection annotation = field.getAnnotation(ResourceSection.class);
            if (null == annotation) {
                continue;
            }
            ignore = false;

            field.setAccessible(true);
            String value = null;
            try {
                value = (String) field.get(entity);
            } catch (IllegalAccessException e) {
                log.warn("get field value failed, field:{}", field, e);
            }
            if (StringUtils.isBlank(value)) {
                return;
            }

            String tableName = annotation.tableName();
            if (StringUtils.isBlank(tableName)) {
                log.error("resource section tableName is null");
                return;
            }
            String dirKey = annotation.dirKey();
            if (StringUtils.isBlank(dirKey)) {
                log.error("resource section dirKey is null");
                return;
            }
            String fieldName = field.getName();
            SqlOperateEnums operate = event.getOperate();
            if (SqlOperateEnums.INSERT != operate) {
                TaskResourceSection taskResourceSection = findByTableId(tableName, entity.getId(), fieldName, TaskResourceSectionStatusEnums.END_STATUS_LIST);
                if (null != taskResourceSection) {
                    if (taskResourceSection.getResourceUrl().equals(value)) {
                        writeBackData(taskResourceSection);
                        continue;
                    }

                }

            }
            String catalogue = fileUploadConfig.getFileDirs().get(dirKey);
            LambdaQueryWrapper<TaskResourceSection> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskResourceSection::getTableName, tableName).eq(TaskResourceSection::getTableId, entity.getId()).eq(TaskResourceSection::getEntityFieldName, fieldName);
            TaskResourceSection task = baseMapper.selectOne(wrapper);
            task = null == task ? new TaskResourceSection() : task;
            task.setTableName(tableName).setEntityFieldName(fieldName).setM3u8UrlColumn(annotation.m3u8UrlColumn()).setM3u8Text2kColumn(annotation.m3u8Text2kColumn()).setM3u8Text1080pColumn(annotation.m3u8Text1080pColumn()).setM3u8Text720pColumn(annotation.m3u8Text720pColumn()).setM3u8Text480pColumn(annotation.m3u8Text480pColumn()).setM3u8Text360pColumn(annotation.m3u8Text360pColumn()).setDurationColum(annotation.durationColum()).setResourceUrl(value).setCatalogue(projectDirectory + catalogue).setTableId(entity.getId()).setStatus(TaskResourceSectionStatusEnums.PENDING).setM3u8Text2k("").setM3u8Text1080p("").setM3u8Text720p("").setM3u8Text480p("").setM3u8Text360p("").setM3u8Text2532Column(annotation.m3u8Text2532Column()).setM3u8Url2532Column(annotation.m3u8Url2532Column());
            task.setCompressionTsColumn(annotation.compressionTsColumn())
                    .setCompressionM3u8Column(annotation.compressionM3u8Column())
                    .setFurtherCompression(annotation.furtherCompression() ? GlobalConstant.YES : GlobalConstant.NO);
            CompressionTsParamsEnums compressionTsParamsEnums = annotation.compressionParams();
            if (compressionTsParamsEnums != CompressionTsParamsEnums.VIDEO_DEFAULT) {
                task.setBitRate(compressionTsParamsEnums.getBitRate())
                        .setMaxBitRate(compressionTsParamsEnums.getMaxBitRate())
                        .setBufSize(compressionTsParamsEnums.getBufSize());
            }
            if (annotation.callback() != null && annotation.callback() != IM3u8SeqCallback.class) {
                task.setCallBackClass(annotation.callback().getName());
            }
            saveOrUpdate(task);
            RunPodResponseDTO res = sendTask(task);
            if (null != res && null != res.getStatus()) {
                task.setJobId(res.getId()).setStatus(res.getStatus());
                updateById(task);
            }
        }
        if (ignore) {
            ignoreClasses.add(entity.getClass());
        }
    }

    @Override
    @Transactional
    public void callBack(TaskResourceSectionCallBackDTO req) {
        log.info("-----------------------------------回调切片任务,req:{}", req);
        TaskResourceSectionRunPodDTO input = req.getInput();
        Integer id = input.getId();
        TaskResourceSection task = baseMapper.selectById(id);
        TaskResourceSectionStatusEnums status = req.getStatus();
        TaskResourceSectionCallBackDTO.Output output = req.getOutput();
        String error = req.getError();
        if (StringUtils.isNotBlank(error)) {
            task.setStatus(TaskResourceSectionStatusEnums.NOT_RETRY);
            task.setMessage(error);
            taskResultListenerCallback(task);
            baseMapper.updateById(task);
            return;
        }

        if (null == output) {
            task.setStatus(TaskResourceSectionStatusEnums.NEED_RETRY);
            if (task.getRetryCount() >= maxRetryCount) {
                task.setStatus(TaskResourceSectionStatusEnums.NOT_RETRY);
            }
            task.setMessage(error);
            taskResultListenerCallback(task);
            baseMapper.updateById(task);
            return;
        }

        task.setDuration(output.getDuration());
        String jobId = req.getId();
        if (StringUtils.isBlank(jobId) || !jobId.equals(task.getJobId())) {
            log.warn("切片任务id:{}回调jobId:{}与数据库jobId:{}不一致,req:{}", id, jobId, task.getJobId(), req);
            return;
        }

        if (status != TaskResourceSectionStatusEnums.COMPLETED) {
            task.setStatus(status);
            task.setMessage(error);
            baseMapper.updateById(task);
            if (status == TaskResourceSectionStatusEnums.VERIFICATION_FAILED) {
                log.error("切片任务id:{}审核失败,req:{}", id, req);
            } else {
                log.info("切片任务id:{}未完成,req:{}", id, req);
            }
            return;
        }
        finish(output, task, status);
    }

    private void finish(TaskResourceSectionCallBackDTO.Output output, TaskResourceSection task, TaskResourceSectionStatusEnums status) {
        String m3u8Url = output.getM3u8Url();
        String m3u8Text2k = output.getM3u8Text2K();
        String m3u8Text1080p = output.getM3u8Text1080P();
        String m3u8Text720p = output.getM3u8Text720P();
        String m3u8Text480p = output.getM3u8Text480P();
        String m3u8Text360p = output.getM3u8Text360P();

        String m3u8TextOriginal = output.getM3u8TextOriginal();
        String m3u8UrlOriginal = output.getM3u8UrlOriginal();
        task.setStatus(status).setMessage("").setM3u8Url(m3u8Url).setM3u8Text2k(m3u8Text2k).setM3u8Text1080p(m3u8Text1080p).setM3u8Text720p(m3u8Text720p).setM3u8Text480p(m3u8Text480p).setM3u8Text360p(m3u8Text360p).setM3u8Text2532(m3u8TextOriginal).setM3u8Url2532(m3u8UrlOriginal);
        task.setCompressionTs(output.getCompressionTs()).setCompressionM3u8(output.getCompressionM3u8());
        baseMapper.updateById(task);
        writeBackData(task);
        //增加回调逻辑
        taskResultListenerCallback(task);
    }

    private void taskResultListenerCallback(TaskResourceSection task) {
        if (task.getCallBackClass() != null) {
            final TaskResourceSectionStatusEnums status = task.getStatus();
            final M3u8CallbackTypeEnums m3u8CallbackTypeEnums = EnumUtil.getBy(M3u8CallbackTypeEnums::getStatus, status);
            if (m3u8CallbackTypeEnums != null) {
                try {
                    final Class<? extends IM3u8SeqCallback> aClass = (Class<? extends IM3u8SeqCallback>) Class.forName(task.getCallBackClass());
                    IM3u8SeqCallback m3u8SeqCallback = null;
                    try {
                        m3u8SeqCallback = SpringUtil.getBean(aClass);
                    } catch (Exception e) {
                        m3u8SeqCallback = aClass.newInstance();
                    }
                    m3u8CallbackTypeEnums.getFunction().apply(m3u8SeqCallback, task);
                } catch (Exception e) {
                    log.error("m3u8 seq callback fail", e);
                }
            }
        }
    }

    @Scheduled(fixedDelay = 1000 * 60 * 3)
    public void retryTaskList() {
        List<TaskResourceSection> retryList = timeoutRetry();
        Set<Integer> taskIdSet = new HashSet<>(64);
        if (CollUtil.isNotEmpty(retryList)) {
            taskIdSet.addAll(retryList.stream().map(BaseModel::getId).collect(Collectors.toSet()));
        }
        statusRetry(taskIdSet);
    }


    /**
     * 向run pod发送任务
     */
    @Override
    public RunPodResponseDTO sendTask(TaskResourceSection task) {
        TaskResourceSectionRunPodDTO req = new TaskResourceSectionRunPodDTO();
        String prefix = task.getCatalogue();
        //去掉bucketUrl最后一个"/"
        if (prefix.endsWith("/")) {
            prefix = prefix.substring(0, prefix.length() - 1);
        }
        String resourceUrl = task.getResourceUrl();
        if (resourceUrl.contains("?")) {
            resourceUrl = resourceUrl.substring(0, resourceUrl.indexOf("?"));
        }
        req.setId(task.getId()).setResourceUrl(fileService.getAbsoluteR2Url(resourceUrl)).setPrefix(prefix).setBucket(r2Config.getBucketName());
        req.setFurtherCompression(task.getFurtherCompression()).setBitRate(task.getBitRate()).setMaxBitRate(task.getMaxBitRate()).setBufSize(task.getBufSize());
        RunPodRequestDTO runPodRequestDTO = new RunPodRequestDTO();
        runPodRequestDTO.setInput(req);
        runPodRequestDTO.setWebhook(runPodConfig.getWebhook());
        // 构建请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JacksonUtil.toJsonString(runPodRequestDTO));
        // 构建请求
        Request request = new Request.Builder().url(runPodConfig.getBaseUrl() + "run").post(body).addHeader("Content-Type", "application/json").addHeader("Authorization", "Bearer " + runPodConfig.getApiKey()).build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("send task failed, code:{}, message:{},body:{}", response.code(), response.message(), JacksonUtil.toJsonString(runPodRequestDTO));
                return null;
            }
            assert response.body() != null;
            String bodyString = response.body().string();
            RunPodResponseDTO runPodResponseDTO = JSON.parseObject(bodyString, RunPodResponseDTO.class);
            TaskResourceSectionStatusEnums status = runPodResponseDTO.getStatus();
            if (null == status || status.getCode() >= TaskResourceSectionStatusEnums.COMPLETED.getCode()) {
                log.error("send task failed, runPodDTO:{},task:{}", runPodResponseDTO, task);
            }
            return runPodResponseDTO;
        } catch (Exception e) {
            log.error("send request to send task failed,task:{} ", task, e);
            return null;
        }
    }

    @Override
    public List<TaskResourceSection> find(String tableName, String fieldName, Set<Integer> tableIdSet) {
        LambdaQueryWrapper<TaskResourceSection> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(tableIdSet), TaskResourceSection::getTableId, tableIdSet).eq(TaskResourceSection::getTableName, tableName).eq(TaskResourceSection::getEntityFieldName, fieldName);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<TaskResourceSection> query(String tableName, String fieldName, List<Integer> tableIdList) {
        if (CollUtil.isEmpty(tableIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TaskResourceSection> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskResourceSection::getTableName, tableName).in(TaskResourceSection::getTableId, tableIdList);
        List<TaskResourceSection> taskList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(taskList)) {
            return new ArrayList<>();
        }
        return taskList;
    }

    private TaskResourceSection findByTableId(String tableName, Integer tableId, String entityFieldName, List<TaskResourceSectionStatusEnums> statusList) {
        LambdaQueryWrapper<TaskResourceSection> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskResourceSection::getTableName, tableName).eq(TaskResourceSection::getTableId, tableId).eq(TaskResourceSection::getEntityFieldName, entityFieldName).in(TaskResourceSection::getStatus, statusList);
        return baseMapper.selectOne(wrapper);
    }

    private void retryTask(TaskResourceSection task) {
        String oldJobId = task.getJobId();
        RunPodResponseDTO res = sendTask(task);
        if (null != res) {
            // 因为只有retryTaskList定时任务一个地方重试，所以直接在这里写了，就不写sql进行+1了
            task.setRetryCount(task.getRetryCount() + 1).setJobId(res.getId());
            baseMapper.updateById(task);
        }
        cancelTask(oldJobId);
    }


    /**
     * 检查任务状态
     */
    private TaskResourceSectionStatusEnums checkTask(String jobId) {
        // 构建请求
        Request request = new Request.Builder().url(runPodConfig.getBaseUrl() + "status/" + jobId).get().addHeader("Authorization", "Bearer " + runPodConfig.getApiKey()).build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("check task failed, code:{}, message:{},jobId:{}", response.code(), response.message(), jobId);
                return null;
            }
            assert response.body() != null;
            RunPodResponseDTO runPodResponseDTO = JSON.parseObject(response.body().string(), RunPodResponseDTO.class);
            return runPodResponseDTO.getStatus();
        } catch (Exception e) {
            log.error("check task failed,jobId:{} ", jobId, e);
            return null;
        }
    }

    /**
     * 删除任务
     */
    private void cancelTask(String jobId) {
        // 构建请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), new byte[0]);
        // 构建请求
        Request request = new Request.Builder().url(runPodConfig.getBaseUrl() + "cancel/" + jobId).post(body).addHeader("Content-Type", "application/json").addHeader("Authorization", "Bearer " + runPodConfig.getApiKey()).build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("cancel task failed, code:{}, message:{},jobId:{}", response.code(), response.message(), jobId);
            }
        } catch (Exception e) {
            log.error("cancel task failed,jobId:{} ", jobId, e);
        }
    }

    /**
     * 回写数据
     */
    private void writeBackData(TaskResourceSection task) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("update ").append(task.getTableName()).append(" set ");
            String m3u8UrlColumn = task.getM3u8UrlColumn();
            String m3u8Text2kColumn = task.getM3u8Text2kColumn();
            String m3u8Text1080pColumn = task.getM3u8Text1080pColumn();
            String m3u8Text720pColumn = task.getM3u8Text720pColumn();
            String m3u8Text480pColumn = task.getM3u8Text480pColumn();
            String m3u8Text360pColumn = task.getM3u8Text360pColumn();
            String durationColum = task.getDurationColum();
            String compressionTsColumn = task.getCompressionTsColumn();
            String compressionM3u8Column = task.getCompressionM3u8Column();
            Integer duration = task.getDuration();
            if (null == duration || duration <= 0) {
                log.warn("duration not can be null or less than 0,task:{}", task);
            }
            List<Object> params = new ArrayList<>();
            splicingColumn(m3u8Text2kColumn, sb, task.getM3u8Text2k(), params);
            splicingColumn(m3u8Text1080pColumn, sb, task.getM3u8Text1080p(), params);
            splicingColumn(m3u8Text720pColumn, sb, task.getM3u8Text720p(), params);
            splicingColumn(m3u8Text480pColumn, sb, task.getM3u8Text480p(), params);
            splicingColumn(m3u8Text360pColumn, sb, task.getM3u8Text360p(), params);
            splicingColumn(m3u8UrlColumn, sb, task.getM3u8Url(), params);
            String m3u8Text2532Column = task.getM3u8Text2532Column();
            splicingColumn(m3u8Text2532Column, sb, task.getM3u8Text2532(), params);
            String m3u8Url2532Column = task.getM3u8Url2532Column();
            splicingColumn(m3u8Url2532Column, sb, task.getM3u8Url2532(), params);
            splicingColumn(durationColum, sb, duration, params);
            splicingColumn(compressionTsColumn, sb, task.getCompressionTs(), params);
            splicingColumn(compressionM3u8Column, sb, task.getCompressionM3u8(), params);
            if (CollectionUtil.isEmpty(params)) {
                return;
            }
            sb.append(" where id = {").append(params.size()).append("}");
            params.add(task.getTableId());
            log.info("sql:{},params:{}", sb.toString(), params.toArray());
            boolean update = SqlRunner.db().update(sb.toString(), params.toArray());
            if (!update) {
                log.warn("write back data error, task:{}", task);
            }
        } catch (Exception e) {
            log.error("write back data exception, task:{}", task, e);
        }
    }

    private void splicingColumn(String textColumn, StringBuilder sb, Object text, List<Object> params) {
        if (StringUtils.isNotBlank(textColumn)) {
            if (CollectionUtil.isNotEmpty(params)) {
                sb.append(", ");
            }
            sb.append(textColumn).append(" = {").append(params.size()).append("}");
            params.add(text);
        }
    }

    private void statusRetry(Set<Integer> taskIdSet) {
        LambdaQueryWrapper<TaskResourceSection> wrapper = new LambdaQueryWrapper<>();
//        wrapper.in(TaskResourceSection::getStatus, Arrays.asList(PENDING, IN_QUEUE, IN_PROGRESS, FAILED, TIMED_OUT, VERIFICATION_FAILED, NEED_RETRY))
        wrapper.ne(TaskResourceSection::getStatus, TaskResourceSectionStatusEnums.COMPLETED)
                .ne(TaskResourceSection::getStatus, TaskResourceSectionStatusEnums.NOT_RETRY)
                .le(TaskResourceSection::getRetryCount, maxRetryCount)
                .notIn(CollUtil.isNotEmpty(taskIdSet), BaseModel::getId, taskIdSet);
        List<TaskResourceSection> taskList = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        for (TaskResourceSection task : taskList) {
            if (StringUtils.isBlank(task.getJobId())) {
                log.warn("task resource section jobId is null, task:{}", task);
                continue;
            }
            if (task.getStatus() == TaskResourceSectionStatusEnums.NEED_RETRY) {
                retryTask(task);
                continue;
            }
            TaskResourceSectionStatusEnums status = checkTask(task.getJobId());

            if (status != task.getStatus()) {
                //这里不应该再重试才对,但现阶段先不改他原有的callback统一回写逻辑
                if (TaskResourceSectionStatusEnums.COMPLETED == status) {
                    retryTask(task);
                    continue;
                }
                task.setStatus(status);
                baseMapper.updateById(task);
            }
            if (null == status || status.getCode() < TaskResourceSectionStatusEnums.CANCELLED.getCode()) {
                continue;
            }
            retryTask(task);
        }
    }

    /**
     * 执行超时重试
     */
    private List<TaskResourceSection> timeoutRetry() {
//        List<TaskResourceSectionStatusEnums> statusList = Collections.singletonList(IN_PROGRESS);
        LambdaQueryWrapper<TaskResourceSection> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(TaskResourceSection::getUpdateTime, System.currentTimeMillis() - 1000 * 60 * 15)
                .ne(TaskResourceSection::getStatus, TaskResourceSectionStatusEnums.NOT_RETRY)
                .ne(TaskResourceSection::getStatus, TaskResourceSectionStatusEnums.COMPLETED)
                .lt(TaskResourceSection::getRetryCount, maxRetryCount);
        List<TaskResourceSection> taskList = baseMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(taskList)) {
            for (TaskResourceSection task : taskList) {
                Integer retryCount = task.getRetryCount();
                if (retryCount < maxRetryCount) {
                    retryTask(task);
                }
                //因为.le(TaskResourceSection::getRetryCount, maxRetryCount); 已经限制了 实际上这里永远不会进来
//                else {
//                    log.error("切片任务id:{}重试次数超过{}次", task.getId(), maxRetryCount);
//                    task.setRetryCount(retryCount + 1)
//                            .setStatus(NOT_RETRY);
//                    baseMapper.updateById(task);
//                }
            }
        }
        return taskList;
    }
}
