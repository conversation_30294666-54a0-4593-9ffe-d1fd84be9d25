package com.laien.web.common.m3u8.seq.annotation;

import com.laien.web.common.m3u8.seq.callback.IM3u8SeqCallback;
import com.laien.web.common.m3u8.seq.enums.CompressionTsParamsEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ResourceSection {

    String tableName();

    String m3u8UrlColumn() default "";

    String m3u8Text2kColumn() default "";

    String m3u8Text1080pColumn() default "";

    String m3u8Text720pColumn() default "";

    String m3u8Text480pColumn() default "";

    String m3u8Text360pColumn() default "";

    String durationColum() default "";

    String dirKey();

    String m3u8Text2532Column() default "";

    String m3u8Url2532Column() default "";

    /**
     * 压缩ts 字段名 (大切片)
     *
     * @return String
     */
    String compressionTsColumn() default "";

    /**
     * 压缩ts生成的m3u8 字段名 (大切片)
     *
     * @return String
     */
    String compressionM3u8Column() default "";

    /**
     * 是否进一步压缩ts
     *
     * @return boolean
     */
    boolean furtherCompression() default false;

    /**
     * 压缩参数
     *
     * @return CompressionTsParamsEnums
     */
    CompressionTsParamsEnums compressionParams() default CompressionTsParamsEnums.VIDEO_DEFAULT;

    /**
     * 切片任务完成、切片任务失败 回调方法
     */
    Class<? extends IM3u8SeqCallback> callback() default IM3u8SeqCallback.class;
}
