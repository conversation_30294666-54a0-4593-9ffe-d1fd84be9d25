package com.laien.web.common.m3u8.seq.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
@Accessors(chain = true)
@Data
public class TaskResourceSectionRunPodDTO {
    /**
     * 任务id
     */
    private Integer id;
    /**
     * 文件存储的相对路径
     */
    private String prefix;
    /**
     * 文件存储的桶
     */
    private String bucket;
    private String resourceUrl;

    @ApiModelProperty(value = "是否进一步压缩")
    private Integer furtherCompression;

    @ApiModelProperty(value = "码率")
    private String bitRate;

    @ApiModelProperty(value = "最大码率")
    private String maxBitRate;

    @ApiModelProperty(value = "缓存区大小")
    private String bufSize;

}
