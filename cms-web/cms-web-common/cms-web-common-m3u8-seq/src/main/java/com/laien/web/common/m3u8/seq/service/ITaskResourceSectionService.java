package com.laien.web.common.m3u8.seq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.common.m3u8.seq.dto.EntityEventDTO;
import com.laien.web.common.m3u8.seq.dto.RunPodResponseDTO;
import com.laien.web.common.m3u8.seq.dto.TaskResourceSectionCallBackDTO;
import com.laien.web.common.m3u8.seq.entity.TaskResourceSection;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 资源切片任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
public interface ITaskResourceSectionService extends IService<TaskResourceSection> {

    void sendTask(EntityEventDTO event);

    void callBack(TaskResourceSectionCallBackDTO req);

    <T> boolean oldResourceSection(Class<T> tClass);

    RunPodResponseDTO sendTask(TaskResourceSection task);

    List<TaskResourceSection> find(String tableName, String fieldName, Set<Integer> tableIdSet);

    /**
     * 查询指定tableName、tableId的task
     */
    List<TaskResourceSection> query(String tableName, String fieldName, List<Integer> tableIdList);
}
