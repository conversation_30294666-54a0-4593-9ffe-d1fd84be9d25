package com.laien.web.common.m3u8.seq.entity;

import com.laien.web.common.m3u8.seq.enums.TaskResourceSectionStatusEnums;
import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资源切片任务表
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "TaskResourceSection对象", description = "资源切片任务表")
public class TaskResourceSection extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "源表名")
    private String tableName;

    @ApiModelProperty(value = "源表数据id")
    private Integer tableId;

    @ApiModelProperty(value = "源表对应实体对应的字段名称")
    private String entityFieldName;

    @ApiModelProperty(value = "资源地址")
    private String resourceUrl;

    @ApiModelProperty(value = "m3u8 url在表中对应的列名")
    private String m3u8UrlColumn;

    @ApiModelProperty(value = "m3u8Text2k在表中对应的列名")
    private String m3u8Text2kColumn;

    @ApiModelProperty(value = "m3u8Text1080p在表中对应的列名")
    private String m3u8Text1080pColumn;

    @ApiModelProperty(value = "m3u8Text720p在表中对应的列名")
    private String m3u8Text720pColumn;

    @ApiModelProperty(value = "m3u8Text480p在表中对应的列名")
    private String m3u8Text480pColumn;

    @ApiModelProperty(value = "m3u8Text360p在表中对应的列名")
    private String m3u8Text360pColumn;

    @ApiModelProperty(value = "duration在表中对应的列名")
    private String durationColum;

    @ApiModelProperty(value = "mp4,ts转换成ts文件地址对应的列名")
    private String compressionTsColumn;

    @ApiModelProperty(value = "mp4,ts转换成m3u8文件地址对应的列名")
    private String compressionM3u8Column;

    @ApiModelProperty(value = "是否进一步压缩")
    private Integer furtherCompression;

    @ApiModelProperty(value = "码率")
    private String bitRate;

    @ApiModelProperty(value = "最大码率")
    private String maxBitRate;

    @ApiModelProperty(value = "缓存区大小")
    private String bufSize;

    @ApiModelProperty(value = "资源对应的m3u8，包含高中低分辨率的m3u8")
    private String m3u8Url;

    @ApiModelProperty(value = "m3u8Text2k")
    private String m3u8Text2k;

    @ApiModelProperty(value = "m3u8Text1080p")
    private String m3u8Text1080p;

    @ApiModelProperty(value = "m3u8Text720p")
    private String m3u8Text720p;

    @ApiModelProperty(value = "m3u8Text480p")
    private String m3u8Text480p;

    @ApiModelProperty(value = "m3u8Text360p")
    private String m3u8Text360p;

    @ApiModelProperty(value = "mp4,ts转换成ts文件地址")
    private String compressionTs;

    @ApiModelProperty(value = "mp4,ts转换成m3u8文件地址")
    private String compressionM3u8;

    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;

    @ApiModelProperty(value = "信息，用来记录失败等信息描述内容")
    private String message;

    @ApiModelProperty(value = "run pod的job id")
    private String jobId;

    @ApiModelProperty(value = "生成的m3u8和ts存放的目录")
    private String catalogue;

    @ApiModelProperty(value = "任务状态")
    private TaskResourceSectionStatusEnums status;

    @ApiModelProperty(value = "资源时长（毫秒）")
    private Integer duration;

    @ApiModelProperty(value = "m3u8Text2532在表中对应的列名")
    private String m3u8Text2532Column;

    @ApiModelProperty(value = "m3u8Text2532")
    private String m3u8Text2532;

    @ApiModelProperty(value = "m3u8Url2532在表中对应的列名")
    private String m3u8Url2532Column;

    @ApiModelProperty(value = "m3u8Url2532")
    private String m3u8Url2532;

    @ApiModelProperty(value = "call back class")
    private String callBackClass;
}
