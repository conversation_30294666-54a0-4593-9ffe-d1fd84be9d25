package com.laien.web.common.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.common.user.entity.SysUserPerms;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户权限关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface SysUserPermsMapper extends BaseMapper<SysUserPerms> {

    public void deleteByUserIds(@Param("ids") List<Integer> userIds);

    public void deleteUserPermsByPermsIds(@Param("userId") Integer userId, @Param("permsIds") List<Integer> userIds);
}
