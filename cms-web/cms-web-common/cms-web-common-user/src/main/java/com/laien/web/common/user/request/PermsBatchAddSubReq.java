package com.laien.web.common.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "批量增加子权限", description = "批量增加子权限")
public class PermsBatchAddSubReq {

    @ApiModelProperty(value = "父级id", required = true)
    private Integer parentId;

    @ApiModelProperty(value = "自动创建子操作权限，0否 1是", required = true)
    private Integer autoCreateOpSubPerms;

    @ApiModelProperty(value = "子权限")
    private List<PermsSaveReq> subPerms;
}
