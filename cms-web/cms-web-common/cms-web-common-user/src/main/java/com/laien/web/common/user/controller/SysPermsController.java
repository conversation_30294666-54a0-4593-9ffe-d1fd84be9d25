package com.laien.web.common.user.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.laien.web.common.user.vo.PermsVO;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.entity.SysPerms;
import com.laien.web.common.user.request.PermsBatchAddSubReq;
import com.laien.web.common.user.request.PermsCoverSubPerms;
import com.laien.web.common.user.request.PermsSaveOneReq;
import com.laien.web.common.user.service.ISysPermsService;
import com.laien.web.common.user.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.laien.web.frame.constant.GlobalConstant.*;

/**
 * <p>
 * 权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@RestController
@Api(tags = "用户管理：权限")
@RequestMapping("/sys/perms")
public class SysPermsController extends ResponseController {

    @Autowired
    private ISysPermsService sysPermsService;

    @Autowired
    private ISysUserService sysUserService;

    @ApiOperation(value = "查询所有权限")
    @GetMapping("/list")
    public ResponseResult<List<PermsVO>> list() {
        return succ(sysPermsService.getAllPermsTree());
    }

    @ApiOperation(value = "查询所有操作权限")
    @GetMapping("/listAllOp")
    public ResponseResult<List<PermsVO>> listAllOp() {
        List<PermsVO> result = Lists.newArrayList();
        LambdaQueryWrapper<SysPerms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPerms::getPermsType, 2);
        queryWrapper.eq(SysPerms::getVisible, SHOW_VISIBLE);
        queryWrapper.eq(SysPerms::getStatus, STATUS_ENABLE);
        List<SysPerms> permsByDept = sysPermsService.list(queryWrapper);
        for (SysPerms sysPerms : permsByDept) {
            PermsVO permsVO = new PermsVO();
            permsVO.setPermsId(sysPerms.getId())
                    .setComponent(sysPerms.getComponent())
                    .setParentId(sysPerms.getParentId())
                    .setPath(sysPerms.getPath())
                    .setPermsIcon(sysPerms.getIcon())
                    .setPermsKey(sysPerms.getPermsKey())
                    .setPermsName(sysPerms.getPermsName())
                    .setPermsType(sysPerms.getPermsType())
                    .setSubPerms(new ArrayList<>(ZERO));
            result.add(permsVO);
        }
        return succ(result);
    }

    @ApiOperation(value = "查询部门权限")
    @GetMapping("/listByDept/{id}")
    public ResponseResult<List<PermsVO>> listByDept(@PathVariable Integer id) {
        return succ(sysPermsService.getPermsByDept(id));
    }

    @ApiOperation(value = "查询用户权限")
    @GetMapping("/listByUser/{id}")
    public ResponseResult<List<PermsVO>> listByUser(@PathVariable Integer id) {
        return succ(sysPermsService.getPermsByUser(id));
    }

    @ApiOperation(value = "查询我的权限")
    @GetMapping("/listByMe")
    public ResponseResult<List<PermsVO>> listByMe() {
        return succ(sysPermsService.getMyOpPerms());
    }

    @ApiOperation(value = "新增权限")
    @PostMapping("/add")
    public ResponseResult<Integer> add(@RequestBody PermsSaveOneReq permsSaveReq) {
        return sysPermsService.savePerms(permsSaveReq);
    }

    @ApiOperation(value = "修改权限")
    @PostMapping("/update")
    public ResponseResult<Integer> update(@RequestBody PermsSaveOneReq permsSaveReq) {
        return sysPermsService.savePerms(permsSaveReq);
    }

    @ApiOperation(value = "批量增加权限")
    @PostMapping("/addBatch")
    public ResponseResult<Void> addBatch(@RequestBody PermsBatchAddSubReq permsSaveReq) {
        return sysPermsService.batchAddPerms(permsSaveReq);
    }

    @ApiOperation(value = "修改所有子权限")
    @PostMapping("/updateSubPerms")
    public ResponseResult<Void> updateSubPerms(@RequestBody PermsCoverSubPerms coverSubPerms) {
        return sysPermsService.coverSubPerms(coverSubPerms);
    }

    @ApiOperation(value = "查询子权限")
    @GetMapping("/listSubPerms")
    public ResponseResult<List<PermsVO>> listSubPerms(Integer permsId) {
        return succ(sysPermsService.getSubPerms(permsId));
    }
}
