package com.laien.web.common.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "用户详情VO", description = "用户详情VO")
public class UserDetailVO {

    @ApiModelProperty(value = "用户id")
    private Integer id;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "真实名称")
    private String realName;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "部门id")
    private Integer deptId;

    @ApiModelProperty(value = "账号状态（1正常 2停用）")
    private Integer status;

    @ApiModelProperty(value = "用户类型（0管理员，1普通用户）")
    private Integer userType;

    @ApiModelProperty(value = "权限列表")
    private List<PermsVO> permsData;
}
