package com.laien.web.common.user.event.impl.processer;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.web.common.user.constant.UserConstant;
import com.laien.web.common.user.entity.SysEvent;
import com.laien.web.common.user.event.IEventProcesser;
import com.laien.web.common.user.event.exception.EventRetryException;
import com.laien.web.common.user.service.ISysPermsService;
import com.laien.web.common.user.vo.PermsVO;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Set;


@Component
public class UpdateRedisAllOperms implements IEventProcesser {

    @Autowired
    private ISysPermsService sysPermsService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public void process(SysEvent event) {
        try {
            RBucket<HashMap<String, Set<String>>> bucket = redissonClient.getBucket(UserConstant.REDIS_ALL_OPPERMS);
            List<PermsVO> allOpPerms = sysPermsService.getAllOpPerms();
            HashMap<String, Set<String>> opPerms = Maps.newHashMap();
            for (PermsVO allOpPerm : allOpPerms) {
                String pathStr = allOpPerm.getPath();
                if (StringUtils.isNotBlank(pathStr)) {
                    String[] paths = pathStr.split(",");
                    for (String path : paths) {
                        if (StringUtils.isNotBlank(path)) {
                            Set<String> permsList = opPerms.get(path);
                            if (permsList == null) {
                                permsList = Sets.newHashSet();
                                opPerms.put(path, permsList);
                            }
                            permsList.add(allOpPerm.getPermsKey());
                        }
                    }
                }
            }
            bucket.set(opPerms);
//            //发布通知
//            RTopic topic = redissonClient.getTopic(REDIS_TOPIC_UPGRADE_PERMS);
//            topic.publish(1);
        } catch (Exception e) {
            throw new EventRetryException(e);
        }
    }

    @Override
    public String type() {
        return UserConstant.EVENT_TYPE_UPDATE_ALLOPERMS;
    }
}
