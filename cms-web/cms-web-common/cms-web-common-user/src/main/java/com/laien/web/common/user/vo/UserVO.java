package com.laien.web.common.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "用户信息VO", description = "用户信息VO")
public class UserVO {

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "显示名称")
    private String nickName;

    @ApiModelProperty(value = "真实名称")
    private String realName;

    @ApiModelProperty(value = "账号状态（1正常 2停用）")
    private Integer status;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "用户性别（0男，1女，2未知）")
    private Integer sex;

    @ApiModelProperty(value = "部门名称")
    private String dept;

}
