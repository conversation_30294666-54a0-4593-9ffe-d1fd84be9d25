package com.laien.web.common.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.common.user.entity.SysPerms;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
public interface SysPermsMapper extends BaseMapper<SysPerms> {

    List<SysPerms> selectByDept(Integer dept_id);

    List<SysPerms> selectByUser(Integer user_id);

    List<SysPerms> selectAllByUser(Integer user_id);

    List<SysPerms> selectAllProjOp();

    List<SysPerms> selectAllProjRead();

    @Select({
            "<script>",
            "select perms_key from sys_perms where parent_id in (select id from sys_perms where perms_key in ",
            "<foreach item='item' index='index' collection='parentPermsKeys'",
            "open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            " )",
            "</script>"
    })
    List<String> selectSubPermsKeys(@Param("parentPermsKeys") Set<String> parentPermsKeys);

}
