package com.laien.web.common.user.event.impl.processer;

import com.google.common.collect.Sets;
import com.laien.web.common.user.constant.UserConstant;
import com.laien.web.common.user.entity.SysEvent;
import com.laien.web.common.user.event.IEventProcesser;
import com.laien.web.common.user.event.exception.EventRetryException;
import com.laien.web.common.user.service.ISysPermsService;
import com.laien.web.common.user.vo.PermsVO;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;


@Component
public class UpdateRedisUserOperms implements IEventProcesser {

    @Autowired
    private ISysPermsService sysPermsService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public void process(SysEvent event) {
        try {
            Integer userId = Integer.parseInt(event.getEventData());
            List<PermsVO> permsByUser = sysPermsService.getAllPermsByUser(userId);
            HashSet<String> userHasPerms = Sets.newHashSet();
            for (PermsVO permsVO : permsByUser) {
                userHasPerms.add(permsVO.getPermsKey());
            }
            RBucket<HashSet<String>> bucket = redissonClient.getBucket(UserConstant.REDIS_USER_OPPERMS.replace("{USERID}", userId + ""));
            bucket.set(userHasPerms);
        } catch (Exception e) {
            throw new EventRetryException(e);
        }
    }

    @Override
    public String type() {
        return UserConstant.EVENT_TYPE_UPDATE_USEROPERMS;
    }
}
