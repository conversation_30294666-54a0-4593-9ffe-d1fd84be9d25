package com.laien.web.common.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: 字典查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "字典VO", description = "字典VO")
public class SysDictVO {

    @ApiModelProperty(value = "用户id")
    private Integer id;

    @ApiModelProperty(value = "字典key")
    private String dictKey;

    @ApiModelProperty(value = "字典value")
    private String dictValue;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "子级列表")
    private List<SysDictVO> children;

}
