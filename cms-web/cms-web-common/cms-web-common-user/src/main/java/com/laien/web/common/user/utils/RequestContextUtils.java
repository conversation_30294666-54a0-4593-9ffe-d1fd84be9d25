package com.laien.web.common.user.utils;

import cn.hutool.core.util.StrUtil;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.frame.constant.DeviceType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

import static com.laien.web.frame.constant.GlobalConstant.HEADER_APPCODE;
import static com.laien.web.frame.constant.GlobalConstant.HEADER_DEVICE_NAME;
import static com.laien.web.frame.constant.GlobalConstant.HEADER_LANGUAGE_NAME;
import static com.laien.web.frame.constant.GlobalConstant.HEADER_PROJECTID_NAME;
import static com.laien.web.frame.constant.GlobalConstant.ONE;
import static com.laien.web.frame.constant.GlobalConstant.PASS_USER_HEADER_NAME;
import static com.laien.web.frame.constant.GlobalConstant.UNKNOWN_USER;
import static com.laien.web.frame.constant.GlobalConstant.ZERO;


/**
 * 获取接口请求作用域工具类
 */
public class RequestContextUtils {

    private static final String VISTOR_DEV_MODE = "VISTOR_DEV_MODE";
    private static final String ENABLE = "ENABLE";
    private static final String VISTOR_NAME = "<EMAIL>";

    /**
     * 获取当前登陆用户
     *
     * @return
     */
    public static LoginUserInfo getLoginUser() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        Object attribute = requestAttributes.getAttribute(PASS_USER_HEADER_NAME, RequestAttributes.SCOPE_SESSION);
        return attribute != null ? (LoginUserInfo) attribute : getVistorUser();
    }

    /**
     * 临时增加的一个免登录的访客，方便single直连后端 绕过gateway 时也能正确加载权限及菜单，方便调试接口
     * 此访客用户 只有在后端和前端都同时开启了访客模式时才会返回具体的访客用户信息
     *
     * @return
     */
    private static LoginUserInfo getVistorUser() {
        return Optional.ofNullable(System.getenv().get(VISTOR_DEV_MODE)).filter(v -> StrUtil.equals(v, ENABLE) && isRunningFromIDEA()).map(p -> Optional.ofNullable(RequestContextHolder.getRequestAttributes()).map(requestAttributes -> {
            LoginUserInfo loginUserInfo = null;
            ServletRequestAttributes attributes = (ServletRequestAttributes) requestAttributes;
            final String userMode = attributes.getRequest().getHeader(VISTOR_DEV_MODE);
            //增加校验fullUri
            if (StrUtil.equals(userMode, ENABLE)) {
                loginUserInfo = new LoginUserInfo();
                loginUserInfo.setUserName(VISTOR_NAME);
                //目前访客是使用的admin的权限
                loginUserInfo.setUserId(ONE);
                loginUserInfo.setUserType(ZERO);
                loginUserInfo.setRealName(VISTOR_NAME);
            }
            return loginUserInfo;
        }).orElse(null)).orElse(null);
    }

    private static boolean isRunningFromIDEA() {
        try {
            // 尝试加载 `com.intellij.rt.execution.application.AppMainV2` 类
            Class.forName("com.intellij.rt.execution.application.AppMainV2");
            // 如果类存在，说明是在 IDEA 中运行
            return true;
        } catch (ClassNotFoundException e) {
            // 如果找不到该类，说明不是在 IDEA 中运行
            return false;
        }
    }


    public static String getLoginUserName() {
        LoginUserInfo loginUserInfo = getLoginUser();
        return Objects.isNull(loginUserInfo) ? UNKNOWN_USER : loginUserInfo.getUserName();
    }

    /**
     * 获取语言
     *
     * @return
     */
    public static String getLanguage() {
        HttpServletRequest request = getRequest();
        String lang = request.getHeader(HEADER_LANGUAGE_NAME);
        if (StringUtils.isBlank(lang)) {
            return request.getParameter(HEADER_LANGUAGE_NAME);
        }

        return lang;
    }

    /**
     * 获取设备，默认phone
     *
     * @return String
     */
    public static String getDeviceDefaultPhone() {
        HttpServletRequest request = getRequest();
        String device = request.getHeader(HEADER_DEVICE_NAME);
        if (device == null) {
            return DeviceType.PHONE;
        }

        return device;
    }

    /**
     * 获取项目id
     *
     * @return
     */
    public static Integer getProjectId() {
        HttpServletRequest request = getRequest();
        String projIdStr = request.getHeader(HEADER_PROJECTID_NAME);
        if (StringUtils.isNotBlank(projIdStr) && StringUtils.isNumeric(projIdStr)) {
            return Integer.parseInt(projIdStr);
        }
        return null;
    }

    /**
     * 获取appcode
     *
     * @return String
     */
    public static String getAppCode() {
        HttpServletRequest request = getRequest();
        return request.getHeader(HEADER_APPCODE);
    }

    private static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // get the request
        HttpServletRequest request = requestAttributes.getRequest();
        return request;
    }

    /**
     * 获取当期请求路径
     *
     * @return String
     */
    public static String getRequestUrl() {
        HttpServletRequest request = getRequest();
        String url = request.getRequestURI();
        String queryString = request.getQueryString();
        if (StringUtils.isNotBlank(queryString)) {
            url = url + "?" + queryString;
        }
        return url;
    }

}
