package com.laien.web.common.user.request;

import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "修改用户密码", description = "修改用户密码")
public class UserChangePwdReq {

    @ApiModelProperty(value = "原密码", required = true)
    @NotBlank(message = "Please input your oloPassword", groups = Group1.class)
    private String oldPassword;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "Please input your password", groups = Group1.class)
    private String password;

}
