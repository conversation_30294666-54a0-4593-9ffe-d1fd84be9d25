package com.laien.web.common.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.common.user.entity.SysEvent;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 事件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
public interface ISysEventService extends IService<SysEvent> {

    /**
     * 获取所有待处理的事件
     *
     * @return
     */
    List<SysEvent> getAllWaitProcessEvent();

    /**
     * 添加刷新redis所有权限事件
     */
    void addUpdateRedisAllOpPermisEvent();

    /**
     * 添加刷新redis用户权限事件
     *
     * @param userId
     */
    void addUpdateRedisUserOpPermisEvent(int userId);

    @Transactional(rollbackFor = Exception.class)
    void addUpdateRedisUserOpPermisEvent(List<Integer> userIds);

    /**
     * 重置所有处理超时的事件
     */
    void resetProcessTimeoutEvent(long timeOut);
}
