package com.laien.web.common.user.constant;

public interface UserConstant {

    String LOGIN_USERNAME_ISNULL = "Please input your account";

    String LOGIN_PASSWORD_ISNULL = "Please input the password";

    String LOGIN_USER_NOT_EXIST = "Couldn't find your account";

    String LOGIN_PASSWORD_ISERROR = "Your password is incorrect";

    String LOGIN_USER_DISABLE = "Your account is disabled";

    String USER_NOT_DEL_ADMIN = "The administrator account cannot be deleted";

    String USER_ID_NOT_EMPTY = "The user ID cannot be empty";

    String USER_NOT_EXIST = "User does not exist";

    String USER_NAME_NOT_EMPTY = "The user name cannot be empty";

    String USER_PASSWORD_NOT_EMPTY = "The user password cannot be empty";

    String USER_NAME_EXISTS = "The user name already exists";


    /**
     * 权限类型
     */
    Integer PERMS_TYPE_MENU = 1;
    Integer PERMS_TYPE_OPERATION = 2;
    Integer PERMS_TYPE_PROJECT = 3;
    Integer PERMS_TYPE_URL = 4;

    /**
     * 事件类型
     */
    String EVENT_TYPE_UPDATE_ALLOPERMS = "EVENT_TYPE_UPDATE_ALLOPERMS";

    String EVENT_TYPE_UPDATE_USEROPERMS = "EVENT_TYPE_UPDATE_USEROPERMS";


    /**
     * 事件处理状态
     */
    int EVENT_STATUS_WAITPROCESS = 0;
    int EVENT_STATUS_PROCESSING = 1;
    int EVENT_STATUS_SUCCESS = 2;
    int EVENT_STATUS_ERROR = 3;

    /**
     * resis相关
     */
    String REDIS_TOPIC_UPGRADE_PERMS = "REDIS_TOPIC_UPGRADE_PERMS";

    String REDIS_TOPIC_UPGRADE_USER_PERMS = "REDIS_TOPIC_UPGRADE_USER_PERMS";

    String REDIS_ALL_OPPERMS = "REDIS_ALL_OPPERMS";

    String REDIS_USER_OPPERMS = "REDIS_{USERID}_OPPERMS";

    /**
     * 登录用户前缀
     */
    String LOGIN_USER_KEY_PREFIX = "CMS_LOGIN_USERS:";

    /**
     * 自动创建的操作菜单集合
     */
    String[] PERMS_AUTO_CREATE_OP_PERMSLIST = new String[]{":read;查看", ":add;增加", ":update;修改", ":del;删除"};
}
