package com.laien.web.common.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.common.user.utils.JwtUtils;
import com.laien.web.common.user.vo.UserLoginVO;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.entity.SysUser;
import com.laien.web.common.user.request.UserLoginReq;
import com.laien.web.common.user.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.laien.web.frame.constant.GlobalConstant.STATUS_DISABLE;
import static com.laien.web.frame.response.setting.ResponseResult.fail;
import static com.laien.web.frame.response.setting.ResponseResult.succ;
import static com.laien.web.common.user.constant.UserConstant.*;

@RestController
@RequestMapping("/sys/user")
@Api(tags = "用户管理：账号")
public class SysAccountController {

    @Autowired
    private ISysUserService sysUserService;

    @Resource
    private RedissonClient redissonClient;

    @ApiOperation(value = "登陆")
    @PostMapping("/login")
    public ResponseResult<UserLoginVO> login(@RequestBody UserLoginReq userLoginReq) {
        //账号为空
        if (StringUtils.isBlank(userLoginReq.getUserName())) {
            return fail(LOGIN_USERNAME_ISNULL);
        }
        //密码为空
        if (StringUtils.isBlank(userLoginReq.getPassword())) {
            return fail(LOGIN_PASSWORD_ISNULL);
        }
        SysUser user = sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, userLoginReq.getUserName()).last("limit 1"));
        //用户不存在
        if (user == null) {
            return fail(LOGIN_USER_NOT_EXIST);
        }
        //用户密码错误
        if (!StringUtils.equals(userLoginReq.getPassword(), user.getPassword())) {
            return fail(LOGIN_PASSWORD_ISERROR);
        }
        //用户被禁用
        if (user.getStatus() == STATUS_DISABLE) {
            return fail(LOGIN_USER_DISABLE);
        }
        UserLoginVO userLoginVO = new UserLoginVO();
        BeanUtils.copyProperties(user, userLoginVO);
        // 生成token
        LoginUserInfo userInfo = new LoginUserInfo();
        userInfo.setUserId(user.getId());
        userInfo.setUserName(user.getUserName());
        userInfo.setRealName(user.getRealName());
        userInfo.setUserType(user.getUserType());
        String token = JwtUtils.createJwt(userInfo);
        userLoginVO.setToken(token);
        // token 放入redis
        String loginKey = LOGIN_USER_KEY_PREFIX + user.getUserName();
        redissonClient.getBucket(loginKey).set(token, 60 * 8, TimeUnit.MINUTES);
        return succ(userLoginVO);
    }

    @ApiOperation(value = "登出")
    @PostMapping("/logout")
    public ResponseResult<Void> logOut() {
        LoginUserInfo userInfo = RequestContextUtils.getLoginUser();
        if (userInfo != null) {
            String loginKey = LOGIN_USER_KEY_PREFIX + userInfo.getUserName();
            redissonClient.getBucket(loginKey).delete();
        }
        return succ();
    }


}
