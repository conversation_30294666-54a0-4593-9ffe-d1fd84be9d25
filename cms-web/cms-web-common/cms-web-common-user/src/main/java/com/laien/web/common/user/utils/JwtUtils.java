package com.laien.web.common.user.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.frame.constant.GlobalConstant;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * note:
 *
 * <AUTHOR>
 */

public class JwtUtils {


    /**
     * token过期时间，单位秒，这里设置12小时
     */
    public static final long EXPIRE = 60 * 60 * 12L;

    /**
     * 秘钥
     */
    public static final String SECRET = "MTY%zNjg|1jo0Ol$IC_3hu46wfQ0zB";

    /**
     * 登录成功后生成Jwt
     *
     * @param userInfo 登录成功的用户对象
     * @return
     */
    public static String createJwt(LoginUserInfo userInfo) {
        Date nowTime = new Date();
        Map<String, Object> header = new HashMap<>(2);
        header.put("alg", "HS256");
        header.put("typ", "JWT");
        long expiresTime = (nowTime.getTime() + EXPIRE * GlobalConstant.THOUSAND * 2 * 365);
        JWTCreator.Builder builder = JWT.create();
        builder.withHeader(header)
                .withClaim("userId", userInfo.getUserId())
                .withClaim("userName", userInfo.getUserName())
                .withClaim("realName", userInfo.getRealName())
                .withClaim("userType", userInfo.getUserType())
                .withIssuedAt(nowTime)
                .withExpiresAt(new Date(expiresTime));
        String token = builder.sign(Algorithm.HMAC256(SECRET));
        return token;
    }


    /**
     * 解密token,解密有问题则会抛出相应异常,获取token中有效负载的信息
     *
     * @param token 加密后的token
     * @return
     */
    public static LoginUserInfo getLoginUser(String token) {
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(SECRET)).build();
        try {
            DecodedJWT decodedJWT = jwtVerifier.verify(token);
            Integer userId = decodedJWT.getClaim("userId").asInt();
            String userName = decodedJWT.getClaim("userName").asString();
            String realName = decodedJWT.getClaim("realName").asString();
            Integer userType = decodedJWT.getClaim("userType").asInt();
            LoginUserInfo userInfo = new LoginUserInfo();
            userInfo.setUserId(userId);
            userInfo.setUserName(userName);
            userInfo.setRealName(realName);
            userInfo.setUserType(userType);
            return userInfo;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

}