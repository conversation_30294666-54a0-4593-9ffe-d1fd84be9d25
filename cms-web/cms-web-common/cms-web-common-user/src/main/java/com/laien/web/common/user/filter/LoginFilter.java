package com.laien.web.common.user.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.laien.web.common.user.model.LoginUserInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Base64;

import static com.laien.web.frame.constant.GlobalConstant.PASS_USER_HEADER_NAME;

@Component
public class LoginFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String userInfoStr = request.getHeader(PASS_USER_HEADER_NAME);
        if (StringUtils.isNotBlank(userInfoStr)) {
            String userInfoJson = new String(Base64.getDecoder().decode(userInfoStr));
            LoginUserInfo loginUserInfo = new ObjectMapper().readValue(userInfoJson, LoginUserInfo.class);
            RequestContextHolder.getRequestAttributes().setAttribute(PASS_USER_HEADER_NAME, loginUserInfo, RequestAttributes.SCOPE_SESSION);
        }
        filterChain.doFilter(request, response);
    }

}