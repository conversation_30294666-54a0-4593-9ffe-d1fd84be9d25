package com.laien.web.common.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.common.user.entity.SysDictionary;
import com.laien.web.common.user.vo.SysDictVO;


import java.util.List;

/**
 * <p>
 * sys_dictionary 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
public interface ISysDictionaryService extends IService<SysDictionary> {

    /**
     * 根据可以查询字典
     *
     * @param dictName dictName
     * @return list
     */
    List<SysDictVO> selectByDictName(String dictName);

}
