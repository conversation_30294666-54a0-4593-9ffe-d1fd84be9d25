package com.laien.web.common.user.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 部门权限关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SysDeptPerms对象", description="部门权限关联表")
public class SysDeptPerms extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门id")
    private Integer deptId;

    @ApiModelProperty(value = "权限id")
    private Integer permsId;


}
