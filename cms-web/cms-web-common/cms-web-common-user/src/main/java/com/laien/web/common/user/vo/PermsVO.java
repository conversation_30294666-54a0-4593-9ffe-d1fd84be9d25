package com.laien.web.common.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * note:音乐列表发布
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value="权限节点VO", description="权限节点VO")
public class PermsVO {

    @ApiModelProperty(value = "权限id")
    private Integer permsId;

    @ApiModelProperty(value = "权限图标，主要是菜单使用")
    private String permsIcon;

    @ApiModelProperty(value = "权限名称")
    private String permsName;

    @ApiModelProperty(value = "权限标识")
    private String permsKey;

    @ApiModelProperty(value = "父节点id")
    private Integer parentId;

    @ApiModelProperty(value = "节点类型")
    private Integer permsType;

    @ApiModelProperty(value = "路由地址，链接地址")
    private String path;

    @ApiModelProperty(value = "组件路径")
    private String component;

    @ApiModelProperty(value = "子节点")
    private List<PermsVO> subPerms;

}