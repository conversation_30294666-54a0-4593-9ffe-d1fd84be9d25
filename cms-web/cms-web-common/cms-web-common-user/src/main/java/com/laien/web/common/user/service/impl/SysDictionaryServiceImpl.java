package com.laien.web.common.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.common.user.vo.SysDictVO;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.common.user.entity.SysDictionary;
import com.laien.web.common.user.mapper.SysDictionaryMapper;
import com.laien.web.common.user.service.ISysDictionaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * sys_dictionary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Service
public class SysDictionaryServiceImpl extends ServiceImpl<SysDictionaryMapper, SysDictionary> implements ISysDictionaryService {

    @Resource
    private SysDictionaryMapper sysDictionaryMapper;

    @Override
    public List<SysDictVO> selectByDictName(String dictName) {
        List<SysDictVO> dictVOList = sysDictionaryMapper.selectByDictName(dictName);
        LinkedHashMap<Integer, List<SysDictVO>> dictListGroupMap = dictVOList.stream()
                .collect(Collectors.groupingBy(SysDictVO::getParentId, LinkedHashMap::new, Collectors.toList()));
        for (SysDictVO sysDictVO : dictVOList) {
            sysDictVO.setChildren(dictListGroupMap.get(sysDictVO.getId()));
        }

        List<SysDictVO> dictResultList = dictListGroupMap.get(GlobalConstant.ZERO);
        if (dictResultList == null) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }

        return dictResultList;
    }

}
