package com.laien.web.common.user.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * sys_dictionary
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SysDictionary对象", description="sys_dictionary")
public class SysDictionary extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典key")
    private String dictKey;

    @ApiModelProperty(value = "字典value")
    private String dictValue;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "父id")
    private Integer parentId;


}
