package com.laien.web.common.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "保存权限", description = "保存权限")
public class PermsUpdateByKeyReq {

    @ApiModelProperty(value = "权限key")
    private String permsKey;

    @ApiModelProperty(value = "匹配方式, 1、%value% 2、%value 3、value% 4、等于")
    private Integer likeType;

    @ApiModelProperty(value = "路由地址，链接地址")
    private String path;

    @ApiModelProperty(value = "组件地址")
    private String component;

    @ApiModelProperty(value = "随着父级联动，必须的（0否 1是）")
    private Integer required;

    @ApiModelProperty(value = "菜单显示 （0隐藏 1显示）")
    private Integer visible;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "排序")
    private Integer sortNo;


}
