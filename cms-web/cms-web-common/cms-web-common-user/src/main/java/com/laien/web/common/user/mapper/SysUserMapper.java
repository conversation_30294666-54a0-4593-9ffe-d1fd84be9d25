package com.laien.web.common.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.laien.web.common.user.entity.SysUser;
import com.laien.web.common.user.request.UserPageReq;
import com.laien.web.common.user.vo.UserPageVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 查询用户分页
     *
     * @param page
     * @param pageReq
     * @return
     */
    Page<UserPageVO> selectUserPage(Page<UserPageVO> page, @Param("myPage") UserPageReq pageReq);

}
