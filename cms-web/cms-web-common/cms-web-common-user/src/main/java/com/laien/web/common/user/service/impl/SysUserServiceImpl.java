package com.laien.web.common.user.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.common.user.entity.SysUser;
import com.laien.web.common.user.mapper.SysUserMapper;
import com.laien.web.common.user.request.UserPageReq;
import com.laien.web.common.user.service.ISysUserService;
import com.laien.web.common.user.vo.UserPageVO;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.utils.PageConverter;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Override
    public PageRes<UserPageVO> getUserPage(UserPageReq pageReq) {
        if (pageReq.getUserName() != null) {
            pageReq.setUserName(pageReq.getUserName().trim());
        }
        Page<UserPageVO> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        getBaseMapper().selectUserPage(page, pageReq);
        return PageConverter.convert(page);
    }
}
