package com.laien.web.common.user.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SysPerms对象", description = "权限表")
public class SysPerms extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "父级id")
    private Integer parentId;

    @ApiModelProperty(value = "权限名称")
    private String permsName;

    @ApiModelProperty(value = "权限标识")
    private String permsKey;

    @ApiModelProperty(value = "权限类型： 1 菜单 2操作类型 3 项目 4外链")
    private Integer permsType;

    @ApiModelProperty(value = "路由地址，链接地址")
    private String path;

    @ApiModelProperty(value = "组件地址")
    private String component;

    @ApiModelProperty(value = "随着父级联动，必须的（0否 1是）")
    private Integer required;

    @ApiModelProperty(value = "菜单显示 （0隐藏 1显示）")
    private Integer visible;

    @ApiModelProperty(value = "菜单状态 （ 1正常 2停用）")
    private Integer status;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sortNo;


}
