package com.laien.web.common.user;//package com.laien.web.common.user;
//
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
//import org.springframework.context.annotation.Bean;
//import org.springframework.web.client.RestTemplate;
//
///**
// * 启动
// *
// * <AUTHOR>
// */
//@MapperScan("com.laien.web.common.user.mapper")
//@SpringBootApplication(scanBasePackages = {"com.laien.web.common.user", "com.laien.web.frame"})
//@EnableDiscoveryClient
//public class UserApplication {
//
//    public static void main(String[] args) {
//        SpringApplication.run(UserApplication.class, args);
//    }
//
//    @Bean
//    public RestTemplate getRestTemplate() {
//        return new RestTemplate();
//    }
//
//
//}
