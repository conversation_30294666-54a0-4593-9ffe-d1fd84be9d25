package com.laien.web.common.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.common.user.entity.SysUser;
import com.laien.web.common.user.request.UserPageReq;
import com.laien.web.common.user.vo.UserPageVO;
import com.laien.web.frame.response.PageRes;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface ISysUserService extends IService<SysUser> {

    PageRes<UserPageVO> getUserPage(UserPageReq pageReq);

}
