package com.laien.web.common.user.entity;

import com.laien.web.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 事件表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SysEvent对象", description = "事件表")
public class SysEvent extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "事件数据")
    private String eventData;

    @ApiModelProperty(value = "处理状态 0未处理 1处理中 2处理完毕")
    private Integer processStatus;

    @ApiModelProperty(value = "开始处理时间")
    private LocalDateTime processStartTime;

    @ApiModelProperty(value = "失败原因")
    private String failReason;


}
