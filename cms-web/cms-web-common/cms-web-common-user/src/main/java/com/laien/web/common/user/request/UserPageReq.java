package com.laien.web.common.user.request;

import com.laien.web.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="查询用户分页列表", description="查询用户分页列表")
public class UserPageReq extends PageReq {

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private Integer deptId;

    @ApiModelProperty(value = "账号状态（0正常 1停用）")
    private Integer status;


}
