package com.laien.web.common.user.event.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import com.laien.web.common.user.constant.UserConstant;
import com.laien.web.common.user.entity.SysEvent;
import com.laien.web.common.user.event.IEventDispatcher;
import com.laien.web.common.user.event.IEventManager;
import com.laien.web.common.user.event.IEventProcesser;
import com.laien.web.common.user.event.exception.EventRetryException;
import com.laien.web.common.user.service.ISysEventService;
import com.laien.web.frame.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class EventDispatcherImpl implements IEventDispatcher {

    @Autowired
    private List<IEventProcesser> processerList;

    @Autowired
    private ISysEventService sysEventService;

    @Autowired
    private IEventManager eventManager;

    ThreadFactory threadNameVal = new ThreadFactoryBuilder().setNameFormat("EventDispatcher").build();

    ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 5, 1, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), threadNameVal, new ThreadPoolExecutor.AbortPolicy());

    @Override
    public void dispatcher(SysEvent event) {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                //适配分布式 模拟抢订单的操作
                LambdaUpdateWrapper<SysEvent> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SysEvent::getId, event.getId());
                updateWrapper.eq(SysEvent::getProcessStatus, UserConstant.EVENT_STATUS_WAITPROCESS);
                updateWrapper.set(SysEvent::getProcessStatus, UserConstant.EVENT_STATUS_PROCESSING);
                updateWrapper.set(SysEvent::getProcessStartTime, new Date());
                boolean update = sysEventService.update(new SysEvent(), updateWrapper);
                if (update) {
                    log.info("Get Event [" + event.getEventType() + "]: " + event.getEventData());
                    String eventType = event.getEventType();
                    IEventProcesser eventProcesser = null;
                    for (IEventProcesser iEventProcesser : processerList) {
                        String type = iEventProcesser.type();
                        if (StringUtils.equals(eventType, type)) {
                            eventProcesser = iEventProcesser;
                        }
                    }
                    if (eventProcesser != null) {
                        Integer statusResult = UserConstant.EVENT_STATUS_SUCCESS;
                        String failReason = null;
                        try {
                            eventProcesser.process(event);
                            updateWrapper.set(SysEvent::getDelFlag, GlobalConstant.YES);
                        } catch (EventRetryException e1) {
                            log.info("Event [" + event.getEventType() + "] Retry: " + event.getEventData());
                            try {
                                eventManager.addEvent(event);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            return;
                        } catch (Throwable e) {
                            statusResult = UserConstant.EVENT_STATUS_ERROR;
                            failReason = e.getMessage();
                        } finally {
                            updateWrapper = new LambdaUpdateWrapper<>();
                            updateWrapper.eq(SysEvent::getId, event.getId());
                            updateWrapper.eq(SysEvent::getProcessStatus, UserConstant.EVENT_STATUS_PROCESSING);
                            updateWrapper.set(SysEvent::getProcessStatus, statusResult);
                            updateWrapper.set(SysEvent::getFailReason, failReason);
                            sysEventService.update(new SysEvent(), updateWrapper);
                        }
                    } else {
                        log.info("Not Found Event [" + event.getEventType() + "] Processer ");
                        try {
                            Thread.sleep(50);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        });

    }
}
