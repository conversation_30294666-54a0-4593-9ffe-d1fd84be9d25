package com.laien.web.common.user.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.entity.SysDept;
import com.laien.web.common.user.entity.SysUser;
import com.laien.web.common.user.request.UserAddReq;
import com.laien.web.common.user.request.UserChangePwdReq;
import com.laien.web.common.user.request.UserPageReq;
import com.laien.web.common.user.request.UserUpdateReq;
import com.laien.web.common.user.service.ISysDeptService;
import com.laien.web.common.user.service.ISysPermsService;
import com.laien.web.common.user.service.ISysUserPermsService;
import com.laien.web.common.user.service.ISysUserService;
import com.laien.web.common.user.utils.RandomGenPwd;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.common.user.vo.PermsVO;
import com.laien.web.common.user.vo.UserDetailVO;
import com.laien.web.common.user.vo.UserPageVO;
import com.laien.web.common.user.vo.UserVO;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.request.IdListReq;
import com.laien.web.frame.response.PageRes;
import com.laien.web.frame.response.setting.ResponseCode;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.frame.validation.Group;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import static com.laien.web.common.user.constant.UserConstant.*;
import static com.laien.web.frame.constant.GlobalConstant.*;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@RestController
@RequestMapping("/sys/user")
@Api(tags = "用户管理：用户")
public class SysUserController extends ResponseController {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysPermsService sysPermsService;

    @Autowired
    private ISysUserPermsService sysUserPermsService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Resource
    private RedissonClient redissonClient;

    @ApiOperation(value = "查询用户列表(分页)")
    @GetMapping("/page")
    public ResponseResult<PageRes<UserPageVO>> list(UserPageReq userPageReq) {
        return succ(sysUserService.getUserPage(userPageReq));
    }

    @ApiOperation(value = "删除用户（支持批量）")
    @PostMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> del(@RequestBody IdListReq idListReq) {
        if (CollectionUtils.isNotEmpty(idListReq.getIdList())) {
            if (idListReq.getIdList().contains(1)) {
                return fail(USER_NOT_DEL_ADMIN);
            }
            //查询账号
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysUser::getId, idListReq.getIdList());
            List<SysUser> waitDelUsers = sysUserService.list(queryWrapper);
            //删除
            LambdaUpdateWrapper<SysUser> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(SysUser::getDelFlag, GlobalConstant.YES);
            wrapper.in(SysUser::getId, idListReq.getIdList());
            //必须传递一个 new SysUser() 否则 自动填充updateTime不会生效
            sysUserService.update(new SysUser(), wrapper);
            //删除关联 为了查询权限效率 这里采用真实删除
            sysUserPermsService.updateUserPerm(new ArrayList<>(ZERO), idListReq.getIdList());
            TransactionSynchronizationManager
                    .registerSynchronization(new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            // 事务提交后执行回调
                            for (SysUser waitDelUser : waitDelUsers) {
                                String userName = waitDelUser.getUserName();
                                String loginKey = LOGIN_USER_KEY_PREFIX + userName;
                                String oldToken = (String) redissonClient.getBucket(loginKey).get();
                                if(StringUtils.isBlank(oldToken)){
                                    return;
                                }
                                redissonClient.getBucket(oldToken).set(ResponseCode.DISABLED.getMsg(), 5, TimeUnit.MINUTES);
                                redissonClient.getBucket(loginKey).delete();
                            }
                        }
                    });
        }
        return succ();
    }

    @ApiOperation(value = "查询用户详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<UserDetailVO> detail(@PathVariable Integer id) {
        if (id == null) {
            return fail(USER_ID_NOT_EMPTY);
        }
        if (id == 1) {
            return fail("The admin account is not allowed to view details");
        }
        SysUser user = sysUserService.getById(id);
        if (user == null) {
            return fail(USER_NOT_EXIST);
        }
        UserDetailVO result = new UserDetailVO();
        BeanUtils.copyProperties(user, result);
        //用户的权限
        List<PermsVO> permsByUser = sysPermsService.getPermsByUser(id);
        result.setPermsData(permsByUser);
        return succ(result);
    }

    @ApiOperation(value = "生成初始密码")
    @GetMapping("/genInitPwd")
    public ResponseResult<String> genInitPwd() {
        return succ(RandomGenPwd.gen(8 + new Random().nextInt(5)));
    }


    @ApiOperation(value = "检测用户名是否存在(false 不存在,true 存在)")
    @GetMapping("/useName/{username}")
    public ResponseResult<Void> useName(@PathVariable String username) {
        if (StringUtils.isBlank(username)) {
            return fail("Please input account");
        }
        int count = sysUserService.count(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, username.trim()));
        if (count > 0) {
            return fail("Account existed");
        }
        return succ();
    }

    @ApiOperation(value = "新增用户")
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> add(@Validated(Group.class) @RequestBody UserAddReq userAddReq) {
        //先检测用户名是否存在
        ResponseResult<Void> check = useName(userAddReq.getUserName());
        if (check.getCode() == ResponseCode.FAILURE.getCode()) {
            return check;
        }
        SysUser sysUser = new SysUser();
        BeanUtils.copyProperties(userAddReq, sysUser);
        sysUser.setUserName(sysUser.getUserName().trim());
        sysUser.setUserType(USER_TYPE_NORMAL);
        sysUserService.save(sysUser);
        Integer id = sysUser.getId();
        List<Integer> userIdList = new ArrayList<>(ONE);
        userIdList.add(id);
        sysUserPermsService.updateUserPerm(userAddReq.getPermsIds(), userIdList);
        return succ();
    }

    @ApiOperation(value = "修改用户")
    @PostMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> update(@Validated(Group.class) @RequestBody UserUpdateReq userUpdateReq) {
        if (userUpdateReq.getId() == 1) {
            return fail("The admin account cannot be modified");
        }
        SysUser user = sysUserService.getById(userUpdateReq.getId());
        if (user == null) {
            return fail("The user with id " + userUpdateReq.getId() + " does not exist");
        }
        List<PermsVO> oldPerms = sysPermsService.getPermsByUser(userUpdateReq.getId());
        Integer oldStatus = user.getStatus();
        Integer newStatus = userUpdateReq.getStatus();
        SysUser sysUser = new SysUser();
        BeanUtils.copyProperties(userUpdateReq, sysUser);
        sysUser.setUserType(null);
        sysUserService.updateById(sysUser);
        Integer id = sysUser.getId();
        sysUserPermsService.updateUserPerm(userUpdateReq.getPermsIds(), Lists.newArrayList(id));
        boolean isUpdatePerms = false;
        List<PermsVO> newPerms = sysPermsService.getPermsByUser(userUpdateReq.getId());
        String oldPermsStr = JSON.toJSONString(oldPerms);
        String newPermsStr = JSON.toJSONString(newPerms);
        if (!StringUtils.equals(oldPermsStr, newPermsStr)) {
            isUpdatePerms = true;
        }
        if ((oldStatus.intValue() != newStatus && newStatus == STATUS_DISABLE) || isUpdatePerms) {
            //账号被禁用
            boolean finalIsUpdatePerms = isUpdatePerms;
            TransactionSynchronizationManager
                    .registerSynchronization(new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            // 事务提交后执行回调
                            String userName = user.getUserName();
                            String loginKey = LOGIN_USER_KEY_PREFIX + userName;
                            String oldToken = (String) redissonClient.getBucket(loginKey).get();
                            if (newStatus == STATUS_DISABLE) {
                                if (StringUtils.isNotBlank(oldToken)) {
                                    redissonClient.getBucket(oldToken).set(ResponseCode.DISABLED.getMsg(), 1, TimeUnit.MINUTES);
                                }
                            } else if (finalIsUpdatePerms) {
                                if (StringUtils.isNotBlank(oldToken)) {
                                    redissonClient.getBucket(oldToken).set(ResponseCode.CHANGE_PERMISSION.getMsg(), 1, TimeUnit.MINUTES);
                                }
                            }
                            redissonClient.getBucket(loginKey).delete();
                        }
                    });
        }
        return succ();
    }


    @ApiOperation(value = "用户修改密码")
    @PostMapping("/updatePwd")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> updatePwd(@Validated(Group.class) @RequestBody UserChangePwdReq userChangePwdReq) {
        String oldPassword = userChangePwdReq.getOldPassword();
        String password = userChangePwdReq.getPassword();
        LoginUserInfo loginUser = RequestContextUtils.getLoginUser();
        Integer loginUserId = null;
        if (loginUser != null) {
            loginUserId = loginUser.getUserId();
        }
        if (loginUserId == null) {
            return fail("Not log in");
        }
        SysUser user = sysUserService.getById(loginUserId);
        if (user != null) {
            if (!StringUtils.equals(oldPassword, user.getPassword())) {
                return fail("The old password is incorrect");
            }
            SysUser updateUser = new SysUser();
            updateUser.setId(loginUserId);
            updateUser.setPassword(password);
            sysUserService.updateById(updateUser);
        } else {
            return fail("User does not exist");
        }
        return succ();
    }

    @ApiOperation(value = "获取登陆用户信息")
    @GetMapping("/getMyInfo")
    public ResponseResult<UserVO> getMyInfo() {
        LoginUserInfo loginUser = RequestContextUtils.getLoginUser();
        Integer loginUserId = null;
        if (loginUser != null) {
            loginUserId = loginUser.getUserId();
        }
        if (loginUserId == null) {
            return fail("Not log in");
        }
        SysUser byId = sysUserService.getById(loginUserId);
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(byId, userVO);
        SysDept dept = sysDeptService.getById(byId.getDeptId());
        if (dept != null) {
            userVO.setDept(dept.getDeptName());
        }
        return succ(userVO);
    }


}
