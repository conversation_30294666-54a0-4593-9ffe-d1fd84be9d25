package com.laien.web.common.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.web.common.user.model.LoginUserInfo;
import com.laien.web.common.user.request.*;
import com.laien.web.common.user.utils.RequestContextUtils;
import com.laien.web.common.user.vo.PermsVO;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.response.setting.ResponseCode;
import com.laien.web.frame.response.setting.ResponseResult;

import com.laien.web.common.user.entity.SysPerms;
import com.laien.web.common.user.entity.SysUser;
import com.laien.web.common.user.entity.SysUserPerms;
import com.laien.web.common.user.mapper.SysPermsMapper;
import com.laien.web.common.user.mapper.SysUserPermsMapper;
import com.laien.web.common.user.service.ISysEventService;
import com.laien.web.common.user.service.ISysPermsService;
import com.laien.web.common.user.service.ISysUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.web.frame.constant.GlobalConstant.*;
import static com.laien.web.common.user.constant.UserConstant.*;

/**
 * <p>
 * 权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@Service
public class SysPermsServiceImpl extends ServiceImpl<SysPermsMapper, SysPerms> implements ISysPermsService {

    @Resource
    private ISysEventService sysEventService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private SysUserPermsMapper sysUserPermsMapper;

    /**
     * 管理团队、产品部、内容部
     */

    private List<Integer> deptIdList1;

    /**
     * 项目部、开发部、测试部
     */

    private List<Integer> deptIdList2;

    public SysPermsServiceImpl() {
        deptIdList1 = new ArrayList<>(THREE);
        //管理团队
        deptIdList1.add(9);
        //产品部
        deptIdList1.add(1);
        //内容部
        deptIdList1.add(6);
        deptIdList2 = new ArrayList<>(THREE);
        //项目部
        deptIdList2.add(5);
        //开发部
        deptIdList2.add(3);
        //测试部
        deptIdList2.add(4);
    }

    @Override
    public List<PermsVO> getPermsByDept(Integer deptId) {
        List<SysPerms> permsByDept = getBaseMapper().selectByDept(deptId);
        //当是管理团队、产品部、内容部时 需要具备所有的项目操作权限
        if (deptIdList1.contains(deptId)) {
            permsByDept.addAll(getBaseMapper().selectAllProjOp());
        }
        //当是项目部、开发部、测试部时 需要具备所有项目的查看权限
        if (deptIdList2.contains(deptId)) {
            permsByDept.addAll(getBaseMapper().selectAllProjRead());
        }
        List<PermsVO> result = sysPerms2PermsVO(permsByDept);
        //项目方面的动态权限
        return result;
    }


    @Override
    public List<PermsVO> getPermsByUser(Integer userId) {
        List<PermsVO> permsByUser = Lists.newArrayList();
        SysUser user = sysUserService.getById(userId);
        if (user != null) {
            if (user.getUserType() == USER_TYPE_ADMIN) {
                permsByUser = getAllOpPerms();
            } else {
                List<SysPerms> permsByDept = getBaseMapper().selectByUser(userId);
                permsByUser = sysPerms2PermsVO(permsByDept);
            }
        }
        return permsByUser;
    }

    @Override
    public List<PermsVO> getAllPermsByUser(Integer userId) {
        List<PermsVO> permsByUser = Lists.newArrayList();
        SysUser user = sysUserService.getById(userId);
        if (user != null) {
            if (user.getUserType() == USER_TYPE_ADMIN) {
                permsByUser = getAllOpPerms();
            } else {
                List<SysPerms> permsByDept = getBaseMapper().selectAllByUser(userId);
                permsByUser = sysPerms2PermsVO(permsByDept);
            }
        }
        return permsByUser;
    }

    @Override
    public List<PermsVO> getAllPermsTree() {
        List<PermsVO> result = Lists.newArrayList();
        LambdaQueryWrapper<SysPerms> queryWrapper = new LambdaQueryWrapper<>();
        //状态为正常
        queryWrapper.eq(SysPerms::getStatus, GlobalConstant.STATUS_ENABLE);
        //显示状态为显示
        queryWrapper.eq(SysPerms::getVisible, GlobalConstant.SHOW_VISIBLE);
        //排序 按sort_no
        queryWrapper.orderByAsc(SysPerms::getSortNo, SysPerms::getId);
        List<SysPerms> list = list(queryWrapper);
        //利用两次循环 将list转为tree
        HashMap<Integer, List<PermsVO>> permsParentMap = list2Tree(list);
        List<PermsVO> permsVOS = permsParentMap.get(0);
        if (CollectionUtils.isNotEmpty(permsVOS)) {
            result.addAll(permsVOS);
        }
        return result;
    }

    @Override
    public List<PermsVO> getSubPerms(Integer permsId) {
        LambdaQueryWrapper<SysPerms> queryWrapper = new LambdaQueryWrapper<>();
        //状态为正常
        queryWrapper.eq(SysPerms::getStatus, GlobalConstant.STATUS_ENABLE);
        //显示状态为显示
        queryWrapper.eq(SysPerms::getVisible, GlobalConstant.SHOW_VISIBLE);
        //指定父节点
        queryWrapper.eq(SysPerms::getParentId, permsId);
        List<SysPerms> list = list(queryWrapper);
        List<PermsVO> result = sysPerms2PermsVO(list);
        return result;
    }

    @Override
    public List<PermsVO> getAllOpPerms() {
        LambdaQueryWrapper<SysPerms> queryWrapper = new LambdaQueryWrapper<>();
        //状态为正常
        queryWrapper.eq(SysPerms::getStatus, GlobalConstant.STATUS_ENABLE);
        //显示状态为显示
        queryWrapper.eq(SysPerms::getVisible, GlobalConstant.SHOW_VISIBLE);
        //类型改为操作类型
        queryWrapper.eq(SysPerms::getPermsType, PERMS_TYPE_OPERATION);
        List<SysPerms> list = list(queryWrapper);
        List<PermsVO> result = sysPerms2PermsVO(list);
        return result;
    }

    @Override
    public List<PermsVO> getMyOpPerms() {
        LoginUserInfo loginUser = RequestContextUtils.getLoginUser();
        Integer loginUserId = null;
        if (loginUser != null) {
            loginUserId = loginUser.getUserId();
        }
        if (loginUserId == null) {
            return Lists.newArrayList();
        }
        return getPermsByUser(loginUserId);
    }

    @Override
    public void batchUpdateByPermsKey(PermsUpdateByKeyReq updateByKeyReq) {
        //提交成功后需要刷新redis
        sysEventService.addUpdateRedisAllOpPermisEvent();
        String permsKey = updateByKeyReq.getPermsKey();
        Integer likeType = updateByKeyReq.getLikeType();
        LambdaUpdateWrapper<SysPerms> updateWrapper = new LambdaUpdateWrapper<>();
        switch (likeType) {
            case ONE:
                updateWrapper.like(SysPerms::getPermsKey, permsKey);
                break;
            case TWO:
                updateWrapper.likeRight(SysPerms::getPermsKey, permsKey);
                break;
            case THREE:
                updateWrapper.likeLeft(SysPerms::getPermsKey, permsKey);
                break;
            case FOUR:
                updateWrapper.eq(SysPerms::getPermsKey, permsKey);
                break;
            default:
                return;
        }
        updateWrapper.set(StringUtils.isNotBlank(updateByKeyReq.getPath()), SysPerms::getPath, updateByKeyReq.getPath());
        updateWrapper.set(StringUtils.isNotBlank(updateByKeyReq.getComponent()), SysPerms::getComponent, updateByKeyReq.getComponent());
        updateWrapper.set(updateByKeyReq.getRequired() != null, SysPerms::getRequired, updateByKeyReq.getRequired());
        updateWrapper.set(StringUtils.isNotBlank(updateByKeyReq.getIcon()), SysPerms::getIcon, updateByKeyReq.getIcon());
        updateWrapper.set(updateByKeyReq.getVisible() != null, SysPerms::getVisible, updateByKeyReq.getVisible());
        updateWrapper.set(updateByKeyReq.getSortNo() != null, SysPerms::getSortNo, updateByKeyReq.getSortNo());
        getBaseMapper().update(new SysPerms(), updateWrapper);

    }

    @Override
    public void authorizationPermsByParent(String permsKey, Integer userId) {
        LambdaQueryWrapper<SysPerms> sysPermsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysPermsLambdaQueryWrapper.likeRight(SysPerms::getPermsKey, permsKey);
        sysPermsLambdaQueryWrapper.eq(SysPerms::getPermsType, PERMS_TYPE_OPERATION);
        List<SysPerms> sysPerms = getBaseMapper().selectList(sysPermsLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(sysPerms)) {
            List<PermsVO> permsByUser = getPermsByUser(userId);
            Set<Integer> permsIds = permsByUser.stream().map(PermsVO::getPermsId).collect(Collectors.toSet());
            for (SysPerms sysPerm : sysPerms) {
                Integer id = sysPerm.getId();
                if (!permsIds.contains(id)) {
                    SysUserPerms sysUserPerms = new SysUserPerms();
                    sysUserPerms.setUserId(userId);
                    sysUserPerms.setPermsId(id);
                    sysUserPermsMapper.insert(sysUserPerms);
                }
            }
        }
        sysEventService.addUpdateRedisUserOpPermisEvent(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Integer> savePerms(PermsSaveOneReq permsSaveReq) {
        //提交成功后需要刷新redis
        sysEventService.addUpdateRedisAllOpPermisEvent();
        //校验参数是否齐全
        ResponseResult<Integer> checkError = validatePerms(permsSaveReq, permsSaveReq.getParentId());
        if (checkError != null) {
            return checkError;
        }
        ResponseResult<String> permsKeyResult = getPermsKey(permsSaveReq.getSubKey(), permsSaveReq.getPermsType(), permsSaveReq.getParentId(), null);
        if (permsKeyResult.getCode() != ResponseCode.SUCCESS.getCode()) {
            return ResponseResult.fail(permsKeyResult.getMessage());
        }
        String permsKey = permsKeyResult.getData();
        //判断权限permsKey是否已存在
        SysPerms one = getOne(new LambdaQueryWrapper<SysPerms>().eq(SysPerms::getPermsKey, permsKey).last("limit 1"));
        //新增
        Integer id = null;
        if (permsSaveReq.getId() == null) {
            if (one != null) {
                return ResponseResult.fail("The permission key already exists," + permsKey);
            }
            SysPerms sysPerms = new SysPerms();
            BeanUtils.copyProperties(permsSaveReq, sysPerms);
            sysPerms.setPermsKey(permsKey);
            save(sysPerms);
            id = sysPerms.getId();
            return ResponseResult.succ(id);
        }
        //修改
        if (one == null) {
            return ResponseResult.fail("The permission corresponding to the ID does not exist," + permsSaveReq.getId());
        }
        if (one != null && one.getId().intValue() != permsSaveReq.getId()) {
            return ResponseResult.fail("The permission key already exists," + permsKey);
        } else {
            SysPerms sysPerms = new SysPerms();
            BeanUtils.copyProperties(permsSaveReq, sysPerms);
            //不允许修改permsKey，permsType
            sysPerms.setPermsType(null);
            updateById(sysPerms);
            id = sysPerms.getId();
            return ResponseResult.succ(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> batchAddPerms(PermsBatchAddSubReq permsSaveReq) {
        //提交成功后需要刷新redis
        sysEventService.addUpdateRedisAllOpPermisEvent();
        Integer parentId = permsSaveReq.getParentId();
        List<PermsSaveReq> subPerms = permsSaveReq.getSubPerms();
        for (PermsSaveReq subPerm : subPerms) {
            ResponseResult<Integer> checkError = validatePerms(subPerm, parentId);
            if (checkError != null) {
                return ResponseResult.fail(checkError.getMessage());
            }
        }
        SysPerms parentPerm = getById(parentId);
        List<SysPerms> batchAddPerms = Lists.newArrayList();
        for (PermsSaveReq subPerm : subPerms) {
            ResponseResult<String> permsKeyResult = getPermsKey(subPerm.getSubKey(), subPerm.getPermsType(), permsSaveReq.getParentId(), parentPerm);
            if (permsKeyResult.getCode() != ResponseCode.SUCCESS.getCode()) {
                return ResponseResult.fail(permsKeyResult.getMessage());
            }
            String permsKey = permsKeyResult.getData();
            int count = count(new LambdaQueryWrapper<SysPerms>().eq(SysPerms::getPermsKey, permsKey).in(SysPerms::getDelFlag, Lists.newArrayList(YES, NO)));
            if (count > 0) {
                return ResponseResult.fail("The permission key already exists," + permsKey);
            }
            SysPerms sysPerms = new SysPerms();
            BeanUtils.copyProperties(subPerm, sysPerms);
            sysPerms.setParentId(parentId);
            sysPerms.setPermsKey(permsKey);
            batchAddPerms.add(sysPerms);
        }
        //自动创建增删查改子权限
        if (permsSaveReq.getAutoCreateOpSubPerms() != null && permsSaveReq.getAutoCreateOpSubPerms() > 0) {
            saveAndAutoCreateOpSub(batchAddPerms);
        } else {
            saveBatch(batchAddPerms);
        }
        return ResponseResult.succ();
    }

    private void saveAndAutoCreateOpSub(List<SysPerms> batchAddPerms) {
        for (SysPerms addPerm : batchAddPerms) {
            save(addPerm);
            List<SysPerms> opSubPerm = Lists.newArrayList();
            for (String opPermsInfo : PERMS_AUTO_CREATE_OP_PERMSLIST) {
                String[] strs = opPermsInfo.split(";");
                String subPermsKey = strs[0];
                String subPermName = strs[1];
                SysPerms addSubPerm = new SysPerms();
                addSubPerm.setParentId(addPerm.getId());
                addSubPerm.setPermsType(PERMS_TYPE_OPERATION);
                addSubPerm.setPermsKey(getPermsKey(subPermsKey, addPerm).getData());
                addSubPerm.setPermsName(subPermName);
                opSubPerm.add(addSubPerm);
            }
            saveBatch(opSubPerm);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> coverSubPerms(PermsCoverSubPerms coverSubPerms) {
        //提交成功后需要刷新redis
        sysEventService.addUpdateRedisAllOpPermisEvent();
        Integer parentId = coverSubPerms.getParentId();
        List<PermsSaveReq> subPerms = coverSubPerms.getSubPerms();
        for (PermsSaveReq subPerm : subPerms) {
            ResponseResult<Integer> checkError = validatePerms(subPerm, parentId);
            if (checkError != null) {
                return ResponseResult.fail(checkError.getMessage());
            }
        }
        SysPerms parentPerm = getById(parentId);
        //新的子权限树
        List<SysPerms> newPerms = Lists.newArrayList();
        Map<String, String> sonKeyAndParentKey = Maps.newHashMap();
        covertPerms(newPerms, coverSubPerms.getSubPerms(), parentPerm, sonKeyAndParentKey, parentPerm.getPermsKey());
        Map<String, SysPerms> newPermsMap = newPerms.stream().collect(Collectors.toMap(SysPerms::getPermsKey, t -> t));
        Set<String> newPermsKeySet = Sets.newHashSet(newPerms.stream().map(SysPerms::getPermsKey).collect(Collectors.toSet()));
        //老的子权限树
        LambdaQueryWrapper<SysPerms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(SysPerms::getPermsKey, parentPerm.getPermsKey() + "\\_");
        List<SysPerms> oldPerms = list(queryWrapper);
        Set<String> oldPermsKeySets = Sets.newHashSet(oldPerms.stream().map(SysPerms::getPermsKey).collect(Collectors.toSet()));
        //进行差集对比 得到本次需要新增的权限\删除的权限\修改的权限
        Sets.SetView<String> addSets = Sets.difference(newPermsKeySet, oldPermsKeySets);
        //add这里要特殊处理下 因为add的时候是要求顺序的
        List<String> addList = Lists.newArrayList();
        for (SysPerms newPerm : newPerms) {
            if (addSets.contains(newPerm.getPermsKey())) {
                addList.add(newPerm.getPermsKey());
            }
        }
        Set<String> removeSets = Sets.difference(oldPermsKeySets, newPermsKeySet).immutableCopy();
        Sets.SetView<String> updateSets = Sets.intersection(oldPermsKeySets, newPermsKeySet);
        //先将删除的全部删除
        if (CollectionUtils.isNotEmpty(removeSets)) {
            LambdaUpdateWrapper<SysPerms> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(SysPerms::getStatus, GlobalConstant.STATUS_DISABLE);
            wrapper.in(SysPerms::getPermsKey, removeSets);
            update(new SysPerms(), wrapper);
        }
        //添加所有新增的
        for (String addPermsKey : addList) {
            SysPerms perms = newPermsMap.get(addPermsKey);
            if (perms != null) {
                save(newPermsMap.get(addPermsKey));
            }
        }
        //修改所有需要修改的
//        检测需要更新权限的人员id
//        List<Integer> updatePermsIds = Lists.newArrayList();
        for (String updatePermsKey : updateSets) {
            SysPerms perms = newPermsMap.get(updatePermsKey);
            if (perms != null) {
                SysPerms odPerms = getOne(new LambdaQueryWrapper<SysPerms>().eq(SysPerms::getPermsKey, perms.getPermsKey()).in(SysPerms::getDelFlag, Lists.newArrayList(YES, NO)).last("limit 1"));
                if (odPerms != null) {
                    perms.setStatus(STATUS_ENABLE);
                    perms.setPermsKey(perms.getPermsKey());
                    perms.setId(odPerms.getId());
                    updateById(perms);
//                    updatePermsIds.add(odPerms.getId());
                }
            }
        }
//        if (CollectionUtils.isNotEmpty(updatePermsIds)) {
//            LambdaQueryWrapper<SysUserPerms> sysUserPermsLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            sysUserPermsLambdaQueryWrapper.in(SysUserPerms::getPermsId, updatePermsIds);
//            sysUserPermsLambdaQueryWrapper.groupBy(SysUserPerms::getUserId);
//            List<SysUserPerms> sysUserPerms = sysUserPermsMapper.selectList(sysUserPermsLambdaQueryWrapper);
//            for (SysUserPerms sysUserPerm : sysUserPerms) {
//                sysEventService.addUpdateRedisUserOpPermisEvent(sysUserPerm.getUserId());
//            }
//        }
        //根据关联关系更新 parentId
        Set<String> upRelationSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(addSets)) {
            upRelationSet.addAll(addSets);
        }
        if (CollectionUtils.isNotEmpty(updateSets)) {
            upRelationSet.addAll(updateSets);
        }
        for (String addPermsKey : upRelationSet) {
            String parentKey = sonKeyAndParentKey.get(addPermsKey);
            if (StringUtils.isNotBlank(parentKey)) {
                SysPerms perms = newPermsMap.get(parentKey);
                Integer updateParentId = null;
                if (perms != null) {
                    updateParentId = perms.getId();
                    if (updateParentId == null) {
                        SysPerms odPerms = getOne(new LambdaQueryWrapper<SysPerms>().eq(SysPerms::getPermsKey, parentKey).last("limit 1"));
                        updateParentId = odPerms.getId();
                    }
                } else {
                    perms = getOne(new LambdaQueryWrapper<SysPerms>().eq(SysPerms::getPermsKey, parentKey).last("limit 1"));
                    updateParentId = perms.getId();
                }
                SysPerms perms1 = newPermsMap.get(addPermsKey);
                perms1.setParentId(updateParentId);
                updateById(perms1);
            }
        }
        return ResponseResult.succ();
    }

    private void covertPerms(List<SysPerms> perms, List<PermsSaveReq> saveReqs, SysPerms parentPerm, Map<String, String> sonKeyAndParentKey, String parentKey) {
        if (CollectionUtils.isNotEmpty(saveReqs)) {
            for (PermsSaveReq saveReq : saveReqs) {
                SysPerms sysPerms2 = new SysPerms();
                BeanUtils.copyProperties(saveReq, sysPerms2);
                sysPerms2.setId(null);
                sysPerms2.setParentId(0);
                ResponseResult<String> permsKeyResult = getPermsKey(saveReq.getSubKey(), parentPerm);
                if (permsKeyResult.getCode() != ResponseCode.SUCCESS.getCode()) {
                    continue;
                }
                sysPerms2.setPermsKey(permsKeyResult.getData());
                perms.add(sysPerms2);
                if (CollectionUtils.isNotEmpty(saveReq.getSubPerms())) {
                    covertPerms(perms, saveReq.getSubPerms(), parentPerm, sonKeyAndParentKey, sysPerms2.getPermsKey());
                }
                sonKeyAndParentKey.put(sysPerms2.getPermsKey(), parentKey);
            }
        }
    }

    private ResponseResult<String> getPermsKey(String subKey, Integer permsType, Integer parentId, SysPerms parentPerm) {
        String permsKey = "";
        if (parentId == 0) {
            //根节点
            if (permsType.intValue() == PERMS_TYPE_PROJECT) {
                permsKey = "proj_" + subKey;
                return ResponseResult.succ(permsKey);
            }
        }
        if (parentId != null && parentId > 0) {
            if (parentPerm == null) {
                parentPerm = getById(parentId);
            }
            ResponseResult<String> result = getPermsKey(subKey, parentPerm);
            if (result.getCode() == ResponseCode.SUCCESS.getCode()) {
                permsKey = result.getData();
            } else {
                return result;
            }
        }
        return ResponseResult.succ(permsKey);
    }

    private ResponseResult<String> getPermsKey(String subKey, SysPerms parentPerm) {
        String permsKey = "";
        if (parentPerm == null) {
            return ResponseResult.fail("The permission parent does not exist，" + parentPerm.getId());
        }
        if (parentPerm.getPermsType().intValue() == PERMS_TYPE_OPERATION) {
            return ResponseResult.fail("Cannot add child nodes for operation permissions");
        }
        if (!subKey.startsWith(":")) {
            permsKey = parentPerm.getPermsKey() + "_" + subKey;
        } else {
            permsKey = parentPerm.getPermsKey() + subKey;
        }
        return ResponseResult.succ(permsKey);
    }


    /**
     * 校验权限数据是否完整
     *
     * @param permsSaveReq
     * @return
     */
    private ResponseResult<Integer> validatePerms(PermsSaveReq permsSaveReq, Integer parentId) {
        if (StringUtils.isBlank(permsSaveReq.getSubKey())) {
            return ResponseResult.fail("The permission key cannot be empty");
        }
        if (StringUtils.isBlank(permsSaveReq.getPermsName())) {
            return ResponseResult.fail("Permission names cannot be empty");
        }
        if (parentId == null) {
            return ResponseResult.fail("The id of the permission parent node cannot be empty");
        }
        if (permsSaveReq.getPermsType() == null) {
            return ResponseResult.fail("The permission type cannot be empty");
        }
        if (!Lists.newArrayList(PERMS_TYPE_MENU, PERMS_TYPE_OPERATION, PERMS_TYPE_PROJECT, PERMS_TYPE_URL).contains(permsSaveReq.getPermsType().intValue())) {
            return ResponseResult.fail("Wrong permission type");
        }
        return null;
    }


    /**
     * 利用两次循环将结果转成一个类似树形的结构
     *
     * @param list
     * @return
     */
    private HashMap<Integer, List<PermsVO>> list2Tree(List<SysPerms> list) {
        HashMap<Integer, List<PermsVO>> permsParentMap = Maps.newHashMap();
        HashMap<Integer, PermsVO> permsMap = Maps.newHashMap();
        for (SysPerms sysPerms : list) {
            PermsVO permsVO = new PermsVO();
            permsVO.setPermsId(sysPerms.getId())
                    .setComponent(sysPerms.getComponent())
                    .setParentId(sysPerms.getParentId())
                    .setPath(sysPerms.getPath())
                    .setPermsIcon(sysPerms.getIcon())
                    .setPermsKey(sysPerms.getPermsKey())
                    .setPermsName(sysPerms.getPermsName())
                    .setPermsType(sysPerms.getPermsType())
                    .setSubPerms(Lists.newArrayList());
            List<PermsVO> permsVOS = permsParentMap.get(permsVO.getParentId().intValue());
            if (permsVOS == null) {
                permsVOS = Lists.newArrayList();
                permsParentMap.put(permsVO.getParentId().intValue(), permsVOS);
            }
            permsVOS.add(permsVO);
            permsMap.put(permsVO.getPermsId(), permsVO);
        }
        for (PermsVO sysPerms : permsMap.values()) {
            Integer sysPermsId = sysPerms.getPermsId();
            List<PermsVO> permsVOS = permsParentMap.get(sysPermsId.intValue());
            if (CollectionUtils.isNotEmpty(permsVOS)) {
                sysPerms.setSubPerms(permsVOS);
            }
        }
        return permsParentMap;
    }

    /**
     * 将sysPerms对象集合转为PermsVO集合
     *
     * @param permsByDept
     * @return
     */
    private List<PermsVO> sysPerms2PermsVO(List<SysPerms> permsByDept) {
        List<PermsVO> result = Lists.newArrayList();
        for (SysPerms sysPerms : permsByDept) {
            PermsVO permsVO = new PermsVO();
            permsVO.setPermsId(sysPerms.getId())
                    .setComponent(sysPerms.getComponent())
                    .setParentId(sysPerms.getParentId())
                    .setPath(sysPerms.getPath())
                    .setPermsIcon(sysPerms.getIcon())
                    .setPermsKey(sysPerms.getPermsKey())
                    .setPermsName(sysPerms.getPermsName())
                    .setPermsType(sysPerms.getPermsType())
                    .setSubPerms(Lists.newArrayList());
            result.add(permsVO);
        }
        return result;
    }
}
