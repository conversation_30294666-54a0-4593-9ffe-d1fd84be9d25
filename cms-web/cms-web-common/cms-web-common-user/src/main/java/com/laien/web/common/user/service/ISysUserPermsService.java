package com.laien.web.common.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.common.user.entity.SysUserPerms;

import java.util.List;

/**
 * <p>
 * 用户权限关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface ISysUserPermsService extends IService<SysUserPerms> {

    /**
     * 防止大量数据影响查询权限性能 这里采用真实删除
     *
     * @param userIds
     */
    void removeByUserIds(List<Integer> userIds);

    /**
     * 更新用户权限 先删除 后添加 防止脏数据
     *
     * @param permsIds
     * @param userIds
     */
    void updateUserPerm(List<Integer> permsIds, List<Integer> userIds);

}
