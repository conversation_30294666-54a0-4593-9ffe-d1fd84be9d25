package com.laien.web.common.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "保存权限", description = "保存权限")
public class PermsSaveReq {

    @ApiModelProperty(value = "权限id")
    private Integer id;

    @ApiModelProperty(value = "权限名称", required = true)
    private String permsName;

    @ApiModelProperty(value = "权限标识，会拼接父权限标识，组成完整标识", required = true)
    private String subKey;

    @ApiModelProperty(value = "权限类型： 1 菜单 2操作类型 3 项目 4外链", required = true)
    private Integer permsType;

    @ApiModelProperty(value = "路由地址，链接地址")
    private String path;

    @ApiModelProperty(value = "组件地址")
    private String component;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "子权限")
    private List<PermsSaveReq> subPerms;
}
