package com.laien.web.common.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.web.common.user.entity.SysUserPerms;
import com.laien.web.common.user.mapper.SysUserPermsMapper;
import com.laien.web.common.user.service.ISysEventService;
import com.laien.web.common.user.service.ISysUserPermsService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户权限关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
@Service
public class SysUserPermsServiceImpl extends ServiceImpl<SysUserPermsMapper, SysUserPerms> implements ISysUserPermsService {

    @Autowired
    private ISysEventService sysEventService;

    @Override
    public void removeByUserIds(List<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        getBaseMapper().deleteByUserIds(userIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserPerm(List<Integer> permsIds, List<Integer> userIds) {
        //先删除再插入 防止脏数据 为了提高查询性能 这里是物理删除
        removeByUserIds(userIds);
        //批量插入权限数据
        if (CollectionUtils.isNotEmpty(permsIds)) {
            ArrayList<SysUserPerms> sysUserPerms = Lists.newArrayList();
            for (Integer id : userIds) {
                for (Integer permsId : permsIds) {
                    sysUserPerms.add(new SysUserPerms()
                            .setPermsId(permsId)
                            .setUserId(id)
                    );
                }
            }
            saveBatch(sysUserPerms);
        }
        //需要刷新redis用户权限
        for (Integer userId : userIds) {
            sysEventService.addUpdateRedisUserOpPermisEvent(userId);
        }

    }
}
