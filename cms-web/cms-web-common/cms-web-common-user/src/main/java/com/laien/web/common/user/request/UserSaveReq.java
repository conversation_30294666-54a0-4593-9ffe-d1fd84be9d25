package com.laien.web.common.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import com.laien.web.frame.validation.Group2;
import com.laien.web.frame.validation.Group3;
import com.laien.web.frame.validation.Group4;



@Data
@ApiModel(value = "保存用户", description = "保存用户")
public class UserSaveReq {

    @ApiModelProperty(value = "真实名称", required = true)
    @NotBlank(message = "Please input your name", groups = Group3.class)
    @Size(max = 50, message = "The name exceed 50 characters", groups = Group3.class)
    private String realName;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "Please input password", groups = Group2.class)
//    @Pattern(regexp = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,12}$", message = "Password must contain both number and letter with 8-12 characters", groups = Group2.class)
    private String password;

    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "Please select your dept", groups = Group4.class)
    private Integer deptId;

    @ApiModelProperty(value = "权限id集合", required = true)
    private List<Integer> permsIds;

}
