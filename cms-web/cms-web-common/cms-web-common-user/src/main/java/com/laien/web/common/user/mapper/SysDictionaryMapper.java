package com.laien.web.common.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.web.common.user.entity.SysDictionary;
import com.laien.web.common.user.vo.SysDictVO;

import java.util.List;

/**
 * <p>
 * sys_dictionary Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
public interface SysDictionaryMapper extends BaseMapper<SysDictionary> {

    /**
     * 根据可以查询字典
     *
     * @param dictName dictName
     * @return list
     */
    List<SysDictVO> selectByDictName(String dictName);

}
