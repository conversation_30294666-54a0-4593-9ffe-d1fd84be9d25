package com.laien.web.common.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

import com.laien.web.frame.validation.Group1;
import com.laien.web.frame.validation.Group5;


@Data
@ApiModel(value = "修改用户", description = "修改用户")
public class UserUpdateReq extends UserSaveReq {

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "The user ID cannot be empty", groups = Group1.class)
    private Integer id;

    @NotNull(message = "The user status cannot be empty", groups = Group5.class)
    @ApiModelProperty(value = "账号状态（1正常 2停用）")
    private Integer status;

}
