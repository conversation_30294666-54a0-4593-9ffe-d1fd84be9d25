package com.laien.web.common.user.controller;


import com.google.common.collect.Lists;
import com.laien.web.common.user.entity.SysDept;
import com.laien.web.common.user.service.ISysDeptService;
import com.laien.web.frame.controller.ResponseController;
import com.laien.web.frame.response.IdAndNameRes;
import com.laien.web.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 部门表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@RestController
@RequestMapping("/sys/dept")
@Api(tags = "用户管理：部门")
public class SysDeptController extends ResponseController {

    @Autowired
    private ISysDeptService sysDeptService;

    @ApiOperation(value="查询所有部门")
    @GetMapping("/list")
    public ResponseResult<List<IdAndNameRes>> list() {
        List<SysDept> list = sysDeptService.list();
        List<IdAndNameRes> result = Lists.newArrayList();
        for (SysDept sysDept : list) {
            IdAndNameRes idAndNameRes = new IdAndNameRes();
            idAndNameRes.setId(sysDept.getId());
            idAndNameRes.setName(sysDept.getDeptName());
            result.add(idAndNameRes);
        }
        return succ(result);
    }



}
