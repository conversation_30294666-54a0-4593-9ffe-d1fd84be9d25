package com.laien.web.common.user.event.impl.thread;


import com.laien.web.common.user.entity.SysEvent;
import com.laien.web.common.user.event.IEventDispatcher;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.LinkedBlockingQueue;

/**
 * 轮询事件队列线程
 */
@Slf4j
public class PollingTaskThread extends Thread {

    private LinkedBlockingQueue<SysEvent> eventQueue;

    private IEventDispatcher eventDispatcher;

    public PollingTaskThread(LinkedBlockingQueue<SysEvent> events, IEventDispatcher eventDispatcher) {
        this.eventQueue = events;
        this.eventDispatcher = eventDispatcher;
    }

    @Override
    public void run() {
        while (true) {
            try {
                SysEvent event = eventQueue.take();
                eventDispatcher.dispatcher(event);
            } catch (Throwable t) {
                log.error("PollingTaskThread error:", t);
            } finally {

            }
        }
    }
}
