package com.laien.web.common.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "覆盖子权限", description = "覆盖子权限,跟原有子权限集求差集，新集合中有，老的没有的则执行新增，新集合中没有，老集合中有的则执行删除")
public class PermsCoverSubPerms {

    @ApiModelProperty(value = "父级id", required = true)
    private Integer parentId;

    @ApiModelProperty(value = "子权限")
    private List<PermsSaveReq> subPerms;
}
