package com.laien.web.common.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.web.common.user.entity.SysEvent;
import com.laien.web.common.user.event.IEventManager;
import com.laien.web.common.user.mapper.SysEventMapper;
import com.laien.web.common.user.service.ISysEventService;
import jodd.util.CollectionUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Date;
import java.util.List;

import static com.laien.web.common.user.constant.UserConstant.*;

/**
 * <p>
 * 事件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Service
public class SysEventServiceImpl extends ServiceImpl<SysEventMapper, SysEvent> implements ISysEventService {

    @Autowired
    private IEventManager eventManager;

    private static ThreadLocal<List<SysEvent>> threadLocal = new ThreadLocal<>();

    @Override
    public List<SysEvent> getAllWaitProcessEvent() {
        LambdaQueryWrapper<SysEvent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysEvent::getProcessStatus, EVENT_STATUS_WAITPROCESS);
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUpdateRedisAllOpPermisEvent() {
        SysEvent sysEvent = new SysEvent();
        sysEvent.setEventType(EVENT_TYPE_UPDATE_ALLOPERMS);
        save(sysEvent);
        addEventQueue(sysEvent);
    }

    private void addEventQueue(SysEvent sysEvent) {
        synchronized (SysEventServiceImpl.class) {
            if (threadLocal == null) {
                threadLocal = new ThreadLocal<>();
            }
        }
        synchronized (threadLocal) {
            if (threadLocal.get() == null) {
                threadLocal.set(Lists.newArrayList());
            }
            if (threadLocal.get().size() == 0) {
                final List<SysEvent> events = threadLocal.get();
                TransactionSynchronizationManager
                        .registerSynchronization(new TransactionSynchronizationAdapter() {
                            @Override
                            public void afterCommit() {
                                // 事务提交后执行回调
                                for (SysEvent event : events) {
                                    try {
                                        eventManager.addEvent(event);
                                    } catch (InterruptedException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }

                            @Override
                            public void afterCompletion(int status) {
                                events.clear();
                            }
                        });
            }
        }
        threadLocal.get().add(sysEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUpdateRedisUserOpPermisEvent(int userId) {
        SysEvent sysEvent = new SysEvent();
        sysEvent.setEventType(EVENT_TYPE_UPDATE_USEROPERMS);
        sysEvent.setEventData(userId + "");
        save(sysEvent);
        addEventQueue(sysEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUpdateRedisUserOpPermisEvent(List<Integer> userIds) {
        List<SysEvent> sysEvents = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(userIds)) {
            for (Integer userId : userIds) {
                SysEvent sysEvent = new SysEvent();
                sysEvent.setEventType(EVENT_TYPE_UPDATE_USEROPERMS);
                sysEvent.setEventData(userId + "");
                sysEvents.add(sysEvent);
                addEventQueue(sysEvent);
            }
            saveBatch(sysEvents);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetProcessTimeoutEvent(long timeOut) {
        long maxProcessStartTime = System.currentTimeMillis() - timeOut;
        LambdaQueryWrapper<SysEvent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysEvent::getProcessStatus, EVENT_STATUS_PROCESSING);
        queryWrapper.le(SysEvent::getProcessStartTime, new Date(maxProcessStartTime));
        if (count(queryWrapper) > 0) {
            LambdaUpdateWrapper<SysEvent> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(SysEvent::getProcessStatus, EVENT_STATUS_WAITPROCESS);
            updateWrapper.set(SysEvent::getProcessStartTime, null);
            updateWrapper.eq(SysEvent::getProcessStatus, EVENT_STATUS_PROCESSING);
            updateWrapper.le(SysEvent::getProcessStartTime, new Date(maxProcessStartTime));
            update(new SysEvent(), updateWrapper);
        }
    }
}
