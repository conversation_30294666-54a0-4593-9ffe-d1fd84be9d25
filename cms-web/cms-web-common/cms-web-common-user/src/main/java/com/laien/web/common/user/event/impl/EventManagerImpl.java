package com.laien.web.common.user.event.impl;

import com.laien.web.common.user.entity.SysEvent;
import com.laien.web.common.user.entity.SysUser;
import com.laien.web.common.user.event.IEventDispatcher;
import com.laien.web.common.user.event.IEventManager;
import com.laien.web.common.user.event.impl.thread.PollingTaskThread;
import com.laien.web.common.user.service.ISysEventService;
import com.laien.web.common.user.service.ISysUserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

@Component
public class EventManagerImpl implements IEventManager, ApplicationListener<ApplicationPreparedEvent> {

    @Autowired
    private ISysEventService sysEventService;

    @Autowired
    private IEventDispatcher eventDispatcher;

    @Resource
    private ISysUserService sysUserService;

    private LinkedBlockingQueue<SysEvent> events = new LinkedBlockingQueue<>(1000);

    @Override
    public void onApplicationEvent(ApplicationPreparedEvent applicationPreparedEvent) {
        //重置所有进行中的任务
        sysEventService.resetProcessTimeoutEvent(0);
        //加载所有未完成的任务
        List<SysEvent> allWaitProcessEvent = sysEventService.getAllWaitProcessEvent();
        if (CollectionUtils.isNotEmpty(allWaitProcessEvent)) {
            for (SysEvent sysEvent : allWaitProcessEvent) {
                try {
                    events.put(sysEvent);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        //增加一个刷新全部权限
        sysEventService.addUpdateRedisAllOpPermisEvent();
        //为了防止不迁移redis 造成用户权限错误，因此每次启动时都会同步一次用户的权限到redis （因为后台用户比较少，因此也不太会影响启动速度）
        List<SysUser> allUser = sysUserService.list();
        if (CollectionUtils.isNotEmpty(allUser)) {
            List<Integer> userIds = allUser.stream().map(SysUser::getId).collect(Collectors.toList());
            sysEventService.addUpdateRedisUserOpPermisEvent(userIds);
        }
        //轮询队列线程
        Thread pollingTaskThread = new PollingTaskThread(events, eventDispatcher);
        pollingTaskThread.setName("pollingTaskThread");
        pollingTaskThread.start();
    }

    @Scheduled(fixedDelay = 1000 * 30)
    public void scanTask() {
        try {
            List<SysEvent> allWaitProcessEvent = sysEventService.getAllWaitProcessEvent();
            if (CollectionUtils.isNotEmpty(allWaitProcessEvent)) {
                for (SysEvent sysEvent : allWaitProcessEvent) {
                    events.put(sysEvent);
                }
            }
        } catch (Throwable e) {
        }
    }

    @Override
    public void addEvent(SysEvent event) throws InterruptedException {
        events.put(event);
    }
}
