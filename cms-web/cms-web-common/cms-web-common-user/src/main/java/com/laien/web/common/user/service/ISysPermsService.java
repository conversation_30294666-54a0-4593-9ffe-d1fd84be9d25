package com.laien.web.common.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.web.common.user.entity.SysPerms;
import com.laien.web.common.user.request.PermsBatchAddSubReq;
import com.laien.web.common.user.request.PermsCoverSubPerms;
import com.laien.web.common.user.request.PermsSaveOneReq;
import com.laien.web.common.user.request.PermsUpdateByKeyReq;
import com.laien.web.common.user.vo.PermsVO;
import com.laien.web.frame.response.setting.ResponseResult;

import java.util.List;

/**
 * <p>
 * 权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
public interface ISysPermsService extends IService<SysPerms> {

    /**
     * 获取单个部门的权限
     *
     * @param deptId
     * @return
     */
    List<PermsVO> getPermsByDept(Integer deptId);

    /**
     * 获取单个用户的权限
     *
     * @param userId
     * @return
     */
    List<PermsVO> getPermsByUser(Integer userId);


    /**
     * 获取单个用户的权限
     *
     * @param userId
     * @return
     */
    List<PermsVO> getAllPermsByUser(Integer userId);


    /**
     * 获取所有权限
     *
     * @return
     */
    List<PermsVO> getAllPermsTree();


    /**
     * 保存单个权限
     *
     * @param permsSaveReq
     * @return
     */
    ResponseResult<Integer> savePerms(PermsSaveOneReq permsSaveReq);

    /**
     * 批量增加权限
     *
     * @param permsSaveReq
     * @return
     */
    ResponseResult<Void> batchAddPerms(PermsBatchAddSubReq permsSaveReq);

    /**
     * 重置子权限
     *
     * @param coverSubPerms
     * @return
     */
    ResponseResult<Void> coverSubPerms(PermsCoverSubPerms coverSubPerms);


    /**
     * 获取某权限的子权限列表
     *
     * @param permsId
     * @return
     */
    List<PermsVO> getSubPerms(Integer permsId);

    /**
     * 获取所有权限
     *
     * @return
     */
    List<PermsVO> getAllOpPerms();

    /**
     * 获取我拥有的操作权限
     *
     * @return
     */
    List<PermsVO> getMyOpPerms();


    /**
     * 根据permsKey 批量修改权限内容
     *
     * @param updateByKeyReq
     */
    void batchUpdateByPermsKey(PermsUpdateByKeyReq updateByKeyReq);


    /**
     * 为用户授予权限 基于某一个父节点的所有权限
     *
     * @param permsKey
     * @param userId
     */
    void authorizationPermsByParent(String permsKey, Integer userId);

}
