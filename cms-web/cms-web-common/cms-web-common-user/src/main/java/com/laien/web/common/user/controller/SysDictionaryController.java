package com.laien.web.common.user.controller;


import com.laien.web.common.user.vo.SysDictVO;
import com.laien.web.frame.response.setting.ResponseResult;
import com.laien.web.common.user.request.SysDictReq;
import com.laien.web.common.user.service.ISysDictionaryService;
import com.laien.web.frame.controller.ResponseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * sys_dictionary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Api(tags = "系统:字典管理")
@RestController
@RequestMapping("/sys/dict")
public class SysDictionaryController extends ResponseController {

    @Resource
    private ISysDictionaryService sysDictionaryService;

    @ApiOperation(value = "字典查询")
    @GetMapping("/list")
    public ResponseResult<List<SysDictVO>> list(SysDictReq sysDictReq) {
        List<SysDictVO> dictVOList = sysDictionaryService.selectByDictName(sysDictReq.getDictName());
        return succ(dictVOList);
    }

}
