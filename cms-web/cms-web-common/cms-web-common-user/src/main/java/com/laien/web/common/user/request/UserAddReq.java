package com.laien.web.common.user.request;

import com.laien.web.frame.validation.Group1;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@ApiModel(value = "新增用户", description = "新增用户")
public class UserAddReq extends UserSaveReq {

    @ApiModelProperty(value = "用户名", required = true)
    @NotBlank(message = "Please input account", groups = Group1.class)
    @Size(max = 20, message = "The account exceed 20 characters", groups = Group1.class)
    private String userName;

}
