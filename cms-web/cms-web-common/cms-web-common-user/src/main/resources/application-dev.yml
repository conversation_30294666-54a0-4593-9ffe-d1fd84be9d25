#server:
#  port: 8102
#
## druid
#spring:
#  application:
#    name: user
#  cloud:
#    nacos:
#      discovery:
#        server-addr: laien.dev:8848
#  #数据库配置
#  datasource:
#    type: com.alibaba.druid.pool.DruidDataSource
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *********************************************************************************************************************************************************************************************************************
#    username: root
#    password: root
#    druid:
#      # 初始连接数
#      initial-size: 10
#      # 最大连接池数量
#      max-active: 100
#      # 最小连接池数量
#      min-idle: 10
#      # 配置获取连接等待超时的时间
#      max-wait: 60000
#      # 打开PSCache，并且指定每个连接上PSCache的大小
#      pool-prepared-statements: true
#      max-pool-prepared-statement-per-connection-size: 20
#      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#      timeBetweenEvictionRunsMillis: 60000
#      # 配置一个连接在池中最小生存的时间，单位是毫秒
#      min-evictable-idle-time-millis: 300000
#      validation-query: SELECT 1 FROM DUAL
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      stat-view-servlet:
#        enabled: true
#        url-pattern: /druid/*
#      filter:
#        stat:
#          log-slow-sql: true
#          slow-sql-millis: 1000
#          merge-sql: false
#        wall:
#          config:
#            multi-statement-allow: true
#  redis:
#    redisson:
#      config: |
#        singleServerConfig:
#          idleConnectionTimeout: 10000
#          connectTimeout: 10000
#          timeout: 3000
#          retryAttempts: 3
#          retryInterval: 1500
#          password: toA2piwM!0yV%IDW
#          subscriptionsPerConnection: 5
#          clientName: null
#          address: "redis://laien.dev:6379"
#          subscriptionConnectionMinimumIdleSize: 1
#          subscriptionConnectionPoolSize: 50
#          connectionMinimumIdleSize: 32
#          connectionPoolSize: 64
#          database: 0
#          dnsMonitoringInterval: 5000
#        threads: 0
#        nettyThreads: 0
#        codec: !<org.redisson.codec.JsonJacksonCodec> {}
#        "transportMode": "NIO"
#
## 打印sql
##mybatis:
##  configuration:
##    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#logging:
#  config: classpath:logback-spring.xml
##  level:
##    root: info
##    #包路径为mapper文件包路径
##    com.laien.workout.mapper: debug
#
#mybatis-plus:
#  configuration:
#    map-underscore-to-camel-case: true
#    auto-mapping-behavior: full
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  mapper-locations: classpath*:mapper/**/*Mapper.xml
#  global-config:
#    # 逻辑删除配置
#    db-config:
#      # 删除前
#      logic-not-delete-value: 0
#      # 删除后
#      logic-delete-value: 1
#
#
#knife4j:
#  # 开启增强配置
#  enable: true
#  # 开启生产环境屏蔽
#  production: false
#
