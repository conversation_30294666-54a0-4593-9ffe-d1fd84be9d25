<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.common.user.mapper.SysPermsMapper">

    <select id="selectByDept" resultType="com.laien.web.common.user.entity.SysPerms">
        SELECT
	        perms.id,perms.icon,perms.perms_name,perms.perms_key,perms.parent_id,perms.path,perms.component,perms.perms_type
        FROM
	        sys_perms perms
	        JOIN sys_dept_perms dept_perms ON perms.id = dept_perms.perms_id
        WHERE
	        perms.del_flag=0 AND dept_perms.dept_id =#{dept_id} AND perms.perms_type=2 AND perms.visible=1 AND perms.status=1
    </select>

    <select id="selectByUser" resultType="com.laien.web.common.user.entity.SysPerms">
        SELECT
	        perms.id,perms.icon,perms.perms_name,perms.perms_key,perms.parent_id,perms.path,perms.component,perms.perms_type
        FROM
	        sys_perms perms
	        JOIN sys_user_perms user_perms ON perms.id = user_perms.perms_id
        WHERE
	        perms.del_flag=0 AND user_perms.user_id =#{user_id} AND perms.perms_type=2  AND perms.status=1 AND perms.visible=1
    </select>


    <select id="selectAllByUser" resultType="com.laien.web.common.user.entity.SysPerms">
        SELECT
	        perms.id,perms.icon,perms.perms_name,perms.perms_key,perms.parent_id,perms.path,perms.component,perms.perms_type
        FROM
	        sys_perms perms
	        JOIN sys_user_perms user_perms ON perms.id = user_perms.perms_id
        WHERE
	        perms.del_flag=0 AND user_perms.user_id =#{user_id} AND perms.perms_type=2  AND perms.visible=1
    </select>

    <select id="selectAllProjOp" resultType="com.laien.web.common.user.entity.SysPerms">
        SELECT
	        perms.id,perms.icon,perms.perms_name,perms.perms_key,perms.parent_id,perms.path,perms.component,perms.perms_type
        FROM
	        sys_perms perms
        WHERE
            del_flag=0 AND perms_type=2 AND status=1  AND perms_key LIKE "proj%" AND perms_key!="proj:add"  AND visible=1
    </select>

    <select id="selectAllProjRead" resultType="com.laien.web.common.user.entity.SysPerms">
        SELECT
	        perms.id,perms.icon,perms.perms_name,perms.perms_key,perms.parent_id,perms.path,perms.component,perms.perms_type
        FROM
	        sys_perms perms
        WHERE
            del_flag=0 AND perms_type=2 AND status=1  AND perms_key LIKE "proj%" AND perms_key!="proj:add" AND perms_key like "%:read"  AND visible=1
    </select>

</mapper>