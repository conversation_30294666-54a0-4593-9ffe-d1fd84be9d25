<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.common.user.mapper.SysDictionaryMapper">

    <select id="selectByDictName" resultType="com.laien.web.common.user.vo.SysDictVO">
        SELECT
            id,
            dict_key,
            dict_value,
            parent_id
        FROM
            sys_dictionary
        WHERE
            del_flag = 0
          AND dict_name = #{dictName}
        ORDER BY
            sort_no,id
    </select>

</mapper>