<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.common.user.mapper.SysUserPermsMapper">

    <select id="deleteByUserIds" >
        DELETE
        FROM
            sys_user_perms
        WHERE
            user_id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="deleteUserPermsByPermsIds" >
        DELETE
        FROM
            sys_user_perms
        WHERE
            user_id = #{userId}
            AND perms_id in
        <foreach collection="permsIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


</mapper>