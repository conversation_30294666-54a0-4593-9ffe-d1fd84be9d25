<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.common.user.mapper.SysUserMapper">

    <select id="selectUserPage" resultType="com.laien.web.common.user.vo.UserPageVO">
        SELECT
        u.id,
        u.user_name,
        u.real_name,
        d.dept_name,
        u.`status`,
        u.create_time,
        u.user_type
        FROM
        sys_user u
        JOIN sys_dept d ON u.dept_id = d.id
        WHERE u.del_flag=0
        AND u.id!=1
        <if test="myPage.userName != null">
            AND u.user_name like CONCAT('%', #{myPage.userName},'%')
        </if>
        <if test="myPage.deptId != null">
            AND u.dept_id=#{myPage.deptId}
        </if>
        <if test="myPage.status != null">
            AND u.`status`=#{myPage.status}
        </if>
        order by u.create_time desc
    </select>

</mapper>