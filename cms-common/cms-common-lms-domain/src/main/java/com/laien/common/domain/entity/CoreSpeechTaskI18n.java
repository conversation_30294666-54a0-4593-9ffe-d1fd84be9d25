package com.laien.common.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.AuditTypeEnums;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import com.laien.common.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "core_speech_task_i18n", autoResultMap = true)
@ApiModel(value="CoreSpeechTaskI18n对象")
public class CoreSpeechTaskI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置的英语音色id")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "多语言音色配置id")
    private Integer coreVoiceTemplateI18nId;

    @ApiModelProperty(value = "core_text_task_i18n id")
    private Integer coreTextTaskI18nId;

    @ApiModelProperty(value = "翻译语种")
    private LanguageEnums language;

    @ApiModelProperty(value = "coreVoiceTemplateI18nName")
    private String coreVoiceTemplateI18nName;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "原文音频")
    private String audioUrl;

    @ApiModelProperty(value = "原文音频时长")
    private Integer duration;

    @ApiModelProperty(value = "译文")
    private String translationText;

    @ApiModelProperty(value = "译文md5")
    private String translationTextMd5;

    @ApiModelProperty(value = "译文音频")
    private String translationAudioUrl;

    @ApiModelProperty(value = "译文音频时长")
    private Integer translationDuration;

    @ApiModelProperty(value = "是否ai缩减")
    private boolean reduced;

    @ApiModelProperty(value = "审核类型")
    private AuditTypeEnums auditType;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "状态")
    private SpeechTaskStatusEnums status;

    @ApiModelProperty(value = "校验状态")
    private boolean checkStatus;

    @ApiModelProperty(value = "proj code的和")
    @TableField(typeHandler = ProjCodeEnums.TypeHandler.class)
    private List<ProjCodeEnums> projCode;

    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;
}
