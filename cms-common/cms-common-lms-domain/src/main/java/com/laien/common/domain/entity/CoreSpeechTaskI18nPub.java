package com.laien.common.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "core_speech_task_i18n_pub",autoResultMap = true)
@ApiModel(value="CoreSpeechTaskI18n对象")
public class CoreSpeechTaskI18nPub extends CoreSpeechTaskI18n {
    @ApiModelProperty(value = "版本")
    private Integer version;
}
