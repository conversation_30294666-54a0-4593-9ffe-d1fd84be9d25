package com.laien.common.domain.response;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.enums.StatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *CoreVoiceTemplateI18nListVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreVoiceTemplateI18nListVO", description="CoreVoiceTemplateI18nListVO")
public class CoreVoiceConfigI18nVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "audio url")
    private String audioUrl;

    @ApiModelProperty(value = "状态")
    private StatusEnums status;

    @ApiModelProperty(value = "语言")
    private List<LanguageEnums> languageList;

    @ApiModelProperty(value = "关联的模板列表")
    private List<CoreVoiceConfigRelationVO> relationList;
}
