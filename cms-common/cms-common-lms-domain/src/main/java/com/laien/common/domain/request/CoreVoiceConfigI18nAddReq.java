package com.laien.common.domain.request;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.GenderEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *CoreVoiceConfigI18nAddReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreVoiceConfig 新增", description = "CoreVoiceConfig 新增")
public class CoreVoiceConfigI18nAddReq {

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "audio url")
    private String audioUrl;

    @ApiModelProperty(value = "语言")
    private List<LanguageEnums> languageList;
}
