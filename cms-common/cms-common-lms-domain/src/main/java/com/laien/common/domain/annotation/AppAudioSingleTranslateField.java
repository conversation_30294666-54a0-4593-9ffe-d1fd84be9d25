package com.laien.common.domain.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 查指定语种的语音结果，文本译文回填到使用注解的字段
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AppAudioSingleTranslateField {

    String durationFieldName() default "";

    String urlFieldName() default "";

}
