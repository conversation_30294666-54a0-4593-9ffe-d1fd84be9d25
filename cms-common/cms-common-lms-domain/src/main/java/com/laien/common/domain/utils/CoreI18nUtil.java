package com.laien.common.domain.utils;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.crypto.digest.MD5;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Slf4j
public class CoreI18nUtil {

    private static final MD5 HUTOOL_MD5 = MD5.create();


    public static <T> T getFieldValue(Object model, Field field) {
        try {
            @SuppressWarnings("unchecked")
            T value = (T) ReflectUtil.getFieldValue(model, field);
            return value;
        } catch (Exception e) {
            log.error("get field value failed, field:{}", field, e);
            return null;
        }
    }

    public static void setFieldValue(Object model, String fieldName, Object value) {
        try {

            ReflectUtil.setFieldValue(model,fieldName, value);
        } catch (Exception e) {
            log.error("set field value failed, field:{}, {}", fieldName, value, e);
            throw new RuntimeException("assignment failed: " + fieldName, e);
        }
    }

    public static String getMd5(String text, MD5 md5) {
        if (null == md5) {
            md5 = MD5.create();
        }
        return md5.digestHex(text);
    }
}
