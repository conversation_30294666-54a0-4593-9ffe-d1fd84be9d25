package com.laien.common.domain.bo;

import com.laien.common.domain.component.AppAudioCoreI18nModel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Slf4j
@Data
@Accessors(chain = true)
public class AppAudioTranslateFieldBO {

    private AppAudioCoreI18nModel model;
    private String audioModelFieldName;
    private Integer coreVoiceConfigI18nId;
    private Set<Integer> voiceConfigTemplateI18nIdSet;
    private String textMd5;
}
