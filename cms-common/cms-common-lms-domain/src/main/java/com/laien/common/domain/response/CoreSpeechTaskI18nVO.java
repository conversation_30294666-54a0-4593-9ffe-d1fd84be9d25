package com.laien.common.domain.response;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.constant.LmsConstant;
import com.laien.common.domain.enums.AuditTypeEnums;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * CoreTextTaskI18nVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreTextTaskI18nVO", description="CoreTextTaskI18nVO")
public class CoreSpeechTaskI18nVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "翻译语种")
    private LanguageEnums language;

    @ApiModelProperty(value = "coreVoiceTemplateI18nName")
    private String coreVoiceTemplateI18nName;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "原文音频")
    private String audioUrl;

    @ApiModelProperty(value = "原文音频时长")
    private Integer duration;

    @ApiModelProperty(value = "译文")
    private String translationText;

    @ApiModelProperty(value = "译文音频")
    private String translationAudioUrl;

    @ApiModelProperty(value = "译文音频时长")
    private Integer translationDuration;

    @ApiModelProperty(value = "是否ai缩减")
    private boolean reduced;

    @ApiModelProperty(value = "审核类型")
    private AuditTypeEnums auditType;

    @ApiModelProperty(value = "状态")
    private SpeechTaskStatusEnums status;

    @ApiModelProperty(value = "校验状态")
    private boolean checkStatus;

    @ApiModelProperty(value = "是否超长")
    private Boolean overLengthStatus;

    /**
     * @return 是否超长
     */
    public Boolean getOverLengthStatus() {
        return this.translationDuration!=null && this.duration!=null
                && this.translationDuration!=0 && this.translationDuration > this.duration + LmsConstant.MAX_DURATION_OFFSET;
    }
}
