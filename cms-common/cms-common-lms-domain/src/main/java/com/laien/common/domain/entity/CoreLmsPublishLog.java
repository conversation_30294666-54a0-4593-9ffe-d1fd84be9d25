package com.laien.common.domain.entity;

import com.laien.common.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * LMS发布日志
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="CoreLmsPublishLog", description="LMS发布日志")
public class CoreLmsPublishLog extends BaseModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发布结果")
    private String result;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "coreBusinessTaskRelationI18ns")
    private String coreBusinessTaskRelationI18ns;

    @ApiModelProperty(value = "coreBusinessTaskRelationI18ns")
    private String textCoreBusinessTaskRelationI18ns;

}
