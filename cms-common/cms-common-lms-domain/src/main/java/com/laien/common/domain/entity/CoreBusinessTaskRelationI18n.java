package com.laien.common.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.UpdateFlagEnums;
import com.laien.common.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("core_business_task_relation_i18n")
@ApiModel(value="CoreBusinessTaskRelationI18n对象")
public class CoreBusinessTaskRelationI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置的英语音色id")
    private Integer coreTextTaskI18nId;

    @ApiModelProperty(value = "语种")
    private LanguageEnums language;

    @ApiModelProperty(value = "业务名")
    private String businessName;

    @ApiModelProperty(value = "业务字段")
    private String businessField;

    @ApiModelProperty(value = "业务数据id")
    private Integer businessDataId;

    @ApiModelProperty(value = "app code")
    private String appCode;

    @ApiModelProperty(value = "修改标识")
    private UpdateFlagEnums updateFlag;


}
