package com.laien.common.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.frame.entity.BaseModel;
import com.laien.common.domain.enums.GenderEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("core_voice_config_template_i18n")
@ApiModel(value="CoreVoiceConfigTemplateI18n对象")
public class CoreVoiceConfigTemplateI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "core_voice_config_i18n_id")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "core_voice_template_i18n_id")
    private Integer coreVoiceTemplateI18nId;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

}
