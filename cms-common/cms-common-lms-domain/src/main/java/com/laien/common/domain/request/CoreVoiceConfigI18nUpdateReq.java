package com.laien.common.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *CoreVoiceConfigI18nAddReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreVoiceConfig 新增", description = "CoreVoiceConfig 新增")
public class CoreVoiceConfigI18nUpdateReq extends CoreVoiceConfigI18nAddReq{

    @ApiModelProperty(value = "数据id")
    private Integer id;
}
