package com.laien.common.domain.request;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.TextTaskTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * CoreTextTaskI18nAddReq 实体类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreTextTaskI18nAddReq", description="CoreTextTaskI18nAddReq")
public class CoreTextTaskI18nDetailReq {

    @ApiModelProperty(value = "文本翻译类型")
    private TextTaskTypeEnums type;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "projcode")
    private List<ProjCodeEnums> projCode;
}
