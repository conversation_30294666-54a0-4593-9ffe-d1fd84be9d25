package com.laien.common.domain.request;

import com.laien.common.frame.request.IdListReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *CoreTextTaskI18nCheckReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreTextTaskI18nCheckReq", description = "CoreTextTaskI18nCheckReq")
public class CoreTaskI18nCheckReq extends IdListReq {

    @ApiModelProperty(value = "需要选中还是取消，true-选中，false-取消")
    private Boolean checkStatus = true;
}
