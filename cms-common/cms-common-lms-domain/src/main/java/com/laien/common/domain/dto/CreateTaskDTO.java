package com.laien.common.domain.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class CreateTaskDTO {

    private List<CoreI18nModel> coreI18nModelList;
    private List<LanguageEnums> textLanguageList;
    private ProjCodeEnums projCode;
    private List<LanguageEnums> audioLanguageList;

    public CreateTaskDTO(List<? extends CoreI18nModel> i18nList, String textLanguages, String audioLanguages, String appCode) {
        this.projCode = EnumUtil.getBy(projCodeEnums -> projCodeEnums.getAppCode().toLowerCase(), appCode.toLowerCase());
        @SuppressWarnings("unchecked")
        List<CoreI18nModel> coreI18nModels = (List<CoreI18nModel>) i18nList;
        this.coreI18nModelList = coreI18nModels;
        this.textLanguageList = LanguageEnums.getLanguageEnums(textLanguages);
        this.audioLanguageList = LanguageEnums.getLanguageEnums(audioLanguages);
    }

    public CreateTaskDTO(List<? extends CoreI18nModel> i18nList) {
        this.projCode = ProjCodeEnums.COMMON;
        @SuppressWarnings("unchecked")
        List<CoreI18nModel> coreI18nModels = (List<CoreI18nModel>) i18nList;
        this.coreI18nModelList = coreI18nModels;
        //取英语外的所有语言
        List<LanguageEnums> allLanguageEnums = CollUtil.newArrayList(LanguageEnums.values()).stream().filter(e -> e != LanguageEnums.EN).collect(Collectors.toList());
        this.textLanguageList = allLanguageEnums;
        this.audioLanguageList = allLanguageEnums;
    }
}
