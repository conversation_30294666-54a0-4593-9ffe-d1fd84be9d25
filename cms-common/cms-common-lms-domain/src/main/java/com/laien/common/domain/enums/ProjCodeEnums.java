package com.laien.common.domain.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Getter
public enum ProjCodeEnums implements IEnumBase {

    COMMON(1,"common"),
    OOG101(1 << 1,"oog101"),
    OOG104(1 << 2,"oog104"),
    OOG116(1 << 3,"oog116"),
    OOG200(1 << 4,"oog200");

    @EnumValue
    private final Integer code;
    private final String appCode;

    ProjCodeEnums(Integer code, String appCode) {
        this.code = code;
        this.appCode = appCode;
    }

    public static List<String> getAppCodeListExclude(ProjCodeEnums projCodeEnums) {
        return CollUtil.newArrayList(values()).stream().filter(item -> !item.equals(projCodeEnums)).map(ProjCodeEnums::getAppCode).collect(Collectors.toList());
    }

    @Override
    public String getName() {
        return getAppCode();
    }

    @Override
    public String getDisplayName() {
        return getAppCode();
    }

    public static Integer sum(Collection<ProjCodeEnums> projCodes){
        if(CollUtil.isEmpty(projCodes)){
            return 0;
        }
        return projCodes.stream().mapToInt(ProjCodeEnums::getCode).sum();
    }

    public static ProjCodeEnums getByAppCodeIgnoreCase(String appCode){
        return EnumUtil.getBy(projCodeEnums -> projCodeEnums.getAppCode().toLowerCase(), appCode.toLowerCase());
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<ProjCodeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<ProjCodeEnums> {
    }

}
