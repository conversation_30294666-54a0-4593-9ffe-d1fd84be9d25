package com.laien.common.domain.component;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * BaseTextI18nModel
 * </p>
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BaseTextI18nModel", description = "BaseTextI18nModel")
public class BaseTextI18nModel implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "uniqueKey")
    private Object uniqueKey;
}
