package com.laien.common.domain.bo;

import cn.hutool.crypto.digest.DigestUtil;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.TextTaskDTO;
import com.laien.common.domain.entity.CoreTextTaskI18n;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
@Data
@Accessors(chain = true)
public class FieldBO {

    private Integer dataId;
    /**
     * 所有的
     */
    private List<CoreTextTaskI18n> textTaskI18nList;
    private String businessName;
    private String fieldName;
    private String fieldValue;
    private String fieldValueMd5;
    private TranslationTaskTypeEnums type;
    private Integer coreVoiceConfigI18nId;
    private Integer duration;
    private String audioUrl;

    public static FieldBO createField(CoreI18nModel i18nModel, Field field, String value, Integer coreVoiceConfigI18nId, TranslationTaskTypeEnums type) {
        FieldBO fieldBO = new FieldBO();
        fieldBO.setDataId(i18nModel.getId())
                .setFieldName(field.getName())
                .setBusinessName(i18nModel.getClass().getSimpleName())
                .setFieldValue(value)
                .setCoreVoiceConfigI18nId(coreVoiceConfigI18nId)
                .setType(type)
                .setFieldValueMd5(DigestUtil.md5Hex(value.getBytes()));
        return fieldBO;
    }
}
