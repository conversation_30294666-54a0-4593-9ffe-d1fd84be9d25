package com.laien.common.domain.request;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.TextTaskTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * CoreTextTaskI18nAddReq 实体类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreTextTaskI18nAddReq", description="CoreTextTaskI18nAddReq")
public class CoreTextTaskI18nAddReq{

    @ApiModelProperty(value = "文本翻译类型")
    private TextTaskTypeEnums type;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "projcode")
    private List<ProjCodeEnums> projCode;

    @ApiModelProperty(value = "各语言任务信息")
    private List<SubTaskReq> languageTasks;

    @ApiModelProperty(value = "原任务信息")
    private CoreTextTaskI18nAddReq sourceTask;

    @ApiModelProperty(value = "原任务IdList")
    private List<Integer> sourceTaskIdList;


    @Data
    @Accessors(chain = true)
    public static class SubTaskReq{
        @ApiModelProperty(value = "翻译语种")
        private LanguageEnums language;

        @ApiModelProperty(value = "译文")
        private String translationText;
    }
}
