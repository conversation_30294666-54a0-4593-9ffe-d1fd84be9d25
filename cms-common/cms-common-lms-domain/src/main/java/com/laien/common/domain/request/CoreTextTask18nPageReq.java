package com.laien.common.domain.request;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.TextTaskStatusEnums;
import com.laien.common.domain.enums.TextTaskTypeEnums;
import com.laien.common.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * CoreTextTask18nPageReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreTextTask18nPageReq 分页", description = "CoreTextTask18nPageReq 分页")
public class CoreTextTask18nPageReq extends PageReq {

    @ApiModelProperty(value = "文本翻译类型")
    private TextTaskTypeEnums type;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "状态")
    private TextTaskStatusEnums status;

    @ApiModelProperty(value = "校验状态")
    private Boolean checkStatus;

    @ApiModelProperty(value = "proj codes")
    private List<ProjCodeEnums> projCode;

    @ApiModelProperty(value = "翻译语种")
    private List<LanguageEnums> language;

    /**
     * 用于sql查询使用
     * @return projCode的和
     */
    public int getProjCodeSum() {
        return BitmaskEnumUtil.sumBitmaskEnumList(this.getProjCode());
    }
}
