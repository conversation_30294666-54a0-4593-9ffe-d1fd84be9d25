package com.laien.common.domain.request;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import com.laien.common.domain.enums.TextTaskStatusEnums;
import com.laien.common.frame.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * CoreSpeechTask18nPageReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreSpeechTask18nPageReq 分页", description = "CoreSpeechTask18nPageReq 分页")
public class CoreSpeechTask18nPageReq extends PageReq {

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "校验状态")
    private Boolean checkStatus;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "状态")
    private SpeechTaskStatusEnums status;

    @ApiModelProperty(value = "是否ai缩减")
    private Boolean reduced;

    @ApiModelProperty(value = "是否超长")
    private Boolean overLength;

    @ApiModelProperty(value = "翻译语种")
    private List<LanguageEnums> language;

    @ApiModelProperty(value = "proj codes")
    private List<ProjCodeEnums> projCode;

    @ApiModelProperty(value = "translationId")
    private Integer coreTextTaskI18nId;

    @ApiModelProperty(value = "配置的英语音色id")
    private Integer coreVoiceConfigI18nId;

    /**
     * 用于sql查询使用
     * @return projCode的和
     */
    public int getProjCodeSum() {
        return BitmaskEnumUtil.sumBitmaskEnumList(this.getProjCode());
    }
}
