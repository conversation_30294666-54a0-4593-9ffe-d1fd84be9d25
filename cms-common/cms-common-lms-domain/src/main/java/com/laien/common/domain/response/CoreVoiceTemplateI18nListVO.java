package com.laien.common.domain.response;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.GenderEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *CoreVoiceTemplateI18nListVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreVoiceTemplateI18nListVO", description="CoreVoiceTemplateI18nListVO")
public class CoreVoiceTemplateI18nListVO{

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "翻译语种,多个用英文逗号分隔")
    private LanguageEnums language;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "audio url")
    private String audioUrl;
}
