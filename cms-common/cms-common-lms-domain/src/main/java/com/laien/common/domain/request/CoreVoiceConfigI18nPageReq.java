package com.laien.common.domain.request;

import com.laien.common.frame.request.PageReq;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.enums.StatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;

/**
 * <p>
 * CoreVoiceConfigI18nPageReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreVoiceConfigI18n 分页", description = "CoreVoiceConfigI18n 分页")
public class CoreVoiceConfigI18nPageReq extends PageReq {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "状态")
    private StatusEnums status;

    @ApiModelProperty(value = "id")
    private Collection<Integer> ids;

    @ApiModelProperty(value = "names")
    private Collection<String> names;
}
