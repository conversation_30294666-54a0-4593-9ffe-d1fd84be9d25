package com.laien.common.domain.bo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Slf4j
@Data
@Accessors(chain = true)
public class AppTextTranslateFieldBO {

    private AppTextCoreI18nModel model;
    private String fieldName;
    private String textMd5;
    private TranslationTaskTypeEnums type;
    private List<String> multipleTextMd5List;
    /**
     * key:原文md5,value：译文
     */
    private Map<String,String> multipleTranslateTextMap;
    private String multiTextDelimiter;


    public void addMultipleTranslateTextMap(String multipleTranslateText, String textMd5) {
        if (StrUtil.isBlank(multipleTranslateText)) {
            return;
        }
        if (CollUtil.isEmpty(multipleTranslateTextMap)) {
            multipleTranslateTextMap = new HashMap<>();
        }
        multipleTranslateTextMap.put(textMd5, multipleTranslateText);
    }

}
