package com.laien.common.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.TextTaskTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * CoreTextTaskI18nPageVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreTextTaskI18nPageVO", description="CoreTextTaskI18nPageVO")
public class CoreTextTaskI18nPageVO {

    @ApiModelProperty(value = "文本翻译类型")
    private TextTaskTypeEnums type;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "proj code的和")
    private List<ProjCodeEnums> projCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "子任务列表")
    private List<CoreTextTaskI18nVO> subTaskList;

    @ApiModelProperty(value = "子任务ids",hidden = true)
    @JsonIgnore
    private String subTaskIds;
}
