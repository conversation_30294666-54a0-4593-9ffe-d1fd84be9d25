package com.laien.common.domain.annotation;

import com.laien.common.domain.enums.TranslationTaskTypeEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TranslateField {

    TranslationTaskTypeEnums type() default TranslationTaskTypeEnums.TEXT;

    /**
     * 如果不填就不限制最大时长
     */
    String durationFieldName() default "";


    String audioUrlFieldName() default "";

    String multiTextDelimiter() default ",";

}
