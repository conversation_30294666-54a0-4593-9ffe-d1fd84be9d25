package com.laien.common.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.frame.entity.BaseModel;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.enums.StatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("core_voice_template_i18n")
@ApiModel(value="CoreVoiceTemplateI18n对象")
public class CoreVoiceTemplateI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "翻译语种,多个用英文逗号分隔")
    private LanguageEnums language;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "audio url")
    private String audioUrl;

    @ApiModelProperty(value = "是否对应语言+性别的默认音色")
    private boolean defaultVoice;

    @ApiModelProperty(value = "状态")
    private StatusEnums status;


}
