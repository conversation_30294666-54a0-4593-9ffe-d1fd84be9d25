package com.laien.common.domain.bo;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.entity.CoreVoiceConfigTemplateI18n;
import com.laien.common.domain.entity.CoreVoiceTemplateI18n;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
@Data
@Accessors(chain = true)
public class Core18nContextBO {

    private List<CoreI18nModel> coreI18nModelList;
    private List<LanguageEnums> textLanguageList;
    private List<LanguageEnums> audioLanguageList;
    private ProjCodeEnums projCode;
    private List<FieldBO> fieldList;
    private List<FieldBO> audioFieldList;
    private Set<String> md5Set;
    private Set<Integer> coreVoiceConfigI18nIdSet = new HashSet<>();
    private Map<Integer, List<CoreVoiceConfigTemplateI18n>> voiceConfigIdGroup;
    private Map<Integer, CoreVoiceTemplateI18n> voiceTemplateIdGroup;


}
