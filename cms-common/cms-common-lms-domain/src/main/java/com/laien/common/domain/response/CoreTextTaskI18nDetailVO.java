package com.laien.common.domain.response;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.TextTaskTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * CoreTextTaskI18nVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreTextTaskI18nVO", description="CoreTextTaskI18nVO")
public class CoreTextTaskI18nDetailVO {

    @ApiModelProperty(value = "文本翻译类型")
    private TextTaskTypeEnums type;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "projcode")
    private List<ProjCodeEnums> projCode;

    @ApiModelProperty(value = "languageTasks")
    private List<SubTaskVO> languageTasks;

    @ApiModelProperty
    private List<LanguageEnums> selectableLanguages;

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class SubTaskVO{
        @ApiModelProperty(value = "翻译语种")
        private LanguageEnums language;

        @ApiModelProperty(value = "译文")
        private String translationText;
    }
}
