package com.laien.common.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Getter
public enum TranslationTaskTypeEnums {

    TEXT(1,TextTaskTypeEnums.TEXT),
    HTML(2,TextTaskTypeEnums.HTML),
    SPEECH(3,TextTaskTypeEnums.TEXT),
    MULTIPLE_TEXT(4,TextTaskTypeEnums.TEXT);

    @EnumValue
    private final Integer code;
    private final TextTaskTypeEnums textTaskType;

    TranslationTaskTypeEnums(Integer code,TextTaskTypeEnums  textTaskType) {
        this.code = code;
        this.textTaskType = textTaskType;
    }
}
