package com.laien.common.domain.component;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * BaseAudioI18nModel
 * </p>
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BaseAudioI18nModel", description = "BaseAudioI18nModel")
public class BaseAudioI18nModel implements AppAudioCoreI18nModel {

    @ApiModelProperty(value = "uniqueKey")
    private Object uniqueKey;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "result")
    private List<AudioTranslateResultModel> result;
}
