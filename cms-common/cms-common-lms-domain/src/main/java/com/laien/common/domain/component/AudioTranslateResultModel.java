package com.laien.common.domain.component;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.GenderEnums;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Accessors(chain = true)
@Data
public class AudioTranslateResultModel {

    private Integer id;
    private GenderEnums gender;
    private LanguageEnums language;
    private String text;
    private String audioUrl;
    private Integer duration;

}
