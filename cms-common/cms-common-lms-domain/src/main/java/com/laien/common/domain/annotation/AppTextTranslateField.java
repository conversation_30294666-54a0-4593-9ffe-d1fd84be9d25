package com.laien.common.domain.annotation;

import com.laien.common.domain.enums.TranslationTaskTypeEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AppTextTranslateField {

    /**
     * 翻译类型，只能填：TEXT,HTML,MULTIPLE_TEXT
     */
    TranslationTaskTypeEnums type() default TranslationTaskTypeEnums.TEXT;

    String multiTextDelimiter() default ",";

}
