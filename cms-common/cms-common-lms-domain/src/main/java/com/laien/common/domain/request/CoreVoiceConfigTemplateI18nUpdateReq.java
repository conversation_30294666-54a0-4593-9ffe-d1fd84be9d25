package com.laien.common.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * CoreVoiceConfigTemplateI18nUpdateReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreVoiceConfigTemplateI18nUpdateReq", description="CoreVoiceConfigTemplateI18nUpdateReq")
public class CoreVoiceConfigTemplateI18nUpdateReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "下拉框旋选中的template的数据Id")
    private Integer coreVoiceTemplateI18nId;
}
