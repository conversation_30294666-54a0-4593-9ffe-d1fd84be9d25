package com.laien.common.domain.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.laien.common.core.converter.GenericEnumNameConverter;
import com.laien.common.core.enums.LanguageEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * CoreBusinessTaskRelationVO导出 VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreBusinessTaskRelationVO", description = "CoreBusinessTaskRelationVO")
public class CoreBusinessTaskRelationVO {

    @ExcelProperty("Id")
    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ExcelProperty("coreTextTaskI18nId")
    @ApiModelProperty(value = "配置的英语音色id")
    private Integer coreTextTaskI18nId;

    @ExcelProperty(value = "language", converter = GenericEnumNameConverter.class)
    @ApiModelProperty(value = "语种")
    private LanguageEnums language;

    @ExcelProperty("businessName")
    @ApiModelProperty(value = "业务名")
    private String businessName;

    @ExcelProperty("businessField")
    @ApiModelProperty(value = "业务字段")
    private String businessField;

    @ExcelProperty("businessDataId")
    @ApiModelProperty(value = "业务数据id")
    private Integer businessDataId;

    @ExcelProperty("coreTextTaskI18nId")
    @ApiModelProperty(value = "app code")
    private String appCode;

}
