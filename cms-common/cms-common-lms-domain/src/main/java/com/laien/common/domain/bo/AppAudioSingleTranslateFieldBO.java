package com.laien.common.domain.bo;

import com.laien.common.domain.component.AppAudioCoreI18nModel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025/6/12
 */
@Slf4j
@Data
@Accessors(chain = true)
public class AppAudioSingleTranslateFieldBO {

    private AppAudioCoreI18nModel model;
    private String textFieldName;
    private String durationFieldName;
    private String urlFieldName;
    private Integer coreVoiceConfigI18nId;
    private Integer voiceConfigTemplateI18nId;
    private String textMd5;

}
