package com.laien.common.domain.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AppAudioTranslateField {

    /**
     * 语言翻译结果字段，字段类型必须是AudioTranslateResultModel list
     */
    String resultFieldName() default "";

}
