package com.laien.common.domain.response;

import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.AuditTypeEnums;
import com.laien.common.domain.enums.TextTaskStatusEnums;
import com.laien.common.domain.enums.TextTaskTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * CoreTextTaskI18nVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreTextTaskI18nVO", description="CoreTextTaskI18nVO")
public class CoreTextTaskI18nVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "文本翻译类型")
    private TextTaskTypeEnums type;

    @ApiModelProperty(value = "翻译语种")
    private LanguageEnums language;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "状态")
    private TextTaskStatusEnums status;

    @ApiModelProperty(value = "校验状态")
    private boolean checkStatus;

    @ApiModelProperty(value = "审核类型")
    private AuditTypeEnums auditType;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "译文")
    private String translationText;
}
