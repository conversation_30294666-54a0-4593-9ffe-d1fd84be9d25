package com.laien.common.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * CoreTextTaskI18nPageVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CoreTextTaskI18nPageVO", description="CoreTextTaskI18nPageVO")
public class CoreSpeechTaskI18nPageVO {

    @ApiModelProperty(value = "配置的英语音色id")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "配置的英语音色名称")
    private String coreVoiceConfigName;

    @ApiModelProperty(value = "原文")
    private String text;

    @ApiModelProperty(value = "原文md5")
    private String textMd5;

    @ApiModelProperty(value = "原文音频")
    private String audioUrl;

    @ApiModelProperty(value = "原文音频时长")
    private Integer duration;

    @ApiModelProperty(value = "性别")
    private GenderEnums gender;

    @ApiModelProperty(value = "proj code的和")
    private List<ProjCodeEnums> projCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "子任务列表")
    private List<CoreSpeechTaskI18nVO> subTaskList;

    @ApiModelProperty(value = "子任务ids",hidden = true)
    @JsonIgnore
    private String subTaskIds;

    @ApiModelProperty(value = "translationId")
    private Integer coreTextTaskI18nId;
}
