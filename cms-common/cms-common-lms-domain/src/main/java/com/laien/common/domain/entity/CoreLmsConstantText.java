package com.laien.common.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.frame.entity.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(autoResultMap = true)
@ApiModel(value="CoreTextTaskI18n对象")
public class CoreLmsConstantText extends BaseModel implements CoreI18nModel {

    @ApiModelProperty(value = "原文")
    @TranslateField
    private String text;

    @ApiModelProperty(value = "proj code的和")
    @TableField(typeHandler = ProjCodeEnums.TypeHandler.class)
    private List<ProjCodeEnums> projCode;
}
