package com.laien.common.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *CoreTextTaskI18nUpdateReq
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CoreTextTaskI18nUpdateReq", description = "CoreTextTaskI18nUpdateReq")
public class CoreTaskI18nUpdateReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "译文")
    private String translationText;
}
