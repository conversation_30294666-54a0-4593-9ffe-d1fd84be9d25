/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.common.oog104.enums.template;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import com.laien.common.core.enums.IEnumBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>Exclusive Type </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Getter
@AllArgsConstructor
public enum ExclusiveTypeEnums implements IEnumBase{
    // 类型
    NORMAL(1,"Normal"),
    PREGNANT(2,"Pregnant"),
    POSTPARTUM(3,"Postpartum"),
    INJURY(4,"Injury"),
    MENOPAUSE(5,"Menopause"),
    DAILY_STRETCH(6, "Daily Stretch"),
    DAILY_HIIT(7, "Daily HIIT"),
    DAILY_ABS(8, "Daily Abs"),
    DAILY_BOOTY(9, "Daily Booty"),
    DAILY_FAT_LOSS(10, "Daily Fat Loss")

    ;

    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<ExclusiveTypeEnums> {
    }
}