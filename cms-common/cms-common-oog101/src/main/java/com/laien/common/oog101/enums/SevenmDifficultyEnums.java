package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * 7M Difficulty 枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Getter
public enum SevenmDifficultyEnums implements IEnumBase {
    NEWBIE(1, "Newbie", "Newbie"),
    BEGINNER(2, "Beginner", "Beginner"),
    INTERMEDIATE(3, "Intermediate", "Intermediate"),
    ADVANCED(4, "Advanced", "Advanced");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmDifficultyEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmDifficultyEnums> {
    }
}
