package com.laien.common.oog101.enums;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * 部位枚举
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmTargetEnums implements IEnumBase,SevenmEnumConvertible  {
    NONE(1, "None", "None"),
    ARMS(1 << 1, "Arms", "Arms"),
    BACK(1 << 2,"Back", "Back"),
    CHEST(1 << 3, "Chest", "Chest"),
    ABS(1 << 4, "Abs", "Abs"),
    BUTT(1 << 5, "Butt", "Butt"),
    LEGS(1 << 6, "Legs", "Legs"),
    FULL_BODY(1 << 7, "Full Body", "Full Body"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmTargetEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static Integer getSum(Collection<SevenmTargetEnums> targetCollection) {
        int sum = 0;
        Set<SevenmTargetEnums> targetSet = new HashSet<>(targetCollection);
        if (CollUtil.isEmpty(targetSet)) {
            return sum;
        }
        // 根据code求和
        for (SevenmTargetEnums target : targetSet) {
            sum += target.getCode();
        }
        return sum;
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<SevenmTargetEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmTargetEnums> {
    }

    @Override
    public Enum<?> getDefaultValue() {
        return FULL_BODY;
    }

    public static SevenmTargetEnums safeValueOf(String name) {
        return SevenmEnumConvertible.safeValueOf(SevenmTargetEnums.class, name);
    }

}
