package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * Intensity 枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmIntensityEnums implements IEnumBase {

    STRETCH(1, "Stretch", "Stretch"),
    CARDIO(2, "Cardio", "Cardio"),
    HIIT(3, "Hiit", "Hiit"),
    POWER(4, "Power", "Power");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmIntensityEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }
    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmIntensityEnums> {
    }
}
