package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Proj7MTypeEnums
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmTypeEnums implements IEnumBase {
    WARM_UP(1, "Warm up", "Warm up"),
    MAIN(2, "Main", "Main"),
    COOL_DOWN(3, "Cool Down", "Cool Down"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmTypeEnums> {
    }

    /**
     * 获取所有name字段的set
     * @return  set
     */
    public static Set<String> getAllNameSet() {
        return Arrays.stream(SevenmTypeEnums.values())
                .map(SevenmTypeEnums::getName)
                .collect(Collectors.toSet());
    }
}
