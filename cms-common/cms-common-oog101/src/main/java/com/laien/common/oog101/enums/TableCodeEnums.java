/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.common.oog101.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.laien.common.core.enums.IEnumBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/**
 * <p>区分不同缓存数据对应的表 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Getter
@AllArgsConstructor
public enum TableCodeEnums implements IEnumBase {

    // 表名
    PROJ_SEVENM_FASTING_ARTICLE(1, "proj_sevenm_fasting_article"),
    PROJ_SEVENM_EXERCISE_VIDEO(2, "proj_sevenm_exercise_video"),
    PROJ_SEVENM_MUSIC(3, "proj_sevenm_music"),
    PROJ_SEVENM_PLAYLIST(4, "proj_sevenm_playlist"),
    PROJ_SEVENM_WORKOUT_IMAGE(5, "proj_sevenm_workout_image"),
    PROJ_SEVENM_MANUAL_WORKOUT(6, "proj_sevenm_manual_workout"),
    PROJ_SEVENM_TEMPLATE(7, "proj_sevenm_template"),
    PROJ_SEVENM_TEMPLATE_EXERCISE_GROUP(8, "proj_sevenm_template_exercise_group"),
    PROJ_SEVENM_TEMPLATE_TASK(9, "proj_sevenm_template_task"),
    PROJ_SEVENM_WORKOUT_GENERATE(10, "proj_sevenm_workout_generate"),
    PROJ_SEVENM_SOUND(11,"proj_sevenm_sound"),
    ;
    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    public static class Deserializer extends JsonDeserializer<TableCodeEnums> {

        @Override
        public TableCodeEnums deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {

            int code = jsonParser.getIntValue();
            return Arrays.stream(TableCodeEnums.values())
                    .filter(t-> Objects.equals(t.getCode(),code)).findFirst()
                    .orElseThrow(()->new IllegalArgumentException("TableCodeEnums code is not exist"));
        }
    }

    public static TableCodeEnums get(Integer code) {
        return Arrays.stream(values()).filter(item -> Objects.equals(item.getCode(),code)).findFirst().orElse(null);
    }
}
