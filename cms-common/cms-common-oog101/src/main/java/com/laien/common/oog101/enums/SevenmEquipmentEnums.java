package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * 器械枚举
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmEquipmentEnums implements IEnumBase {

    NO_EQUIPMENT(1, "No Equipment", "No Equipment"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmEquipmentEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }


    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<SevenmEquipmentEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmEquipmentEnums> {
    }
}
