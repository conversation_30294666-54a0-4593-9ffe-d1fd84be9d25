package com.laien.common.oog101.enums;

/**
 * 通用安全转换（该类转换仅限于采用URL参数传递时生效，原因为转换机制是通过Converter<String, Enum> 处理，）
 * 当传递参数方式为body时不生效，因为为采用 Jackson 反序列化处理，需要单独配置 Jackson 的枚举处理规则
 */
public interface SevenmEnumConvertible {
    /**
     * 获取默认枚举值
     */
    Enum<?> getDefaultValue();

    /**
     * 根据名称安全转换枚举
     */
    static <E extends Enum<E> & SevenmEnumConvertible> E safeValueOf(Class<E> enumClass, String name) {
        if (name == null || name.isEmpty()) {
            return (E) enumClass.getEnumConstants()[0].getDefaultValue();
        }
        try {
            return Enum.valueOf(enumClass, name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return (E) enumClass.getEnumConstants()[0].getDefaultValue();
        }
    }
}