package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

@Getter
@AllArgsConstructor
public enum SevenmSoundSubTypeEnums implements IEnumBase {
    PROMPT(1, "Prompt"),
    BASIC(2, "Basic"),
    COMPLETE(3, "Complete"),
    WELCOME(4, "Welcome"),
    FREE_WALK_BEGIN(5, "Free Walk Begin"),
    FREE_WALK_PROGRESS_5MIN(6, "Free Walk Progress-5Min"),
    FREE_WALK_PROGRESS_15MIN(7, "Free Walk Progress-15Min"),
    FREE_WALK_PROGRESS_30MIN(8, "Free Walk Progress-30Min"),
    FREE_WALK_REACH_GOAL(9, "Free Walk Reach Goal"),
    FREE_WALK_COMPLETED(10, "Free Walk Completed"),
    TARGET_WALK_BEGIN(11, "Target Walk Begin"),
    TARGET_WALK_PROGRESS_25(12, "Target Walk Progress-25%"),
    TARGET_WALK_PROGRESS_50(13, "Target Walk Progress-50%"),
    TARGET_WALK_PROGRESS_75(14, "Target Walk Progress-75%"),
    TARGET_WALK_REACH_GOAL(15, "Target Walk Reach Goal"),
    BURNING_WALK_BEGIN(16, "Burning Walk Begin"),
    BURNING_WALK_PROGRESS_SLOW(17, "Burning Walk Progress-Slow"),
    BURNING_WALK_PROGRESS_FASTER(18, "Burning Walk Progress-Faster"),
    BURNING_WALK_COMPLETED(19, "Burning Walk Completed"),
    MINDFUL_WALK_BEGIN_BEACH(20, "Mindful Walk Begin-Beach"),
    MINDFUL_WALK_BEGIN_FOREST(21, "Mindful Walk Begin-Forest"),
    MINDFUL_WALK_BEGIN_CREEK(22, "Mindful Walk Begin-Creek"),
    MINDFUL_WALK_BEGIN_VALLEY(23, "Mindful Walk Begin-Valley"),
    MINDFUL_WALK_PROGRESS(24, "Mindful Walk Progress"),
    MINDFUL_WALK_COMPLETED_BEACH(25, "Mindful Walk Completed-Beach"),
    MINDFUL_WALK_COMPLETED_FOREST(26, "Mindful Walk Completed-Forest"),
    MINDFUL_WALK_COMPLETED_CREEK(27, "Mindful Walk Completed-Creek"),
    MINDFUL_WALK_COMPLETED_VALLEY(28, "Mindful Walk Completed-Valley");


    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmSoundSubTypeEnums> {
    }
}
