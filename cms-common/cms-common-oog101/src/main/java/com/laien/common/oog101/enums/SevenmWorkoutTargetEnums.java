package com.laien.common.oog101.enums;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部位枚举
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmWorkoutTargetEnums implements IEnumBase  {
    ARMS_AND_BACK(1, "Arms & Back", "Arms & Back",
            ListUtil.of(new TargetMapping(1.0f,SevenmTargetEnums.ARMS,SevenmTargetEnums.BACK))),
    ABS(1 << 1,"Abs", "Abs",
            ListUtil.of(new TargetMapping(0.8f,SevenmTargetEnums.ABS))),
    CHEST(1 << 2, "Chest", "Chest",
            ListUtil.of(new TargetMapping(0.8f,SevenmTargetEnums.CHEST))),
    BUTT_AND_LEGS(1 << 3, "Butt & Legs", "Butt & Legs",
            ListUtil.of(new TargetMapping(1.0f,SevenmTargetEnums.BUTT,SevenmTargetEnums.LEGS))),
    FULL_BODY(1 << 4, "Full Body", "Full Body",
            ListUtil.of(new TargetMapping(1.0f,SevenmTargetEnums.FULL_BODY))),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;
    private final List<TargetMapping> relatedTargets;

    SevenmWorkoutTargetEnums(Integer code, String name, String displayName, List<TargetMapping> relatedTargets) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
        this.relatedTargets = relatedTargets;
    }

    public List<SevenmTargetEnums> getRelatedTargetsEnumsList(){
        return this.relatedTargets.stream().map(TargetMapping::getTarget).flatMap(Collection::stream).collect(Collectors.toList());
    }
    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<SevenmWorkoutTargetEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmWorkoutTargetEnums> {
    }

    @Data
    @AllArgsConstructor
    public static class TargetMapping {
        private List<SevenmTargetEnums> target;
        private float percent;

        TargetMapping(float percent,SevenmTargetEnums... target) {
            this.target = ListUtil.of(target);
            this.percent = percent;
        }
    }
}
