package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * VideoDirectionEnums
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmVideoDirectionEnums implements IEnumBase {

    CENTRAL(1, "Central", "Central"),
    LEFT(2, "Left", "Left"),
    RIGHT(3, "Right", "Right");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmVideoDirectionEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmVideoDirectionEnums> {
    }
}
