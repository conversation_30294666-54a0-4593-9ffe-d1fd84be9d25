package com.laien.common.oog101.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.Getter;

import java.util.*;

/**
 * 部位枚举
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmTargetMappingEnums {

    NONE(Collections.singletonList(SevenmTargetEnums.NONE)),
    ARMS_BACK(Arrays.asList(SevenmTargetEnums.ARMS,SevenmTargetEnums.BACK)),
    CHEST(Collections.singletonList(SevenmTargetEnums.CHEST)),
    ABS(Collections.singletonList(SevenmTargetEnums.ABS)),
    BUTT_LEGS(Arrays.asList(SevenmTargetEnums.BUTT,SevenmTargetEnums.LEGS)),
    FULL_BODY(Collections.singletonList(SevenmTargetEnums.FULL_BODY)),
    ;

    private final List<SevenmTargetEnums> targetList;

    SevenmTargetMappingEnums(List<SevenmTargetEnums> targetList) {
        this.targetList = targetList;
    }

    public static Set<SevenmTargetMappingEnums> toMappingSet(Set<SevenmTargetEnums> targetSet) {
        Set<SevenmTargetMappingEnums> mappingSet = new HashSet<>();
        if(CollUtil.isEmpty(targetSet)){
            return mappingSet;
        }
        for (SevenmTargetMappingEnums mappingEnums : values()) {
            for (SevenmTargetEnums target : targetSet) {
                if (mappingEnums.targetList.contains(target)) {
                    mappingSet.add(mappingEnums);
                }
            }
        }
        return mappingSet;
    }

}
