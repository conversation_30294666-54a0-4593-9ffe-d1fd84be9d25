package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * Proj7MExerciseTypeEnums枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmExerciseTypeEnums implements IEnumBase {
    REGULAR_FITNESS(1, "Regular Fitness", "Regular Fitness",
            SevenmGenderEnums.FEMALE, SevenmEquipmentEnums.NO_EQUIPMENT),
    FITNESS_105(1 << 1, "105 Fitness", "105 Fitness",
            SevenmGenderEnums.MALE, SevenmEquipmentEnums.NO_EQUIPMENT),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;
    private final SevenmGenderEnums gender;
    private final SevenmEquipmentEnums equipment;

    SevenmExerciseTypeEnums(Integer code, String name, String displayName,
                            SevenmGenderEnums gender, SevenmEquipmentEnums equipment) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
        this.gender = gender;
        this.equipment = equipment;
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<SevenmExerciseTypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmExerciseTypeEnums> {
    }
}
