package com.laien.common.oog101.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseListHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Getter
@AllArgsConstructor
public enum SevenmTemplateTaskStatusEnum implements IEnumBase {

    //待处理
    PENDING(1,"Pending"),
    RUNNING(2,"Running"),
    FAIL(3,"Fail"),
    SUCCESS(4,"Success"),

    ;
    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmTemplateTaskStatusEnum> {
    }

    /**
     * 枚举集合类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseListHandler<SevenmTemplateTaskStatusEnum> {
    }

}
