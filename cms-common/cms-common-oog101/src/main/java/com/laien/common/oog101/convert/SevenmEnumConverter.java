package com.laien.common.oog101.convert;

import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Slf4j
public class SevenmEnumConverter {


    /**
     * 性别枚举转换器
     */
    public static class GenderConverter implements Converter<String, SevenmGenderEnums> {
        @Override
        public SevenmGenderEnums convert(String source) {
            SevenmGenderEnums result = SevenmGenderEnums.safeValueOf(source);
            if (!source.equalsIgnoreCase(result.name())) {
                log.error("Invalid SevenmGenderEnums value: [{}]. Using default: [{}].",
                        source, result.name());
            }
            return result;
        }
    }

    /**
     * 目标部位枚举转换器
     */
    public static class TargetConverter implements Converter<String, SevenmTargetEnums> {
        @Override
        public SevenmTargetEnums convert(String source) {
            SevenmTargetEnums result = SevenmTargetEnums.safeValueOf(source);
            if (!source.equalsIgnoreCase(result.name())) {
                log.error("Invalid SevenmTargetEnums value: [{}]. Using default: [{}].",
                        source, result.name());
            }
            return result;
        }
    }
}
