package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * SevenmMusicTypeEnum
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum SevenmMusicTypeEnums implements IEnumBase {

    NORMAL_BGM(1, "Normal BGM",  "Normal BGM"),
    TABATA_BGM(2, "Tabata BGM",  "Tabata BGM"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmMusicTypeEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmMusicTypeEnums> {
    }

}
