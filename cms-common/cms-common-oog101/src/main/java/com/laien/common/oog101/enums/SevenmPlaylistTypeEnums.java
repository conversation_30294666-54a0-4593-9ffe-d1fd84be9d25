package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
@AllArgsConstructor
public enum SevenmPlaylistTypeEnums implements IEnumBase {

    SEVENM_NORMAL_WORKOUT(1, "7M Normal Workout"),
    SEVENM_STRETCH_WORKOUT(2, "7M Stretch Workout"),
    REGULAR_WORKOUT(3, "Regular Workout"),
    FREE_WALK(4, "Free Walk"),
    TARGET_WALK(5, "Target Walk"),
    BURNING_WALK_FAST(6, "Burning Walk-Fast"),
    BURNING_WALK_SLOWLY(7, "Burning Walk-Slowly"),
    MINDFUL_WALK(8, "Mindful Walk"),
    SEVENM_TABATA_WORKOUT(9, "7M Tabata Workout"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmPlaylistTypeEnums> {
    }

}
