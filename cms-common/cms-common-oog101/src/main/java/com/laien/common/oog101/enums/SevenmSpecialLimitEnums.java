package com.laien.common.oog101.enums;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Proj7MSpecialLimits枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
@AllArgsConstructor
public enum SevenmSpecialLimitEnums implements IEnumBase {

    WRISTS(1, "Wrists", "Wrists"),
    FEET(1 << 1, "Feet", "Feet"),
    BACK(1 << 2, "Back", "Back"),
    SHOULDERS(1 << 3, "Shoulders", "Shoulders"),
    ABS(1 << 4, "Abs", "Abs"),
    KNEES(1 << 5, "Knees", "Knees");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<SevenmSpecialLimitEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmSpecialLimitEnums> {
    }

    /**
     * 获取在传入list范围内的所有限制组合。
     * 包括空集 (empty combination)。
     *
     * @param availableLimits The list of SevenmSpecialLimitEnums to generate combinations from.
     * @return A list of all possible combinations (subsets) of the input list.
     * Returns a list containing an empty list if the input is empty.
     * Returns an empty list if the input is null.
     */
    public static List<List<SevenmSpecialLimitEnums>> getAllCombination(List<SevenmSpecialLimitEnums> availableLimits) {
        if (CollUtil.isEmpty(availableLimits)) {
            availableLimits = CollUtil.newArrayList(SevenmSpecialLimitEnums.values());
        }

        List<List<SevenmSpecialLimitEnums>> result = new ArrayList<>();
        int n = availableLimits.size();
        // There are 2^n possible combinations (subsets)
        int maxCombinations = 1 << n; // Equivalent to Math.pow(2, n)

        for (int i = 1; i < maxCombinations; i++) { // i represents a bitmask
            List<SevenmSpecialLimitEnums> currentCombination = new ArrayList<>();
            for (int j = 0; j < n; j++) {
                // Check if the j-th bit is set in the bitmask i
                // This means we should include the j-th element from availableLimits in this combination
                if ((i & (1 << j)) != 0) {
                    currentCombination.add(availableLimits.get(j));
                }
            }
            result.add(currentCombination);
        }
        return result;
    }

    public static Integer getSum(Collection<SevenmSpecialLimitEnums> collection){
        int sum = 0;
        if(CollUtil.isEmpty(collection)){
            return sum;
        }
        //根据code求和
        for (SevenmSpecialLimitEnums specialLimit : collection) {
            sum += specialLimit.getCode();
        }
        return sum;
    }
}
