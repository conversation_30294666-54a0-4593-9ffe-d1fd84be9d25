package com.laien.common.oog101.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * <p>
 * 枚举：SevenmWorkoutCategoryEnums
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Getter
public enum SevenmWorkoutCategoryEnums implements IEnumBase {

    FIVE_MINUTE_FAT_BURNERS(1, "5 Minute Fat Burners", "5 Minute Fat Burners"),
    TRENDING(1 << 1, "Trending", "Trending"),
    QUICK_FIT(1 << 2, "Quick Fit", "Quick Fit"),
    HITT_ZONE(1 << 3, "Hitt Zone", "Hitt Zone"),
    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    SevenmWorkoutCategoryEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<SevenmWorkoutCategoryEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SevenmWorkoutCategoryEnums> {
    }
}
