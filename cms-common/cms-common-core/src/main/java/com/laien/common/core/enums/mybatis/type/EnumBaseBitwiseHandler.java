package com.laien.common.core.enums.mybatis.type;

import cn.hutool.core.collection.CollUtil;
import com.laien.common.core.enums.IEnumBase;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于处理位运算枚举（如 Restriction116Enums）的 MyBatis TypeHandler
 * 存储时将枚举列表转换为整数（或位掩码和），读取时将整数解析为枚举集合
 * <AUTHOR>
 * @since 2025/05/09
 */
public abstract class EnumBaseBitwiseHandler<T extends IEnumBase> extends BaseTypeHandler<List<T>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        int sum = CollUtil.isEmpty(parameter) ? 0 : parameter.stream().map(IEnumBase::getCode).reduce(0, Integer::sum);
        ps.setInt(i, sum);
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseEnumList(rs.getInt(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseEnumList(rs.getInt(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseEnumList(cs.getInt(columnIndex));
    }

    private List<T> parseEnumList(Integer sum) {
        if (sum == null || sum == 0) {
            return new ArrayList<>();
        }

        List<T> list = new ArrayList<>();
        Class<T> enumClass = (Class<T>) getRawType();
        for (T enumConstant : enumClass.getEnumConstants()) {
            int value = enumConstant.getCode(); // 假设 IEnumBase#getCode 返回的是位掩码值
            if ((value & sum) == value) {
                list.add(enumConstant);
            }
        }
        return list;
    }

    @Override
    public List<T> getResult(ResultSet rs, String columnName) throws SQLException {
        return super.getResult(rs, columnName);
    }

    @Override
    public List<T> getResult(ResultSet rs, int columnIndex) throws SQLException {
        return super.getResult(rs, columnIndex);
    }

    @Override
    public List<T> getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return super.getResult(cs, columnIndex);
    }

}
