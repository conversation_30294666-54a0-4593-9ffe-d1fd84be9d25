package com.laien.common.core.aggregator;

import cn.hutool.core.collection.CollUtil;
import com.laien.common.frame.entity.BaseModel;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 通用任务聚合工具类
 * 提供高性能的分组聚合功能
 *
 * <AUTHOR>
 * @since 2025/07/11
 */
public class TaskAggregatorUtil {

    /**
     * 高性能分组聚合处理 - 单次遍历完成所有聚合操作
     *
     * @param allTasks 所有符合条件的任务数据
     * @param keyGenerator 分组键生成函数
     * @param aggregatorFactory 聚合器工厂函数
     * @param <T> 实体类型，必须继承BaseModel
     * @param <A> 聚合器类型
     * @return 分组聚合后的聚合器列表
     */
    public static <T extends BaseModel, A extends BaseTaskAggregator<T, ?>>
            List<A> groupAndAggregate(List<T> allTasks,
                                    Function<T, String> keyGenerator,
                                    Function<T, A> aggregatorFactory) {

        if (CollUtil.isEmpty(allTasks)) {
            return new ArrayList<>();
        }

        // 使用LinkedHashMap保持插入顺序，预分配容量减少扩容
        Map<String, A> aggregatorMap = new LinkedHashMap<>(allTasks.size() / 2);

        // 单次遍历完成分组和聚合
        for (T task : allTasks) {
            // 生成分组键
            String groupKey = keyGenerator.apply(task);

            // 获取或创建聚合器
            A aggregator = aggregatorMap.get(groupKey);
            if (aggregator == null) {
                aggregator = aggregatorFactory.apply(task);
                aggregatorMap.put(groupKey, aggregator);
            } else {
                aggregator.aggregate(task);
            }
        }

        // 返回聚合器列表
        return new ArrayList<>(aggregatorMap.values());
    }

    /**
     * 带StringBuilder复用的分组聚合处理
     * 适用于需要复杂字符串拼接的分组键场景
     *
     * @param allTasks 所有符合条件的任务数据
     * @param keyBuilder 分组键构建函数，接收task和可复用的StringBuilder
     * @param aggregatorFactory 聚合器工厂函数
     * @param <T> 实体类型，必须继承BaseModel
     * @param <A> 聚合器类型
     * @return 分组聚合后的聚合器列表
     */
    public static <T extends BaseModel, A extends BaseTaskAggregator<T, ?>>
            List<A> groupAndAggregateWithKeyBuilder(List<T> allTasks,
                                                   KeyBuilder<T> keyBuilder,
                                                   Function<T, A> aggregatorFactory) {

        if (CollUtil.isEmpty(allTasks)) {
            return new ArrayList<>();
        }

        // 使用LinkedHashMap保持插入顺序，预分配容量减少扩容
        Map<String, A> aggregatorMap = new LinkedHashMap<>(allTasks.size() / 2);
        StringBuilder stringBuilder = new StringBuilder(64); // 复用StringBuilder

        // 单次遍历完成分组和聚合
        for (T task : allTasks) {
            // 生成分组键
            String groupKey = keyBuilder.buildKey(task, stringBuilder);

            // 获取或创建聚合器
            A aggregator = aggregatorMap.get(groupKey);
            if (aggregator == null) {
                aggregator = aggregatorFactory.apply(task);
                aggregatorMap.put(groupKey, aggregator);
            } else {
                aggregator.aggregate(task);
            }
        }

        // 返回聚合器列表
        return new ArrayList<>(aggregatorMap.values());
    }

    /**
     * 分组键构建器接口
     * @param <T> 实体类型
     */
    @FunctionalInterface
    public interface KeyBuilder<T> {
        /**
         * 构建分组键
         * @param task 任务实体
         * @param keyBuilder 可复用的StringBuilder
         * @return 分组键字符串
         */
        String buildKey(T task, StringBuilder keyBuilder);
    }
}
