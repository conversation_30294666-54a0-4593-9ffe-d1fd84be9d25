package com.laien.common.core.enums;

import java.util.Optional;

import static com.laien.common.core.constant.CmsCommonConstant.ENUM_NAME_METHOD;

/**
 * 以下三个接口在子类中一定要实现，因为在IBaseEnumInterface中会调用
 */
public interface IEnumBase {

    /**
     * 数据库中存储的编号，不可变更
     *
     * @return
     */
    Integer getCode();

    /**
     * 数据库中也许会存储的名称，不可变更
     *
     * @return
     */
    String getName();

    /**
     * 展示名称
     *
     * @return
     */
    String getDisplayName();

    default Integer getShowCode(){
        return this.getCode();
    }

    /**
     * 是否展示，多用于隐藏一些废弃的枚举类型，但为了兼容老数据，有时又必须保持他们之间的映射关系
     *
     * @return
     */
    default Boolean getShow() {
        return Boolean.TRUE;
    }

    default String getEnumName() {
        return Optional.ofNullable(this.getClass()).filter(c -> c.isEnum()).map(c -> {
            try {
                return (String) c.getMethod(ENUM_NAME_METHOD).invoke(this);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).orElse(null);
    }
}
