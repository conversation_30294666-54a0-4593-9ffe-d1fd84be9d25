package com.laien.common.core.enums.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.util.EnumBaseUtils;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.8.RELEASE
 */
public class EnumBaseCodeSerializer extends JsonSerializer<Object> {

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value instanceof IEnumBase) {
            gen.writeNumber(EnumBaseUtils.getCode((IEnumBase) value));
        }
        if (value instanceof List) {
            final int[] result = ((List) value).stream().filter(o -> o instanceof IEnumBase).mapToInt(o -> EnumBaseUtils.getCode((IEnumBase) o).intValue()).toArray();
            gen.writeArray(result, 0, result.length);
        }
    }
}