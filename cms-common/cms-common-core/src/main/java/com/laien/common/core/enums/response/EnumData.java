package com.laien.common.core.enums.response;

import com.laien.common.core.enums.IEnumBase;
import lombok.Data;

@Data
public class EnumData {
    private Integer code;
    private String name;
    private String displayName;
    private String enumName;

    public static EnumData toEnumData(IEnumBase iEnumBase) {
        EnumData enumData = new EnumData();
        enumData.setCode(iEnumBase.getCode());
        enumData.setName(iEnumBase.getName());
        enumData.setDisplayName(iEnumBase.getDisplayName());
        enumData.setEnumName(iEnumBase.getEnumName());
        return enumData;
    }
}
