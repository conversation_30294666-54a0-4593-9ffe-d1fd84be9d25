package com.laien.common.core.enums.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.util.EnumBaseUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface IEnumBaseMapStruct<T extends IEnumBase> {

    default List<Integer> toCodeList(List<T> list) {
        return EnumBaseUtils.toList(IEnumBase::getCode, list);
    }

    default List<String> toNameList(List<T> list) {
        return EnumBaseUtils.toList(IEnumBase::getName, list);
    }

    default Integer getCodeFromT(T t) {
        return EnumBaseUtils.getCode(t);
    }

    default String getNameFromT(T t) {
        return EnumBaseUtils.getName(t);
    }

    default String getNamesFromT(List<T> list) {
        return EnumBaseUtils.getNames(list);
    }

    default T getFirst(List<T> list) {
        return Optional.ofNullable(list).orElse(Collections.emptyList()).stream().filter(ObjUtil::isNotNull).findFirst().orElse(null);
    }

    default Integer getFirstCode(List<T> list) {
        return Optional.ofNullable(getFirst(list)).map(IEnumBase::getCode).orElse(null);
    }

}
