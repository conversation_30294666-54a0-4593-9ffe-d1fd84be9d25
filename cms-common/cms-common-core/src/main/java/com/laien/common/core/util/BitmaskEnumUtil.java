package com.laien.common.core.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.laien.common.core.enums.IEnumBase;

import java.util.List;

/**
 * <p>
 * BitmaskQueryUtil
 * 位运算查询工具类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
public class BitmaskEnumUtil {

    /**
     * 添加位运算查询条件到 LambdaQueryWrapper（支持全包含或任意包含）
     *
     * @param wrapper      查询条件构造器
     * @param columnGetter 字段方法引用（如 YourEntity::getXxx）
     * @param enumList     枚举列表（如 Restriction116Enums）
     * @param matchAll     true = 全包含，false = 任意包含, null = 全相等
     * @param <T>          实体类型
     * @param <E>          枚举类型，必须实现 IEnumBase
     */
    public static <T, E extends IEnumBase> void addBitmaskCondition(
            AbstractLambdaWrapper<T,?> wrapper,Func1<T, ?> columnGetter,
            List<E> enumList,Boolean matchAll) {
        if (CollUtil.isEmpty(enumList)) return; // 无条件，不加查询
        int sum = sumBitmaskEnumList(enumList);
        String columnName = StrUtil.toUnderlineCase(LambdaUtil.getFieldName(columnGetter));
        String conditionSql;
        if (matchAll == null) {
            conditionSql = String.format("(%s = {0})", columnName);
        } else if (matchAll) {
            conditionSql = String.format("(%s & {0}) = {0}", columnName);
        } else {
            conditionSql = String.format("(%s & {0}) > 0", columnName);
        }
        wrapper.apply(conditionSql, sum);
    }

    public static int sumBitmaskEnumList(List<? extends IEnumBase> list) {
        if(CollUtil.isEmpty(list)){
            return 0;
        }
        return list.stream().map(IEnumBase::getCode).reduce(0, Integer::sum);
    }
}
