package com.laien.common.core.util;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.poi.ss.formula.functions.T;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Function;

/**
 * 分页工具类
 *
 * <AUTHOR>
 * @since 2025/07/11
 */
public class PaginationUtil {

    /**
     * 通用手动分页工具方法
     * 适用于需要在Java代码中对已查询的数据进行分页的场景
     *
     * @param dataList 数据列表
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> IPage<T> manualPagination(List<T> dataList, int pageNum, int pageSize) {
        return manualPaginationWithConvert(dataList, pageNum, pageSize, Function.identity());
    }

    /**
     * 带数据转换的手动分页工具方法
     * 在分页的同时进行数据类型转换，避免转换全量数据，提升性能
     *
     * @param dataList 原始数据列表
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param converter 数据转换函数，不能为null
     * @param <T> 原始数据类型
     * @param <R> 转换后数据类型
     * @return 转换后的分页结果
     * @throws IllegalArgumentException 当converter为null时抛出
     */
    public static <T, R> IPage<R> manualPaginationWithConvert(List<T> dataList, int pageNum, int pageSize,
                                                              Function<? super T, ? extends R> converter) {
        if (converter == null) {
            throw new IllegalArgumentException("Converter function cannot be null");
        }

        if (CollUtil.isEmpty(dataList)) {
            return new Page<>(pageNum, pageSize, 0);
        }

        int total = dataList.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        // 边界检查
        if (startIndex >= total) {
            IPage<R> page = new Page<>(pageNum, pageSize, total);
            page.setRecords(new ArrayList<>());
            return page;
        }

        // 只转换当页数据，提升性能
        List<R> convertedRecords = new ArrayList<>(endIndex - startIndex);
        for (int i = startIndex; i < endIndex; i++) {
            T item = dataList.get(i);
            R convertedItem = converter.apply(item);
            convertedRecords.add(convertedItem);
        }

        IPage<R> page = new Page<>(pageNum, pageSize, total);
        page.setRecords(convertedRecords);
        return page;
    }

    /**
     * 计算总页数
     *
     * @param total 总记录数
     * @param pageSize 每页大小
     * @return 总页数
     */
    public static int calculateTotalPages(int total, int pageSize) {
        if (total <= 0 || pageSize <= 0) {
            return 0;
        }
        return (total + pageSize - 1) / pageSize;
    }

    /**
     * 检查页码是否有效
     *
     * @param pageNum 页码
     * @param total 总记录数
     * @param pageSize 每页大小
     * @return 是否有效
     */
    public static boolean isValidPageNum(int pageNum, int total, int pageSize) {
        if (pageNum <= 0 || pageSize <= 0) {
            return false;
        }
        if (total <= 0) {
            return pageNum == 1;
        }
        int totalPages = calculateTotalPages(total, pageSize);
        return pageNum <= totalPages;
    }

    /**
     * 获取安全的页码（确保在有效范围内）
     *
     * @param pageNum 原始页码
     * @param total 总记录数
     * @param pageSize 每页大小
     * @return 安全的页码
     */
    public static int getSafePageNum(int pageNum, int total, int pageSize) {
        if (pageNum <= 0) {
            return 1;
        }
        if (total <= 0) {
            return 1;
        }
        int totalPages = calculateTotalPages(total, pageSize);
        return Math.min(pageNum, totalPages);
    }
}
