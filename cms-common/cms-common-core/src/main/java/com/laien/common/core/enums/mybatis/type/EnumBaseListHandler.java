package com.laien.common.core.enums.mybatis.type;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.constant.CmsCommonConstant;
import com.laien.common.core.enums.util.EnumBaseUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class EnumBaseListHandler<T extends IEnumBase> extends BaseTypeHandler<List<T>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, Optional.ofNullable(parameter).filter(CollUtil::isNotEmpty).map(enumList -> EnumBaseUtils.getCodes(parameter)).orElse(CmsCommonConstant.EMPTY_STRING));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getResult(rs.getString(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getResult(rs.getString(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getResult(cs.getString(columnIndex));
    }

    private List<T> getResult(String data) {
        return Optional.ofNullable(data).filter(StrUtil::isNotEmpty).map(d -> StrUtil.split(d, CmsCommonConstant.COMMA).stream().filter(StrUtil::isNumeric).map(code -> Arrays.stream(((Class<T>) getRawType()).getEnumConstants()).filter(e -> e.getCode().equals(Integer.parseInt(code))).findFirst().orElse(null)).filter(r -> r != null).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    @Override
    public List<T> getResult(ResultSet rs, String columnName) throws SQLException {
        return super.getResult(rs, columnName);
    }

    @Override
    public List<T> getResult(ResultSet rs, int columnIndex) throws SQLException {
        return super.getResult(rs, columnIndex);
    }

    @Override
    public List<T> getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return super.getResult(cs, columnIndex);
    }


}
