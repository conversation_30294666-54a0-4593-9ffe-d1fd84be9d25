package com.laien.common.core.enums.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.common.core.constant.CmsCommonConstant;
import com.laien.common.core.enums.IEnumBase;
import lombok.SneakyThrows;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 枚举工具类
 *
 * <AUTHOR>
 * @since 2024/10/23 17:42
 **/
public class EnumBaseUtils {

    /**
     * 根据指定枚举列和名称获取枚举
     *
     * @param condition
     * @param value
     * @param <E>
     * @param <C>
     * @return
     */
    public static <E extends Enum<E>, C> E findEnum(Func1<E, C> condition, C value) {
        return Optional.ofNullable(value).map(v -> EnumUtil.getBy(condition, v)).orElse(null);
    }

    /**
     * 根据指定枚举列和名称获取枚举-批量，value必须是以,分隔的字符串
     *
     * @param condition
     * @param value
     * @param <E>
     * @param <C>
     * @return
     */
    public static <E extends Enum<E>, C> List<E> findEnumList(Func1<E, C> condition, String value) {
        return Optional.ofNullable(value).map(vm -> {
            final String fieldName = LambdaUtil.getFieldName(condition);
            final List<C> tagList = Optional.ofNullable(fieldName).filter(f -> f.equals(CmsCommonConstant.ENUM_CODE)).map(v2 -> StrUtil.split(value, CmsCommonConstant.COMMA).stream().map(s -> (C) (Integer.valueOf(s))).collect(Collectors.toList())).orElse(StrUtil.split(value, CmsCommonConstant.COMMA).stream().map(s -> (C) (s)).collect(Collectors.toList()));
            return tagList.stream().map(tag -> EnumUtil.getBy(condition, tag)).collect(Collectors.toList());
        }).orElse(Collections.emptyList());
    }

    /**
     * 根据指定枚举列和名称获取枚举-批量，value必须是以,分隔的字符串
     *
     * @param condition
     * @param value
     * @param <E>
     * @param <C>
     * @return
     */
    public static <E extends Enum<E>, C> List<E> findEnumList(Func1<E, C> condition, List<C> value) {
        return Optional.ofNullable(value).filter(CollUtil::isNotEmpty).map(vm -> {
            final String valueStr = StrUtil.join(CmsCommonConstant.COMMA, value);
            return findEnumList(condition, valueStr);
        }).orElse(Collections.emptyList());
    }

    @SneakyThrows
    public static <E extends IEnumBase, C> List<C> toList(Func1<E, C> condition, List<E> value) {
        final String methodName = LambdaUtil.getMethodName(condition);
        return (List<C>) Optional.ofNullable(value).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).map(v -> {
            try {
                return MethodUtils.invokeMethod(v, methodName);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 获取枚举code
     *
     * @param enumBase
     * @return
     */
    public static Integer getCode(IEnumBase enumBase) {
        return Optional.ofNullable(enumBase).map(IEnumBase::getCode).orElse(null);
    }

    /**
     * 获取枚举code
     *
     * @param enumBase
     * @return
     */
    public static String getName(IEnumBase enumBase) {
        return Optional.ofNullable(enumBase).map(IEnumBase::getName).orElse(null);
    }

    /**
     * 获取codes ,分隔字符串
     *
     * @param enumBaseList
     * @return
     */
    public static String getCodes(List<? extends IEnumBase> enumBaseList) {
        return Optional.ofNullable(enumBaseList).filter(CollUtil::isNotEmpty).orElse(Collections.emptyList()).stream().map(e -> e.getCode() + CmsCommonConstant.EMPTY_STRING).collect(Collectors.joining(CmsCommonConstant.COMMA));
    }

    /**
     * 获取name ,分隔字符串
     *
     * @param enumBaseList
     * @return
     */
    public static String getNames(List<? extends IEnumBase> enumBaseList) {
        return Optional.ofNullable(enumBaseList).filter(CollUtil::isNotEmpty).orElse(Collections.emptyList()).stream().map(IEnumBase::getName).collect(Collectors.joining(CmsCommonConstant.COMMA));
    }

    /**
     * 获取name list
     *
     * @param enumBaseList enumBaseList
     * @return List<String>
     */
    public static List<String> getNamesList(List<? extends IEnumBase> enumBaseList) {
        return Optional.ofNullable(enumBaseList).filter(CollUtil::isNotEmpty).orElse(Collections.emptyList()).stream().map(IEnumBase::getName).collect(Collectors.toList());
    }

    /**
     * 获取showCode set
     *
     * @param enumBaseList enumBaseList
     * @return Set<Integer>
     */
    public static Set<Integer> getShowCodeSet(Collection<? extends IEnumBase> enumBaseList) {
        return Optional.ofNullable(enumBaseList).filter(CollUtil::isNotEmpty).orElse(Collections.emptyList()).stream().map(IEnumBase::getShowCode).collect(Collectors.toSet());
    }
    
    public static <T extends IEnumBase> List<T> findEnumListByShowCode(Class<T> enumClass, Collection<Integer> showCodeList) {
        if (enumClass == null || CollUtil.isEmpty(showCodeList)) {
            return Collections.emptyList();
        }

        T[] enumConstants = enumClass.getEnumConstants();
        if (enumConstants == null) {
            return Collections.emptyList();
        }

        return Arrays.stream(enumConstants)
                .filter(enumConstant -> showCodeList.contains(enumConstant.getShowCode()))
                .collect(Collectors.toList());
    }

    /**
     * 根据指定枚举列和名称获取枚举,并校验值是否存在
     *
     * @param condition condition
     * @param value value
     * @param <E> <E>
     * @param <C> <C>
     * @return list
     */
    public static <E extends Enum<E>, C> E findEnumAndCheck(Func1<E, C> condition, C value, String fieldName) throws RuntimeException {
        E e = Optional.ofNullable(value).map(v -> EnumUtil.getBy(condition, v)).orElse(null);
        if (e == null) {
            throw new RuntimeException(fieldName + "[" + value + "] not found");
        }
        return e;
    }

    /**
     * 根据指定枚举列和名称获取枚举,并校验值是否存在
     *
     * @param condition condition
     * @param valueList valueList
     * @param <E> <E>
     * @param <C> <C>
     * @return list
     */
    public static <E extends Enum<E>, C> List<E> findEnumListAndCheck(Func1<E, C> condition, List<C> valueList, String fieldName) throws RuntimeException {
        if (valueList != null && !valueList.isEmpty()) {
            List<String> notFoundValues = new ArrayList<>();
            List<E> list = new ArrayList<>(valueList.size());
            for (C value : valueList) {
                E e = Optional.ofNullable(value).map(v -> EnumUtil.getBy(condition, v)).orElse(null);
                if (e != null) {
                    list.add(e);
                } else {
                    notFoundValues.add(value == null ? "null" : value.toString());
                }
            }
            if (!notFoundValues.isEmpty()) {
                throw new RuntimeException(fieldName + notFoundValues + " not found");
            }
            return list;
        } else {
            return Collections.emptyList();
        }
    }

}
