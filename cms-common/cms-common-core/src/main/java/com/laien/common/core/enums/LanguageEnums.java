package com.laien.common.core.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Getter
@Slf4j
public enum LanguageEnums implements IEnumBase{

    EN("en"),
    ZH("zh"),
    ZH_TW("zh-TW",ListUtil.of("zh-Hant")),
    IT("it"),
    DA("da"),
    SV("sv"),
    JA("ja",true),
    TR("tr"),
    RU("ru"),
    KO("ko"),
    TH("th"),
    VI("vi"),
    ES("es"),
    PT("pt"),
    DE("de", true),
    NL("nl"),
    FR("fr"),
    AR("ar"),
    ID("id"),
    PL("pl"),
    NO("no"),
    FI("fi"),
    UK("uk"),
    MS("ms"),
    HI("hi"),
    HR("hr"),
    CS("cs"),
    RO("ro"),
    SK("sk"),
    HU("hu"),
    EL("el"),
    HE("he");

    @EnumValue
    private final String name;

    private final List<String> alias;

    private final boolean selectedByDefault;

    LanguageEnums(String name, boolean... selectedByDefault) {
        this(name, CollUtil.newArrayList(), selectedByDefault);
    }

    LanguageEnums(String name, List<String> alias, boolean... selectedByDefault) {
        this.name = name;
        this.alias = alias;
        this.selectedByDefault = selectedByDefault.length > 0 && selectedByDefault[0];
    }

    public static List<LanguageEnums> getLanguageEnums(String langStr) {
        List<String> languageList = StrUtil.split(langStr, StrUtil.COMMA);
        return getLanguageEnums(languageList);
    }

    public static List<LanguageEnums> getLanguageEnums(Collection<String> languageList) {
        List<LanguageEnums> collect = languageList.stream()
                .map(lang -> Arrays.stream(values())
                        .filter(getLanguageEnumsPredicate(lang))
                        .findFirst()
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            log.error("can not find language enums, languageList:{}", languageList);
        }
        return collect;
    }

    public static LanguageEnums getByNameIgnoreCase(String language) {
        LanguageEnums languageEnums = Arrays.stream(values())
                .filter(getLanguageEnumsPredicate(language))
                .findFirst()
                .orElse(null);
        if (languageEnums == null) {
            log.error("can not find language enums, language:{}", language);
        }
        return languageEnums;
    }


    private static Predicate<LanguageEnums> getLanguageEnumsPredicate(String lang) {
        if (lang == null) {
            return enumItem -> false;
        }
        String langLower = lang.toLowerCase();
        return enumItem ->
                enumItem.name.toLowerCase().equals(langLower) ||
                        enumItem.alias.stream().map(String::toLowerCase).anyMatch(langLower::equals);
    }

    @Override
    public Integer getCode() {
        return 0;
    }

    @Override
    public String getDisplayName() {
        return name;
    }
}
