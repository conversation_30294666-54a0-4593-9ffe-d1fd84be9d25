package com.laien.common.core.aggregator;

import cn.hutool.core.util.StrUtil;
import com.laien.common.frame.entity.BaseModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用任务聚合器基类
 * 提供基于BaseModel的通用聚合逻辑
 *
 * <AUTHOR>
 * @since 2025/07/11
 * @param <T> 实体类型，必须继承BaseModel
 * @param <R> 返回的VO类型
 */
@Data
public abstract class BaseTaskAggregator<T extends BaseModel, R> {

    protected final T baseTask; // 基础任务数据
    protected final List<T> allTasks; // 所有任务
    protected LocalDateTime minCreateTime;
    protected LocalDateTime maxUpdateTime;
    protected T latestUpdateTask; // 最新更新的任务

    public BaseTaskAggregator(T firstTask) {
        this.baseTask = firstTask;
        this.allTasks = new ArrayList<>();
        this.allTasks.add(firstTask);
        this.minCreateTime = firstTask.getCreateTime();
        this.maxUpdateTime = firstTask.getUpdateTime();
        this.latestUpdateTask = firstTask;
    }

    /**
     * 聚合新任务
     * 自动处理BaseModel的通用字段聚合
     */
    public void aggregate(T task) {
        allTasks.add(task);

        // 更新最小创建时间
        if (task.getCreateTime() != null &&
            (minCreateTime == null || task.getCreateTime().isBefore(minCreateTime))) {
            minCreateTime = task.getCreateTime();
        }

        // 更新最大更新时间和最新更新任务
        if (task.getUpdateTime() != null) {
            if (maxUpdateTime == null || task.getUpdateTime().isAfter(maxUpdateTime) ||
                (task.getUpdateTime().equals(maxUpdateTime) &&
                 task.getId().compareTo(latestUpdateTask.getId()) > 0)) {
                maxUpdateTime = task.getUpdateTime();
                if (StrUtil.isNotBlank(task.getUpdateUser())) {
                    latestUpdateTask = task;
                }
            }
        }

        // 调用子类的自定义聚合逻辑
        doCustomAggregate(task);
    }

    /**
     * 子类可重写此方法实现自定义聚合逻辑
     * 默认实现为空，子类可根据需要选择性重写
     * @param task 要聚合的任务
     */
    protected void doCustomAggregate(T task) {
        // 默认空实现，子类可根据需要重写
    }

    /**
     * 转换为VO对象
     * @return VO对象
     */
    public abstract R toVO();
}
