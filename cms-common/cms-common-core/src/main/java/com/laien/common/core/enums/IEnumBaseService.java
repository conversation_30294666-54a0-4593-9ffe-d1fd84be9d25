package com.laien.common.core.enums;

import cn.hutool.core.lang.ClassScanner;
import cn.hutool.core.map.MapUtil;
import com.laien.common.core.enums.annotation.EnumDisplayName;
import com.laien.common.core.enums.response.EnumData;
import com.laien.common.core.enums.response.ProjectEnumData;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.core.constant.CmsCommonConstant.DEFAULT_SCAN_ENUM_PACKAGE;

public interface IEnumBaseService {


    Map<String, List<ProjectEnumData>> projectEnumCache = MapUtil.newHashMap();

    /**
     * 返回枚举类型Set
     */
    default Set<Class<? extends IEnumBase>> configConstantEnumToSet() {
        final String scanPackage = String.format(DEFAULT_SCAN_ENUM_PACKAGE, getAppCode().toLowerCase());
        return ClassScanner.scanPackageBySuper(scanPackage, IEnumBase.class).stream().map(clazz -> (Class<? extends IEnumBase>) clazz).collect(Collectors.toSet());
    }

    String getAppCode();

    default List<ProjectEnumData> getAllEnumDataList() {
        return Optional.ofNullable(getAppCode()).map(appCode -> {
            return projectEnumCache.computeIfAbsent(appCode, (code) -> configConstantEnumToSet().stream().map(c -> new ProjectEnumData(c.getSimpleName(), Optional.ofNullable(c.getAnnotation(EnumDisplayName.class)).map(EnumDisplayName::value).orElse(c.getSimpleName()), Arrays.stream(c.getEnumConstants()).filter(IEnumBase::getShow).map(EnumData::toEnumData).collect(Collectors.toList()))).sorted(Comparator.comparing(ProjectEnumData::getDisplayName)).collect(Collectors.toList()));
        }).orElse(Collections.emptyList());
    }
}