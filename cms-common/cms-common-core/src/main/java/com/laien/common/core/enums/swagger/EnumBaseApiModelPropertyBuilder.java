package com.laien.common.core.enums.swagger;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.classmate.ResolvedType;
import com.fasterxml.classmate.TypeResolver;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.jackson.EnumBaseCodeSerializer;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import springfox.documentation.service.AllowableValues;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.schema.ModelPropertyBuilderPlugin;
import springfox.documentation.spi.schema.contexts.ModelPropertyContext;
import springfox.documentation.spring.web.DescriptionResolver;
import springfox.documentation.swagger.common.SwaggerPluginSupport;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static com.github.xiaoymin.knife4j.core.util.CollectionUtils.isEmpty;
import static java.util.Optional.empty;
import static springfox.documentation.schema.Annotations.findPropertyAnnotation;
import static springfox.documentation.swagger.schema.ApiModelProperties.allowableValueFromString;

@Component
public class EnumBaseApiModelPropertyBuilder implements ModelPropertyBuilderPlugin {
    private static final String ENUM_TYPE = "enum type";
    private static final String SPLIT = "=>";
    private static final String LINES = "<br/>";
    private static final String DESCRIPTION = "description";
    private final DescriptionResolver descriptions;

    @Autowired
    public EnumBaseApiModelPropertyBuilder(DescriptionResolver descriptions) {
        this.descriptions = descriptions;
    }

    @Override
    public void apply(ModelPropertyContext context) {
        Optional<JsonSerialize> annotation = empty();
        if (context.getBeanPropertyDefinition().isPresent()) {
            annotation = annotation.map(Optional::of).orElse(findPropertyAnnotation(
                    context.getBeanPropertyDefinition().get(),
                    JsonSerialize.class));
        }
        final Class rawPrimaryType = context.getBeanPropertyDefinition().get().getRawPrimaryType();
        Boolean isBaseEnum = Boolean.FALSE;
        Class enumCls = null;
        if (IEnumBase.class.isAssignableFrom(rawPrimaryType)) {
            enumCls = rawPrimaryType;
            isBaseEnum = Boolean.TRUE;
        } else {
            if (context.getBeanPropertyDefinition().get().getRawPrimaryType() == List.class) {
                final JavaType[] typeParameters = context.getBeanPropertyDefinition().get().getPrimaryType().findTypeParameters(List.class);
                if (typeParameters.length > 0 && IEnumBase.class.isAssignableFrom(typeParameters[0].getRawClass())) {
                    isBaseEnum = Boolean.TRUE;
                    enumCls = typeParameters[0].getRawClass();
                }
            }
        }
        if (isBaseEnum && annotation.isPresent() && annotation.get().using() == EnumBaseCodeSerializer.class) {
            if (rawPrimaryType == List.class) {
                context.getBuilder().type(context.getResolver().resolve(List.class, Integer.class));
            } else {
                context.getBuilder().type(context.getResolver().resolve(Integer.class));
            }
        }
        if (isBaseEnum) {
            List<String> descStrList = CollUtil.newArrayList();
//            context.getBuilder().description
            final Field field = ReflectionUtils.findField(context.getBuilder().getClass(), DESCRIPTION);
            ReflectionUtils.makeAccessible(field);
            final Object name = ReflectionUtils.getField(field, context.getBuilder());
            descStrList.add((String) name);
            descStrList.add(ENUM_TYPE);
            for (Object enumConstant : enumCls.getEnumConstants()) {
                final IEnumBase enumBase = (IEnumBase) enumConstant;
                descStrList.add(enumBase.getCode() + SPLIT + enumBase.getName());
            }
            context.getBuilder().description(StrUtil.join(LINES, descStrList));
        }
    }

    @Override
    public boolean supports(DocumentationType delimiter) {
        return SwaggerPluginSupport.pluginDoesApply(delimiter);
    }

    Function<ApiModelProperty, AllowableValues> toAllowableValues() {
        return annotation -> allowableValueFromString(annotation.allowableValues());
    }

    Function<ApiModelProperty, String> toExample() {
        return annotation -> {
            String example = "";
            if (!isEmpty(annotation.example())) {
                example = annotation.example();
            }
            return example;
        };
    }

    Function<ApiModelProperty, String> toDescription(
            final DescriptionResolver descriptions) {

        return annotation -> {
            String description = "";
            if (!isEmpty(annotation.value())) {
                description = annotation.value();
            } else if (!isEmpty(annotation.notes())) {
                description = annotation.notes();
            }
            return descriptions.resolve(description);
        };
    }

    Function<ApiModelProperty, ResolvedType> toType(final TypeResolver resolver) {
        return annotation -> {
            try {
                return resolver.resolve(Class.forName(annotation.dataType()));
            } catch (ClassNotFoundException e) {
                return resolver.resolve(Object.class);
            }
        };
    }
}