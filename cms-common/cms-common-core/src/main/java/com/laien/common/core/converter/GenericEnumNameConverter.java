package com.laien.common.core.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.laien.common.core.enums.IEnumBase;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;

@Slf4j
public class GenericEnumNameConverter implements Converter<IEnumBase> {

    /**
     * 利用 contentProperty 反射获取字段实际的枚举类型
     */
    private Class<? extends IEnumBase> getEnumClass(ExcelContentProperty contentProperty) {
        if (contentProperty != null && contentProperty.getField() != null) {
            Field field = contentProperty.getField();
            Class<?> fieldType = field.getType();
            if (fieldType.isEnum() && IEnumBase.class.isAssignableFrom(fieldType)) {
                return (Class<? extends IEnumBase>) fieldType;
            }
        }
        return null;
    }

    @Override
    public Class supportJavaTypeKey() {
        return IEnumBase.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public IEnumBase convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration){
        String cellValue = cellData.getStringValue();
        if (cellValue == null || cellValue.trim().isEmpty()) {
            return null;
        }
        Class<? extends IEnumBase> enumClass = getEnumClass(contentProperty);
        if (enumClass == null) {
            log.warn("无法从 contentProperty 中确定枚举类型");
            throw new IllegalStateException("无法从 contentProperty 中确定枚举类型");
        }
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(e -> e.getName().equals(cellValue))
                .findFirst()
                .orElse(null);
    }

    @Override
    public CellData convertToExcelData(IEnumBase value, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration) {
        String result = value != null ? value.getName() : "";
        return new CellData(result);
    }
}
