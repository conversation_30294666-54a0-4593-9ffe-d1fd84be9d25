package com.laien.common.lms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.core.aggregator.BaseTaskAggregator;
import com.laien.common.core.aggregator.TaskAggregatorUtil;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.core.util.PaginationUtil;
import com.laien.common.domain.annotation.TranslateField;
import com.laien.common.domain.bo.Core18nContextBO;
import com.laien.common.domain.bo.FieldBO;
import com.laien.common.domain.component.CoreI18nModel;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.domain.entity.*;
import com.laien.common.domain.enums.*;
import com.laien.common.domain.request.*;
import com.laien.common.domain.response.CoreTextTaskI18nDetailVO;
import com.laien.common.domain.response.CoreTextTaskI18nPageVO;
import com.laien.common.domain.response.CoreTextTaskI18nVO;
import com.laien.common.domain.utils.CoreI18nUtil;
import com.laien.common.frame.entity.BaseModel;
import com.laien.common.frame.exception.BizException;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.utils.BizExceptionUtil;
import com.laien.common.lms.mapper.CoreTextTaskI18nMapper;
import com.laien.common.lms.mapstruct.CoreTextTaskI18nMapStruct;
import com.laien.common.lms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.domain.enums.UpdateFlagEnums.TEXT_CHANGED;

/**
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreTextTaskI18nServiceImpl extends ServiceImpl<CoreTextTaskI18nMapper, CoreTextTaskI18n> implements ICoreTextTaskI18nService {

    @Resource
    private ICoreSpeechTaskI18nService coreSpeechTaskI18nService;
    @Resource
    private ICoreBusinessTaskRelationI18nService coreBusinessTaskRelationI18nService;
    @Resource
    private ICoreVoiceConfigI18nService coreVoiceConfigI18nService;
    @Resource
    private ICoreVoiceConfigTemplateI18nService coreVoiceConfigTemplateI18nService;
    @Resource
    private ICoreVoiceTemplateI18nService coreVoiceTemplateI18nService;
    private final CoreTextTaskI18nMapStruct mapStruct;

    @Override
    public List<CoreTextTaskI18n> query(Collection<String> md5s, List<LanguageEnums> languageList) {
        if (CollUtil.isEmpty(md5s)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CoreTextTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreTextTaskI18n::getTextMd5, md5s)
                .in(CoreTextTaskI18n::getLanguage, languageList);
        return baseMapper.selectList(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdate(List<CoreTextTaskI18n> taskI18nList){
        if(CollUtil.isEmpty(taskI18nList)){
            log.warn("taskI18nList is empty");
            return;
        }
        updateBatchById(taskI18nList);
        Map<String, Set<Integer>> taskUpdateUserMap = taskI18nList.stream().collect(Collectors.groupingBy(BaseModel::getUpdateUser, Collectors.mapping(BaseModel::getId, Collectors.toSet())));
        for (Map.Entry<String, Set<Integer>> entry : taskUpdateUserMap.entrySet()) {
            LambdaUpdateWrapper<CoreTextTaskI18n> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(BaseModel::getId, entry.getValue())
                    .set(BaseModel::getUpdateUser, entry.getKey());
            baseMapper.update(null, updateWrapper);
        }
        Set<Integer> taskIdSet = taskI18nList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        taskI18nList = baseMapper.selectBatchIds(taskIdSet);
        coreBusinessTaskRelationI18nService.updateTextFlag(taskIdSet);
        List<CoreSpeechTaskI18n> speechTaskList = coreSpeechTaskI18nService.query(taskIdSet, SpeechTaskStatusEnums.WAITING_TEXT_TRANSLATION_COMPLETE);
        if(CollUtil.isEmpty(speechTaskList)){
            log.warn("speechTaskList is empty");
            return;
        }
        Map<String, List<CoreSpeechTaskI18n>> speechTaskGroup = new HashMap<>();
        for (CoreSpeechTaskI18n speechTaskI18n : speechTaskList) {
            String key = createBatchUpdateSpeechTaskKey(speechTaskI18n);
            List<CoreSpeechTaskI18n> coreSpeechTaskI18nList = speechTaskGroup.getOrDefault(key, new ArrayList<>());
            coreSpeechTaskI18nList.add(speechTaskI18n);
            speechTaskGroup.put(key, coreSpeechTaskI18nList);
        }
        log.warn("speechTaskGroup:{}", speechTaskGroup);
        List<CoreSpeechTaskI18n> needUpdateSpeechTaskList = new ArrayList<>(speechTaskList.size());
        for (CoreTextTaskI18n textTaskI18n : taskI18nList) {
            String key = createBatchUpdateSpeechTaskKey(textTaskI18n);
            List<CoreSpeechTaskI18n> coreSpeechTaskI18nList = speechTaskGroup.get(key);
            if(CollUtil.isEmpty(coreSpeechTaskI18nList)){
                log.warn("coreSpeechTaskI18nList is empty, key:{},task:{}", key,textTaskI18n);
                continue;
            }
            for (CoreSpeechTaskI18n speechTaskI18n : coreSpeechTaskI18nList) {
                speechTaskI18n.setTranslationText(textTaskI18n.getTranslationText())
                        .setTranslationTextMd5(textTaskI18n.getTranslationTextMd5())
                        .setReduced(false)
                        .setAuditType(AuditTypeEnums.MACHINE_TRANSLATION)
                        .setStatus(SpeechTaskStatusEnums.PENDING)
                        .setUpdateTime(LocalDateTime.now());
                needUpdateSpeechTaskList.add(speechTaskI18n);
            }
        }
        log.warn("needUpdateSpeechTaskList:{}", needUpdateSpeechTaskList);
        if (CollUtil.isEmpty(needUpdateSpeechTaskList)) {
            return;
        }
        coreSpeechTaskI18nService.updateBatchById(needUpdateSpeechTaskList);
        Map<String, Set<Integer>> updateUserMap = needUpdateSpeechTaskList.stream()
                .collect(Collectors.groupingBy(BaseModel::getUpdateUser, Collectors.mapping(BaseModel::getId, Collectors.toSet())));
        for (Map.Entry<String, Set<Integer>> entry : updateUserMap.entrySet()) {
            LambdaUpdateWrapper<CoreSpeechTaskI18n> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(CoreSpeechTaskI18n::getId, entry.getValue())
                    .set(BaseModel::getUpdateUser, entry.getKey());
            coreSpeechTaskI18nService.update(wrapper);
        }
    }

    @Override
    public void addRetryCount(Collection<Integer> ids) {
        if(CollUtil.isEmpty(ids)){
            return;
        }
        baseMapper.addRetryCount(ids);
    }

    private String createBatchUpdateSpeechTaskKey(CoreSpeechTaskI18n speechTaskI18n){
        return speechTaskI18n.getCoreTextTaskI18nId() + "-" + speechTaskI18n.getLanguage();
    }

    private String createBatchUpdateSpeechTaskKey(CoreTextTaskI18n textTaskI18n){
        return textTaskI18n.getId() + "-" + textTaskI18n.getLanguage();
    }



    @Override
    public List<CoreTextTaskI18n> query(List<TextTaskStatusEnums> statusList, Integer limitSize, Integer retryCount) {
        if(null == limitSize || limitSize <= 0){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CoreTextTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreTextTaskI18n::getStatus, statusList)
                .lt(CoreTextTaskI18n::getRetryCount, retryCount)
                .last("limit " + limitSize);
        return list(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeStatus(Collection<Integer> isd, TextTaskStatusEnums status, boolean checkStatus) {
        if(CollUtil.isEmpty(isd)){
            return;
        }
        LambdaUpdateWrapper<CoreTextTaskI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CoreTextTaskI18n::getId, isd)
                .set(CoreTextTaskI18n::getStatus, status)
                .set(BaseModel::getUpdateTime, LocalDateTime.now())
                .set(CoreTextTaskI18n::isCheckStatus, checkStatus);
        update(wrapper);
    }

    @Override
    public List<CoreTextTaskI18n> queryStatusTimeoutTask(List<TextTaskStatusEnums> statusList, Integer timeoutDuration, Integer limitSize, Integer retryCount) {
        LocalDateTime timeoutDateTime = LocalDateTimeUtil.offset(LocalDateTime.now(), -timeoutDuration, ChronoUnit.MINUTES);
        LambdaQueryWrapper<CoreTextTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreTextTaskI18n::getStatus, statusList)
                .le(BaseModel::getUpdateTime,timeoutDateTime)
                .lt(CoreTextTaskI18n::getRetryCount, retryCount)
                .last("limit " + limitSize);
        return list(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized void batchSaveOrUpdate(CreateTaskDTO createTaskDTO) throws NoSuchFieldException {
        List<LanguageEnums> textLanguageList = createTaskDTO.getTextLanguageList();
        List<CoreI18nModel> coreI18nModelList = createTaskDTO.getCoreI18nModelList();
        List<LanguageEnums> audioLanguageList = createTaskDTO.getAudioLanguageList();
        if (CollUtil.isEmpty(coreI18nModelList)) {
            return;
        }
        if (CollUtil.isEmpty(textLanguageList) && CollUtil.isEmpty(audioLanguageList)) {
            return;
        }
        Core18nContextBO contextBO = new Core18nContextBO();
        contextBO.setTextLanguageList(textLanguageList)
                .setAudioLanguageList(audioLanguageList)
                .setCoreI18nModelList(coreI18nModelList)
                .setProjCode(createTaskDTO.getProjCode());

        createFieldList(contextBO);

        if (CollUtil.isEmpty(contextBO.getFieldList())) {
            log.error("create core i18n task field list is empty");
            return;
        }

        batchSaveTextTask(contextBO);
        batchSaveBusinessTaskRelation(contextBO);
        batchSaveSpeechTask(contextBO);
    }

    private void batchSaveSpeechTask(Core18nContextBO contextBO) {
        List<LanguageEnums> audioLanguageList = contextBO.getAudioLanguageList();
        List<FieldBO> audioFieldList = contextBO.getAudioFieldList();
        if (CollUtil.isEmpty(audioLanguageList) || CollUtil.isEmpty(audioFieldList)) {
            return;
        }
        createVoiceConfigIdGroup(contextBO);
        Map<String, CoreSpeechTaskI18n> allSpeechTaskGroup = createSpeechTaskGroup(audioFieldList, audioLanguageList);
        List<CoreSpeechTaskI18n> needAddOrUpdateTaskList = createSpeechTask(contextBO, allSpeechTaskGroup);
        if (CollUtil.isNotEmpty(needAddOrUpdateTaskList)) {
            coreSpeechTaskI18nService.saveOrUpdateBatch(needAddOrUpdateTaskList);
        }
    }

    private void createVoiceConfigIdGroup(Core18nContextBO contextBO) {
        List<LanguageEnums> audioLanguageList = contextBO.getAudioLanguageList();
        Set<Integer> coreVoiceConfigI18nIdSet = contextBO.getCoreVoiceConfigI18nIdSet();
        if (CollUtil.isEmpty(coreVoiceConfigI18nIdSet)) {
            log.error("core voice config id is empty");
            throw new BizException("core voice config id set not can be null");
        }
        Collection<CoreVoiceConfigI18n> voiceConfigI18ns = coreVoiceConfigI18nService.listByIds(coreVoiceConfigI18nIdSet);
        if (CollUtil.isEmpty(voiceConfigI18ns) || voiceConfigI18ns.size() < coreVoiceConfigI18nIdSet.size()) {
            log.error("voice config id not found enough,voiceConfigId:{}", coreVoiceConfigI18nIdSet);
            throw new BizException("voice config id not found enough,voiceConfigId:" + coreVoiceConfigI18nIdSet);
        }
        Set<Integer> voiceConfigSet = voiceConfigI18ns.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<CoreVoiceConfigTemplateI18n> voiceConfigTemplateList = coreVoiceConfigTemplateI18nService.query(voiceConfigSet);
        if (CollUtil.isEmpty(voiceConfigTemplateList)) {
            log.error("voice config template not found,voiceConfigId:{}", voiceConfigSet);
            throw new BizException("voice config template not found,voiceConfigId:" + voiceConfigSet);
        }

        Map<Integer, List<CoreVoiceConfigTemplateI18n>> voiceConfigIdGroup = voiceConfigTemplateList.stream().collect(Collectors.groupingBy(CoreVoiceConfigTemplateI18n::getCoreVoiceConfigI18nId));
        for (Integer voiceConfigId : voiceConfigSet) {
            List<CoreVoiceConfigTemplateI18n> configTemplateList = voiceConfigIdGroup.get(voiceConfigId);
            if (CollUtil.isEmpty(configTemplateList) || configTemplateList.size() < audioLanguageList.size()) {
                log.error("voice config template not found enough,language:{},voice config id: {}", audioLanguageList, voiceConfigId);
                throw new BizException("voice config template not found enough,language:" + voiceConfigSet + " voice config id:" + voiceConfigId);
            }
        }
        Set<Integer> templateSet = voiceConfigTemplateList.stream().map(CoreVoiceConfigTemplateI18n::getCoreVoiceTemplateI18nId).collect(Collectors.toSet());
        Collection<CoreVoiceTemplateI18n> voiceTemplateList = coreVoiceTemplateI18nService.listByIds(templateSet);
        Map<Integer, CoreVoiceTemplateI18n> voiceTemplateIdGroup = voiceTemplateList.stream().collect(Collectors.toMap(BaseModel::getId, template -> template));
        contextBO.setVoiceConfigIdGroup(voiceConfigIdGroup);
        contextBO.setVoiceTemplateIdGroup(voiceTemplateIdGroup);
    }

    private Map<String, CoreSpeechTaskI18n> createSpeechTaskGroup(List<FieldBO> audioFieldList, List<LanguageEnums> audioLanguageList) {
        Set<String> audioTextMd5Set = audioFieldList.stream().map(FieldBO::getFieldValueMd5).collect(Collectors.toSet());

        List<CoreSpeechTaskI18n> speechTaskI18nList = querySpeechTask(audioTextMd5Set, audioLanguageList);
        Map<String, CoreSpeechTaskI18n> allSpeechTaskGroup = new HashMap<>();
        for (CoreSpeechTaskI18n speechTaskI18n : speechTaskI18nList) {
            String speechTaskKey = getSpeechTaskKey(speechTaskI18n);
            allSpeechTaskGroup.put(speechTaskKey, speechTaskI18n);
        }
        return allSpeechTaskGroup;
    }

    private List<CoreSpeechTaskI18n> createSpeechTask(Core18nContextBO contextBO, Map<String, CoreSpeechTaskI18n> allSpeechTaskGroup) {
        Map<Integer, List<CoreVoiceConfigTemplateI18n>> voiceConfigIdGroup = contextBO.getVoiceConfigIdGroup();
        Map<Integer, CoreVoiceTemplateI18n> voiceTemplateIdGroup = contextBO.getVoiceTemplateIdGroup();
        List<FieldBO> audioFieldList = contextBO.getAudioFieldList();
        List<CoreSpeechTaskI18n> needAddOrUpdateTaskList = new ArrayList<>(audioFieldList.size());
        List<LanguageEnums> audioLanguageList = contextBO.getAudioLanguageList();
        for (FieldBO fieldBO : audioFieldList) {
            Integer voiceConfigId = fieldBO.getCoreVoiceConfigI18nId();
            for (LanguageEnums language : audioLanguageList) {
                List<CoreVoiceConfigTemplateI18n> voiceTemplateList = voiceConfigIdGroup.get(voiceConfigId);
                for (CoreVoiceConfigTemplateI18n templateI18n : voiceTemplateList) {
                    Integer voiceTemplateI18nId = templateI18n.getCoreVoiceTemplateI18nId();
                    CoreVoiceTemplateI18n coreVoiceTemplateI18n = voiceTemplateIdGroup.get(voiceTemplateI18nId);
                    if(coreVoiceTemplateI18n.getLanguage() != language){
                        continue;
                    }
                    List<CoreTextTaskI18n> textTaskI18nList = fieldBO.getTextTaskI18nList();
                    if(CollUtil.isEmpty(textTaskI18nList)){
                        continue;
                    }
                    for (CoreTextTaskI18n coreTextTaskI18n : textTaskI18nList) {
                        if (coreTextTaskI18n.getLanguage() != language) {
                            continue;
                        }
                        Integer maxDuration = fieldBO.getDuration();
                        CoreSpeechTaskI18n speechTaskI18n = new CoreSpeechTaskI18n();
                        speechTaskI18n.setCoreVoiceConfigI18nId(fieldBO.getCoreVoiceConfigI18nId())
                                .setCoreVoiceTemplateI18nId(voiceTemplateI18nId)
                                .setCoreTextTaskI18nId(coreTextTaskI18n.getId())
                                .setLanguage(language)
                                .setCoreVoiceTemplateI18nName(coreVoiceTemplateI18n.getName())
                                .setText(fieldBO.getFieldValue())
                                .setTextMd5(fieldBO.getFieldValueMd5())
                                .setAudioUrl(fieldBO.getAudioUrl())
                                .setDuration(maxDuration)
                                .setGender(templateI18n.getGender())
                                .setProjCode(coreTextTaskI18n.getProjCode());
                        String speechTaskKey = getSpeechTaskKey(speechTaskI18n);
                        CoreSpeechTaskI18n speechTask = allSpeechTaskGroup.get(speechTaskKey);
                        TextTaskStatusEnums textTask = coreTextTaskI18n.getStatus();
                        if (null == speechTask) {
                            if (textTask == TextTaskStatusEnums.SUCCESS) {
                                speechTaskI18n.setStatus(SpeechTaskStatusEnums.PENDING)
                                        .setTranslationText(coreTextTaskI18n.getTranslationText())
                                        .setTranslationTextMd5(coreTextTaskI18n.getTranslationTextMd5());
                            }
                            needAddOrUpdateTaskList.add(speechTaskI18n);
                            allSpeechTaskGroup.put(speechTaskKey, speechTaskI18n);
                        } else {
                            int oldDuration = speechTask.getDuration()==null ? Integer.MAX_VALUE : speechTask.getDuration();
                            if (null != maxDuration && maxDuration < oldDuration) {
                                speechTask.setDuration(maxDuration)
                                        .setAudioUrl(fieldBO.getAudioUrl());
                                if (speechTask.getStatus() == SpeechTaskStatusEnums.SUCCESS) {
                                    speechTask.setStatus(SpeechTaskStatusEnums.PENDING);
                                }
                            }
                            if (textTask == TextTaskStatusEnums.SUCCESS && speechTask.getStatus() == SpeechTaskStatusEnums.WAITING_TEXT_TRANSLATION_COMPLETE) {
                                speechTask.setStatus(SpeechTaskStatusEnums.PENDING)
                                        .setTranslationText(coreTextTaskI18n.getTranslationText())
                                        .setTranslationTextMd5(coreTextTaskI18n.getTranslationTextMd5());
                            }
                            if (!needAddOrUpdateTaskList.contains(speechTask)) {
                                needAddOrUpdateTaskList.add(speechTask);
                            }
                        }
                    }

                }
            }
        }
        return needAddOrUpdateTaskList;
    }

    private void batchSaveBusinessTaskRelation(Core18nContextBO contextBO) {
        List<LanguageEnums> textLanguageList = contextBO.getTextLanguageList();
        if (CollUtil.isEmpty(textLanguageList)) {
            return;
        }
        List<CoreI18nModel> coreI18nModelList = contextBO.getCoreI18nModelList();
        List<FieldBO> fieldList = contextBO.getFieldList();
        ProjCodeEnums projCode = contextBO.getProjCode();
        Set<Integer> modelIdSet = coreI18nModelList.stream().map(CoreI18nModel::getId).collect(Collectors.toSet());
        List<CoreBusinessTaskRelationI18n> relationList = coreBusinessTaskRelationI18nService.queryByDataIds(modelIdSet);

        Map<String, CoreBusinessTaskRelationI18n> allRelationMap = new HashMap<>(relationList.size());
        for (CoreBusinessTaskRelationI18n relationI18n : relationList) {
            String key = getRelationKey(relationI18n);
            allRelationMap.put(key, relationI18n);
        }
        Set<CoreBusinessTaskRelationI18n> saveOrUpdateRelationSet = new HashSet<>(fieldList.size());
        for (FieldBO fieldBO : fieldList) {
            List<CoreTextTaskI18n> textTaskI18nListOriginal = fieldBO.getTextTaskI18nList();
            List<CoreTextTaskI18n> commonTextTaskI18nList = new ArrayList<>(textTaskI18nListOriginal.size());
            List<CoreTextTaskI18n> specialTextTaskI18nList = new ArrayList<>(textTaskI18nListOriginal.size());
            for (CoreTextTaskI18n coreTextTaskI18n : textTaskI18nListOriginal) {
                List<ProjCodeEnums> projCodeList = coreTextTaskI18n.getProjCode();
                if (projCodeList.contains(projCode)) {
                    specialTextTaskI18nList.add(coreTextTaskI18n);
                } else if (projCodeList.contains(ProjCodeEnums.COMMON)) {
                    commonTextTaskI18nList.add(coreTextTaskI18n);
                }
            }
            List<CoreTextTaskI18n> textTaskI18nList = new ArrayList<>(textTaskI18nListOriginal.size());
            textTaskI18nList.addAll(commonTextTaskI18nList);
            textTaskI18nList.addAll(specialTextTaskI18nList);
            for (CoreTextTaskI18n taskI18n : textTaskI18nList) {
                CoreBusinessTaskRelationI18n relationI18n = new CoreBusinessTaskRelationI18n();
                relationI18n.setCoreTextTaskI18nId(taskI18n.getId())
                        .setLanguage(taskI18n.getLanguage())
                        .setBusinessName(fieldBO.getBusinessName())
                        .setBusinessField(fieldBO.getFieldName())
                        .setBusinessDataId(fieldBO.getDataId())
                        .setUpdateFlag(TEXT_CHANGED)
                        .setAppCode(projCode.getAppCode());
                String relationKey = getRelationKey(relationI18n);
                CoreBusinessTaskRelationI18n relation = allRelationMap.get(relationKey);
                if (null == relation) {
                    saveOrUpdateRelationSet.add(relationI18n);
                    allRelationMap.put(relationKey, relationI18n);
                } else if (!relation.getCoreTextTaskI18nId().equals(relationI18n.getCoreTextTaskI18nId())) {
                    saveOrUpdateRelationSet.remove(relation);
                    relation.setCoreTextTaskI18nId(relationI18n.getCoreTextTaskI18nId());
                    saveOrUpdateRelationSet.add(relation);
                }
            }
        }
        if (CollUtil.isNotEmpty(saveOrUpdateRelationSet)) {
            coreBusinessTaskRelationI18nService.saveOrUpdateBatch(saveOrUpdateRelationSet);
        }
    }

    private void batchSaveTextTask(Core18nContextBO contextBO) {
        List<LanguageEnums> textLanguageList = contextBO.getTextLanguageList();
        if (CollUtil.isEmpty(textLanguageList)) {
            return;
        }
        List<FieldBO> fieldList = contextBO.getFieldList();
        List<CoreTextTaskI18n> textTaskList = query(contextBO);
        Map<String, CoreTextTaskI18n> allTextTaskGroup = new HashMap<>();
        for (CoreTextTaskI18n textTaskI18n : textTaskList) {
            String textTaskKey = getTextTaskKey(textTaskI18n);
            allTextTaskGroup.put(textTaskKey, textTaskI18n);
        }
        List<CoreTextTaskI18n> needAddTaskList = new ArrayList<>();
        List<CoreTextTaskI18n> fieldTextTaskList;
        Set<LanguageEnums> specialLanguageSet;
        for (FieldBO fieldBO : fieldList) {
            specialLanguageSet = new HashSet<>();
            fieldTextTaskList = new ArrayList<>();
            if (CollUtil.isNotEmpty(textLanguageList)) {
                for (LanguageEnums languageEnums : textLanguageList) {
                    CoreTextTaskI18n coreTextTaskI18n = new CoreTextTaskI18n();
                    coreTextTaskI18n.setType(fieldBO.getType().getTextTaskType())
                            .setLanguage(languageEnums)
                            .setText(fieldBO.getFieldValue())
                            .setTextMd5(fieldBO.getFieldValueMd5())
                            .setProjCode(Collections.singletonList(ProjCodeEnums.COMMON));
                    String textTaskKey = getTextTaskKey(coreTextTaskI18n);
                    CoreTextTaskI18n textTask = allTextTaskGroup.get(textTaskKey);
                    if (null == textTask) {
                        needAddTaskList.add(coreTextTaskI18n);
                        fieldTextTaskList.add(coreTextTaskI18n);
                        allTextTaskGroup.put(textTaskKey, coreTextTaskI18n);
                    }else {
                        fieldTextTaskList.add(textTask);
                    }

                    CoreTextTaskI18n projTextTaskI18n = new CoreTextTaskI18n();
                    BeanUtils.copyProperties(coreTextTaskI18n, projTextTaskI18n);
                    projTextTaskI18n.setProjCode(Collections.singletonList(contextBO.getProjCode()));
                    textTaskKey = getTextTaskKey(projTextTaskI18n);
                    textTask = allTextTaskGroup.get(textTaskKey);
                    if (null == textTask) {
                        fieldTextTaskList.add(projTextTaskI18n);
                        allTextTaskGroup.put(textTaskKey, projTextTaskI18n);
                    } else {
                        fieldTextTaskList.add(textTask);
                        if (!textTask.getProjCode().contains(ProjCodeEnums.COMMON) && null != textTask.getId()) {
                            specialLanguageSet.add(textTask.getLanguage());
                        }
                    }

                }
            }
            List<CoreTextTaskI18n> finalFieldTextTaskList = new ArrayList<>();
            for (CoreTextTaskI18n coreTextTaskI18n : fieldTextTaskList) {
                if (finalFieldTextTaskList.contains(coreTextTaskI18n)) {
                    continue;
                }
                if (coreTextTaskI18n.getProjCode().contains(ProjCodeEnums.COMMON)) {
                    finalFieldTextTaskList.add(coreTextTaskI18n);
                    continue;
                }
                if (null != coreTextTaskI18n.getId()) {
                    finalFieldTextTaskList.add(coreTextTaskI18n);
                    continue;
                }
                if (specialLanguageSet.contains(coreTextTaskI18n.getLanguage())) {
                    finalFieldTextTaskList.add(coreTextTaskI18n);
                }
            }
            fieldBO.setTextTaskI18nList(finalFieldTextTaskList);
        }
        if (CollUtil.isNotEmpty(needAddTaskList)) {
            super.saveBatch(needAddTaskList);
        }
    }

    private String getTextTaskKey(CoreTextTaskI18n textTaskI18n) {
        String keyTemplate = "%s-%s-%s-%s";
        return String.format(keyTemplate,
                textTaskI18n.getType(),
                textTaskI18n.getLanguage(),
                textTaskI18n.getTextMd5(),
                textTaskI18n.getProjCode());
    }

    private String getSpeechTaskKey(CoreSpeechTaskI18n speechTaskI18n) {
        String keyTemplate = "%s-%s-%s-%s-%s";
        return String.format(keyTemplate,
                speechTaskI18n.getCoreVoiceConfigI18nId(),
                speechTaskI18n.getCoreVoiceTemplateI18nId(),
                speechTaskI18n.getLanguage(),
                speechTaskI18n.getTextMd5(),
                speechTaskI18n.getProjCode()
        );
    }

    private String getRelationKey(CoreBusinessTaskRelationI18n relation) {
        String keyTemplate = "%s-%s-%s-%s-%s";
        return String.format(keyTemplate,
                relation.getLanguage(),
                relation.getBusinessName(),
                relation.getBusinessField(),
                relation.getBusinessDataId(),
                relation.getAppCode());
    }

    private List<CoreTextTaskI18n> query(Core18nContextBO contextBO) {
        List<LanguageEnums> textLanguageList = contextBO.getTextLanguageList();
        List<List<String>> md5BatchList = Lists.partition(new ArrayList<>(contextBO.getMd5Set()), 100);
        List<CoreTextTaskI18n> taskList = new ArrayList<>();
        for (List<String> md5List : md5BatchList) {
            taskList.addAll(query(md5List, textLanguageList));
        }
        return taskList;
    }

    private List<CoreSpeechTaskI18n> querySpeechTask(Set<String> md5Set, List<LanguageEnums> languageList) {
        List<List<String>> md5BatchList = Lists.partition(new ArrayList<>(md5Set), 100);
        List<CoreSpeechTaskI18n> taskList = new ArrayList<>();
        for (List<String> md5List : md5BatchList) {
            taskList.addAll(coreSpeechTaskI18nService.query(md5List, languageList));
        }
        return taskList;
    }

    private void createFieldList(Core18nContextBO contextBO) throws NoSuchFieldException {
        List<CoreI18nModel> coreI18nModelList = contextBO.getCoreI18nModelList();
        List<FieldBO> fieldList = new ArrayList<>(coreI18nModelList.size() * 10);
        List<FieldBO> audioFieldList = new ArrayList<>(coreI18nModelList.size() * 10);
        Set<Integer> coreVoiceConfigI18nIdSet = contextBO.getCoreVoiceConfigI18nIdSet();
        for (CoreI18nModel i18nModel : coreI18nModelList) {
            for (Field field : i18nModel.getClass().getDeclaredFields()) {
                TranslateField annotation = field.getAnnotation(TranslateField.class);
                if (null == annotation) {
                    continue;
                }
                String value = CoreI18nUtil.getFieldValue(i18nModel, field);

                if (StringUtils.isBlank(value)) {
                    continue;
                }
                TranslationTaskTypeEnums type = annotation.type();
                Integer coreVoiceConfigI18nId = i18nModel.getCoreVoiceConfigI18nId();
                if (TranslationTaskTypeEnums.MULTIPLE_TEXT == type) {
                    String delimiter = annotation.multiTextDelimiter();
                    List<String> textList = StrUtil.split(value, delimiter, true, true);
                    if (CollUtil.isNotEmpty(textList)) {
                        for (String text : textList) {
                            FieldBO fieldBO = FieldBO.createField(i18nModel, field, text, coreVoiceConfigI18nId, type);
                            fieldList.add(fieldBO);
                        }
                    }
                } else {
                    FieldBO fieldBO = FieldBO.createField(i18nModel, field, value, coreVoiceConfigI18nId, type);
                    fieldList.add(fieldBO);
                    if (TranslationTaskTypeEnums.SPEECH == type && null != coreVoiceConfigI18nId) {
                        String durationFieldName = annotation.durationFieldName();
                        String audioUrlFieldName = annotation.audioUrlFieldName();
                        if (StringUtils.isNotBlank(durationFieldName)) {
                            Field durationField = i18nModel.getClass().getDeclaredField(durationFieldName);
                            Integer duration = CoreI18nUtil.getFieldValue(i18nModel, durationField);
                            fieldBO.setDuration(duration);
                        }
                        if (StringUtils.isNotBlank(audioUrlFieldName)) {
                            Field audioUrlField = i18nModel.getClass().getDeclaredField(audioUrlFieldName);
                            String audioUrl = CoreI18nUtil.getFieldValue(i18nModel, audioUrlField);
                            fieldBO.setAudioUrl(audioUrl);
                        }
                        audioFieldList.add(fieldBO);
                        if (null != coreVoiceConfigI18nIdSet) {
                            coreVoiceConfigI18nIdSet.add(coreVoiceConfigI18nId);
                        }
                    }
                }
            }
        }
        contextBO.setFieldList(fieldList);
        contextBO.setAudioFieldList(audioFieldList);
        Set<String> md5Set = fieldList.stream().map(FieldBO::getFieldValueMd5).collect(Collectors.toSet());
        contextBO.setMd5Set(md5Set);
    }


    @Override
    public PageRes<CoreTextTaskI18nPageVO> pageTextTasks(CoreTextTask18nPageReq req) {
        // 先查询所有符合条件的原始数据（不分页）
        LambdaQueryWrapper<CoreTextTaskI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoreTextTaskI18n::getDelFlag, false);

        // 添加查询条件 - 使用链式调用，将判断条件作为第一个参数
        queryWrapper.eq(req.getType() != null, CoreTextTaskI18n::getType, req.getType())
                   .likeRight(StrUtil.isNotBlank(req.getText()), CoreTextTaskI18n::getText, req.getText())
                   .eq(req.getStatus() != null, CoreTextTaskI18n::getStatus, req.getStatus())
                   .eq(req.getCheckStatus() != null, CoreTextTaskI18n::isCheckStatus, req.getCheckStatus())
                   .apply(req.getProjCodeSum() != 0, "proj_code & {0} > 0", req.getProjCodeSum())
                   .in(CollUtil.isNotEmpty(req.getLanguage()), CoreTextTaskI18n::getLanguage, req.getLanguage());

        // 查询所有符合条件的数据
        List<CoreTextTaskI18n> allTasks = this.list(queryWrapper);
        if (CollUtil.isEmpty(allTasks)) {
            return new PageRes<>(new Page<>(req.getPageNum(), req.getPageSize()));
        }

        // 在Java代码中进行分组处理
        List<TextTaskAggregator> taskAggregators = groupAndAggregateTasksInJava(allTasks);

        // 手动分页+转vo
        IPage<CoreTextTaskI18nPageVO> page = PaginationUtil.manualPaginationWithConvert(taskAggregators,
                req.getPageNum(), req.getPageSize(),TextTaskAggregator::toVO);

        return new PageRes<>(page);
    }


    @Transactional
    @Override
    public void updateTranslationText(CoreTaskI18nUpdateReq req) {
        //check if text change
        CoreTextTaskI18n task = super.getById(req.getId());
        if (StrUtil.equals(task.getTranslationText(), req.getTranslationText())) {
            return;
        }
        //update TextTask
        CoreTextTaskI18n entity = mapStruct.toEntity(req);
        entity.setTranslationTextMd5(CoreI18nUtil.getMd5(entity.getTranslationText(), MD5.create()));
        this.updateById(entity.setAuditType(AuditTypeEnums.MANUAL_VERIFICATION));
        //update business relation
        LambdaUpdateWrapper<CoreBusinessTaskRelationI18n> relationWrapper = new LambdaUpdateWrapper<>();
        relationWrapper.eq(CoreBusinessTaskRelationI18n::getCoreTextTaskI18nId, task.getId());
        relationWrapper.eq(CoreBusinessTaskRelationI18n::getLanguage, task.getLanguage());
        relationWrapper.set(CoreBusinessTaskRelationI18n::getUpdateFlag, TEXT_CHANGED);
        coreBusinessTaskRelationI18nService.update(new CoreBusinessTaskRelationI18n(), relationWrapper);
        //update related SpeechTask
        LambdaUpdateWrapper<CoreSpeechTaskI18n> speechTaskWrapper = new LambdaUpdateWrapper<>();
        speechTaskWrapper.eq(CoreSpeechTaskI18n::getCoreTextTaskI18nId, req.getId());
        speechTaskWrapper.set(CoreSpeechTaskI18n::getTranslationText, req.getTranslationText());
        speechTaskWrapper.set(CoreSpeechTaskI18n::getStatus, SpeechTaskStatusEnums.PENDING);
        speechTaskWrapper.set(CoreSpeechTaskI18n::getAuditType, AuditTypeEnums.MANUAL_VERIFICATION);
        speechTaskWrapper.set(CoreSpeechTaskI18n::getTranslationTextMd5, CoreI18nUtil.getMd5(req.getTranslationText(), MD5.create()));
        this.coreSpeechTaskI18nService.update(new CoreSpeechTaskI18n(),speechTaskWrapper);
    }


    @Transactional
    @Override
    public void updateCheckStatus(CoreTaskI18nCheckReq req) {
        BizExceptionUtil.throwIf(req==null || CollUtil.isEmpty(req.getIdList()), "idList is empty");
        LambdaUpdateWrapper<CoreTextTaskI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CoreTextTaskI18n::getId, req.getIdList());
        wrapper.set(CoreTextTaskI18n::isCheckStatus, req.getCheckStatus());
        this.update(new CoreTextTaskI18n(),wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void oldTranslate(OldDataTranslateEnums oldDataTranslate, String textLanguages, String audioLanguages, String appCode) throws NoSuchFieldException {
        IService service = SpringUtil.getBean(oldDataTranslate.getServiceName());
        @SuppressWarnings("unchecked")
        List<CoreI18nModel> list = (List<CoreI18nModel>)service.list();
        CreateTaskDTO createTaskDTO = new CreateTaskDTO(list,textLanguages,audioLanguages,appCode);
        batchSaveOrUpdate(createTaskDTO);
    }

    @Override
    public void oldTranslateByProjCode(ProjCodeEnums projCodeEnums, String textLanguages, String audioLanguages, String appCode) throws NoSuchFieldException {
        List<OldDataTranslateEnums> oldDataTranslateList = OldDataTranslateEnums.getEnumsListByProjCode(projCodeEnums);
        List<String> errorList = new ArrayList<>();
        for (OldDataTranslateEnums oldDataTranslate : oldDataTranslateList) {
            try {
                IService service = SpringUtil.getBean(oldDataTranslate.getServiceName());
                @SuppressWarnings("unchecked")
                List<CoreI18nModel> list = (List<CoreI18nModel>)service.list();
                CreateTaskDTO createTaskDTO = new CreateTaskDTO(list,textLanguages,audioLanguages,appCode);
                ICoreTextTaskI18nService bean = SpringUtil.getBean(ICoreTextTaskI18nService.class);
                bean.batchSaveOrUpdate(createTaskDTO);
            } catch (Exception e) {
                log.error("old translate error: OldDataTranslateEnums -> {}", oldDataTranslate, e);
                errorList.add(oldDataTranslate.getServiceName());
            }
        }
        if (CollUtil.isNotEmpty(errorList)) {
            throw new BizException("old translate error: " + String.join(",", errorList));
        }
    }

    @Transactional
    @Override
    public void addWithTask(CoreTextTaskI18nAddReq req) {
        CoreTextTaskI18nAddReq sourceTask = req.getSourceTask();
        List<CoreTextTaskI18nAddReq.SubTaskReq> languageTasks = req.getLanguageTasks();
        checkTask(req.getSourceTask().getTextMd5(),req.getProjCode());
        checkLanguage(req);
        //assemble new task
        List<CoreTextTaskI18n> tasks = languageTasks.stream().map(t -> {
            CoreTextTaskI18n entity = mapStruct.toEntity(t, req);
            entity.setStatus(TextTaskStatusEnums.SUCCESS).setTranslationTextMd5(MD5.create().digestHex(entity.getTranslationText()));
            entity.setTextMd5(sourceTask.getTextMd5());
            entity.setText(sourceTask.getText());
            return entity;
        }).collect(Collectors.toList());
        this.saveBatch(tasks);
        //handle others
        List<CoreTextTaskI18n> sourceTasks = this.getTasks(req,ListUtil.of(ProjCodeEnums.COMMON));
        updateSourceTaskRelation(sourceTasks,tasks);
        copySourceTaskSpeechTask(sourceTasks,tasks);
    }


    @Override
    public CoreTextTaskI18nDetailVO findTaskDetail(CoreTextTaskI18nDetailReq req) {
        LambdaQueryWrapper<CoreTextTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreTextTaskI18n::getTextMd5,req.getTextMd5());
        wrapper.eq(CoreTextTaskI18n::getType,req.getType());
        List<CoreTextTaskI18n> tasks = this.list(wrapper);
        Map<Object, List<CoreTextTaskI18n>> projTaskMap = tasks.stream().collect(Collectors.groupingBy(
                coreTextTaskI18n -> coreTextTaskI18n.getProjCode().get(0)));
        List<CoreTextTaskI18n> commonTasks = projTaskMap.get(ProjCodeEnums.COMMON);
        List<CoreTextTaskI18n> resultTasks = CollUtil.newArrayList();
        if (ObjUtil.equal(req.getProjCode().get(0), ProjCodeEnums.COMMON)) {
            resultTasks.addAll(commonTasks);
        } else {
            resultTasks.addAll(projTaskMap.get(req.getProjCode().get(0)));
        }
        CoreTextTaskI18n baseTask = resultTasks.get(0);
        CoreTextTaskI18nDetailVO vo = mapStruct.toDetailVO(baseTask);
        vo.setLanguageTasks(resultTasks.stream().map(
                        t->new CoreTextTaskI18nDetailVO.SubTaskVO(t.getLanguage(),t.getTranslationText()))
                        .collect(Collectors.toList()));
        vo.setSelectableLanguages(commonTasks.stream().map(CoreTextTaskI18n::getLanguage).collect(Collectors.toList()));
        return vo;
    }

    /**
     * 高性能分组聚合处理 - 使用通用聚合框架
     *
     * <AUTHOR>
     * @since 2025/07/11
     * @param allTasks 所有符合条件的任务数据
     * @return 分组聚合后的聚合器列表
     */
    private List<TextTaskAggregator> groupAndAggregateTasksInJava(List<CoreTextTaskI18n> allTasks) {
        // 使用通用聚合工具进行分组聚合
        List<TextTaskAggregator> result = TaskAggregatorUtil.groupAndAggregateWithKeyBuilder(
                allTasks,
                this::buildTextGroupKey,
                TextTaskAggregator::new
        );

        // 按text排序
        result.sort(Comparator.comparing(t -> t.getBaseTask().getText(),
                Comparator.nullsLast(String::compareTo)));

        return result;
    }


    /**
     * 高效构造Text任务分组键
     */
    private String buildTextGroupKey(CoreTextTaskI18n task, StringBuilder keyBuilder) {
        keyBuilder.setLength(0); // 清空StringBuilder复用

        // type
        keyBuilder.append(task.getType());
        keyBuilder.append('|');

        // projCode - 优化：避免重复排序
        if (task.getProjCode() != null && !task.getProjCode().isEmpty()) {
            // 如果只有一个元素，直接添加
            if (task.getProjCode().size() == 1) {
                keyBuilder.append(task.getProjCode().get(0).name());
            } else {
                // 多个元素时才排序
                task.getProjCode().stream()
                    .map(Enum::name)
                    .sorted()
                    .forEach(name -> keyBuilder.append(name).append(','));
                // 移除最后一个逗号
                if (keyBuilder.charAt(keyBuilder.length() - 1) == ',') {
                    keyBuilder.setLength(keyBuilder.length() - 1);
                }
            }
        }
        keyBuilder.append('|');

        // textMd5
        if (task.getTextMd5() != null) {
            keyBuilder.append(task.getTextMd5());
        }

        return keyBuilder.toString();
    }

    /**
     * Text任务聚合器 - 继承通用聚合器
     */
    private class TextTaskAggregator extends BaseTaskAggregator<CoreTextTaskI18n, CoreTextTaskI18nPageVO> {

        public TextTaskAggregator(CoreTextTaskI18n firstTask) {
            super(firstTask);
        }

        @Override
        public CoreTextTaskI18nPageVO toVO() {
            CoreTextTaskI18nPageVO vo = mapStruct.toPageVO(baseTask);
            vo.setCreateTime(minCreateTime);
            vo.setUpdateTime(maxUpdateTime);
            vo.setUpdateUser(latestUpdateTask.getUpdateUser());

            // 转换子任务列表 - 预分配容量
            List<CoreTextTaskI18nVO> subTaskList = new ArrayList<>(getAllTasks().size());
            for (CoreTextTaskI18n task : getAllTasks()) {
                subTaskList.add(mapStruct.toVO(task));
            }
            vo.setSubTaskList(subTaskList);

            return vo;
        }
    }

    private List<CoreTextTaskI18n> getTasks(CoreTextTaskI18nAddReq req, List<ProjCodeEnums> projCode) {
        LambdaQueryWrapper<CoreTextTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreTextTaskI18n::getTextMd5,req.getSourceTask().getTextMd5());
        wrapper.eq(CoreTextTaskI18n::getType,req.getSourceTask().getType());
        wrapper.in(CoreTextTaskI18n::getLanguage,req.getLanguageTasks().stream().map(CoreTextTaskI18nAddReq.SubTaskReq::getLanguage).collect(Collectors.toList()));
        BitmaskEnumUtil.addBitmaskCondition(wrapper, CoreTextTaskI18n::getProjCode, projCode, false);
        return this.list(wrapper);
    }

    @Transactional
    @Override
    public void editSpecialTask(CoreTextTaskI18nAddReq req) {
        CoreTextTaskI18nAddReq sourceTask = req.getSourceTask();
        List<CoreTextTaskI18nAddReq.SubTaskReq> languageTasks = req.getLanguageTasks();
        this.checkLanguage( req);
        List<CoreTextTaskI18n> existTask = this.getTasks(req, req.getProjCode());
        Map<LanguageEnums, CoreTextTaskI18n> existTaskMap = existTask.stream().collect(
                Collectors.toMap(CoreTextTaskI18n::getLanguage, task -> task, (a, b) -> b));
        //assemble new task
        List<CoreTextTaskI18n> tasks = languageTasks.stream()
                .filter(t->!existTaskMap.containsKey(t.getLanguage())).map(t -> {
            CoreTextTaskI18n entity = mapStruct.toEntity(t, req);
            entity.setStatus(TextTaskStatusEnums.SUCCESS).setTranslationTextMd5(MD5.create().digestHex(entity.getTranslationText()));
            entity.setTextMd5(sourceTask.getTextMd5());
            entity.setText(sourceTask.getText());
            return entity;
        }).collect(Collectors.toList());
        this.saveBatch(tasks);
        List<CoreTextTaskI18n> sourceTasks = this.getTasks(req,ListUtil.of(ProjCodeEnums.COMMON));
        updateSourceTaskRelation(sourceTasks,tasks);
        copySourceTaskSpeechTask(sourceTasks,tasks);
    }

    private void copySourceTaskSpeechTask(List<CoreTextTaskI18n> sourceTasks, List<CoreTextTaskI18n> tasks) {
        Map<LanguageEnums, CoreTextTaskI18n> taskMap = tasks.stream().collect(
                Collectors.toMap(CoreTextTaskI18n::getLanguage, task -> task, (a, b) -> b));
        LambdaQueryWrapper<CoreSpeechTaskI18n> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.in(CoreSpeechTaskI18n::getCoreTextTaskI18nId,sourceTasks.stream().map(CoreTextTaskI18n::getId).collect(Collectors.toList()));
        List<CoreSpeechTaskI18n> sourceSpeechTasks = this.coreSpeechTaskI18nService.list(relationWrapper);
        List<CoreSpeechTaskI18n> newSpeechTasks = sourceSpeechTasks.stream()
                .filter(speechTask->taskMap.containsKey(speechTask.getLanguage()))
                .peek(speechTask -> {
                    CoreTextTaskI18n textTask = taskMap.get(speechTask.getLanguage());
                    coreSpeechTaskI18nService.resetSpeechTaskField(speechTask,true);
                    speechTask.setProjCode(textTask.getProjCode())
                            .setCoreTextTaskI18nId(textTask.getId())
                            .setTranslationText(textTask.getTranslationText())
                            .setTranslationTextMd5(textTask.getTranslationTextMd5());
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(newSpeechTasks)) {
            coreSpeechTaskI18nService.saveBatch(newSpeechTasks);
        }
    }

    private void updateSourceTaskRelation(List<CoreTextTaskI18n> sourceTasks, List<CoreTextTaskI18n> tasks) {
        Map<LanguageEnums, CoreTextTaskI18n> taskMap = new HashMap<>();
        Set<String> appCodes = new HashSet<>();
        tasks.forEach(task -> {
            taskMap.put(task.getLanguage(),task);
            appCodes.addAll(task.getProjCode().stream().map(ProjCodeEnums::getAppCode).collect(Collectors.toSet()));
        });
        LambdaQueryWrapper<CoreBusinessTaskRelationI18n> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.in(CoreBusinessTaskRelationI18n::getCoreTextTaskI18nId,sourceTasks.stream().map(CoreTextTaskI18n::getId).collect(Collectors.toList()));
        List<CoreBusinessTaskRelationI18n> relations = this.coreBusinessTaskRelationI18nService.list(relationWrapper);

        List<CoreBusinessTaskRelationI18n> updateRelations = relations.stream()
                .filter(relation ->
                        taskMap.containsKey(relation.getLanguage()) && appCodes.contains(relation.getAppCode()))
                .peek(relation -> {
                    CoreTextTaskI18n task = taskMap.get(relation.getLanguage());
                    if (task != null) {
                        relation.setCoreTextTaskI18nId(task.getId());
                    }
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updateRelations)) {
            coreBusinessTaskRelationI18nService.updateBatchById(updateRelations);
        }
    }

    private void checkTask(String textMd5, List<ProjCodeEnums> projCode) {
        LambdaQueryWrapper<CoreTextTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreTextTaskI18n::getTextMd5, textMd5);
        BitmaskEnumUtil.addBitmaskCondition(wrapper, CoreTextTaskI18n::getProjCode, projCode, false);
        List<CoreTextTaskI18n> existTask = this.list(wrapper);
        BizExceptionUtil.throwIfDelay(CollUtil.isNotEmpty(existTask), "task already exist: text:{},projCode:{}",
                () -> existTask.get(0).getText(),
                () -> existTask.stream().map(CoreTextTaskI18n::getProjCode).flatMap(List::stream)
                        .distinct().filter(projCode::contains)
                        .map(ProjCodeEnums::getName).collect(Collectors.joining(","))
        );
    }


    private void checkLanguage(CoreTextTaskI18nAddReq req) {
        LambdaQueryWrapper<CoreTextTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreTextTaskI18n::getTextMd5, req.getSourceTask().getTextMd5());
        wrapper.eq(CoreTextTaskI18n::getType, req.getSourceTask().getType());
        BitmaskEnumUtil.addBitmaskCondition(wrapper, CoreTextTaskI18n::getProjCode, ListUtil.of(ProjCodeEnums.COMMON), false);
        Set<LanguageEnums> commonTaskLanguages = this.list(wrapper).stream()
                .map(CoreTextTaskI18n::getLanguage)
                .collect(Collectors.toSet());
        Set<LanguageEnums> requestedLanguages = req.getLanguageTasks().stream()
                .map(CoreTextTaskI18nAddReq.SubTaskReq::getLanguage)
                .collect(Collectors.toSet());
        requestedLanguages.removeAll(commonTaskLanguages);
        BizExceptionUtil.throwIf(!requestedLanguages.isEmpty(),"language:{} is not in the public Task",requestedLanguages);
    }
}
