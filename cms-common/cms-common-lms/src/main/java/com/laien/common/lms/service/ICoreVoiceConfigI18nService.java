package com.laien.common.lms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.domain.response.LanguageEnumVO;
import com.laien.common.frame.response.PageRes;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.domain.request.CoreVoiceConfigI18nAddReq;
import com.laien.common.domain.request.CoreVoiceConfigI18nPageReq;
import com.laien.common.domain.request.CoreVoiceConfigI18nUpdateReq;
import com.laien.common.domain.request.CoreVoiceConfigTemplateI18nUpdateReq;
import com.laien.common.domain.response.CoreVoiceConfigI18nVO;
import com.laien.common.domain.response.CoreVoiceTemplateI18nListVO;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ICoreVoiceConfigI18nService extends IService<CoreVoiceConfigI18n> {

    PageRes<CoreVoiceConfigI18nVO> pageConfig(CoreVoiceConfigI18nPageReq req);

    List<CoreVoiceConfigI18n> listConfigByIds(Collection<Integer> ids);

    List<CoreVoiceConfigI18n> listConfigByNames(Collection<String> names);

    CoreVoiceConfigI18nVO findDetailById(Integer id);

    void addConfig(CoreVoiceConfigI18nAddReq addReq);

    void updateRelation(CoreVoiceConfigTemplateI18nUpdateReq req);

    void updateConfig(CoreVoiceConfigI18nUpdateReq req);

    void updateEnableByIds(List<Integer> idList);

    void updateDisableByIds(List<Integer> idList);

    List<CoreVoiceTemplateI18nListVO> getAllTemplates();

    List<LanguageEnumVO> getAllSupportLanguages();
}
