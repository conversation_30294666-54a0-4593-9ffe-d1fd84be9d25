package com.laien.common.lms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.dto.CreateTaskDTO;
import com.laien.common.domain.entity.CoreTextTaskI18n;
import com.laien.common.domain.enums.OldDataTranslateEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.TextTaskStatusEnums;
import com.laien.common.domain.request.*;
import com.laien.common.domain.response.CoreTextTaskI18nDetailVO;
import com.laien.common.domain.response.CoreTextTaskI18nPageVO;
import com.laien.common.frame.response.PageRes;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ICoreTextTaskI18nService extends IService<CoreTextTaskI18n> {

    void batchSaveOrUpdate(CreateTaskD<PERSON> createTaskDTO) throws NoSuchFieldException;

    void changeStatus(Collection<Integer> isd, TextTaskStatusEnums status, boolean checkStatus);

    void addRetryCount(Collection<Integer> ids);

    List<CoreTextTaskI18n> query(Collection<String> md5s, List<LanguageEnums> languageList);

    void batchUpdate(List<CoreTextTaskI18n> taskI18nList);

    List<CoreTextTaskI18n> query(List<TextTaskStatusEnums> statusList, Integer limitSize, Integer retryCount);

    /**
     * 查询执行超时任务
     */
    List<CoreTextTaskI18n> queryStatusTimeoutTask(List<TextTaskStatusEnums> statusList, Integer timeoutDuration, Integer limitSize, Integer retryCount);

    PageRes<CoreTextTaskI18nPageVO> pageTextTasks(CoreTextTask18nPageReq req);

    void updateTranslationText(CoreTaskI18nUpdateReq req);

    void updateCheckStatus(CoreTaskI18nCheckReq req);

    void oldTranslateByProjCode(ProjCodeEnums projCodeEnums, String textLanguages, String audioLanguages, String appCode) throws NoSuchFieldException;

    void addWithTask(CoreTextTaskI18nAddReq req);

    void oldTranslate(OldDataTranslateEnums oldDataTranslate, String textLanguages, String audioLanguages, String appCode) throws NoSuchFieldException;

    CoreTextTaskI18nDetailVO findTaskDetail(CoreTextTaskI18nDetailReq req);

    void editSpecialTask(CoreTextTaskI18nAddReq req);
}
