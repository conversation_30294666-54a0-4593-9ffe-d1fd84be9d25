package com.laien.common.lms.controller;

import com.alibaba.excel.EasyExcel;
import com.laien.common.domain.request.CoreLmsPubReq;
import com.laien.common.domain.response.CoreBusinessTaskRelationVO;
import com.laien.common.domain.response.CoreLmsPublishLogPageVO;
import com.laien.common.domain.response.CoreLmsTaskStatusCountVO;
import com.laien.common.frame.controller.ResponseController;
import com.laien.common.frame.request.PageReq;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.response.setting.ResponseResult;
import com.laien.common.lms.service.ICoreLmsPublishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * <p>
 * CoreLmsPublishLog 控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Api(tags = "LMS发布管理")
@RestController
@RequestMapping("/lms/publish")
@RequiredArgsConstructor
public class CoreLmsPublishLogController extends ResponseController {

    private final ICoreLmsPublishService service;


    /**
     * 分页查询LMS发布记录
     * @param req 分页请求体
     * @return 分页结果
     */
    @ApiOperation(value = "分页查询LMS发布记录",tags = "lms")
    @GetMapping("/page")
    public ResponseResult<PageRes<CoreLmsPublishLogPageVO>> page(PageReq req) {
        return succ(service.selectPage(req));
    }

    /**
     *
     * @return 任务状态统计信息
     */
    @ApiOperation(value = "查询LMS任务状态统计信息",tags = "lms")
    @GetMapping("/taskStatusCount")
    public ResponseResult<CoreLmsTaskStatusCountVO> taskStatusCount() {
        return succ(service.getTaskStatusCount());
    }

    /**
     * LMS发布操作
     * @param req 发布请求体
     * @return 发布版本号
     */
    @ApiOperation(value = "LMS发布操作",tags = "lms")
    @PostMapping("/pubOnline")
    public ResponseResult<Integer> pubOnline(@RequestBody CoreLmsPubReq req) {
        return succ(service.savePublish(req));
    }

    @SneakyThrows
    @ApiOperation(value = "批量导出受影响的数据信息")
    @GetMapping("/exportRelation")
    public void export(HttpServletResponse response, Integer id) {

        // 查询数据
        List<CoreBusinessTaskRelationVO> list = service.query(id);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("pubResult", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), CoreBusinessTaskRelationVO.class)
                .sheet("pubResult")
                .doWrite(list);
    }

}
