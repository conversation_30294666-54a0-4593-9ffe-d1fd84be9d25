package com.laien.common.lms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreBusinessTaskRelationI18n;
import com.laien.common.domain.enums.UpdateFlagEnums;
import com.laien.common.lms.mapper.CoreBusinessTaskRelationI18nMapper;
import com.laien.common.lms.service.ICoreBusinessTaskRelationI18nService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class CoreBusinessTaskRelationI18nServiceImpl extends ServiceImpl<CoreBusinessTaskRelationI18nMapper, CoreBusinessTaskRelationI18n> implements ICoreBusinessTaskRelationI18nService {

    @Override
    public List<CoreBusinessTaskRelationI18n> queryByDataIds(Collection<Integer> businessDataIds) {
        if(CollUtil.isEmpty(businessDataIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CoreBusinessTaskRelationI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreBusinessTaskRelationI18n::getBusinessDataId, businessDataIds);
        return list(wrapper);
    }

    @Override
    public void update(Integer coreTextTaskI18nId, UpdateFlagEnums updateFlag) {
        LambdaUpdateWrapper<CoreBusinessTaskRelationI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CoreBusinessTaskRelationI18n::getCoreTextTaskI18nId, coreTextTaskI18nId)
                .set(CoreBusinessTaskRelationI18n::getUpdateFlag, updateFlag);
        baseMapper.update(new CoreBusinessTaskRelationI18n(), wrapper);
    }

    @Override
    public void updateTextFlag(Set<Integer> coreTextTaskI18nIdSet) {
        LambdaUpdateWrapper<CoreBusinessTaskRelationI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.ne(CoreBusinessTaskRelationI18n::getUpdateFlag, UpdateFlagEnums.AUDIO_CHANGED)
                .in(CoreBusinessTaskRelationI18n::getCoreTextTaskI18nId, coreTextTaskI18nIdSet)
                .set(CoreBusinessTaskRelationI18n::getUpdateFlag, UpdateFlagEnums.TEXT_CHANGED);
        baseMapper.update(new CoreBusinessTaskRelationI18n(), wrapper);
    }

    @Override
    public List<CoreBusinessTaskRelationI18n> query(Collection<UpdateFlagEnums> updateFlags) {
        LambdaQueryWrapper<CoreBusinessTaskRelationI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreBusinessTaskRelationI18n::getUpdateFlag, updateFlags);
        return list(wrapper);
    }

    @Override
    public void batchUpdateByIds(Collection<Integer> ids, UpdateFlagEnums updateFlag) {
        if(CollUtil.isEmpty(ids)){
            return;
        }
        LambdaUpdateWrapper<CoreBusinessTaskRelationI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CoreBusinessTaskRelationI18n::getId, ids)
                .set(CoreBusinessTaskRelationI18n::getUpdateFlag, updateFlag);
        baseMapper.update(new CoreBusinessTaskRelationI18n(), wrapper);
    }
}
