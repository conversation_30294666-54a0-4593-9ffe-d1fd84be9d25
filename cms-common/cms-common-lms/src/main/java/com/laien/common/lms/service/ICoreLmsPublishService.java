package com.laien.common.lms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.domain.entity.CoreLmsPublishLog;
import com.laien.common.domain.request.CoreLmsPubReq;
import com.laien.common.domain.response.CoreBusinessTaskRelationVO;
import com.laien.common.domain.response.CoreLmsPublishLogPageVO;
import com.laien.common.domain.response.CoreLmsTaskStatusCountVO;
import com.laien.common.frame.request.PageReq;
import com.laien.common.frame.response.PageRes;

import java.util.List;

/**
 * <p>
 * 发布日志 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
public interface ICoreLmsPublishService extends IService<CoreLmsPublishLog> {

    Integer savePublish(CoreLmsPubReq req);

    List<CoreBusinessTaskRelationVO> query(Integer id);

    PageRes<CoreLmsPublishLogPageVO> selectPage(PageReq pageReq);

    CoreLmsTaskStatusCountVO getTaskStatusCount();
}
