package com.laien.common.lms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.laien.common.domain.entity.CoreTextTaskI18n;
import com.laien.common.domain.request.CoreTextTask18nPageReq;
import com.laien.common.domain.response.CoreTextTaskI18nPageVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface CoreTextTaskI18nMapper extends BaseMapper<CoreTextTaskI18n> {

    IPage<CoreTextTaskI18nPageVO> pageGroupByTasks(IPage<?> page,
                                                   @Param("req") CoreTextTask18nPageReq req);


    void addRetryCount(@Param("ids") Collection<Integer> ids);
}
