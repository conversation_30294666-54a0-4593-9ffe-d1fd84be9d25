package com.laien.common.lms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.common.frame.response.IdAndStatusCountsRes;
import com.laien.common.domain.entity.CoreLmsPublishLog;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import com.laien.common.domain.enums.TextTaskStatusEnums;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 发布日志 Mapper 接口
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
public interface CoreLmsPublishLogMapper extends BaseMapper<CoreLmsPublishLog> {

    @Insert("INSERT INTO core_text_task_i18n_pub SELECT #{version} version, t.* FROM core_text_task_i18n t " +
            "where t.del_flag = 0 and t.status = #{successStatusCode}")
    int insertTextTaskToPub(@Param("version") Integer version,@Param("successStatusCode") Integer successStatusCode);

    @Insert("INSERT INTO core_speech_task_i18n_pub SELECT #{version} version, t.* FROM core_speech_task_i18n t " +
            "where t.del_flag = 0 and t.status = #{successStatusCode}")
    int insertSpeechTaskToPub(@Param("version") Integer version,@Param("successStatusCode") Integer successStatusCode);

    @Delete(" <script> " +
            " DELETE from core_text_task_i18n_pub t WHERE t.version NOT IN " +
            " <foreach collection='versionList' item='version'  open='(' separator=',' close=')'> " +
            " #{version}" +
            " </foreach>" +
            "</script>")
    int deleteTextTaskPub(@Param("versionList") Set<Integer> excludeVersionList);


    @Delete(" <script> " +
            " DELETE from core_speech_task_i18n_pub t WHERE t.version NOT IN " +
            " <foreach collection='versionList' item='version'  open='(' separator=',' close=')'> " +
            " #{version}" +
            " </foreach>" +
            "</script>")
    int deleteSpeechTaskPub(@Param("versionList") Set<Integer> excludeVersionList);


    @Select("SELECT " +
            "  COUNT(DISTINCT tt.id) as counts, " +
            "  tt.status as status " +
            "  FROM core_text_task_i18n tt " +
            "  WHERE tt.status != #{textStatus}  and tt.del_flag = 0 " +
            "  GROUP BY tt.status "
            )
    List<IdAndStatusCountsRes> countTextTaskStatus(@Param("textStatus") TextTaskStatusEnums textStatus);

    @Select("SELECT " +
            "  COUNT(DISTINCT st.id) as counts, " +
            "  st.status as status " +
            " FROM core_speech_task_i18n st " +
            " WHERE st.status != #{speechStatus} and st.del_flag = 0 " +
            " GROUP BY st.status"
            )
    List<IdAndStatusCountsRes> countSpeechTaskStatus(@Param("speechStatus") SpeechTaskStatusEnums speechStatus);
}
