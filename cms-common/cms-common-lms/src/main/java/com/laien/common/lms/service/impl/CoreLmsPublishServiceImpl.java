package com.laien.common.lms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.constant.RedisKeyConstant;
import com.laien.common.domain.entity.CoreBusinessTaskRelationI18n;
import com.laien.common.domain.entity.CoreLmsPublishLog;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.entity.CoreTextTaskI18n;
import com.laien.common.domain.enums.PublishResultEnums;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import com.laien.common.domain.enums.TextTaskStatusEnums;
import com.laien.common.domain.enums.UpdateFlagEnums;
import com.laien.common.domain.request.CoreLmsPubReq;
import com.laien.common.domain.response.CoreBusinessTaskRelationVO;
import com.laien.common.domain.response.CoreLmsPublishLogPageVO;
import com.laien.common.domain.response.CoreLmsTaskStatusCountVO;
import com.laien.common.frame.exception.BizException;
import com.laien.common.frame.request.PageReq;
import com.laien.common.frame.response.IdAndStatusCountsRes;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.utils.BizExceptionUtil;
import com.laien.common.lms.config.CoreI18nConfig;
import com.laien.common.lms.mapper.CoreLmsPublishLogMapper;
import com.laien.common.lms.mapstruct.CoreLmsPublishLogMapStruct;
import com.laien.common.lms.service.ICoreBusinessTaskRelationI18nService;
import com.laien.common.lms.service.ICoreLmsPublishService;
import com.laien.common.lms.service.ICoreSpeechTaskI18nService;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 文本任务多语言表 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreLmsPublishServiceImpl extends ServiceImpl<CoreLmsPublishLogMapper, CoreLmsPublishLog>
        implements ICoreLmsPublishService {

    private final RedissonClient redissonClient;
    private final ICoreTextTaskI18nService textTaskI18nService;
    private final ICoreSpeechTaskI18nService speechTaskI18nService;
    private final CoreLmsPublishLogMapStruct mapStruct;
    private final TransactionTemplate transactionTemplate;
    private final ICoreBusinessTaskRelationI18nService coreBusinessTaskRelationI18nService;

    private final CoreI18nConfig coreI18nConfig;

    private final String slackMsgTemplate = "{\n" +
            "\t\"blocks\": [\n" +
            "\t\t{\n" +
            "\t\t\t\"type\": \"section\",\n" +
            "\t\t\t\"text\": {\n" +
            "\t\t\t\t\"type\": \"mrkdwn\",\n" +
            "\t\t\t\t\"text\": \"\uD83D\uDC11<@U03MJ64NLFJ>有新的本地化数据发布，影响信息\"\n" +
            "\t\t\t}\n" +
            "\t\t},\n" +
            "\t\t{\n" +
            "\t\t\t\"type\": \"rich_text\",\n" +
            "\t\t\t\"elements\": [\n" +
            "\t\t\t\t{\n" +
            "\t\t\t\t\t\"type\": \"rich_text_section\",\n" +
            "\t\t\t\t\t\"elements\": [\n" +
            "\t\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\t\"type\": \"text\",\n" +
            "\t\t\t\t\t\t\t\"text\": \"app code: \"\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\t\"type\": \"text\",\n" +
            "\t\t\t\t\t\t\t\"text\": \"%s\",\n" +
            "\t\t\t\t\t\t\t\"style\": {\n" +
            "\t\t\t\t\t\t\t\t\"bold\": true\n" +
            "\t\t\t\t\t\t\t}\n" +
            "\t\t\t\t\t\t}\n" +
            "\t\t\t\t\t]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t]\n" +
            "\t\t}\n" +
            "\t]\n" +
            "}";

    private static final int DB_MAX_ERROR_LENGTH = 3000;


    @Override
    public Integer savePublish(CoreLmsPubReq req) {
        final RLock lock = redissonClient.getLock(RedisKeyConstant.LMS_PUBLISH_LOCK_KEY);
        BizExceptionUtil.throwIf(!lock.tryLock(), "Someone is publishing [lms] in the current period, Please operate later");
        Integer publishVersion = null;
        try {
            checkPublishStatus();
            publishVersion = determinePublishVersion();
            processPublishData(publishVersion,req);
            return publishVersion;
        } catch (Exception e) {
            log.error("An error occurred in publishing", e);
            handlePublishFailure(publishVersion,req, e);
            if (e instanceof BizException) {
                throw (BizException)e;
            } else {
                throw new BizException("An error occurred in publishing");
            }
        } finally {
            handlePostPublishActions();
            lock.unlock();
        }
    }

    @Override
    public List<CoreBusinessTaskRelationVO> query(Integer id) {
        CoreLmsPublishLog coreLmsPublishLog = baseMapper.selectById(id);
        String relationI18ns = coreLmsPublishLog.getCoreBusinessTaskRelationI18ns();
        if(StrUtil.isBlank(relationI18ns)){
            return new ArrayList<>();
        }
        List<Integer> relationIdList = JSONUtil.parseArray(relationI18ns).toList(Integer.class);
        if(CollUtil.isEmpty(relationIdList)){
            return new ArrayList<>();
        }
        Collection<CoreBusinessTaskRelationI18n> relationList = coreBusinessTaskRelationI18nService.listByIds(relationIdList);
        List<CoreBusinessTaskRelationVO> relationVOList = new ArrayList<>(relationList.size());
        for (CoreBusinessTaskRelationI18n relation : relationList) {
            CoreBusinessTaskRelationVO relationVO = new CoreBusinessTaskRelationVO();
            BeanUtil.copyProperties(relation,relationVO);
            relationVOList.add(relationVO);
        }
        return relationVOList;
    }


    private void sendSlack(Set<String> appCodeList){
        if(CollUtil.isEmpty(appCodeList)){
            return;
        }
        String appCodes = CollUtil.join(appCodeList, ",");
        String msg = String.format(slackMsgTemplate, appCodes);
        sendMessage(msg, coreI18nConfig.getI18nSlackWebhookUrl());

    }
    private void checkPublishStatus() {
        LambdaQueryWrapper<CoreTextTaskI18n> textQuery = new LambdaQueryWrapper<>();
        List<TextTaskStatusEnums> textTaskStatusEnumsList = EnumSet.allOf(TextTaskStatusEnums.class)
                .stream().filter(e -> e != TextTaskStatusEnums.SUCCESS).collect(Collectors.toList());
        textQuery.in(CoreTextTaskI18n::getStatus, textTaskStatusEnumsList);
        int textCount = textTaskI18nService.count(textQuery);
        BizExceptionUtil.throwIf(textCount > 0, "{} text tasks are still pending.",textCount);

        LambdaQueryWrapper<CoreSpeechTaskI18n> speechQuery = new LambdaQueryWrapper<>();
        List<SpeechTaskStatusEnums> speechTaskStatusEnumsList = EnumSet.allOf(SpeechTaskStatusEnums.class)
                .stream().filter(e -> e != SpeechTaskStatusEnums.SUCCESS).collect(Collectors.toList());
        speechQuery.in(CoreSpeechTaskI18n::getStatus, speechTaskStatusEnumsList);
        int speechCount = speechTaskI18nService.count(speechQuery);
        BizExceptionUtil.throwIf(speechCount > 0, "{}  speech tasks are still pending.",speechCount);
    }

    private void deletePubHistory() {
        // 保留最近3次发布成功的数据，发布失败不保留
        LambdaQueryWrapper<CoreLmsPublishLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoreLmsPublishLog::getResult, PublishResultEnums.Success.name())
                .orderByDesc(CoreLmsPublishLog::getId)
                .last("LIMIT 3");
        List<CoreLmsPublishLog> list = this.list(queryWrapper);
        if (list.isEmpty()) return;
        Set<Integer> versionSet = list.stream().map(CoreLmsPublishLog::getVersion).collect(Collectors.toSet());
        baseMapper.deleteTextTaskPub(versionSet);
        baseMapper.deleteSpeechTaskPub(versionSet);
    }

    private void handlePublishFailure (Integer publishVersion, CoreLmsPubReq req, Exception e) throws BizException {
        if (publishVersion != null) {
            savePublishLog(publishVersion, req.getRemark(), PublishResultEnums.Fail, e.getMessage(), null, null);
        }
    }

    private void handlePostPublishActions() {
        // 在确保数据库操作完成后，执行缓存清理
        redissonClient.getBucket(RedisKeyConstant.LMS_PUBLISH_VERSION).delete();
    }

    private void savePublishLog(Integer version, String remark, PublishResultEnums PublishResultEnums, String errorMsg, String audioRelations, String textRelations) {
        CoreLmsPublishLog log = new CoreLmsPublishLog()
                .setRemark(remark)
                .setVersion(version)
                .setResult(PublishResultEnums.name())
                .setCoreBusinessTaskRelationI18ns(audioRelations)
                .setTextCoreBusinessTaskRelationI18ns(textRelations)
                .setFailReason(processErrorMessage(errorMsg));
        this.save(log);
    }

    private String processErrorMessage(String error) {
        return error != null && error.length() > DB_MAX_ERROR_LENGTH
                ? error.substring(0, DB_MAX_ERROR_LENGTH)
                : error;
    }

    private void processPublishData(Integer publishVersion, CoreLmsPubReq req) {
        transactionTemplate.executeWithoutResult(status -> {
            try {
                List<CoreBusinessTaskRelationI18n> relationI18nList = coreBusinessTaskRelationI18nService.query(Arrays.asList(UpdateFlagEnums.TEXT_CHANGED,UpdateFlagEnums.AUDIO_CHANGED));
                List<Integer> textRelationIds = new ArrayList<>(relationI18nList.size());
                List<Integer> audioRelationIds = new ArrayList<>(relationI18nList.size());
                List<Integer> allRelationIds = new ArrayList<>(relationI18nList.size());
                Set<String> appCodeSet = new HashSet<>();
                for (CoreBusinessTaskRelationI18n relationI18n : relationI18nList) {
                    allRelationIds.add(relationI18n.getId());
                    appCodeSet.add(relationI18n.getAppCode());
                    if (relationI18n.getUpdateFlag() == UpdateFlagEnums.TEXT_CHANGED) {
                        textRelationIds.add(relationI18n.getId());
                    } else {
                        audioRelationIds.add(relationI18n.getId());
                    }
                }

                baseMapper.insertTextTaskToPub(publishVersion,TextTaskStatusEnums.SUCCESS.getCode());
                baseMapper.insertSpeechTaskToPub(publishVersion,SpeechTaskStatusEnums.SUCCESS.getCode());
                // 保存发布日志（数据库操作）
                savePublishLog(publishVersion, req.getRemark(), PublishResultEnums.Success, null, JSONUtil.toJsonStr(audioRelationIds), JSONUtil.toJsonStr(textRelationIds));
                coreBusinessTaskRelationI18nService.batchUpdateByIds(allRelationIds, UpdateFlagEnums.NOT_CHANGED);
                if (coreI18nConfig.isEnableSlackMsg()) {
                    sendSlack(appCodeSet);
                }
                deletePubHistory();
            } catch (Exception ex) {            // 任何异常都标记回滚
                status.setRollbackOnly();
                throw ex;
            }
        });
    }

    private void sendMessage(String message, String webhookUrl) {

        try {

            String result = HttpRequest.post(webhookUrl)
                    .header("Content-Type", "application/json")
                    .body(message)
                    .execute().body();
            if (!"ok".equals(result)) {
                log.error("Error posting log to Slack: {}, text: {}", result, message);
            }
        } catch (Exception e) {
            log.error("Error posting log to Slack: {}", e.getMessage(), e);
        }
    }

    private Integer determinePublishVersion() {
        QueryWrapper<CoreLmsPublishLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("ifnull(max(version), 0) maxVersion");
        return this.getObj(queryWrapper,o -> Integer.parseInt(o.toString()))+1;
    }


    @Override
    public PageRes<CoreLmsPublishLogPageVO> selectPage(PageReq pageReq) {
        LambdaQueryWrapper<CoreLmsPublishLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(CoreLmsPublishLog::getId);
        Page<CoreLmsPublishLog> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        this.page(page, queryWrapper);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), mapStruct.toVOList(page.getRecords()));
    }


    @Override
    public CoreLmsTaskStatusCountVO getTaskStatusCount() {
        List<IdAndStatusCountsRes> textTaskStatus = baseMapper.countTextTaskStatus(TextTaskStatusEnums.SUCCESS);
        List<IdAndStatusCountsRes> speechTaskStatus = baseMapper.countSpeechTaskStatus(SpeechTaskStatusEnums.SUCCESS);
        CoreLmsTaskStatusCountVO result = new CoreLmsTaskStatusCountVO();
        Map<Boolean, Integer> textCounts = textTaskStatus.stream()
                .collect(Collectors.partitioningBy(
                        o -> ObjUtil.equals(o.getStatus(), TextTaskStatusEnums.FAIL.getCode()),
                        Collectors.summingInt(IdAndStatusCountsRes::getCounts)
                ));
        result.setTextFailCount(textCounts.getOrDefault(true, 0));
        result.setTextPendingCount(textCounts.getOrDefault(false, 0));
        Map<Boolean, Integer> speechCounts = speechTaskStatus.stream()
                .collect(Collectors.partitioningBy(
                        o -> ObjUtil.equals(o.getStatus(), SpeechTaskStatusEnums.FAIL.getCode()),
                        Collectors.summingInt(IdAndStatusCountsRes::getCounts)
                ));
        result.setSpeechFailCount(speechCounts.getOrDefault(true, 0));
        result.setSpeechPendingCount(speechCounts.getOrDefault(false, 0));
        return result;
    }
}
