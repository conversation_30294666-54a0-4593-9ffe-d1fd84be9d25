package com.laien.common.lms.controller;

import com.laien.common.domain.request.*;
import com.laien.common.domain.response.CoreTextTaskI18nDetailVO;
import com.laien.common.domain.response.CoreTextTaskI18nPageVO;
import com.laien.common.frame.controller.ResponseController;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.response.setting.ResponseResult;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * CoreTextTaskI18nController 控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Api(tags = "LMS-TextHtml管理")
@RestController
@RequestMapping("/lms/text")
@RequiredArgsConstructor
public class CoreTextTaskI18nController extends ResponseController {

    private final ICoreTextTaskI18nService service;

    /**
     * 分页查询LMS发布记录
     * @param req 分页请求体
     * @return 分页结果
     */
    @ApiOperation(value = "分页查询LMS-TextHtml列表",tags = "lms")
    @GetMapping("/page")
    public ResponseResult<PageRes<CoreTextTaskI18nPageVO>> page(CoreTextTask18nPageReq req) {
        return succ(service.pageTextTasks(req));
    }

    @ApiOperation(value = "taskDetail详情",tags = "lms")
    @GetMapping("/taskDetail")
    public ResponseResult<CoreTextTaskI18nDetailVO> findTaskDetail(CoreTextTaskI18nDetailReq req) {
        return succ(service.findTaskDetail(req));
    }

    /**
     * 修改翻译结果文本
     * @param updateReq 修改请求体
     * @return 操作结果
     */
    @ApiOperation(value = "修改LMS-TextHtml翻译结果文本",tags = "lms")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody CoreTaskI18nUpdateReq updateReq) {
        service.updateTranslationText(updateReq);
        return succ();
    }


    /**
     * 修改LMS-TextHtml的checkStatus
     * @param req 修改请求体
     * @return 操作结果
     */
    @ApiOperation(value = "修改LMS-TextHtml的checkStatus",tags = "lms")
    @PostMapping("/updateCheckStatus")
    public ResponseResult<Void> updateCheckStatus(@RequestBody CoreTaskI18nCheckReq req) {
        service.updateCheckStatus(req);
        return succ();
    }

    @ApiOperation(value = "特殊添加LMS-TextHtml任务",tags = "lms")
    @PostMapping("/addWithTask")
    public ResponseResult<Void> addWithTask(@RequestBody CoreTextTaskI18nAddReq req) {
        service.addWithTask(req);
        return succ();
    }

    @ApiOperation(value = "编辑特殊的LMS-TextHtml任务",tags = "lms")
    @PostMapping("/editSpecialTask")
    public ResponseResult<Void> editSpecialTask(@RequestBody CoreTextTaskI18nAddReq req) {
        service.editSpecialTask(req);
        return succ();
    }



}
