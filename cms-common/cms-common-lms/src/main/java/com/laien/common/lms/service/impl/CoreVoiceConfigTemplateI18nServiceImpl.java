package com.laien.common.lms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreVoiceConfigTemplateI18n;
import com.laien.common.lms.mapper.CoreVoiceConfigTemplateI18nMapper;
import com.laien.common.lms.service.ICoreVoiceConfigTemplateI18nService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class CoreVoiceConfigTemplateI18nServiceImpl extends ServiceImpl<CoreVoiceConfigTemplateI18nMapper, CoreVoiceConfigTemplateI18n> implements ICoreVoiceConfigTemplateI18nService {

    @Override
    public List<CoreVoiceConfigTemplateI18n> query(Set<Integer> coreVoiceConfigI18nIds) {
        if(CollUtil.isEmpty(coreVoiceConfigI18nIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CoreVoiceConfigTemplateI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreVoiceConfigTemplateI18n::getCoreVoiceConfigI18nId, coreVoiceConfigI18nIds);
        return list(wrapper);
    }

}
