package com.laien.common.lms.controller;

import com.laien.common.domain.request.CoreSpeechTask18nPageReq;
import com.laien.common.domain.request.CoreTaskI18nCheckReq;
import com.laien.common.domain.request.CoreTaskI18nUpdateReq;
import com.laien.common.domain.response.CoreSpeechTaskI18nPageVO;
import com.laien.common.frame.controller.ResponseController;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.response.setting.ResponseResult;
import com.laien.common.lms.service.ICoreSpeechTaskI18nService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * CoreSpeechTaskI18nController 控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Api(tags = "LMS-TTS管理")
@RestController
@RequestMapping("/lms/tts")
@RequiredArgsConstructor
public class CoreSpeechTaskI18nController extends ResponseController {

    private final ICoreSpeechTaskI18nService service;

    /**
     * 分页查询LMS发布记录
     * @param req 分页请求体
     * @return 分页结果
     */
    @ApiOperation(value = "分页查询LMS-TTS列表",tags = "lms")
    @GetMapping("/page")
    public ResponseResult<PageRes<CoreSpeechTaskI18nPageVO>> page(CoreSpeechTask18nPageReq req) {
        return succ(service.pageSpeechTasks(req));
    }

    /**
     * 修改翻译结果文本
     * @param updateReq 修改请求体
     * @return 操作结果
     */
    @ApiOperation(value = "修改LMS-TTS翻译结果文本",tags = "lms")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody CoreTaskI18nUpdateReq updateReq) {
        service.updateTranslationText(updateReq);
        return succ();
    }

    /**
     * 修改LMS-TTS的checkStatus
     * @param req 修改请求体
     * @return 操作结果
     */
    @ApiOperation(value = "修改LMS-TTS的checkStatus",tags = "lms")
    @PostMapping("/updateCheckStatus")
    public ResponseResult<Void> updateCheckStatus(@RequestBody CoreTaskI18nCheckReq req) {
        service.updateCheckStatus(req);
        return succ();
    }

}
