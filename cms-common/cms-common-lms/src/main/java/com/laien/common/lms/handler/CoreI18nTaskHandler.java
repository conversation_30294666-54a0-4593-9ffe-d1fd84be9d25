package com.laien.common.lms.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.laien.common.core.dto.*;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.constant.LmsConstant;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.entity.CoreTextTaskI18n;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import com.laien.common.domain.enums.TextTaskStatusEnums;
import com.laien.common.frame.response.setting.ResponseCode;
import com.laien.common.frame.response.setting.ResponseResult;
import com.laien.common.lms.config.CoreI18nConfig;
import com.laien.common.lms.service.ICoreSpeechTaskI18nService;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.laien.common.domain.enums.TextTaskStatusEnums.RUNNING;

/**
 * <AUTHOR>
 * @since 2025/6/10
 */
@Slf4j
@Component
public class CoreI18nTaskHandler {

    @Resource
    private ICoreTextTaskI18nService coreTextTaskI18nService;

    @Resource
    private ICoreSpeechTaskI18nService coreSpeechTaskI18nService;

    @Resource
    private OkHttpClient client;
    @Resource
    private CoreI18nConfig coreI18nConfig;

    private static final Integer SLEEP_DURATION = 2000;

    private static final Integer LIMIT_SIZE = 5000;
    private static final Integer QUEUE_SIZE = 100;
    private static final Integer TIME_OUT_LIMIT_SIZE = 100;
    private static final Integer BATCH_SIZE = 50;
    private static final Integer TIMEOUT_DURATION = 15;
    private static final Integer RETRY_COUNT = 10;

    public static final BlockingQueue<TextTaskCallbackDTO> TEXT_TASK_CALLBACK_QUEUE = new ArrayBlockingQueue<>(QUEUE_SIZE);
    public static final BlockingQueue<SpeechTaskCallbackDTO> SPEECH_TASK_CALLBACK_QUEUE = new ArrayBlockingQueue<>(QUEUE_SIZE);
    private final MD5 md5 = MD5.create();
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(2,2,60,TimeUnit.SECONDS,new ArrayBlockingQueue<>(1));


    @Scheduled(fixedDelay = 1000 * 20)
    public void scanTask() {
        List<CoreTextTaskI18n> textTaskList = coreTextTaskI18nService.query(Arrays.asList(TextTaskStatusEnums.FAIL, TextTaskStatusEnums.PENDING), LIMIT_SIZE, RETRY_COUNT);
        batchSendTextTask(textTaskList);
        List<CoreSpeechTaskI18n> speechTaskList = coreSpeechTaskI18nService.query(Arrays.asList(SpeechTaskStatusEnums.FAIL, SpeechTaskStatusEnums.PENDING), LIMIT_SIZE, RETRY_COUNT);
        batchSendSpeechTask(speechTaskList);
        List<CoreTextTaskI18n> timeoutTextTaskList = coreTextTaskI18nService.queryStatusTimeoutTask(Collections.singletonList(RUNNING), TIMEOUT_DURATION, TIME_OUT_LIMIT_SIZE, RETRY_COUNT);
        timeoutTextTaskList.removeAll(textTaskList);
        batchSendTextTask(timeoutTextTaskList);
        List<CoreSpeechTaskI18n> timeoutSpeechTaskList = coreSpeechTaskI18nService.queryStatusTimeoutTask(Collections.singletonList(SpeechTaskStatusEnums.RUNNING), TIMEOUT_DURATION, TIME_OUT_LIMIT_SIZE, RETRY_COUNT);
        timeoutSpeechTaskList.removeAll(speechTaskList);
        batchSendSpeechTask(timeoutSpeechTaskList);
    }



    @org.springframework.context.event.EventListener(ContextRefreshedEvent.class)
    public void initExecutor() {
        executor.execute(this::handleTextTaskCallback);
        executor.execute(this::handleSpeechTaskCallback);
    }

    public void handleTextTaskCallback() {
        List<CoreTextTaskI18n> taskI18nList = new ArrayList<>(100);
        while (true) {
            try {
                TextTaskCallbackDTO callbackDTO = TEXT_TASK_CALLBACK_QUEUE.poll(1, TimeUnit.SECONDS);
                if (null == callbackDTO) {
                    if (CollUtil.isNotEmpty(taskI18nList)) {
                        coreTextTaskI18nService.batchUpdate(taskI18nList);
                        taskI18nList.clear();
                    }
                    continue;
                }
                if(StrUtil.isBlank(callbackDTO.getTranslationText())) {
                    log.error("text task callback: translationText is empty,callbackDTO:{}", callbackDTO);
                    continue;
                }
                if (taskI18nList.size() >= QUEUE_SIZE) {
                    coreTextTaskI18nService.batchUpdate(taskI18nList);
                    taskI18nList.clear();
                }
                CoreTextTaskI18n taskI18n = new CoreTextTaskI18n();
                String translationText = callbackDTO.getTranslationText();
                taskI18n.setTranslationText(translationText)
                        .setTranslationTextMd5(md5.digestHex(translationText))
                        .setStatus(TextTaskStatusEnums.SUCCESS)
                        .setCheckStatus(false)
                        .setId(callbackDTO.getI18nData().getId());
                taskI18nList.add(taskI18n);
            } catch (Exception e) {
                log.error("poll text task callback dto failed", e);
            }
        }
    }


    public void handleSpeechTaskCallback() {
        while (true) {
            try {
                SpeechTaskCallbackDTO callbackDTO = SPEECH_TASK_CALLBACK_QUEUE.take();
                coreSpeechTaskI18nService.callback(callbackDTO);
            } catch (Exception e) {
                log.error("poll speech task callback dto failed", e);
            }
        }
    }

    private void batchSendTextTask(List<CoreTextTaskI18n> taskList) {
        List<Integer> failedTaskIdList = new ArrayList<>(taskList.size());
        List<Integer> taskIdList = null;
        try {
            taskIdList = taskList.stream().map(CoreTextTaskI18n::getId).collect(Collectors.toList());
            List<List<CoreTextTaskI18n>> batcheTaskList = ListUtil.partition(taskList, BATCH_SIZE);
            for (List<CoreTextTaskI18n> taskI18nList : batcheTaskList) {
                List<Integer> updateTaskIdList = new ArrayList<>(taskI18nList.size());
                for (CoreTextTaskI18n taskI18n : taskI18nList) {
                    boolean success = sendTextTask(taskI18n);
                    if (!success) {
                        failedTaskIdList.add(taskI18n.getId());
                        if(CollUtil.isNotEmpty(updateTaskIdList)){
                            coreTextTaskI18nService.changeStatus(updateTaskIdList, RUNNING, false);
                            updateTaskIdList.clear();
                        }
                        Thread.sleep(SLEEP_DURATION);
                    }
                    updateTaskIdList.add(taskI18n.getId());
                }
                if(CollUtil.isNotEmpty(updateTaskIdList)){
                    coreTextTaskI18nService.changeStatus(updateTaskIdList, RUNNING, false);
                }
            }
        } catch (Exception e) {
            log.error("send text task error", e);
        }
        coreTextTaskI18nService.addRetryCount(taskIdList);
        coreTextTaskI18nService.changeStatus(failedTaskIdList, TextTaskStatusEnums.FAIL, false);
    }


    private boolean sendTextTask(CoreTextTaskI18n task) {
        TextTaskSendDTO textTaskDTO = new TextTaskSendDTO();
        textTaskDTO.setSourceLanguage(LanguageEnums.EN.getName())
                .setText(task.getText())
                .setLanguage(task.getLanguage().getName())
                .setWebhookUrl(coreI18nConfig.getCallbackTextTaskUrl());
        TextI18nDataDTO textI18nDataDTO = new TextI18nDataDTO();
        textI18nDataDTO.setId(task.getId());
        textTaskDTO.setI18nData(textI18nDataDTO);
        // 构建请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONUtil.toJsonStr(textTaskDTO));
        // 构建请求
        Request request = new Request.Builder()
                .url(coreI18nConfig.getSendTextTaskUrl())
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.warn("send task failed, code:{}, message:{},body:{}", response.code(), response.message(), JSONUtil.toJsonStr(textTaskDTO));
                return false;
            }
            ResponseBody resBody = response.body();
            if(null == resBody){
                log.error("response body is null,task:{}", task);
                return false;
            }
            String bodyString = resBody.string();
            ResponseResult responseResult = JSON.parseObject(bodyString, ResponseResult.class);
            int code = responseResult.getCode();
            if (ResponseCode.SUCCESS.getCode() != code) {
                return false;
            }
        } catch (Exception e) {
            log.error("send request to send task failed,task:{} ", task, e);
            return false;
        }
        return true;
    }


    private void batchSendSpeechTask(List<CoreSpeechTaskI18n> taskList) {
        try {
            List<List<CoreSpeechTaskI18n>> batcheTaskList = ListUtil.partition(taskList, BATCH_SIZE);
            for (List<CoreSpeechTaskI18n> taskI18nList : batcheTaskList) {
                for (CoreSpeechTaskI18n taskI18n : taskI18nList) {
                    if (!sendSpeechTask(taskI18n)) {
                        coreSpeechTaskI18nService.updateFailed(taskI18n.getId(), taskI18n.getRetryCount() + 1);
                        Thread.sleep(SLEEP_DURATION);
                    }
                }
            }
        } catch (Exception e) {
            log.error("send text task error", e);
        }
    }

    private boolean sendSpeechTask(CoreSpeechTaskI18n task) {
        SpeechTaskSendDTO taskSendDTO = new SpeechTaskSendDTO();
        taskSendDTO.setText(task.getTranslationText())
                .setName(task.getCoreVoiceTemplateI18nName())
                .setLanguage(task.getLanguage().getName())
                .setWebhookUrl(coreI18nConfig.getCallbackSpeechTaskUrl());
        Integer duration = task.getDuration();
        if(null != duration){
            BigDecimal maxDuration = NumberUtil.round((duration + LmsConstant.MAX_DURATION_OFFSET) / 1000.0, 1, RoundingMode.DOWN);
            taskSendDTO.setMaxDuration(maxDuration);
        }
        SpeechI18nDataDTO dataDTO = new SpeechI18nDataDTO();
        dataDTO.setId(task.getId())
                .setTranslationMd5(task.getTranslationTextMd5());
        taskSendDTO.setI18nData(dataDTO);
        // 构建请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONUtil.toJsonStr(taskSendDTO));
        // 构建请求
        Request request = new Request.Builder()
                .url(coreI18nConfig.getSendSpeechTaskUrl())
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("send task failed, code:{}, message:{},body:{}", response.code(), response.message(), JSONUtil.toJsonStr(taskSendDTO));
                return false;
            }
            ResponseBody resBody = response.body();
            if(null == resBody){
                log.error("response body is null,task:{}", task);
                return false;
            }
            String bodyString = resBody.string();
            ResponseResult responseResult = JSONUtil.toBean(bodyString, ResponseResult.class);
            int code = responseResult.getCode();
            if (ResponseCode.SUCCESS.getCode() != code) {
                return false;
            }
        } catch (Exception e) {
            log.error("send request to send task failed,task:{} ", task, e);
            return false;
        }
        coreSpeechTaskI18nService.changeStatus(task.getId(),SpeechTaskStatusEnums.RUNNING,false,task.getTranslationTextMd5(), task.getRetryCount() + 1);
        return true;
    }

}
