package com.laien.common.lms.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.domain.entity.CoreVoiceConfigTemplateI18n;
import com.laien.common.domain.entity.CoreVoiceTemplateI18n;
import com.laien.common.domain.request.CoreVoiceConfigI18nAddReq;
import com.laien.common.domain.request.CoreVoiceConfigI18nUpdateReq;
import com.laien.common.domain.request.CoreVoiceConfigTemplateI18nUpdateReq;
import com.laien.common.domain.response.CoreVoiceConfigI18nVO;
import com.laien.common.domain.response.CoreVoiceConfigRelationVO;
import com.laien.common.domain.response.CoreVoiceTemplateI18nListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;


/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface CoreVoiceConfigI18nMapStruct {

    CoreVoiceConfigI18n toEntity(CoreVoiceConfigI18nAddReq addReq);
    CoreVoiceConfigI18n toEntity(CoreVoiceConfigI18nUpdateReq addReq);
    List<CoreVoiceTemplateI18nListVO> toTemplateVOList(List<CoreVoiceTemplateI18n> list);
    List<CoreVoiceConfigI18nVO> toPageList(List<CoreVoiceConfigI18n> records);
    CoreVoiceConfigI18nVO toVO(CoreVoiceConfigI18n config);
    CoreVoiceConfigTemplateI18n toRelationEntity(CoreVoiceConfigTemplateI18nUpdateReq req);
    @Mapping(source = "relation.id", target = "id")
    @Mapping(source = "template.gender", target= "gender")
    CoreVoiceConfigRelationVO toRelationVO(CoreVoiceConfigTemplateI18n relation, CoreVoiceTemplateI18n template);
}
