package com.laien.common.lms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.laien.common.core.dto.SpeechI18nDataDTO;
import com.laien.common.core.dto.SpeechTaskCallbackDTO;
import com.laien.common.core.dto.SpeechTaskSendDTO;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.entity.CoreVoiceTemplateI18n;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.request.CoreVoiceConfigI18nAddReq;
import com.laien.common.domain.request.CoreVoiceConfigI18nPageReq;
import com.laien.common.domain.request.CoreVoiceConfigI18nUpdateReq;
import com.laien.common.domain.request.CoreVoiceConfigTemplateI18nUpdateReq;
import com.laien.common.domain.response.CoreVoiceConfigI18nVO;
import com.laien.common.domain.response.CoreVoiceTemplateI18nListVO;
import com.laien.common.domain.response.LanguageEnumVO;
import com.laien.common.frame.controller.ResponseController;
import com.laien.common.frame.request.IdListReq;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.response.setting.ResponseCode;
import com.laien.common.frame.response.setting.ResponseResult;
import com.laien.common.lms.config.CoreI18nConfig;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import com.laien.common.lms.service.ICoreVoiceTemplateI18nService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * CoreVoiceConfigI18n 控制器（类主食）
 * </p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Api(tags = "语音配置国际化管理")
@RestController
@RequestMapping("/lms/ttsConfig")
@RequiredArgsConstructor
@Slf4j
public class CoreVoiceConfigI18nController extends ResponseController {

    private final ICoreVoiceConfigI18nService service;
    private final ICoreVoiceTemplateI18nService templateI18nService;
    private final CoreI18nConfig coreI18nConfig;
    private final OkHttpClient client;


    @ApiOperation(value = "分页查询coreVoiceConfig",tags = "lms")
    @GetMapping("/page")
    public ResponseResult<PageRes<CoreVoiceConfigI18nVO>> page(CoreVoiceConfigI18nPageReq req) {
        return succ(service.pageConfig(req));
    }


    @ApiOperation(value = "coreVoiceConfig详情")
    @GetMapping("/detail/{id}")
    public ResponseResult<CoreVoiceConfigI18nVO> detail(@PathVariable Integer id) {
        return succ(service.findDetailById(id));
    }

    /**
     * 新增语音配置国际化
     * @param addReq 新增请求体
     * @return 操作结果
     */
    @ApiOperation(value = "新增语音配置国际化",tags = "lms")
    @PostMapping("/add")
    public ResponseResult<Void> add(@RequestBody CoreVoiceConfigI18nAddReq addReq) {
        service.addConfig(addReq);
        return succ();
    }

    /**
     * 修改语音配置国际化
     * @param updateReq 修改请求体
     * @return 操作结果
     */
    @ApiOperation(value = "修改语音配置国际化",tags = "lms")
    @PostMapping("/update")
    public ResponseResult<Void> update(@RequestBody CoreVoiceConfigI18nUpdateReq updateReq) {
        service.updateConfig(updateReq);
        return succ();
    }

    /**
     * 修改配置音色
     * @param updateReq 修改请求体
     * @return 操作结果
     */
    @ApiOperation(value = "修改配置音色",tags = "lms")
    @PostMapping("/updateVoice")
    public ResponseResult<Void> updateVoice(@RequestBody CoreVoiceConfigTemplateI18nUpdateReq updateReq) {
        service.updateRelation(updateReq);
        return succ();
    }

    /**
     * 批量启用语音配置国际化
     * @param req 需要启用的ID列表
     * @return 操作结果
     */
    @ApiOperation(value = "批量启用语音配置国际化",tags = "lms")
    @PostMapping("/enable")
    public ResponseResult<Void> enable(@RequestBody IdListReq req) {
        if (Objects.isNull(req) || CollUtil.isEmpty(req.getIdList())) {
            return fail("ID列表不能为空");
        }
        service.updateEnableByIds(req.getIdList());
        return succ();
    }

    /**
     * 批量禁用语音配置国际化
     * @param req 需要禁用的ID列表
     * @return 操作结果
     */
    @ApiOperation(value = "批量禁用语音配置国际化",tags = "lms")
    @PostMapping("/disable")
    public ResponseResult<Void> disable(@RequestBody IdListReq req) {
        if (Objects.isNull(req) || CollUtil.isEmpty(req.getIdList())) {
            return fail("ID列表不能为空");
        }
        service.updateDisableByIds(req.getIdList());
        return succ();
    }

    /**
     * 查询全部语音模板
     * @return 模板列表
     */
    @ApiOperation(value = "查询全部语音模板",tags = "lms")
    @GetMapping("/allTemplates")
    public ResponseResult<List<CoreVoiceTemplateI18nListVO>> getAllTemplates() {
        List<CoreVoiceTemplateI18nListVO> list = service.getAllTemplates();
        return succ(list);
    }

    /**
     * 查询全部支持的语种
     * @return 语种列表
     */
    @ApiOperation(value = "查询全部支持语种",tags = "lms")
    @GetMapping("/allSupportLanguages")
    public ResponseResult<List<LanguageEnumVO>> getAllSupportLanguages() {
        return succ(service.getAllSupportLanguages());
    }

    @ApiOperation(value = "查询全部支持语种",tags = "lms")
    @GetMapping("/insertAllTemplate")
    public void insertAllTemplate() {
        //读取/Users/<USER>/Downloads/voices_template.json文件内容
        String filePath = "/Users/<USER>/Downloads/voices_template.json";
        String content = FileUtil.readString(filePath, CharsetUtil.CHARSET_UTF_8);
        //将content转换为json对象
        JSONArray array = JSONUtil.parseArray(content);
        List<CoreVoiceTemplateI18n> list = templateI18nService.list();
        Set<String> strings = list.stream().map(CoreVoiceTemplateI18n::getName).collect(Collectors.toSet());
        List<CoreVoiceTemplateI18n> insert = new java.util.ArrayList<>();
        for (Object item : array) {
            JSONObject jsonObject = (JSONObject) item;
            String name = jsonObject.getStr("name");
            String Locale = jsonObject.getStr("Locale");
            String Gender = jsonObject.getStr("Gender");
            if (strings.contains(name)) {
                continue;
            }
            GenderEnums enums = EnumUtil.getBy(GenderEnums::name, Gender);
            switch (Locale) {
                case "zh-CN":
                    Locale = "zh";
                    break;
                case "zh-TW":
                    Locale = "zh-TW";
                    break;
                default:
                    Locale = Locale.split("-")[0];
                    break;
            }
            EnumUtil.getBy(LanguageEnums::getName, Locale);
            CoreVoiceTemplateI18n template = new CoreVoiceTemplateI18n();
            template.setLanguage(EnumUtil.getBy(LanguageEnums::getName, Locale));
            if (template.getLanguage() == null) {
                continue;
            }
            template.setGender(enums);
            template.setName(name);
            template.setAudioUrl("");
            insert.add(template);
        }
        log.info("insert:{}", insert.size());
        templateI18nService.saveBatch(insert);
    }


    @ApiOperation(value = "updateTemplateAudioUrl",tags = "lms")
    @GetMapping("/updateTemplateAudioUrl")
    public void updateTemplateAudioUrl(@RequestParam Integer size) {
        LambdaQueryWrapper<CoreVoiceTemplateI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.last("limit "+size);
        List<CoreVoiceTemplateI18n> list = templateI18nService.list(queryWrapper);
        String json = "{\n" +
                "  \"en\": \"Hi, I'm your Coach . Let's get this workout started!\",\n" +
                "  \"zh-TW\": \"嗨，我是你的教練。讓我們開始鍛鍊吧！\",\n" +
                "  \"zh\": \"嗨，我是你的教练。让我们开始锻炼吧！\",\n" +
                "  \"vi\": \"Chào, tôi là huấn luyện viên của bạn. Hãy bắt đầu buổi tập luyện nào!\",\n" +
                "  \"uk\": \"Привіт, я твій тренер. Давай почнемо тренування!\",\n" +
                "  \"tr\": \"Merhaba, ben senin antrenörünüm. Haydi antrenmana başlayalım!\",\n" +
                "  \"th\": \"สวัสดี ฉันคือโค้ชของคุณ มาเริ่มออกกำลังกายกันเถอะ!\",\n" +
                "  \"sv\": \"Hej, jag är din tränare. Låt oss börja träningen!\",\n" +
                "  \"sk\": \"Ahoj, som tvoj tréner. Poďme začať s tréningom!\",\n" +
                "  \"ru\": \"Привет, я твой тренер. Давай начнем тренировку!\",\n" +
                "  \"ro\": \"Salut, sunt antrenorul tău. Să începem antrenamentul!\",\n" +
                "  \"pt\": \"Olá, sou o seu treinador. Vamos começar este treino!\",\n" +
                "  \"pl\": \"Cześć, jestem twoim trenerem. Zaczynajmy trening!\",\n" +
                "  \"nl\": \"Hoi, ik ben je coach. Laten we beginnen met de training!\",\n" +
                "  \"nb\": \"Hei, jeg er treneren din. La oss starte treningen!\",\n" +
                "  \"ms\": \"Hai, saya jurulatih anda. Mari mulakan senaman ini!\",\n" +
                "  \"ko\": \"안녕하세요, 당신의 코치입니다. 운동을 시작해봅시다!\",\n" +
                "  \"ja\": \"こんにちは、あなたのコーチです。さあ、ワークアウトを始めましょう！\",\n" +
                "  \"it\": \"Ciao, sono il tuo allenatore. Iniziamo l'allenamento!\",\n" +
                "  \"id\": \"Hai, saya pelatih Anda. Mari mulai latihan ini!\",\n" +
                "  \"hu\": \"Szia, én vagyok az edződ. Kezdjük el az edzést!\",\n" +
                "  \"hr\": \"Bok, ja sam tvoj trener. Krenimo s treningom!\",\n" +
                "  \"hi\": \"नमस्ते, मैं आपका कोच हूँ। चलिए इस कसरत की शुरुआत करते हैं!\",\n" +
                "  \"he\": \"היי, אני המאמן שלך. בוא נתחיל את האימון!\",\n" +
                "  \"fr\": \"Salut, je suis ton coach. Commençons l'entraînement !\",\n" +
                "  \"fi\": \"Hei, olen valmentajasi. Aloitetaan harjoitus!\",\n" +
                "  \"es\": \"¡Hola! Soy tu entrenador. ¡Vamos a empezar este entrenamiento!\",\n" +
                "  \"el\": \"Γεια σου, είμαι ο προπονητής σου. Ας ξεκινήσουμε την προπόνηση!\",\n" +
                "  \"de\": \"Hallo, ich bin dein Coach. Lass uns mit dem Training beginnen!\",\n" +
                "  \"da\": \"Hej, jeg er din træner. Lad os komme i gang med træningen!\",\n" +
                "  \"cs\": \"Ahoj, jsem tvůj trenér. Začněme s tréninkem!\",\n" +
                "  \"ar\": \"مرحبًا، أنا مدربك. لنبدأ هذا التمرين!\"\n" +
                "}";
        //解析成map
        Map<String, String> map = JSONUtil.toBean(json, Map.class);
        for (CoreVoiceTemplateI18n coreVoiceTemplateI18n : list) {
            String language = coreVoiceTemplateI18n.getLanguage().getName();
            String name = coreVoiceTemplateI18n.getName();
            String string = map.get(language);
            if (StrUtil.isEmpty(string)) {
                continue;
            }
            //TODO 组装请求调用tts
            SpeechTaskSendDTO taskSendDTO = new SpeechTaskSendDTO();
            taskSendDTO.setText(string)
                    .setName(name)
                    .setLanguage(language)
                    .setWebhookUrl("http://laien.dev:8200/lms/ttsConfig/template/callback");
            SpeechI18nDataDTO dataDTO = new SpeechI18nDataDTO();
            dataDTO.setId(coreVoiceTemplateI18n.getId());
            taskSendDTO.setI18nData(dataDTO);
            // 构建请求体
            okhttp3.RequestBody body = okhttp3.RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONUtil.toJsonStr(taskSendDTO));
            // 构建请求
            Request request = new Request.Builder()
                    .url(coreI18nConfig.getSendSpeechTaskUrl())
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            // 发送请求并获取响应
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("send task failed, code:{}, message:{},task:{}", response.code(), response.message(), taskSendDTO);
                    continue;
                }
                okhttp3.ResponseBody resBody = response.body();
                if(null == resBody){
                    log.error("response body is null,task:{}", taskSendDTO);
                    continue;
                }
                String bodyString = resBody.string();
                ResponseResult responseResult = JSONUtil.toBean(bodyString, ResponseResult.class);
                int code = responseResult.getCode();
                if (ResponseCode.SUCCESS.getCode() != code) {
                    continue;
                }
                //限制调用频率
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("send request to send task failed,task:{} ", taskSendDTO, e);
                continue;
            }
        }
    }


    @PostMapping("/template/callback")
    public ResponseResult<Void> speechCallback(@RequestBody SpeechTaskCallbackDTO callbackDTO){
        String audioUrl = StrUtil.replaceFirst(URLUtil.getPath(callbackDTO.getAudioUrl()),"/","");
        Integer id = callbackDTO.getI18nData().getId();
        CoreVoiceTemplateI18n byId = templateI18nService.getById(id);
        if (byId != null) {
            byId.setAudioUrl(audioUrl);
            templateI18nService.updateById(byId);
        }
        return ResponseResult.succ();
    }

}
