package com.laien.common.lms.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.common.domain.entity.CoreTextTaskI18n;
import com.laien.common.domain.request.CoreTaskI18nUpdateReq;
import com.laien.common.domain.request.CoreTextTaskI18nAddReq;
import com.laien.common.domain.response.CoreTextTaskI18nDetailVO;
import com.laien.common.domain.response.CoreTextTaskI18nPageVO;
import com.laien.common.domain.response.CoreTextTaskI18nVO;
import org.mapstruct.Mapper;


/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface CoreTextTaskI18nMapStruct {

    CoreTextTaskI18nVO toVO(CoreTextTaskI18n coreTextTaskI18n);

    CoreTextTaskI18n toEntity(CoreTaskI18nUpdateReq req);

    CoreTextTaskI18n toEntity(CoreTextTaskI18nAddReq.SubTaskReq subTask, CoreTextTaskI18nAddReq task);

    CoreTextTaskI18nDetailVO toDetailVO(CoreTextTaskI18n baseTask);

    CoreTextTaskI18nPageVO toPageVO(CoreTextTaskI18n baseTask);
}
