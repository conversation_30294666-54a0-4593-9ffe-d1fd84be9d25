package com.laien.common.lms.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.request.CoreTaskI18nUpdateReq;
import com.laien.common.domain.response.CoreSpeechTaskI18nPageVO;
import com.laien.common.domain.response.CoreSpeechTaskI18nVO;
import org.mapstruct.Mapper;


/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface CoreSpeechTaskI18nMapStruct {

    CoreSpeechTaskI18nVO toVO(CoreSpeechTaskI18n coreSpeechTaskI18n);

    CoreSpeechTaskI18n toEntity(CoreTaskI18nUpdateReq req);

    CoreSpeechTaskI18nPageVO toPageVO(CoreSpeechTaskI18n baseTask);
}
