package com.laien.common.lms.controller;

import com.laien.common.frame.controller.ResponseController;
import com.laien.common.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/05
 */
@Slf4j
@Api(tags = "lms")
@RestController
@RequestMapping("/lms")
@RequiredArgsConstructor
public class LmsTestController extends ResponseController {

    @ApiOperation(value = "查询最新的一条翻译结果")
    @GetMapping("/test")
    public ResponseResult<String> test() {
        return succ("hello world");
    }

}
