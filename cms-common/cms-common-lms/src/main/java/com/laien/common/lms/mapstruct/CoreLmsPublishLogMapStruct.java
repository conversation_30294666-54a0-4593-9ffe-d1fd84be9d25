package com.laien.common.lms.mapstruct;

import cn.hutool.core.util.ObjUtil;
import com.laien.common.domain.entity.CoreLmsPublishLog;
import com.laien.common.domain.response.CoreLmsPublishLogPageVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Mapper(componentModel = "spring", imports = {ObjUtil.class})
public interface CoreLmsPublishLogMapStruct {

    List<CoreLmsPublishLogPageVO> toVOList(List<CoreLmsPublishLog> records);
}
