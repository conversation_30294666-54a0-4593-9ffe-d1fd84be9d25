package com.laien.common.lms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.response.LanguageEnumVO;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.utils.BizExceptionUtil;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.domain.entity.CoreVoiceConfigTemplateI18n;
import com.laien.common.domain.entity.CoreVoiceTemplateI18n;
import com.laien.common.domain.enums.GenderEnums;
import com.laien.common.domain.enums.StatusEnums;
import com.laien.common.lms.mapper.CoreVoiceConfigI18nMapper;
import com.laien.common.lms.mapstruct.CoreVoiceConfigI18nMapStruct;
import com.laien.common.domain.request.CoreVoiceConfigI18nAddReq;
import com.laien.common.domain.request.CoreVoiceConfigI18nPageReq;
import com.laien.common.domain.request.CoreVoiceConfigI18nUpdateReq;
import com.laien.common.domain.request.CoreVoiceConfigTemplateI18nUpdateReq;
import com.laien.common.domain.response.CoreVoiceConfigI18nVO;
import com.laien.common.domain.response.CoreVoiceConfigRelationVO;
import com.laien.common.domain.response.CoreVoiceTemplateI18nListVO;
import com.laien.common.lms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreVoiceConfigI18nServiceImpl extends ServiceImpl<CoreVoiceConfigI18nMapper, CoreVoiceConfigI18n> implements ICoreVoiceConfigI18nService {

    private final CoreVoiceConfigI18nMapStruct mapStruct;
    private final ICoreVoiceTemplateI18nService templateI18nService;
    private final ICoreVoiceConfigTemplateI18nService templateI18nRelationService;
    private final ICoreSpeechTaskI18nService speechTaskI18nService;
    private final ICoreTextTaskI18nService textTaskI18nService;


    @Override
    public PageRes<CoreVoiceConfigI18nVO> pageConfig(CoreVoiceConfigI18nPageReq req) {
        //query config
        LambdaQueryWrapper<CoreVoiceConfigI18n> wrapper = getConfigWrapper(req);
        wrapper.orderByDesc(CoreVoiceConfigI18n::getId);
        IPage<CoreVoiceConfigI18n> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), wrapper);
        List<CoreVoiceConfigI18n> list = page.getRecords();
        if (CollUtil.isEmpty(list)) {
            return new PageRes<>(req.getPageNum(), req.getPageSize(), 0L, 0L, Collections.emptyList());
        }
        RelationTemplateResult result = getRelationTemplateResult(list);
        //assemble VO
        List<CoreVoiceConfigI18nVO> voList = mapStruct.toPageList(list);
        assembleVOs(voList, result);
        return new PageRes<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), voList);
    }


    @Override
    public List<CoreVoiceConfigI18n> listConfigByIds(Collection<Integer> ids) {
        return this.list(getConfigWrapper(new CoreVoiceConfigI18nPageReq().setIds(ids)));
    }

    @Override
    public List<CoreVoiceConfigI18n> listConfigByNames(Collection<String> names) {
        return this.list(getConfigWrapper(new CoreVoiceConfigI18nPageReq().setNames(names)));
    }

    private void assembleVOs(List<CoreVoiceConfigI18nVO> voList, RelationTemplateResult result) {
        voList.forEach(item -> {
            List<CoreVoiceConfigTemplateI18n> relations = result.relationMap.get(item.getId());
            item.setRelationList(CollUtil.newArrayList());
            item.setLanguageList(CollUtil.newArrayList());
            if (CollUtil.isNotEmpty(relations)) {
                List<CoreVoiceConfigRelationVO> templateVOList = relations.stream().map(relation -> {
                    CoreVoiceTemplateI18n template = result.templateMap.get(relation.getCoreVoiceTemplateI18nId());
                    return mapStruct.toRelationVO(relation,template);
                }).collect(Collectors.toList());
                item.getRelationList().addAll(templateVOList);
                item.getLanguageList().addAll(templateVOList.stream().map(CoreVoiceConfigRelationVO::getLanguage).collect(Collectors.toList()));
            }
        });
    }

    private RelationTemplateResult getRelationTemplateResult(List<CoreVoiceConfigI18n> list) {
        //query relation
        LambdaQueryWrapper<CoreVoiceConfigTemplateI18n> templateWrapper = new LambdaQueryWrapper<>();
        templateWrapper.in(CoreVoiceConfigTemplateI18n::getCoreVoiceConfigI18nId, list.stream().map(CoreVoiceConfigI18n::getId).collect(Collectors.toList()));
        List<CoreVoiceConfigTemplateI18n> templateRelations = templateI18nRelationService.list(templateWrapper);
        //group by config id
        Map<Integer, List<CoreVoiceConfigTemplateI18n>> relationMap = templateRelations.stream().collect(Collectors.groupingBy(CoreVoiceConfigTemplateI18n::getCoreVoiceConfigI18nId));
        //query template
        Set<Integer> templateIdSet = templateRelations.stream().map(CoreVoiceConfigTemplateI18n::getCoreVoiceTemplateI18nId).collect(Collectors.toSet());
        Collection<CoreVoiceTemplateI18n> templateList = templateI18nService.listByIds(templateIdSet);
        //group by template id
        Map<Integer, CoreVoiceTemplateI18n> templateMap = templateList.stream().collect(Collectors.toMap(CoreVoiceTemplateI18n::getId, Function.identity()));
        return new RelationTemplateResult(relationMap, templateMap);
    }

    private static class RelationTemplateResult {
        public final Map<Integer, List<CoreVoiceConfigTemplateI18n>> relationMap;
        public final Map<Integer, CoreVoiceTemplateI18n> templateMap;

        public RelationTemplateResult(Map<Integer, List<CoreVoiceConfigTemplateI18n>> relationMap, Map<Integer, CoreVoiceTemplateI18n> templateMap) {
            this.relationMap = relationMap;
            this.templateMap = templateMap;
        }
    }

    private LambdaQueryWrapper<CoreVoiceConfigI18n> getConfigWrapper(CoreVoiceConfigI18nPageReq req) {
        LambdaQueryWrapper<CoreVoiceConfigI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjUtil.isNotEmpty(req.getId()), CoreVoiceConfigI18n::getId, req.getId());
        wrapper.eq(ObjUtil.isNotEmpty(req.getGender()), CoreVoiceConfigI18n::getGender, req.getGender());
        wrapper.eq(ObjUtil.isNotEmpty(req.getStatus()), CoreVoiceConfigI18n::getStatus, req.getStatus());
        wrapper.like(ObjUtil.isNotEmpty(req.getName()), CoreVoiceConfigI18n::getName, req.getName());
        wrapper.in(CollUtil.isNotEmpty(req.getIds()), CoreVoiceConfigI18n::getId, req.getIds());
        wrapper.in(CollUtil.isNotEmpty(req.getNames()),  CoreVoiceConfigI18n::getName, req.getNames());
        return wrapper;
    }

    @Override
    public CoreVoiceConfigI18nVO findDetailById(Integer id) {
        CoreVoiceConfigI18nPageReq req = new CoreVoiceConfigI18nPageReq();
        req.setId(id);
        PageRes<CoreVoiceConfigI18nVO> page = this.pageConfig(req);
        BizExceptionUtil.throwIf(CollUtil.isEmpty(page.getList()), "data not found, id:{}", id);
        return page.getList().get(0);
    }

    @Transactional
    @Override
    public void addConfig(CoreVoiceConfigI18nAddReq req) {
        check(req, null);
        CoreVoiceConfigI18n entity = mapStruct.toEntity(req);
        this.save(entity);
        this.saveRelation(entity.getId(), req);
    }


    @Override
    public void updateRelation(CoreVoiceConfigTemplateI18nUpdateReq req) {
        templateI18nRelationService.updateById(mapStruct.toRelationEntity(req));
    }

    @Transactional
    @Override
    public void updateConfig(CoreVoiceConfigI18nUpdateReq req) {
        check(req, req.getId());
        CoreVoiceConfigI18n entity = mapStruct.toEntity(req);
        this.updateById(entity);
        this.updateRelation(entity.getId(), req);
    }

    private void updateRelation(Integer id, CoreVoiceConfigI18nUpdateReq req) {
        //get exist relation and template
        LambdaQueryWrapper<CoreVoiceConfigTemplateI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreVoiceConfigTemplateI18n::getCoreVoiceConfigI18nId, id);
        List<CoreVoiceConfigTemplateI18n> existRelations = templateI18nRelationService.list(wrapper);
        List<Integer> templateIds = existRelations.stream().map(CoreVoiceConfigTemplateI18n::getCoreVoiceTemplateI18nId).collect(Collectors.toList());
        Collection<CoreVoiceTemplateI18n> existTemplates = templateI18nService.listByIds(templateIds);
        //get selected language and gender's template
        List<LanguageEnums> languages = req.getLanguageList();
        GenderEnums gender = req.getGender();
        List<CoreVoiceTemplateI18n> defaultTemplates = this.getDefaultTemplates(gender, languages);
        BizExceptionUtil.throwIf(CollUtil.isEmpty(defaultTemplates), "template not found");
        //group by language using lambda
        Map<LanguageEnums, CoreVoiceTemplateI18n> existTemplatesByLanguage = existTemplates.stream().collect(
                Collectors.toMap(CoreVoiceTemplateI18n::getLanguage, Function.identity(),
                        (oldValue, newValue) ->  newValue));
        Map<LanguageEnums, CoreVoiceTemplateI18n> defaultTemplatesByLanguage = defaultTemplates.stream().collect(
                Collectors.toMap(CoreVoiceTemplateI18n::getLanguage, Function.identity(),
                        (oldValue, newValue) ->  newValue));
        //compare and update
        List<CoreVoiceConfigTemplateI18n> addRelation = CollUtil.newArrayList();
        for (LanguageEnums language : languages) {
            CoreVoiceTemplateI18n exist = existTemplatesByLanguage.remove(language);
            boolean defaultFlag = defaultTemplatesByLanguage.containsKey(language);
            if (exist!=null && defaultFlag) continue;
            if (exist==null && defaultFlag) {
                //add
                CoreVoiceTemplateI18n template = defaultTemplatesByLanguage.get(language);
                CoreVoiceConfigTemplateI18n relation = new CoreVoiceConfigTemplateI18n();
                relation.setCoreVoiceConfigI18nId(id);
                relation.setCoreVoiceTemplateI18nId(template.getId());
                relation.setGender(gender);
                addRelation.add(relation);
            }
        }
        //add
        if (CollUtil.isNotEmpty(addRelation)) {
            templateI18nRelationService.saveBatch(addRelation);
        }
    }

    private void saveRelation(Integer id,CoreVoiceConfigI18nAddReq req) {
        //get template
        List<LanguageEnums> languages = req.getLanguageList();
        GenderEnums gender = req.getGender();
        List<CoreVoiceTemplateI18n> templates = getDefaultTemplates(gender, languages);
        BizExceptionUtil.throwIf(CollUtil.isEmpty(templates), "template not found");
        //group by language using lambda
        Map<LanguageEnums, CoreVoiceTemplateI18n> collect = templates.stream().collect(
                Collectors.toMap(CoreVoiceTemplateI18n::getLanguage, Function.identity(),
                        (oldValue, newValue) ->  newValue));
        //assemble data
        List<CoreVoiceConfigTemplateI18n> relations = CollUtil.newArrayList();
        for (LanguageEnums language : languages) {
            CoreVoiceConfigTemplateI18n relation = new CoreVoiceConfigTemplateI18n();
            relation.setCoreVoiceConfigI18nId(id);
            relation.setCoreVoiceTemplateI18nId(collect.get(language).getId());
            relation.setGender(gender);
            relations.add(relation);
        }
        templateI18nRelationService.saveBatch(relations);
    }

    private List<CoreVoiceTemplateI18n> getDefaultTemplates(GenderEnums gender, List<LanguageEnums> languages) {
        LambdaQueryWrapper<CoreVoiceTemplateI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreVoiceTemplateI18n::getGender, gender);
        wrapper.in(CoreVoiceTemplateI18n::getLanguage, languages);
        wrapper.eq(CoreVoiceTemplateI18n::isDefaultVoice, true);
        return templateI18nService.list(wrapper);
    }

    private void check(CoreVoiceConfigI18nAddReq addReq, Integer id) {
        BizExceptionUtil.throwIf(CollUtil.isEmpty(addReq.getLanguageList()), "language can not be empty");
        LambdaQueryWrapper<CoreVoiceConfigI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreVoiceConfigI18n::getName, addReq.getName());
        wrapper.eq(CoreVoiceConfigI18n::getGender, addReq.getGender());
        wrapper.ne(ObjUtil.isNotNull(id), CoreVoiceConfigI18n::getId, id);
        List<CoreVoiceConfigI18n> list = this.list(wrapper);
        BizExceptionUtil.throwIf(CollUtil.isNotEmpty(list), "name:{} + gender:{} already exist", addReq.getName(), addReq.getGender().name());
    }

    @Transactional
    @Override
    public void updateEnableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<CoreVoiceConfigI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CoreVoiceConfigI18n::getStatus, StatusEnums.ENABLE)
                .in(CoreVoiceConfigI18n::getStatus, StatusEnums.DRAFT, StatusEnums.DISABLE)
                .in(CoreVoiceConfigI18n::getId, idList);
        this.update(new CoreVoiceConfigI18n(), wrapper);
        processRelatedSpeechTasks(idList);
    }


    /**
     * 更新启用的语音配置后，自动同步已存在的语音任务模板
     */
    private void processRelatedSpeechTasks(List<Integer> idList) {
        // ① 查配置（带 Relation + Template）
        List<CoreVoiceConfigI18n> configs = this.listConfigByIds(idList);
        if (CollUtil.isEmpty(configs)) return;
        RelationTemplateResult relationData = getRelationTemplateResult(configs);
        //② 预构建：期望模板 (configId + "_" + language) → template
        Map<String, CoreVoiceTemplateI18n> desiredTemplateMap = relationData.relationMap
                .values()
                .stream()
                .flatMap(Collection::stream)                  // 展平成单条关系
                .map(r -> {                                   // 关系 ➜ (key, template)
                    CoreVoiceTemplateI18n tpl = relationData.templateMap.get(r.getCoreVoiceTemplateI18nId());
                    String key = r.getCoreVoiceConfigI18nId() + "_" + tpl.getLanguage().getName();
                    return new AbstractMap.SimpleEntry<>(key, tpl);
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (a, b) -> b)); // key 冲突取后者

        Set<LanguageEnums> allLanguages = desiredTemplateMap.values().stream()
                .map(CoreVoiceTemplateI18n::getLanguage)
                .collect(Collectors.toSet());
        //③ 查出可能受影响的语音任务
        LambdaQueryWrapper<CoreSpeechTaskI18n> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.in(CoreSpeechTaskI18n::getCoreVoiceConfigI18nId, idList);
        taskWrapper.in(CoreSpeechTaskI18n::getLanguage, allLanguages);

        List<CoreSpeechTaskI18n> tasks = speechTaskI18nService.list(taskWrapper);
        if (CollUtil.isEmpty(tasks)) return;
        //④ 比对 & 收集需要更新的任务
        List<CoreSpeechTaskI18n> toUpdate = tasks.stream()
                .filter(task -> {
                    CoreVoiceTemplateI18n desired =
                            desiredTemplateMap.get(this.getSpeechTaskKey(task));
                    return desired != null && !Objects.equals(task.getCoreVoiceTemplateI18nId(), desired.getId());
                })
                .peek(task -> { // 就地修改任务实体
                    CoreVoiceTemplateI18n desired =
                            desiredTemplateMap.get(this.getSpeechTaskKey(task));
                    task.setCoreVoiceTemplateI18nId(desired.getId());
                    task.setCoreVoiceTemplateI18nName(desired.getName());
                    speechTaskI18nService.resetSpeechTaskField(task,false);
                })
                .collect(Collectors.toList());
        //⑤ 批量更新
        if (CollUtil.isNotEmpty(toUpdate)) {
            speechTaskI18nService.updateBatchById(toUpdate);
        }
    }

    private String getSpeechTaskKey(CoreSpeechTaskI18n speechTask) {
        return speechTask.getCoreVoiceConfigI18nId() + "_" + speechTask.getLanguage().getName();
    }

    @Override
    public void updateDisableByIds(List<Integer> idList) {
        LambdaUpdateWrapper<CoreVoiceConfigI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CoreVoiceConfigI18n::getStatus, StatusEnums.DISABLE)
                .eq(CoreVoiceConfigI18n::getStatus, StatusEnums.ENABLE)
                .in(CoreVoiceConfigI18n::getId, idList);
        this.update(new CoreVoiceConfigI18n(), wrapper);
    }


    @Override
    public List<CoreVoiceTemplateI18nListVO> getAllTemplates() {
        LambdaQueryWrapper<CoreVoiceTemplateI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CoreVoiceTemplateI18n::getStatus, StatusEnums.ENABLE);
        return mapStruct.toTemplateVOList(templateI18nService.list(wrapper));
    }

    @Override
    public List<LanguageEnumVO> getAllSupportLanguages() {
        return Arrays.stream(LanguageEnums.values())
                .filter(e -> !ObjUtil.equal(e, LanguageEnums.EN)) // 如保留
                .map(e -> new LanguageEnumVO(
                        e.getName(),            // 实际 code 字符串，如 "zh"
                        e.name(),               // 显示 label，可映射为中文名等
                        e.isSelectedByDefault()
                ))
                .collect(Collectors.toList());
    }
}
