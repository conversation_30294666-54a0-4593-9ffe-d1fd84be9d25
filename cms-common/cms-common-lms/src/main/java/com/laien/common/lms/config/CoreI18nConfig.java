package com.laien.common.lms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/6/11
 */
@Data
@Configuration
@ConfigurationProperties("laien.core.i18n")
public class CoreI18nConfig {

    private String sendTextTaskUrl;
    private String callbackTextTaskUrl;
    private String sendSpeechTaskUrl;
    private String callbackSpeechTaskUrl;
    private String i18nSlackWebhookUrl;
    private boolean enableSlackMsg;
}
