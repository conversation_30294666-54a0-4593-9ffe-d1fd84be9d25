package com.laien.common.lms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreLmsConstantText;
import com.laien.common.lms.mapper.CoreLmsConstantTextMapper;
import com.laien.common.lms.service.ICoreLmsConstantTextService;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 文本任务多语言表 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreLmsConstantTextServiceImpl extends ServiceImpl<CoreLmsConstantTextMapper, CoreLmsConstantText>
        implements ICoreLmsConstantTextService {


}
