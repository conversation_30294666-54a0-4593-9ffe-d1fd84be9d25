package com.laien.common.lms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.domain.entity.CoreBusinessTaskRelationI18n;
import com.laien.common.domain.enums.UpdateFlagEnums;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ICoreBusinessTaskRelationI18nService extends IService<CoreBusinessTaskRelationI18n> {

    List<CoreBusinessTaskRelationI18n> queryByDataIds(Collection<Integer> businessDataIds);

    /**
     * 根据coreTextTaskI18nId修改updateFlag
     */
    void update(Integer coreTextTaskI18nId, UpdateFlagEnums updateFlag);

    void updateTextFlag(Set<Integer> coreTextTaskI18nIdSet);

    List<CoreBusinessTaskRelationI18n> query(Collection<UpdateFlagEnums> updateFlags);

    void batchUpdateByIds(Collection<Integer> ids, UpdateFlagEnums updateFlag);

}
