package com.laien.common.lms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.dto.SpeechTaskCallbackDTO;
import com.laien.common.core.util.PaginationUtil;
import com.laien.common.core.aggregator.BaseTaskAggregator;
import com.laien.common.core.aggregator.TaskAggregatorUtil;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.entity.CoreVoiceConfigI18n;
import com.laien.common.domain.enums.AuditTypeEnums;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import com.laien.common.domain.enums.UpdateFlagEnums;
import com.laien.common.domain.request.CoreSpeechTask18nPageReq;
import com.laien.common.domain.request.CoreTaskI18nCheckReq;
import com.laien.common.domain.request.CoreTaskI18nUpdateReq;
import com.laien.common.domain.response.CoreSpeechTaskI18nPageVO;
import com.laien.common.domain.response.CoreSpeechTaskI18nVO;
import com.laien.common.frame.entity.BaseModel;
import com.laien.common.frame.response.PageRes;
import com.laien.common.frame.utils.BizExceptionUtil;
import com.laien.common.lms.mapper.CoreSpeechTaskI18nMapper;
import com.laien.common.lms.mapstruct.CoreSpeechTaskI18nMapStruct;
import com.laien.common.lms.service.ICoreBusinessTaskRelationI18nService;
import com.laien.common.lms.service.ICoreSpeechTaskI18nService;
import com.laien.common.lms.service.ICoreTextTaskI18nService;
import com.laien.common.lms.service.ICoreVoiceConfigI18nService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Comparator;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@Service
public class CoreSpeechTaskI18nServiceImpl extends ServiceImpl<CoreSpeechTaskI18nMapper, CoreSpeechTaskI18n> implements ICoreSpeechTaskI18nService {

    @Resource
    private ICoreTextTaskI18nService coreTextTaskI18nService;
    @Resource
    private CoreSpeechTaskI18nMapStruct mapStruct;
    @Resource
    private ICoreVoiceConfigI18nService coreVoiceConfigI18nService;
    @Resource
    private ICoreBusinessTaskRelationI18nService coreBusinessTaskRelationI18nService;

    private final MD5 md5 = MD5.create();

    @Override
    public List<CoreSpeechTaskI18n> query(Collection<String> md5s, List<LanguageEnums> languageList) {
        if (CollUtil.isEmpty(md5s) || CollUtil.isEmpty(languageList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CoreSpeechTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreSpeechTaskI18n::getTextMd5, md5s)
                .in(CoreSpeechTaskI18n::getLanguage, languageList);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<CoreSpeechTaskI18n> query(List<SpeechTaskStatusEnums> speechTaskStatusList, Integer limitSize, Integer retryCount) {
        if(null == limitSize || limitSize <= 0){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CoreSpeechTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreSpeechTaskI18n::getStatus, speechTaskStatusList)
                .lt(CoreSpeechTaskI18n::getRetryCount, retryCount)
                .last("limit " + limitSize);
        return list(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeStatus(Integer id, SpeechTaskStatusEnums status, boolean checkStatus, String translationMd5, Integer retryCount) {
        LambdaUpdateWrapper<CoreSpeechTaskI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CoreSpeechTaskI18n::getId, id)
                .eq(StrUtil.isNotBlank(translationMd5),CoreSpeechTaskI18n::getTranslationTextMd5, translationMd5)
                .set(CoreSpeechTaskI18n::getStatus, status)
                .set(BaseModel::getUpdateTime, LocalDateTime.now())
                .set(CoreSpeechTaskI18n::getRetryCount, retryCount)
                .set(CoreSpeechTaskI18n::isCheckStatus, checkStatus);
        update(wrapper);
        CoreSpeechTaskI18n speechTaskI18n = getById(id);
        coreBusinessTaskRelationI18nService.update(speechTaskI18n.getCoreTextTaskI18nId(), UpdateFlagEnums.AUDIO_CHANGED);
    }

    @Override
    public List<CoreSpeechTaskI18n> queryStatusTimeoutTask(List<SpeechTaskStatusEnums> statusList, Integer timeoutDuration, Integer limitSize, Integer retryCount) {
        LocalDateTime timeoutDateTime = LocalDateTimeUtil.offset(LocalDateTime.now(), -timeoutDuration, ChronoUnit.MINUTES);
        LambdaQueryWrapper<CoreSpeechTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreSpeechTaskI18n::getStatus, statusList)
                .le(BaseModel::getUpdateTime,timeoutDateTime)
                .lt(CoreSpeechTaskI18n::getRetryCount, retryCount)
                .last("limit " + limitSize);
        return list(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void callback(SpeechTaskCallbackDTO callbackDTO) {
        if(StrUtil.isBlank(callbackDTO.getAudioUrl())) {
            log.error("speech task callback: audio url is empty,callbackDTO:{}", callbackDTO);
            return;
        }
        Integer id = callbackDTO.getI18nData().getId();
        String newTranslationText = callbackDTO.getText();
        boolean reduced = callbackDTO.isReduced();
        String newTranslationTextMd5 = md5.digestHex(newTranslationText);
        LambdaUpdateWrapper<CoreSpeechTaskI18n> wrapper = new LambdaUpdateWrapper<>();
        String translationMd5 = callbackDTO.getI18nData().getTranslationMd5();
        Integer duration = NumberUtil.round(NumberUtil.mul(callbackDTO.getDuration(), 1000), 0).intValue();
        String audioUrl = StrUtil.replaceFirst(URLUtil.getPath(callbackDTO.getAudioUrl()),"/","");
        wrapper.eq(CoreSpeechTaskI18n::getId, id)
                .eq(StrUtil.isNotBlank(translationMd5),CoreSpeechTaskI18n::getTranslationTextMd5, translationMd5)
                .set(CoreSpeechTaskI18n::getTranslationText, newTranslationText)
                .set(CoreSpeechTaskI18n::getStatus, SpeechTaskStatusEnums.SUCCESS)
                .set(CoreSpeechTaskI18n::getTranslationTextMd5, newTranslationTextMd5)
                .set(CoreSpeechTaskI18n::getTranslationAudioUrl,audioUrl)
                .set(BaseModel::getUpdateTime, LocalDateTime.now())
                .set(CoreSpeechTaskI18n::getTranslationDuration, duration)
                .set(CoreSpeechTaskI18n::isReduced, reduced);
        if(reduced){
            wrapper.set(CoreSpeechTaskI18n::getAuditType, AuditTypeEnums.AI_COMPRESSION);
        }
        update(wrapper);
        CoreSpeechTaskI18n speechTaskI18n = getById(id);
        coreBusinessTaskRelationI18nService.update(speechTaskI18n.getCoreTextTaskI18nId(), UpdateFlagEnums.AUDIO_CHANGED);
    }


    @Override
    public PageRes<CoreSpeechTaskI18nPageVO> pageSpeechTasks(CoreSpeechTask18nPageReq req) {
        // 先查询所有符合条件的原始数据（不分页）
        LambdaQueryWrapper<CoreSpeechTaskI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoreSpeechTaskI18n::getDelFlag, false);

        // 添加查询条件 - 使用链式调用，将判断条件作为第一个参数
        queryWrapper.eq(req.getGender() != null, CoreSpeechTaskI18n::getGender, req.getGender())
                   .eq(req.getCheckStatus() != null, CoreSpeechTaskI18n::isCheckStatus, req.getCheckStatus())
                   .likeRight(StrUtil.isNotBlank(req.getText()), CoreSpeechTaskI18n::getText, req.getText())
                   .eq(req.getStatus() != null, CoreSpeechTaskI18n::getStatus, req.getStatus())
                   .eq(req.getReduced() != null, CoreSpeechTaskI18n::isReduced, req.getReduced())
                   .apply(req.getOverLength() != null && req.getOverLength(),
                          "translation_duration!=0 AND translation_duration > duration")
                   .in(CollUtil.isNotEmpty(req.getLanguage()), CoreSpeechTaskI18n::getLanguage, req.getLanguage())
                   .eq(req.getCoreTextTaskI18nId() != null, CoreSpeechTaskI18n::getCoreTextTaskI18nId, req.getCoreTextTaskI18nId())
                   .eq(req.getCoreVoiceConfigI18nId() != null, CoreSpeechTaskI18n::getCoreVoiceConfigI18nId, req.getCoreVoiceConfigI18nId())
                   .apply(req.getProjCodeSum() != 0, "proj_code & {0} > 0", req.getProjCodeSum());

        // 查询所有符合条件的数据
        List<CoreSpeechTaskI18n> allTasks = this.list(queryWrapper);
        if (CollUtil.isEmpty(allTasks)) {
            return new PageRes<>(new Page<>(req.getPageNum(), req.getPageSize()));
        }

        // 在Java代码中进行分组处理
        List<SpeechTaskAggregator> aggregators = groupAndAggregateSpeechTasksInJava(allTasks);

        // 分页并转换为VO（只转换当页数据）
        IPage<CoreSpeechTaskI18nPageVO> page = PaginationUtil.manualPaginationWithConvert(
                aggregators, req.getPageNum(), req.getPageSize(), SpeechTaskAggregator::toVO);

        // 批量查询并设置音色配置名称（仅对当页数据）
        batchSetVoiceConfigNames(page.getRecords());

        return new PageRes<>(page);
    }

    /**
     * 高性能分组聚合处理 - 使用通用聚合框架
     *
     * <AUTHOR>
     * @since 2025/07/11
     * @param allTasks 所有符合条件的任务数据
     * @return 分组聚合后的聚合器列表
     */
    private List<SpeechTaskAggregator> groupAndAggregateSpeechTasksInJava(List<CoreSpeechTaskI18n> allTasks) {
        // 使用通用聚合工具进行分组聚合
        List<SpeechTaskAggregator> result = TaskAggregatorUtil.groupAndAggregateWithKeyBuilder(
                allTasks,
                this::buildSpeechGroupKey,
                SpeechTaskAggregator::new
        );

        // 按text排序
        result.sort(Comparator.comparing(t -> t.getBaseTask().getText(),
                Comparator.nullsLast(String::compareTo)));

        return result;
    }

    /**
     * 高效构造语音任务分组键
     */
    private String buildSpeechGroupKey(CoreSpeechTaskI18n task, StringBuilder keyBuilder) {
        keyBuilder.setLength(0); // 清空StringBuilder复用

        // textMd5
        if (task.getTextMd5() != null) {
            keyBuilder.append(task.getTextMd5());
        }
        keyBuilder.append('|');

        // coreVoiceConfigI18nId
        if (task.getCoreVoiceConfigI18nId() != null) {
            keyBuilder.append(task.getCoreVoiceConfigI18nId());
        }
        keyBuilder.append('|');

        // projCode - 优化：避免重复排序
        if (task.getProjCode() != null && !task.getProjCode().isEmpty()) {
            // 如果只有一个元素，直接添加
            if (task.getProjCode().size() == 1) {
                keyBuilder.append(task.getProjCode().get(0).name());
            } else {
                // 多个元素时才排序
                task.getProjCode().stream()
                    .map(Enum::name)
                    .sorted()
                    .forEach(name -> keyBuilder.append(name).append(','));
                // 移除最后一个逗号
                if (keyBuilder.charAt(keyBuilder.length() - 1) == ',') {
                    keyBuilder.setLength(keyBuilder.length() - 1);
                }
            }
        }

        return keyBuilder.toString();
    }

    /**
     * 批量查询并设置音色配置名称
     *
     * <AUTHOR>
     * @since 2025/07/11
     * @param pageVos 当页的VO数据
     */
    private void batchSetVoiceConfigNames(List<CoreSpeechTaskI18nPageVO> pageVos) {
        if (CollUtil.isEmpty(pageVos)) {
            return;
        }

        // 收集所有需要查询的音色配置ID
        Set<Integer> configIds = pageVos.stream()
                .map(CoreSpeechTaskI18nPageVO::getCoreVoiceConfigI18nId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(configIds)) {
            return;
        }

        // 批量查询音色配置
        LambdaQueryWrapper<CoreVoiceConfigI18n> configWrapper = new LambdaQueryWrapper<>();
        configWrapper.in(CoreVoiceConfigI18n::getId, configIds);
        Map<Integer, String> configNameMap = coreVoiceConfigI18nService.list(configWrapper).stream()
                .collect(Collectors.toMap(CoreVoiceConfigI18n::getId, CoreVoiceConfigI18n::getName));

        // 回写音色配置名称
        pageVos.forEach(vo -> {
            if (vo.getCoreVoiceConfigI18nId() != null) {
                vo.setCoreVoiceConfigName(configNameMap.get(vo.getCoreVoiceConfigI18nId()));
            }
        });
    }

    /**
     * Speech任务聚合器 - 继承通用聚合器
     */
    private class SpeechTaskAggregator extends BaseTaskAggregator<CoreSpeechTaskI18n, CoreSpeechTaskI18nPageVO> {

        public SpeechTaskAggregator(CoreSpeechTaskI18n firstTask) {
            super(firstTask);
        }

        @Override
        public CoreSpeechTaskI18nPageVO toVO() {
            CoreSpeechTaskI18nPageVO vo = mapStruct.toPageVO(baseTask);
            vo.setCreateTime(getMinCreateTime());
            vo.setUpdateTime(getMaxUpdateTime());
            vo.setUpdateUser(getLatestUpdateTask().getUpdateUser());

            // 音色配置名称将在分页后批量设置，这里不单独查询

            // 转换子任务列表 - 预分配容量
            List<CoreSpeechTaskI18nVO> subTaskList = new ArrayList<>(getAllTasks().size());
            for (CoreSpeechTaskI18n task : getAllTasks()) {
                subTaskList.add(mapStruct.toVO(task));
            }
            vo.setSubTaskList(subTaskList);

            return vo;
        }
    }

    @Transactional
    @Override
    public void updateTranslationText(CoreTaskI18nUpdateReq req) {
        //check if text change
        CoreSpeechTaskI18n task = super.getById(req.getId());
        if (StrUtil.equals(task.getTranslationText(), req.getTranslationText())) {
            return;
        }
        //update SpeechTask
        CoreSpeechTaskI18n entity = mapStruct.toEntity(req);
        entity.setTranslationTextMd5(md5.digestHex(entity.getTranslationText()));
        this.resetSpeechTaskField(entity,false);
        this.updateById(entity);
    }

    @Override
    public void resetSpeechTaskField(CoreSpeechTaskI18n task, boolean resetId) {
        task.setTranslationAudioUrl(null)
                .setTranslationDuration(null)
                .setReduced(false)
                .setStatus(SpeechTaskStatusEnums.PENDING)
                .setCheckStatus(false)
                .setAuditType(AuditTypeEnums.MANUAL_VERIFICATION);
        if (resetId) {
            task.setId(null)
                    .setCreateTime(null)
                    .setUpdateTime(null)
                    .setCreateUser(null)
                    .setUpdateUser(null);
        }
    }


    @Transactional
    @Override
    public void updateCheckStatus(CoreTaskI18nCheckReq req) {
        BizExceptionUtil.throwIf(req==null || CollUtil.isEmpty(req.getIdList()), "idList is empty");
        LambdaUpdateWrapper<CoreSpeechTaskI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CoreSpeechTaskI18n::getId, req.getIdList());
        wrapper.set(CoreSpeechTaskI18n::isCheckStatus, req.getCheckStatus());
        this.update(new CoreSpeechTaskI18n(),wrapper);
    }

    @Override
    public List<CoreSpeechTaskI18n> query(Collection<Integer> coreTextTaskI18nIds, SpeechTaskStatusEnums status) {
        if(CollUtil.isEmpty(coreTextTaskI18nIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CoreSpeechTaskI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CoreSpeechTaskI18n::getCoreTextTaskI18nId, coreTextTaskI18nIds)
                .eq(CoreSpeechTaskI18n::getStatus, status);
        return list(wrapper);
    }

    @Override
    public void updateFailed(Integer id, Integer retryCount) {
        LambdaUpdateWrapper<CoreSpeechTaskI18n> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BaseModel::getId, id)
                .set(CoreSpeechTaskI18n::getRetryCount, retryCount)
                .set(CoreSpeechTaskI18n::getStatus, SpeechTaskStatusEnums.FAIL)
                .set(CoreSpeechTaskI18n::getUpdateTime, LocalDateTime.now());
        baseMapper.update(null, wrapper);
    }
}

