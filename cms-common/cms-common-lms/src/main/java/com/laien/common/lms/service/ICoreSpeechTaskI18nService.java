package com.laien.common.lms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.core.dto.SpeechTaskCallbackDTO;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.enums.SpeechTaskStatusEnums;
import com.laien.common.domain.request.CoreSpeechTask18nPageReq;
import com.laien.common.domain.request.CoreTaskI18nCheckReq;
import com.laien.common.domain.request.CoreTaskI18nUpdateReq;
import com.laien.common.domain.response.CoreSpeechTaskI18nPageVO;
import com.laien.common.frame.response.PageRes;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ICoreSpeechTaskI18nService extends IService<CoreSpeechTaskI18n> {

    List<CoreSpeechTaskI18n> query(Collection<String> md5s, List<LanguageEnums> languageList);

    List<CoreSpeechTaskI18n> query(List<SpeechTaskStatusEnums> speechTaskStatusList, Integer limitSize, Integer retryCount);

    void changeStatus(Integer id, SpeechTaskStatusEnums status, boolean checkStatus, String translationMd5, Integer retryCount);

    List<CoreSpeechTaskI18n> queryStatusTimeoutTask(List<SpeechTaskStatusEnums> statusList, Integer timeoutDuration, Integer limitSize, Integer retryCount);

    void callback(SpeechTaskCallbackDTO callbackDTO);

    PageRes<CoreSpeechTaskI18nPageVO> pageSpeechTasks(CoreSpeechTask18nPageReq req);

    void updateTranslationText(CoreTaskI18nUpdateReq req);

    void resetSpeechTaskField(CoreSpeechTaskI18n task, boolean resetId);

    void updateCheckStatus(CoreTaskI18nCheckReq req);

    List<CoreSpeechTaskI18n> query(Collection<Integer> coreTextTaskI18nIds, SpeechTaskStatusEnums status);

    void updateFailed(Integer id, Integer retryCount);
}
