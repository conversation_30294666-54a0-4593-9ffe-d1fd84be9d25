package com.laien.common.lms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.request.CoreSpeechTask18nPageReq;
import com.laien.common.domain.response.CoreSpeechTaskI18nPageVO;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface CoreSpeechTaskI18nMapper extends BaseMapper<CoreSpeechTaskI18n> {

    IPage<CoreSpeechTaskI18nPageVO> pageGroupByTasks(IPage<?> page,
                                                   @Param("req") CoreSpeechTask18nPageReq req);
}
