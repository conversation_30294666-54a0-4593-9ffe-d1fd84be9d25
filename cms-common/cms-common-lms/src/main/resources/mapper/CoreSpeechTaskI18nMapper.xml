<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.common.lms.mapper.CoreSpeechTaskI18nMapper">

    <resultMap id="CoreSpeechTaskGroupResultMap" type="com.laien.common.domain.response.CoreSpeechTaskI18nPageVO">
        <result property="text" column="text"/>
        <result property="textMd5" column="textMd5"/>
        <result property="audioUrl" column="audioUrl"/>
        <result property="duration" column="duration"/>
        <result property="coreVoiceConfigI18nId" column="coreVoiceConfigI18nId"/>
        <result property="gender" column="gender"/>
        <result property="projCode" column="projCode" typeHandler="com.laien.common.domain.enums.ProjCodeEnums$TypeHandler"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="updateUser" column="updateUser"/>
        <result property="coreTextTaskI18nId" column="coreTextTaskI18nId"/>
        <result property="subTaskIds" column="subTaskIds"/>
    </resultMap>

    <select id="pageGroupByTasks" resultMap="CoreSpeechTaskGroupResultMap">
        SELECT
        ANY_VALUE(`text`) AS text,
        `text_md5` AS textMd5,
        ANY_VALUE(`audio_url`) as audioUrl,
        ANY_VALUE(`duration`) as duration,
        ANY_VALUE(`core_voice_config_i18n_id`) as coreVoiceConfigI18nId,
        ANY_VALUE(`gender`) AS gender,
        `proj_code` AS projCode,
        MIN(create_time) AS createTime,
        MAX(update_time) AS updateTime,
        SUBSTRING_INDEX(GROUP_CONCAT(update_user ORDER BY update_time DESC, id DESC), ',', 1) AS updateUser,
        ANY_VALUE(`core_text_task_i18n_id`) as coreTextTaskI18nId,
        GROUP_CONCAT(id) AS subTaskIds
        FROM
        core_speech_task_i18n
        <where>
            del_flag = 0
            <if test="req.gender != null">
                AND gender = #{req.gender}
            </if>
            <if test="req.checkStatus != null">
                AND check_status = #{req.checkStatus}
            </if>
            <if test="req.text != null and req.text != ''">
                AND text LIKE CONCAT(#{req.text}, '%')
            </if>
            <if test="req.status != null">
                AND status = #{req.status}
            </if>
            <if test="req.reduced != null">
                AND reduced = #{req.reduced}
            </if>
            <if test="req.overLength != null and req.overLength">
                AND translation_duration!=0 AND translation_duration > duration
            </if>
            <if test="req.language != null and req.language.size() > 0">
                AND language IN
                <foreach item="item" collection="req.language" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.coreTextTaskI18nId != null">
                AND core_text_task_i18n_id = #{req.coreTextTaskI18nId}
            </if>
            <if test="req.coreVoiceConfigI18nId != null">
                AND core_voice_config_i18n_id = #{req.coreVoiceConfigI18nId}
            </if>
            <if test="req.projCodeSum != null and req.projCodeSum != 0">
                AND proj_code &amp; #{req.projCodeSum} > 0
            </if>
        </where>
        GROUP BY
        `text_md5`,`coreVoiceConfigI18nId`, `proj_code`
        ORDER BY
        `text` ASC
    </select>
</mapper>
