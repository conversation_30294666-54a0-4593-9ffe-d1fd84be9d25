<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.common.lms.mapper.CoreTextTaskI18nMapper">

    <resultMap id="CoreTextTaskGroupResultMap" type="com.laien.common.domain.response.CoreTextTaskI18nPageVO">
        <result property="projCode" column="projCode" typeHandler="com.laien.common.domain.enums.ProjCodeEnums$TypeHandler"/>
        <result property="type" column="type"/>
        <result property="textMd5" column="textMd5"/>
        <result property="text" column="text"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="updateUser" column="updateUser"/>
        <result property="subTaskIds" column="subTaskIds"/>
    </resultMap>
    <update id="addRetryCount">
        UPDATE core_text_task_i18n
        SET retry_count = retry_count + 1,
        update_time = now()
        WHERE
        id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="pageGroupByTasks" resultMap="CoreTextTaskGroupResultMap">
        SELECT
        `type`,
        `proj_code` AS projCode,
        `text_md5` AS textMd5,
        ANY_VALUE(`text`) AS text,
        MIN(create_time) AS createTime,
        MAX(update_time) AS updateTime,
        SUBSTRING_INDEX(GROUP_CONCAT(update_user ORDER BY update_time DESC, id DESC), ',', 1) AS updateUser,
        GROUP_CONCAT(id) AS subTaskIds
        FROM
        core_text_task_i18n
        <where>
            del_flag = 0
            <if test="req.type != null">
                AND type = #{req.type}
            </if>
            <if test="req.text != null and req.text != ''">
                AND text LIKE CONCAT(#{req.text}, '%')
            </if>
            <if test="req.status != null">
                AND status = #{req.status}
            </if>
            <if test="req.checkStatus != null">
                AND check_status = #{req.checkStatus}
            </if>
            <if test="req.projCodeSum != null and req.projCodeSum != 0">
                AND proj_code &amp; #{req.projCodeSum} > 0
            </if>
            <if test="req.language != null and req.language.size() > 0">
                AND language IN
                <foreach item="item" collection="req.language" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        `type`, `proj_code`, `text_md5`
        ORDER BY
        `text` ASC
    </select>
</mapper>
