package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/2/24
 */
@Getter
public enum DifficultyEnums implements IEnumBase {


    EASY(10, "Easy"),
    MEDIUM(11, "Medium"),
    HARD(12, "Hard");



    @EnumValue
    private final Integer code;
    private final String name;

    DifficultyEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getDisplayName() {
        return this.name;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }

    public static DifficultyEnums getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (DifficultyEnums value : DifficultyEnums.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
