package com.laien.common.oog116.enums;

import cn.hutool.core.util.StrUtil;
import com.laien.common.oog116.vo.PlanName116VO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/15
 */
@Getter
public enum Position116Enums {
    STANDING("Standing", "28 Days Enhance Balance Plan", getStadingNameList(), 10),
    SEATED("Seated", "28 Days Chair Yoga Plan", getSeatedNameList(), 11),
    BOTH("Both", "28 Days Enhance Vitality Plan", getBothNameList(), 12),
    LYING("Lying", "28 Days Lying Recovery Plan", Collections.emptyList(), 13);

    private final String name;
    private final String planName;
    private final Integer code;
    private final List<PlanName116VO> planNameList;

    Position116Enums(String name, String planName,List<PlanName116VO> planNameList, Integer code) {
        this.name = name;
        this.planName = planName;
        this.code = code;
        this.planNameList = planNameList;
    }

    public static List<PlanName116VO> getStadingNameList() {
        List<PlanName116VO> planName116List = new ArrayList<>();
        planName116List.add(new PlanName116VO("28 Days", "Foundational Stability Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Enhance Balance Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Progressive Strength Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Flexibility Mastery Plan"));
        return planName116List;
    }

    public static List<PlanName116VO> getSeatedNameList() {
        List<PlanName116VO> planName116List = new ArrayList<>();
        planName116List.add(new PlanName116VO("28 Days", "Chair Yoga Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Advanced Chair Yoga Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Elevated Chair Yoga Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Chair Yoga Evolution Plan"));
        return planName116List;
    }

    public static List<PlanName116VO> getBothNameList() {
        List<PlanName116VO> planName116List = new ArrayList<>();
        planName116List.add(new PlanName116VO("28 Days", "Enhance Vitality Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Vitality Boost Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Vitality Mastery Plan"));
        planName116List.add(new PlanName116VO("28 Days", "Peak Vitality Plan"));
        return planName116List;
    }

    public PlanName116VO matchPlanName(Integer completeTimes){
        if(null == completeTimes){
            return null;
        }
        int size = planNameList.size();
        if(completeTimes >= size){
            return planNameList.get(size -1);
        }
        return planNameList.get(completeTimes);
    }

    public static Position116Enums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (Position116Enums position : values()) {
            if (code.equals(position.code)) {
                return position;
            }
        }
        return null;
    }

    public static Position116Enums getByName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        for (Position116Enums position : values()) {
            if (name.equals(position.name)) {
                return position;
            }
        }
        return null;
    }

}
