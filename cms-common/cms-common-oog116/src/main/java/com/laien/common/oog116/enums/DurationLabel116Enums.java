package com.laien.common.oog116.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@Getter
public enum DurationLabel116Enums {
    MIN_5_10("5-10", 10),
    MIN_10_15("10-15", 11),
    MIN_15_20("15-20", 12),
    MIN_20_30("20-30", 13);


    private final String value;
    private final Integer code;

    DurationLabel116Enums(String value, Integer code) {
        this.value = value;
        this.code = code;
    }

    public static DurationLabel116Enums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (DurationLabel116Enums duration : values()) {
            if (code.equals(duration.code)) {
                return duration;
            }
        }
        return null;
    }
}
