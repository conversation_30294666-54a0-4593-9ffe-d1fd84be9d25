package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/2/21
 */
@Getter
public enum TargetEnums implements IEnumBase {

    FULL_BODY(10,"Full Body"),
    UPPER_BODY(11,"Upper Body"),
    LOWER_BODY(12,"Lower Body");



    @EnumValue
    private final Integer code;
    private final String name;

    TargetEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getDisplayName() {
        return this.name;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
