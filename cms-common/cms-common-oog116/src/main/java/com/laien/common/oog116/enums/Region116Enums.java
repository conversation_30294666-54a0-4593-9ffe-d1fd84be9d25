package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * 身体部位枚举
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
public enum Region116Enums implements IEnumBase {

    NECK(1, "Neck", 10),
    SHOULDER(1 << 1, "Shoulder", 11),
    WRIST(1 << 2, "Wrist", 12),
    BACK(1 << 3, "Back", 13),
    SI_JOINT(1 << 4, "SI Joint", 14),
    KNEE(1 << 5, "Knee", 15),
    HIP(1 << 6, "Hip", 16),
    ANKLE(1 << 7, "Ankle", 17);

    @EnumValue
    private final Integer code;
    private final String name;
    private final Integer showCode;

    Region116Enums(Integer code, String name, Integer showCode) {
        this.code = code;
        this.name = name;
        this.showCode = showCode;
    }

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<Region116Enums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<Region116Enums> {
    }
}
