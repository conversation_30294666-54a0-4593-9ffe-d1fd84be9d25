package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/3/15
 */
@Getter
public enum Gender116Enums {
    FEMALE("Female", 10),
    MALE("Male", 11),
    BOTH("Both", 12);

    private final String name;
    @EnumValue
    private final Integer code;

    Gender116Enums(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public static Gender116Enums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (Gender116Enums enums : values()) {
            if (code.equals(enums.code)) {
                return enums;
            }
        }
        return null;
    }

    public static Gender116Enums getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (Gender116Enums value : Gender116Enums.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

}
