package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * category showType 枚举
 *
 * <AUTHOR>
 * @since 2024/4/16
 */
@Getter
public enum Category116ShowTypeEnums {
    /**
     * 展示样式为标签
     */
    LABEL("Label"),
    /**
     * 展示样式为卡片
     */
    CARD("Card"),
    /**
     * 展示样式为Grid
     */
    GRID("Grid");

    @EnumValue
    private final String value;

    Category116ShowTypeEnums(String value) {
        this.value = value;
    }

}
