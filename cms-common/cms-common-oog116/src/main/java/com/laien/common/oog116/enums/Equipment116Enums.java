package com.laien.common.oog116.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/15
 */
@Getter
public enum Equipment116Enums {

    NONE("No equipment",10, "No equipment", "cms/video116/img/no_equipment.png", null),
    DUMBBELL("Dumbbell (lightweight)",11, "Dumbbell", "cms/video116/img/dumbbell_little.png", "cms/video116/img/dumbbell.png"),
    RESISTANCE_BAND("Resistance band",12, "Resistance Band", "cms/video116/img/resistant_band.png","cms/video116/img/band.png"),
    CHAIR("Chair",13, "Chair", null,"cms/video116/img/chair.png"),
    DUMBBELL_MODERATE("Dumbbell (moderate)",14, "Dumbbell (moderate)", "cms/video116/img/dumbbell_little.png", "cms/video116/img/dumbbell.png"),
    DUMBBELL_MIDWEIGHT("Dumbbell (midweight)",15, "Dumbbell (midweight)", "cms/video116/img/dumbbell_little.png", "cms/video116/img/dumbbell.png"),
    ;

    private final String name;
    private final Integer code;
    private final String cardName;
    private final String smallImgUrl;
    private final String detailImgUrl;

    Equipment116Enums(String name, Integer code, String cardName, String smallImgUrl, String detailImgUrl) {
        this.name = name;
        this.code = code;
        this.cardName = cardName;
        this.smallImgUrl = smallImgUrl;
        this.detailImgUrl = detailImgUrl;
    }

    public static List<Equipment116Enums> getEquipmentList(String equipment) {
        if (StringUtils.isBlank(equipment)) {
            return null;
        }
        List<String> nameList = Arrays.stream(equipment.split(",")).collect(Collectors.toList());
        List<Equipment116Enums> list = new ArrayList<>();
        for (String item : nameList) {
            for (Equipment116Enums value : values()) {
                if (value.getName().equals(item)) {
                    list.add(value) ;
                }
            }
        }
        return list;
    }

    public static Equipment116Enums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (Equipment116Enums enums : values()) {
            if (code.equals(enums.code)) {
                return enums;
            }
        }
        return null;
    }

}
