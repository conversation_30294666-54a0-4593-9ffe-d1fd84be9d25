package com.laien.common.oog116.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * id和name
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@ApiModel(value="116 plan name", description="")
public class PlanName116VO {

    @ApiModelProperty(value = "preName")
    private String preName;
    @ApiModelProperty(value = "lastName")
    private String lastName;

}
