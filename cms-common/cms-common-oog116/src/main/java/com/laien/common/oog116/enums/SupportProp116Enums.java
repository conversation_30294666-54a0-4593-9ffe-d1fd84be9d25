package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * 支撑道具枚举
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
public enum SupportProp116Enums implements IEnumBase {

    CHAIR(1, "Chair", 10),
    MAT(1 << 1, "Mat", 11),
    NONE(1 << 2, "None", 12);

    @EnumValue
    private final Integer code;
    private final String name;
    private final Integer showCode;

    SupportProp116Enums(Integer code, String name, Integer showCode) {
        this.code = code;
        this.name = name;
        this.showCode = showCode;
    }

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<SupportProp116Enums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SupportProp116Enums> {
    }
}
