package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * 焦点类型枚举
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
public enum Focus116Enums implements IEnumBase {

    RELAX(1, "Relax", 10),
    MOBILITY(1 << 1, "Mobility", 11),
    STRENGTH(1 << 2, "Strength", 12);

    @EnumValue
    private final Integer code;
    private final String name;
    private final Integer showCode;

    Focus116Enums(Integer code, String name, Integer showCode) {
        this.code = code;
        this.name = name;
        this.showCode = showCode;
    }

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<Focus116Enums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<Focus116Enums> {
    }
}
