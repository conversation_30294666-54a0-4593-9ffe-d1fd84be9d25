package com.laien.common.oog116.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/15
 */
@Getter
public enum Restriction116Enums {
    SHOULDER(1,"Shoulder", 10),
    BACK(1 << 1,"Back", 11),
    WRIST(1 << 2,"Wrist", 12),
    KNEE(1 << 3,"Knee", 13),
    ANKLE(1 << 4,"Ankle", 14),
    HIP(1 << 5,"Hip", 15),

    ;
    private final String name;
    private final Integer code;
    private final Integer value;

    Restriction116Enums(int value, String name, Integer code) {
        this.name = name;
        this.value = value;
        this.code = code;
    }

    public static Restriction116Enums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (Restriction116Enums restriction116Enums : values()) {
            if (code.equals(restriction116Enums.code)) {
                return restriction116Enums;
            }
        }
        return null;
    }

    /**
     * 获取所有的限制组合,不包含None
     */
    public static List<List<Restriction116Enums>> getRestrictionCombination() {
        List<List<Restriction116Enums>> result = new ArrayList<>();
        Restriction116Enums[] values = Restriction116Enums.values();
        int length = values.length;
        int max = 1 << length;
        for (int i = 0; i < max; i++) {
            List<Restriction116Enums> combination = new ArrayList<>();
            for (Restriction116Enums restriction : values) {
                if ((i & restriction.getValue()) != 0) {
                    combination.add(restriction);
                }
            }
            result.add(combination);
        }
        return result;
    }

    /**
     * 获取排除restrictionSet后的限制
     */
    public static List<String> eliminateRestriction(Set<String> restrictionSet) {
        List<String> allRestriction = Arrays.stream(values()).map(Restriction116Enums::getName).collect(Collectors.toList());
        allRestriction.removeAll(restrictionSet);
        return allRestriction;
    }

    /**
     * 根据字符串列表计算sum
     *
     * @param restrictionStringList restrictionStringList
     * @return Integer
     */
    public static Integer sumByString(List<String> restrictionStringList) {
        if(CollUtil.isEmpty(restrictionStringList)){
            return 0;
        }
        List<Restriction116Enums> restrictionList = new ArrayList<>();
        for (Restriction116Enums restriction : values()) {
            restrictionStringList.stream().filter(o -> Objects.equals(o, restriction.getName()))
                    .findFirst()
                    .ifPresent(o -> restrictionList.add(restriction));
        }
        return sum(restrictionList);
    }

    /**
     * 根据字符串获取枚举set
     *
     * @param restrictionNameList restrictionNameList
     * @return Integer
     */
    public static Set<Integer> getCodeByString(List<String> restrictionNameList) {
        Set<Integer> restrictionCodeSet = new HashSet<>();
        if(CollUtil.isEmpty(restrictionNameList)){
            return restrictionCodeSet;
        }
        for (Restriction116Enums restriction : values()) {
            restrictionNameList.stream().filter(o -> Objects.equals(o, restriction.getName()))
                    .findFirst()
                    .ifPresent(o -> restrictionCodeSet.add(restriction.code));
        }
        return restrictionCodeSet;
    }

    public static Integer sum(List<Restriction116Enums> restrictionList) {
        if(CollUtil.isEmpty(restrictionList)){
            return 0;
        }
        return restrictionList.stream().map(Restriction116Enums::getValue).reduce(0, Integer::sum);
    }

    public static List<Restriction116Enums> getRestriction(Integer sum) {
        if(null == sum){
            return new ArrayList<>();
        }
        List<Restriction116Enums> restrictionList = new ArrayList<>();
        for (Restriction116Enums restriction : values()) {
            Integer restrictionValue = restriction.value;
            if((restrictionValue & sum) == restrictionValue){
                restrictionList.add(restriction);
            }
        }
        return restrictionList;
    }

    /**
     * 根据sum获取name逗号分隔的字符串
     *
     * @param sum sum
     * @return String
     */
    public static String getRestrictionString(Integer sum) {
        List<String> stringList = new ArrayList<>();
        for (Restriction116Enums restriction : values()) {
            Integer restrictionValue = restriction.value;
            if((restrictionValue & sum) == restrictionValue){
                stringList.add(restriction.name);
            }
        }
        if (stringList.isEmpty()) {
            return "None";
        }
        return String.join(",", stringList);
    }

}
