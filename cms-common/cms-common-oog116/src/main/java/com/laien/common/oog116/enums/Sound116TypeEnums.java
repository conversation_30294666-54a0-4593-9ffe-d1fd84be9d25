package com.laien.common.oog116.enums;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.List;
@Getter
@AllArgsConstructor
public enum Sound116TypeEnums implements IEnumBase {
    REGULAR_FITNESS(1, "Prompt",
            ListUtil.of(Sound116SubTypeEnums.PROMPT, Sound116SubTypeEnums.BASIC, Sound116SubTypeEnums.COMPLETE, Sound116SubTypeEnums.WELCOME)),
    CHAIR_YOGA(2, "Chair Yoga",
            ListUtil.of(Sound116SubTypeEnums.PROMPT,Sound116SubTypeEnums.BASIC,Sound116SubTypeEnums.COMPLETE,Sound116SubTypeEnums.WELCOME)),
    TAI_CHI(3, "<PERSON> Chi",
            ListUtil.of(Sound116SubTypeEnums.PROMPT,Sound116SubTypeEnums.BASIC,Sound116SubTypeEnums.COMPLETE,Sound116SubTypeEnums.WELCOME)),
    CARDIO_105(4, "105 Cardio",
            ListUtil.of(Sound116SubTypeEnums.PROMPT,Sound116SubTypeEnums.BASIC,Sound116SubTypeEnums.COMPLETE,Sound116SubTypeEnums.WELCOME)),
    RECOVERY(5, "Recovery",
            ListUtil.of(Sound116SubTypeEnums.PROMPT,Sound116SubTypeEnums.BASIC,Sound116SubTypeEnums.COMPLETE,Sound116SubTypeEnums.WELCOME)),
    DUMBBELL_MIDWEIGHT(6, "Dumbbell (midweight)",
            ListUtil.of(Sound116SubTypeEnums.PROMPT,Sound116SubTypeEnums.BASIC,Sound116SubTypeEnums.COMPLETE,Sound116SubTypeEnums.WELCOME))
            ;


    @EnumValue
    private final Integer code;
    private final String name;
    private final List<Sound116SubTypeEnums> soundSubTypes;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<Sound116TypeEnums> {
    }
}
