package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
public enum Template116TypeEnums {

    NORMAL(100, "Normal", normalExerciseTypeList()),

    TAI_CHI(200, "Tai Chi", Lists.newArrayList(ExerciseType116Enums.TAI_CHI)),

    CHAIR_YOGA(300, "Chair Yoga", Lists.newArrayList(ExerciseType116Enums.GENTLE_CHAIR_YOGA)),

    DUMBBELL_MIDWEIGHT(400, "Dumbbell (midweight)", Lists.newArrayList(ExerciseType116Enums.DUMBBELL_MIDWEIGHT))
    ;

    @EnumValue
    private Integer code;

    private String name;

    private List<ExerciseType116Enums> exerciseType116EnumsList;

    Template116TypeEnums(Integer code, String name, List<ExerciseType116Enums> exerciseType116EnumsList) {
        this.code = code;
        this.name = name;
        this.exerciseType116EnumsList = exerciseType116EnumsList;
    }

    private static List<ExerciseType116Enums> normalExerciseTypeList() {
        return Arrays.stream(ExerciseType116Enums.values())
                .filter(exerciseType116 -> !Objects.equals(exerciseType116, ExerciseType116Enums.DUMBBELL_MODERATE))
                .filter(exerciseType116 -> !Objects.equals(exerciseType116, ExerciseType116Enums.CARDIO_105))
                .filter(exerciseType116 -> !Objects.equals(exerciseType116, ExerciseType116Enums.GENTLE_CHAIR_YOGA))
                .filter(exerciseType116 -> !Objects.equals(exerciseType116, ExerciseType116Enums.TAI_CHI))
                .collect(Collectors.toList());
    }

}
