package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * category 116 Type 枚举
 *
 * <AUTHOR>
 * @since 2025/03/25
 */
@AllArgsConstructor
@Getter
public enum Category116TypeEnums implements IEnumBase {
    FULL_BODY(10, "Full Body", "Full Body"),
    BELLY(11, "Belly", "Belly"),
    LEGS(12, "Legs", "Legs"),
    BACK(13, "Back", "Back"),
    ARMS(14, "Arms", "Arms"),
    ABS(15, "Abs", "Abs"),
    SHOULDERS(16, "Shoulders", "Shoulders"),

    ;

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<Category116TypeEnums> {
    }

    public static Category116TypeEnums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (Category116TypeEnums enums : values()) {
            if (code.equals(enums.code)) {
                return enums;
            }
        }
        return null;
    }
}
