package com.laien.common.oog116.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/3/15
 */
@Getter
public enum ExerciseType116Enums {

    // CHAIR_YOGA 在App端展示名为 Chair-Based Cardio
    CHAIR_YOGA("Chair Yoga", "OOG116_Chair Yoga", 10, Collections.singletonList(Equipment116Enums.NONE),
            false, Collections.singletonList(Position116Enums.SEATED), 1, 14, 28) {
        @Override
        public String getImportAliasName() {
            return "Chair Cardio";
        }
    },

    TAI_CHI("Tai Chi", "Tai Chi", 11, Collections.singletonList(Equipment116Enums.NONE),
            true, Collections.singletonList(Position116Enums.STANDING), 2, 0, 28),

    DANCING("Dancing", "Dancing", 12, Collections.singletonList(Equipment116Enums.NONE),
            false, Arrays.asList(Position116Enums.SEATED, Position116Enums.STANDING), 1, 0, 28),

    GENTLE_CARDIO("Gentle Cardio", "OOG116_Gentle Cardio", 13, Collections.singletonList(Equipment116Enums.NONE),
            false, Collections.singletonList(Position116Enums.STANDING), 1, 14, 28),

    WALKING("Walking", "OOG116_Walking", 14, Collections.singletonList(Equipment116Enums.NONE),
            false, Arrays.asList(Position116Enums.SEATED, Position116Enums.STANDING), 1, 14, 28),

    DUMBBELL("Dumbbell (lightweight)", "OOG116_Dumbbell (lightweight)", 15, Collections.singletonList(Equipment116Enums.DUMBBELL),
            true, Arrays.asList(Position116Enums.SEATED, Position116Enums.STANDING), 1, 14, 28),

    RESISTANCE_BAND("Resistance Band", "OOG116_Resistance Band", 16, Collections.singletonList(Equipment116Enums.RESISTANCE_BAND),
            true, Arrays.asList(Position116Enums.SEATED, Position116Enums.STANDING), 1, 0, 28),

    GENTLE_CHAIR_YOGA("Gentle Chair Yoga", "Plan_Classic and Chair", 17, Collections.singletonList(Equipment116Enums.NONE),
            true, Arrays.asList(Position116Enums.SEATED), 3, 0, 28){
        @Override
        public String getImportAliasName() {
            return "Chair Yoga";
        }
    },

    DUMBBELL_MODERATE("Dumbbell (moderate)", "OOG116_Dumbbell (moderate)", 18, Collections.singletonList(Equipment116Enums.DUMBBELL_MODERATE),
            true, Collections.emptyList(), -1, 14, 28),

    CARDIO_105("105 Cardio", "OOG116_105 Cardio", 19, Collections.singletonList(Equipment116Enums.NONE),
            false, Collections.emptyList(), -1, 14, 28),

    RECOVERY("Recovery", "OOG116_Recovery", 20, Collections.singletonList(Equipment116Enums.NONE),
            true, Arrays.asList(Position116Enums.SEATED, Position116Enums.STANDING, Position116Enums.LYING), -1, 0, 0),

    DUMBBELL_MIDWEIGHT("Dumbbell (midweight)", "OOG116_Dumbbell (midweight)", 21, Collections.singletonList(Equipment116Enums.DUMBBELL_MIDWEIGHT),
            true, Arrays.asList(Position116Enums.SEATED, Position116Enums.STANDING), 4, 14, 0),

    ;

    private final String name;
    private final String playlistType;
    private final Integer code;
    private final List<Equipment116Enums> equipmentList;
    private final List<Position116Enums> positionList;
    private final Integer maleDay;
    private final Integer femaleDay;
    private final boolean isSingle;
    private final Integer version;

    ExerciseType116Enums(String name,
                         String playlistType,
                         Integer code,
                         List<Equipment116Enums> equipmentList,
                         boolean isSingle,
                         List<Position116Enums> positionList,
                         Integer version,
                         Integer maleDay,
                         Integer femaleDay) {
        this.name = name;
        this.playlistType = playlistType;
        this.code = code;
        this.equipmentList = equipmentList;
        this.isSingle = isSingle;
        this.positionList = positionList;
        this.version = version;
        this.maleDay = maleDay;
        this.femaleDay = femaleDay;
    }

    public String getImportAliasName() {
        return this.name;
    }

    public static ExerciseType116Enums getByImportAliasName(String importAliasName) {

        for (ExerciseType116Enums value : ExerciseType116Enums.values()) {
            if (Objects.equals(value.getImportAliasName(), importAliasName)) {
                return value;
            }
        }
        return null;
    }

    public static ExerciseType116Enums getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (ExerciseType116Enums value : ExerciseType116Enums.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }


    public static ExerciseType116Enums getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (ExerciseType116Enums enums : values()) {
            if (code.equals(enums.code)) {
                return enums;
            }
        }
        return null;
    }

    //获取version小于等于传入的version的ExerciseType116Enums
    public static List<ExerciseType116Enums> getByVersion(Integer version) {
        if (null == version) {
            return new ArrayList<>();
        }
        List<ExerciseType116Enums> list = new ArrayList<>();
        for (ExerciseType116Enums enums : values()) {
            if (version >= enums.version && enums.version > 0) {
                list.add(enums);
            }
        }
        return list;
    }

    //返回排除传入枚举的所有ExerciseType116Enums的name
    public static List<String> eliminateExerciseType(ExerciseType116Enums... exerciseType116Enums) {
        List<String> allExerciseType = Arrays.stream(values()).map(ExerciseType116Enums::getName).collect(Collectors.toList());
        allExerciseType.removeAll(Arrays.stream(exerciseType116Enums).map(ExerciseType116Enums::getName).collect(Collectors.toList()));
        return allExerciseType;
    }

}
