package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * recovery category showType 枚举
 *
 * <AUTHOR>
 * @since 2025/07/11
 */
@Getter
public enum RecoveryCategory116ShowTypeEnums implements IEnumBase {
    /**
     * 展示样式为标签
     */
    LABEL(1, "Label"),
    /**
     * 展示样式为卡片
     */
    CARD(2, "Card"),
    /**
     * 展示样式为Grid
     */
    GRID(3, "Grid");

    @EnumValue
    private final Integer code;
    private final String name;

    RecoveryCategory116ShowTypeEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<RecoveryCategory116ShowTypeEnums> {
    }
}
