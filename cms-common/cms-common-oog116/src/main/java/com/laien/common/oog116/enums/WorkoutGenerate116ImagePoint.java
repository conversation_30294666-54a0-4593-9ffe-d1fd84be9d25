package com.laien.common.oog116.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/4
 */
@Getter
public enum WorkoutGenerate116ImagePoint {

    CHAIR_YOGA("Chair Yoga", Position116Enums.SEATED, ExerciseType116Enums.CHAIR_YOGA),
    GENTLE_CARDIO("Gentle Cardio", Position116Enums.STANDING, ExerciseType116Enums.GENTLE_CARDIO),
    WALKING_SEATED("Walking(Seated)", Position116Enums.SEATED, ExerciseType116Enums.WALKING),
    WALKING_STANDING("Walking(Standing)", Position116Enums.STANDING, ExerciseType116Enums.WALKING),
    DUMBBELL_SEATED("Dumbbell (lightweight)(Seated)", Position116Enums.SEATED, ExerciseType116Enums.DUMBBELL),
    DUMBBELL_STANDING("Dumbbell (lightweight)(Standing)", Position116Enums.STANDING, ExerciseType116Enums.DUMBBELL),
    RESISTANCE_BAND_SEATED("Resistance Band(Seated)", Position116Enums.SEATED, ExerciseType116Enums.RESISTANCE_BAND),
    RESISTANCE_BAND_STANDING("Resistance Band(Standing)", Position116Enums.STANDING, ExerciseType116Enums.RESISTANCE_BAND),
    DANCING_SEATED("Dancing(Seated)", Position116Enums.SEATED, ExerciseType116Enums.DANCING),
    DANCING_STANDING("Dancing(Standing)", Position116Enums.STANDING, ExerciseType116Enums.DANCING),
    TAI_CHI("Tai Chi", Position116Enums.STANDING, ExerciseType116Enums.TAI_CHI),
    GENTLE_CHAIR_YOGA("Gentle Chair Yoga", Position116Enums.SEATED, ExerciseType116Enums.GENTLE_CHAIR_YOGA),

    DUMBBELL_MIDWEIGHT_SEATED("Dumbbell (midweight)(Seated)", Position116Enums.SEATED, ExerciseType116Enums.DUMBBELL_MIDWEIGHT),
    DUMBBELL_MIDWEIGHT_STANDING("Dumbbell (midweight)(Standing)", Position116Enums.STANDING, ExerciseType116Enums.DUMBBELL_MIDWEIGHT)

    ;

    private final String name;
    private final Position116Enums position;
    private final ExerciseType116Enums exerciseType;

    WorkoutGenerate116ImagePoint(String name, Position116Enums position,ExerciseType116Enums exerciseType) {
        this.name = name;
        this.position = position;
        this.exerciseType = exerciseType;
    }

    public static WorkoutGenerate116ImagePoint get(Position116Enums position, ExerciseType116Enums exerciseType) {
        for (WorkoutGenerate116ImagePoint value : values()) {
            if (Objects.equals(value.position, position) && Objects.equals(value.exerciseType, exerciseType)) {
                return value;
            }
        }
        return null;
    }

}
