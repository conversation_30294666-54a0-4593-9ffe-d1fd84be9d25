package com.laien.common.oog116.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import com.laien.common.core.enums.mybatis.type.EnumBaseBitwiseHandler;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Recovery Category类型枚举
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
public enum RecoveryCategory116TypeEnums implements IEnumBase {

    JOINT_PAIN_RELIEF(1, "Joint Pain Relief", 10),
    BALANCE(1 << 1, "Balance", 11),
    FLEXIBILITY(1 << 2, "Flexibility", 12),
    MAINTAIN_STRENGTH(1 << 3, "Maintain Strength", 13),
    REDUCE_STRESS(1 << 4, "Reduce Stress", 14);

    @EnumValue
    private final Integer code;
    private final String name;
    private final Integer showCode;

    RecoveryCategory116TypeEnums(Integer code, String name, Integer showCode) {
        this.code = code;
        this.name = name;
        this.showCode = showCode;
    }

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * 枚举位运算List类型处理器，为了解决泛型消除的问题
     */
    public static class TypeHandler extends EnumBaseBitwiseHandler<RecoveryCategory116TypeEnums> {
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<RecoveryCategory116TypeEnums> {
    }

    public static List<RecoveryCategory116TypeEnums> getListExclude(RecoveryCategory116TypeEnums exclude) {
        return Arrays.stream(values())
                .filter(item -> item != exclude)
                .collect(Collectors.toList());
    }
}
