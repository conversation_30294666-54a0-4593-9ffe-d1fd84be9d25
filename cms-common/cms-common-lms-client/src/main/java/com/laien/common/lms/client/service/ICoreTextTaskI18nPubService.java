package com.laien.common.lms.client.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.component.BaseTextI18nModel;
import com.laien.common.domain.entity.CoreTextTaskI18nPub;
import com.laien.common.domain.enums.ProjCodeEnums;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ICoreTextTaskI18nPubService extends IService<CoreTextTaskI18nPub> {

    void translate(List<? extends AppTextCoreI18nModel> modelList, ProjCodeEnums projCode, LanguageEnums language);
}
