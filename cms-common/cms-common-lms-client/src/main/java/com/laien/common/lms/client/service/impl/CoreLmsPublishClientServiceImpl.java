package com.laien.common.lms.client.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.core.constant.RedisKeyConstant;
import com.laien.common.domain.entity.CoreLmsPublishLog;
import com.laien.common.domain.enums.PublishResultEnums;
import com.laien.common.lms.client.mapper.CoreLmsPublishLogClientMapper;
import com.laien.common.lms.client.service.ICoreLmsPublishClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文本任务多语言表 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreLmsPublishClientServiceImpl extends ServiceImpl<CoreLmsPublishLogClientMapper, CoreLmsPublishLog>
        implements ICoreLmsPublishClientService {

    private final RedissonClient redissonClient;

    @Override
    public Integer getLmsCurrentVersion() {
        RBucket<Object> bucket = redissonClient.getBucket(RedisKeyConstant.LMS_PUBLISH_VERSION);
        Object versionObj = bucket.get();
        if (ObjUtil.isNotNull(versionObj)) {
            return Integer.valueOf(versionObj.toString());
        }
        LambdaQueryWrapper<CoreLmsPublishLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoreLmsPublishLog::getDelFlag, 0)
                .eq(CoreLmsPublishLog::getResult, PublishResultEnums.Success.name());
        queryWrapper.orderByDesc(CoreLmsPublishLog::getId);
        queryWrapper.last(" limit 1");
        CoreLmsPublishLog coreLmsPublishLog = this.getOne(queryWrapper);
        Integer version = ObjUtil.isNotNull(coreLmsPublishLog)  ? coreLmsPublishLog.getVersion() : 0;
        bucket.set(version);
        return version;
    }

}
