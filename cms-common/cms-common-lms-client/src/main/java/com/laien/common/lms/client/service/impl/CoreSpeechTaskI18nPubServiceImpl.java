package com.laien.common.lms.client.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.annotation.AppAudioSingleTranslateField;
import com.laien.common.domain.annotation.AppAudioTranslateField;
import com.laien.common.domain.bo.AppAudioSingleTranslateFieldBO;
import com.laien.common.domain.bo.AppAudioTranslateFieldBO;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.common.domain.entity.CoreSpeechTaskI18n;
import com.laien.common.domain.entity.CoreSpeechTaskI18nPub;
import com.laien.common.domain.entity.CoreVoiceConfigTemplateI18n;
import com.laien.common.domain.entity.CoreVoiceTemplateI18n;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.utils.CoreI18nUtil;
import com.laien.common.lms.client.mapper.CoreSpeechTaskI18nPubMapper;
import com.laien.common.lms.client.service.ICoreLmsPublishClientService;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.client.service.ICoreVoiceConfigTemplateI18nClientService;
import com.laien.common.lms.client.service.ICoreVoiceTemplateI18nClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@Service
public class CoreSpeechTaskI18nPubServiceImpl extends ServiceImpl<CoreSpeechTaskI18nPubMapper, CoreSpeechTaskI18nPub> implements ICoreSpeechTaskI18nPubService {

    @Resource
    private ICoreLmsPublishClientService coreLmsPublishClientService;
    @Resource
    private ICoreVoiceConfigTemplateI18nClientService coreVoiceConfigTemplateI18nClientService;
    @Resource
    private ICoreVoiceTemplateI18nClientService templateI18nClientService;

    // 本地缓存 voiceTemplateI18nMap，避免重复查询数据库
    private static final Map<Integer, CoreVoiceTemplateI18n> voiceTemplateI18nCache = new java.util.concurrent.ConcurrentHashMap<>();


    @Override
    public void translate(List<? extends AppAudioCoreI18nModel> modelList, ProjCodeEnums projCode, LanguageEnums language) {
        if(null == language){
            log.error("core speech translate single language is null");
            return;
        }
        if(LanguageEnums.EN == language){
            return;
        }
        if(CollUtil.isEmpty(modelList)){
            return;
        }
        Set<Integer> voiceConfigI18nSet = modelList.stream().map(AppAudioCoreI18nModel::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(voiceConfigI18nSet)) {
            return;
        }
        List<CoreVoiceConfigTemplateI18n> voiceConfigTemplateI18nList = coreVoiceConfigTemplateI18nClientService.query(voiceConfigI18nSet);
        if (CollUtil.isEmpty(voiceConfigTemplateI18nList)) {
            return;
        }
        List<AppAudioSingleTranslateFieldBO> fieldList = createSingleField(modelList, voiceConfigTemplateI18nList,language);
        Map<String, List<AppAudioSingleTranslateFieldBO>> md5FieldGroup = fieldList.stream().collect(Collectors.groupingBy(AppAudioSingleTranslateFieldBO::getTextMd5));
        List<CoreSpeechTaskI18nPub> taskI18nList = query(md5FieldGroup.keySet(), CollUtil.newHashSet(language),coreLmsPublishClientService.getLmsCurrentVersion(), voiceConfigI18nSet, voiceConfigTemplateI18nList);
        if(CollUtil.isEmpty(taskI18nList)){
            return;
        }
        // key：text md5
        // value：key:ProjCodeEnums，value：coreTextTaskI18n
        Map<String, Map<ProjCodeEnums, List<CoreSpeechTaskI18nPub>>> md5TaskGroup = new HashMap<>();
        for (CoreSpeechTaskI18nPub taskI18n : taskI18nList) {
            String textMd5 = taskI18n.getTextMd5();
            Map<ProjCodeEnums, List<CoreSpeechTaskI18nPub>> projCodeTaskGroup = md5TaskGroup.getOrDefault(textMd5, new HashMap<>());
            md5TaskGroup.put(textMd5, projCodeTaskGroup);
            for (ProjCodeEnums projCodeEnum : taskI18n.getProjCode()) {
                List<CoreSpeechTaskI18nPub> taskList = projCodeTaskGroup.getOrDefault(projCodeEnum, new ArrayList<>());
                projCodeTaskGroup.put(projCodeEnum, taskList);
                taskList.add(taskI18n);
            }
        }
        for (Map.Entry<String, List<AppAudioSingleTranslateFieldBO>> entry : md5FieldGroup.entrySet()) {
            Map<ProjCodeEnums, List<CoreSpeechTaskI18nPub>> projCodeTaskGroup = md5TaskGroup.get(entry.getKey());
            if (CollUtil.isEmpty(projCodeTaskGroup)) {
                continue;
            }
            List<CoreSpeechTaskI18nPub> coreTaskI18nList = projCodeTaskGroup.getOrDefault(projCode, new ArrayList<>());

            List<CoreSpeechTaskI18nPub> commonSpeechTaskList = projCodeTaskGroup.get(ProjCodeEnums.COMMON);
            if (CollUtil.isNotEmpty(commonSpeechTaskList)) {
                Set<String> speechTaskKeyList = new HashSet<>();
                for (CoreSpeechTaskI18nPub task : coreTaskI18nList) {
                    speechTaskKeyList.add(getSpeechTaskKey(task));
                }
                for (CoreSpeechTaskI18nPub task : commonSpeechTaskList) {
                    String speechTaskKey = getSpeechTaskKey(task);
                    if (!speechTaskKeyList.contains(speechTaskKey)) {
                        coreTaskI18nList.add(task);
                    }
                }
            }
            if (CollUtil.isEmpty(coreTaskI18nList)) {
                continue;
            }
            for (AppAudioSingleTranslateFieldBO fieldBO : entry.getValue()) {
                Integer voiceConfigTemplateI18nId = fieldBO.getVoiceConfigTemplateI18nId();
                if(null == voiceConfigTemplateI18nId){
                    log.error("single voice config template is null,coreVoiceConfigI18nId:{}, language:{}", fieldBO.getCoreVoiceConfigI18nId(), language);
                    continue;
                }

                for (CoreSpeechTaskI18nPub taskI18nPub : coreTaskI18nList) {
                    if (!Objects.equals(fieldBO.getCoreVoiceConfigI18nId(), taskI18nPub.getCoreVoiceConfigI18nId())) {
                        continue;
                    }
                    if (!voiceConfigTemplateI18nId.equals(taskI18nPub.getCoreVoiceTemplateI18nId())) {
                        continue;
                    }
                    CoreI18nUtil.setFieldValue(fieldBO.getModel(), fieldBO.getTextFieldName(), taskI18nPub.getTranslationText());
                    String durationFieldName = fieldBO.getDurationFieldName();
                    if(StrUtil.isNotBlank(durationFieldName)){
                        CoreI18nUtil.setFieldValue(fieldBO.getModel(), durationFieldName, taskI18nPub.getTranslationDuration());
                    }
                    String urlFieldName = fieldBO.getUrlFieldName();
                    if(StrUtil.isNotBlank(urlFieldName)){
                        CoreI18nUtil.setFieldValue(fieldBO.getModel(), urlFieldName, taskI18nPub.getTranslationAudioUrl());
                    }
                }
            }
        }
    }

    @Override
    public void translate(List<? extends AppAudioCoreI18nModel> modelList, ProjCodeEnums projCode, Set<LanguageEnums> languageSet) {

        if(CollUtil.isEmpty(languageSet)){
            log.error("core speech translate language is empty");
            return;
        }
        languageSet.remove(LanguageEnums.EN);
        if(CollUtil.isEmpty(languageSet)){
            return;
        }

        if(CollUtil.isEmpty(modelList)){
            return;
        }
        Set<Integer> voiceConfigI18nSet = modelList.stream().map(AppAudioCoreI18nModel::getCoreVoiceConfigI18nId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(voiceConfigI18nSet)) {
            return;
        }
        List<CoreVoiceConfigTemplateI18n> voiceConfigTemplateI18nList = coreVoiceConfigTemplateI18nClientService.query(voiceConfigI18nSet);
        if (CollUtil.isEmpty(voiceConfigTemplateI18nList)) {
            return;
        }
        List<AppAudioTranslateFieldBO> fieldList = createField(modelList, voiceConfigTemplateI18nList);
        if(CollUtil.isEmpty(fieldList)){
            return;
        }
        Map<String, List<AppAudioTranslateFieldBO>> md5FieldGroup = fieldList.stream().collect(Collectors.groupingBy(AppAudioTranslateFieldBO::getTextMd5));
        List<CoreSpeechTaskI18nPub> taskI18nList = query(md5FieldGroup.keySet(), languageSet,coreLmsPublishClientService.getLmsCurrentVersion(), voiceConfigI18nSet, voiceConfigTemplateI18nList);
        if(CollUtil.isEmpty(taskI18nList)){
            return;
        }
        // key：text md5
        // value：key:ProjCodeEnums，value：coreTextTaskI18n
        Map<String, Map<ProjCodeEnums, List<CoreSpeechTaskI18nPub>>> md5TaskGroup = new HashMap<>();
        for (CoreSpeechTaskI18nPub taskI18n : taskI18nList) {
            String textMd5 = taskI18n.getTextMd5();
            Map<ProjCodeEnums, List<CoreSpeechTaskI18nPub>> projCodeTaskGroup = md5TaskGroup.getOrDefault(textMd5, new HashMap<>());
            md5TaskGroup.put(textMd5, projCodeTaskGroup);
            for (ProjCodeEnums projCodeEnum : taskI18n.getProjCode()) {
                List<CoreSpeechTaskI18nPub> taskList = projCodeTaskGroup.getOrDefault(projCodeEnum, new ArrayList<>());
                projCodeTaskGroup.put(projCodeEnum, taskList);
                taskList.add(taskI18n);
            }
        }
        for (Map.Entry<String, List<AppAudioTranslateFieldBO>> entry : md5FieldGroup.entrySet()) {
            Map<ProjCodeEnums, List<CoreSpeechTaskI18nPub>> projCodeTaskGroup = md5TaskGroup.get(entry.getKey());
            if (CollUtil.isEmpty(projCodeTaskGroup)) {
                continue;
            }
            List<CoreSpeechTaskI18nPub> coreTaskI18nList = projCodeTaskGroup.getOrDefault(projCode, new ArrayList<>());

            List<CoreSpeechTaskI18nPub> commonSpeechTaskList = projCodeTaskGroup.get(ProjCodeEnums.COMMON);
            if (CollUtil.isNotEmpty(commonSpeechTaskList)) {
                Set<String> speechTaskKeyList = new HashSet<>();
                for (CoreSpeechTaskI18nPub task : coreTaskI18nList) {
                    speechTaskKeyList.add(getSpeechTaskKey(task));
                }
                for (CoreSpeechTaskI18nPub task : commonSpeechTaskList) {
                    String speechTaskKey = getSpeechTaskKey(task);
                    if (!speechTaskKeyList.contains(speechTaskKey)) {
                        coreTaskI18nList.add(task);
                    }
                }
            }
            if (CollUtil.isEmpty(coreTaskI18nList)) {
                continue;
            }
            for (AppAudioTranslateFieldBO fieldBO : entry.getValue()) {
                List<AudioTranslateResultModel> resultModelList = new ArrayList<>();
                Set<Integer> voiceConfigTemplateI18nIdSet = fieldBO.getVoiceConfigTemplateI18nIdSet();
                if (CollUtil.isEmpty(voiceConfigTemplateI18nIdSet) || voiceConfigTemplateI18nIdSet.size() < languageSet.size()) {
                    log.error("voice config template is not enough,voiceConfigTemplateIdSet:{}, languageSet:{}", voiceConfigTemplateI18nIdSet, languageSet);
                    continue;
                }
                for (CoreSpeechTaskI18nPub taskI18nPub : coreTaskI18nList) {
                    AudioTranslateResultModel resultModel = new AudioTranslateResultModel();
                    if (!Objects.equals(fieldBO.getCoreVoiceConfigI18nId(), taskI18nPub.getCoreVoiceConfigI18nId())) {
                        continue;
                    }
                    if (!voiceConfigTemplateI18nIdSet.contains(taskI18nPub.getCoreVoiceTemplateI18nId())) {
                        continue;
                    }
                    resultModel.setId(taskI18nPub.getId())
                            .setGender(taskI18nPub.getGender())
                            .setLanguage(taskI18nPub.getLanguage())
                            .setText(taskI18nPub.getTranslationText())
                            .setAudioUrl(taskI18nPub.getTranslationAudioUrl())
                            .setDuration(taskI18nPub.getTranslationDuration());
                    resultModelList.add(resultModel);
                }
                CoreI18nUtil.setFieldValue(fieldBO.getModel(), fieldBO.getAudioModelFieldName(), resultModelList);
            }
        }
    }

    private String getSpeechTaskKey(CoreSpeechTaskI18n speechTaskI18n) {
        String keyTemplate = "%s-%s-%s-%s";
        return String.format(keyTemplate,
                speechTaskI18n.getCoreVoiceConfigI18nId(),
                speechTaskI18n.getCoreVoiceTemplateI18nId(),
                speechTaskI18n.getLanguage(),
                speechTaskI18n.getTextMd5()
        );
    }
    private List<AppAudioSingleTranslateFieldBO> createSingleField(List<? extends AppAudioCoreI18nModel> modelList, List<CoreVoiceConfigTemplateI18n> voiceConfigTemplateI18nList, LanguageEnums language) {
        Map<Integer, CoreVoiceConfigTemplateI18n> configTemplateGroup = voiceConfigTemplateI18nList
                .stream()
                .filter(configTemplateI18n -> {
                    CoreVoiceTemplateI18n coreVoiceTemplateI18n = this.getVoiceTemplateI18nMap().get(configTemplateI18n.getCoreVoiceTemplateI18nId());
                    if (null == coreVoiceTemplateI18n) {
                        log.error("core voice template i18n is null,coreVoiceTemplateI18nId:{}", configTemplateI18n.getCoreVoiceTemplateI18nId());
                        return false;
                    }
                    return Objects.equals(coreVoiceTemplateI18n.getLanguage(), language);
                })
                .collect(Collectors.toMap(CoreVoiceConfigTemplateI18n::getCoreVoiceConfigI18nId, Function.identity(),(existing,replacement)->replacement));
        List<AppAudioSingleTranslateFieldBO> fieldList = new ArrayList<>(modelList.size() * 10);
        MD5 md5 = MD5.create();
        for (AppAudioCoreI18nModel model : modelList) {
            for (Field field : ReflectUtil.getFields(model.getClass())) {
                AppAudioSingleTranslateField audioTranslateAnnotation = field.getAnnotation(AppAudioSingleTranslateField.class);
                if (null == audioTranslateAnnotation) {
                    continue;
                }
                String value = CoreI18nUtil.getFieldValue(model, field);
                Integer coreVoiceConfigI18nId = model.getCoreVoiceConfigI18nId();
                if(StrUtil.isBlank(value) || null == coreVoiceConfigI18nId){
                    continue;
                }
                AppAudioSingleTranslateFieldBO audioTranslateFieldBO = new AppAudioSingleTranslateFieldBO();
                audioTranslateFieldBO.setModel(model)
                        .setDurationFieldName(audioTranslateAnnotation.durationFieldName())
                        .setTextFieldName(field.getName())
                        .setUrlFieldName(audioTranslateAnnotation.urlFieldName())
                        .setTextMd5(CoreI18nUtil.getMd5(value, md5))
                        .setVoiceConfigTemplateI18nId(configTemplateGroup.get(coreVoiceConfigI18nId).getCoreVoiceTemplateI18nId())
                        .setCoreVoiceConfigI18nId(coreVoiceConfigI18nId);
                fieldList.add(audioTranslateFieldBO);
            }
        }
        return fieldList;
    }

    private List<AppAudioTranslateFieldBO> createField(List<? extends AppAudioCoreI18nModel> modelList, List<CoreVoiceConfigTemplateI18n> voiceConfigTemplateI18nList) {
        Map<Integer, Set<Integer>> configTemplateGroup = voiceConfigTemplateI18nList
                .stream()
                .collect(Collectors.groupingBy(
                        CoreVoiceConfigTemplateI18n::getCoreVoiceConfigI18nId,
                        Collectors.mapping(CoreVoiceConfigTemplateI18n::getCoreVoiceTemplateI18nId, Collectors.toSet()
                        )));
        MD5 md5 = MD5.create();
        List<AppAudioTranslateFieldBO> fieldList = new ArrayList<>(modelList.size() * 10);
        for (AppAudioCoreI18nModel model : modelList) {
            for (Field field : ReflectUtil.getFields(model.getClass())) {
                AppAudioTranslateField audioTranslateAnnotation = field.getAnnotation(AppAudioTranslateField.class);
                if (null == audioTranslateAnnotation) {
                    continue;
                }
                String value = CoreI18nUtil.getFieldValue(model, field);
                Integer coreVoiceConfigI18nId = model.getCoreVoiceConfigI18nId();
                if(StrUtil.isBlank(value) || null == coreVoiceConfigI18nId){
                    continue;
                }
                AppAudioTranslateFieldBO audioTranslateFieldBO = new AppAudioTranslateFieldBO();
                audioTranslateFieldBO.setModel(model)
                        .setAudioModelFieldName(audioTranslateAnnotation.resultFieldName())
                        .setTextMd5(CoreI18nUtil.getMd5(value, md5))
                        .setVoiceConfigTemplateI18nIdSet(configTemplateGroup.get(coreVoiceConfigI18nId))
                        .setCoreVoiceConfigI18nId(coreVoiceConfigI18nId);
                fieldList.add(audioTranslateFieldBO);
            }
        }
        return fieldList;
    }


    private List<CoreSpeechTaskI18nPub> query(Collection<String> md5s, Set<LanguageEnums> languageSet, Integer coreLmsPublishVersion, Collection<Integer> voiceConfigI18ns, List<CoreVoiceConfigTemplateI18n> voiceConfigTemplateI18nList) {
        List<List<String>> md5BatchList = Lists.partition(new ArrayList<>(md5s), 100);
        List<CoreSpeechTaskI18nPub> allTaskList = new ArrayList<>(md5s.size() * 2);
        for (List<String> md5List : md5BatchList) {
            LambdaQueryWrapper<CoreSpeechTaskI18nPub> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(CoreSpeechTaskI18nPub::getTextMd5, md5List)
                    .eq(CoreSpeechTaskI18nPub::getVersion, coreLmsPublishVersion)
                    .in(CoreSpeechTaskI18n::getCoreVoiceTemplateI18nId,voiceConfigTemplateI18nList.stream().map(CoreVoiceConfigTemplateI18n::getCoreVoiceTemplateI18nId).collect(Collectors.toSet()))
                    .in(CoreSpeechTaskI18n::getCoreVoiceConfigI18nId, voiceConfigI18ns)
                    .in(CoreSpeechTaskI18nPub::getLanguage, languageSet);
            List<CoreSpeechTaskI18nPub> taskI18nList = list(wrapper);
            if(CollUtil.isNotEmpty(taskI18nList)){
                allTaskList.addAll(taskI18nList);
            }
        }
        return allTaskList;
    }

    private Map<Integer, CoreVoiceTemplateI18n> getVoiceTemplateI18nMap() {
        if (voiceTemplateI18nCache.isEmpty()) {
            synchronized (voiceTemplateI18nCache) {
                if (voiceTemplateI18nCache.isEmpty()) {
                    List<CoreVoiceTemplateI18n> list = templateI18nClientService.list();
                    if (CollUtil.isNotEmpty(list)) {
                        voiceTemplateI18nCache.putAll(
                                list.stream().collect(Collectors.toMap(CoreVoiceTemplateI18n::getId, Function.identity()))
                        );
                    }
                }
            }
        }
        return voiceTemplateI18nCache;
    }


    @SuppressWarnings("unchecked")
    @Override
    public Map<Object, Map<LanguageEnums,AudioTranslateResultModel>> getI18nResultGroupByKey(List<? extends BaseAudioI18nModel> i18nModelList,
                                                                                             Collection<String> languageList,
                                                                                             ProjCodeEnums projCodeEnums) {
        this.translate((List<AppAudioCoreI18nModel>)(List<?>) i18nModelList, projCodeEnums,
                CollUtil.newHashSet(LanguageEnums.getLanguageEnums(languageList)));
        return i18nModelList.stream()
                .filter(sound -> sound.getResult() != null) // 防御性空指针
                .collect(Collectors.toMap(
                        BaseAudioI18nModel::getUniqueKey, // 外层 key = id
                        sound -> sound.getResult().stream()
                                .filter(r -> r.getLanguage() != null) // 防御性过滤
                                .collect(Collectors.toMap(
                                        AudioTranslateResultModel::getLanguage, // 内层 key = language
                                        r -> r,
                                        (a, b) -> b // 如果语言重复，保留后者
                                )),
                        (a, b) -> b // 如果有相同的 id，保留后者（可根据实际调整）
                ));
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T extends BaseAudioI18nModel> Map<Object, T> getI18nModelGroupByKey(List<T> i18nModelList,
                                                                                Collection<String> languageList,
                                                                                ProjCodeEnums projCodeEnums) {
        this.translate((List<AppAudioCoreI18nModel>)(List<?>) i18nModelList, projCodeEnums,
                CollUtil.newHashSet(LanguageEnums.getLanguageEnums(languageList)));
        return i18nModelList.stream()
                .filter(Objects::nonNull) // 防御性空指针
                .collect(Collectors.toMap(
                        BaseAudioI18nModel::getUniqueKey, // 外层 key = id
                        t -> t, // 直接返回模型
                        (a, b) -> b // 如果有相同的 id，保留后者（可根据实际调整）
                ));
    }
}
