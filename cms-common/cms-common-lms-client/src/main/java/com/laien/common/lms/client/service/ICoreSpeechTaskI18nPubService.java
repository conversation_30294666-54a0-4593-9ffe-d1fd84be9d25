package com.laien.common.lms.client.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.component.BaseAudioI18nModel;
import com.laien.common.domain.entity.CoreSpeechTaskI18nPub;
import com.laien.common.domain.enums.ProjCodeEnums;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ICoreSpeechTaskI18nPubService extends IService<CoreSpeechTaskI18nPub> {


    void translate(List<? extends AppAudioCoreI18nModel> modelList, ProjCodeEnums projCode, Set<LanguageEnums> languageSet);

    void translate(List<? extends AppAudioCoreI18nModel> modelList, ProjCodeEnums projCode, LanguageEnums language);

    Map<Object, Map<LanguageEnums, AudioTranslateResultModel>> getI18nResultGroupByKey(List<? extends BaseAudioI18nModel> i18nModelList, Collection<String> languageList,ProjCodeEnums projCodeEnums);

    <T extends BaseAudioI18nModel> Map<Object, T> getI18nModelGroupByKey(List<T> i18nModelList, Collection<String> languageList,ProjCodeEnums projCodeEnums);
}
