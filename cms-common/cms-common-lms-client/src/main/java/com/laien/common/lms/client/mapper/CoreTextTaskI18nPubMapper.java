package com.laien.common.lms.client.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.laien.common.domain.entity.CoreTextTaskI18nPub;
import com.laien.common.domain.request.CoreTextTask18nPageReq;
import com.laien.common.domain.response.CoreTextTaskI18nPageVO;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface CoreTextTaskI18nPubMapper extends BaseMapper<CoreTextTaskI18nPub> {

    IPage<CoreTextTaskI18nPageVO> pageGroupByTasks(IPage<?> page,
                                                   @Param("req") CoreTextTask18nPageReq req);
}
