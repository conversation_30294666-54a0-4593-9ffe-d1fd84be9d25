package com.laien.common.lms.client.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.common.domain.entity.CoreVoiceTemplateI18n;
import com.laien.common.lms.client.mapper.CoreVoiceTemplateI18nClientMapper;
import com.laien.common.lms.client.service.ICoreVoiceTemplateI18nClientService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 *
 *
 * <p>
 *
 * <p>
 * <AUTHOR>
 * @since 2025/07/07
 * */
@Service
public class CoreVoiceTemplateI18nClientServiceImpl
        extends ServiceImpl<CoreVoiceTemplateI18nClientMapper, CoreVoiceTemplateI18n>
        implements ICoreVoiceTemplateI18nClientService {

}
