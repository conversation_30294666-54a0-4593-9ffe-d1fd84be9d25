package com.laien.common.lms.client.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.bo.AppTextTranslateFieldBO;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.entity.CoreTextTaskI18nPub;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.common.domain.utils.CoreI18nUtil;
import com.laien.common.lms.client.mapper.CoreTextTaskI18nPubMapper;
import com.laien.common.lms.client.service.ICoreLmsPublishClientService;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreTextTaskI18nPubServiceImpl extends ServiceImpl<CoreTextTaskI18nPubMapper, CoreTextTaskI18nPub> implements ICoreTextTaskI18nPubService {

    @Resource
    private ICoreLmsPublishClientService coreLmsPublishService;

    @Override
    public void translate(List<? extends AppTextCoreI18nModel> modelList, ProjCodeEnums projCode, LanguageEnums language) {

        if(null == language){
            log.error("text translate language is null");
            return;
        }
        if(language == LanguageEnums.EN){
            return;
        }

        if(CollUtil.isEmpty(modelList)){
            return;
        }
        List<AppTextTranslateFieldBO> fieldList = createField(modelList);
        if(CollUtil.isEmpty(fieldList)){
            return;
        }
        Map<String, List<AppTextTranslateFieldBO>> md5FieldGroup = fieldList.stream().collect(Collectors.groupingBy(AppTextTranslateFieldBO::getTextMd5));
        for (AppTextTranslateFieldBO fieldBO : fieldList) {
            TranslationTaskTypeEnums type = fieldBO.getType();
            if(type != TranslationTaskTypeEnums.MULTIPLE_TEXT){
                String textMd5 = fieldBO.getTextMd5();
                List<AppTextTranslateFieldBO> textFieldList = md5FieldGroup.getOrDefault(textMd5, new ArrayList<>());
                textFieldList.add(fieldBO);
                md5FieldGroup.put(textMd5, textFieldList);
            }
            List<String> multipleTextMd5List = fieldBO.getMultipleTextMd5List();
            if(CollUtil.isEmpty(multipleTextMd5List)){
                continue;
            }
            for (String textMd5 : multipleTextMd5List) {
                List<AppTextTranslateFieldBO> textFieldList = md5FieldGroup.getOrDefault(textMd5, new ArrayList<>());
                textFieldList.add(fieldBO);
                md5FieldGroup.put(textMd5, textFieldList);
            }
        }
        List<CoreTextTaskI18nPub> taskI18nList = query(md5FieldGroup.keySet(), language, coreLmsPublishService.getLmsCurrentVersion());
        if(CollUtil.isEmpty(taskI18nList)){
            return;
        }
        // key：text md5
        // value：key:ProjCodeEnums，value：coreTextTaskI18n
        Map<String, Map<ProjCodeEnums, CoreTextTaskI18nPub>> md5TaskGroup = new HashMap<>();
        for (CoreTextTaskI18nPub taskI18n : taskI18nList) {
            String textMd5 = taskI18n.getTextMd5();
            Map<ProjCodeEnums, CoreTextTaskI18nPub> projCodeTaskGroup = md5TaskGroup.getOrDefault(textMd5, new HashMap<>());
            md5TaskGroup.put(textMd5, projCodeTaskGroup);
            for (ProjCodeEnums projCodeEnum : taskI18n.getProjCode()) {
                projCodeTaskGroup.put(projCodeEnum, taskI18n);
            }
        }
        Set<AppTextTranslateFieldBO> multipleTextFieldList = new HashSet<>(64);
        for (Map.Entry<String, List<AppTextTranslateFieldBO>> entry : md5FieldGroup.entrySet()) {
            String textMd5 = entry.getKey();
            Map<ProjCodeEnums, CoreTextTaskI18nPub> projCodeTaskGroup = md5TaskGroup.get(textMd5);
            if (CollUtil.isEmpty(projCodeTaskGroup)) {
                continue;
            }
            CoreTextTaskI18nPub coreTextTaskI18n = projCodeTaskGroup.get(projCode);
            if (coreTextTaskI18n == null) {
                coreTextTaskI18n = projCodeTaskGroup.get(ProjCodeEnums.COMMON);
            }
            if (coreTextTaskI18n == null) {
                continue;
            }
            for (AppTextTranslateFieldBO fieldBO : entry.getValue()) {
                TranslationTaskTypeEnums type = fieldBO.getType();
                String translationText = coreTextTaskI18n.getTranslationText();
                if (StrUtil.isBlank(translationText)) {
                    log.error("translationText is empty,text md5:{}", fieldBO.getTextMd5());
                    continue;
                }
                if (type != TranslationTaskTypeEnums.MULTIPLE_TEXT) {
                    CoreI18nUtil.setFieldValue(fieldBO.getModel(), fieldBO.getFieldName(), translationText);
                    continue;
                }
                fieldBO.addMultipleTranslateTextMap(translationText, textMd5);
                multipleTextFieldList.add(fieldBO);
            }
        }
        for (AppTextTranslateFieldBO fieldBO : multipleTextFieldList) {
            Map<String, String> multipleTranslateTextMap = fieldBO.getMultipleTranslateTextMap();
            List<String> multipleTextMd5List = fieldBO.getMultipleTextMd5List();
            if(CollUtil.isEmpty(multipleTranslateTextMap) || CollUtil.isEmpty(multipleTextMd5List)){
                continue;
            }
            List<String> translateTextList = new ArrayList<>();
            for (String textMd5 : multipleTextMd5List) {
                String translateText = multipleTranslateTextMap.get(textMd5);
                if (StrUtil.isBlank(translateText)) {
                    log.error("translateText is empty,text md5:{}", textMd5);
                    continue;
                }
                translateTextList.add(translateText);
            }
            CoreI18nUtil.setFieldValue(fieldBO.getModel(), fieldBO.getFieldName(), CollUtil.join(translateTextList, fieldBO.getMultiTextDelimiter()));
        }
    }

    private List<AppTextTranslateFieldBO> createField(List<? extends AppTextCoreI18nModel> modelList) {
        List<AppTextTranslateFieldBO> fieldList = new ArrayList<>(modelList.size() * 10);
        MD5 md5 = MD5.create();
        for (AppTextCoreI18nModel model : modelList) {
            for (Field field : ReflectUtil.getFields(model.getClass())) {
                AppTextTranslateField textTranslateAnnotation = field.getAnnotation(AppTextTranslateField.class);
                if (null == textTranslateAnnotation) {
                    continue;
                }
                String value = CoreI18nUtil.getFieldValue(model, field);
                if(StrUtil.isBlank(value)){
                    continue;
                }
                AppTextTranslateFieldBO textTranslateFieldBO = new AppTextTranslateFieldBO();
                TranslationTaskTypeEnums taskType = textTranslateAnnotation.type();
                String delimiter = textTranslateAnnotation.multiTextDelimiter();
                textTranslateFieldBO.setType(taskType);
                if (TranslationTaskTypeEnums.MULTIPLE_TEXT == taskType) {
                    List<String> textList = StrUtil.split(value, delimiter, true, true);
                    if (CollUtil.isNotEmpty(textList)) {
                        List<String> multipleTextMd5List = new ArrayList<>();
                        for (String text : textList) {
                            multipleTextMd5List.add(CoreI18nUtil.getMd5(text, md5));
                        }
                        textTranslateFieldBO.setMultipleTextMd5List(multipleTextMd5List);
                    }
                }
                String valueMd5 = CoreI18nUtil.getMd5(value, md5);
                textTranslateFieldBO.setMultiTextDelimiter(delimiter)
                        .setModel(model)
                        .setFieldName(field.getName())
                        .setTextMd5(valueMd5);
                fieldList.add(textTranslateFieldBO);
            }
        }
        return fieldList;
    }

    private List<CoreTextTaskI18nPub> query(Collection<String> md5s, LanguageEnums language,Integer coreLmsPublishVersion) {
        List<List<String>> md5BatchList = Lists.partition(new ArrayList<>(md5s), 100);
        List<CoreTextTaskI18nPub> allTaskList = new ArrayList<>(md5s.size() * 2);
        for (List<String> md5List : md5BatchList) {
            LambdaQueryWrapper<CoreTextTaskI18nPub> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(CoreTextTaskI18nPub::getTextMd5, md5List)
                    .eq(CoreTextTaskI18nPub::getVersion, coreLmsPublishVersion)
                    .eq(CoreTextTaskI18nPub::getLanguage, language);
            List<CoreTextTaskI18nPub> taskI18nList = list(wrapper);
            if(CollUtil.isNotEmpty(taskI18nList)){
                allTaskList.addAll(taskI18nList);
            }
        }
        return allTaskList;
    }

}
