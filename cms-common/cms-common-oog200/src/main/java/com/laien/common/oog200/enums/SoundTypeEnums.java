package com.laien.common.oog200.enums;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;
@Getter
@AllArgsConstructor
public enum SoundTypeEnums implements IEnumBase {
    CHAIR_YOGA(1, "Chair Yoga",
            ListUtil.of(SoundSubTypeEnums.COMPLETE, SoundSubTypeEnums.CUE_PROMPTS)),
    CLASSIC_YOGA(2, "Classic Yoga",new ArrayList<>()
            ),
    WALL_PILATES(3, "Wall Pilates",
            new ArrayList<>()),
    POSE_LIBRARY(4, "Pose Library",
            new ArrayList<>()),
            ;


    @EnumValue
    private final Integer code;
    private final String name;
    private final List<SoundSubTypeEnums> soundSubTypes;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<SoundTypeEnums> {
    }
}
