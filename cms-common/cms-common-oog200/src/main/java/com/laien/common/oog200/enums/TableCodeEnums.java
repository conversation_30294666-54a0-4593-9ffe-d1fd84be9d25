/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.common.oog200.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.laien.common.core.enums.IEnumBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/**
 * <p>区分不同缓存数据对应的表 </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Getter
@AllArgsConstructor
public enum TableCodeEnums implements IEnumBase {

    // 表名
    PROJ_SOUND(1,"proj_sound"),
    ;
    @EnumValue
    private final Integer code;
    private final String name;

    @Override
    public String getDisplayName() {
        return this.getName();
    }

    public static class Deserializer extends JsonDeserializer<TableCodeEnums> {

        @Override
        public TableCodeEnums deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {

            int code = jsonParser.getIntValue();
            return Arrays.stream(TableCodeEnums.values())
                    .filter(t-> Objects.equals(t.getCode(),code)).findFirst()
                    .orElseThrow(()->new IllegalArgumentException("TableCodeEnums code is not exist"));
        }
    }

    public static TableCodeEnums get(Integer code) {
        return Arrays.stream(values()).filter(item -> Objects.equals(item.getCode(),code)).findFirst().orElse(null);
    }
}
