package com.laien.common.oog200.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@Getter
public enum SpecialLimitEnum {
    ALL_GOOD(0, "All Good"),
    SENSITIVE_WRIST(1, "Sensitive Wrist"),
    BACK_PAIN(2, "Back Pain"),
    KNEE_ISSUES(3, "Knee Issues"),
    OVERWEIGHT(4, "Overweight"),
    ELDERLY_NO(5, "Elderly No"),
    NO_PLANK(6, "No plank"),
    PREGNANCY_OR_POSTPARTUM(7, "Pregnancy or postpartum"),
    ;
    private final Integer code;
    private final String name;

    SpecialLimitEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
