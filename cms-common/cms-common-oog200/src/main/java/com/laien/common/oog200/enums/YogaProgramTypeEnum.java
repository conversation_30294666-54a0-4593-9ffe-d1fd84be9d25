package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/12/17 21:17
 *
 */
@Getter
public enum YogaProgramTypeEnum implements IEnumBase {

    BROWSE_BY_GOAL(101, "Browse by Goal", "Browse by Goal"),
    BROWSE_BY_STYLE(201, "Browse by Style", "Browse by Style"),
    FEATURED(301, "Featured", "Featured"),
    BEGINNER_PATH(401, "Beginner Path", "Beginner Path");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    YogaProgramTypeEnum(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static YogaProgramTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static YogaProgramTypeEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
