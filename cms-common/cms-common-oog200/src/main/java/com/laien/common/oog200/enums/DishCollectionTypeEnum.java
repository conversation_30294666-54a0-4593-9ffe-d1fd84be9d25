package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * Author:  hhl
 * Date:  2024/12/31 14:34
 */
@Getter
public enum DishCollectionTypeEnum {

    SMOOTHIE(101, "Smoothie");

    @EnumValue
    private final Integer code;
    private final String name;

    DishCollectionTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DishCollectionTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

}
