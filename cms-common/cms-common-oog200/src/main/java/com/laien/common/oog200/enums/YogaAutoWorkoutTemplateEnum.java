package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/9/25
 */
@Getter
public enum YogaAutoWorkoutTemplateEnum {

    // type
    CLASSIC_YOGA(0, "Classic Yoga"),
    WALL_PILATES(1, "Wall Pilates"),
    CHAIR_YOGA(2, "Chair Yoga"),
    FACE_YOGA(3, "Face Yoga"),
    LAZY_YOGA(4, "Lazy Yoga"),
    SOMATIC_YOGA(5, "Somatic Yoga"),
    POSE_LIBRARY(6, "Pose Library"),
    TAI_CHI(7, "Tai Chi"),
    MEDITATION(8, "Meditation"),

    ;

    @EnumValue
    private final Integer code;
    private final String name;

    YogaAutoWorkoutTemplateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YogaAutoWorkoutTemplateEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static YogaAutoWorkoutTemplateEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }
}



