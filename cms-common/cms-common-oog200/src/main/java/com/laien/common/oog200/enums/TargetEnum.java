package com.laien.common.oog200.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * Author:  hhl
 * Date:  2024/9/25 11:11
 */
@Getter
public enum TargetEnum {

    UPPER_BODY(1, "Upper Body"),
    ABS_CORE(2, "Abs & Core"),
    LOWER_BODY(3, "Lower Body");

    private final Integer code;
    private final String name;

    TargetEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TargetEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static TargetEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }
}
