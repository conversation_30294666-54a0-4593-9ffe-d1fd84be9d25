package com.laien.common.oog200.enums;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Date:  2024/9/25 11:11
 * <AUTHOR>
 */
@Getter
public enum GoalEnum  implements IEnumBase {

    GOAL_BASIC(0, "goal basic", AutoWorkoutBasicInfoPointEnum.LEARN_YOGA_BASICS,"Learn Yoga Basics"),
    WEIGHT_LOSS(1, "weight-loss", AutoWorkoutBasicInfoPointEnum.WEIGHT_LOSS,"Weight Loss"),
    FLEXIBILITY(2, "flexibility", AutoWorkoutBasicInfoPointEnum.IMPROVE_FLEXIBILITY,"Improve Flexibility"),
    MINDFULNESS(3, "mindfulness", AutoWorkoutBasicInfoPointEnum.MINDFULNESS,"Mindfulness");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String newName;
    private final AutoWorkoutBasicInfoPointEnum pointEnum;

    GoalEnum(Integer code, String name, AutoWorkoutBasicInfoPointEnum pointEnum, String newName) {
        this.code = code;
        this.name = name;
        this.newName = newName;
        this.pointEnum = pointEnum;
    }


    public static GoalEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static GoalEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

    public static GoalEnum getByNewName(String newName) {
        if (StringUtils.isBlank(newName)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getNewName().equals(newName))
                .findFirst().orElse(null);
    }

    public static List<Integer> getCodeList(List<GoalEnum> goalList) {
        if(CollUtil.isEmpty(goalList)){
            return new ArrayList<>();
        }
        return goalList.stream().map(GoalEnum::getCode).collect(Collectors.toList());
    }

    @Override
    public String getDisplayName() {
        return this.newName;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
