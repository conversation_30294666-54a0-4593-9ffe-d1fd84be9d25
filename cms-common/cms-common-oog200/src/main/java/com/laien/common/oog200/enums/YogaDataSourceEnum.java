package com.laien.common.oog200.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum YogaDataSourceEnum implements IEnumBase {

    TOP_PICKS(0, "Regular Workout"),
    PROGRAM(1, "Program");






    @EnumValue
    private final Integer code;
    private final String name;
    private static final String COMMA = ",";
    private static final String EMPTY_STRING = "";

    YogaDataSourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YogaDataSourceEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static YogaDataSourceEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }
    public static List<YogaDataSourceEnum> convertToYogaDataSourceEnum(String yogaDataSourceCodes) {

        if (StringUtils.isBlank(yogaDataSourceCodes)) {
            return new ArrayList<>();
        }
        List<String> codeStringSet = Arrays.stream(yogaDataSourceCodes.split(COMMA)).collect(Collectors.toList());
        List<YogaDataSourceEnum> yogaDataSourceEnumList = new ArrayList<>();
        for (String codeString : codeStringSet) {
            if (NumberUtil.isInteger(codeString)) {
                int code = NumberUtil.parseInt(codeString);
                YogaDataSourceEnum yogaDataSourceEnum = getByCode(code);
                if (null != yogaDataSourceEnum) {
                    yogaDataSourceEnumList.add(yogaDataSourceEnum);
                }
            }
        }
        return yogaDataSourceEnumList;
    }

    public static String dataSourceListToString(List<YogaDataSourceEnum> dataSourceList) {
        if (CollUtil.isNotEmpty(dataSourceList)) {
            List<Integer> dataSourceCodeList = dataSourceList.stream().map(YogaDataSourceEnum::getCode).collect(Collectors.toList());
            return CollUtil.join(dataSourceCodeList, COMMA);
        }
        return EMPTY_STRING;
    }

    @Override
    public String getDisplayName() {
        return this.name;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
