package com.laien.common.oog200.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum PositionEnum {

    STANDING(0, "Standing"),
    SEATED(1, "Seated"),
    SUPIN<PERSON>(2, "Supine"),
    <PERSON>ON<PERSON>(3, "Prone"),
    ARM_LEG_SUPPORT(4, "Arm & Leg Support"),
    LYING(5, "Lying");


    private final Integer code;
    private final String name;

    PositionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public static PositionEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static PositionEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }
}
