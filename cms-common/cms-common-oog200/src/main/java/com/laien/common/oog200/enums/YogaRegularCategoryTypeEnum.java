package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum YogaRegularCategoryTypeEnum implements IEnumBase {
    FOR_YOU(0, "For You"),
    TRENDING(1, "Trending"),
    FOCUS_AREA(2, "Focus Area"),
    TIME(3, "Time"),
    FEATURED_SERIES(4, "Featured Series");


    @EnumValue
    private final Integer code;
    private final String name;

    YogaRegularCategoryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YogaRegularCategoryTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static YogaRegularCategoryTypeEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

    @Override
    public String getDisplayName() {
        return this.name;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
