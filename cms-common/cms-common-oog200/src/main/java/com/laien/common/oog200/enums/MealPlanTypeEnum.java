package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * Author:  hhl
 * Date:  2024/12/31 14:23
 */
@Getter
public enum MealPlanTypeEnum {

    MEDITERRANEAN(101,"Mediterranean"),
    KETO(201, "Keto"),
    VEGAN(301, "Vegan");


    @EnumValue
    private final Integer code;
    private final String name;

    MealPlanTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MealPlanTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

}
