package com.laien.common.oog200.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/8/6
 */
@Getter
public enum YogaTypeEnum {
    // yoga type
    CLASSIC_YOGA(0, "Classic Yoga"),
    LAZY_YOGA(1, "Lazy Yoga"),
    SOMATIC_YOGA(2, "Somatic Yoga"),
    CHAIR_YOGA(3, "Chair Yoga"),
    WALL_PILATES(4, "Wall Pilates"),
    OTHER(5, "Other"),
    TAI_CHI(6, "Tai Chi"),
    FACE_YOGA(7, "Face Yoga"),
    MEDITATION(8, "Meditation"),
    ;


    private final Integer code;

    private final String name;

    YogaTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Integer getCodeByName(String name) {
        return Stream.of(values()).filter(e -> Objects.equals(name,e.getName())).findFirst().map(e -> e.code).orElse(null);
    }

}
