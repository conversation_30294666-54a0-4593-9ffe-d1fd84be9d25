package com.laien.common.oog200.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.oog200.constant.GlobalConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.laien.common.oog200.constant.GlobalConstant.COMMA;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum DishTypeEnum implements IEnumBase {

    BREAKFAST(100, "Breakfast"),
    LUNCH(101, "Lunch"),
    DINNER(102, "Dinner"),
    MEAL_REPLACEMENT(103, "Meal Replacement");


    @EnumValue
    private final Integer code;
    private final String name;

    DishTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DishTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static DishTypeEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

    public static String getCodeString(List<DishTypeEnum> dishTypeEnumList) {
        if (CollUtil.isEmpty(dishTypeEnumList)) {
            return GlobalConstant.EMPTY_STRING;
        }
        Set<Integer> codeSet = dishTypeEnumList.stream().map(DishTypeEnum::getCode).collect(Collectors.toSet());
        return CollUtil.join(codeSet, COMMA);
    }

    public static List<DishTypeEnum> getByCodesString(String codesString) {

        if (StringUtils.isBlank(codesString)) {
            return new ArrayList<>();
        }
        List<String> codeStringSet = Arrays.stream(codesString.split(COMMA)).collect(Collectors.toList());
        List<DishTypeEnum> dishTypeEnumList = new ArrayList<>();
        for (String codeString : codeStringSet) {
            if (NumberUtil.isInteger(codeString)) {
                int code = NumberUtil.parseInt(codeString);
                DishTypeEnum dishTypeEnum = getByCode(code);
                if (null != dishTypeEnum) {
                    dishTypeEnumList.add(dishTypeEnum);
                }
            }
        }
        return dishTypeEnumList;
    }

    @Override
    public String getDisplayName() {
        return this.name;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
