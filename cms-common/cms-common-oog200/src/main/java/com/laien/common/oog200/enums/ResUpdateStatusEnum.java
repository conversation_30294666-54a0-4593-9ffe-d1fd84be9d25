package com.laien.common.oog200.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @since 2024/07/17
 */
public enum ResUpdateStatusEnum {

    SUCCESS(0, "Succeeded"),
    UPDATE(1, "Updating"),
    FAIL(2, "Failed");

    private Integer status;

    private String statusName;

    ResUpdateStatusEnum(Integer status, String statusName) {

        this.status = status;
        this.statusName = statusName;
    }

    public Integer getStatus() {

        return this.status;
    }

    public String getStatusName() {

        return this.statusName;
    }

    public static class SingletonHolder {

        private static final Map<Integer, String> STATUS_MAP = createMap();

        private static Map<Integer, String> createMap() {
            Map<Integer, String> map = Arrays.stream(ResUpdateStatusEnum.values()).collect(Collectors.toMap(ResUpdateStatusEnum::getStatus, ResUpdateStatusEnum::getStatusName));
            return Collections.unmodifiableMap(map);
        }

        public static Map<Integer, String> getStatusMap() {
            return SingletonHolder.STATUS_MAP;
        }
    }

}
