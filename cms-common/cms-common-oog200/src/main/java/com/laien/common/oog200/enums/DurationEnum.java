package com.laien.common.oog200.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Getter
public enum DurationEnum {

    MIN_0_10(1, 0, 10 * 60 * 1000,"-10"),
    MIN_10_20(2, 10 * 60 * 1000, 20 * 60 * 1000,"10-20"),
    MIN_20_ABOVE(3, 20 * 60 * 1000, Integer.MAX_VALUE,"20-");

    private final Integer code;
    private final Integer min;
    private final Integer max;
    /**
     * 老版本接口传的值
     */
    private final String oldValue;

    DurationEnum(Integer code, Integer min, Integer max, String oldValue) {
        this.code = code;
        this.min = min;
        this.max = max;
        this.oldValue = oldValue;
    }

    public static DurationEnum getByCode(Integer code){
        for (DurationEnum value : values()) {
            if(Objects.equals(value.code, code)){
                return value;
            }
        }
        return null;
    }
}



