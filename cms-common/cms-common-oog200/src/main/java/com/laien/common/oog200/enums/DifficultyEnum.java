package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum DifficultyEnum implements IEnumBase {
    NEWBIE(0, "Newbie", "Newbie"),
    BEGINNER(1, "Beginner", "Beginner"),
    INTERMEDIATE(2, "Intermediate", "Intermediate"),
    ADVANCED(3, "Advanced", "Advanced");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    DifficultyEnum(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    public static DifficultyEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static DifficultyEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
