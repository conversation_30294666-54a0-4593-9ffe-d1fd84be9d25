package com.laien.common.oog200.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum FocusEnum {
    FLEXIBILITY(0, "Flexibility"),
    BALANCE(1, "Balance"),
    STRENGTH(2, "Strength"),
    RELAXATION(3, "Relaxation");

    private final Integer code;
    private final String name;

    FocusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FocusEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static FocusEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

}
