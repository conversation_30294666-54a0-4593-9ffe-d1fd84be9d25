package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.core.enums.mapstruct.IEnumBaseMapStruct;
import lombok.Getter;
import org.mapstruct.Mapper;

/**
 * Proj7MGenderEnums
 * <AUTHOR>
 * @since 2025/05/09
 */
@Getter
public enum GenderEnums implements IEnumBase {
    FEMALE(1,"Female","Female"),
    MALE(2,"Male","Male"),
    BOTH(3,"Both","Both");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String displayName;

    GenderEnums(Integer code, String name, String displayName) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * mapStruct 内置一些常用的映射方法
     */
    @Mapper(componentModel = "spring")
    public static class MapStruct implements IEnumBaseMapStruct<GenderEnums> {
    }


}
