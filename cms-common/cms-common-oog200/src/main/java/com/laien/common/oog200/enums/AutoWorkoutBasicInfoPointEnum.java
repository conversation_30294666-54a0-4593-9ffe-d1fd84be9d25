package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Objects;

/**
 * Date:  2024/9/25 15:13
 * <AUTHOR>
 */
@Getter
public enum AutoWorkoutBasicInfoPointEnum {

    /**
     * wall pilates和chair yoga使用的
     */
    UPPER_BODY(0, "Upper Body"),
    ABS_CORE(1, "Abs & Core"),
    LOWER_BODY(2, "Lower Body"),
    UPPER_BODY_ABS_CORE(3, "Upper Body+Abs & Core"),
    ABS_CORE_LOWER_BODY(4, "Abs & Core+Lower Body"),
    FULL_BODY(5, "Fullbody"),

    /**
     * classic yoga
     */
    LEARN_YOGA_BASICS(6, "Learn Yoga Basics"),
    MINDFULNESS(7, "Mindfulness"),
    WEIGHT_LOSS(8, "Weight Loss"),
    IMPROVE_FLEXIBILITY(9, "Improve Flexibility");
    ;

    @EnumValue
    private final Integer code;
    private final String value;

    AutoWorkoutBasicInfoPointEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static AutoWorkoutBasicInfoPointEnum getByCode(Integer code){
        for (AutoWorkoutBasicInfoPointEnum value : values()) {
            if(Objects.equals(value.code, code)){
                return value;
            }
        }
        return null;
    }


    public static AutoWorkoutBasicInfoPointEnum getByName(String name) {
        for (AutoWorkoutBasicInfoPointEnum value : values()) {
            if (Objects.equals(value.value, name)) {
                return value;
            }
        }
        return null;
    }
}
