package com.laien.common.oog200.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.laien.common.core.enums.IEnumBase;
import com.laien.common.oog200.constant.GlobalConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.laien.common.oog200.constant.GlobalConstant.COMMA;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum DishStyleEnum implements IEnumBase {

    VEGAN(100, "Vegan"),
    MEDITERRANEAN(101, "Mediterranean"),
    KETO(102, "Keto"),
    SMOOTHIE(103, "Smoothie");


    @EnumValue
    private final Integer code;
    private final String name;

    DishStyleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DishStyleEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static DishStyleEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

    public static String getCodeString(List<DishStyleEnum> dishStyleEnumList) {
        if (CollUtil.isEmpty(dishStyleEnumList)) {
            return GlobalConstant.EMPTY_STRING;
        }
        Set<Integer> codeSet = dishStyleEnumList.stream().map(DishStyleEnum::getCode).collect(Collectors.toSet());
        return CollUtil.join(codeSet, COMMA);
    }

    /**
     * 获取dish枚举集合
     *
     * @param codesString 多个code，以英文逗号分隔的字符串
     * @return dish枚举集合
     */
    public static List<DishStyleEnum> getByCodesString(String codesString) {

        if (StringUtils.isBlank(codesString)) {
            return new ArrayList<>();
        }
        List<String> codeStringSet = Arrays.stream(codesString.split(COMMA)).collect(Collectors.toList());
        List<DishStyleEnum> dishStyleEnumList = new ArrayList<>();
        for (String codeString : codeStringSet) {
            if (NumberUtil.isInteger(codeString)) {
                int code = NumberUtil.parseInt(codeString);
                DishStyleEnum dishStyleEnum = getByCode(code);
                if (null != dishStyleEnum) {
                    dishStyleEnumList.add(dishStyleEnum);
                }
            }
        }
        return dishStyleEnumList;
    }

    @Override
    public String getDisplayName() {
        return this.name;
    }

    @Override
    public String getEnumName() {
        return this.name();
    }
}
