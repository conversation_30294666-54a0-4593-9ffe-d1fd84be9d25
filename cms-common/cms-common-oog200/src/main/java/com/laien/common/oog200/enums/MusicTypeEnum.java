package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * Author:  hhl
 * Date:  2025/2/21 10:12
 */
@Getter
public enum MusicTypeEnum {

    //200用的
    NORMAL_BGM(101, "Normal BGM"),
    YO<PERSON>_BGM(102, "Yoga BGM"),
    FACE_BGM(103, "Face BGM"),
    DANCE_BGM(104, "Dance BGM"),
    TAI_CHI_BGM(105, "Tai Chi BGM"),
    MEDITATION(106, "Meditation"),
    SOUNDSCAPE(107, "Soundscape"),



    //104用的
    OOG104_REGULAR_FITNESS(108, "OOG104 Regular Fitness"),
    OOG104_CHAIR_YOGA(109, "OOG104 Chair Yoga"),
    OOG104_CLASSIC_YOGA(110, "OOG104 Classic Yoga"),
    OOG104_WALL_PILATES(111, "OOG104 Wall Pilates"),
    OOG104_106_FITNESS(112, "OOG104 106 Fitness"),
    //104 v8.3.0新增OOG104 Pilates
    OOG104_PILATES(113, "OOG104 Pilates");








    private Integer code;

    @EnumValue
    private String value;

    MusicTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

}
