package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * Author:  hhl
 * Date:  2025/2/19 15:51
 */
@Getter
public enum PlaylistTypeEnum {

    //200用的
    PLAN_CLASSIC_CHAIR(101, "Plan_Classic and Chair"),
    ANIMATION(102, "Animation"),
    MEDITA<PERSON>ON(103, "Meditation"),
    SOUNDSCAPE(104, "Soundscape"),
    POSE_LIBRARY(105, "Pose Library"),

    //104用的
    OOG104_REGULAR_FITNESS(106, "OOG104 Regular Fitness"),
    OOG104_CHAIR_YOGA(107, "OOG104 Chair Yoga"),
    OOG104_CLASSIC_YOGA(108, "OOG104 Classic Yoga"),
    OOG104_WALL_PILATES(109, "OOG104 Wall Pilates"),
    OOG104_106_FITNESS(110, "OOG104 106 Fitness"),
    //V8.3.0新增OOG104 Pilates
    OOG104_PILATES(111, "OOG104 Pilates"),
    ;

    @EnumValue
    private Integer code;

    private String value;

    PlaylistTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

}
