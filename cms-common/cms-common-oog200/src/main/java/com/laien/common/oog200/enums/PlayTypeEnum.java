package com.laien.common.oog200.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * Author:  hhl
 * Date:  2024/11/21 16:30
 */
@Getter
public enum PlayTypeEnum {

    WIDE_SCREEN(0, "WideScreen(16:9)"),
    SQUARE_SCREEN(1, "SquareScreen(1:1)"),
    PORTRAIT_SCREEN(2, "PortraitScreen(9:16)");

//    @JsonValue
    @EnumValue
    private Integer code;
    private String value;

    PlayTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}
