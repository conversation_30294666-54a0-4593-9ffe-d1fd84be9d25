package com.laien.common.oog200.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
@Getter
public enum YogaPoseGroupTypeEnum {

    TODAY_POSE(0, "today pose"),
    TRAINING_PATH(1, "training path");

    private final Integer code;
    private final String name;

    YogaPoseGroupTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YogaPoseGroupTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst().orElse(null);
    }

    public static YogaPoseGroupTypeEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getName().equals(name))
                .findFirst().orElse(null);
    }

}
