package com.laien.common.oog200.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Getter
public enum TaskResourceSectionQueryEnums {

    RES_YOGA_VIDEO_SIDE("res_yoga_video", "sideVideoUrl"),
    RES_YOGA_VIDEO_FRONT("res_yoga_video", "frontVideoUrl"),

    RES_TRANSITION_SIDE("res_transition", "sideVideoUrl"),
    RES_TRANSITION_FRONT("res_transition", "frontVideoUrl"),

    PROJ_YOGA_POSE_VIDEO_SIDE("proj_yoga_pose_video", "sideVideoUrl"),
    PROJ_YOGA_POSE_VIDEO_FRONT("proj_yoga_pose_video", "frontVideoUrl"),

    PROJ_YOGA_POSE_TRANSITION_FRONT("proj_yoga_pose_transition", "frontVideoUrl"),

    RES_VIDEO_CLASS("res_video_class", "videoUrl");

    private final String tableName;

    private final String entityFieldName;

    TaskResourceSectionQueryEnums(String tableName, String entityFieldName) {
        this.tableName = tableName;
        this.entityFieldName = entityFieldName;
    }
}
