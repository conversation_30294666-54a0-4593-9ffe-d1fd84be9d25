package com.laien.common.oog200.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.Getter;

import java.util.*;

/**
 * point与target的mapping_relationship
 * 一个point可以映射成多个target组合
 * 如FULL_BODY可以映射成UPPER_BODY、ABS_CORE、LOWER_BODY的组合和UPPER_BODY、LOWER_BODY的组合
 * Date:  2024/9/25 15:13
 * <AUTHOR>
 */
@Getter
public enum BasicInfoPointTargetMappingEnum {

    /**
     * wall pilates和chair yoga使用的
     */
    UPPER_BODY_MAPPING(AutoWorkoutBasicInfoPointEnum.UPPER_BODY, Collections.singletonList(Collections.singletonList(TargetEnum.UPPER_BODY))),
    ABS_CORE_MAPPING(AutoWorkoutBasicInfoPointEnum.ABS_CORE, Collections.singletonList(Collections.singletonList(TargetEnum.ABS_CORE))),
    LOWER_BODY_MAPPING(AutoWorkoutBasicInfoPointEnum.LOWER_BODY, Collections.singletonList(Collections.singletonList(TargetEnum.LOWER_BODY))),
    UPPER_BODY_ABS_CORE_MAPPING(AutoWorkoutBasicInfoPointEnum.UPPER_BODY_ABS_CORE, Collections.singletonList(Arrays.asList(TargetEnum.UPPER_BODY, TargetEnum.ABS_CORE))),
    ABS_CORE_LOWER_BODY_MAPPING(AutoWorkoutBasicInfoPointEnum.ABS_CORE_LOWER_BODY, Collections.singletonList(Arrays.asList(TargetEnum.ABS_CORE, TargetEnum.LOWER_BODY))),
    FULL_BODY_MAPPING(AutoWorkoutBasicInfoPointEnum.FULL_BODY, Arrays.asList(Arrays.asList(TargetEnum.UPPER_BODY, TargetEnum.LOWER_BODY), Arrays.asList(TargetEnum.UPPER_BODY, TargetEnum.LOWER_BODY, TargetEnum.ABS_CORE))),

    ;

    private final AutoWorkoutBasicInfoPointEnum point;
    /**
     * point与target的mapping_relationship
     * 一个point可以映射成多个target组合
     * 如FULL_BODY可以映射成UPPER_BODY、ABS_CORE、LOWER_BODY的组合和UPPER_BODY、LOWER_BODY的组合
     */
    private final List<List<TargetEnum>> targetMappingList;

    BasicInfoPointTargetMappingEnum(AutoWorkoutBasicInfoPointEnum point, List<List<TargetEnum>> targetMappingList) {
        this.point = point;
        this.targetMappingList = targetMappingList;
    }

    public static BasicInfoPointTargetMappingEnum getByTargetList(List<TargetEnum> targetList) {
        if (CollUtil.isEmpty(targetList)) {
            return null;
        }
        Set<TargetEnum> targetSet = new HashSet<>(targetList);
        for (BasicInfoPointTargetMappingEnum value : values()) {
            for (List<TargetEnum> targetEnumList : value.getTargetMappingList()) {
                Collection<TargetEnum> intersection = CollUtil.intersection(targetEnumList, targetSet);
                if (intersection.size() == targetSet.size()) {
                    return value;
                }
            }
        }
        return null;
    }
}
