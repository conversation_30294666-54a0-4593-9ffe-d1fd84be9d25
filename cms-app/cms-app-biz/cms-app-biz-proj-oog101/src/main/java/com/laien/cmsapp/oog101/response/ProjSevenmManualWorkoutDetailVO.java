package com.laien.cmsapp.oog101.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog101.enums.SevenmWorkoutCategoryEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  manual workout detail VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjSevenmManualWorkoutDetailVO extends ProjSevenmGenerateWorkoutDetailVO {

    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    @AbsoluteR2Url
    private String coverImage;

    @ApiModelProperty(value = "Detail Image, supports png/webp formats")
    @AbsoluteR2Url
    private String detailImage;

    @ApiModelProperty(value = "supplementImage, supports png/webp formats")
    @AbsoluteR2Url
    private String supplementImage;

    @ApiModelProperty(value = "sketchImage, supports png/webp formats")
    @AbsoluteR2Url
    private String sketchImage;

    @ApiModelProperty(value = "Description, maximum 1000 characters")
    private String description;

    @ApiModelProperty(value = "Categories")
    private List<SevenmWorkoutCategoryEnums> category;

    @ApiModelProperty(value = "workout Name")
    private String name;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;
}
