package com.laien.cmsapp.oog101.controller;


import com.laien.common.controller.ResponseController;
import com.laien.common.oog101.enums.ExampleEnums;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * Fitness Fasting article 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Api(tags = "app端：101Example")
@RestController
@RequestMapping("/oog101/101Example")
public class Proj101ExampleController extends ResponseController {

    @ApiOperation(value = "example")
    @GetMapping( "/example")
    public ResponseResult<Object> list() {
        return succ(ExampleEnums.EXAMPLE_ENUMS.getName()+"-oog101app");
    }

}


