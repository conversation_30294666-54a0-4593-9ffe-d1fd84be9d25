package com.laien.cmsapp.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmFastingArticlePub;
import com.laien.cmsapp.oog101.request.ProjSevenmFastingArticleListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleListVO;

import java.util.List;

/**
 * <p>
 * SevenmFasting article 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
public interface IProjSevenmFastingArticlePubService extends IService<ProjSevenmFastingArticlePub> {

    List<ProjSevenmFastingArticleListVO> list(ProjSevenmFastingArticleListReq req, ProjPublishCurrentVersionInfoBO versionInfoBO);

    ProjSevenmFastingArticleDetailVO detail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO, String lang);
}
