package com.laien.cmsapp.oog101.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog101.entity.ProjSevenmSound;
import com.laien.cmsapp.oog101.request.ProjSevenmSoundReq;
import com.laien.cmsapp.oog101.response.ProjSevenmSoundVO;

import java.util.List;

public interface IProjSevenmSoundService extends IService<ProjSevenmSound> {
    /**
     * 按照类型，子类型 , 性别查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    List<ProjSevenmSoundVO> selectSoundList(ProjSevenmSoundReq soundReq);

}