package com.laien.cmsapp.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmWorkoutImagePub;
import com.laien.cmsapp.oog101.response.ProjSevenmWorkoutImageVO;
import com.laien.common.oog101.enums.SevenmGenderEnums;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_workout_image_pub】的数据库操作Service
* @createDate 2025-05-20 12:11:41
*/
public interface IProjSevenmWorkoutImagePubService extends IService<ProjSevenmWorkoutImagePub> {

    List<ProjSevenmWorkoutImageVO> query(SevenmGenderEnums gender, ProjPublishCurrentVersionInfoBO versionInfoBO);

}
