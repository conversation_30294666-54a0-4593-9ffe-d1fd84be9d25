package com.laien.cmsapp.oog101.response;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ProjSevenmGenerateWorkoutDetailVO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmGenerateWorkoutDetailVO对象", description = "ProjSevenmGenerateWorkoutDetailVO")
@TableName(autoResultMap = true)
public class ProjSevenmGenerateWorkoutDetailVO extends BaseTableCodeVO {

    @ApiModelProperty(value = "Event Name")
    private String eventName;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "target code")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "difficulty code")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "equipment code")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "性别")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制")
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @AbsoluteR2Url
    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json list")
    private List<AudioI18nVO> audioJsonList;

    @ApiModelProperty(value = "groupList")
    private List<ProjSevenmWorkoutDetailGroupVO> groupList;
}
