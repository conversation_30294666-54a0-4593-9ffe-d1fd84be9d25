package com.laien.cmsapp.oog101.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.laien.common.core.enums.jackson.EnumBaseCodeSerializer;
import com.laien.common.oog101.enums.TableCodeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@Data
public class BaseTableCodeVO {

    @ApiModelProperty(value = "table code")
    @JsonSerialize(using = EnumBaseCodeSerializer.class)
    private TableCodeEnums tableCode;

    @ApiModelProperty(value = "id")
    private Integer Id;

}
