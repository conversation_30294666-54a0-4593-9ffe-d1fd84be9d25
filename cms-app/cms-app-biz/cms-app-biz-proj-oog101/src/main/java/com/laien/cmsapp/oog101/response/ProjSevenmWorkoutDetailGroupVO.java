package com.laien.cmsapp.oog101.response;

import com.laien.common.oog101.enums.SevenmTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "video group", description = "video group")
public class ProjSevenmWorkoutDetailGroupVO {

    @ApiModelProperty("exercise组名称")
    private String groupName;

    @ApiModelProperty("exercise组类型")
    private SevenmTypeEnums groupType;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("播放循环次数")
    private Integer rounds = 1;

    @ApiModelProperty(value = "video list")
    private List<ProjSevenmExerciseVideoDetailVO> videoList;

}
