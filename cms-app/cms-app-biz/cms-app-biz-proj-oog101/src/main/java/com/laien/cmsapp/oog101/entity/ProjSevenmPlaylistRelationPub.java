package com.laien.cmsapp.oog101.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPlaylistMusic对象", description="播放列表音乐关联表")
public class ProjSevenmPlaylistRelationPub extends BaseModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "播放列表id")
    private Integer projSevenmPlaylistId;

    @ApiModelProperty(value = "音乐id")
    private Integer projSevenmMusicId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "music展示名称")
    private String displayName;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

}
