package com.laien.cmsapp.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmPlaylistPub;
import com.laien.cmsapp.oog101.request.ProjSevenmPlaylistListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmPlaylistVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public interface IProjSevenmPlaylistService extends IService<ProjSevenmPlaylistPub> {

    List<ProjSevenmPlaylistVO> list(ProjSevenmPlaylistListReq req, ProjPublishCurrentVersionInfoBO versionInfoBO);
}
