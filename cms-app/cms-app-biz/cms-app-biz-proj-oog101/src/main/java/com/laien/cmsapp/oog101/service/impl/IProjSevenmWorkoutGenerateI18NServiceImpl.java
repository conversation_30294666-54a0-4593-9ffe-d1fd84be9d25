package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog101.entity.ProjSevenmWorkoutGenerateI18n;
import com.laien.cmsapp.oog101.mapper.ProjSevenmWorkoutGenerateI18nMapper;
import com.laien.cmsapp.oog101.service.IProjSevenmWorkoutGenerateI18nService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_workout_generate_i18n】的数据库操作Service实现
* @createDate 2025-05-20 12:11:41
*/
@Service
public class IProjSevenmWorkoutGenerateI18NServiceImpl extends ServiceImpl<ProjSevenmWorkoutGenerateI18nMapper, ProjSevenmWorkoutGenerateI18n>
    implements IProjSevenmWorkoutGenerateI18nService {

    @Override
    public List<ProjSevenmWorkoutGenerateI18n> query(Collection<Integer> workoutIds) {
        if(CollUtil.isEmpty(workoutIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjSevenmWorkoutGenerateI18n> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjSevenmWorkoutGenerateI18n::getProjSevenmWorkoutGenerateId, workoutIds);
        return baseMapper.selectList(wrapper);
    }
}




