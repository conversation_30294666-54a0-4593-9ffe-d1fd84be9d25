package com.laien.cmsapp.oog101.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "audioJson", description = "audioJson")
public class AudioI18nVO {

    @ApiModelProperty(value = "语言")
    private String language;

    @AbsoluteR2Url
    @ApiModelProperty(value = "音频文件地址")
    private String audioJsonUrl;

}
