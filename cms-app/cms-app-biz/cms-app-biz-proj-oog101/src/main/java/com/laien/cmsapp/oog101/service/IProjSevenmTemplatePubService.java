package com.laien.cmsapp.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmTemplatePub;
import com.laien.cmsapp.oog101.request.ProjSevenmDailyReq;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_template_pub】的数据库操作Service
* @createDate 2025-05-20 12:11:41
*/
public interface IProjSevenmTemplatePubService extends IService<ProjSevenmTemplatePub> {

    List<ProjSevenmTemplatePub> list(ProjSevenmDailyReq req, ProjPublishCurrentVersionInfoBO versionInfoBO);
}
