package com.laien.cmsapp.oog101.mapstruct;

import com.laien.cmsapp.oog101.entity.ProjSevenmMusicPub;
import com.laien.cmsapp.oog101.entity.ProjSevenmPlaylistPub;
import com.laien.cmsapp.oog101.entity.ProjSevenmPlaylistRelationPub;
import com.laien.cmsapp.oog101.response.ProjSevenmPlaylistMusicVO;
import com.laien.cmsapp.oog101.response.ProjSevenmPlaylistVO;
import org.mapstruct.Mapper;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/16
 */
@Mapper(componentModel = "spring")
public interface ProjSevenmPlaylistMapStruct {

    ProjSevenmPlaylistMusicVO toMusicVO(ProjSevenmMusicPub musicPub, ProjSevenmPlaylistRelationPub relation);

    ProjSevenmPlaylistVO toListVO(ProjSevenmPlaylistPub entity);
}
