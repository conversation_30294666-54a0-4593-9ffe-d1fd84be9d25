package com.laien.cmsapp.oog101.request;

import com.laien.cmsapp.requst.LangReq;
import com.laien.common.oog101.enums.SevenmPlaylistTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *  ProjSevenmPlaylistListReq
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Data
@ApiModel(value = "ProjSevenmPlaylistListReq", description = "ProjSevenmPlaylistListReq")
public class ProjSevenmPlaylistListReq extends LangReq {
    @ApiModelProperty(value = "workoutType")
    SevenmPlaylistTypeEnums workoutType;
}
