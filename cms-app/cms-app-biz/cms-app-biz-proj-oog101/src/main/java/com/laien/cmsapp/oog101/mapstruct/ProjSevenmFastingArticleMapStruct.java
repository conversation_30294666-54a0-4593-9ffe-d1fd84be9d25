package com.laien.cmsapp.oog101.mapstruct;

import com.laien.cmsapp.oog101.entity.ProjSevenmFastingArticlePub;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
@Mapper(componentModel = "spring")
public interface ProjSevenmFastingArticleMapStruct {

    List<ProjSevenmFastingArticleListVO> toVOList(List<ProjSevenmFastingArticlePub> fastingArticleList);

    ProjSevenmFastingArticleDetailVO toDetailVO(ProjSevenmFastingArticlePub fastingArticle);
}
