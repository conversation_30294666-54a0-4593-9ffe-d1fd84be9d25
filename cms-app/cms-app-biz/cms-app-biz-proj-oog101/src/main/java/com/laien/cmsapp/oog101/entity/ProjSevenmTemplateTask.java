package com.laien.cmsapp.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog101.enums.SevenmTemplateTaskStatusEnum;
import com.laien.common.oog101.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  proj_sevenm_template_task
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
@Accessors(chain = true)
public class ProjSevenmTemplateTask extends BaseModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_TEMPLATE_TASK;

    @ApiModelProperty("项目ID")
    private Integer projId;

    @ApiModelProperty("生成的workout数量")
    private Integer workoutNum;

    @ApiModelProperty("模板ID")
    private Integer projSevenmTemplateId;

    @ApiModelProperty("是否需要清理已生成的数据 0 否，1是")
    private Integer cleanUp;

    @ApiModelProperty("失败信息")
    private String failureMessage;

    @ApiModelProperty("任务状态 1待处理、2处理中、3处理失败、4 处理成功")
    private SevenmTemplateTaskStatusEnum status;











}
