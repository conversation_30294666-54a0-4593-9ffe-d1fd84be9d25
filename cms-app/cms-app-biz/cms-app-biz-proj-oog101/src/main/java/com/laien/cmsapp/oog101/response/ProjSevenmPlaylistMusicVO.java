package com.laien.cmsapp.oog101.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog101.enums.SevenmMusicTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
public class ProjSevenmPlaylistMusicVO {

    @ApiModelProperty(value = "播放列表id")
    private Integer projSevenmPlaylistId;

    @ApiModelProperty(value = "音乐id")
    private Integer projSevenmMusicId;

    @ApiModelProperty(value = "music 类型")
    private SevenmMusicTypeEnums musicType;

    @ApiModelProperty(value = "music展示名称")
    private String displayName;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @AbsoluteR2Url
    @ApiModelProperty(value = "音频")
    private String audio;

    @ApiModelProperty(value = "音频总时长,毫秒")
    private Integer audioDuration;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

}
