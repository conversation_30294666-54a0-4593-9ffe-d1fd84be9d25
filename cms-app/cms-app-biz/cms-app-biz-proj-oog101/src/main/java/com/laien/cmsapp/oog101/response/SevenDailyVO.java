package com.laien.cmsapp.oog101.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/20
 */

@Data
@Accessors(chain = true)
@ApiModel(value = "SevenDailyVO", description = "SevenDailyVO")
public class SevenDailyVO {

    @ApiModelProperty("workoutList")
    private List<ProjSevenmGenerateWorkoutDetailVO> workoutList;

    @ApiModelProperty("imageList")
    private List<ProjSevenmWorkoutImageVO> imageList;

}
