package com.laien.cmsapp.oog101.request;

import com.laien.cmsapp.requst.LangReq;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmSoundSubTypeEnums;
import com.laien.common.oog101.enums.SevenmSoundTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "声音请求参数", description = "声音请求参数")
public class ProjSevenmSoundReq extends LangReq {

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private SevenmSoundTypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private SevenmSoundSubTypeEnums soundSubType;

    @ApiModelProperty(value = "声音源(female|male 默认female")
    private SevenmGenderEnums gender;
}
