package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.bo.SevenTargetMappingCountBO;
import com.laien.cmsapp.oog101.entity.*;
import com.laien.cmsapp.oog101.mapper.ProjSevenmWorkoutGenerateMapper;
import com.laien.cmsapp.oog101.request.CommonRefreshReq;
import com.laien.cmsapp.oog101.request.ProjSevenmDailyReq;
import com.laien.cmsapp.oog101.response.*;
import com.laien.cmsapp.oog101.service.*;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog101.enums.*;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog101.enums.SevenmTemplateTypeEnums.SEVENM_NORMAL_WORKOUT;
import static com.laien.common.oog101.enums.SevenmTemplateTypeEnums.SEVENM_STRETCH_WORKOUT;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_workout_generate】的数据库操作Service实现
* @createDate 2025-05-20 12:11:41
*/
@Slf4j
@Service
public class IProjSevenmWorkoutGenerateServiceImpl extends ServiceImpl<ProjSevenmWorkoutGenerateMapper, ProjSevenmWorkoutGenerate>
    implements IProjSevenmWorkoutGenerateService {

    @Resource
    private IProjSevenmTemplatePubService projSevenmTemplatePubService;

    @Resource
    private IProjSevenmWorkoutGenerateExerciseVideoService projSevenmWorkoutGenerateExerciseVideoService;

    @Resource
    private IProjSevenmExerciseVideoService projSevenmExerciseVideoService;

    @Resource
    private IProjSevenmTemplateExerciseGroupService projSevenmTemplateExerciseGroupService;

    @Resource
    private IProjSevenmWorkoutGenerateI18nService projSevenmWorkoutGenerateI18nService;

    @Resource
    private IProjSevenmWorkoutImagePubService projSevenmWorkoutImagePubService;

    @Override
    public SevenDailyVO daily(ProjSevenmDailyReq req, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        SevenDailyVO sevenDailyVO = new SevenDailyVO();
        List<ProjSevenmTemplatePub> templateList = projSevenmTemplatePubService.list(req, versionInfoBO);
        if(CollUtil.isEmpty(templateList)){
            log.error("oog101 daily not found template, req:{}",req);
            return sevenDailyVO;
        }
        Set<SevenmTargetEnums> targetSet = req.getTargetSet();
        Set<SevenmTargetMappingEnums> targetMappingSet = SevenmTargetMappingEnums.toMappingSet(targetSet);
        List<SevenTargetMappingCountBO> targetMappingCountList = SevenTargetMappingCountBO.list(targetMappingSet, req.getGender());
        List<ProjSevenmWorkoutImageVO> imageVOList = matchImage(req, versionInfoBO, targetMappingCountList);
        sevenDailyVO.setImageList(imageVOList);

        Set<Integer> templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjSevenmWorkoutGenerate> workoutGenerateList = list(req, templateIdSet);
        if(CollUtil.isEmpty(workoutGenerateList)){
            return sevenDailyVO;
        }

        Collections.shuffle(workoutGenerateList);
        Map<String, List<ProjSevenmWorkoutGenerate>> workoutMap = new HashMap<>();
        String keyTemplate = "%s-%s";
        for (ProjSevenmWorkoutGenerate workout : workoutGenerateList) {
            Integer sum = SevenmTargetEnums.getSum(workout.getTarget());
            String key = String.format(keyTemplate, workout.getWorkoutType(), sum);
            List<ProjSevenmWorkoutGenerate> workoutList = workoutMap.getOrDefault(key, new ArrayList<>(100));
            workoutList.add(workout);
            workoutMap.put(key, workoutList);
        }
        List<ProjSevenmWorkoutGenerate> matchedWorkoutList = match(req, targetMappingCountList, keyTemplate, workoutMap);
        List<ProjSevenmGenerateWorkoutDetailVO> workoutList = toSevenmGenerateWorkoutDetailVO(matchedWorkoutList, versionInfoBO,req.getLang());
        sevenDailyVO.setWorkoutList(workoutList);
        return sevenDailyVO;
    }

    @Override
    public List<ProjSevenmGenerateWorkoutDetailVO> detailList(List<CommonRefreshReq> reqList, ProjPublishCurrentVersionInfoBO versionInfoBO, String language) {
        if (CollUtil.isEmpty(reqList)) {
            return new ArrayList<>();
        }
        Set<Integer> workoutIdSet = reqList.stream().map(CommonRefreshReq::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjSevenmWorkoutGenerate::getId, workoutIdSet)
                .eq(ProjSevenmWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjSevenmWorkoutGenerate> workoutGenerateList = list(wrapper);
        if(CollUtil.isEmpty(workoutGenerateList)){
            return new ArrayList<>();
        }
        return toSevenmGenerateWorkoutDetailVO(workoutGenerateList,versionInfoBO, language);
    }

    private List<ProjSevenmWorkoutImageVO> matchImage(ProjSevenmDailyReq req, ProjPublishCurrentVersionInfoBO versionInfoBO, List<SevenTargetMappingCountBO> targetMappingCountList) {
        List<ProjSevenmWorkoutImageVO> imageVOList = projSevenmWorkoutImagePubService.query(req.getGender(), versionInfoBO);
        if (CollUtil.isEmpty(imageVOList)) {
            return imageVOList;
        }
        Collections.shuffle(imageVOList);
        Map<Integer, List<ProjSevenmWorkoutImageVO>> imageMap = new HashMap<>();
        for (ProjSevenmWorkoutImageVO imageVO : imageVOList) {
            Integer sum = SevenmTargetEnums.getSum(imageVO.getTarget());
            List<ProjSevenmWorkoutImageVO> imageList = imageMap.getOrDefault(sum, new ArrayList<>());
            imageList.add(imageVO);
            imageMap.put(sum, imageList);
        }
        List<ProjSevenmWorkoutImageVO> finalImageList = new ArrayList<>();
        for (SevenTargetMappingCountBO targetMappingCount : targetMappingCountList) {
            Integer count = targetMappingCount.getCount();
            SevenmTargetMappingEnums targetMapping = targetMappingCount.getTargetMapping();
            Integer sum = SevenmTargetEnums.getSum(targetMapping.getTargetList());
            List<ProjSevenmWorkoutImageVO> imageList = imageMap.getOrDefault(sum, new ArrayList<>());
            List<ProjSevenmWorkoutImageVO> matchedImageList = CollUtil.sub(imageList, 0, count);
            if (matchedImageList.size() < count) {
                log.error("oog101 match image not enough, req:{},target mapping:{}", req, targetMapping);
            }
            finalImageList.addAll(matchedImageList);
        }
        return finalImageList;
    }

    private List<ProjSevenmWorkoutGenerate> match(ProjSevenmDailyReq req,
                                                  List<SevenTargetMappingCountBO> targetMappingCountList,
                                                  String keyTemplate,
                                                  Map<String, List<ProjSevenmWorkoutGenerate>> workoutMap) {
        List<ProjSevenmWorkoutGenerate> matchedWorkoutList = new ArrayList<>(70);
        for (SevenTargetMappingCountBO targetMappingCount : targetMappingCountList) {
            int stretchCount = targetMappingCount.getCount() * 2;
            int normalCount = stretchCount * 3;
            List<ProjSevenmWorkoutGenerate> normalWorkoutList = doMatchWorkout(targetMappingCount, keyTemplate, workoutMap, SEVENM_NORMAL_WORKOUT, normalCount);

            if (normalWorkoutList.size() < normalCount) {
                log.error("oog101 daily workout not enough, req:{}, targetMapping:{},workout type:{}", req,targetMappingCount,SEVENM_NORMAL_WORKOUT);
            }
            List<ProjSevenmWorkoutGenerate> stretchWorkoutList = doMatchWorkout(targetMappingCount, keyTemplate, workoutMap, SEVENM_STRETCH_WORKOUT, stretchCount);
            if (normalWorkoutList.size() < stretchCount) {
                log.error("oog101 daily workout not enough, req:{}, targetMapping:{},workout type:{}", req,targetMappingCount,SEVENM_STRETCH_WORKOUT);
            }
            matchedWorkoutList.addAll(normalWorkoutList);
            matchedWorkoutList.addAll(stretchWorkoutList);
        }
        return matchedWorkoutList;
    }

    private List<ProjSevenmGenerateWorkoutDetailVO> toSevenmGenerateWorkoutDetailVO(List<ProjSevenmWorkoutGenerate> workoutList,
                                                                                    ProjPublishCurrentVersionInfoBO versionInfoBO,
                                                                                    String lang){

        Set<Integer> workoutIdSet = new HashSet<>();
        Set<Integer> templateIdSet = new HashSet<>();
        for (ProjSevenmWorkoutGenerate projSevenmWorkoutGenerate : workoutList) {
            workoutIdSet.add(projSevenmWorkoutGenerate.getId());
            templateIdSet.add(projSevenmWorkoutGenerate.getProjSevenmTemplateId());
        }
        List<ProjSevenmWorkoutGenerateExerciseVideo> videoWorkoutRelationList = projSevenmWorkoutGenerateExerciseVideoService.query(workoutIdSet);
        Map<Integer, List<ProjSevenmWorkoutGenerateExerciseVideo>> videoWorkoutRelationMap = videoWorkoutRelationList.stream().collect(Collectors.groupingBy(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId));
        Set<Integer> videoIdSet = videoWorkoutRelationList.stream().map(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmExerciseVideoId).collect(Collectors.toSet());
        List<ProjSevenmTemplateExerciseGroup> groupList = projSevenmTemplateExerciseGroupService.query(templateIdSet);
        Map<Integer, List<ProjSevenmTemplateExerciseGroup>> exerciseGroupMap = groupList.stream().collect(Collectors.groupingBy(ProjSevenmTemplateExerciseGroup::getProjSevenmTemplateId));
        List<ProjSevenmExerciseVideoDetailVO> videoList = projSevenmExerciseVideoService.ListVOByIdList(videoIdSet, versionInfoBO, lang);
        Map<Integer, ProjSevenmExerciseVideoDetailVO> videoMap = videoList.stream().collect(Collectors.toMap(BaseTableCodeVO::getId, item -> item));
        List<ProjSevenmWorkoutGenerateI18n> workoutGenerateI18nList = projSevenmWorkoutGenerateI18nService.query(workoutIdSet);
        Map<Integer,List<AudioI18nVO>> audioI18nMap = new HashMap<>(workoutList.size());
        for (ProjSevenmWorkoutGenerateI18n generateI18n : workoutGenerateI18nList) {
            Integer workoutId = generateI18n.getProjSevenmWorkoutGenerateId();
            List<AudioI18nVO> audioI18nList = audioI18nMap.getOrDefault(workoutId, new ArrayList<>(30));
            AudioI18nVO audioI18nVO = new AudioI18nVO();
            audioI18nVO.setLanguage(generateI18n.getLanguage())
                    .setAudioJsonUrl(generateI18n.getAudioJsonUrl());
            audioI18nList.add(audioI18nVO);
            audioI18nMap.put(workoutId, audioI18nList);
        }
        List<ProjSevenmGenerateWorkoutDetailVO> workoutDetailList = new ArrayList<>(workoutList.size());
        for (ProjSevenmWorkoutGenerate workout : workoutList) {
            ProjSevenmGenerateWorkoutDetailVO detailVO = doToSevenmGenerateWorkoutDetailVO(workout, audioI18nMap, videoWorkoutRelationMap, exerciseGroupMap, videoMap);
            workoutDetailList.add(detailVO);
        }
        return workoutDetailList;
    }

    private ProjSevenmGenerateWorkoutDetailVO doToSevenmGenerateWorkoutDetailVO(ProjSevenmWorkoutGenerate workout, Map<Integer, List<AudioI18nVO>> audioI18nMap, Map<Integer, List<ProjSevenmWorkoutGenerateExerciseVideo>> videoWorkoutRelationMap, Map<Integer, List<ProjSevenmTemplateExerciseGroup>> exerciseGroupMap, Map<Integer, ProjSevenmExerciseVideoDetailVO> videoMap) {
        ProjSevenmGenerateWorkoutDetailVO detailVO = new ProjSevenmGenerateWorkoutDetailVO();
        BeanUtils.copyProperties(workout, detailVO);
        String eventName = workout.getId() + "-" + workout.getWorkoutType() + "-" + workout.getTableCode();
        detailVO.setEventName(eventName);
        detailVO.setAudioJsonList(audioI18nMap.get(workout.getId()));
        List<ProjSevenmWorkoutGenerateExerciseVideo> workoutVideoRelationList = videoWorkoutRelationMap.get(workout.getId());
        Map<Integer, List<ProjSevenmWorkoutGenerateExerciseVideo>> currentWorkoutVideoRelationMap = workoutVideoRelationList.stream().collect(Collectors.groupingBy(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmTemplateExerciseGroupId));
        List<ProjSevenmTemplateExerciseGroup> exerciseGroupList = exerciseGroupMap.get(workout.getProjSevenmTemplateId());
        List<ProjSevenmWorkoutDetailGroupVO> exerciseGroupVOList = new ArrayList<>();
        for (ProjSevenmTemplateExerciseGroup exerciseGroup : exerciseGroupList) {
            ProjSevenmWorkoutDetailGroupVO groupVO = new ProjSevenmWorkoutDetailGroupVO();
            List<ProjSevenmWorkoutGenerateExerciseVideo> exerciseVideoList = currentWorkoutVideoRelationMap.get(exerciseGroup.getId());
            List<ProjSevenmExerciseVideoDetailVO> detailVOList = new ArrayList<>(exerciseVideoList.size());
            for (ProjSevenmWorkoutGenerateExerciseVideo relation : exerciseVideoList) {
                ProjSevenmExerciseVideoDetailVO videoDetailVO = videoMap.get(relation.getProjSevenmExerciseVideoId());
                videoDetailVO.setExerciseCircuit(exerciseGroup.getRounds())
                        .setPreviewDuration(relation.getPreviewDuration())
                        .setVideoDuration(relation.getVideoDuration());
                detailVOList.add(videoDetailVO);
            }
            groupVO.setGroupType(exerciseGroup.getGroupType())
                    .setGroupName(exerciseGroup.getGroupName())
                    .setCount(detailVOList.size())
                    .setRounds(exerciseGroup.getRounds())
                    .setVideoList(detailVOList);
            exerciseGroupVOList.add(groupVO);
        }
        detailVO.setGroupList(exerciseGroupVOList);
        return detailVO;
    }

    private List<ProjSevenmWorkoutGenerate> doMatchWorkout(SevenTargetMappingCountBO targetMappingCount,
                                                           String keyTemplate,
                                                           Map<String, List<ProjSevenmWorkoutGenerate>> workoutMap,
                                                           SevenmTemplateTypeEnums workoutType,
                                                           Integer count) {
        SevenmTargetMappingEnums targetMapping = targetMappingCount.getTargetMapping();
        List<SevenmTargetEnums> targetList = targetMapping.getTargetList();
        Integer sum = SevenmTargetEnums.getSum(targetList);
        String key = String.format(keyTemplate, workoutType, sum);
        List<ProjSevenmWorkoutGenerate> workoutList = workoutMap.get(key);
        List<ProjSevenmWorkoutGenerate> matchedWorkoutList = new ArrayList<>();
        if (CollUtil.isEmpty(workoutList)) {
            return matchedWorkoutList;
        }
        matchedWorkoutList.addAll(CollUtil.sub(workoutList, 0, count));
        return matchedWorkoutList;
    }

    private List<ProjSevenmWorkoutGenerate> list(ProjSevenmDailyReq req, Set<Integer> templateIdSet) {

        SevenmGenderEnums gender = req.getGender();

        LambdaQueryWrapper<ProjSevenmWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmWorkoutGenerate::getDifficulty, req.getDifficulty())
                .eq(ProjSevenmWorkoutGenerate::getGender, gender)
                .eq(ProjSevenmWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmWorkoutGenerate::getWorkoutType, Arrays.asList(SEVENM_STRETCH_WORKOUT,SEVENM_NORMAL_WORKOUT))
                .in(ProjSevenmWorkoutGenerate::getProjSevenmTemplateId, templateIdSet);
        Set<SevenmSpecialLimitEnums> specialLimitSet = req.getSpecialLimitSet();
        if(CollUtil.isNotEmpty(specialLimitSet)){
            Integer sum = SevenmSpecialLimitEnums.getSum(specialLimitSet);
            wrapper.apply(" special_limit & {0} = {0} ",sum);
        }
        return baseMapper.selectList(wrapper);
    }


}




