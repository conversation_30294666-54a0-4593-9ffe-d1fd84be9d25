package com.laien.cmsapp.oog101.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjUtil;
import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutPub;
import com.laien.cmsapp.oog101.request.CommonRefreshReq;
import com.laien.cmsapp.oog101.request.CommonRefreshWrapperReq;
import com.laien.cmsapp.oog101.request.ProjSevenmManualWorkoutListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmManualWorkoutDetailVO;
import com.laien.cmsapp.oog101.response.ProjSevenmManualWorkoutListVO;
import com.laien.cmsapp.oog101.service.IProjSevenmManualWorkoutService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.oog101.enums.SevenmWorkoutCategoryEnums;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * app端：SevenmManualWorkout controller
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Api(tags = "app端：SevenmManualWorkout")
@RestController
@RequestMapping("/{appCode}/sevenmManualWorkout")
@AllArgsConstructor
public class ProjSevenmManualWorkoutController extends ResponseController {

    private final IProjSevenmManualWorkoutService service;

    @ApiOperation(value = "SevenmManualWorkout detail v1",tags = {"oog101"})
    @GetMapping("/v1/detailList")
    public ResponseResult<List<ProjSevenmManualWorkoutDetailVO>> detailList(CommonRefreshWrapperReq req) {
        List<ProjSevenmManualWorkoutDetailVO> vos = service.selectDetailsByIds(GlobalConstant.DEFAULT_LANGUAGE,
                req.getReq().stream().map(CommonRefreshReq::getId).collect(Collectors.toList()));
        //过滤非英语的audioJson
        for (ProjSevenmManualWorkoutDetailVO vo : vos) {
            vo.getAudioJsonList().removeIf(audio -> !audio.getLanguage().equals(GlobalConstant.DEFAULT_LANGUAGE));
        }
        return succ(vos);
    }

    @ApiOperation(value = "SevenmManualWorkout detail v2",tags = {"oog101"})
    @GetMapping("/v2/detailList")
    public ResponseResult<List<ProjSevenmManualWorkoutDetailVO>> detailListV2(CommonRefreshWrapperReq req) {
        return succ(service.selectDetailsByIds(RequestContextUtils.getLanguage(),
                req.getReq().stream().map(CommonRefreshReq::getId).collect(Collectors.toList())));
    }

    @ApiOperation(value = "SevenmManualWorkout list v1",tags = {"oog101"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjSevenmManualWorkoutListVO>> list(ProjSevenmManualWorkoutListReq req) {
        req.setOrderByColumn(ListUtil.of(Pair.of(false,ProjSevenmManualWorkoutPub::getNewTimeStart)));
        req.setLang(GlobalConstant.DEFAULT_LANGUAGE);
        return succ(service.selectList(req));
    }

    @ApiOperation(value = "SevenmManualWorkout list v2",tags = {"oog101"})
    @GetMapping("/v2/list")
    public ResponseResult<List<ProjSevenmManualWorkoutListVO>> listV2(ProjSevenmManualWorkoutListReq req) {
        req.setOrderByColumn(new ArrayList<>());
        if (ObjUtil.equal(req.getCategory(), SevenmWorkoutCategoryEnums.HITT_ZONE)) {
            req.getOrderByColumn().add(Pair.of(true,ProjSevenmManualWorkoutPub::getSort));
        }
        req.getOrderByColumn().add(Pair.of(false,ProjSevenmManualWorkoutPub::getNewTimeStart));
        req.setLang(RequestContextUtils.getLanguage());
        return succ(service.selectList(req));
    }

}
