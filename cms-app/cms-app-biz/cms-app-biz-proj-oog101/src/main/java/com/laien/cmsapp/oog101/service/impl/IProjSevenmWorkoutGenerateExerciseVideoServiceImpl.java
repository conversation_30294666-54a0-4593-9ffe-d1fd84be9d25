package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog101.entity.ProjSevenmWorkoutGenerateExerciseVideo;
import com.laien.cmsapp.oog101.mapper.ProjSevenmWorkoutGenerateExerciseVideoMapper;
import com.laien.cmsapp.oog101.service.IProjSevenmWorkoutGenerateExerciseVideoService;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_workout_generate_exercise_video】的数据库操作Service实现
* @createDate 2025-05-20 12:11:41
*/
@Service
public class IProjSevenmWorkoutGenerateExerciseVideoServiceImpl extends ServiceImpl<ProjSevenmWorkoutGenerateExerciseVideoMapper, ProjSevenmWorkoutGenerateExerciseVideo>
    implements IProjSevenmWorkoutGenerateExerciseVideoService {


    @Override
    public List<ProjSevenmWorkoutGenerateExerciseVideo> query(Collection<Integer> workoutIds) {
        if(CollUtil.isEmpty(workoutIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjSevenmWorkoutGenerateExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjSevenmWorkoutGenerateExerciseVideo::getProjSevenmWorkoutGenerateId, workoutIds)
                .orderByAsc(BaseModel::getId);
        return baseMapper.selectList(wrapper);
    }
}




