package com.laien.cmsapp.oog101.mapstruct;

import com.laien.cmsapp.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.cmsapp.oog101.response.ProjSevenmExerciseVideoDetailVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/16
 */
@Mapper(componentModel = "spring")
public interface ProjSevenmExerciseVideoMapStruct {

    List<ProjSevenmExerciseVideoDetailVO> toVOList(List<ProjSevenmExerciseVideo> videos);
}
