package com.laien.cmsapp.oog101.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.cmsapp.oog101.entity.ProjSevenmSound;
import com.laien.common.util.FireBaseUrlSubUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "Sound VO", description = "Sound VO")
public class ProjSevenmSoundVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "文件地址")
    @AbsoluteR2Url
    private String soundUrl;

    public ProjSevenmSoundVO(ProjSevenmSound projSevenmSound) {
        this.id = projSevenmSound.getId();
        this.soundName = projSevenmSound.getSoundName();
        this.soundScript = projSevenmSound.getSoundScript();
        this.soundUrl = projSevenmSound.getUrl();
    }

    /**
     * SoundUrlName返回前端使用
     * @return String
     */
    @ApiModelProperty(value = "文件名称")
    public String getSoundUrlName() {
        return FireBaseUrlSubUtils.getFileName(this.getSoundUrl());
    }
}
