package com.laien.cmsapp.oog101.response;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 7M Manual Workout
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmManualWorkout对象", description = "proj_sevenm_manual_workout")
@TableName(autoResultMap = true)
public class ProjSevenmManualWorkoutListVO extends BaseTableCodeVO {

    @ApiModelProperty(value = "Exercise Name")
    private String name;

    @ApiModelProperty(value = "Event Name, auto-generated")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    private String coverImage;

    @ApiModelProperty(value = "Detail Image, supports png/webp formats")
    @AbsoluteR2Url
    private String detailImage;

    @ApiModelProperty(value = "sketchImage, supports png/webp formats")
    @AbsoluteR2Url
    private String sketchImage;

    @ApiModelProperty(value = "supplementImage, supports png/webp formats")
    @AbsoluteR2Url
    private String supplementImage;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "Difficulty")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "Target Areas")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "Categories")
    private List<SevenmWorkoutCategoryEnums> category;

    @ApiModelProperty(value = "Description, maximum 1000 characters")
    private String description;

    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;

    @AbsoluteR2Url
    @ApiModelProperty(value = "Video URL")
    private String videoUrl;
}
