package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmTemplatePub;
import com.laien.cmsapp.oog101.mapper.ProjSevenmTemplatePubMapper;
import com.laien.cmsapp.oog101.request.ProjSevenmDailyReq;
import com.laien.cmsapp.oog101.service.IProjSevenmTemplatePubService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog101.enums.SevenmSpecialLimitEnums;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_template_pub】的数据库操作Service实现
* @createDate 2025-05-20 12:11:41
*/
@Service
public class IProjSevenmTemplatePubServiceImpl extends ServiceImpl<ProjSevenmTemplatePubMapper, ProjSevenmTemplatePub>
    implements IProjSevenmTemplatePubService {

    @Override
    public List<ProjSevenmTemplatePub> list(ProjSevenmDailyReq req, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjSevenmTemplatePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmTemplatePub::getLevel,req.getDifficulty())
                .eq(ProjSevenmTemplatePub::getProjId,versionInfoBO.getProjId())
                .eq(ProjSevenmTemplatePub::getVersion,versionInfoBO.getCurrentVersion())
                .eq(ProjSevenmTemplatePub::getStatus, GlobalConstant.STATUS_ENABLE);
        Set<SevenmSpecialLimitEnums> specialLimitSet = req.getSpecialLimitSet();
        if(CollUtil.isNotEmpty(specialLimitSet)){
            Integer sum = SevenmSpecialLimitEnums.getSum(specialLimitSet);
            wrapper.apply(" special_limit & {0} = {0} ",sum);
        }
        return baseMapper.selectList(wrapper);
    }

}




