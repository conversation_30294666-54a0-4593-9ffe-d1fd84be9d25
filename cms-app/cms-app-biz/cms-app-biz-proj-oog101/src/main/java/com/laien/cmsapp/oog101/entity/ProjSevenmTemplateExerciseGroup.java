package com.laien.cmsapp.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog101.enums.SevenmTypeEnums;
import com.laien.common.oog101.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
@Data
public class ProjSevenmTemplateExerciseGroup extends BaseModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_TEMPLATE_EXERCISE_GROUP;

    @ApiModelProperty("项目ID")
    private Integer projId;

    @ApiModelProperty("模板ID")
    private Integer projSevenmTemplateId;

    @ApiModelProperty("exercise组名称")
    private String groupName;

    @ApiModelProperty("exercise组类型")
    private SevenmTypeEnums groupType;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("播放循环次数")
    private Integer rounds;











}
