package com.laien.cmsapp.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmWorkoutGenerate;
import com.laien.cmsapp.oog101.request.CommonRefreshReq;
import com.laien.cmsapp.oog101.request.ProjSevenmDailyReq;
import com.laien.cmsapp.oog101.response.ProjSevenmGenerateWorkoutDetailVO;
import com.laien.cmsapp.oog101.response.SevenDailyVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_workout_generate】的数据库操作Service
* @createDate 2025-05-20 12:11:41
*/
public interface IProjSevenmWorkoutGenerateService extends IService<ProjSevenmWorkoutGenerate> {

    SevenDailyVO daily(ProjSevenmDailyReq req, ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<ProjSevenmGenerateWorkoutDetailVO> detailList(List<CommonRefreshReq> reqList, ProjPublishCurrentVersionInfoBO versionInfoBO, String language);
}
