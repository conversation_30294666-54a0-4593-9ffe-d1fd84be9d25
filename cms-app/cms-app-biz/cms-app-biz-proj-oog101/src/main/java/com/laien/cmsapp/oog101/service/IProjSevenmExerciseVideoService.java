package com.laien.cmsapp.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.cmsapp.oog101.response.ProjSevenmExerciseVideoDetailVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_7m_exercise_video 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
public interface IProjSevenmExerciseVideoService extends IService<ProjSevenmExerciseVideo> {

    List<ProjSevenmExerciseVideoDetailVO> ListVOByIdList(Collection<Integer> idList, ProjPublishCurrentVersionInfoBO currentVersion, String lang);

    void handleI18n(List<ProjSevenmExerciseVideo> videoList, String lang);
}
