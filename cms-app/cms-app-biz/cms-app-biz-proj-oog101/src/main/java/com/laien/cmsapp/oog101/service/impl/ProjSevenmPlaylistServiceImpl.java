package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmMusicPub;
import com.laien.cmsapp.oog101.entity.ProjSevenmPlaylistPub;
import com.laien.cmsapp.oog101.entity.ProjSevenmPlaylistRelationPub;
import com.laien.cmsapp.oog101.mapper.ProjSevenmPlaylistPubMapper;
import com.laien.cmsapp.oog101.mapstruct.ProjSevenmPlaylistMapStruct;
import com.laien.cmsapp.oog101.request.ProjSevenmPlaylistListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmPlaylistMusicVO;
import com.laien.cmsapp.oog101.response.ProjSevenmPlaylistVO;
import com.laien.cmsapp.oog101.service.IProjSevenmMusicService;
import com.laien.cmsapp.oog101.service.IProjSevenmPlaylistRelationService;
import com.laien.cmsapp.oog101.service.IProjSevenmPlaylistService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * SevenmPlaylist 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSevenmPlaylistServiceImpl extends
        ServiceImpl<ProjSevenmPlaylistPubMapper, ProjSevenmPlaylistPub>
        implements IProjSevenmPlaylistService {

    private final ProjSevenmPlaylistMapStruct mapStruct;
    private final IProjSevenmPlaylistRelationService relationService;
    private final IProjSevenmMusicService musicService;
    private final IProjLmsI18nService projLmsI18nService;


    @Override
    public List<ProjSevenmPlaylistVO> list(ProjSevenmPlaylistListReq req, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        LambdaQueryWrapper<ProjSevenmPlaylistPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmPlaylistPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjSevenmPlaylistPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjSevenmPlaylistPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(req.getWorkoutType()!=null,ProjSevenmPlaylistPub::getPlaylistType, req.getWorkoutType())
                .orderByAsc(ProjSevenmPlaylistPub::getSortNo)
                .orderByDesc(ProjSevenmPlaylistPub::getId);

        List<ProjSevenmPlaylistPub> entities = this.list(wrapper);
        projLmsI18nService.handleTextI18n(entities, ProjCodeEnums.OOG101,req.getLang());
        Map<Integer, List<ProjSevenmPlaylistMusicVO>> relatedMusic = this.getRelatedMusic(entities, versionInfoBO);
        if (CollUtil.isNotEmpty(entities)) {
            return entities.stream().map(entity -> {
                ProjSevenmPlaylistVO vo = mapStruct.toListVO(entity);
                vo.setMusicList(relatedMusic.get(entity.getId()));
                return vo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private Map<Integer, List<ProjSevenmPlaylistMusicVO>> getRelatedMusic(List<ProjSevenmPlaylistPub> entities, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        if (CollUtil.isEmpty(entities)) return Collections.emptyMap();
        Set<Integer> playlistIds = entities.stream().map(ProjSevenmPlaylistPub::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<ProjSevenmPlaylistRelationPub> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.in(ProjSevenmPlaylistRelationPub::getProjSevenmPlaylistId, playlistIds)
                .eq(ProjSevenmPlaylistRelationPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjSevenmPlaylistRelationPub::getProjId, versionInfoBO.getProjId());
        List<ProjSevenmPlaylistRelationPub> relationPubList = relationService.list(relationWrapper);
        Set<Integer> musicIds = relationPubList.stream().map(ProjSevenmPlaylistRelationPub::getProjSevenmMusicId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(musicIds)) return Collections.emptyMap();
        LambdaQueryWrapper<ProjSevenmMusicPub> musicWrapper = new LambdaQueryWrapper<>();
        musicWrapper.in(ProjSevenmMusicPub::getId, musicIds)
                .eq(ProjSevenmMusicPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjSevenmMusicPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjSevenmMusicPub::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjSevenmMusicPub> musicList = musicService.list(musicWrapper);
        Map<Integer, ProjSevenmMusicPub> musicMap = musicList.stream()
                .collect(Collectors.toMap(ProjSevenmMusicPub::getId, music -> music));
        return relationPubList.stream()
                .filter(r -> musicMap.containsKey(r.getProjSevenmMusicId()))
                .collect(Collectors.groupingBy(ProjSevenmPlaylistRelationPub::getProjSevenmPlaylistId,
                        Collectors.mapping(relation -> mapStruct.toMusicVO(musicMap.get(relation.getProjSevenmMusicId()), relation),
                                Collectors.toList())));
    }
}
