package com.laien.cmsapp.oog101.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog101.enums.SevenmPlaylistTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPlaylist对象", description="项目播放列表")
public class ProjSevenmPlaylistVO extends BaseTableCodeVO {

    @ApiModelProperty(value = "playlist type")
    private SevenmPlaylistTypeEnums playlistType;

    @ApiModelProperty(value = "列表名称")
    private String playlistName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "手机封面图")
    private String phoneCoverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "平板封面图")
    private String tabletCoverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "手机详情图")
    private String phoneDetailImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "平板详情图")
    private String tabletDetailImgUrl;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "relation")
    private List<ProjSevenmPlaylistMusicVO> musicList;

}
