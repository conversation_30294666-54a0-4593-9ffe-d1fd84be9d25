package com.laien.cmsapp.oog101.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog101.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * ExerciseVideo detail VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ExerciseVideo 详情", description = "ExerciseVideo 详情")
public class ProjSevenmExerciseVideoDetailVO extends BaseTableCodeVO {

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "图片地址，支持webp,png")
    private String imageUrl;

    @ApiModelProperty(value = "类型code")
    private SevenmTypeEnums type;

    @ApiModelProperty(value = "exerciseType")
    private SevenmExerciseTypeEnums exerciseType;

    @ApiModelProperty(value = "intensity")
    private SevenmIntensityEnums intensity;

    @ApiModelProperty(value = "难度")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "部位")
    private SevenmPositionEnums position;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制列表")
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "目标")
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "器械")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "指导文本 (500字符限制)")
    private String guidance;

    @ApiModelProperty(value = "如何做 (500字符限制)")
    private String howToDo;

    @AbsoluteR2Url
    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "如何做音频 (mp3格式)")
    private String howToDoAudioUrl;

    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @ApiModelProperty(value = "Exercise Circuit")
    private Integer exerciseCircuit = 1;

    @ApiModelProperty(value = "preview_duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "video_duration")
    private Integer videoDuration;

}
