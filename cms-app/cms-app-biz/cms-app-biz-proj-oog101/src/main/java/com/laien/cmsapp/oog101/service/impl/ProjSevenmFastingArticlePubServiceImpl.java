package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmFastingArticlePub;
import com.laien.cmsapp.oog101.mapper.ProjSevenmFastingArticlePubMapper;
import com.laien.cmsapp.oog101.mapstruct.ProjSevenmFastingArticleMapStruct;
import com.laien.cmsapp.oog101.request.ProjSevenmFastingArticleListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleDetailVO;
import com.laien.cmsapp.oog101.response.ProjSevenmFastingArticleListVO;
import com.laien.cmsapp.oog101.service.IProjSevenmFastingArticlePubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * SevenmFasting article 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSevenmFastingArticlePubServiceImpl extends
        ServiceImpl<ProjSevenmFastingArticlePubMapper, ProjSevenmFastingArticlePub>
        implements IProjSevenmFastingArticlePubService {

    private final ProjSevenmFastingArticleMapStruct mapStruct;
    private final ICoreTextTaskI18nPubService textTaskI18nPubService;
    private final IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjSevenmFastingArticleListVO> list(ProjSevenmFastingArticleListReq req, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjSevenmFastingArticlePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmFastingArticlePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjSevenmFastingArticlePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjSevenmFastingArticlePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(req.getType()!=null,ProjSevenmFastingArticlePub::getType, req.getType())
                .orderByAsc(ProjSevenmFastingArticlePub::getSorted)
                .orderByDesc(ProjSevenmFastingArticlePub::getId);
        List<ProjSevenmFastingArticlePub> fastingArticleList = this.list(wrapper);
        if (CollUtil.isEmpty(fastingArticleList)) {
            return new ArrayList<>();
        }
        projLmsI18nService.handleTextI18n(fastingArticleList, ProjCodeEnums.OOG101, req.getLang());
        return mapStruct.toVOList(fastingArticleList);
    }

    @Override
    public ProjSevenmFastingArticleDetailVO detail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO, String lang) {
        LambdaQueryWrapper<ProjSevenmFastingArticlePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmFastingArticlePub::getVersion, versionInfoBO.getCurrentVersion());
        wrapper.eq(ProjSevenmFastingArticlePub::getId, id);
        ProjSevenmFastingArticlePub fastingArticle = this.getOne(wrapper);
        projLmsI18nService.handleTextI18n(ListUtil.of(fastingArticle), ProjCodeEnums.OOG101,lang);
        if (Objects.isNull(fastingArticle)) {
            return null;
        }
        return mapStruct.toDetailVO(fastingArticle);
    }
}
