package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog101.entity.ProjSevenmTemplateExerciseGroup;
import com.laien.cmsapp.oog101.mapper.ProjSevenmTemplateExerciseGroupMapper;
import com.laien.cmsapp.oog101.service.IProjSevenmTemplateExerciseGroupService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_template_exercise_group】的数据库操作Service实现
* @createDate 2025-05-20 12:11:41
*/
@Service
public class IProjSevenmTemplateExerciseGroupServiceImpl extends ServiceImpl<ProjSevenmTemplateExerciseGroupMapper, ProjSevenmTemplateExerciseGroup>
    implements IProjSevenmTemplateExerciseGroupService {

    @Override
    public List<ProjSevenmTemplateExerciseGroup> query(Collection<Integer> templateIds) {
        if(CollUtil.isEmpty(templateIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjSevenmTemplateExerciseGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjSevenmTemplateExerciseGroup::getProjSevenmTemplateId, templateIds)
                .orderByAsc(ProjSevenmTemplateExerciseGroup::getId);
        return baseMapper.selectList(wrapper);
    }
}




