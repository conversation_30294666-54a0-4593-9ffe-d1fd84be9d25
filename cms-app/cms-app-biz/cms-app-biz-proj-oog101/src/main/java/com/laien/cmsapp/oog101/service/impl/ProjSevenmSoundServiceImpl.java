package com.laien.cmsapp.oog101.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog101.entity.ProjSevenmSound;
import com.laien.cmsapp.oog101.mapper.ProjSevenmSoundMapper;
import com.laien.cmsapp.oog101.request.ProjSevenmSoundReq;
import com.laien.cmsapp.oog101.response.ProjSevenmSoundVO;
import com.laien.cmsapp.oog101.service.IProjSevenmSoundService;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSevenmSoundServiceImpl extends ServiceImpl<ProjSevenmSoundMapper, ProjSevenmSound>
        implements IProjSevenmSoundService {

    @Autowired
    private final IProjLmsI18nService projLmsI18nService;
    @Autowired
    private final FileService fileService;

    /**
     * 按照类型，子类型 , 性别查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    @Override
    public List<ProjSevenmSoundVO> selectSoundList(ProjSevenmSoundReq soundReq) {
        if (Objects.isNull(soundReq.getSoundType())) {
            log.error("The soundReq is null");
            return new ArrayList<>();
        }
        //兜底没有传 gender 时,默认给 female
        if (Objects.isNull(soundReq.getGender())) {
            soundReq.setGender(SevenmGenderEnums.FEMALE);
        }
        List<ProjSevenmSoundVO> result = new ArrayList<>();
        LambdaQueryWrapper<ProjSevenmSound> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(soundReq.getSoundType()), ProjSevenmSound::getSoundType, soundReq.getSoundType())
                .eq(ObjUtil.isNotNull(soundReq.getSoundSubType()), ProjSevenmSound::getSoundSubType, soundReq.getSoundSubType())
                .eq(ObjUtil.isNotNull(soundReq.getGender()), ProjSevenmSound::getGender, soundReq.getGender())
                .eq(ProjSevenmSound::getDelFlag, 0)
                .eq(ProjSevenmSound::getStatus, 1);
        query.orderByDesc(ProjSevenmSound::getId);
        List<ProjSevenmSound> soundList = baseMapper.selectList(query);
        if (soundList == null || soundList.isEmpty()) {
            log.error("The soundList is null");
            return result;
        }
        List<ProjSevenmSound> needI18nList = soundList.stream().filter(ProjSevenmSound::getNeedTranslation).collect(Collectors.toList());
        projLmsI18nService.handleSpeechI18nSingle(CollUtil.newArrayList(needI18nList), ProjCodeEnums.OOG101, soundReq.getLang());
        result = soundList.stream()
                .map(ProjSevenmSoundVO::new)
                .collect(Collectors.toList());
        return result;
    }

}
