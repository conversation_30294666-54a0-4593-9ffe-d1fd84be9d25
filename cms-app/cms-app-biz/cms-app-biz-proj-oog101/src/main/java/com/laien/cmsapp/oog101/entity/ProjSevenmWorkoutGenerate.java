package com.laien.cmsapp.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog101.enums.*;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * proj_sevenm_workout_generate
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjSevenmWorkoutGenerate对象", description = "proj_sevenm_workout_generate")
@TableName(autoResultMap = true)
public class ProjSevenmWorkoutGenerate extends BaseModel {

    private static final long serialVersionUID = -3980786728453076657L;

    @ApiModelProperty(value = "proj_sevenm_template_id")
    private Integer projSevenmTemplateId;

    @ApiModelProperty(value = "proj_sevenm_template_task_id")
    private Integer projSevenmTemplateTaskId;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_WORKOUT_GENERATE;

    @ApiModelProperty(value = "Workout Type")
    private SevenmTemplateTypeEnums workoutType;

    @ApiModelProperty(value = "target code")
    @TableField(typeHandler = SevenmTargetEnums.TypeHandler.class)
    private List<SevenmTargetEnums> target;

    @ApiModelProperty(value = "difficulty code")
    private SevenmDifficultyEnums difficulty;

    @ApiModelProperty(value = "equipment code")
    private SevenmEquipmentEnums equipment;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "特殊限制code (多选, 逗号分隔)")
    @TableField(typeHandler = SevenmSpecialLimitEnums.TypeHandler.class)
    private List<SevenmSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
