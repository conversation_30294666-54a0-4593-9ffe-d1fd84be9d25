package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutExerciseVideoPub;
import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutI18nPub;
import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutPub;
import com.laien.cmsapp.oog101.mapper.ProjSevenmManualWorkoutPubMapper;
import com.laien.cmsapp.oog101.mapstruct.ProjSevenmManualWorkoutMapStruct;
import com.laien.cmsapp.oog101.request.ProjSevenmManualWorkoutListReq;
import com.laien.cmsapp.oog101.response.*;
import com.laien.cmsapp.oog101.service.IProjSevenmExerciseVideoService;
import com.laien.cmsapp.oog101.service.IProjSevenmManualWorkoutExerciseVideoService;
import com.laien.cmsapp.oog101.service.IProjSevenmManualWorkoutI18nService;
import com.laien.cmsapp.oog101.service.IProjSevenmManualWorkoutService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.util.BitmaskEnumUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog101.enums.SevenmTypeEnums;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_sevenm_manual_workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjSevenmManualWorkoutServiceImpl
        extends ServiceImpl<ProjSevenmManualWorkoutPubMapper, ProjSevenmManualWorkoutPub>
        implements IProjSevenmManualWorkoutService {

    private final ProjSevenmManualWorkoutMapStruct mapStruct;
    private final IProjSevenmManualWorkoutExerciseVideoService relationService;
    private final IProjSevenmManualWorkoutI18nService i18nService;
    private final IProjSevenmExerciseVideoService videoService;
    private final IProjLmsI18nService projLmsI18nService;
    @Override
    public List<ProjSevenmManualWorkoutListVO> selectList(ProjSevenmManualWorkoutListReq req) {
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjSevenmManualWorkoutPub> queryWrapper = this.getWorkoutPubLambdaQueryWrapper(null, version);
        if (ObjUtil.isNotNull(req.getOrderByDescColumn())) {
            queryWrapper.orderByDesc(req.getOrderByDescColumn());
        }
        if (ObjUtil.isNotNull(req.getOrderByAscColumn())) {
            queryWrapper.orderByAsc(req.getOrderByAscColumn());
        }
        if (req.isCommon()) {
            queryWrapper.eq(ObjUtil.isNotNull(req.getGender()), ProjSevenmManualWorkoutPub::getGender, req.getGender());
        } else {
            queryWrapper.eq(ObjUtil.isNotNull(req.getGender()), ProjSevenmManualWorkoutPub::getGender, req.getGender());
        }
        BitmaskEnumUtil.addBitmaskCondition(queryWrapper,  ProjSevenmManualWorkoutPub::getCategory,
                req.getCategory()==null?Collections.emptyList():ListUtil.of(req.getCategory()), false);
        List<ProjSevenmManualWorkoutPub> list = this.list(queryWrapper);
        projLmsI18nService.handleTextI18n(list, ProjCodeEnums.OOG101,req.getLang());
        return mapStruct.toVOList(list);
    }

    private LambdaQueryWrapper<ProjSevenmManualWorkoutPub> getWorkoutPubLambdaQueryWrapper(Collection<Integer> idCollection, ProjPublishCurrentVersionInfoBO version) {
        LambdaQueryWrapper<ProjSevenmManualWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(idCollection),ProjSevenmManualWorkoutPub::getId, idCollection);
        queryWrapper.eq(ProjSevenmManualWorkoutPub::getDelFlag, GlobalConstant.NO);
        queryWrapper.eq(ProjSevenmManualWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ProjSevenmManualWorkoutPub::getProjId, version.getProjId());
        queryWrapper.eq(ProjSevenmManualWorkoutPub::getVersion, version.getCurrentVersion());
        return queryWrapper;
    }

    @Override
    public List<ProjSevenmManualWorkoutDetailVO> selectDetailsByIds(String lang, Collection<Integer> idCollection) {

        if (CollUtil.isEmpty(idCollection)) return new ArrayList<>();
        List<ProjSevenmManualWorkoutDetailVO> result = new ArrayList<>();
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();

        //query manualWorkoutPub
        LambdaQueryWrapper<ProjSevenmManualWorkoutPub> queryWrapper = getWorkoutPubLambdaQueryWrapper(idCollection, version);
        queryWrapper.orderByAsc(ProjSevenmManualWorkoutPub::getId);
        List<ProjSevenmManualWorkoutPub> workouts = this.list(queryWrapper);
        if (CollUtil.isEmpty(workouts)) return result;
        projLmsI18nService.handleTextI18n(workouts, ProjCodeEnums.OOG101,lang);

        //query relation
        List<Integer> workoutIds = workouts.stream().map(ProjSevenmManualWorkoutPub::getId).collect(Collectors.toList());
        LambdaQueryWrapper<ProjSevenmManualWorkoutExerciseVideoPub> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.in(ProjSevenmManualWorkoutExerciseVideoPub::getProjSevenmManualWorkoutId, workoutIds);
        relationQueryWrapper.eq(ProjSevenmManualWorkoutExerciseVideoPub::getDelFlag, GlobalConstant.NO);
        relationQueryWrapper.eq(ProjSevenmManualWorkoutExerciseVideoPub::getVersion, version.getCurrentVersion());
        relationQueryWrapper.orderByAsc(ProjSevenmManualWorkoutExerciseVideoPub::getId);
        List<ProjSevenmManualWorkoutExerciseVideoPub> relationList = relationService.list(relationQueryWrapper);
        Map<Integer,List<ProjSevenmManualWorkoutExerciseVideoPub>> relationMap = relationList.stream().
                collect(Collectors.groupingBy(ProjSevenmManualWorkoutExerciseVideoPub::getProjSevenmManualWorkoutId));

        //query exerciseVideo
        Set<Integer> exerciseVideoIds = relationList.stream().map(ProjSevenmManualWorkoutExerciseVideoPub::getProjSevenmExerciseVideoId).collect(Collectors.toSet());
        List<ProjSevenmExerciseVideoDetailVO> videos = videoService.ListVOByIdList(exerciseVideoIds, version, lang);
        Map<Integer, ProjSevenmExerciseVideoDetailVO> videoMap = videos.stream().
                collect(Collectors.toMap(ProjSevenmExerciseVideoDetailVO::getId, Function.identity()));

        //query i18n audio Info
        Map<Integer, List<ProjSevenmManualWorkoutI18nPub>> audioI18nMap = i18nService.list(new LambdaQueryWrapper<ProjSevenmManualWorkoutI18nPub>()
                .in(ProjSevenmManualWorkoutI18nPub::getProjSevenmManualWorkoutId, idCollection)
                .eq(ProjSevenmManualWorkoutI18nPub::getVersion, version.getCurrentVersion())
        ).stream().collect(Collectors.groupingBy(ProjSevenmManualWorkoutI18nPub::getProjSevenmManualWorkoutId));

        //assemble result
        for (ProjSevenmManualWorkoutPub workout : workouts) {
            ProjSevenmManualWorkoutDetailVO vo = mapStruct.toManualWorkoutDetailVO(workout);
            //assemble unitList
            vo.setGroupList(this.assembleGroupList(relationMap.get(workout.getId()), videoMap));
            //assemble audioJsonList
            vo.setAudioJsonList(this.getAudioI18nVOS(audioI18nMap.getOrDefault(workout.getId(),new ArrayList<>())));
            result.add(vo);
        }
        return result;
    }


    private List<AudioI18nVO> getAudioI18nVOS(List<ProjSevenmManualWorkoutI18nPub> pubs) {
        List<AudioI18nVO> audioJsonList = new ArrayList<>();
        //other language
        pubs.forEach(i18n -> {
            AudioI18nVO audioI18nVO = new AudioI18nVO();
            audioI18nVO.setLanguage(i18n.getLanguage()).setAudioJsonUrl(i18n.getAudioJsonUrl());
            audioJsonList.add(audioI18nVO);
        });
        return audioJsonList;
    }

    private List<ProjSevenmWorkoutDetailGroupVO> assembleGroupList(List<ProjSevenmManualWorkoutExerciseVideoPub> relationListByWorkout,
                                                                   Map<Integer, ProjSevenmExerciseVideoDetailVO> videoMap) {
        // Step 1: 映射并设置 exerciseCircuit，同时收集所有非空视频
        // Step 2: 按 unitName 分组并构造返回结构
        return relationListByWorkout.stream()
                .peek(relation -> {
                    ProjSevenmExerciseVideoDetailVO video = videoMap.get(relation.getProjSevenmExerciseVideoId());
                    if (video != null) {
                        // videoDuration减掉previewDuration时长
                        video.setExerciseCircuit(relation.getExerciseCircuit());
                        video.setVideoDuration(relation.getVideoDuration());
                        video.setPreviewDuration(relation.getPreviewDuration());
                    }
                })
                .collect(Collectors.groupingBy(
                        ProjSevenmManualWorkoutExerciseVideoPub::getUnitName,
                        Collectors.mapping(
                                pub -> videoMap.get(pub.getProjSevenmExerciseVideoId()),
                                Collectors.toList()
                        )
                ))
                .entrySet().stream()
                .map(entry -> {
                    ProjSevenmWorkoutDetailGroupVO unitVO = new ProjSevenmWorkoutDetailGroupVO();
                    SevenmTypeEnums typeEnums = entry.getKey();
                    String groupName = typeEnums.getName();
                    unitVO.setGroupName(groupName);
                    unitVO.setGroupType(typeEnums);
                    unitVO.setVideoList(entry.getValue());
                    unitVO.setCount(entry.getValue().size());
                    return unitVO;
                })
                .sorted(Comparator.comparingInt(unit -> unit.getGroupType().ordinal()))
                .collect(Collectors.toList());
    }
}
