package com.laien.cmsapp.oog101.bo;

import cn.hutool.core.collection.CollUtil;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetMappingEnums;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.laien.common.oog101.enums.SevenmTargetMappingEnums.*;


/**
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class SevenTargetMappingCountBO {

    private SevenmTargetMappingEnums targetMapping;
    private Integer count;

    public static final int DAILY_SEVEN_COUNT = 7;

    public SevenTargetMappingCountBO(SevenmTargetMappingEnums targetMapping, Integer count) {
        this.targetMapping = targetMapping;
        this.count = count;
    }

    public static List<SevenTargetMappingCountBO> list(Set<SevenmTargetMappingEnums> targetMappingSet, SevenmGenderEnums gender) {
        if (CollUtil.isEmpty(targetMappingSet)) {
            return new ArrayList<>();
        }
        List<SevenmTargetMappingEnums> targetMappingListCopy = new ArrayList<>(targetMappingSet);
        List<SevenTargetMappingCountBO> targetCountList = new ArrayList<>();
        if (targetMappingSet.size() > 1 && targetMappingListCopy.contains(FULL_BODY)) {
            targetMappingListCopy.remove(FULL_BODY);
            log.error("oog 101 daily target is illegal，has been removed full body,targetMappingSet: {}", targetMappingSet);
        }
        int targetMappingListCopySize = targetMappingListCopy.size();
        int fullBodySize = 4;
        if (targetMappingListCopySize > 1 && targetMappingListCopySize != fullBodySize) {
            int count = (DAILY_SEVEN_COUNT + targetMappingListCopySize - 1) / targetMappingListCopySize;
            for (SevenmTargetMappingEnums targetMapping : targetMappingListCopy) {
                targetCountList.add(new SevenTargetMappingCountBO(targetMapping, count));
            }
            return targetCountList;
        }
        if (targetMappingListCopy.contains(FULL_BODY) || targetMappingListCopy.isEmpty() || targetMappingListCopySize == fullBodySize) {
            targetMappingListCopy.remove(FULL_BODY);
            if (SevenmGenderEnums.FEMALE == gender) {
                targetCountList.add(new SevenTargetMappingCountBO(FULL_BODY, 2));
                targetCountList.add(new SevenTargetMappingCountBO(ABS, 2));
                targetCountList.add(new SevenTargetMappingCountBO(BUTT_LEGS, 2));
                targetCountList.add(new SevenTargetMappingCountBO(ARMS_BACK, 1));
                return targetCountList;
            }
            targetCountList.add(new SevenTargetMappingCountBO(FULL_BODY, 3));
            targetCountList.add(new SevenTargetMappingCountBO(CHEST, 1));
            targetCountList.add(new SevenTargetMappingCountBO(ABS, 1));
            targetCountList.add(new SevenTargetMappingCountBO(BUTT_LEGS, 1));
            targetCountList.add(new SevenTargetMappingCountBO(ARMS_BACK, 1));
            return targetCountList;
        }
        targetCountList.add(new SevenTargetMappingCountBO(targetMappingListCopy.get(0), 5));
        targetCountList.add(new SevenTargetMappingCountBO(FULL_BODY, 2));
        return targetCountList;
    }


}
