package com.laien.cmsapp.oog101.request;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutPub;
import com.laien.cmsapp.requst.LangReq;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmWorkoutCategoryEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *  ProjSevenmManualWorkoutListReq
 *
 * <AUTHOR>
 * @since 2025/05/20
 */
@Data
@ApiModel(value = "manual workout list 请求", description = "manual workout list 请求")
public class ProjSevenmManualWorkoutListReq extends LangReq {

    @ApiModelProperty(value = "workout category")
    private SevenmWorkoutCategoryEnums category;

    @ApiModelProperty(value = "gender")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "是否通用,需要有common-workout的传 true")
    private boolean common;

    /**
     * 用于内部排序的字段，不暴露给Swagger
     * key-asc-true,key-desc-false ,
     * value-排序字段
     */
    @ApiModelProperty(hidden = true)
    private List<Pair<Boolean,SFunction<ProjSevenmManualWorkoutPub, ?>>> orderByColumn;

}
