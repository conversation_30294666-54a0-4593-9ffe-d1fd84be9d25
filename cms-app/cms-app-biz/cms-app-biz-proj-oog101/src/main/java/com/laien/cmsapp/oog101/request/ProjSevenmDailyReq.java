package com.laien.cmsapp.oog101.request;

import com.laien.cmsapp.requst.LangReq;
import com.laien.common.oog101.enums.SevenmDifficultyEnums;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmSpecialLimitEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 *  ProjSevenmDailyReq
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ProjSevenmDailyReq", description = "ProjSevenmDailyReq")
public class ProjSevenmDailyReq extends LangReq {

    @ApiModelProperty(value = "difficulty")
    SevenmDifficultyEnums difficulty;

    @ApiModelProperty("特殊限制")
    private Set<SevenmSpecialLimitEnums> specialLimitSet;

    @ApiModelProperty(value = "性别")
    private SevenmGenderEnums gender;

    @ApiModelProperty(value = "target code")
    private Set<SevenmTargetEnums> targetSet;

    @ApiModelProperty(value = "0-20")
    private Integer random;
}
