package com.laien.cmsapp.oog101.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog101.entity.ProjSevenmManualWorkoutPub;
import com.laien.cmsapp.oog101.request.ProjSevenmManualWorkoutListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmManualWorkoutDetailVO;
import com.laien.cmsapp.oog101.response.ProjSevenmManualWorkoutListVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_sevenm_manual_workout 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public interface IProjSevenmManualWorkoutService extends IService<ProjSevenmManualWorkoutPub> {

    List<ProjSevenmManualWorkoutListVO> selectList(ProjSevenmManualWorkoutListReq req);

    List<ProjSevenmManualWorkoutDetailVO> selectDetailsByIds(String lang, Collection<Integer> idCollection);
}
