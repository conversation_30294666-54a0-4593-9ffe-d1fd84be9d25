package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmWorkoutImagePub;
import com.laien.cmsapp.oog101.mapper.ProjSevenmWorkoutImagePubMapper;
import com.laien.cmsapp.oog101.response.ProjSevenmWorkoutImageVO;
import com.laien.cmsapp.oog101.service.IProjSevenmWorkoutImagePubService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_sevenm_workout_image_pub】的数据库操作Service实现
* @createDate 2025-05-20 12:11:41
*/
@Service
public class IProjSevenmWorkoutImagePubServiceImpl extends ServiceImpl<ProjSevenmWorkoutImagePubMapper, ProjSevenmWorkoutImagePub>
    implements IProjSevenmWorkoutImagePubService {


    @Override
    public List<ProjSevenmWorkoutImageVO> query(SevenmGenderEnums gender, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjSevenmWorkoutImagePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmWorkoutImagePub::getGender, gender)
                .eq(ProjSevenmWorkoutImagePub::getProjId,versionInfoBO.getProjId())
                .eq(ProjSevenmWorkoutImagePub::getVersion,versionInfoBO.getCurrentVersion())
                .eq(ProjSevenmWorkoutImagePub::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjSevenmWorkoutImageVO> imageVOList = new ArrayList<>();
        List<ProjSevenmWorkoutImagePub> imageList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(imageList)){
            return imageVOList;
        }
        for (ProjSevenmWorkoutImagePub image : imageList) {
            ProjSevenmWorkoutImageVO imageVO = new ProjSevenmWorkoutImageVO();
            BeanUtils.copyProperties(image,imageVO);
            imageVOList.add(imageVO);
        }
        return imageVOList;
    }
}




