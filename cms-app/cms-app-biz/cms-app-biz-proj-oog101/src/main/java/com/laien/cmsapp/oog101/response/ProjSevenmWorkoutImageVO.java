package com.laien.cmsapp.oog101.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog101.enums.SevenmGenderEnums;
import com.laien.common.oog101.enums.SevenmTargetEnums;
import com.laien.common.oog101.enums.TableCodeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Data
@Accessors(chain = true)
public class ProjSevenmWorkoutImageVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode;

    @ApiModelProperty("图片名称")
    private String name;

    @ApiModelProperty(value = "Target Areas")
    @TableField(typeHandler = SevenmTargetEnums.TypeHandler.class)
    private List<SevenmTargetEnums> target;

    @ApiModelProperty("封面图片地址")
    @AbsoluteR2Url
    private String coverImage;

    @ApiModelProperty("详情图片地址")
    @AbsoluteR2Url
    private String detailImage;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private SevenmGenderEnums gender;

}
