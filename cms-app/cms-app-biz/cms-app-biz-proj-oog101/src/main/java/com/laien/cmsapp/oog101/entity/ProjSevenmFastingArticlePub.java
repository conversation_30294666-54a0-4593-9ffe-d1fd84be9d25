package com.laien.cmsapp.oog101.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.TranslationTaskTypeEnums;
import com.laien.common.oog101.enums.SevenmFastingArticleEnums;
import com.laien.common.oog101.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *     Proj7MFastingArticlePub
 * </p>
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(autoResultMap = true)
@ApiModel(value="Proj7MFastingArticlePub对象", description="Proj7M fasting article pub")
public class ProjSevenmFastingArticlePub extends BaseModel  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SEVENM_FASTING_ARTICLE;

    @ApiModelProperty(value = "动作展示名称")
    @AppTextTranslateField
    private String titleName;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型")
    private SevenmFastingArticleEnums type;

    @ApiModelProperty(value = "内容")
    @AppTextTranslateField(type = TranslationTaskTypeEnums.HTML)
    private String content;

    @ApiModelProperty(value = "参考文档")
    private String reference;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer sorted;
}
