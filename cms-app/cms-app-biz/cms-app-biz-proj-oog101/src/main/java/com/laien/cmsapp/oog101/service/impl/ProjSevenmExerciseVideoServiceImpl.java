package com.laien.cmsapp.oog101.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.entity.ProjSevenmExerciseVideo;
import com.laien.cmsapp.oog101.mapper.ProjSevenmExerciseVideoMapper;
import com.laien.cmsapp.oog101.mapstruct.ProjSevenmExerciseVideoMapStruct;
import com.laien.cmsapp.oog101.response.ProjSevenmExerciseVideoDetailVO;
import com.laien.cmsapp.oog101.service.IProjSevenmExerciseVideoService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * proj_7m_exercise_video 服务类实现
 * <p>
 *
 * <AUTHOR>
 * @since 2025/05/09
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjSevenmExerciseVideoServiceImpl extends ServiceImpl<ProjSevenmExerciseVideoMapper, ProjSevenmExerciseVideo>
        implements IProjSevenmExerciseVideoService {

    private final ProjSevenmExerciseVideoMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjSevenmExerciseVideoDetailVO> ListVOByIdList(Collection<Integer> idList, ProjPublishCurrentVersionInfoBO currentVersion, String lang) {
        if (idList.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ProjSevenmExerciseVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjSevenmExerciseVideo::getProjId, currentVersion.getProjId())
                .eq(ProjSevenmExerciseVideo::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjSevenmExerciseVideo::getId, idList)
                .orderByDesc(ProjSevenmExerciseVideo::getId);
        List<ProjSevenmExerciseVideo> videos = this.list(wrapper);
        handleI18n(videos, lang);
        return mapStruct.toVOList(videos);
    }

    @Override
    public void handleI18n(List<ProjSevenmExerciseVideo> videoList, String lang) {
        projLmsI18nService.handleSpeechI18n(videoList, ProjCodeEnums.OOG101,lang);
        for (ProjSevenmExerciseVideo video : videoList) {

            List<AudioTranslateResultModel> nameResults = video.getNameResult();
            List<AudioTranslateResultModel> guidanceResults = video.getGuidanceResult();

            AudioTranslateResultModel nameResult;
            AudioTranslateResultModel guidanceResult;

            if (CollUtil.isNotEmpty(nameResults) && (nameResult = nameResults.get(0)) != null) {
                video.setName(nameResult.getText())
                     .setNameAudioUrl(nameResult.getAudioUrl())
                     .setNameAudioDuration(nameResult.getDuration());
            }

            if (CollUtil.isNotEmpty(guidanceResults) && (guidanceResult = guidanceResults.get(0)) != null) {
                video.setGuidance(guidanceResult.getText())
                     .setGuidanceAudioUrl(guidanceResult.getAudioUrl())
                     .setGuidanceAudioDuration(guidanceResult.getDuration());
            }

/*            AudioTranslateResultModel howToDoResult = howToDoMap.get(le);
            if (howToDoResult != null) {
                video.setHowToDo(howToDoResult.getText())
                     .setHowToDoAudioUrl(howToDoResult.getAudioUrl())
                     .setHowToDoAudioDuration(howToDoResult.getDuration());
            }*/
        }
    }

}
