package com.laien.cmsapp.oog101.config;

import com.laien.common.oog101.convert.SevenmEnumConverter;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2023/9/26
 */
@Configuration
@ComponentScan(value = "com.laien.cmsapp.oog101")
@MapperScan({"com.laien.cmsapp.oog101.mapper"})
public class Oog101AutoConfiguration implements WebMvcConfigurer {
    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new SevenmEnumConverter.GenderConverter());
        registry.addConverter(new SevenmEnumConverter.TargetConverter());
    }
}
