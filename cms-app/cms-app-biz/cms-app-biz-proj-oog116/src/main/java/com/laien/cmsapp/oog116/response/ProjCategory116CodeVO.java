package com.laien.cmsapp.oog116.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note:
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Data
@ApiModel(value = "category116 list", description = "category116 list")
public class ProjCategory116CodeVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "category名称")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "workout list")
    private List<ProjWorkout116ListV4WithEnumStrVO> workoutList;

    @AbsoluteR2Url
    @ApiModelProperty(value = "icon url")
    private String iconUrl;

}
