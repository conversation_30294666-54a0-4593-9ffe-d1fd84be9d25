package com.laien.cmsapp.oog116.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ProjWorkout116GenerateResVideo116;
import com.laien.cmsapp.oog116.mapper.ProjWorkout116GenerateResVideo116Mapper;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateResVideo116Service;
import org.springframework.stereotype.Service;

/**
 * <p>
 * proj_workout116_generate和res_video116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjWorkout116GenerateResVideo116ServiceImpl extends ServiceImpl<ProjWorkout116GenerateResVideo116Mapper, ProjWorkout116GenerateResVideo116> implements IProjWorkout116GenerateResVideo116Service {


    @Override
    public void findByProjTemplate116Id(Integer template116Id) {

        return;
    }
}
