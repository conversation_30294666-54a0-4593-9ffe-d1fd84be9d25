package com.laien.cmsapp.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: Workout116 Detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Stage116VO", description = "Stage116VO")
public class Stage116VO {

    @ApiModelProperty(value = "阶段名称")
    private String name;
    /**
     * workout列表
     */
    @ApiModelProperty(value = "workout list")
    private List<ProjWorkout116DetailVO> workoutList;
}
