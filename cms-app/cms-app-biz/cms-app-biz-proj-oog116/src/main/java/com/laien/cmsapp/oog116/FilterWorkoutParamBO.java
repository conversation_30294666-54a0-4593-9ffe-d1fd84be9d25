package com.laien.cmsapp.oog116;

import com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.oog116.enums.Position116Enums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class FilterWorkoutParamBO {
    private List<ExerciseType116Enums> exerciseTypeEnumList;
    private List<ProjWorkout116DetailVO> workoutList;
    private Position116Enums positionEnum;
    private Set<String> equipmentNameSet;
    private Gender116Enums coachGenderEnum;
}
