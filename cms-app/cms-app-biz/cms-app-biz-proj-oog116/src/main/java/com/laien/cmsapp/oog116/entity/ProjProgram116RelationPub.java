package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: hhl
 * @date: 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjProgram116RelationPub对象", description="proj program 116 relation pub")
public class ProjProgram116RelationPub extends BaseModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    private Integer projProgram116Id;

    private Integer projWorkout116Id;

    private Integer projId;
}
