package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog116.entity.ProjWorkout116I18nPub;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * proj_category116多语言表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface IProjWorkout116I18nPubService extends IService<ProjWorkout116I18nPub> {

    /**
     * 根据ID查询多语言
     *
     * @param ids ids
     * @return map
     */
    Map<Integer, ProjWorkout116I18nPub> getByIds(List<Integer> ids);

    /**
     * 根据ID查询所有多语言
     *
     * @param ids ids
     * @return map
     */
    Map<Integer, List<ProjWorkout116I18nPub>> getAllLanguageListByIds(List<Integer> ids);
}
