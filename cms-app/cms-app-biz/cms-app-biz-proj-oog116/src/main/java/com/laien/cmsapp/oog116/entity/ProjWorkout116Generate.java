package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 116生成的workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116Generate对象", description="116生成的workout")
public class ProjWorkout116Generate extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_template116_id")
    private Integer projTemplate116Id;

    @ApiModelProperty(value = "proj_template116_task_id")
    private Integer projTemplate116TaskId;

    @ApiModelProperty(value = "res_image_id")
    private Integer resImageId;

    @ApiModelProperty(value = "Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String restriction;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "生成workout的时候的限制（限制枚举值的和）")
    private Integer restrictionSum;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

}
