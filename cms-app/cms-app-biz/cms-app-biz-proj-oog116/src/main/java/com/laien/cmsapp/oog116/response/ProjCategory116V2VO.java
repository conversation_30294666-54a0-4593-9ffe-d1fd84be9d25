package com.laien.cmsapp.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "category116 v2 list", description = "category116 v2 list")
public class ProjCategory116V2VO {

    @ApiModelProperty(value = "lable 类型category")
    private List<ProjCategory116LableVO> labelList;

    @ApiModelProperty(value = "card 类型category")
    private List<ProjCategory116VO> cardList;

    @ApiModelProperty(value = "grid 类型category")
    private List<ProjCategory116GridVO> gridList;

}
