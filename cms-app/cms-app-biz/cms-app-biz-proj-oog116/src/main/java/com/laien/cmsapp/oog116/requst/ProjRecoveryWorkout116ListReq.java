package com.laien.cmsapp.oog116.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * <p>
 * ProjRecoveryWorkout116ListReq 列表
 * <p>
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Data
@ApiModel(value = "ProjRecoveryWorkout116ListReq 列表", description = "ProjRecoveryWorkout116ListReq")
public class ProjRecoveryWorkout116ListReq {

    @ApiModelProperty(value = "regionCodeSet: 10-Neck, 11-Should<PERSON>, 12-<PERSON><PERSON>, 13-Back, 14-S<PERSON> Joint, 15-Knee, 16-Hip, 17-Ankle")
    private Set<Integer> regionCodeSet = new HashSet<>();

    @ApiModelProperty(value = "categoryTypeCodeSet: 10-Joint Pain Relief, 11-Balance, 12-Flexibility, 13-Maintain Strength, 14-Reduce Stress")
    private Set<Integer> categoryTypeCodeSet = new HashSet<>();

}
