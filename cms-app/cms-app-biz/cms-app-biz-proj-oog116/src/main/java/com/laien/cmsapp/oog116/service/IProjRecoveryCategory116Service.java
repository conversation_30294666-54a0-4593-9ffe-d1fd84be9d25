package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog116.entity.ProjRecoveryCategory116Pub;
import com.laien.cmsapp.oog116.requst.RecoveryCategoryListReq;
import com.laien.cmsapp.oog116.response.ProjRecoveryCategory116ListVO;

/**
 * <p>
 * Recovery Category116 发布表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IProjRecoveryCategory116Service extends IService<ProjRecoveryCategory116Pub> {

    /**
     * Recovery Category v2 列表查询
     *
     * @param req 请求参数
     * @param containsTypes 包含的运动类型
     * @return ProjRecoveryCategory116V2VO
     */
    ProjRecoveryCategory116ListVO list(RecoveryCategoryListReq req);

}
