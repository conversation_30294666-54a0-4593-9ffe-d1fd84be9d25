package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.core.enums.util.EnumBaseUtils;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * note: Workout116 list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout116 list", description = "Workout116 list")
public class ProjRecoveryWorkout116ListVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "难度10:Easy,11:Medium,12:Hard")
    private Integer difficultyCode;

    @ApiModelProperty(value = "性别，Female:10,Male:11,Both:12")
    private Integer genderCode;

    @ApiModelProperty(value = "Standing:10,Seated:11,Both:12")
    private Integer positionCode;

    @ApiModelProperty(value = "Exercise 类型：20: Recovery")
    private Integer exerciseTypeCode;

    @ApiModelProperty(value = "身体部位 (多选)",hidden = true)
    @JsonIgnore
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)",hidden = true)
    @JsonIgnore
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具 (多选)",hidden = true)
    @JsonIgnore
    private List<SupportProp116Enums> supportProp;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Boolean subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "总时长:毫秒")
    private Integer duration;

    @ApiModelProperty(value = "regionCodeSet: 10-Neck, 11-Shoulder, 12-Wrist, 13-Back, 14-SI Joint, 15-Knee, 16-Hip, 17-Ankle")
    public Set<Integer> getRegionCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getRegion());
    }

    @ApiModelProperty(value = "supportPropCodeSet: 10-Chair, 11-Mat, 12-None")
    public Set<Integer> getSupportPropCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getSupportProp());
    }

    @ApiModelProperty(value = "focusCodeSet: 10-Relax, 11-Mobility, 12-Strength")
    public Set<Integer> getFocusCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getFocus());
    }

}
