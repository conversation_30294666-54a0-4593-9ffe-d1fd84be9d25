package com.laien.cmsapp.oog116.config;

import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.oog116.enums.WorkoutGenerate116ImagePoint;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * cms 移动端 oog120需要特殊处理的workout
 * 具体看需求cms_2024_02（https://www.xiaopiu.com/h5/byId?type=project&id=627c6edaecd2105ef7a7438a&isprd=true）
 *
 * <AUTHOR>
 */

@Configuration
@ConfigurationProperties(prefix = "cms-app.oog116")
@Data
public class Oog116Config {
    private List<Integer> standingWorkoutIdList;
    private List<Integer> seatedWorkoutIdList;
    private List<Integer> bothWorkoutIdList;

    private List<Integer> femaleStandingWorkoutIdList;
    private List<Integer> femaleSeatedWorkoutIdList;
    private List<Integer> femaleBothWorkoutIdList;

    private List<Integer> maleStandingWorkoutIdList;
    private List<Integer> maleSeatedWorkoutIdList;
    private List<Integer> maleBothWorkoutIdList;
    private Map<WorkoutGenerate116ImagePoint, List<Integer>> femaleWorkoutIdMap = new HashMap<>();
    private Map<WorkoutGenerate116ImagePoint, List<Integer>> maleWorkoutIdMap = new HashMap<>();
    private Integer fixCount = 1;

    private Integer taiChiFixedWorkoutCount = 1;


}
