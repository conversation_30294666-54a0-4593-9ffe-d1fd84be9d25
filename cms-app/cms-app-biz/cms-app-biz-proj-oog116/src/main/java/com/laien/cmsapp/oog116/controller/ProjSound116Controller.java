package com.laien.cmsapp.oog116.controller;


import com.laien.cmsapp.oog116.requst.ProjSound116Req;
import com.laien.cmsapp.oog116.response.ProjSound116VO;
import com.laien.cmsapp.oog116.service.IProjSound116Service;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "app端:sound116")
@RestController
@RequestMapping("/oog116/sound116")
public class ProjSound116Controller extends ResponseController {

    @Resource
    private IProjSound116Service soundService;

    @ApiOperation(value = "Sound116 list v1", tags = {"oog116"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjSound116VO>> list(ProjSound116Req soundReq) {
        List<ProjSound116VO> soundList = soundService.selectSoundList(soundReq);
        return succ(soundList);
    }

}