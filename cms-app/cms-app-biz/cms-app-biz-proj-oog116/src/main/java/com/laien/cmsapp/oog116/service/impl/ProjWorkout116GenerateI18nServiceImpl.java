package com.laien.cmsapp.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ProjWorkout116GenerateI18n;
import com.laien.cmsapp.oog116.mapper.ProjWorkout116GenerateI18nMapper;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateI18nService;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_workout116_generate i18n 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Service
public class ProjWorkout116GenerateI18nServiceImpl extends ServiceImpl<ProjWorkout116GenerateI18nMapper, ProjWorkout116GenerateI18n> implements IProjWorkout116GenerateI18nService {

    @Override
    public Map<Integer, ProjWorkout116GenerateI18n> getByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<ProjWorkout116GenerateI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkout116GenerateI18n::getLanguage, RequestContextUtils.getLanguage())
                .in(ProjWorkout116GenerateI18n::getId, ids);
        return this.list(queryWrapper).stream().collect(Collectors.toMap(BaseModel::getId, o -> o));
    }

    @Override
    public Map<Integer, List<ProjWorkout116GenerateI18n>> getAllLanguageListByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<ProjWorkout116GenerateI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjWorkout116GenerateI18n::getId, ids);
        return this.list(queryWrapper).stream().collect(Collectors.groupingBy(ProjWorkout116GenerateI18n::getId));
    }
}
