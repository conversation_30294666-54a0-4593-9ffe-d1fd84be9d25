package com.laien.cmsapp.oog116.requst;

import com.laien.common.oog116.enums.DurationLabel116Enums;
import com.laien.common.oog116.enums.PlanType116Enums;
import com.laien.common.oog116.enums.Position116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: ProjTemplate116PlanReq
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "oog116 template plan req", description = "oog116 template plan req")
public class Workout116GeneratePlanReq {

    @ApiModelProperty(value = "时长标签")
    private DurationLabel116Enums duration;

    @ApiModelProperty(value = "position")
    private Position116Enums position;

    @ApiModelProperty(value = "限制")
    private List<String> restriction;

    @ApiModelProperty(value = "random")
    private Integer random;

    @ApiModelProperty(value = "dataType")
    private PlanType116Enums planType;

    @ApiModelProperty(value = "完成次数")
    private Integer completeTimes;

}
