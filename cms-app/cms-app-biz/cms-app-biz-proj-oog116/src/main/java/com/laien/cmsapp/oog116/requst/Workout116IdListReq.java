package com.laien.cmsapp.oog116.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ids list列表参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="id list", description="")
public class Workout116IdListReq {

    @ApiModelProperty(value = "id list", required = true)
    private List<Integer> generateWorkoutIdList;

    @ApiModelProperty(value = "id list", required = true)
    private List<Integer> assembleWorkoutIdList;

}
