package com.laien.cmsapp.oog116.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Workout116 audio
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout116 audio", description = "Workout116 audio")
public class ProjWorkout116AudioDetailVO {

    @ApiModelProperty(value = "language")
    private String language;

    @ApiModelProperty(value = "audio json url")
    @AbsoluteR2Url
    private String audioJsonUrl;

}
