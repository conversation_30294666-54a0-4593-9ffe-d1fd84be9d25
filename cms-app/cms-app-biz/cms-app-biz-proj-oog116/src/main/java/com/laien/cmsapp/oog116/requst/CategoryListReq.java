package com.laien.cmsapp.oog116.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * categoryListReq
 *
 * <AUTHOR>
 * @since 2025/03/25
 */
@Data
@ApiModel(value="categoryListReq", description="categoryListReq")
public class CategoryListReq {

    @ApiModelProperty(value = "gender: 10-Female 11-Male")
    private Integer gender = 10;

    @ApiModelProperty(value = "当前版本app包含的exerciseTypeCodeSet")
    private Set<Integer> containsExerciseCodeSet;

}
