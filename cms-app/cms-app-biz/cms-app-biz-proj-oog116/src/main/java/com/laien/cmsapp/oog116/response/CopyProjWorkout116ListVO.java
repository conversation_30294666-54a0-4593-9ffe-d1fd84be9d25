package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog116.enums.WorkoutDataType116Enums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CopyProjWorkout116ListVO implements AppTextCoreI18nModel
{
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "难度Easy，Medium，Hard")
    private String difficulty;

    @ApiModelProperty(value = "难度Easy，Medium，Hard",hidden = true)
    private String difficultyEn;

    @ApiModelProperty(value = "Seated/Standing")
    private String position;
    @ApiModelProperty(value = "Seated/Standing",hidden = true)
    private String positionEn;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String restriction;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;",hidden = true)
    private String restrictionEn;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Boolean subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "性别，Female/Male",hidden = true)
    private String genderEn;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing",hidden = true)
    private String exerciseTypeEn;

    @ApiModelProperty(value = "dataType")
    private WorkoutDataType116Enums dataType = WorkoutDataType116Enums.ASSEMBLE;
}
