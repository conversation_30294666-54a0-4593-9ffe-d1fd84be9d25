package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjRecoveryCategory116ProjWorkout116Pub;
import com.laien.cmsapp.oog116.entity.ProjRecoveryCategory116Pub;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Pub;
import com.laien.cmsapp.oog116.mapper.ProjRecoveryCategory116PubMapper;
import com.laien.cmsapp.oog116.requst.RecoveryCategoryListReq;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.IProjRecoveryCategory116ProjWorkout116Service;
import com.laien.cmsapp.oog116.service.IProjRecoveryCategory116Service;
import com.laien.cmsapp.oog116.service.IProjRecoveryWorkout116Service;
import com.laien.cmsapp.oog116.service.IProjWorkout116PubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog116.enums.RecoveryCategory116ShowTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * Recovery Category116 发布表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class ProjRecoveryCategory116ServiceImpl
        extends ServiceImpl<ProjRecoveryCategory116PubMapper, ProjRecoveryCategory116Pub>
        implements IProjRecoveryCategory116Service {

    @Resource
    private IProjWorkout116PubService projWorkout116PubService;
    @Resource
    private IProjRecoveryCategory116ProjWorkout116Service relationService;
    @Resource
    private IProjLmsI18nService projLmsI18nService;
    @Resource
    private IProjRecoveryWorkout116Service recoveryWorkout116Service;

    @Override
    public ProjRecoveryCategory116ListVO list(RecoveryCategoryListReq req) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        if (versionInfoBO == null) {
            log.error("Failed to get version info");
            return new ProjRecoveryCategory116ListVO();
        }

        // 查询所有启用的Recovery Category
        Map<RecoveryCategory116ShowTypeEnums, List<ProjRecoveryCategory116Pub>> categoryMap =
                queryCategoriesByShowType(versionInfoBO);

        if (categoryMap.isEmpty()) {
            log.error("No enabled Recovery Category found");
            return new ProjRecoveryCategory116ListVO();
        }

        // 处理不同展示类型的分类
        List<ProjRecoveryCategory116CardVO> cardList = processCardCategories(
                categoryMap.getOrDefault(RecoveryCategory116ShowTypeEnums.CARD, Collections.emptyList()),
                versionInfoBO);

        List<ProjRecoveryCategory116LabelVO> labelList = processLabelCategories(
                categoryMap.getOrDefault(RecoveryCategory116ShowTypeEnums.LABEL, Collections.emptyList()));

        List<ProjRecoveryCategory116GridVO> gridList = processGridCategories(
                categoryMap.getOrDefault(RecoveryCategory116ShowTypeEnums.GRID, Collections.emptyList()),
                versionInfoBO);

        // 处理国际化
        handleI18nTranslation(cardList, labelList, gridList);

        // 构建返回结果
        ProjRecoveryCategory116ListVO result = new ProjRecoveryCategory116ListVO();
        result.setCardList(cardList);
        result.setLabelList(labelList);
        result.setGridList(gridList);

        return result;
    }

    /**
     * 查询按展示类型分组的分类数据
     */
    private Map<RecoveryCategory116ShowTypeEnums, List<ProjRecoveryCategory116Pub>> queryCategoriesByShowType(
            ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjRecoveryCategory116Pub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                        ProjRecoveryCategory116Pub::getId,
                        ProjRecoveryCategory116Pub::getName,
                        ProjRecoveryCategory116Pub::getEventName,
                        ProjRecoveryCategory116Pub::getShowType,
                        ProjRecoveryCategory116Pub::getIconUrl,
                        ProjRecoveryCategory116Pub::getCoverImgUrl)
                .eq(ProjRecoveryCategory116Pub::getProjId, versionInfoBO.getProjId())
                .eq(ProjRecoveryCategory116Pub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjRecoveryCategory116Pub::getVersion, versionInfoBO.getCurrentVersion())
                .orderByAsc(ProjRecoveryCategory116Pub::getSortNo)
                .orderByDesc(ProjRecoveryCategory116Pub::getId);

        List<ProjRecoveryCategory116Pub> categories = this.list(queryWrapper);
        if (CollUtil.isEmpty(categories)) {
            return Collections.emptyMap();
        }

        return categories.stream()
                .collect(Collectors.groupingBy(ProjRecoveryCategory116Pub::getShowType));
    }

    /**
     * 处理卡片类型的分类数据
     */
    private List<ProjRecoveryCategory116CardVO> processCardCategories(
            List<ProjRecoveryCategory116Pub> cardCategories,
            ProjPublishCurrentVersionInfoBO versionInfoBO) {

        if (CollUtil.isEmpty(cardCategories)) {
            return Collections.emptyList();
        }

        List<Integer> categoryIds = cardCategories.stream()
                .map(ProjRecoveryCategory116Pub::getId)
                .collect(Collectors.toList());

        // 批量查询关联关系
        Map<Integer, List<ProjRecoveryCategory116ProjWorkout116Pub>> relationMap =
                queryCategoryWorkoutRelations(categoryIds, versionInfoBO);

        // 批量查询workout数据
        Map<Integer, ProjWorkout116Pub> workoutMap = batchQueryWorkouts(relationMap, versionInfoBO);

        // 组装结果
        List<ProjRecoveryCategory116CardVO> result = new ArrayList<>(cardCategories.size());
        for (ProjRecoveryCategory116Pub category : cardCategories) {
            List<ProjRecoveryWorkout116ListVO> workoutList = buildWorkoutList(
                    relationMap.getOrDefault(category.getId(), Collections.emptyList()),
                    workoutMap);
            ProjRecoveryCategory116CardVO cardVO = new ProjRecoveryCategory116CardVO();
            BeanUtils.copyProperties(category, cardVO);
            cardVO.setWorkoutList(workoutList);
            result.add(cardVO);
        }

        return result;
    }

    /**
     * 处理标签类型的分类数据
     */
    private List<ProjRecoveryCategory116LabelVO> processLabelCategories(
            List<ProjRecoveryCategory116Pub> labelCategories) {

        if (CollUtil.isEmpty(labelCategories)) {
            return Collections.emptyList();
        }

        return labelCategories.stream()
                .map(category -> {
                    ProjRecoveryCategory116LabelVO labelVO = new ProjRecoveryCategory116LabelVO();
                    BeanUtils.copyProperties(category, labelVO);
                    return labelVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理网格类型的分类数据
     */
    private List<ProjRecoveryCategory116GridVO> processGridCategories(
            List<ProjRecoveryCategory116Pub> gridCategories,
            ProjPublishCurrentVersionInfoBO versionInfoBO) {

        if (CollUtil.isEmpty(gridCategories)) {
            return Collections.emptyList();
        }

        List<Integer> categoryIds = gridCategories.stream()
                .map(ProjRecoveryCategory116Pub::getId)
                .collect(Collectors.toList());

        // 批量查询关联关系
        Map<Integer, List<ProjRecoveryCategory116ProjWorkout116Pub>> relationMap =
                queryCategoryWorkoutRelations(categoryIds, versionInfoBO);

        // 批量查询workout数据
        Map<Integer, ProjWorkout116Pub> workoutMap = batchQueryWorkouts(relationMap, versionInfoBO);

        // 组装结果
        List<ProjRecoveryCategory116GridVO> result = new ArrayList<>(gridCategories.size());
        for (ProjRecoveryCategory116Pub category : gridCategories) {
            List<ProjRecoveryWorkout116ListVO> workoutList = buildWorkoutList(
                    relationMap.getOrDefault(category.getId(), Collections.emptyList()),
                    workoutMap);

            ProjRecoveryCategory116GridVO gridVO = new ProjRecoveryCategory116GridVO();
            BeanUtils.copyProperties(category, gridVO);
            gridVO.setWorkoutCount(workoutList.size());
            gridVO.setWorkoutList(workoutList);
            result.add(gridVO);
        }

        return result;
    }

    /**
     * 查询分类与workout的关联关系
     */
    private Map<Integer, List<ProjRecoveryCategory116ProjWorkout116Pub>> queryCategoryWorkoutRelations(
            List<Integer> categoryIds, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        if (CollUtil.isEmpty(categoryIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<ProjRecoveryCategory116ProjWorkout116Pub> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.in(ProjRecoveryCategory116ProjWorkout116Pub::getProjRecoveryCategory116Id, categoryIds)
                .eq(ProjRecoveryCategory116ProjWorkout116Pub::getVersion, versionInfoBO.getCurrentVersion())
                .orderByAsc(ProjRecoveryCategory116ProjWorkout116Pub::getId);

        List<ProjRecoveryCategory116ProjWorkout116Pub> relations = relationService.list(relationWrapper);
        if (CollUtil.isEmpty(relations)) {
            return Collections.emptyMap();
        }

        return relations.stream()
                .collect(Collectors.groupingBy(ProjRecoveryCategory116ProjWorkout116Pub::getProjRecoveryCategory116Id));
    }

    /**
     * 批量查询workout数据
     */
    private Map<Integer, ProjWorkout116Pub> batchQueryWorkouts(
            Map<Integer, List<ProjRecoveryCategory116ProjWorkout116Pub>> relationMap,
            ProjPublishCurrentVersionInfoBO versionInfoBO) {

        if (relationMap.isEmpty()) {
            return Collections.emptyMap();
        }

        Set<Integer> workoutIds = relationMap.values().stream()
                .flatMap(List::stream)
                .map(ProjRecoveryCategory116ProjWorkout116Pub::getProjWorkout116Id)
                .collect(Collectors.toSet());

        if (workoutIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return getRecoveryWorkoutList(workoutIds, versionInfoBO);
    }

    /**
     * 构建workout列表
     */
    private List<ProjRecoveryWorkout116ListVO> buildWorkoutList(
            List<ProjRecoveryCategory116ProjWorkout116Pub> relations,
            Map<Integer, ProjWorkout116Pub> workoutMap) {

        if (CollUtil.isEmpty(relations) || workoutMap.isEmpty()) {
            return Collections.emptyList();
        }

        List<ProjRecoveryWorkout116ListVO> workoutList = new ArrayList<>();
        for (ProjRecoveryCategory116ProjWorkout116Pub relation : relations) {
            ProjWorkout116Pub workout = workoutMap.get(relation.getProjWorkout116Id());
            if (workout != null) {
                ProjRecoveryWorkout116ListVO workoutVO = recoveryWorkout116Service.convertToWorkoutListVO(workout);
                workoutList.add(workoutVO);
            }
        }

        return workoutList;
    }

    /**
     * 处理国际化翻译
     */
    private void handleI18nTranslation(List<ProjRecoveryCategory116CardVO> cardList,
                                       List<ProjRecoveryCategory116LabelVO> labelList,
                                       List<ProjRecoveryCategory116GridVO> gridList) {
        List<AppTextCoreI18nModel> transList = new ArrayList<>();

        if (CollUtil.isNotEmpty(cardList)) {
            transList.addAll(cardList);
            transList.addAll(cardList.stream().flatMap(card -> card.getWorkoutList().stream()).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(labelList)) {
            transList.addAll(labelList);
        }
        if (CollUtil.isNotEmpty(gridList)) {
            transList.addAll(gridList);
            transList.addAll(gridList.stream().flatMap(grid -> grid.getWorkoutList().stream()).collect(Collectors.toList()));
        }

        if (!transList.isEmpty()) {
            projLmsI18nService.handleTextI18n(transList, ProjCodeEnums.OOG116);
        }
    }

    private Map<Integer, ProjWorkout116Pub> getRecoveryWorkoutList(Set<Integer> workoutIds, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        if (CollUtil.isEmpty(workoutIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<ProjWorkout116Pub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjWorkout116Pub::getId, workoutIds)
                .eq(ProjWorkout116Pub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjWorkout116Pub::getProjId, versionInfoBO.getProjId());

        List<ProjWorkout116Pub> workoutList = projWorkout116PubService.list(queryWrapper);
        if (CollUtil.isEmpty(workoutList)) {
            return Collections.emptyMap();
        }

        return workoutList.stream()
                .collect(Collectors.toMap(ProjWorkout116Pub::getId, Function.identity()));
    }

}
