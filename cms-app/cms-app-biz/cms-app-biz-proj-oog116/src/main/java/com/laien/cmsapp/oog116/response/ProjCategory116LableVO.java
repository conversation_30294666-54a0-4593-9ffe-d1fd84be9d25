package com.laien.cmsapp.oog116.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note:
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "category116 label list", description = "category116 label list")
public class ProjCategory116LableVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "category名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "icon url")
    private String iconUrl;

}
