package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjProgram116Pub;
import com.laien.cmsapp.oog116.response.ProjProgram116DetailVO;
import com.laien.cmsapp.oog116.response.ProjProgram116ListVO;

import java.util.List;
import java.util.Set;

/**
 * @author: hhl
 * @date: 2025/5/20
 */

public interface IProjProgram116PubService extends IService<ProjProgram116Pub> {


    List<ProjProgram116ListVO> listAllProgram(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang);


    ProjProgram116DetailVO getDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang, Integer id, Integer m3u8Type);

    Set<Integer> getAllProgramWokroutIdSet(ProjPublishCurrentVersionInfoBO versionInfoBO);
}
