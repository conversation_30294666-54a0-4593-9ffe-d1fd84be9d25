package com.laien.cmsapp.oog116.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Recovery Category116 Grid VO
 *
 * <AUTHOR>
 * @since 2025/07/14
 */
@Data
@ApiModel(value = "Recovery Category116 Grid VO", description = "Recovery Category116 Grid VO")
public class ProjRecoveryCategory116GridVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "category名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "workout数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "workout list")
    private List<ProjRecoveryWorkout116ListVO> workoutList;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "icon url")
    private String iconUrl;

}
