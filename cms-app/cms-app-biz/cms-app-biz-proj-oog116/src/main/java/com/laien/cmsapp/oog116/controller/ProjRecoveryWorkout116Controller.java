package com.laien.cmsapp.oog116.controller;


import com.laien.cmsapp.oog116.requst.ProjRecoveryWorkout116ListReq;
import com.laien.cmsapp.oog116.response.ProjRecoveryWorkout116ListVO;
import com.laien.cmsapp.oog116.service.IProjRecoveryWorkout116Service;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * recoveryWorkout116 前端控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/07/16
 */
@Slf4j
@Api(tags = "app端：RecoveryWorkout116")
@RestController
@RequestMapping("/{appCode}/RecoveryWorkout116")
public class ProjRecoveryWorkout116Controller extends ResponseController {

    @Resource
    private IProjRecoveryWorkout116Service recoveryWorkout116Service;

    @ApiOperation(value = "list", tags = {"oog116"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjRecoveryWorkout116ListVO>> list(ProjRecoveryWorkout116ListReq workout116ListReq) {
        return succ(recoveryWorkout116Service.list(workout116ListReq));
    }


}
