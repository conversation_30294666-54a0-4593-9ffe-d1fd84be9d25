package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.core.enums.util.EnumBaseUtils;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * note: Workout116 Detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout116 Detail", description = "Workout116 Detail")
@Accessors(chain = true)
public class ProjWorkout116DetailV4VO  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @JsonIgnore
    @ApiModelProperty(value = "male封面图")
    @AbsoluteR2Url
    private String coverImgMaleUrl;

    @JsonIgnore
    @ApiModelProperty(value = "male详情图")
    @AbsoluteR2Url
    private String detailImgMaleUrl;

    @ApiModelProperty(value = "难度10:Easy,11:Medium,12:Hard")
    private Integer difficultyCode;

    @ApiModelProperty(value = "Standing:10,Seated:11,Both:12")
    private Integer positionCode;

    @ApiModelProperty(value = "限制，Shoulder:10,Back:11,Wrist:12,Knee:13,Ankle:14,Hip:15")
    private Set<Integer> restrictionCodeSet;

    @JsonIgnore
    private Integer restrictionSum;

    @ApiModelProperty(value = "简介")
    private List<String> descriptionList;

    @JsonIgnore
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Boolean subscription = false;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "video的m3u8地址")
    @AbsoluteR2Url
    private String videoUrl;

    @ApiModelProperty(value = "video的2532 m3u8地址")
    @JsonIgnore
    private String video2532Url;

    @JsonIgnore
    private String audioJsonUrl;

    @ApiModelProperty(value = "audio 列表")
    private List<ProjWorkout116AudioDetailVO> audioList;

    @ApiModelProperty(value = "单元组")
    private List<ProjWorkout116UnitDetailV4VO> units;

    @ApiModelProperty(value = "数据类型，GENERATE:10,ASSEMBLE:11")
    private Integer dataTypeCode;

    @ApiModelProperty(value = "性别，Female:10,Male:11,Both:12")
    private Integer genderCode;

    @ApiModelProperty(value = "Exercise 类型：Chair Yoga: 10,Tai Chi: 11,Dancing: 12,Gentle Cardio: 13,Walking: 14,Dumbbell (lightweight): 15,Resistance Band: 16,Chair Yoga:17")
    private Integer exerciseTypeCode;

    @ApiModelProperty(value = "playlistId")
    private Integer playlistId;

    @JsonIgnore
    @ApiModelProperty(value = "imageId")
    private Integer imageId;

    /**
     * 详情页用这个字段
     */
    @ApiModelProperty(value = "equipmentList")
    private List<EquipmentV4VO> equipmentList;


    /**
     * 列表用这个字段
     */
    @ApiModelProperty(value = "workout卡片用的equipment")
    private List<EquipmentV4VO> cardEquipment;

    @ApiModelProperty(value = "身体部位 (多选)",hidden = true)
    @JsonIgnore
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)",hidden = true)
    @JsonIgnore
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具 (多选)",hidden = true)
    @JsonIgnore
    private List<SupportProp116Enums> supportProp;

    @ApiModelProperty(value = "regionCodeSet")
    public Set<Integer> getRegionCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getRegion());
    }

    @ApiModelProperty(value = "supportPropCodeSet")
    public Set<Integer> getSupportPropCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getSupportProp());
    }

    @ApiModelProperty(value = "focusCodeSet")
    public Set<Integer> getFocusCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getFocus());
    }

}
