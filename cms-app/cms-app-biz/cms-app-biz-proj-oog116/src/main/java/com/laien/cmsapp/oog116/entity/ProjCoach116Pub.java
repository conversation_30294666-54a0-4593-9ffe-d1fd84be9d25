package com.laien.cmsapp.oog116.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: hhl
 * @date: 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCoach116Pub对象", description="proj coach 116 pub")
public class ProjCoach116Pub extends BaseModel  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "coverImgUrl")
    private String coverImgUrl;

    @ApiModelProperty(value = "introduction")
    @AppTextTranslateField
    private String introduction;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;

}
