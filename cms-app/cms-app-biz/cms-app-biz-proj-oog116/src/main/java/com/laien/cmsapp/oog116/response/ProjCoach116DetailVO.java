package com.laien.cmsapp.oog116.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessCoachVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjCoach116DetailVO", description="ProjCoach116DetailVO")
public class ProjCoach116DetailVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "coach name")
    private String name;

    @AbsoluteR2Url
    @ApiModelProperty(value = "coverImgUrl")
    private String coverImgUrl;

    @ApiModelProperty(value = "coach 简介")
    private String introduction;

}
