package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_template116_rule
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjTemplate116Rule对象", description="proj_template116_rule")
public class ProjTemplate116Rule extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_template116_id")
    private Integer projTemplate116Id;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "warm_up、main、cool_down")
    private String videoType;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "播放循环次数")
    private Integer rounds;


}
