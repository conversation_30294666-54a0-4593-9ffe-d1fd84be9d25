package com.laien.cmsapp.oog116.response;

import cn.hutool.core.util.EnumUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.core.enums.util.EnumBaseUtils;
import com.laien.common.domain.annotation.AppAudioSingleTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * note: video116
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video116", description = "video116")
public class ResVideo116V4VO  implements AppAudioCoreI18nModel {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @JsonIgnore
    private Integer workoutId;

    @JsonIgnore
    private Integer ruleId;

    @JsonIgnore
    private String unitName;

    @JsonIgnore
    private Integer rounds;

    @ApiModelProperty(value = "动作名称")
    @AppAudioSingleTranslateField(urlFieldName = "nameAudioUrl")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "Standing:10,Seated:11,Both:12")
    private Integer positionCode;

    @ApiModelProperty(value = "限制，Shoulder:10,Back:11,Wrist:12,Knee:13,Ankle:14,Hip:15")
    private Set<Integer> restrictionCodeSet;

    @JsonIgnore
    @ApiModelProperty(value = "Video总的播放轮数，目前仅针对TaiChi类型Video")
    private Integer circuit = 1;

    @ApiModelProperty(value = "Video播放轮数, 示例值 1、2、3、4")
    private Integer videoPlayIndex = 1;

    @JsonIgnore
    @AppAudioSingleTranslateField(urlFieldName = "instructionsAudioUrl")
    private String instructions;

    @ApiModelProperty(value = "指导文本 (500字符限制)")
    @AppAudioSingleTranslateField(urlFieldName = "guidanceAudioUrl")
    private String guidance;

    @ApiModelProperty(value = "动作简介（How To Do）")
    private List<String> instructionList;

    @ApiModelProperty(value = "video时长")
    private Integer duration;

    @JsonIgnore
    @ApiModelProperty(value = "workout生成时Video的时长")
    private Integer generatedVideoDuration;

    @JsonIgnore
    @ApiModelProperty(value = "workout生成时多轮Video的时长")
    private String circuitVideoDuration;

    @JsonIgnore
    @ApiModelProperty(value = "workout生成时Video Preview的时长")
    private Integer generatedPreviewDuration;

    @ApiModelProperty(value = "video时长（毫秒）")
    private Integer previewDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    @AbsoluteR2Url
    private String videoUrl;

    @ApiModelProperty(value = "视频地址(正机位 2532m3u8)")
    @JsonIgnore
    private String video2532Url;

    @ApiModelProperty(value = "名称音频地址")
    @AbsoluteR2Url
    private String nameAudioUrl;

    @ApiModelProperty(value = "Guidance音频")
    @AbsoluteR2Url
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "Instructions的音频")
    @AbsoluteR2Url
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "Exercise 类型：Chair Yoga: 10,Tai Chi: 11,Dancing: 12,Gentle Cardio: 13,Walking: 14,Dumbbell (lightweight): 15,Resistance Band: 16")
    @JsonIgnore
    private Integer exerciseTypeCode;

    @JsonIgnore
    @ApiModelProperty(value = "性别，Female:10,Male:11,Both:12")
    private Integer genderCode;

    @JsonIgnore
    @ApiModelProperty(value = "单元id")
    private Integer unitId;

    @ApiModelProperty(value = "身体部位 (多选)",hidden = true)
    @JsonIgnore
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)",hidden = true)
    @JsonIgnore
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具 (多选)",hidden = true)
    @JsonIgnore
    private SupportProp116Enums supportProp;

    @ApiModelProperty(value = "regionCodeSet")
    public Set<Integer> getRegionCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getRegion());
    }

    @ApiModelProperty(value = "supportPropCodeSet")
    public Integer getSupportPropCode(){
        return supportProp != null ? supportProp.getShowCode() : null;
    }

    @ApiModelProperty(value = "focusCodeSet")
    public Set<Integer> getFocusCodeSet(){
        return EnumBaseUtils.getShowCodeSet(this.getFocus());
    }
}
