package com.laien.cmsapp.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Workout116 Categories VO
 *
 * <AUTHOR>
 * @since  2025/03/18
 */
@Data
@ApiModel(value = "Workout116 Categories VO", description = "Workout116 Categories VO")
public class ProjWorkout116CategoriesVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "categoryIds")
    private String categoryIds;

    @ApiModelProperty(value = "categoryNames")
    private String categoryNames;

    @ApiModelProperty(value = "categoryTypes")
    private String categoryTypes;

}
