package com.laien.cmsapp.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog116.entity.ProjTemplate116Rule;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_template116_rule Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface ProjTemplate116RuleMapper extends BaseMapper<ProjTemplate116Rule> {

    List<ProjTemplate116Rule> find(@Param("idSet") Set<Integer> idSet);

}
