package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog116.enums.WorkoutDataType116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * note: Workout116 list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout116 list V4", description = "Workout116 list V4")
public class ProjWorkout116ListV4VO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "难度10:Easy,11:Medium,12:Hard")
    private Integer difficultyCode;

    @ApiModelProperty(value = "Standing:10,Seated:11,Both:12")
    private Integer positionCode;

    @ApiModelProperty(value = "限制，Shoulder:10,Back:11,Wrist:12,Knee:13,Ankle:14,Hip:15")
    private Set<Integer> restrictionCodeSet;

    @ApiModelProperty(value = "性别，Female:10,Male:11,Both:12")
    private Integer genderCode;

    @ApiModelProperty(value = "Exercise 类型：Chair Yoga: 10,Tai Chi: 11,Dancing: 12,Gentle Cardio: 13,Walking: 14,Dumbbell (lightweight): 15,Resistance Band: 16")
    private Integer exerciseTypeCode;

    @ApiModelProperty(value = "数据类型，GENERATE:10,ASSEMBLE:11")
    private Integer dataTypeCode = WorkoutDataType116Enums.ASSEMBLE.getCode();

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Boolean subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

}
