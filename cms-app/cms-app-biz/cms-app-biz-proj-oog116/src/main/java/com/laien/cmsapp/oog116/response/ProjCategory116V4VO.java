package com.laien.cmsapp.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * category116 v4 list
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/28
 */
@Data
@ApiModel(value = "category116 v4 list", description = "category116 v4 list")
public class ProjCategory116V4VO {

    @ApiModelProperty(value = "lable 类型category")
    private List<ProjCategory116LableVO> labelList;

    @ApiModelProperty(value = "card 类型category")
    private List<ProjCategory116CodeVO> cardList;

    @ApiModelProperty(value = "grid 类型category")
    private List<ProjCategory116GridVO> gridList;

}
