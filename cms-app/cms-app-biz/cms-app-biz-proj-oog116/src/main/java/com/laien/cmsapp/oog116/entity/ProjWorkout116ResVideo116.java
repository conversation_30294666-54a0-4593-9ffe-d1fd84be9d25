package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_workout116_res_video116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_workout116_res_video116")
@ApiModel(value="ProjWorkout116ResVideo116对象", description="proj_workout116_res_video116")
public class ProjWorkout116ResVideo116 extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "循环次数")
    private Integer rounds;

    @ApiModelProperty(value = "proj_workout116_id")
    private Integer projWorkout116Id;

    @ApiModelProperty(value = "res_video116_id")
    private Integer resVideo116Id;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
