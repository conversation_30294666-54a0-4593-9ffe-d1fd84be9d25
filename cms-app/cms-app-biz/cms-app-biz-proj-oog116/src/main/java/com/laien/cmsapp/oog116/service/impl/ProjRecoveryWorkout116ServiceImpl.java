package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjRecoveryCategory116ProjWorkout116Pub;
import com.laien.cmsapp.oog116.entity.ProjRecoveryCategory116Pub;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Pub;
import com.laien.cmsapp.oog116.mapper.ProjWorkout116PubMapper;
import com.laien.cmsapp.oog116.requst.ProjRecoveryWorkout116ListReq;
import com.laien.cmsapp.oog116.response.ProjRecoveryWorkout116ListVO;
import com.laien.cmsapp.oog116.service.IProjRecoveryCategory116ProjWorkout116Service;
import com.laien.cmsapp.oog116.service.IProjRecoveryCategory116Service;
import com.laien.cmsapp.oog116.service.IProjRecoveryWorkout116Service;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.core.enums.util.EnumBaseUtils;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.frame.constant.GlobalConstant;
import com.laien.common.oog116.enums.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Recovery Workout116 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Slf4j
@Service
public class ProjRecoveryWorkout116ServiceImpl extends ServiceImpl<ProjWorkout116PubMapper, ProjWorkout116Pub> implements IProjRecoveryWorkout116Service {

    @Resource
    private IProjRecoveryCategory116ProjWorkout116Service categoryRelationService;
    @Resource
    private IProjRecoveryCategory116Service category116Service;
    @Resource
    private IProjLmsI18nService i18nService;

    @Override
    public List<ProjRecoveryWorkout116ListVO> list(ProjRecoveryWorkout116ListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();

        // 预处理请求参数
        List<RecoveryCategory116TypeEnums> requestTypeEnums = EnumBaseUtils.findEnumListByShowCode(
                RecoveryCategory116TypeEnums.class, req.getCategoryTypeCodeSet());
        List<Region116Enums> requestRegions = EnumBaseUtils.findEnumListByShowCode(
                Region116Enums.class, req.getRegionCodeSet());

        // 查询启用的分类
        List<ProjRecoveryCategory116Pub> categoryList = queryEnabledCategories(versionInfoBO);
        if (CollectionUtil.isEmpty(categoryList)) {
            return Collections.emptyList();
        }

        // 过滤匹配的分类
        List<ProjRecoveryCategory116Pub> matchTypeList = filterMatchingCategories(categoryList, requestTypeEnums);
        boolean fallbackFlag = false;
        if (CollectionUtil.isEmpty(matchTypeList)) {
            matchTypeList = categoryList;
            fallbackFlag = true;
            log.warn("No matching recovery categories found, fallback to all recovery categories" );
        }
        // 分离需要region检查和不需要region检查的分类
        CategoryFilterResult filterResult = categorizeByRegionRequirement(matchTypeList, requestTypeEnums,fallbackFlag);

        // 查询workout关联关系和数据
        Map<Integer, List<Integer>> workoutCategoryMap = queryWorkoutCategoryRelations(filterResult.getAllCategoryIds(), versionInfoBO);
        if (MapUtil.isEmpty(workoutCategoryMap)) {
            return Collections.emptyList();
        }

        List<ProjWorkout116Pub> workoutList = queryWorkouts(workoutCategoryMap.keySet(), versionInfoBO);
        // 过滤workout
        List<ProjWorkout116Pub> resultList;
        if (!fallbackFlag) {
            // 正常过滤：满足type + region条件的workout
            resultList = filterWorkouts(workoutList, workoutCategoryMap, filterResult, requestRegions);

            // 兜底1：满足type条件但忽略region的workout
            if (resultList.isEmpty()) {
                resultList = workoutList;
                log.warn("No workouts found with region filter, fallback to workouts matching type only");
            }

            // 兜底2：忽略所有条件的workout
            if (resultList.isEmpty()) {
                log.warn("No matching recovery categories workout found, fallback to all recovery workout categories");
                List<Integer> allCategoryIds = categoryList.stream().map(ProjRecoveryCategory116Pub::getId).collect(Collectors.toList());
                resultList = queryWorkouts(queryWorkoutCategoryRelations(allCategoryIds, versionInfoBO).keySet(), versionInfoBO);
            }
        } else {
            // 第一层已经fallback：直接使用所有categories的workout
            resultList = workoutList;
        }

        if (CollectionUtil.isEmpty(resultList)) {
            log.error("Recovery Workout list is empty, with req param : {}, version : {}", req, versionInfoBO.getCurrentVersion());
            return Collections.emptyList();
        }

        // 转换为VO并处理国际化
        return convertToVOsWithI18n(resultList);
    }


    /**
     * 查询启用的分类
     */
    private List<ProjRecoveryCategory116Pub> queryEnabledCategories(ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjRecoveryCategory116Pub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjRecoveryCategory116Pub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjRecoveryCategory116Pub::getProjId, versionInfoBO.getProjId())
                .eq(ProjRecoveryCategory116Pub::getStatus, GlobalConstant.STATUS_ENABLE);
        return category116Service.list(wrapper);
    }

    /**
     * 过滤匹配的分类
     */
    private List<ProjRecoveryCategory116Pub> filterMatchingCategories(List<ProjRecoveryCategory116Pub> categoryList,
                                                                      List<RecoveryCategory116TypeEnums> requestTypeEnums) {
        if (CollectionUtil.isEmpty(requestTypeEnums)) {
            return categoryList;
        }
        return categoryList.stream()
                .filter(category -> CollectionUtil.containsAny(category.getType(), requestTypeEnums))
                .collect(Collectors.toList());
    }

    /**
     * 分类过滤结果
     */
    @Getter
    @AllArgsConstructor
    private static class CategoryFilterResult {
        private final List<Integer> needRegionIds;
        private final List<Integer> noNeedRegionIds;
        private final List<Integer> allCategoryIds;
    }

    /**
     * 将ProjWorkout116Pub转换为ProjRecoveryWorkout116ListVO
     *
     * @param workout workout实体
     * @return ProjRecoveryWorkout116ListVO
     */
    @Override
    public ProjRecoveryWorkout116ListVO convertToWorkoutListVO(ProjWorkout116Pub workout) {
        ProjRecoveryWorkout116ListVO workoutVO = new ProjRecoveryWorkout116ListVO();
        BeanUtils.copyProperties(workout, workoutVO);

        // 转换枚举字段
        DifficultyEnums difficultyEnum = DifficultyEnums.getByName(workout.getDifficulty());
        workoutVO.setDifficultyCode(difficultyEnum != null ? difficultyEnum.getCode() : null);

        Position116Enums positionEnum = Position116Enums.getByName(workout.getPosition());
        workoutVO.setPositionCode(positionEnum != null ? positionEnum.getCode() : null);

        Gender116Enums genderEnum = Gender116Enums.getByName(workout.getGender());
        workoutVO.setGenderCode(genderEnum != null ? genderEnum.getCode() : null);

        ExerciseType116Enums exerciseTypeEnum = ExerciseType116Enums.getByName(workout.getExerciseType());
        workoutVO.setExerciseTypeCode(exerciseTypeEnum != null ? exerciseTypeEnum.getCode() : null);

        // 设置subscription字段为Boolean类型
        workoutVO.setSubscription(workout.getSubscription() != null && workout.getSubscription() == 1);

        return workoutVO;
    }

    /**
     * 根据region需求分类
     */
    private CategoryFilterResult categorizeByRegionRequirement(List<ProjRecoveryCategory116Pub> matchTypeList,
                                                               List<RecoveryCategory116TypeEnums> requestTypeEnums,
                                                               boolean fallbackFlag) {
        List<Integer> needRegionIds = new ArrayList<>();
        List<Integer> noNeedRegionIds = new ArrayList<>();
        List<Integer> allCategoryIds = new ArrayList<>();

        // 预先计算非JOINT_PAIN_RELIEF类型，避免在循环中重复计算
        List<RecoveryCategory116TypeEnums> nonJointPainTypes = RecoveryCategory116TypeEnums.getListExclude(RecoveryCategory116TypeEnums.JOINT_PAIN_RELIEF);
        boolean requestTypeEmpty = CollUtil.isEmpty(requestTypeEnums);
        boolean requestContainsJointPain = requestTypeEmpty || requestTypeEnums.contains(RecoveryCategory116TypeEnums.JOINT_PAIN_RELIEF);
        boolean requestContainsNonJointPain = requestTypeEmpty || CollectionUtil.containsAny(requestTypeEnums, nonJointPainTypes);
        // 一次循环处理所有逻辑
        for (ProjRecoveryCategory116Pub category : matchTypeList) {
            Integer categoryId = category.getId();
            allCategoryIds.add(categoryId);
            if (fallbackFlag) {
                continue;
            }

            List<RecoveryCategory116TypeEnums> categoryTypes = category.getType();

            // 检查是否需要region：包含JOINT_PAIN_RELIEF类型 且 请求的type也包含JOINT_PAIN_RELIEF
            if (categoryTypes.contains(RecoveryCategory116TypeEnums.JOINT_PAIN_RELIEF)
                    && requestContainsJointPain) {
                needRegionIds.add(categoryId);
            }

            // 检查是否不需要region：包含非JOINT_PAIN_RELIEF类型 且 请求的type也包含这些非JOINT_PAIN_RELIEF类型
            if (CollectionUtil.containsAny(categoryTypes, nonJointPainTypes)
                    && requestContainsNonJointPain) {
                noNeedRegionIds.add(categoryId);
            }
        }

        return new CategoryFilterResult(needRegionIds, noNeedRegionIds, allCategoryIds);
    }

    /**
     * 查询workout与category的关联关系
     */
    private Map<Integer, List<Integer>> queryWorkoutCategoryRelations(List<Integer> categoryIds,
                                                                      ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjRecoveryCategory116ProjWorkout116Pub> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjRecoveryCategory116ProjWorkout116Pub::getProjRecoveryCategory116Id, categoryIds)
                .eq(ProjRecoveryCategory116ProjWorkout116Pub::getVersion, versionInfoBO.getCurrentVersion());

        List<ProjRecoveryCategory116ProjWorkout116Pub> relationList = categoryRelationService.list(wrapper);
        if (CollectionUtil.isEmpty(relationList)) {
            return Collections.emptyMap();
        }

        return relationList.stream()
                .collect(Collectors.groupingBy(
                        ProjRecoveryCategory116ProjWorkout116Pub::getProjWorkout116Id,
                        Collectors.mapping(ProjRecoveryCategory116ProjWorkout116Pub::getProjRecoveryCategory116Id, Collectors.toList())
                ));
    }

    /**
     * 查询workout数据
     */
    private List<ProjWorkout116Pub> queryWorkouts(Set<Integer> workoutIds, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjWorkout116Pub> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjWorkout116Pub::getId, workoutIds)
                .eq(ProjWorkout116Pub::getExerciseType, ExerciseType116Enums.RECOVERY.getName())
                .eq(ProjWorkout116Pub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjWorkout116Pub::getProjId, versionInfoBO.getProjId());
        return this.list(wrapper);
    }

    /**
     * 过滤workout
     */
    private List<ProjWorkout116Pub> filterWorkouts(List<ProjWorkout116Pub> workoutList,
                                                   Map<Integer, List<Integer>> workoutCategoryMap,
                                                   CategoryFilterResult filterResult,
                                                   List<Region116Enums> requestRegions) {
        return workoutList.stream().filter(workout -> {
            List<Integer> workoutCategoryIds = workoutCategoryMap.get(workout.getId());
            if (CollectionUtil.isEmpty(workoutCategoryIds)) {
                return false;
            }

            // 检查不需要region的分类
            if (CollectionUtil.containsAny(workoutCategoryIds, filterResult.getNoNeedRegionIds())) {
                return true;
            }

            // 检查需要region的分类
            if (CollectionUtil.containsAny(workoutCategoryIds, filterResult.getNeedRegionIds())) {
                // 如果没有传region参数，则不匹配需要region的分类
                if (CollectionUtil.isEmpty(requestRegions)) {
                    return true;
                }
                // 检查workout的region是否与请求的region有交集
                return CollectionUtil.containsAny(workout.getRegion(), requestRegions);
            }

            return false;
        }).collect(Collectors.toList());
    }

    /**
     * 转换为VO并处理国际化
     */
    private List<ProjRecoveryWorkout116ListVO> convertToVOsWithI18n(List<ProjWorkout116Pub> workoutList) {
        List<ProjRecoveryWorkout116ListVO> vos = workoutList.stream()
                .map(this::convertToWorkoutListVO)
                .collect(Collectors.toList());

        i18nService.handleTextI18n(vos, ProjCodeEnums.OOG116);
        return vos;
    }
}
