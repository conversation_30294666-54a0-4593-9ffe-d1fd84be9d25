package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog116.entity.ProjWorkout116GenerateI18n;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * proj_workout116_generate i18n 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface IProjWorkout116GenerateI18nService extends IService<ProjWorkout116GenerateI18n> {

    /**
     * 根据ID查询多语言
     *
     * @param ids ids
     * @return map
     */
    Map<Integer, ProjWorkout116GenerateI18n> getByIds(List<Integer> ids);

    /**
     * 根据ID查询所有多语言
     *
     * @param ids ids
     * @return map
     */
    Map<Integer, List<ProjWorkout116GenerateI18n>> getAllLanguageListByIds(List<Integer> ids);

}
