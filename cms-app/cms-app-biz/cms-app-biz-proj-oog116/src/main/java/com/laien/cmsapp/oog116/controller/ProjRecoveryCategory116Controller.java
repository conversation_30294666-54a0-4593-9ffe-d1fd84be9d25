package com.laien.cmsapp.oog116.controller;

import com.laien.cmsapp.oog116.requst.RecoveryCategoryListReq;
import com.laien.cmsapp.oog116.response.ProjRecoveryCategory116ListVO;
import com.laien.cmsapp.oog116.service.IProjRecoveryCategory116Service;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * Recovery Category116 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Api(value = "/{appCode}/recoveryCategory116", tags = {"app端：Recovery Category116分类表 前端控制器"})
@RestController
@RequestMapping("/{appCode}/recoveryCategory116")
public class ProjRecoveryCategory116Controller extends ResponseController {

    @Resource
    private IProjRecoveryCategory116Service projRecoveryCategory116PubService;

    /**
     * 查询Recovery分类列表 V1
     *
     * @param appCode appCode
     * @return Recovery分类列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true)
    })
    @ApiOperation(value = "Recovery分类列表v1", notes = "查询Recovery分类列表v1", httpMethod = "GET", tags = {"oog116"})
    @GetMapping("/v1/list")
    public ResponseResult<ProjRecoveryCategory116ListVO> list(@PathVariable String appCode,
                                                              RecoveryCategoryListReq req) {
        return succ(projRecoveryCategory116PubService.list(req));
    }

}
