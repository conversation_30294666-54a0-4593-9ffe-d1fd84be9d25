package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppAudioSingleTranslateField;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.common.util.MyStringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import static com.laien.common.domain.enums.TranslationTaskTypeEnums.MULTIPLE_TEXT;

/**
 * note: video116
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video116", description = "video116")
public class ResVideo116VO implements AppAudioCoreI18nModel , AppTextCoreI18nModel {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @JsonIgnore
    private Integer workoutId;

    @JsonIgnore
    private Integer ruleId;

    @JsonIgnore
    private String unitName;

    @JsonIgnore
    private Integer rounds;

    @ApiModelProperty(value = "动作名称")
    @AppAudioSingleTranslateField(urlFieldName = "nameAudioUrl")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "视频类型Warm Up/Cool Down/Main")
    private String type;

    @ApiModelProperty(value = "部位Seated/Standing")
    @AppTextTranslateField
    private String position;

    @ApiModelProperty(value = "限制,多个用英文逗号分隔，取值Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    @AppTextTranslateField(type = MULTIPLE_TEXT)
    private String restriction;

    @JsonIgnore
    @AppAudioSingleTranslateField(urlFieldName = "instructionsAudioUrl")
    private String instructions;

    @ApiModelProperty(value = "指导文本 (500字符限制)")
    @AppAudioSingleTranslateField(urlFieldName = "guidanceAudioUrl")
    private String guidance;

    @ApiModelProperty(value = "动作简介（How To Do）")
    private List<String> instructionList;

    @ApiModelProperty(value = "video时长")
    private Integer duration;

    @ApiModelProperty(value = "video时长（毫秒）")
    private Integer previewDuration;

    @JsonIgnore
    @ApiModelProperty(value = "workout生成时Video的时长")
    private Integer generatedVideoDuration;

    @JsonIgnore
    @ApiModelProperty(value = "workout生成时多轮Video的时长")
    private String circuitVideoDuration;

    @JsonIgnore
    @ApiModelProperty(value = "workout生成时Video Preview的时长")
    private Integer generatedPreviewDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    @AbsoluteR2Url
    private String videoUrl;

    @ApiModelProperty(value = "视频地址(正机位 2532m3u8)")
    @JsonIgnore
    private String video2532Url;

    @ApiModelProperty(value = "名称音频地址")
    @AbsoluteR2Url
    private String nameAudioUrl;

    @ApiModelProperty(value = "Guidance音频")
    @AbsoluteR2Url
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "Instructions的音频")
    @AbsoluteR2Url
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    @JsonIgnore
    private String exerciseType;

    @JsonIgnore
    @ApiModelProperty(value = "Video总的播放轮数，目前仅针对TaiChi类型Video")
    private Integer circuit;

    @JsonIgnore
    @ApiModelProperty(value = "Video播放轮数, 示例值 1、2、3、4")
    private Integer videoPlayIndex = 1;

    @JsonIgnore
    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @JsonIgnore
    @ApiModelProperty(value = "单元id")
    private Integer unitId;

    @JsonIgnore
    @ApiModelProperty(value = "身体部位 (多选)")
    private List<Region116Enums> region;

    @JsonIgnore
    @ApiModelProperty(value = "焦点类型 (多选)")
    private List<Focus116Enums> focus;

    @JsonIgnore
    @ApiModelProperty(value = "支撑道具")
    private SupportProp116Enums supportProp;


    public List<String> getInstructionList() {
        return MyStringUtil.toList(instructions);
    }

}
