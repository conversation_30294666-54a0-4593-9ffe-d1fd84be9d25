package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * note: Workout116 unit
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Workout116 unit", description = "Workout116 unit")
public class ProjWorkout116UnitDetailVO implements AppTextCoreI18nModel {

    @JsonIgnore
    private Integer ruleId;

    @ApiModelProperty(value = "单元名称")
    @AppTextTranslateField
    private String unitName;

    @ApiModelProperty(value = "循环次数")
    private Integer rounds;

    @ApiModelProperty(value = "videos")
    private List<ResVideo116VO> videos;

}
