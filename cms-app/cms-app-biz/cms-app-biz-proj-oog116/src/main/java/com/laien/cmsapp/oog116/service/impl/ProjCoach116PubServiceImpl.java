package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjCoach116Pub;
import com.laien.cmsapp.oog116.mapper.ProjCoach116PubMapper;
import com.laien.cmsapp.oog116.response.ProjCoach116DetailVO;
import com.laien.cmsapp.oog116.service.IProjCoach116PubService;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/5/20
 */
@Service
public class ProjCoach116PubServiceImpl extends ServiceImpl<ProjCoach116PubMapper, ProjCoach116Pub> implements IProjCoach116PubService {
    @Resource
    private ICoreTextTaskI18nPubService textTaskI18nPubService;
    @Override
    public ProjCoach116DetailVO getCoach(Integer coachId, ProjPublishCurrentVersionInfoBO versionInfoBO, String lang) {

        List<ProjCoach116Pub> coach116PubList = listByStatusAndVersion(Collections.singletonList(coachId), versionInfoBO);
        if (CollectionUtils.isEmpty(coach116PubList)) {
            return null;
        }

        setI18n4Coach(coach116PubList, lang);
        return convert2DetailVO(coach116PubList.get(0));
    }

    private ProjCoach116DetailVO convert2DetailVO(ProjCoach116Pub projCoach116Pub) {

        ProjCoach116DetailVO detailVO = new ProjCoach116DetailVO();
        BeanUtils.copyProperties(projCoach116Pub, detailVO);
        return detailVO;
    }

    private void setI18n4Coach(List<ProjCoach116Pub> coach116PubList, String lang) {
        //文本翻译
        if (CollUtil.isNotEmpty(coach116PubList))  textTaskI18nPubService.translate(coach116PubList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
    }


    private List<ProjCoach116Pub> listByStatusAndVersion(Collection<Integer> coachIds, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        LambdaQueryWrapper<ProjCoach116Pub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(coachIds), ProjCoach116Pub::getId, coachIds);

        queryWrapper.eq(ProjCoach116Pub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjCoach116Pub::getProjId, versionInfoBO.getProjId());
        return list(queryWrapper);
    }

}
