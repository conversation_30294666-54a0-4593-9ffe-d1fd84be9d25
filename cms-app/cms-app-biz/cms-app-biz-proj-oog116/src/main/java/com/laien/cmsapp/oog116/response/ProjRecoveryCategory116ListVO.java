package com.laien.cmsapp.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Recovery Category116 V2 VO
 *
 * <AUTHOR>
 * @since 2025/07/14
 */
@Data
@ApiModel(value = "Recovery Category116 V2 VO", description = "Recovery Category116 V2 VO")
public class ProjRecoveryCategory116ListVO {

    @ApiModelProperty(value = "卡片类型分类列表")
    private List<ProjRecoveryCategory116CardVO> cardList;

    @ApiModelProperty(value = "标签类型分类列表")
    private List<ProjRecoveryCategory116LabelVO> labelList;

    @ApiModelProperty(value = "网格类型分类列表")
    private List<ProjRecoveryCategory116GridVO> gridList;

}
