package com.laien.cmsapp.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Pub;
import com.laien.cmsapp.oog116.requst.ProjWorkout116ListReq;
import com.laien.cmsapp.oog116.response.ProjWorkout116CategoriesVO;
import com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO;
import com.laien.cmsapp.oog116.response.ProjWorkout116ListVO;
import com.laien.cmsapp.oog116.response.ResVideo116VO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * proj_workout116 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface ProjWorkout116PubMapper extends BaseMapper<ProjWorkout116Pub> {

    /**
     * 获取workout列表
     *
     * @param versionInfoBO     versionInfoBO
     * @param workout116ListReq workout116ListReq
     * @return list
     */
    List<ProjWorkout116ListVO> queryList(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, @Param("param") ProjWorkout116ListReq workout116ListReq);

    /**
     * 获取workout详情列表
     *
     * @param versionInfoBO versionInfoBO
     * @param idList        idList
     * @return list
     */
    List<ProjWorkout116DetailVO> queryDetailByIdList(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, @Param("idList") List<Integer> idList);

    /**
     * 根据id list 查询video 信息
     *
     * @param versionInfoBO versionInfoBO
     * @param idList        idList
     * @return list
     */
    List<ResVideo116VO> queryVideoByIdList(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, @Param("idList") List<Integer> idList);

    /**
     * 获取workout关联的category名称集合
     *
     * @param versionInfoBO versionInfoBO
     * @param idList        workout id列表
     * @return list category名称集合
     */
    List<ProjWorkout116CategoriesVO> queryWorkoutCategories(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, @Param("idList") List<Integer> idList);

}
