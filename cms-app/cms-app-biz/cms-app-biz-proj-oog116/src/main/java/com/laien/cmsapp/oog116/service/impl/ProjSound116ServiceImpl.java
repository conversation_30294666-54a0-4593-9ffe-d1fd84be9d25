package com.laien.cmsapp.oog116.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ProjSound116;
import com.laien.cmsapp.oog116.mapper.ProjSound116Mapper;
import com.laien.cmsapp.oog116.requst.ProjSound116Req;
import com.laien.cmsapp.oog116.response.ProjSound116VO;
import com.laien.cmsapp.oog116.service.IProjSound116Service;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.util.FireBaseUrlSubUtils;
import com.laien.common.util.RequestContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.Sound116SubTypeEnums.COMPLETE;
import static com.laien.common.oog116.enums.Sound116SubTypeEnums.WELCOME;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSound116ServiceImpl extends ServiceImpl<ProjSound116Mapper, ProjSound116>
        implements IProjSound116Service {

    @Autowired
    private final IProjLmsI18nService projLmsI18nService;
    @Autowired
    private final FileService fileService;

    /**
     * 按照类型，子类型 , 性别查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    @Override
    public List<ProjSound116VO> selectSoundList(ProjSound116Req soundReq) {
        //兜底没有传 gender 时,默认给 female
        if (Objects.isNull(soundReq.getGender())) {
            soundReq.setGender(Gender116Enums.FEMALE);
        }
        List<ProjSound116VO> result = new ArrayList<>();
        LambdaQueryWrapper<ProjSound116> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(soundReq.getGender()), ProjSound116::getGender, soundReq.getGender())
                .eq(ProjSound116::getDelFlag, 0)
                .eq(ProjSound116::getStatus, 1)
                .and(wrapper -> wrapper.eq(ProjSound116::getSoundSubType, COMPLETE).or().eq(ProjSound116::getSoundSubType, WELCOME));
        query.orderByDesc(ProjSound116::getId);
        List<ProjSound116> soundList = baseMapper.selectList(query);
        if (soundList == null || soundList.isEmpty()) {
            log.error("The soundList is null");
            return result;
        }
        String lang = RequestContextUtils.getLanguage();
        List<ProjSound116> needI18nList = soundList.stream().filter(ProjSound116::getNeedTranslation).collect(Collectors.toList());
        projLmsI18nService.handleSpeechI18nSingle(CollUtil.newArrayList(needI18nList), ProjCodeEnums.OOG116,lang);
        result = soundList.stream()
                .map(projSound116 -> {
                        ProjSound116VO sound116VO = new ProjSound116VO(projSound116);
                        sound116VO.setSoundUrlName(FireBaseUrlSubUtils.getFileName(projSound116.getUrl()));
                        sound116VO.setSoundUrl(fileService.getAbsoluteUrl(projSound116.getUrl()));
                        return sound116VO;
                })
                .collect(Collectors.toList());
        return result;
    }

}
