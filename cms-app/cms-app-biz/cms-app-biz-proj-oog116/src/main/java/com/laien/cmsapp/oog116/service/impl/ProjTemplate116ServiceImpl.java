package com.laien.cmsapp.oog116.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.config.FixedTemplateConfig;
import com.laien.cmsapp.oog116.config.Oog116Config;
import com.laien.cmsapp.oog116.entity.ProjTemplate116Pub;
import com.laien.cmsapp.entity.ResImage;
import com.laien.cmsapp.oog116.mapper.ProjTemplate116Mapper;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanReq;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV3Req;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV4Req;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.IProjTemplate116Service;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateService;
import com.laien.cmsapp.oog116.service.IProjWorkout116PubService;
import com.laien.cmsapp.response.*;
import com.laien.cmsapp.service.*;
import com.laien.cmsapp.oog116.utils.Workout116Util;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.exception.BizException;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog116.enums.*;
import com.laien.common.oog116.vo.PlanName116VO;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.laien.common.oog116.enums.PlanType116Enums.NEW;
import static com.laien.common.oog116.enums.PlanType116Enums.OLD;
import static com.laien.common.oog116.enums.Position116Enums.*;


/**
 * <p>
 * template116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Slf4j
@Service
public class ProjTemplate116ServiceImpl extends ServiceImpl<ProjTemplate116Mapper, ProjTemplate116Pub> implements IProjTemplate116Service {

    @Resource
    private IProjWorkout116GenerateService projWorkout116GenerateService;
    @Resource
    private ICoreTextTaskI18nPubService textTaskI18nPubService;
    @Resource
    private Oog116Config oog116Config;
    @Resource
    private IProjWorkout116PubService projWorkout116PubService;
    @Resource
    private IProjPlaylistService projPlaylistService;

    @Resource
    private FixedTemplateConfig fixedTemplateConfig;

    @Resource
    private IResImageService resImageService;

    private static final String IMAGE_FUNCTION = "template-workout";

    @Override
    public List<Integer> findIdList(Workout116GeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        List<Integer> fixedTemplateIdList = fixedTemplateConfig.getFixedTemplateIdList();
        LambdaQueryWrapper<ProjTemplate116Pub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjTemplate116Pub::getDurationRange, planReq.getDuration().getValue()).in(CollUtil.isNotEmpty(fixedTemplateIdList), BaseModel::getId, fixedTemplateIdList).eq(ProjTemplate116Pub::getProjId, versionInfoBO.getProjId()).eq(ProjTemplate116Pub::getVersion, versionInfoBO.getCurrentVersion());
        List<ProjTemplate116Pub> templateList = baseMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(templateList)) {
            return null;
        }
        return templateList.stream().map(BaseModel::getId).collect(Collectors.toList());
    }

    @Override
    public List<Integer> findIdList(Integer durationCode, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        DurationLabel116Enums duration = DurationLabel116Enums.getByCode(durationCode);
        if (null == duration) {
            log.error("durationCode is illegal");
            duration = DurationLabel116Enums.MIN_5_10;
        }
        List<Integer> fixedTemplateIdList = fixedTemplateConfig.getFixedTemplateIdList();
        LambdaQueryWrapper<ProjTemplate116Pub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjTemplate116Pub::getDurationRange, duration.getValue()).eq(ProjTemplate116Pub::getProjId, versionInfoBO.getProjId()).eq(ProjTemplate116Pub::getStatus, GlobalConstant.STATUS_ENABLE).eq(ProjTemplate116Pub::getVersion, versionInfoBO.getCurrentVersion()).notIn(CollUtil.isNotEmpty(fixedTemplateIdList), BaseModel::getId, fixedTemplateIdList);
        List<ProjTemplate116Pub> templateList = baseMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(templateList)) {
            return new ArrayList<>();
        }
        return templateList.stream().map(BaseModel::getId).collect(Collectors.toList());
    }

    @Override
    public ProjWorkout116GeneratePlanV4VO planV4(Workout116GeneratePlanV4Req planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        List<Integer> idList = findIdList(planReq.getDurationCode(), versionInfoBO);
        if (CollectionUtil.isEmpty(idList)) {
            log.error("oog116 planV4 not find template planReq is {}", planReq);
            return null;
        }

        List<ProjWorkout116DetailV4VO> workoutList = projWorkout116GenerateService.queryV4(planReq, idList, versionInfoBO);
        if (CollectionUtil.isEmpty(workoutList)) {
            return null;
        }
        if (workoutList.size() < GlobalConstant.TWENTY_EIGHT) {
            log.error("oog116 plan v4 workout count not enough， planReq:{}", planReq);
        }

        ProjWorkout116GenerateServiceImpl.replaceImage(workoutList);

        workoutList = replaceWorkoutV4(workoutList, planReq);

        // 太极类型按前三个替换处理
        replaceWorkoutV5TaiChi(workoutList, planReq);
        ProjWorkout116GeneratePlanV4VO plan = new ProjWorkout116GeneratePlanV4VO();
        plan.setWorkoutList(workoutList);
        Position116Enums positionEnum = Position116Enums.getByCode(planReq.getPositionCode());
        PlanName116VO planName116VO = positionEnum.matchPlanName(planReq.getCompleteTimes());
        // 后期plan分两部分返回，原plan名字逻辑不变
        plan.setName(positionEnum.getPlanName());
        if (null != planName116VO) {
            plan.setPreName(planName116VO.getPreName()).setLastName(planName116VO.getLastName());
        }
        Workout116Util.handleGenerateWorkoutI18n(plan.getWorkoutList());
        return plan;
    }

    private void replaceWorkoutV5TaiChi(List<ProjWorkout116DetailV4VO> workoutList, Workout116GeneratePlanV4Req planReq) {

        if (Objects.equals(planReq.getPlanType(), OLD.getCode())) {
            return;
        }
        Integer taiChiFixedWorkoutCount = oog116Config.getTaiChiFixedWorkoutCount();
        List<Integer> femaleWorkoutIdList = new ArrayList<>(Optional.ofNullable(oog116Config.getFemaleWorkoutIdMap().get(WorkoutGenerate116ImagePoint.TAI_CHI)).orElse(Collections.emptyList()));
        List<Integer> maleWorkoutIdList = new ArrayList<>(Optional.ofNullable(oog116Config.getMaleWorkoutIdMap().get(WorkoutGenerate116ImagePoint.TAI_CHI)).orElse(Collections.emptyList()));
        List<Integer> allReplaceWorkoutIdList = Stream.of(femaleWorkoutIdList, maleWorkoutIdList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<Integer, ProjWorkout116DetailV4VO> replaceWorkoutMap = projWorkout116PubService.getDetailByIdList(allReplaceWorkoutIdList, planReq.getM3u8Type()).stream().collect(Collectors.toMap(ProjWorkout116DetailV4VO::getId, Function.identity(), (k1, k2) -> k1));

        int replacedCount = 0;
        for (int index = 0; index < workoutList.size(); index++) {
            // 已替换足够数量，退出循环
            if (replacedCount >= taiChiFixedWorkoutCount) {
                break;
            }
            ProjWorkout116DetailV4VO workout = workoutList.get(index);

            Position116Enums position = Position116Enums.getByCode(workout.getPositionCode());
            ExerciseType116Enums exerciseType = ExerciseType116Enums.getByCode(workout.getExerciseTypeCode());
            WorkoutGenerate116ImagePoint point = WorkoutGenerate116ImagePoint.get(position, exerciseType);
            // 类型不符合太极类型point，跳过
            if (Objects.isNull(point) || !Objects.equals(point.getExerciseType(), ExerciseType116Enums.TAI_CHI)) {
                continue;
            }
            // 性别符合，且有配置替换数据，执行替换
            if (Objects.equals(workout.getGenderCode(), Gender116Enums.FEMALE.getCode()) && CollUtil.isNotEmpty(femaleWorkoutIdList)) {

                Integer replaceWorkoutId = femaleWorkoutIdList.remove(0);
                if (replaceWorkoutMap.containsKey(replaceWorkoutId)) {
                    workoutList.set(index, replaceWorkoutMap.get(replaceWorkoutId));
                    replacedCount++;
                }
            }

            if (Objects.equals(workout.getGenderCode(), Gender116Enums.MALE.getCode()) && CollUtil.isNotEmpty(maleWorkoutIdList)) {
                Integer replaceWorkoutId = maleWorkoutIdList.remove(0);
                if (replaceWorkoutMap.containsKey(replaceWorkoutId)) {
                    workoutList.set(index, replaceWorkoutMap.get(replaceWorkoutId));
                    replacedCount++;
                }
            }
        }

    }

    @Override
    public ProjWorkout116GeneratePlanV4VO planV5(Workout116GeneratePlanV4Req planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        ProjWorkout116GeneratePlanV4VO plan = planV4(planReq, versionInfoBO);
        if (Objects.isNull(plan)) {
            return null;
        }
        List<ProjWorkout116DetailV4VO> workoutList = plan.getWorkoutList();
        if (CollectionUtil.isEmpty(workoutList)) {
            return plan;
        }

        List<ResImage> imageList = resImageService.find(versionInfoBO.getAppCode(), IMAGE_FUNCTION, null, null);
        if (CollUtil.isEmpty(imageList)) {
            return plan;
        }
        Collections.shuffle(imageList);
        Set<ResImage> matchedImageSet = new HashSet<>();
        Map<String, List<ResImage>> femalePointGroup = new HashMap<>();
        Map<String, List<ResImage>> malePointGroup = new HashMap<>();
        for (ResImage image : imageList) {
            Map<String, List<ResImage>> pointGroup;
            if (Gender116Enums.FEMALE.getName().equals(image.getGender())) {
                pointGroup = femalePointGroup;
            } else {
                pointGroup = malePointGroup;
            }
            String point = image.getPoint();
            List<ResImage> images = pointGroup.getOrDefault(point, new ArrayList<>());
            images.add(image);
            pointGroup.put(point, images);
        }

        for (ProjWorkout116DetailV4VO detail : workoutList) {
            if (Objects.equals(WorkoutDataType116Enums.ASSEMBLE.getCode(), detail.getDataTypeCode())) {
                continue;
            }
            WorkoutGenerate116ImagePoint point = WorkoutGenerate116ImagePoint.get(getByCode(detail.getPositionCode()), ExerciseType116Enums.getByCode(detail.getExerciseTypeCode()));
            if (null == point) {
                log.error("plan workout not find exercise point {},detail is {},plan req is {}", detail.getPositionCode(), detail, planReq);
                continue;
            }
            Integer genderCode = detail.getGenderCode();
            Map<String, List<ResImage>> imagePointGroup;
            if (Gender116Enums.FEMALE.getCode().equals(genderCode)) {
                imagePointGroup = femalePointGroup;
            } else {
                imagePointGroup = malePointGroup;
            }
            List<ResImage> resImageList = imagePointGroup.get(point.getName());
            if (CollUtil.isEmpty(resImageList)) {
                log.error("plan workout not find image list {},detail is {},plan req is {}, point is {}", detail.getPositionCode(), detail, planReq, point);
                continue;
            }
            List<ResImage> imageCopyList = new ArrayList<>(resImageList);
            imageCopyList.removeAll(matchedImageSet);
            ResImage image;
            if (CollectionUtil.isEmpty(imageCopyList)) {
                Collections.shuffle(resImageList);
                image = resImageList.get(0);
            } else {
                image = imageCopyList.remove(0);
            }
            matchedImageSet.add(image);
            String coverImage = image.getCoverImage();
            String detailImage = image.getDetailImage();
            if (Gender116Enums.MALE.getCode().equals(detail.getGenderCode())) {
                String coverImageMale = image.getCoverImageMale();
                String detailImageMale = image.getDetailImageMale();
                if (StrUtil.isNotBlank(coverImageMale)) {
                    coverImage = coverImageMale;
                }
                if (StrUtil.isNotBlank(detailImageMale)) {
                    detailImage = detailImageMale;
                }
            }
            if (StrUtil.isNotBlank(detailImage)) {
                detail.setDetailImgUrl(detailImage);
            }
            if (StrUtil.isNotBlank(coverImage)) {
                detail.setCoverImgUrl(coverImage);
            }
            detail.setName(image.getName());
            detail.setEventName(image.getName() + "-" + detail.getId());
        }
        //特殊处理,更换 name 后,需要翻译
        String lang = RequestContextUtils.getLanguage();
        if (!Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, lang)){
            if(!plan.getWorkoutList().isEmpty()) textTaskI18nPubService.translate(plan.getWorkoutList(), ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
        }
        return plan;
    }

    private List<ProjWorkout116DetailV4VO> replaceWorkoutV4(List<ProjWorkout116DetailV4VO> workoutList, Workout116GeneratePlanV4Req planReq) {

        if (Objects.equals(planReq.getPlanType(), OLD.getCode())) {
            return workoutList;
        }
        Map<WorkoutGenerate116ImagePoint, List<Integer>> femaleWorkoutIdMap = oog116Config.getFemaleWorkoutIdMap();
        Map<WorkoutGenerate116ImagePoint, List<Integer>> maleWorkoutIdMap = oog116Config.getMaleWorkoutIdMap();
        Map<ProjWorkout116DetailV4VO, Integer> workoutFixIdMap = new HashMap<>();
        for (int i = 0; i < oog116Config.getFixCount(); i++) {
            ProjWorkout116DetailV4VO workout = workoutList.get(i);
            Position116Enums position = Position116Enums.getByCode(workout.getPositionCode());
            ExerciseType116Enums exerciseType = ExerciseType116Enums.getByCode(workout.getExerciseTypeCode());
            WorkoutGenerate116ImagePoint point = WorkoutGenerate116ImagePoint.get(position, exerciseType);
            List<Integer> fixWorkoutConfig;
            if (workout.getGenderCode().equals(Gender116Enums.FEMALE.getCode())) {
                fixWorkoutConfig = femaleWorkoutIdMap.getOrDefault(point, new ArrayList<>());
            } else {
                fixWorkoutConfig = maleWorkoutIdMap.getOrDefault(point, new ArrayList<>());
            }
            List<Integer> fixWorkoutConfigCopy = new ArrayList<>(fixWorkoutConfig);
            fixWorkoutConfigCopy.removeAll(workoutFixIdMap.values());
            if (CollUtil.isNotEmpty(fixWorkoutConfigCopy)) {
                workoutFixIdMap.put(workout, fixWorkoutConfigCopy.get(0));
            }
        }
        if (CollUtil.isEmpty(workoutFixIdMap)) {
            return workoutList;
        }
        List<Integer> fixWorkoutIdList = new ArrayList<>(workoutFixIdMap.values());
        List<ProjWorkout116DetailV4VO> fixWorkoutList = projWorkout116PubService.getDetailByIdList(fixWorkoutIdList, planReq.getM3u8Type());
        if (CollectionUtil.isEmpty(fixWorkoutList)) {
            return workoutList;
        }
        Map<Integer, ProjWorkout116DetailV4VO> fixWorkoutGroup = fixWorkoutList.stream().collect(Collectors.toMap(ProjWorkout116DetailV4VO::getId, v -> v));
        List<ProjWorkout116DetailV4VO> finalWorkoutList = new ArrayList<>();
        for (ProjWorkout116DetailV4VO detail : workoutList) {
            Integer fixId = workoutFixIdMap.get(detail);
            if (null == fixId) {
                finalWorkoutList.add(detail);
            } else {
                ProjWorkout116DetailV4VO workout = fixWorkoutGroup.get(fixId);
                if (null != workout) {
                    finalWorkoutList.add(workout);
                }
            }
        }
        return finalWorkoutList;
    }

    @Override
    public ProjWorkout116GeneratePlanVO planV3(Workout116GeneratePlanV3Req planReq, ProjPublishCurrentVersionInfoBO versionInfoBO, List<Restriction116Enums> restriction116List) {
        List<Integer> idList = findIdList(planReq, versionInfoBO);
        if (CollectionUtil.isEmpty(idList)) {
            return null;
        }
        Collections.shuffle(idList);
        List<ProjWorkout116DetailVO> workoutList = null;
        for (Integer id : idList) {
            workoutList = projWorkout116GenerateService.query(planReq, id, versionInfoBO, restriction116List, planReq.getEquipmentSet());
            if (CollectionUtil.isNotEmpty(workoutList)) {
                break;
            }
        }
        if (CollectionUtil.isEmpty(workoutList)) {
            log.error("oog116 plan workout count is 0， planReq:{}", planReq);
            return null;
        }
        if (workoutList.size() < GlobalConstant.TWENTY_EIGHT) {
            log.error("oog116 plan workout count not enough， planReq:{}", planReq);
        }
        for (ProjWorkout116DetailVO detailVO : workoutList) {
            if (Gender116Enums.MALE.getName().equals(detailVO.getGender())) {
                // 如果是男就将CoverImgUrl和DetailImgUrl替换为男性的，前端使用的是coverImgUrl和detailImgUrl
                detailVO.setCoverImgUrl(detailVO.getCoverImgMaleUrl()).setDetailImgUrl(detailVO.getDetailImgMaleUrl());
            }
        }
        List<ProjWorkout116DetailVO> finalWorkoutList = replaceWorkout(workoutList, planReq);
        Map<String, ProjPlaylistAppVO> playlistMap = new HashMap<>();
        Set<String> playlistEmptyExerciseTypeSet = new HashSet<>();
        for (ProjWorkout116DetailVO detailVO : finalWorkoutList) {
            String exerciseType = detailVO.getExerciseType();
            ExerciseType116Enums exerciseTypeEnums = ExerciseType116Enums.getByName(exerciseType);
            String equipment = detailVO.getEquipment();
            List<Equipment116Enums> equipmentList = Equipment116Enums.getEquipmentList(equipment);
            if (CollectionUtil.isEmpty(equipmentList)) {
                log.error("card name is empty, workoutId:{}, equipment:{}", detailVO.getId(), equipment);
            } else {
                List<EquipmentVO> equipmentVoList = new ArrayList<>(equipmentList.size());
                if (equipmentList.size() > 1) {
                    equipmentList.remove(Equipment116Enums.NONE);
                }
                for (Equipment116Enums equipmentEnums : equipmentList) {
                    equipmentVoList.add(new EquipmentVO(equipmentEnums.getCardName(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                }
                detailVO.setCardEquipment(equipmentVoList);
            }

            ProjPlaylistAppVO playlist = playlistMap.get(exerciseType);
            if (null != playlist) {
                detailVO.setPlaylistId(playlist.getId());
                continue;
            }
            if (playlistEmptyExerciseTypeSet.contains(exerciseType)) {
                continue;
            }
            List<ProjPlaylistAppVO> playlists;
            if (null != exerciseTypeEnums) {
                playlists = projPlaylistService.selectListApp(exerciseTypeEnums.getPlaylistType());
            } else {
                playlists = projPlaylistService.selectListApp("Normal");
            }
            if (CollUtil.isEmpty(playlists)) {
                playlistEmptyExerciseTypeSet.add(exerciseType);
                log.error("plan v3 oog116 exercise type:{} playlist is empty, workout id: {}", exerciseType, detailVO.getId());
                continue;
            }
            playlist = playlists.get(0);
            detailVO.setPlaylistId(playlist.getId());
            playlistMap.put(exerciseType, playlist);
        }
        return assembleStage(planReq, finalWorkoutList);
    }

    @Override
    public ProjWorkout116GeneratePlanVO plan(Workout116GeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO, boolean downloadJson, List<Restriction116Enums> restriction116List) {
        List<Integer> idList = findIdList(planReq, versionInfoBO);
        if (CollectionUtil.isEmpty(idList)) {
            return null;
        }
        Collections.shuffle(idList);
        List<ProjWorkout116DetailVO> workoutList = null;
        for (Integer id : idList) {
            workoutList = projWorkout116GenerateService.query(planReq, id, versionInfoBO, downloadJson, restriction116List);
            if (CollectionUtil.isNotEmpty(workoutList)) {
                break;
            }
        }
        if (CollectionUtil.isEmpty(workoutList)) {
            return null;
        }
        Collections.shuffle(workoutList);
        List<ProjWorkout116DetailVO> finalWorkoutList = replaceWorkout(workoutList, planReq, downloadJson);

        return assembleStage(planReq, finalWorkoutList);
    }


    /**
     * 组装stage
     */
    private static ProjWorkout116GeneratePlanVO assembleStage(Workout116GeneratePlanReq planReq, List<ProjWorkout116DetailVO> finalWorkoutList) {
        List<Stage116VO> stage116List = new ArrayList<>();
        ProjWorkout116GeneratePlanVO planVO = new ProjWorkout116GeneratePlanVO();
        Position116Enums position = planReq.getPosition();
        PlanName116VO planName116VO = position.matchPlanName(planReq.getCompleteTimes());
        // 后期plan分两部分返回，原plan名字逻辑不变
        planVO.setName(position.getPlanName());
        if (null != planName116VO) {
            planVO.setPreName(planName116VO.getPreName()).setLastName(planName116VO.getLastName());
        }
        planVO.setStageList(stage116List);

        // 组装阶段
        Stage116VO stage116VO = new Stage116VO();
        stage116List.add(stage116VO);
        stage116VO.setName("Stage 1: Start Easily");
        List<ProjWorkout116DetailVO> stateWorkoutList = new ArrayList<>();

        stateWorkoutList.addAll(CollectionUtil.sub(finalWorkoutList, 0, 3));
        stage116VO.setWorkoutList(stateWorkoutList);

        stage116VO = new Stage116VO();
        stage116List.add(stage116VO);
        stage116VO.setName("Stage 2: Burn Calories");
        stateWorkoutList = new ArrayList<>();
        stateWorkoutList.addAll(CollectionUtil.sub(finalWorkoutList, 3, 10));
        stage116VO.setWorkoutList(stateWorkoutList);

        stage116VO = new Stage116VO();
        stage116List.add(stage116VO);
        stage116VO.setName("Stage 3: Healthy Bones");
        stateWorkoutList = new ArrayList<>();
        stateWorkoutList.addAll(CollectionUtil.sub(finalWorkoutList, 10, 20));
        stage116VO.setWorkoutList(stateWorkoutList);

        stage116VO = new Stage116VO();
        stage116List.add(stage116VO);
        stage116VO.setName("Stage 4: Sculpt Figure");
        stateWorkoutList = new ArrayList<>();
        stateWorkoutList.addAll(CollectionUtil.sub(finalWorkoutList, 20, 28));
        stage116VO.setWorkoutList(stateWorkoutList);
        return planVO;
    }

    private List<ProjWorkout116DetailVO> replaceWorkout(List<ProjWorkout116DetailVO> workoutList, Workout116GeneratePlanV3Req planReq) {
        if (NEW != planReq.getPlanType()) {
            return workoutList;
        }
        Position116Enums position = planReq.getPosition();
        Gender116Enums coachGender = planReq.getCoachGender();
        List<Integer> replaceWorkoutIdList = null;
        if (Gender116Enums.FEMALE == coachGender || Gender116Enums.BOTH == coachGender) {
            if (SEATED == position) {
                replaceWorkoutIdList = oog116Config.getFemaleSeatedWorkoutIdList();
            } else if (STANDING == position) {
                replaceWorkoutIdList = oog116Config.getFemaleStandingWorkoutIdList();
            } else if (BOTH == position) {
                replaceWorkoutIdList = oog116Config.getFemaleBothWorkoutIdList();
            }
        } else if (Gender116Enums.MALE == coachGender) {
            if (SEATED == position) {
                replaceWorkoutIdList = oog116Config.getMaleSeatedWorkoutIdList();
            } else if (STANDING == position) {
                replaceWorkoutIdList = oog116Config.getMaleStandingWorkoutIdList();
            } else if (BOTH == position) {
                replaceWorkoutIdList = oog116Config.getMaleBothWorkoutIdList();
            }
        }
        if (CollUtil.isEmpty(replaceWorkoutIdList)) {
            return workoutList;
        }

        List<ProjWorkout116DetailVO> replaceWorkoutList = projWorkout116PubService.getDetailByIdList(replaceWorkoutIdList, false);
        if (CollectionUtil.isEmpty(replaceWorkoutList)) {
            return workoutList;
        }
        Map<Integer, List<ProjWorkout116DetailVO>> replaceWorkoutMap = replaceWorkoutList.stream().collect(Collectors.groupingBy(ProjWorkout116DetailVO::getId));
        List<ProjWorkout116DetailVO> workoutVOList = new ArrayList<>();
        for (Integer workoutId : replaceWorkoutIdList) {
            List<ProjWorkout116DetailVO> detailList = replaceWorkoutMap.get(workoutId);
            if (CollectionUtil.isNotEmpty(detailList)) {
                workoutVOList.add(detailList.get(0));
            }
        }
        replaceWorkoutList = workoutVOList;
        List<ProjWorkout116DetailVO> finalWorkoutList = new ArrayList<>(workoutList.size());
        finalWorkoutList.addAll(replaceWorkoutList);

        finalWorkoutList.addAll(CollectionUtil.sub(workoutList, replaceWorkoutList.size(), workoutList.size()));
        return finalWorkoutList;
    }

    private List<ProjWorkout116DetailVO> replaceWorkout(List<ProjWorkout116DetailVO> workoutList, Workout116GeneratePlanReq planReq, boolean downloadJson) {
        if (NEW != planReq.getPlanType()) {
            return workoutList;
        }
        Position116Enums position = planReq.getPosition();
        List<Integer> replaceWorkoutIdList;
        if (BOTH == position) {
            replaceWorkoutIdList = oog116Config.getBothWorkoutIdList();
        } else if (STANDING == position) {
            replaceWorkoutIdList = oog116Config.getStandingWorkoutIdList();
        } else if (SEATED == position) {
            replaceWorkoutIdList = oog116Config.getSeatedWorkoutIdList();
        } else {
            throw new BizException("not support position");
        }
        List<ProjWorkout116DetailVO> replaceWorkoutList = projWorkout116PubService.getDetailByIdList(replaceWorkoutIdList, downloadJson);
        if (CollectionUtil.isEmpty(replaceWorkoutList)) {
            return workoutList;
        }
        Map<Integer, List<ProjWorkout116DetailVO>> replaceWorkoutMap = replaceWorkoutList.stream().collect(Collectors.groupingBy(ProjWorkout116DetailVO::getId));
        List<ProjWorkout116DetailVO> workoutVOList = new ArrayList<>();
        for (Integer workoutId : replaceWorkoutIdList) {
            List<ProjWorkout116DetailVO> detailList = replaceWorkoutMap.get(workoutId);
            if (CollectionUtil.isNotEmpty(detailList)) {
                workoutVOList.add(detailList.get(0));
            }
        }
        replaceWorkoutList = workoutVOList;
        List<ProjWorkout116DetailVO> finalWorkoutList = new ArrayList<>(workoutList.size());
        finalWorkoutList.addAll(replaceWorkoutList);

        finalWorkoutList.addAll(CollectionUtil.sub(workoutList, replaceWorkoutList.size(), workoutList.size()));
        return finalWorkoutList;
    }
}
