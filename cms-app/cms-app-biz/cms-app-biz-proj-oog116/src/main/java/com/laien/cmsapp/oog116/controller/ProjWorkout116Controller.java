package com.laien.cmsapp.oog116.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.requst.ProjWorkout116ListReq;
import com.laien.cmsapp.oog116.requst.ProjWorkout116RecommendListReq;
import com.laien.cmsapp.oog116.requst.Workout116GenerateReq;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateService;
import com.laien.cmsapp.oog116.service.IProjWorkout116PubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.common.oog116.enums.WorkoutDataType116Enums;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * proj_workout116 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Slf4j
@Api(tags = "app端：workout 116")
@RestController
@RequestMapping("/{appCode}/workout116")
public class ProjWorkout116Controller extends ResponseController {

    @Resource
    private IProjWorkout116PubService projWorkout116PubService;
    @Resource
    private IProjWorkout116GenerateService projWorkout116GenerateService;

    public static final List<ExerciseType116Enums> EXERCISE_TYPES_V1 = ExerciseType116Enums.getByVersion(1);
    public static final List<ExerciseType116Enums> EXERCISE_TYPES_V2 = ExerciseType116Enums.getByVersion(2);
    public static final List<ExerciseType116Enums> EXERCISE_TYPES_V3 = ExerciseType116Enums.getByVersion(3);
    public static final List<ExerciseType116Enums> EXERCISE_TYPES_V4 = ExerciseType116Enums.getByVersion(4);

    @ApiOperation(value = "list", tags = {"oog116"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjWorkout116ListVO>> list(ProjWorkout116ListReq workout116ListReq) {
        return succ(projWorkout116PubService.queryList(workout116ListReq, EXERCISE_TYPES_V1));
    }

    @ApiOperation(value = "list v4", notes = "在 list v1 基础上将Difficulty、Position、Gender、ExerciseType等字符串转换为枚举Code", tags = {"oog116"})
    @GetMapping("/v4/list")
    public ResponseResult<List<ProjWorkout116ListV4VO>> listV4(ProjWorkout116ListReq workout116ListReq) {

        return succ(projWorkout116PubService.listV4(workout116ListReq));
    }

    @ApiOperation(value = "workout116 detail v4",
            notes = "在 detail v1 基础上将Difficulty、Position、Gender、ExerciseType等字符串转换为枚举Code; Video数据中新增videoPlayIndex字段，表示该Video是第几次播放", tags = {"oog116"})
    @GetMapping("/v4/detail/{id}")
    public ResponseResult<ProjWorkout116DetailV4VO> detailV4(@PathVariable Integer id, Integer m3u8Type) {

        List<ProjWorkout116DetailV4VO> workout116DetailVOList = projWorkout116PubService.getDetailByIdList(Collections.singletonList(id), m3u8Type);
        if (CollectionUtil.isEmpty(workout116DetailVOList)) {
            log.error("Can't find workout116 detail by id : {}", id);
            return succ(null);
        }
        return succ(workout116DetailVOList.get(GlobalConstant.ZERO));
    }

    @ApiOperation(value = "listAll", tags = {"oog116"})
    @GetMapping("/v1/listAll")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listAll() {
        return succ(projWorkout116PubService.queryAllList(new ProjWorkout116RecommendListReq(), EXERCISE_TYPES_V1));
    }

    @ApiOperation(value = "listRecommend", tags = {"oog116"})
    @GetMapping("/v1/listRecommend")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listRecommend(ProjWorkout116RecommendListReq req) {
        req.setExcludeProgramWorkout(true);
        return succ(projWorkout116PubService.queryAllList(req, EXERCISE_TYPES_V1));
    }

    @ApiOperation(value = "listAllV2", tags = {"oog116"})
    @GetMapping("/v2/listAll")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listAllV2() {
        return succ(projWorkout116PubService.queryAllList(new ProjWorkout116RecommendListReq(),EXERCISE_TYPES_V2));
    }

    @ApiOperation(value = "listAllV3", tags = {"oog116"})
    @GetMapping("/v3/listAll")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listAllV3() {
        return succ(projWorkout116PubService.queryAllList(new ProjWorkout116RecommendListReq(),EXERCISE_TYPES_V3));
    }

    @ApiOperation(value = "listAllV4", notes = "在v3的基础上，修改为前端传入需要包含的exerciseType", tags = {"oog116"})
    @GetMapping("/v4/listAll")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listAllV4(ProjWorkout116RecommendListReq req) {
/*        Set<Integer> containsExerciseCodeSet = req.getContainsExerciseCodeSet();
        BizExceptionUtil.throwIf(CollUtil.isEmpty(containsExerciseCodeSet),"containsExerciseCodeSet can not be empty");
        List<ExerciseType116Enums> containsTypes = containsExerciseCodeSet.stream()
                .map(ExerciseType116Enums::getByCode).filter(Objects::nonNull).collect(Collectors.toList());*/
        return succ(projWorkout116PubService.queryAllList(req,EXERCISE_TYPES_V4));
    }

    @ApiOperation(value = "listRecommendV2", tags = {"oog116"})
    @GetMapping("/v2/listRecommend")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listRecommendV2(ProjWorkout116RecommendListReq req) {
        req.setExcludeProgramWorkout(true);
        return succ(projWorkout116PubService.queryAllList(req,EXERCISE_TYPES_V2));
    }

    @ApiOperation(value = "listRecommendV3", tags = {"oog116"})
    @GetMapping("/v3/listRecommend")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listRecommendV3(ProjWorkout116RecommendListReq req) {
        req.setExcludeProgramWorkout(true);
        return succ(projWorkout116PubService.queryAllList(req,EXERCISE_TYPES_V3));
    }

    @ApiOperation(value = "listRecommendV4", notes = "在v3的基础上，修改为前端传入需要包含的exerciseType", tags = {"oog116"})
    @GetMapping("/v4/listRecommend")
    public ResponseResult<List<ProjWorkout116EnumCodeListVO>> listRecommendV4(ProjWorkout116RecommendListReq req) {
       /* Set<Integer> containsExerciseCodeSet = req.getContainsExerciseCodeSet();
        BizExceptionUtil.throwIf(CollUtil.isEmpty(containsExerciseCodeSet),"containsExerciseCodeSet can not be empty");
        List<ExerciseType116Enums> containsTypes = containsExerciseCodeSet.stream().map(ExerciseType116Enums::getByCode).filter(Objects::nonNull).collect(Collectors.toList());*/
        req.setExcludeProgramWorkout(true);
        return succ(projWorkout116PubService.queryAllList(req,EXERCISE_TYPES_V4));
    }

    @ApiOperation(value = "workout detail v1", tags = {"oog116"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjWorkout116DetailVO> detailV1(@PathVariable Integer id, WorkoutDataType116Enums dataType) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjWorkout116DetailVO detailVO = null;
        if (dataType == WorkoutDataType116Enums.ASSEMBLE) {
            List<ProjWorkout116DetailVO> workout116DetailVOList = projWorkout116PubService.getDetailByIdList(Collections.singletonList(id), true);
            if (!workout116DetailVOList.isEmpty()) {
                detailVO = workout116DetailVOList.get(GlobalConstant.ZERO);
            }
        } else if (dataType == WorkoutDataType116Enums.GENERATE) {
            Workout116GenerateReq workoutReq = new Workout116GenerateReq();
            workoutReq.setDataType(dataType).setId(id);
            List<ProjWorkout116DetailVO> workoutList = projWorkout116GenerateService.query(Collections.singletonList(id), null, versionInfoBO, true);
            if (CollectionUtil.isNotEmpty(workoutList)) {
                detailVO = workoutList.get(0);
            }
        }
        return succ(detailVO);
    }

    @ApiOperation(value = "workout detail v2", tags = {"oog116"})
    @GetMapping("/v2/detail/{id}")
    public ResponseResult<ProjWorkout116DetailVO> detailV2(@PathVariable Integer id, WorkoutDataType116Enums dataType, Integer m3u8Type) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjWorkout116DetailVO detailVO = null;
        if (dataType == WorkoutDataType116Enums.ASSEMBLE) {
            List<ProjWorkout116DetailVO> workout116DetailVOList = projWorkout116PubService.getDetailByIdList(Collections.singletonList(id), false);
            if (!workout116DetailVOList.isEmpty()) {
                detailVO = workout116DetailVOList.get(GlobalConstant.ZERO);
            }
        } else if (dataType == WorkoutDataType116Enums.GENERATE) {
            Workout116GenerateReq workoutReq = new Workout116GenerateReq();
            workoutReq.setDataType(dataType).setId(id);
            List<ProjWorkout116DetailVO> workoutList = projWorkout116GenerateService.query(Collections.singletonList(id), null, versionInfoBO, false);
            if (CollectionUtil.isNotEmpty(workoutList)) {
                detailVO = workoutList.get(0);
                m3u8Type = m3u8Type == null ? GlobalConstant.ONE : m3u8Type;
                if (m3u8Type != GlobalConstant.TWO) {
                    Optional.ofNullable(detailVO.getVideo2532Url()).filter(StrUtil::isNotBlank).ifPresent(detailVO::setVideoUrl);
                }
            }
        }
        replaceM3u8By2532(m3u8Type, detailVO);
        return succ(detailVO);
    }

    private void replaceM3u8By2532(Integer m3u8Type, ProjWorkout116DetailVO detail) {
        m3u8Type = m3u8Type == null ? GlobalConstant.ONE : m3u8Type;
        if (m3u8Type != GlobalConstant.TWO && detail != null) {
            Optional.ofNullable(detail.getVideo2532Url()).filter(StrUtil::isNotBlank).ifPresent(detail::setVideoUrl);
            for (ProjWorkout116UnitDetailVO unit : detail.getUnits()) {
                for (ResVideo116VO video : unit.getVideos()) {
                    Optional.ofNullable(video.getVideo2532Url()).filter(StrUtil::isNotBlank).ifPresent(video::setVideoUrl);
                }
            }
        }
    }

}
