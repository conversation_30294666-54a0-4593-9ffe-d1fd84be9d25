package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjCategory116Pub;
import com.laien.cmsapp.oog116.mapper.ProjCategory116PubMapper;
import com.laien.cmsapp.oog116.requst.CategoryListReq;
import com.laien.cmsapp.oog116.requst.ProjWorkout116ListReq;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.IProjCategory116PubService;
import com.laien.cmsapp.oog116.service.IProjWorkout116PubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog116.enums.Category116ShowTypeEnums;
import com.laien.common.oog116.enums.ExerciseType116Enums;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.util.RequestContextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * template116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Service
public class ProjCategory116PubServiceImpl extends ServiceImpl<ProjCategory116PubMapper, ProjCategory116Pub> implements IProjCategory116PubService {

    @Resource
    private IProjWorkout116PubService projWorkout116PubService;
    @Resource
    private  ICoreTextTaskI18nPubService textTaskI18nPubService;
    @Override
    public List<ProjCategory116VO> queryList(List<ExerciseType116Enums> containsTypes) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjCategory116Pub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                        ProjCategory116Pub::getId,
                        ProjCategory116Pub::getName,
                        ProjCategory116Pub::getEventName)
                .eq(ProjCategory116Pub::getProjId, versionInfoBO.getProjId())
                .eq(ProjCategory116Pub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjCategory116Pub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjCategory116Pub::getShowType, Category116ShowTypeEnums.CARD.getValue())
                .eq(ProjCategory116Pub::getGender, Gender116Enums.FEMALE)
                .orderByAsc(ProjCategory116Pub::getSortNo)
                .orderByDesc(ProjCategory116Pub::getId);
        List<ProjCategory116Pub> list = this.list(queryWrapper);
        List<ProjCategory116VO> returnList = new ArrayList<>(list.size());
        for (ProjCategory116Pub category116Pub : list) {

            ProjWorkout116ListReq workout116ListReq = new ProjWorkout116ListReq();
            workout116ListReq.setCategoryId(category116Pub.getId());
            workout116ListReq.setLimit(GlobalConstant.FIVE);
            List<ProjWorkout116ListVO> workoutList = projWorkout116PubService.queryList(workout116ListReq, containsTypes);
            if (!workoutList.isEmpty()) {
                ProjCategory116VO category116VO = new ProjCategory116VO();
                BeanUtils.copyProperties(category116Pub, category116VO);
                category116VO.setWorkoutList(workoutList);
                returnList.add(category116VO);
            }
        }

        return returnList;
    }

    @Override
    public ProjCategory116V2VO queryListV2(CategoryListReq req, List<ExerciseType116Enums> containsTypes) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjCategory116Pub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                        ProjCategory116Pub::getId,
                        ProjCategory116Pub::getName,
                        ProjCategory116Pub::getEventName,
                        ProjCategory116Pub::getShowType,
                        ProjCategory116Pub::getIconUrl,
                        ProjCategory116Pub::getCoverImgUrl)
                .eq(ProjCategory116Pub::getProjId, versionInfoBO.getProjId())
                .eq(ProjCategory116Pub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjCategory116Pub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(req.getGender() != null, ProjCategory116Pub::getGender, EnumUtil.getBy(Gender116Enums::getCode, req.getGender()))
                .orderByAsc(ProjCategory116Pub::getSortNo)
                .orderByDesc(ProjCategory116Pub::getId);
        Map<String, List<ProjCategory116Pub>> ListMap = this.list(queryWrapper)
                .stream().collect(Collectors.groupingBy(ProjCategory116Pub::getShowType));

        List<ProjCategory116Pub> cardPubList = ListMap.getOrDefault(Category116ShowTypeEnums.CARD.getValue(), new ArrayList<>());
        List<ProjCategory116VO> cardList = new ArrayList<>(cardPubList.size());
        List<Integer> idList = new ArrayList<>();
        for (ProjCategory116Pub category116Pub : cardPubList) {
            idList.add(category116Pub.getId());
            ProjWorkout116ListReq workout116ListReq = new ProjWorkout116ListReq();
            workout116ListReq.setCategoryId(category116Pub.getId());
            workout116ListReq.setLimit(GlobalConstant.FIVE);
            List<ProjWorkout116ListVO> workoutList = projWorkout116PubService.queryList(workout116ListReq, containsTypes);
            if (!workoutList.isEmpty()) {
                ProjCategory116VO category116VO = new ProjCategory116VO();
                BeanUtils.copyProperties(category116Pub, category116VO);
                category116VO.setWorkoutList(workoutList);
                cardList.add(category116VO);
            }
        }

        List<ProjCategory116Pub> labelPubList = ListMap.getOrDefault(Category116ShowTypeEnums.LABEL.getValue(), new ArrayList<>());
        List<ProjCategory116LableVO> labelList = new ArrayList<>(labelPubList.size());
        for (ProjCategory116Pub category116Pub : labelPubList) {
            idList.add(category116Pub.getId());
            ProjCategory116LableVO category116LableVO = new ProjCategory116LableVO();
            BeanUtils.copyProperties(category116Pub, category116LableVO);
            labelList.add(category116LableVO);
        }

        List<ProjCategory116Pub> gridPubList = ListMap.getOrDefault(Category116ShowTypeEnums.GRID.getValue(), new ArrayList<>());
        List<ProjCategory116GridVO> gridList = new ArrayList<>(gridPubList.size());
        for (ProjCategory116Pub category116Pub : gridPubList) {
            idList.add(category116Pub.getId());
            ProjWorkout116ListReq workout116ListReq = new ProjWorkout116ListReq();
            workout116ListReq.setCategoryId(category116Pub.getId());
            List<ProjWorkout116ListVO> workoutList = projWorkout116PubService.queryList(workout116ListReq, containsTypes);
            ProjCategory116GridVO category116VO = new ProjCategory116GridVO();
            BeanUtils.copyProperties(category116Pub, category116VO);
            category116VO.setWorkoutCount(workoutList.size());
            gridList.add(category116VO);
        }

        String language = RequestContextUtils.getLanguage();
        if (!Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)) {
            //新的翻译逻辑
            List<AppTextCoreI18nModel> transList = new ArrayList<>();
            if (CollUtil.isNotEmpty(cardList)) transList.addAll(cardList);
            if (CollUtil.isNotEmpty(gridList)) transList.addAll(gridList);
            if (CollUtil.isNotEmpty(labelList)) transList.addAll(labelList);
            if (!transList.isEmpty()) textTaskI18nPubService.translate(transList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(language));
        }

        ProjCategory116V2VO category116V2VO = new ProjCategory116V2VO();
        category116V2VO.setCardList(cardList);
        category116V2VO.setLabelList(labelList);
        category116V2VO.setGridList(gridList);
        return category116V2VO;
    }

    @Override
    public ProjCategory116V4VO queryListV4(CategoryListReq req, List<ExerciseType116Enums> containsTypes) {
        ProjCategory116V2VO v2VO = this.queryListV2(req, containsTypes);
        ProjCategory116V4VO category116V4VO = new ProjCategory116V4VO();
        category116V4VO.setGridList(v2VO.getGridList());
        category116V4VO.setLabelList(v2VO.getLabelList());

        List<ProjCategory116CodeVO> cardResultList = v2VO.getCardList().stream().map(category116VO -> {
            ProjCategory116CodeVO category116CodeVO = new ProjCategory116CodeVO();
            BeanUtil.copyProperties(category116VO, category116CodeVO);

            List<ProjWorkout116ListV4WithEnumStrVO> convertedWorkouts = category116VO.getWorkoutList().stream().map(workout116ListVO -> {
                ProjWorkout116ListV4WithEnumStrVO resultVO = new ProjWorkout116ListV4WithEnumStrVO();
                BeanUtil.copyProperties(workout116ListVO, resultVO);
                this.setDeEnumStrToVO(workout116ListVO);
                ProjWorkout116ListV4VO v4VO = projWorkout116PubService.convertWorkout2V4VO(workout116ListVO);
                BeanUtil.copyProperties(v4VO, resultVO);
                return resultVO;
            }).collect(Collectors.toList());

            category116CodeVO.setWorkoutList(convertedWorkouts);
            return category116CodeVO;
        }).collect(Collectors.toList());

        category116V4VO.setCardList(cardResultList);
        return category116V4VO;
    }

    private void setDeEnumStrToVO(ProjWorkout116ListVO workout116ListVO) {
        workout116ListVO.setRestriction(workout116ListVO.getRestrictionEn());
        workout116ListVO.setPosition(workout116ListVO.getPositionEn());
        workout116ListVO.setDifficulty(workout116ListVO.getDifficultyEn());
        workout116ListVO.setExerciseType(workout116ListVO.getExerciseTypeEn());
        workout116ListVO.setGender(workout116ListVO.getGenderEn());
    }
}
