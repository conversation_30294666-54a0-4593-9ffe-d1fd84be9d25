package com.laien.cmsapp.oog116.requst;

import com.laien.common.oog116.enums.WorkoutDataType116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ids list列表参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="id list", description="")
@Accessors(chain = true)
public class Workout116GenerateReq {

    @ApiModelProperty(value = "id", required = true)
    private Integer id;

    @ApiModelProperty(value = "是否是生成的workout,true:是，false:否", required = true)
    private WorkoutDataType116Enums dataType;

}
