package com.laien.cmsapp.oog116.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.AppAudioSingleTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.common.oog116.enums.TargetEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 116 video
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResVideo116对象", description="116 video")
@TableName(autoResultMap = true)
public class ResVideo116 extends BaseModel implements AppAudioCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作名称")
    @AppAudioSingleTranslateField(urlFieldName = "nameAudioUrl")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "视频类型Warm Up/Cool Down/Main")
    private String type;

    @ApiModelProperty(value = "部位Seated/Standing")
    private String position;

    @ApiModelProperty(value = "限制,多个用英文逗号分隔，取值Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private String restriction;

    @ApiModelProperty(value = "动作简介（How To Do）")
    @AppAudioSingleTranslateField(urlFieldName = "instructionsAudioUrl")
    private String instructions;

    @ApiModelProperty(value = "Video总的播放轮数，目前仅针对TaiChi类型Video")
    private Integer circuit;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontDuration;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "guidance")
    @AppAudioSingleTranslateField(urlFieldName = "guidanceAudioUrl")
    private String guidance;

    @ApiModelProperty(value = "Guidance音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "Instructions的音频")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "met,1-12的整数")
    private Integer met;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "取值 10:Full Body,11:Upper Body,12:Lower Body")
    private TargetEnums target;

    @ApiModelProperty(value = "身体部位 (多选)")
    @TableField(typeHandler = Region116Enums.TypeHandler.class)
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)")
    @TableField(typeHandler = Focus116Enums.TypeHandler.class)
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具")
    private SupportProp116Enums supportProp;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "i18n配置的语音")
    private Integer coreVoiceConfigI18nId;

}
