package com.laien.cmsapp.oog116.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.response.ProjProgram116DetailVO;
import com.laien.cmsapp.oog116.response.ProjProgram116ListVO;
import com.laien.cmsapp.oog116.service.IProjProgram116PubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Api(tags = "app端：program 116")
@RestController
@RequestMapping({"/{appCode}/program116"})
public class ProjProgram116Controller extends ResponseController {

    @Autowired
    private IProjProgram116PubService program116PubService;

    @ApiOperation(value = "list v1", tags = {"oog116"})
    @GetMapping(value = "v1/list")
    public ResponseResult<List<ProjProgram116ListVO>> listAll(@RequestParam(defaultValue = GlobalConstant.DEFAULT_LANGUAGE, required = false) String lang) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(program116PubService.listAllProgram(versionInfoBO, lang));
    }

    @ApiOperation(value = "detail v1", tags = {"oog116"})
    @GetMapping(value = "v1/detail")
    public ResponseResult<ProjProgram116DetailVO> getById(@RequestParam Integer id,
                                                          @RequestParam(defaultValue = "1", required = false) Integer m3u8Type,
                                                          @RequestParam(defaultValue = GlobalConstant.DEFAULT_LANGUAGE, required = false) String lang) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(program116PubService.getDetail(versionInfoBO, lang, id, m3u8Type));
    }

}
