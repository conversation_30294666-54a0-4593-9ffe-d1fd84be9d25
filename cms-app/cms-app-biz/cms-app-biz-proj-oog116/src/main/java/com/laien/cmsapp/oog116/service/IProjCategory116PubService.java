package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog116.entity.ProjCategory116Pub;
import com.laien.cmsapp.oog116.requst.CategoryListReq;
import com.laien.cmsapp.oog116.response.ProjCategory116V2VO;
import com.laien.cmsapp.oog116.response.ProjCategory116V4VO;
import com.laien.cmsapp.oog116.response.ProjCategory116VO;
import com.laien.common.oog116.enums.ExerciseType116Enums;

import java.util.List;

/**
 * <p>
 * template116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IProjCategory116PubService extends IService<ProjCategory116Pub> {

    /**
     * Category 列表查询
     *
     * @return list
     */
    List<ProjCategory116VO> queryList(List<ExerciseType116Enums> ignoreTypes);

    /**
     * Category v2 列表查询
     *
     * @return ProjCategory116V2VO
     */
    ProjCategory116V2VO queryListV2(CategoryListReq req, List<ExerciseType116Enums> containsTypes);

    /**
     * Category v4 列表查询
     * @param req  CategoryListReq
     * @param containsTypes  ExerciseType116Enums
     * @return ProjCategory116V4VO
     */
    ProjCategory116V4VO queryListV4(CategoryListReq req, List<ExerciseType116Enums> containsTypes);
}
