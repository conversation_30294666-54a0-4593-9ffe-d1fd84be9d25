package com.laien.cmsapp.oog116.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog116.enums.RecoveryCategory116ShowTypeEnums;
import com.laien.common.oog116.enums.RecoveryCategory116TypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * Recovery Category116 发布表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjRecoveryCategory116Pub对象", description="Recovery Category116 发布表")
@TableName(autoResultMap = true)
public class ProjRecoveryCategory116Pub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "展示类型")
    private RecoveryCategory116ShowTypeEnums showType;

    @TableField(typeHandler = RecoveryCategory116TypeEnums.TypeHandler.class)
    private List<RecoveryCategory116TypeEnums> type;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "icon url")
    private String iconUrl;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
