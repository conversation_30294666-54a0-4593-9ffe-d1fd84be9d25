package com.laien.cmsapp.oog116.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Generate;
import com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO;
import com.laien.cmsapp.oog116.response.ResVideo116VO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 116生成的workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface ProjWorkout116GenerateMapper extends BaseMapper<ProjWorkout116Generate> {

    List<ProjWorkout116DetailVO> query(@Param("templateId")Integer templateId,
                                       @Param("restrictionSum") Integer restrictionSum,
                                       @Param("dataVersion") Integer dataVersion);

    List<ProjWorkout116DetailVO> queryByTemplateIds(@Param("templateIds") List<Integer> templateIds,
                                                    @Param("restrictionSum") Integer restrictionSum,
                                                    @Param("dataVersion") Integer dataVersion);

    List<ResVideo116VO> queryVideoList(@Param("templateId") Integer templateId,
                                       @Param("restrictionSum") Integer restrictionSum);

    List<ResVideo116VO> queryVideoListByIds(@Param("idList") List<Integer> idList);

    List<ProjWorkout116DetailVO> findByIdList(@Param("idList") List<Integer> idList,
                                              @Param("dataVersion") Integer dataVersion);
}
