package com.laien.cmsapp.oog116.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Recovery Category116 Label VO
 *
 * <AUTHOR>
 * @since 2025/07/14
 */
@Data
@ApiModel(value = "Recovery Category116 Label VO", description = "Recovery Category116 Label VO")
public class ProjRecoveryCategory116LabelVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "category名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "icon url")
    private String iconUrl;

}
