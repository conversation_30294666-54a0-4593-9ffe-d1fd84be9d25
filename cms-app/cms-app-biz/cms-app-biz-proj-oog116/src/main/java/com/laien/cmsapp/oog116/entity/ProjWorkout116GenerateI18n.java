package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_workout116_generate i18n
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116GenerateI18n对象", description="proj_workout116_generate_i18n")
public class ProjWorkout116GenerateI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;


}
