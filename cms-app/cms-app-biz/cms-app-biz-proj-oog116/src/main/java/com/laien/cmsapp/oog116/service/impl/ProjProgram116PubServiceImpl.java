package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjProgram116Pub;
import com.laien.cmsapp.oog116.entity.ProjProgram116RelationPub;
import com.laien.cmsapp.oog116.mapper.ProjProgram116PubMapper;
import com.laien.cmsapp.oog116.response.ProjCoach116DetailVO;
import com.laien.cmsapp.oog116.response.ProjProgram116DetailVO;
import com.laien.cmsapp.oog116.response.ProjProgram116ListVO;
import com.laien.cmsapp.oog116.response.ProjWorkout116DetailV4VO;
import com.laien.cmsapp.oog116.service.IProjCoach116PubService;
import com.laien.cmsapp.oog116.service.IProjProgram116PubService;
import com.laien.cmsapp.oog116.service.IProjProgram116RelationPubService;
import com.laien.cmsapp.oog116.service.IProjWorkout116PubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.util.MyStringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: hhl
 * @date: 2025/5/20
 */
@Slf4j
@Service
public class ProjProgram116PubServiceImpl extends ServiceImpl<ProjProgram116PubMapper, ProjProgram116Pub> implements IProjProgram116PubService {

    @Resource
    IProjProgram116RelationPubService program116RelationPubService;

    @Resource
    IProjCoach116PubService coach116PubService;

    @Resource
    IProjWorkout116PubService workout116PubService;

    @Resource
    private ICoreTextTaskI18nPubService textTaskI18nPubService;

    @Override
    public List<ProjProgram116ListVO> listAllProgram(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang) {

        List<ProjProgram116Pub> program116Pubs = listByStatusAndVersion(null, GlobalConstant.STATUS_ENABLE, versionInfoBO);
        if (CollectionUtils.isEmpty(program116Pubs)) {
            return Collections.emptyList();
        }

        program116Pubs.sort(Comparator.comparing(ProjProgram116Pub::getNewStartTime, Comparator.nullsLast(Comparator.reverseOrder())).thenComparing(ProjProgram116Pub::getId, Comparator.reverseOrder()));
        setI18n4Program(program116Pubs, lang);
        List<ProjProgram116ListVO> program116ListVOS = program116Pubs.stream().map(this::convert2ListVO).collect(Collectors.toList());

        List<Integer> programIds = program116Pubs.stream().map(ProjProgram116Pub::getId).collect(Collectors.toList());
        List<ProjProgram116RelationPub> program116RelationPubs = program116RelationPubService.listProgramRelation(programIds, versionInfoBO);
        if (CollectionUtils.isEmpty(program116RelationPubs)) {
            return program116ListVOS;
        }

        Map<Integer, Integer> programWorkoutNumMap = program116RelationPubs.stream().collect(Collectors.groupingBy(ProjProgram116RelationPub::getProjProgram116Id, Collectors.summingInt(p -> 1)));
        program116ListVOS.forEach(program116ListVO -> {
            program116ListVO.setWorkoutNum(programWorkoutNumMap.getOrDefault(program116ListVO.getId(), 0));
        });
        return program116ListVOS;
    }

    private ProjProgram116ListVO convert2ListVO(ProjProgram116Pub projProgram116Pub) {

        ProjProgram116ListVO listVO = new ProjProgram116ListVO();
        BeanUtils.copyProperties(projProgram116Pub, listVO);

        if (Objects.equals(projProgram116Pub.getSubscription(), GlobalConstant.ONE)) {
            listVO.setSubscription(Boolean.TRUE);
        } else {
            listVO.setSubscription(Boolean.FALSE);
        }

        if (StringUtils.isEmpty(projProgram116Pub.getGoals())) {
            listVO.setGoals(Collections.emptyList());
        } else {
            List<String> goals = Arrays.stream(MyStringUtil.getSplitWithComa(projProgram116Pub.getGoals())).collect(Collectors.toList());
            listVO.setGoals(goals);
        }
        return listVO;
    }

    private List<ProjProgram116Pub> listByStatusAndVersion(Collection<Integer> programIds, Integer status, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        LambdaQueryWrapper<ProjProgram116Pub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(programIds), ProjProgram116Pub::getId, programIds);
        queryWrapper.eq(Objects.nonNull(status), ProjProgram116Pub::getStatus, status);

        queryWrapper.eq(ProjProgram116Pub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjProgram116Pub::getProjId, versionInfoBO.getProjId());
        return list(queryWrapper);
    }

    private void setI18n4Program(List<ProjProgram116Pub> program116PubList, String lang) {
        //文本翻译
        if (CollUtil.isNotEmpty(program116PubList))  textTaskI18nPubService.translate(program116PubList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
    }

    @Override
    public ProjProgram116DetailVO getDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang, Integer id, Integer m3u8Type) {

        List<ProjProgram116Pub> program116Pubs = listByStatusAndVersion(Collections.singleton(id), GlobalConstant.STATUS_ENABLE, versionInfoBO);
        if (CollectionUtils.isEmpty(program116Pubs)) {
            return null;
        }

        setI18n4Program(program116Pubs, lang);
        ProjProgram116Pub program116Pub = program116Pubs.get(0);
        ProjProgram116DetailVO program116DetailVO = convert2DetailVO(program116Pub);

        ProjCoach116DetailVO coach116DetailVO = coach116PubService.getCoach(program116Pub.getCoachId(), versionInfoBO, lang);
        if (Objects.nonNull(coach116DetailVO)) {
            program116DetailVO.setInstructor(coach116DetailVO);
        }

        List<ProjProgram116RelationPub> program116RelationPubs = program116RelationPubService.listProgramRelation(Collections.singletonList(program116Pub.getId()), versionInfoBO);
        if (!CollectionUtils.isEmpty(program116RelationPubs)) {
            List<Integer> workoutIds = program116RelationPubs.stream().map(ProjProgram116RelationPub::getProjWorkout116Id).collect(Collectors.toList());
            List<ProjWorkout116DetailV4VO> detailV4VOList = workout116PubService.getDetailByIdList(workoutIds, m3u8Type);
            if(CollUtil.isEmpty(detailV4VOList)){
                return program116DetailVO;
            }
            Map<Integer, ProjWorkout116DetailV4VO> workoutMap = detailV4VOList.stream().collect(Collectors.toMap(ProjWorkout116DetailV4VO::getId, item -> item));
            List<ProjWorkout116DetailV4VO> finalWrokoutList = new ArrayList<>();
            for (Integer workoutId : workoutIds) {
                ProjWorkout116DetailV4VO projWorkout116DetailV4VO = workoutMap.get(workoutId);
                if(null != projWorkout116DetailV4VO){
                    finalWrokoutList.add(projWorkout116DetailV4VO);
                }
            }
            program116DetailVO.setClassList(finalWrokoutList);
        }
        return program116DetailVO;
    }

    private ProjProgram116DetailVO convert2DetailVO(ProjProgram116Pub projProgram116Pub) {

        ProjProgram116DetailVO detailVO = new ProjProgram116DetailVO();
        BeanUtils.copyProperties(projProgram116Pub, detailVO);

        if (Objects.equals(projProgram116Pub.getSubscription(), GlobalConstant.ONE)) {
            detailVO.setSubscription(Boolean.TRUE);
        } else {
            detailVO.setSubscription(Boolean.FALSE);
        }

        if (StringUtils.isEmpty(projProgram116Pub.getDescription())) {
            detailVO.setDescriptionList(Collections.emptyList());
        } else {
            List<String> descList = MyStringUtil.toList(projProgram116Pub.getDescription());
            detailVO.setDescriptionList(descList);
        }

        if (StringUtils.isEmpty(projProgram116Pub.getGoals())) {
            detailVO.setGoals(Collections.emptyList());
        } else {
            List<String> goals = Arrays.stream(MyStringUtil.getSplitWithComa(projProgram116Pub.getGoals())).collect(Collectors.toList());
            detailVO.setGoals(goals);
        }

        return detailVO;
    }

    @Override
    public Set<Integer> getAllProgramWokroutIdSet(ProjPublishCurrentVersionInfoBO versionInfoBO) {
        if (versionInfoBO == null) versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjProgram116Pub> program116Pubs = listByStatusAndVersion(null, GlobalConstant.STATUS_ENABLE, versionInfoBO);
        if(CollUtil.isEmpty(program116Pubs)) return Collections.emptySet();
        List<Integer> programIds = program116Pubs.stream().map(ProjProgram116Pub::getId).collect(Collectors.toList());
        List<ProjProgram116RelationPub> program116RelationPubs = program116RelationPubService.listProgramRelation(programIds, versionInfoBO);
        if(CollUtil.isEmpty(program116RelationPubs)) return Collections.emptySet();
        return program116RelationPubs.stream().map(ProjProgram116RelationPub::getProjWorkout116Id).collect(Collectors.toSet());
    }

}
