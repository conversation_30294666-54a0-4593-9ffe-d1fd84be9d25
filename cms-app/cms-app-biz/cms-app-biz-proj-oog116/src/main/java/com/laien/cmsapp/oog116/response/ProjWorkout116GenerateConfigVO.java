package com.laien.cmsapp.oog116.response;

import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.oog116.enums.WorkoutGenerate116ImagePoint;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProjWorkout116GenerateConfigVO {

    @ApiModelProperty(value = "Standing:10,Seated:11,Both:12")
    private Integer positionCode;

    @ApiModelProperty(value = "性别，Female:10,Male:11,Both:12")
    private Integer genderCode;

    @ApiModelProperty(value = "Exercise 类型：Chair Cardio: 10,Tai Chi: 11,Dancing: 12,Gentle Cardio: 13,Walking: 14,<PERSON><PERSON><PERSON> (lightweight): 15,Resistance Band: 16, Gentle Chair Yoga: 17")
    private Integer exerciseTypeCode;

    @ApiModelProperty(value = "手组 workout id list")
    private List<Integer> workoutIdList;

    public ProjWorkout116GenerateConfigVO(WorkoutGenerate116ImagePoint imagePoint, Gender116Enums gender116Enums, List<Integer> workoutIdList) {

        this.exerciseTypeCode = imagePoint.getExerciseType().getCode();
        this.positionCode = imagePoint.getPosition().getCode();
        this.genderCode = gender116Enums.getCode();
        this.workoutIdList = workoutIdList;
    }
}
