package com.laien.cmsapp.oog116.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * note: ProjTemplate116PlanReq
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "oog116 template plan req", description = "oog116 template plan req")
public class Workout116GeneratePlanV4Req {


    @ApiModelProperty(value = "性别，Female:10,Male:11,Both:12")
    private Integer genderCode;

    @ApiModelProperty(value = "m3u8Type 传1 代表查询2532 m3u8,传2 代表查询 dynamic m3u8, 默认按1的逻辑")
    private Integer m3u8Type;

    @ApiModelProperty(value = "器械，No equipment:10,Dumbbell (lightweight):11,Resistance band:12," +
            "<PERSON><PERSON><PERSON> (midweight):15")
    private Set<Integer> equipmentCodeSet;

    @ApiModelProperty(value = "性别，Female:10,Male:11,Both:12")
    private Integer coachGenderCode;

    @ApiModelProperty(value = "时长标签,5-10:10,10-15:11,15-20:12,20-30:13")
    private Integer durationCode;

    @ApiModelProperty(value = "Standing:10,Seated:11,Both:12")
    private Integer positionCode;

    @ApiModelProperty(value = "限制，Shoulder:10,Back:11,Wrist:12,Knee:13,Ankle:14,Hip:15")
    private Set<Integer> restrictionCodeSet;

    @ApiModelProperty(value = "random")
    private Integer random;

    @ApiModelProperty(value = "planType,NEW:10,OLD:11")
    private Integer planType;

    @ApiModelProperty(value = "完成次数")
    private Integer completeTimes;

    @ApiModelProperty(value = "exercise type，取值：Chair Cardio:10,Tai Chi:11,Dancing:12,Gentle Cardio:13,Walking:14," +
            "Dumbbell (lightweight):15,Resistance Band:16,Chair Yoga:17, Dumbbell (midweight) :21")
    private Set<Integer> exerciseTypeCodeSet;

}
