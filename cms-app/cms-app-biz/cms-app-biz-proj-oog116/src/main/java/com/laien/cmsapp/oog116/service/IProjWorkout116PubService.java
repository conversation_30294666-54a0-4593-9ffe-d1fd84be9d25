package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Pub;
import com.laien.cmsapp.oog116.requst.ProjWorkout116ListReq;
import com.laien.cmsapp.oog116.requst.ProjWorkout116RecommendListReq;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.impl.ProjWorkout116PubServiceImpl;
import com.laien.common.oog116.enums.ExerciseType116Enums;

import java.util.List;

/**
 * <p>
 * proj_workout116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IProjWorkout116PubService extends IService<ProjWorkout116Pub> {

    /**
     * workout 列表查询
     *
     * @param workout116ListReq workout116ListReq
     * @return list
     */
    List<ProjWorkout116ListVO> queryList(ProjWorkout116ListReq workout116ListReq, List<ExerciseType116Enums> containsTypes);

    List<ProjWorkout116ListV4VO> listV4(ProjWorkout116ListReq workout116ListReq);

    ProjWorkout116ListV4VO convertWorkout2V4VO(ProjWorkout116ListVO workout116);

    /**
     *  根据id list 查询workout 列表
     *
     * @param idList idList
     * @param downloadJson downloadJson
     * @return list
     */
    List<ProjWorkout116DetailVO> getDetailByIdList(List<Integer> idList, boolean downloadJson);

    List<ProjWorkout116DetailV4VO> getDetailByIdList(List<Integer> idList, Integer m3u8Type);

    /**
     * 查询所有 workout 列表
     *
     * @return
     */
    List<ProjWorkout116EnumCodeListVO> queryAllList(ProjWorkout116RecommendListReq req, List<ExerciseType116Enums> ignoreTypes);
    /**
     * 获取workout关联的分类名称集合
     *
     * @param idList workout id列表
     * @return workout对应的分类名称集合
     */
    ProjWorkout116PubServiceImpl.CategoryRelatedResult getWorkoutCategoryNameSetMap(List<Integer> idList);
}
