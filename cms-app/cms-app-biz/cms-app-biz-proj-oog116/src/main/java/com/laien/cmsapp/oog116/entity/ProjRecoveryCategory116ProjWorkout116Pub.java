package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Recovery Category116 与 Workout116 关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjRecoveryCategory116ProjWorkout116Pub对象", description="Recovery Category116 与 Workout116 关联表")
public class ProjRecoveryCategory116ProjWorkout116Pub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "proj_recovery_category116_id")
    private Integer projRecoveryCategory116Id;

    @ApiModelProperty(value = "proj_workout116_id")
    private Integer projWorkout116Id;
}
