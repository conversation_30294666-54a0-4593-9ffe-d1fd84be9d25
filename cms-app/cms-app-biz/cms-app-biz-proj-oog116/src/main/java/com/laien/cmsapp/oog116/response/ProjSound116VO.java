package com.laien.cmsapp.oog116.response;

import com.laien.cmsapp.oog116.entity.ProjSound116;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "Sound VO", description = "Sound VO")
public class ProjSound116VO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "文件地址")
    private String soundUrl;

    @ApiModelProperty(value = "文件名称")
    private String soundUrlName;

    public ProjSound116VO(ProjSound116 projSound116) {
        this.id = projSound116.getId();
        this.soundName = projSound116.getSoundName();
        this.soundScript = projSound116.getSoundScript();
        this.soundUrl = projSound116.getUrl();

    }
}
