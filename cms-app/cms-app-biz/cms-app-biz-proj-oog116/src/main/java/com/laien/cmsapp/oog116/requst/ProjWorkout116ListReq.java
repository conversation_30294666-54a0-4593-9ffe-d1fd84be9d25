package com.laien.cmsapp.oog116.requst;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Set;

/**
 * note: workout116 列表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout116 列表", description = "workout video 查询")
public class ProjWorkout116ListReq {

    @ApiModelProperty(value = "category id")
    private Integer categoryId;

    @ApiModelProperty(value = "排序 Newest | Time 默认Newest")
    private String orderBy;

    @JsonIgnore
    @ApiModelProperty(value = "返回条数")
    private Integer limit;

    @ApiModelProperty(value = "Workout Type name",hidden = true)
    private Set<String> workoutTypeSet;

    @ApiModelProperty(value = "containsExerciseSet",hidden = true)
    private Set<String> containsExerciseSet;

    @ApiModelProperty(value = "Difficulty name",hidden = true)
    private String difficulty;

    @ApiModelProperty(value = "Category name",hidden = true)
    private Set<Integer> categoryTypeSet;

    @ApiModelProperty(value = "不翻译枚举字段标识",hidden = true)
    private Boolean noEnumFieldI18nFlag = false;

}
