package com.laien.cmsapp.oog116.requst;


import com.laien.common.oog116.enums.Gender116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "声音请求参数", description = "声音请求参数")
public class ProjSound116Req {

    @ApiModelProperty(value = "声音源(female|male 默认female")
    private Gender116Enums gender;
}
