package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.BiMap;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.config.Oog116Config;
import com.laien.cmsapp.oog116.entity.ProjTemplate116Rule;
import com.laien.cmsapp.oog116.entity.ProjWorkout116I18nPub;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Pub;
import com.laien.cmsapp.oog116.mapper.ProjWorkout116PubMapper;
import com.laien.cmsapp.oog116.requst.ProjWorkout116ListReq;
import com.laien.cmsapp.oog116.requst.ProjWorkout116RecommendListReq;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.IProjProgram116PubService;
import com.laien.cmsapp.oog116.service.IProjTemplate116RuleService;
import com.laien.cmsapp.oog116.service.IProjWorkout116I18nPubService;
import com.laien.cmsapp.oog116.service.IProjWorkout116PubService;
import com.laien.cmsapp.oog116.utils.Workout116Util;
import com.laien.cmsapp.response.ProjPlaylistAppVO;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjPlaylistService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog116.enums.*;
import com.laien.common.util.MyStringUtil;
import com.laien.common.util.RequestContextUtils;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.WorkoutDataType116Enums.ASSEMBLE;

/**
 * <p>
 * proj_workout116 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Slf4j
@Service
public class ProjWorkout116PubServiceImpl extends ServiceImpl<ProjWorkout116PubMapper, ProjWorkout116Pub> implements IProjWorkout116PubService {

    @Resource
    private FileService fileService;
    @Resource
    private IProjPlaylistService projPlaylistService;
    @Resource
    private IProjWorkout116I18nPubService projWorkout116I18nPubService;
    @Resource
    private IProjTemplate116RuleService projTemplate116RuleService;
    @Resource
    private Oog116Config oog116Config;
    @Resource
    private IProjProgram116PubService projProgram116PubService;
    @Resource
    private  ICoreSpeechTaskI18nPubService speechTaskI18nPubService;
    @Resource
    private ICoreTextTaskI18nPubService textTaskI18nPubService;


    public static String NONE = "None";
    public static String ALL_BODY_PARTS = "All body parts";
    public static String CHAIR = "Chair";

    @Override
    public List<ProjWorkout116ListVO> queryList(ProjWorkout116ListReq workout116ListReq, List<ExerciseType116Enums> containsTypes) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        handleExerciseTypeReq(workout116ListReq, containsTypes);
        List<ProjWorkout116ListVO> workout116ListVOList = this.baseMapper.queryList(versionInfoBO, workout116ListReq);
        // 是None 替换为 All body parts
        List<Integer> idList = new ArrayList<>(workout116ListVOList.size());
        Set<String> textSet = new HashSet<>();
        for (ProjWorkout116ListVO projWorkout116ListVO : workout116ListVOList) {
            if (Objects.equals(projWorkout116ListVO.getRestriction(), NONE)) {
                projWorkout116ListVO.setRestriction(ALL_BODY_PARTS);
                textSet.add(ALL_BODY_PARTS);
            }
            idList.add(projWorkout116ListVO.getId());
        }
        setEnumFieldEn(workout116ListVOList);
        String language = RequestContextUtils.getLanguage();
        if (!Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)) {
            if (workout116ListReq.getNoEnumFieldI18nFlag()) {
                // 将原始列表转换为副本列表
                List<CopyProjWorkout116ListVO> copyList = workout116ListVOList.stream()
                        .map(item -> {
                            CopyProjWorkout116ListVO copy = new CopyProjWorkout116ListVO();
                            BeanUtil.copyProperties(item, copy);
                            return copy;
                        })
                        .collect(Collectors.toList());

                // 翻译副本
                textTaskI18nPubService.translate(copyList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(language));
                // 将副本再转换回原始类型
                workout116ListVOList = copyList.stream()
                        .map(copy -> {
                            ProjWorkout116ListVO original = new ProjWorkout116ListVO();
                            BeanUtil.copyProperties(copy, original);
                            return original;
                        })
                        .collect(Collectors.toList());
            } else {
                if (CollUtil.isNotEmpty(workout116ListVOList))  textTaskI18nPubService.translate(workout116ListVOList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(language));
            }
        }
        return workout116ListVOList;
    }

    /**
     * 设置数据类型枚举字段
     * @param workout116ListVOList
     */
    private void setEnumFieldEn(List<ProjWorkout116ListVO> workout116ListVOList) {
        workout116ListVOList.forEach(
                workout116ListVO -> {
                    workout116ListVO.setRestrictionEn(workout116ListVO.getRestriction());
                    workout116ListVO.setPositionEn(workout116ListVO.getPosition());
                    workout116ListVO.setDifficultyEn(workout116ListVO.getDifficulty());
                    workout116ListVO.setExerciseTypeEn(workout116ListVO.getExerciseType());
                    workout116ListVO.setGenderEn(workout116ListVO.getGender());
                }
        );
    }

    private void handleExerciseTypeReq(ProjWorkout116ListReq workout116ListReq,
                                       List<ExerciseType116Enums> containsTypes) {
        if (!CollectionUtil.isEmpty(containsTypes)) {
            workout116ListReq.setContainsExerciseSet(containsTypes.stream().map(ExerciseType116Enums::getName).collect(Collectors.toSet()));
        }
    }

    private List<ProjWorkout116ListVO> ignoreByExerciseType(List<ProjWorkout116ListVO> workout116ListVOList,
                                                            List<ExerciseType116Enums> exerciseType116Enums) {
        if (CollectionUtil.isEmpty(workout116ListVOList) || CollectionUtil.isEmpty(exerciseType116Enums)) {
            return new ArrayList<>();
        }

        Set<String> exerciseTypeNames = exerciseType116Enums.stream().map(ExerciseType116Enums::getName).collect(Collectors.toSet());
        return workout116ListVOList.stream().filter(workout -> exerciseTypeNames.contains(workout.getExerciseType())).collect(Collectors.toList());
    }

    @Override
    public List<ProjWorkout116ListV4VO> listV4(ProjWorkout116ListReq workout116ListReq) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjWorkout116ListVO> workout116ListVOList = this.baseMapper.queryList(versionInfoBO, workout116ListReq);
        if (CollectionUtil.isEmpty(workout116ListVOList)) {
            log.error("Workout116 V4 list is empty, with req param : {}, version : {}", workout116ListReq, versionInfoBO.getCurrentVersion());
            return Collections.emptyList();
        }

        List<ProjWorkout116ListV4VO> workoutV4List = workout116ListVOList.stream().map(this::convertWorkout2V4VO).collect(Collectors.toList());
        String language = RequestContextUtils.getLanguage();
        if (Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)) {
            return workoutV4List;
        }
        //文本翻译
        if (CollUtil.isNotEmpty(workoutV4List))  textTaskI18nPubService.translate(workoutV4List, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(language));
        return workoutV4List;
    }

    @Override
    public ProjWorkout116ListV4VO convertWorkout2V4VO(ProjWorkout116ListVO workout116) {

        ProjWorkout116ListV4VO workoutV4VO = new ProjWorkout116ListV4VO();
        BeanUtils.copyProperties(workout116, workoutV4VO);

        DifficultyEnums difficultyEnums = DifficultyEnums.getByName(workout116.getDifficulty());
        if (Objects.nonNull(difficultyEnums)) {
            workoutV4VO.setDifficultyCode(difficultyEnums.getCode());
        }

        Position116Enums position116Enums = Position116Enums.getByName(workout116.getPosition());
        if (Objects.nonNull(position116Enums)) {
            workoutV4VO.setPositionCode(position116Enums.getCode());
        }

        Gender116Enums gender116Enums = Gender116Enums.getByName(workout116.getGender());
        if (Objects.nonNull(gender116Enums)) {
            workoutV4VO.setGenderCode(gender116Enums.getCode());
        }

        ExerciseType116Enums exerciseType116Enums = ExerciseType116Enums.getByName(workout116.getExerciseType());
        if (Objects.nonNull(exerciseType116Enums)) {
            workoutV4VO.setExerciseTypeCode(exerciseType116Enums.getCode());
        }

        if (StringUtils.isNotBlank(workout116.getRestriction())) {
            List<String> restrictionList = Arrays.stream(MyStringUtil.getSplitWithComa(workout116.getRestriction())).collect(Collectors.toList());
            Set<Integer> restrictionCodes = Restriction116Enums.getCodeByString(restrictionList);
            workoutV4VO.setRestrictionCodeSet(restrictionCodes);
        }
        return workoutV4VO;
    }

    @Override
    public List<ProjWorkout116DetailVO> getDetailByIdList(List<Integer> idList, boolean downloadJson) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjWorkout116DetailVO> workout116DetailVOList = this.baseMapper.queryDetailByIdList(versionInfoBO, idList);
        if (workout116DetailVOList.isEmpty()) {
            return workout116DetailVOList;
        }
        Map<Integer, List<ResVideo116VO>> videoListMap = this.baseMapper.queryVideoByIdList(versionInfoBO, idList)
                .stream().collect(Collectors.groupingBy(ResVideo116VO::getWorkoutId));
        // key:exerciseType, value: playListId
        HashMap<String, Integer> playListMap = new HashMap<>();
        for (ProjWorkout116DetailVO detailVO : workout116DetailVOList) {
            if (!downloadJson) {
                // v2 接口返回空 不能设置为空字符，可以为null
                detailVO.setAudioJsonList(null);
            } else if (StringUtil.isBlank(detailVO.getAudioJsonList())) {
                String audioJsonContent = null;
                try {
                    audioJsonContent = fileService.getJsonContent(detailVO.getAudioJsonUrl());
                } catch (Exception e) {
                    log.error("There was an error downloading the oog116 json file. Please clear the Cloudflare cache now.");
                }
                detailVO.setAudioJsonList(audioJsonContent);
            }
            List<ResVideo116VO> videoList = videoListMap.getOrDefault(detailVO.getId(), new ArrayList<>(GlobalConstant.ZERO));

            List<ProjWorkout116UnitDetailVO> units = new ArrayList<>();
            Map<String, ProjWorkout116UnitDetailVO> unitMap = new HashMap<>();
            List<String> myEquipmentList = new ArrayList<>();
            boolean findChair = false;
            for (ResVideo116VO video : videoList) {
                String unitName = video.getUnitName();
                if (unitMap.containsKey(unitName)) {
                    unitMap.get(unitName).getVideos().add(video);
                } else {
                    ProjWorkout116UnitDetailVO unit = new ProjWorkout116UnitDetailVO();
                    unit.setRuleId(video.getUnitId());
                    unit.setUnitName(video.getUnitName());
                    unit.setRounds(video.getRounds());
                    List<ResVideo116VO> videos = new ArrayList<>();
                    videos.add(video);
                    unit.setVideos(videos);

                    unitMap.put(unitName, unit);
                    units.add(unit);
                }

                if (!findChair && Objects.equals(video.getPosition(), Position116Enums.SEATED.getName())) {
                    myEquipmentList.add(CHAIR);
                    findChair = true;
                }

            }
            detailVO.setEquipmentList(myEquipmentList);

            String execriseType = detailVO.getExerciseType();
            Integer playlistId = playListMap.get(execriseType);
            if (null == playlistId) {
                playlistId = getPlaylistId(execriseType);
                playListMap.put(execriseType, playlistId);
            }
            detailVO.setPlaylistId(playlistId);
            detailVO.setUnits(units);
            String equipment = detailVO.getEquipment();
            List<Equipment116Enums> equipmentList = Equipment116Enums.getEquipmentList(equipment);
            if (CollectionUtil.isEmpty(equipmentList)) {
                log.error("card name is empty, workoutId:{}, equipment:{}", detailVO.getId(), equipment);
            } else {
                List<EquipmentVO> equipmentVoList = new ArrayList<>(equipmentList.size());
                if (equipmentList.size() > 1) {
                    equipmentList.remove(Equipment116Enums.NONE);
                }
                for (Equipment116Enums equipmentEnums : equipmentList) {
                    equipmentVoList.add(new EquipmentVO(equipmentEnums.getCardName(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                }
                detailVO.setCardEquipment(equipmentVoList);
            }

            // setEquipmentV3List
            List<EquipmentVO> equipmentVoList = new ArrayList<>();
            if (StringUtils.isNotBlank(equipment)) {
                List<Equipment116Enums> equipmentEnumList = Equipment116Enums.getEquipmentList(equipment);
                if (CollectionUtil.isNotEmpty(equipmentEnumList)) {
                    // 排查掉NONE
                    equipmentEnumList.remove(Equipment116Enums.NONE);
                    for (Equipment116Enums equipmentEnums : equipmentEnumList) {
                        equipmentVoList.add(new EquipmentVO(equipmentEnums.getCardName(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(myEquipmentList)) {
                // 不是空就肯定是chair
                equipmentVoList.add(new EquipmentVO(myEquipmentList.get(0), null, "cms/video116/img/chair.png"));
            }
            detailVO.setEquipmentV3List(equipmentVoList);


            if (Objects.equals(detailVO.getRestriction(), NONE)) {
                detailVO.setRestriction(ALL_BODY_PARTS);
            }
            // 添加默认英语
            List<ProjWorkout116AudioDetailVO> audioList = new ArrayList<>();
            ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
            audioDetailVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
            audioDetailVO.setAudioJsonUrl(detailVO.getAudioJsonUrl());
            audioList.add(audioDetailVO);
            detailVO.setAudioList(audioList);
        }


        //  多语言处理，workout 不管传的什么语言，audioList都要全量返回，video 按指定语言返回
        String lang = RequestContextUtils.getLanguage();
        if (!workout116DetailVOList.isEmpty()) {
            List<Integer> workoutIdList = new ArrayList<>();
            for (ProjWorkout116DetailVO detailVO : workout116DetailVOList) {
                workoutIdList.add(detailVO.getId());
            }

            // 查询翻译
            Map<Integer, List<ProjWorkout116I18nPub>> workoutBizI18nMap = projWorkout116I18nPubService.getAllLanguageListByIds(workoutIdList);
            Map<String, String> equipmentMap = translateI18n(workout116DetailVOList, lang);
            //文本翻译
            for (ProjWorkout116DetailVO detailVO : workout116DetailVOList) {
                    Integer workoutId = detailVO.getId();
                    List<String> equipmentList = detailVO.getEquipmentList();
                    List<EquipmentVO> cardEquipment = detailVO.getCardEquipment();
                    List<EquipmentVO> equipmentV3List = detailVO.getEquipmentV3List();
                if (CollectionUtil.isNotEmpty(equipmentList)) {
                    List<String> equipmentI18nList = new ArrayList<>();
                    for (String equipment : equipmentList) {
                        equipmentI18nList.add(equipmentMap.get(equipment));
                    }
                    detailVO.setEquipmentList(equipmentI18nList);
                }
                if (workoutBizI18nMap.containsKey(workoutId)) {
                    List<ProjWorkout116I18nPub> i18nBizList = workoutBizI18nMap.get(workoutId);
                    for (ProjWorkout116I18nPub workout116I18nBiz : i18nBizList) {
                        // 添加其他语言音频
                        String audioJsonUrl = workout116I18nBiz.getAudioJsonUrl();
                        if (StringUtils.isNotBlank(audioJsonUrl)) {
                            ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
                            audioDetailVO.setLanguage(workout116I18nBiz.getLanguage());
                            audioDetailVO.setAudioJsonUrl(workout116I18nBiz.getAudioJsonUrl());
                            detailVO.getAudioList().add(audioDetailVO);
                        }
                    }
                }
            }
        }


        return workout116DetailVOList;
    }



    @Override
    public List<ProjWorkout116DetailV4VO> getDetailByIdList(List<Integer> idList, Integer m3u8Type) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjWorkout116DetailVO> workout116DetailVOList = this.baseMapper.queryDetailByIdList(versionInfoBO, idList);
        if (workout116DetailVOList.isEmpty()) {
            return new ArrayList<>();
        }
        List<ResVideo116VO> videoList = this.baseMapper.queryVideoByIdList(versionInfoBO, idList);
        Set<Integer> ruleIdSet = videoList.stream().map(ResVideo116VO::getRuleId).collect(Collectors.toSet());

        Set<ExerciseType116Enums> exerciseTypeSet = new HashSet<>();
        for (ProjWorkout116DetailVO detailVO : workout116DetailVOList) {
            List<String> restrictionStringList = Arrays.stream(MyStringUtil.getSplitWithComa(detailVO.getRestriction())).collect(Collectors.toList());
            detailVO.setRestrictionSum(Restriction116Enums.sumByString(restrictionStringList));
            ExerciseType116Enums exerciseType = ExerciseType116Enums.getByName(detailVO.getExerciseType());
            if(null != exerciseType){
                exerciseTypeSet.add(exerciseType);
            }
        }

        List<ProjTemplate116Rule> ruleList = projTemplate116RuleService.find(ruleIdSet);
        List<ProjWorkout116DetailV4VO> workoutList = Workout116Util.dataConversion(videoList, workout116DetailVOList, new ArrayList<>(exerciseTypeSet), ruleList, m3u8Type, ASSEMBLE);
        List<Integer> workoutIdList = workoutList.stream().map(ProjWorkout116DetailV4VO::getId).collect(Collectors.toList());

        // 查询翻译
        String language = RequestContextUtils.getLanguage();
        Map<Integer, List<ProjWorkout116I18nPub>> workoutBizI18nMap = projWorkout116I18nPubService.getAllLanguageListByIds(workoutIdList);
        //翻译 workoutList
        List<AppTextCoreI18nModel> textCoreI18nModelList = new ArrayList<>();
        List<AppAudioCoreI18nModel> audioCoreI18nModelList = new ArrayList<>();
        if (CollUtil.isNotEmpty(workoutList)) textCoreI18nModelList.addAll(workoutList);
        for (ProjWorkout116DetailV4VO detailVO : workoutList) {
            if (CollUtil.isNotEmpty(detailVO.getUnits()))   {
                textCoreI18nModelList.addAll(detailVO.getUnits());
               for (ProjWorkout116UnitDetailV4VO unit : detailVO.getUnits()) {
                   if (CollUtil.isNotEmpty(unit.getVideos())) audioCoreI18nModelList.addAll(unit.getVideos());
               }
            }
        }
        if(!textCoreI18nModelList.isEmpty()) textTaskI18nPubService.translate(textCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(language));
        if(!audioCoreI18nModelList.isEmpty()) speechTaskI18nPubService.translate(audioCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(language));
        for (ProjWorkout116DetailV4VO detailVO : workoutList) {
            Integer workoutId = detailVO.getId();
            handleWorkoutI18n(detailVO, workoutId, workoutBizI18nMap);
            //当不为英语时,descriptionList, instructionList需要重新处理
            if (!Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)){
                handleI18n(detailVO);
            }
        }

        Workout116Util.handleWorkout4TaiChi(workoutList);
        return workoutList;
    }

    private void handleI18n(ProjWorkout116DetailV4VO detailVO) {
        detailVO.setDescriptionList(MyStringUtil.toList(detailVO.getDescription()));
        List<ProjWorkout116UnitDetailV4VO> units = detailVO.getUnits();
        for (ProjWorkout116UnitDetailV4VO unit : units) {
            List<ResVideo116V4VO> videos = unit.getVideos();
            for (ResVideo116V4VO video : videos) {
                video.setInstructionList(MyStringUtil.toList(video.getInstructions()));
            }
        }
    }

    private static void handleWorkoutI18n(ProjWorkout116DetailV4VO detailVO, Integer workoutId, Map<Integer, List<ProjWorkout116I18nPub>> workoutBizI18nMap) {
        if (workoutBizI18nMap.containsKey(workoutId)) {
            List<ProjWorkout116I18nPub> i18nBizList = workoutBizI18nMap.get(workoutId);
            for (ProjWorkout116I18nPub workout116I18nBiz : i18nBizList) {
                // 添加其他语言音频
                String audioJsonUrl = workout116I18nBiz.getAudioJsonUrl();
                if (StringUtils.isNotBlank(audioJsonUrl)) {
                    ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
                    audioDetailVO.setLanguage(workout116I18nBiz.getLanguage());
                    audioDetailVO.setAudioJsonUrl(workout116I18nBiz.getAudioJsonUrl());
                    detailVO.getAudioList().add(audioDetailVO);
                }
            }
        }
    }

    private Integer getPlaylistId(String exerciseType) {
        if (StringUtils.isBlank(exerciseType)) {
            return null;
        }
        ExerciseType116Enums exerciseTypeEnum = ExerciseType116Enums.getByName(exerciseType);
        if (null == exerciseTypeEnum) {
            return null;
        }
        List<ProjPlaylistAppVO> playlists = projPlaylistService.selectListApp(exerciseTypeEnum.getPlaylistType());
        if (CollectionUtil.isNotEmpty(playlists)) {
            return playlists.get(0).getId();
        }
        return null;
    }


    @Override
    public List<ProjWorkout116EnumCodeListVO> queryAllList(ProjWorkout116RecommendListReq req, List<ExerciseType116Enums> containsTypes) {
        ProjWorkout116ListReq projWorkout116ListReq = getProjWorkout116ListReq(req);
        List<ProjWorkout116ListVO> projWorkout116ListVOS = this.queryList(projWorkout116ListReq, containsTypes);
        //获取所有workout关联的category名称集合
        List<Integer> workoutIdList = projWorkout116ListVOS.stream().map(ProjWorkout116ListVO::getId).collect(Collectors.toList());
        CategoryRelatedResult categoryNameSetMap = this.getWorkoutCategoryNameSetMap(workoutIdList);
        Set<Integer> allExcludeWorkoutSet = getAllFixWorkoutIds();
        if (req.getExcludeProgramWorkout()) allExcludeWorkoutSet.addAll(projProgram116PubService.getAllProgramWokroutIdSet(null));
        return projWorkout116ListVOS.stream().filter(w->!allExcludeWorkoutSet.contains(w.getId()))
                .map(v -> {
                    ProjWorkout116EnumCodeListVO vo = new ProjWorkout116EnumCodeListVO();
                    BeanUtil.copyProperties(v, vo,new CopyOptions().setIgnoreError(true));
                    // 处理基础枚举
                    vo.setDifficultyCode(Optional.ofNullable(DifficultyEnums.getByName(v.getDifficulty()))
                            .map(DifficultyEnums::getCode).orElse(null));
                    vo.setPositionCode(Optional.ofNullable(Position116Enums.getByName(v.getPosition()))
                            .map(Position116Enums::getCode).orElse(null));
                    vo.setGenderCode(Optional.ofNullable(Gender116Enums.getByName(v.getGender()))
                            .map(Gender116Enums::getCode).orElse(null));
                    vo.setExerciseTypeCode(Optional.ofNullable(ExerciseType116Enums.getByName(v.getExerciseType()))
                            .map(ExerciseType116Enums::getCode).orElse(null));
                    // 处理restriction多值枚举
                    Set<Integer> restrictionCodes = Optional.ofNullable(v.getRestriction())
                            .map(r -> Restriction116Enums.getCodeByString(Arrays.stream(MyStringUtil.getSplitWithComa(r))
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList())))
                            .filter(CollUtil::isNotEmpty)
                            .map(HashSet::new)
                            .orElse(null);
                    vo.setRestrictionCodeSet(restrictionCodes);
                    // 获取分类名称集合
                    vo.setCategoryNameSet(categoryNameSetMap.getNameSetMap().get(v.getId()));
                    vo.setCategoryTypeSet(categoryNameSetMap.typeSetMap.get(v.getId()));
                    return vo;
                }
        ).collect(Collectors.toList());
    }

    private void replaceVOWorkoutType(ProjWorkout116EnumCodeListVO vo, BiMap<Integer, Integer> workoutTypeReplaceMap) {
        if (vo == null || MapUtil.isEmpty(workoutTypeReplaceMap) ||
                !workoutTypeReplaceMap.containsValue(vo.getExerciseTypeCode())) {
            return;
        }
        vo.setExerciseTypeCode(workoutTypeReplaceMap.getKey(vo.getExerciseTypeCode()));
    }

    private void replaceReqWorkoutType(ProjWorkout116RecommendListReq req, BiMap<Integer, Integer> workoutTypeReplaceMap) {
        Set<Integer> workoutTypeCodeSet = req.getWorkoutTypeCodeSet();
        if (CollUtil.isNotEmpty(workoutTypeCodeSet) && MapUtil.isNotEmpty(workoutTypeReplaceMap)) {
            Set<Integer> workoutTypeSet = workoutTypeCodeSet.stream()
                  .map(workoutTypeReplaceMap::get)
                  .filter(Objects::nonNull)
                  .collect(Collectors.toSet());
            req.setWorkoutTypeCodeSet(workoutTypeSet);
        }
    }

    /**
     * 获取手组Workout固定到Plan前三天的workoutId集合
     * @return Set<Integer> workoutId集合
     */
    private Set<Integer> getAllFixWorkoutIds() {
        Set<Integer> allWorkoutIdSet = new HashSet<>();
        Map<WorkoutGenerate116ImagePoint, List<Integer>> femaleWorkoutIdMap = oog116Config.getFemaleWorkoutIdMap();
        if (MapUtil.isNotEmpty(femaleWorkoutIdMap)) {
            femaleWorkoutIdMap.values().stream()
                .filter(CollectionUtil::isNotEmpty)
                .forEach(allWorkoutIdSet::addAll);
        }
        Map<WorkoutGenerate116ImagePoint, List<Integer>> maleWorkoutIdMap = oog116Config.getMaleWorkoutIdMap();
        if (MapUtil.isNotEmpty(maleWorkoutIdMap)) {
            maleWorkoutIdMap.values().stream()
                .filter(CollectionUtil::isNotEmpty)
                .forEach(allWorkoutIdSet::addAll);
        }
        return allWorkoutIdSet;
    }


    private ProjWorkout116ListReq getProjWorkout116ListReq(ProjWorkout116RecommendListReq req) {
        ProjWorkout116ListReq projWorkout116ListReq = new ProjWorkout116ListReq();
        Set<Integer> workoutTypeCodeSet = req.getWorkoutTypeCodeSet();
        Set<Integer> categoryTypeCodeSet = req.getCategoryTypeCodeSet();
        Integer difficultyCode = req.getDifficultyCode();
        if (CollUtil.isNotEmpty(workoutTypeCodeSet)) {
            Set<String> workoutTypeNameSet = workoutTypeCodeSet.stream()
                    .map(ExerciseType116Enums::getByCode)
                    .map(ExerciseType116Enums::getName)
                    .collect(Collectors.toSet());
            projWorkout116ListReq.setWorkoutTypeSet(workoutTypeNameSet);
        }
        if (CollUtil.isNotEmpty(categoryTypeCodeSet)) {
            projWorkout116ListReq.setCategoryTypeSet(categoryTypeCodeSet);
        }
        if (difficultyCode != null) {
            String difficultyName = EnumUtil.getBy(DifficultyEnums::getCode,difficultyCode).getName();
            projWorkout116ListReq.setDifficulty(difficultyName);
        }
        projWorkout116ListReq.setNoEnumFieldI18nFlag(true);
        return projWorkout116ListReq;
    }

    @Override
    public CategoryRelatedResult getWorkoutCategoryNameSetMap(List<Integer> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return new CategoryRelatedResult(new HashMap<>(),new HashMap<>());
        }
        //查询workout关联categoryIds
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjWorkout116CategoriesVO> categories = this.baseMapper.queryWorkoutCategories(versionInfoBO, idList);
        if (CollUtil.isEmpty(categories)) {
            return new CategoryRelatedResult(new HashMap<>(),new HashMap<>());
        }
        //处理多语言
        String language = RequestContextUtils.getLanguage();
        // 如果是默认语言，直接返回categoryNames
        if (Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)) {
            Map<Integer, Set<String>> resultMapFinal = new HashMap<>(categories.size());
            Map<Integer, Set<Integer>> typeSetMap = new HashMap<>(categories.size());
            for (ProjWorkout116CategoriesVO vo : categories) {
                Integer workoutId = vo.getId();
                String categoryNamesStr = vo.getCategoryNames();
                String categoryTypesStr = vo.getCategoryTypes();
                if (StringUtils.isNotBlank(categoryNamesStr)) {
                    Set<String> categoryNames = new HashSet<>(StrUtil.split(categoryNamesStr, ","));
                    resultMapFinal.put(workoutId, categoryNames);
                }
                if (StringUtils.isNotBlank(categoryTypesStr)) {
                    Set<String> categoryTypes = new HashSet<>(StrUtil.split(categoryTypesStr, ","));
                    Set<Integer> categoryTypeSet = categoryTypes.stream().map(Integer::valueOf)
                        .collect(Collectors.toSet());
                    typeSetMap.put(workoutId, categoryTypeSet);
                }
            }
            return new CategoryRelatedResult(resultMapFinal, typeSetMap);
        }
        //获取所有的categoryIds
        List<Integer> categoryIds = categories.stream()
            .map(vo -> StrUtil.split(vo.getCategoryIds(), ","))
            .flatMap(List::stream)
            .map(Integer::valueOf)
            .collect(Collectors.toList());
        // 将数据转换为workout ID到category名称集合的映射
        Map<Integer, Set<String>> finalResultMap = new HashMap<>(idList.size());
        Map<Integer, Set<Integer>> typeSetMap = new HashMap<>(idList.size());
        for (ProjWorkout116CategoriesVO vo : categories) {
            Integer workoutId = vo.getId();
            String categoryNamesStr = vo.getCategoryNames();
            // 分割categoryIds和categoryNames，并做语言替换
            List<String> catIds = StrUtil.split(vo.getCategoryIds(), ",");
            List<String> catNames = StrUtil.split(categoryNamesStr, ",");
            Set<String> translatedNames = finalResultMap.getOrDefault(workoutId, new HashSet<>());
            List<ConstantTranslateVo> constantTranslateVos = new ArrayList<>();
           //获取翻译内容
            for (int i = 0; i < catIds.size(); i++) {
                String catName = catNames.get(i);
                ConstantTranslateVo constantTranslateVo = new ConstantTranslateVo(catName);
                constantTranslateVos.add(constantTranslateVo);
            }
            //获取翻译结果
            textTaskI18nPubService.translate(constantTranslateVos, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(language));
            translatedNames.addAll(
                    constantTranslateVos.stream()
                            .map(ConstantTranslateVo::getTarget)
                            .collect(Collectors.toList())
            );
            finalResultMap.put(workoutId, translatedNames);
            String categoryTypesStr = vo.getCategoryTypes();
            if (StringUtils.isNotBlank(categoryTypesStr)) {
                Set<Integer> categoryTypes = typeSetMap.getOrDefault(workoutId, new HashSet<>());
                categoryTypes.addAll(StrUtil.split(categoryTypesStr, ",").stream().map(Integer::valueOf)
                        .collect(Collectors.toSet()));
                typeSetMap.put(workoutId, categoryTypes);
            }
        }
        return new CategoryRelatedResult(finalResultMap, typeSetMap);
    }
    private Map<String, String> translateI18n(List<ProjWorkout116DetailVO> detailList, String lang) {
        List<AppTextCoreI18nModel> textCoreI18nModelList = new ArrayList<>();
        List<AppAudioCoreI18nModel> audioCoreI18nModelList = new ArrayList<>();
        List<ConstantTranslateVo> constantTranslateVoList = new ArrayList<>();
        for (ProjWorkout116DetailVO detailVO : detailList) {
            textCoreI18nModelList.add(detailVO);
            List<String> equipmentList = detailVO.getEquipmentList();
            if (CollectionUtil.isNotEmpty(equipmentList)) {
                for (String equipment : equipmentList) {
                    ConstantTranslateVo constantTranslateVo = new ConstantTranslateVo(equipment);
                    constantTranslateVoList.add(constantTranslateVo);
                }
            }
            if (CollUtil.isNotEmpty(detailVO.getCardEquipment()))
                textCoreI18nModelList.addAll(detailVO.getCardEquipment());
            if (CollUtil.isNotEmpty(detailVO.getEquipmentV3List()))
                textCoreI18nModelList.addAll(detailVO.getEquipmentV3List());
            if (CollUtil.isNotEmpty(detailVO.getUnits())) textCoreI18nModelList.addAll(detailVO.getUnits());
            for (ProjWorkout116UnitDetailVO unit : detailVO.getUnits()) {
                if (CollUtil.isNotEmpty(unit.getVideos())) {
                    audioCoreI18nModelList.addAll(unit.getVideos());
                    textCoreI18nModelList.addAll(unit.getVideos());
                }
            }
        }
        if(!constantTranslateVoList.isEmpty()){
            constantTranslateVoList = new ArrayList<>(new HashSet<>(constantTranslateVoList));
            textCoreI18nModelList.addAll(constantTranslateVoList);
        }
        if(!textCoreI18nModelList.isEmpty()) textTaskI18nPubService.translate(textCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
        if(!audioCoreI18nModelList.isEmpty()) speechTaskI18nPubService.translate(audioCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));

        Map<String, String> equipmentMap = constantTranslateVoList.stream()
                .collect(Collectors.toMap(ConstantTranslateVo::getSource, ConstantTranslateVo::getTarget));
        return equipmentMap;
    }
    @Data
    @AllArgsConstructor
    public static class CategoryRelatedResult {
        private Map<Integer, Set<String>> nameSetMap;
        private Map<Integer, Set<Integer>> typeSetMap;
    }
}
