package com.laien.cmsapp.oog116.response;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog116.enums.Focus116Enums;
import com.laien.common.oog116.enums.Region116Enums;
import com.laien.common.oog116.enums.SupportProp116Enums;
import com.laien.common.oog116.enums.WorkoutDataType116Enums;
import com.laien.common.util.MyStringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

import static com.laien.common.domain.enums.TranslationTaskTypeEnums.MULTIPLE_TEXT;

/**
 * note: Workout116 Detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout116 Detail", description = "Workout116 Detail")
@Accessors(chain = true)
public class ProjWorkout116DetailVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @JsonIgnore
    @ApiModelProperty(value = "male封面图")
    @AbsoluteR2Url
    private String coverImgMaleUrl;

    @JsonIgnore
    @ApiModelProperty(value = "male详情图")
    @AbsoluteR2Url
    private String detailImgMaleUrl;

    @ApiModelProperty(value = "难度Easy，Medium，Hard")
    private String difficulty;

    @ApiModelProperty(value = "Seated/Standing")
    @AppTextTranslateField
    private String position;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    @AppTextTranslateField(type = MULTIPLE_TEXT)
    private String restriction;

    @JsonIgnore
    private Integer restrictionSum;

    @ApiModelProperty(value = "简介")
    private List<String> descriptionList;

    @JsonIgnore
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Boolean subscription = false;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "video的m3u8地址")
    @AbsoluteR2Url
    private String videoUrl;

    @ApiModelProperty(value = "video的2532 m3u8地址")
    @JsonIgnore
    private String video2532Url;

    @JsonIgnore
    private String audioJsonUrl;

    @ApiModelProperty(value = "audio 列表")
    private List<ProjWorkout116AudioDetailVO> audioList;

    @JsonRawValue
    @ApiModelProperty(value = "audio json 列表")
    private String audioJsonList;

    @ApiModelProperty(value = "单元组")
    private List<ProjWorkout116UnitDetailVO> units;

    @ApiModelProperty(value = "dataType")
    private WorkoutDataType116Enums dataType = WorkoutDataType116Enums.ASSEMBLE;

    @ApiModelProperty(value = "性别，Female/Male")
    private String gender;

    @JsonIgnore
    @ApiModelProperty(value = "器械：No equipment、Dumbbell (lightweight)、Resistance band")
    private String equipment;

    @ApiModelProperty(value = "workout卡片用的equipment")
    private List<EquipmentVO> cardEquipment;

    @ApiModelProperty(value = "Exercise Type：Regular、Tai Chi、Dancing")
    private String exerciseType;

    @ApiModelProperty(value = "playlistId")
    private Integer playlistId;

    @JsonIgnore
    @ApiModelProperty(value = "imageId")
    private Integer imageId;

    @ApiModelProperty(value = "equipmentList")
    private List<String> equipmentList;
    @ApiModelProperty(value = "equipmentV3List")
    private List<EquipmentVO> equipmentV3List;


    @ApiModelProperty(value = "身体部位 (多选)",hidden = true)
    @JsonIgnore
    private List<Region116Enums> region;

    @ApiModelProperty(value = "焦点类型 (多选)",hidden = true)
    @JsonIgnore
    private List<Focus116Enums> focus;

    @ApiModelProperty(value = "支撑道具 (多选)",hidden = true)
    @JsonIgnore
    private List<SupportProp116Enums> supportProp;

    public List<String> getDescriptionList() {
        return MyStringUtil.toList(description);
    }

}
