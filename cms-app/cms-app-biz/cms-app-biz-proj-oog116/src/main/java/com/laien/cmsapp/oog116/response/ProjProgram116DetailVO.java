package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ProjProgram116DetailVO {

    @ApiModelProperty(value = "program id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "equipment选项，单选")
    private String equipment;

    @ApiModelProperty(value = "标签")
    private List<String> goals;

    @ApiModelProperty(value = "简介")
    private List<String> descriptionList;

    @ApiModelProperty(value = "教练信息")
    private ProjCoach116DetailVO instructor;

    @ApiModelProperty(value = "课程列表")
    private List<ProjWorkout116DetailV4VO> classList;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图片")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图片")
    private String detailImgUrl;

    @ApiModelProperty(value = "是否收费")
    private Boolean subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

}
