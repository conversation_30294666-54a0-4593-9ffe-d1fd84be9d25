package com.laien.cmsapp.oog116.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjWorkout116GenerateI18n;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanReq;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV3Req;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV4Req;
import com.laien.cmsapp.oog116.requst.Workout116IdListReq;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.IProjTemplate116Service;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateI18nService;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateService;
import com.laien.cmsapp.oog116.service.impl.ProjWorkout116PubServiceImpl;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog116.enums.*;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * app端：workout116 generate
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Slf4j
@Api(tags = "app端：oog116 plan")
@RestController
@RequestMapping(value = {"/{appCode}/workout116Generate", "/oog116/workoutGenerate", "/OOG116/workoutGenerate"})
public class ProjWorkout116GenerateController extends ResponseController {

    @Resource
    private IProjTemplate116Service projTemplate116Service;
    @Resource
    private IProjWorkout116GenerateService projWorkout116GenerateService;
    @Resource
    private IProjWorkout116GenerateI18nService projWorkout116GenerateI18nService;
    @Resource
    private  ICoreSpeechTaskI18nPubService speechTaskI18nPubService;
    @Resource
    private  ICoreTextTaskI18nPubService textTaskI18nPubService;


    /**
     * 获取plan（会下载audio json）
     *
     * @param planReq planReq
     * @return workout列表
     */
    @ApiOperation(value = "plan v1", tags = {"oog116"})
    @GetMapping("/v1/plan")
    public ResponseResult<ProjWorkout116GeneratePlanVO> plan(Workout116GeneratePlanReq planReq) {
        return succ(findPlan(planReq, true));
    }

    /**
     * 列表v1（会下载audio json）
     */
    @ApiOperation(value = "列表v1", tags = {"oog116"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjWorkout116DetailVO>> list(Workout116IdListReq idListReq) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projWorkout116GenerateService.query(idListReq.getGenerateWorkoutIdList(), idListReq.getAssembleWorkoutIdList(), versionInfoBO, true));
    }


    /**
     * 获取plan
     *
     * @param planReq planReq
     * @return workout列表
     */
    @ApiOperation(value = "plan v2", tags = {"oog116"})
    @GetMapping("/v2/plan")
    public ResponseResult<ProjWorkout116GeneratePlanVO> planV2(Workout116GeneratePlanV3Req planReq) {
        if (null == planReq.getGender()) {
            planReq.setGender(Gender116Enums.FEMALE);
        }
        if (null == planReq.getCoachGender()) {
            planReq.setCoachGender(Gender116Enums.FEMALE);
        }

        if (CollectionUtil.isEmpty(planReq.getEquipmentSet())) {
            Set<Equipment116Enums> equipmentSet = new HashSet<>();
            equipmentSet.add(Equipment116Enums.NONE);
            planReq.setEquipmentSet(equipmentSet);
        }
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<Restriction116Enums> restrictionEnumsList = getRestriction(planReq);
        ProjWorkout116GeneratePlanVO plan = projTemplate116Service.planV3(planReq, versionInfoBO, restrictionEnumsList);
        if (null != plan) {
            replaceM3u8By2532(planReq.getM3u8Type(), plan);
            handleData(plan);
            handleI18n(plan);
            return succ(plan);
        }
        log.error("oog116 plan v2 not matched, planReq: {}", planReq);
        // 传入的duration没查询到plan，则换其它duration，直到查询到为止
        List<DurationLabel116Enums> durationList = Arrays.stream(DurationLabel116Enums.values()).collect(Collectors.toList());
        durationList.remove(planReq.getDuration());
        for (DurationLabel116Enums duration : durationList) {
            planReq.setDuration(duration);
            plan = projTemplate116Service.planV3(planReq, versionInfoBO, restrictionEnumsList);
            if (null != plan) {
                replaceM3u8By2532(planReq.getM3u8Type(), plan);
                handleData(plan);
                handleI18n(plan);
                return succ(plan);
            }
        }
        log.error("oog116 plan v2 all duration not matched workout, planReq: {}", planReq);
        return null;
    }

    @ApiOperation(value = "plan v4", tags = {"oog116"})
    @GetMapping("/v4/plan")
    public ResponseResult<ProjWorkout116GeneratePlanV4VO> planV4(Workout116GeneratePlanV4Req planReq) {
        Set<Integer> exerciseTypeCodeSet = planReq.getExerciseTypeCodeSet();
        if(CollUtil.isEmpty(exerciseTypeCodeSet)){
            log.error("plan v4 exerciseTypeCodeSet is empty,default values have been used, planReq: {}", planReq);
            exerciseTypeCodeSet = Arrays.stream(ExerciseType116Enums.values()).map(ExerciseType116Enums::getCode).collect(Collectors.toSet());
            planReq.setExerciseTypeCodeSet(exerciseTypeCodeSet);
        }
        if (null == planReq.getGenderCode()) {
            planReq.setGenderCode(Gender116Enums.FEMALE.getCode());
        }
        if (null == planReq.getCoachGenderCode()) {
            planReq.setCoachGenderCode(Gender116Enums.FEMALE.getCode());
        }

        if (CollectionUtil.isEmpty(planReq.getEquipmentCodeSet())) {
            Set<Integer> equipmentSet = new HashSet<>();
            equipmentSet.add(Equipment116Enums.NONE.getCode());
            planReq.setEquipmentCodeSet(equipmentSet);
        }
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjWorkout116GeneratePlanV4VO plan = projTemplate116Service.planV4(planReq, versionInfoBO);
        if (null != plan) {
            return succ(plan);
        }
        log.error("oog116 plan v4 not matched, planReq: {}", planReq);
        // 传入的duration没查询到plan，则换其它duration，直到查询到为止
        List<DurationLabel116Enums> durationList = Arrays.stream(DurationLabel116Enums.values()).collect(Collectors.toList());
        Integer durationCode = planReq.getDurationCode();
        DurationLabel116Enums durationEnum = DurationLabel116Enums.getByCode(durationCode);
        durationList.remove(durationEnum);
        for (DurationLabel116Enums duration : durationList) {
            planReq.setDurationCode(duration.getCode());
            plan = projTemplate116Service.planV4(planReq, versionInfoBO);
            if (null != plan) {
                return succ(plan);
            }
        }
        log.error("oog116 plan v4 all duration not matched workout, planReq: {}", planReq);
        return succ(null);
    }

    @ApiOperation(value = "plan v5", tags = {"oog116"})
    @GetMapping("/v5/plan")
    public ResponseResult<ProjWorkout116GeneratePlanV4VO> planV5(Workout116GeneratePlanV4Req planReq) {
        Set<Integer> exerciseTypeCodeSet = planReq.getExerciseTypeCodeSet();
        if(CollUtil.isEmpty(exerciseTypeCodeSet)){
            log.error("plan v5 exerciseTypeCodeSet is empty,default values have been used, planReq: {}", planReq);
            exerciseTypeCodeSet = Arrays.stream(ExerciseType116Enums.values()).map(ExerciseType116Enums::getCode).collect(Collectors.toSet());
            planReq.setExerciseTypeCodeSet(exerciseTypeCodeSet);
        }
        if (null == planReq.getGenderCode()) {
            planReq.setGenderCode(Gender116Enums.FEMALE.getCode());
        }
        if (null == planReq.getCoachGenderCode()) {
            planReq.setCoachGenderCode(Gender116Enums.FEMALE.getCode());
        }

        if (CollectionUtil.isEmpty(planReq.getEquipmentCodeSet())) {
            Set<Integer> equipmentSet = new HashSet<>();
            equipmentSet.add(Equipment116Enums.NONE.getCode());
            planReq.setEquipmentCodeSet(equipmentSet);
        }
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjWorkout116GeneratePlanV4VO plan = projTemplate116Service.planV5(planReq, versionInfoBO);
        if (null != plan) {
            return succ(plan);
        }
        log.error("oog116 plan v5 not matched, planReq: {}", planReq);
        // 传入的duration没查询到plan，则换其它duration，直到查询到为止
        List<DurationLabel116Enums> durationList = Arrays.stream(DurationLabel116Enums.values()).collect(Collectors.toList());
        Integer durationCode = planReq.getDurationCode();
        DurationLabel116Enums durationEnum = DurationLabel116Enums.getByCode(durationCode);
        durationList.remove(durationEnum);
        for (DurationLabel116Enums duration : durationList) {
            planReq.setDurationCode(duration.getCode());
            plan = projTemplate116Service.planV5(planReq, versionInfoBO);
            if (null != plan) {
                return succ(plan);
            }
        }
        log.error("oog116 plan v5 all duration not matched workout, planReq: {}", planReq);
        return succ(null);
    }

    @ApiOperation(value = "获取用于替换Plan中Workout的配置", tags = {"oog116"})
    @GetMapping("/replacement/config")
    public ResponseResult<List<ProjWorkout116GenerateConfigVO>> listReplaceConfig() {

        return succ(projWorkout116GenerateService.listReplacementConfig());
    }


    @ApiOperation(value = "列表v4", tags = {"oog116"})
    @GetMapping("/v4/list")
    public ResponseResult<List<ProjWorkout116DetailV4VO>> listV4(Workout116IdListReq idListReq, Integer m3u8Type) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projWorkout116GenerateService.queryV4(idListReq.getGenerateWorkoutIdList(), idListReq.getAssembleWorkoutIdList(), versionInfoBO, m3u8Type));
    }

    private void replaceM3u8By2532(Integer m3u8Type, ProjWorkout116GeneratePlanVO plan) {
        m3u8Type = m3u8Type == null ? GlobalConstant.ONE : m3u8Type;
        if (m3u8Type != GlobalConstant.TWO && plan != null) {
            for (Stage116VO stage116VO : plan.getStageList()) {
                for (ProjWorkout116DetailVO projWorkout116DetailVO : stage116VO.getWorkoutList()) {
                    extracted2532(m3u8Type, projWorkout116DetailVO);
                }
            }
        }
    }

    private void extracted2532(Integer m3u8Type, ProjWorkout116DetailVO projWorkout116DetailVO) {
        m3u8Type = m3u8Type == null ? GlobalConstant.ONE : m3u8Type;
        if (m3u8Type != GlobalConstant.TWO && projWorkout116DetailVO != null) {
            Optional.ofNullable(projWorkout116DetailVO.getVideo2532Url()).filter(StrUtil::isNotBlank).ifPresent(projWorkout116DetailVO::setVideoUrl);
            for (ProjWorkout116UnitDetailVO unit : projWorkout116DetailVO.getUnits()) {
                for (ResVideo116VO video : unit.getVideos()) {
                    Optional.ofNullable(video.getVideo2532Url()).filter(StrUtil::isNotBlank).ifPresent(video::setVideoUrl);
                }
            }
        }
    }

    /**
     * 处理数据
     *
     * @param plan
     */
    private void handleData(ProjWorkout116GeneratePlanVO plan) {
        List<Stage116VO> stageList = plan.getStageList();
        for (Stage116VO stage116VO : stageList) {
            List<ProjWorkout116DetailVO> detailList = stage116VO.getWorkoutList();
            for (ProjWorkout116DetailVO detailVO : detailList) {
                if (WorkoutDataType116Enums.GENERATE == detailVO.getDataType()) {

                    if (Objects.equals(detailVO.getRestriction(), ProjWorkout116PubServiceImpl.NONE)) {
                        detailVO.setRestriction(ProjWorkout116PubServiceImpl.ALL_BODY_PARTS);
                    }
                    // 添加默认英语
                    List<ProjWorkout116AudioDetailVO> audioList = new ArrayList<>();
                    ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
                    audioDetailVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
                    audioDetailVO.setAudioJsonUrl(detailVO.getAudioJsonUrl());
                    audioList.add(audioDetailVO);
                    detailVO.setAudioList(audioList);


                    String equipment = detailVO.getEquipment();
                    List<Equipment116Enums> equipmentList = Equipment116Enums.getEquipmentList(equipment);
                    if (CollectionUtil.isEmpty(equipmentList)) {
                        log.error("card name is empty, workoutId:{}, equipment:{}", detailVO.getId(), equipment);
                    } else {
                        if (equipmentList.size() > 1) {
                            equipmentList.remove(Equipment116Enums.NONE);
                        }
                        List<EquipmentVO> equipmentVoList = new ArrayList<>(equipmentList.size());
                        for (Equipment116Enums equipmentEnums : equipmentList) {
                            equipmentVoList.add(new EquipmentVO(equipmentEnums.getCardName(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                        }
                        detailVO.setCardEquipment(equipmentVoList);
                    }

                    List<String> myEquipmentList = new ArrayList<>();
                    for (ProjWorkout116UnitDetailVO unit : detailVO.getUnits()) {
                        List<ResVideo116VO> videos = unit.getVideos();
                        if (videos != null) {
                            for (ResVideo116VO video : videos) {
                                if (Objects.equals(video.getPosition(), Position116Enums.SEATED.getName())) {
                                    myEquipmentList.add(ProjWorkout116PubServiceImpl.CHAIR);
                                    break;
                                }
                            }
                        }
                    }
                    detailVO.setEquipmentList(myEquipmentList);

                    // setEquipmentV3List
                    List<EquipmentVO> equipmentVoList = new ArrayList<>();
                    if (StringUtils.isNotBlank(equipment)) {
                        List<Equipment116Enums> equipmentEnumList = Equipment116Enums.getEquipmentList(equipment);
                        if (CollectionUtil.isNotEmpty(equipmentEnumList)) {
                            // 排查掉NONE
                            equipmentEnumList.remove(Equipment116Enums.NONE);
                            for (Equipment116Enums equipmentEnums : equipmentEnumList) {
                                equipmentVoList.add(new EquipmentVO(equipmentEnums.getCardName(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(myEquipmentList)) {
                        // 不是空就肯定是chair
                        equipmentVoList.add(new EquipmentVO(myEquipmentList.get(0), null, "cms/video116/img/chair.png"));
                    }
                    detailVO.setEquipmentV3List(equipmentVoList);

                }
            }
        }
    }

    private void handleI18n(ProjWorkout116GeneratePlanVO plan) {
        //  多语言处理，workout 不管传的什么语言，audioList都要全量返回，video 按指定语言返回
        String lang = RequestContextUtils.getLanguage();
        List<Stage116VO> stageList = plan.getStageList();
        List<Integer> workoutIdList = new ArrayList<>();
        for (Stage116VO stage116VO : stageList) {
            List<ProjWorkout116DetailVO> detailList = stage116VO.getWorkoutList();
            for (ProjWorkout116DetailVO detailVO : detailList) {
                if (WorkoutDataType116Enums.GENERATE == detailVO.getDataType()) workoutIdList.add(detailVO.getId());
            }
        }
        // 查询翻译
        //将所有内容统一翻译
        Map<String, String> equipmentMap = translateI18n(stageList, lang);
        Map<Integer, List<ProjWorkout116GenerateI18n>> workoutBizI18nMap = projWorkout116GenerateI18nService.getAllLanguageListByIds(workoutIdList);
        for (Stage116VO stage116VO : stageList) {
            List<ProjWorkout116DetailVO> detailList = stage116VO.getWorkoutList();
            for (ProjWorkout116DetailVO detailVO : detailList) {
                if (WorkoutDataType116Enums.ASSEMBLE == detailVO.getDataType()) {
                    continue;
                }
                Integer workoutId = detailVO.getId();
                List<String> equipmentList = detailVO.getEquipmentList();
                if (CollectionUtil.isNotEmpty(equipmentList)) {
                    List<String> equipmentI18nList = new ArrayList<>();
                    for (String equipment : equipmentList) {
                        equipmentI18nList.add(equipmentMap.get(equipment));
                    }
                    detailVO.setEquipmentList(equipmentI18nList);
                }
                if (workoutBizI18nMap.containsKey(workoutId)) {
                    List<ProjWorkout116GenerateI18n> i18nBizList = workoutBizI18nMap.get(workoutId);
                    for (ProjWorkout116GenerateI18n generateI18nBiz : i18nBizList) {
                        // 添加其他语言音频
                        String audioJsonUrl = generateI18nBiz.getAudioJsonUrl();
                        if (StringUtils.isNotBlank(audioJsonUrl)) {
                            ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
                            audioDetailVO.setLanguage(generateI18nBiz.getLanguage());
                            audioDetailVO.setAudioJsonUrl(generateI18nBiz.getAudioJsonUrl());
                            detailVO.getAudioList().add(audioDetailVO);
                        }
                    }
                }
            }
        }

    }

    private Map<String, String> translateI18n(List<Stage116VO> stageList, String lang) {
        List<AppTextCoreI18nModel> textCoreI18nModelList = new ArrayList<>();
        List<AppAudioCoreI18nModel> audioCoreI18nModelList = new ArrayList<>();
        List<ConstantTranslateVo> constantTranslateVoList = new ArrayList<>();
        for (Stage116VO stage116VO : stageList) {
            List<ProjWorkout116DetailVO> detailList = stage116VO.getWorkoutList();
            for (ProjWorkout116DetailVO detailVO : detailList) {
                if (!Objects.equals(detailVO.getDataType(), WorkoutDataType116Enums.ASSEMBLE)) {
                    textCoreI18nModelList.add(detailVO);
                    List<String> equipmentList = detailVO.getEquipmentList();
                    if (CollectionUtil.isNotEmpty(equipmentList)) {
                        for (String equipment : equipmentList) {
                            ConstantTranslateVo constantTranslateVo = new ConstantTranslateVo(equipment);
                            constantTranslateVoList.add(constantTranslateVo);
                        }
                    }
                    if (CollUtil.isNotEmpty(detailVO.getCardEquipment())) textCoreI18nModelList.addAll(detailVO.getCardEquipment());
                    if (CollUtil.isNotEmpty(detailVO.getEquipmentV3List())) textCoreI18nModelList.addAll(detailVO.getEquipmentV3List());
                    if (CollUtil.isNotEmpty(detailVO.getUnits())) textCoreI18nModelList.addAll(detailVO.getUnits());
                    for (ProjWorkout116UnitDetailVO unit : detailVO.getUnits()) {
                        if (CollUtil.isNotEmpty(unit.getVideos())) {
                            audioCoreI18nModelList.addAll(unit.getVideos());
                            textCoreI18nModelList.addAll(unit.getVideos());
                        }
                    }
                }
            }
        }
        if(!constantTranslateVoList.isEmpty()){
            constantTranslateVoList = new ArrayList<>(new HashSet<>(constantTranslateVoList));
            textCoreI18nModelList.addAll(constantTranslateVoList);
        }
        if(!textCoreI18nModelList.isEmpty()) textTaskI18nPubService.translate(textCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
        if(!audioCoreI18nModelList.isEmpty()) speechTaskI18nPubService.translate(audioCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));

        Map<String, String> equipmentMap = constantTranslateVoList.stream()
                .collect(Collectors.toMap(ConstantTranslateVo::getSource, ConstantTranslateVo::getTarget));
        return equipmentMap;
    }

    /**
     * 列表v2
     */
    @ApiOperation(value = "列表v2", tags = {"oog116"})
    @GetMapping("/v2/list")
    public ResponseResult<List<ProjWorkout116DetailVO>> listV2(Workout116IdListReq idListReq, Integer m3u8Type) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjWorkout116DetailVO> projWorkout116DetailVOS = projWorkout116GenerateService.query(idListReq.getGenerateWorkoutIdList(), idListReq.getAssembleWorkoutIdList(), versionInfoBO, false);
        for (ProjWorkout116DetailVO projWorkout116DetailVO : projWorkout116DetailVOS) {
            extracted2532(m3u8Type, projWorkout116DetailVO);
        }
        return succ(projWorkout116DetailVOS);
    }

    /**
     * 列表v3
     */
    @ApiOperation(value = "列表v3", tags = {"oog116"})
    @GetMapping("/v3/list")
    public ResponseResult<List<ProjWorkout116DetailVO>> listV3(Workout116IdListReq idListReq, Integer m3u8Type) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjWorkout116DetailVO> projWorkout116DetailVOS = projWorkout116GenerateService.queryV3(idListReq.getGenerateWorkoutIdList(), idListReq.getAssembleWorkoutIdList(), versionInfoBO);
        for (ProjWorkout116DetailVO projWorkout116DetailVO : projWorkout116DetailVOS) {
            extracted2532(m3u8Type, projWorkout116DetailVO);
        }
        return succ(projWorkout116DetailVOS);
    }


    private ProjWorkout116GeneratePlanVO findPlan(Workout116GeneratePlanReq planReq, boolean downloadJson) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<Restriction116Enums> restrictionEnumsList = getRestriction(planReq);
        ProjWorkout116GeneratePlanVO plan = projTemplate116Service.plan(planReq, versionInfoBO, downloadJson, restrictionEnumsList);
        if (null != plan) {
            return plan;
        }
        log.error("v1 interface is deprecated, oog116 plan not matched, planReq: {}", planReq);
        // 传入的duration没查询到plan，则换其它duration，直到查询到为止
        List<DurationLabel116Enums> durationList = Arrays.stream(DurationLabel116Enums.values()).collect(Collectors.toList());
        durationList.remove(planReq.getDuration());
        for (DurationLabel116Enums duration : durationList) {
            planReq.setDuration(duration);
            plan = projTemplate116Service.plan(planReq, versionInfoBO, downloadJson, restrictionEnumsList);
            if (null != plan) {
                return plan;
            }
        }
        return null;
    }

    private static List<Restriction116Enums> getRestriction(Workout116GeneratePlanReq planReq) {
        List<String> restrictionList = planReq.getRestriction();
        // 改成set，去除重复的restriction
        Set<Restriction116Enums> restrictionEnumsSet = new HashSet<>();
        // 为了兼容restriction入参不对的问题，把枚举改成了String，restriction如果不对，就忽略不对的
        if (CollectionUtil.isNotEmpty(restrictionList)) {
            for (String restriction : restrictionList) {
                Restriction116Enums restriction116Enums = null;
                try {
                    // 将app端传入的错误参数WRISTS和KNEES替换成对应的枚举，WAIST无对应枚举，直接忽略
                    if ("WAIST".equals(restriction)) {
                        continue;
                    } else if ("WRISTS".equals(restriction)) {
                        restriction116Enums = Restriction116Enums.WRIST;
                    } else if ("KNEES".equals(restriction)) {
                        restriction116Enums = Restriction116Enums.KNEE;
                    } else {
                        restriction116Enums = Restriction116Enums.valueOf(restriction);
                    }
                } catch (Exception e) {
                    log.error("restriction illegal, value is {}, the parameter has been ignored", restriction);
                }
                if (null != restriction116Enums) {
                    restrictionEnumsSet.add(restriction116Enums);
                }
            }
        }
        return new ArrayList<>(restrictionEnumsSet);
    }

}
