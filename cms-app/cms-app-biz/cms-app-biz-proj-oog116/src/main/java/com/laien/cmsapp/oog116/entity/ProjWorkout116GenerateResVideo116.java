package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_workout116_generate和res_video116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116GenerateResVideo116对象", description="proj_workout116_generate和res_video116")
public class ProjWorkout116GenerateResVideo116 extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_template116_id")
    private Integer projTemplate116Id;

    @ApiModelProperty(value = "proj_template116_task_id")
    private Integer projTemplate116TaskId;

    @ApiModelProperty(value = "proj_workout116_id")
    private Integer projWorkout116GenerateId;

    @ApiModelProperty(value = "proj_template116_rule_id")
    private Integer projTemplate116RuleId;

    @ApiModelProperty(value = "res_video116_id")
    private Integer resVideo116Id;


}
