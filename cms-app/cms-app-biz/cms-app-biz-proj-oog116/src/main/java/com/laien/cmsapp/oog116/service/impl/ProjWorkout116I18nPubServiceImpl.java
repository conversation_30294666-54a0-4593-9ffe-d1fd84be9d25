package com.laien.cmsapp.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ProjWorkout116I18nPub;
import com.laien.cmsapp.oog116.mapper.ProjWorkout116I18nPubMapper;
import com.laien.cmsapp.oog116.service.IProjWorkout116I18nPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_category116多语言表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Service
public class ProjWorkout116I18nPubServiceImpl extends ServiceImpl<ProjWorkout116I18nPubMapper, ProjWorkout116I18nPub> implements IProjWorkout116I18nPubService {

    @Override
    public Map<Integer, ProjWorkout116I18nPub> getByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<ProjWorkout116I18nPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkout116I18nPub::getLanguage, RequestContextUtils.getLanguage())
                .eq(ProjWorkout116I18nPub::getVersion, RequestContextAppUtils.getPublishCurrentVersionInfo().getCurrentVersion())
                .in(ProjWorkout116I18nPub::getId, ids);
        return this.list(queryWrapper).stream().collect(Collectors.toMap(BaseModel::getId, o -> o));
    }

    @Override
    public Map<Integer, List<ProjWorkout116I18nPub>> getAllLanguageListByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<ProjWorkout116I18nPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjWorkout116I18nPub::getVersion, RequestContextAppUtils.getPublishCurrentVersionInfo().getCurrentVersion())
                .in(ProjWorkout116I18nPub::getId, ids);
        return this.list(queryWrapper).stream().collect(Collectors.groupingBy(ProjWorkout116I18nPub::getId));
    }
}
