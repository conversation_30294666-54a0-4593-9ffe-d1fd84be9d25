package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 教练表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjCoach116", description="ProjCoach116表")
public class ProjCoach116 extends BaseModel {

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "coverImgUrl")
    private String coverImgUrl;

    @ApiModelProperty(value = "introduction")
    private String introduction;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;

}
