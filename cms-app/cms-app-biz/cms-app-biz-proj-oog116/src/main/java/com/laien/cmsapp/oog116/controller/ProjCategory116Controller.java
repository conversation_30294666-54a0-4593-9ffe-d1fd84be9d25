package com.laien.cmsapp.oog116.controller;

import com.laien.cmsapp.oog116.requst.CategoryListReq;
import com.laien.cmsapp.oog116.response.ProjCategory116V2VO;
import com.laien.cmsapp.oog116.response.ProjCategory116V4VO;
import com.laien.cmsapp.oog116.response.ProjCategory116VO;
import com.laien.cmsapp.oog116.service.IProjCategory116PubService;
import com.laien.common.controller.ResponseController;
import com.laien.common.oog116.enums.Gender116Enums;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * template116 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Slf4j
@Api(value = "/{appCode}/category116", tags = {"app端：category116分类表 前端控制器"})
@RestController
@RequestMapping("/{appCode}/category116")
public class ProjCategory116Controller extends ResponseController {

    @Resource
    private IProjCategory116PubService projCategory116PubService;

    /**
     * 查询分类列表
     *
     * @param appCode appCode
     * @return 分类列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true)
    })
    @ApiOperation(value = "列表v1", notes = "查询分类列表v1", httpMethod = "GET", tags ={"oog116"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjCategory116VO>> list(@PathVariable String appCode) {
        return succ(projCategory116PubService.queryList(ProjWorkout116Controller.EXERCISE_TYPES_V1));
    }

    /**
     * 查询分类列表
     *
     * @param appCode appCode
     * @return 分类列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true)
    })
    @ApiOperation(value = "列表v2", notes = "查询分类列表v2", httpMethod = "GET", tags ={"oog116"})
    @GetMapping("/v2/list")
    public ResponseResult<ProjCategory116V2VO> listV2(@PathVariable String appCode,
                                                      CategoryListReq req) {
        if (req.getGender() == null) req.setGender(Gender116Enums.FEMALE.getCode());
        return succ(projCategory116PubService.queryListV2(req, ProjWorkout116Controller.EXERCISE_TYPES_V1));
    }

    /**
     * 查询分类列表
     *
     * @param appCode appCode
     * @return 分类列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true)
    })
    @ApiOperation(value = "列表v3", notes = "查询分类列表v3", httpMethod = "GET", tags ={"oog116"})
    @GetMapping("/v3/list")
    public ResponseResult<ProjCategory116V2VO> listV3(@PathVariable String appCode,
                                                      CategoryListReq req) {
        if (req.getGender() == null) {
            req.setGender(Gender116Enums.FEMALE.getCode());
            log.error("/category116/v3/list: gender is null,use female to search");
        }
        return succ(projCategory116PubService.queryListV2(req,ProjWorkout116Controller.EXERCISE_TYPES_V2));
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true)
    })
    @ApiOperation(value = "列表v4", notes = "查询分类列表v4", httpMethod = "GET", tags ={"oog116"})
    @GetMapping("/v4/list")
    public ResponseResult<ProjCategory116V4VO> listV4(@PathVariable String appCode,
                                                      CategoryListReq req) {
        if (req.getGender() == null) {
            req.setGender(Gender116Enums.FEMALE.getCode());
            log.error("/category116/v4/list: gender is null,use female to search");
        }
        return succ(projCategory116PubService.queryListV4(req,ProjWorkout116Controller.EXERCISE_TYPES_V3));
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "appCode", value = "appCode", required = true)
    })
    @ApiOperation(value = "列表v5", notes = "查询分类列表v5", httpMethod = "GET", tags ={"oog116"})
    @GetMapping("/v5/list")
    public ResponseResult<ProjCategory116V4VO> listV5(@PathVariable String appCode,
                                                      CategoryListReq req) {
        if (req.getGender() == null) {
            req.setGender(Gender116Enums.FEMALE.getCode());
            log.error("/category116/v5/list: gender is null,use female to search");
        }
        /*Set<Integer> containsExerciseCodeSet = req.getContainsExerciseCodeSet();
        log.error("/category116/v5/list: containsExerciseCodeSet is null");
        BizExceptionUtil.throwIf(CollUtil.isEmpty(containsExerciseCodeSet),"workoutTypeCodeSet can not be empty");
        List<ExerciseType116Enums> containsTypes = containsExerciseCodeSet.stream().map(ExerciseType116Enums::getByCode).filter(Objects::nonNull).collect(Collectors.toList());
        */
        return succ(projCategory116PubService.queryListV4(req,ProjWorkout116Controller.EXERCISE_TYPES_V4));
    }

}
