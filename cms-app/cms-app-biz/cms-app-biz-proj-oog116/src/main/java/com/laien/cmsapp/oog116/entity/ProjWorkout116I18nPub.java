package com.laien.cmsapp.oog116.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_category116多语言表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116I18nPub对象", description="proj_category116多语言表")
public class ProjWorkout116I18nPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
