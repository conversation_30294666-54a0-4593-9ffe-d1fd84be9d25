package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ProjProgram116ListVO {

    @ApiModelProperty(value = "program id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "equipment选项，单选")
    private String equipment;

    @ApiModelProperty(value = "goal选项，可多选")
    private List<String> goals;

    @ApiModelProperty(value = "该program下面workout的数量")
    private Integer workoutNum;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图片")
    private String coverImgUrl;

    @ApiModelProperty(value = "是否收费")
    private Boolean subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

}
