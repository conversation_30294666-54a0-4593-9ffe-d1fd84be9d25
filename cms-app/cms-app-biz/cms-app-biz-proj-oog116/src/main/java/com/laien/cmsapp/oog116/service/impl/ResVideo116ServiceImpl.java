package com.laien.cmsapp.oog116.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ResVideo116;
import com.laien.cmsapp.oog116.mapper.ResVideo116Mapper;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.oog116.service.IResVideo116Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Validator;

/**
 * <p>
 * 116 video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ResVideo116ServiceImpl extends ServiceImpl<ResVideo116Mapper, ResVideo116> implements IResVideo116Service {

    @Resource
    private FileService fileService;

    @Resource
    private Validator validator;


}
