package com.laien.cmsapp.oog116.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Generate;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanReq;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV3Req;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV4Req;
import com.laien.cmsapp.oog116.response.ProjWorkout116DetailV4VO;
import com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO;
import com.laien.cmsapp.oog116.response.ProjWorkout116GenerateConfigVO;
import com.laien.common.oog116.enums.Equipment116Enums;
import com.laien.common.oog116.enums.Restriction116Enums;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 116生成的workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjWorkout116GenerateService extends IService<ProjWorkout116Generate> {

    List<ProjWorkout116DetailVO> query(Workout116GeneratePlanReq planReq,
                                       Integer templateId,
                                       ProjPublishCurrentVersionInfoBO versionInfoBO,
                                       boolean downloadJson, List<Restriction116Enums> restrictionList);

    List<ProjWorkout116DetailVO> query(Workout116GeneratePlanV3Req planReq,
                                       Integer templateId,
                                       ProjPublishCurrentVersionInfoBO versionInfoBO,
                                       List<Restriction116Enums> restrictionList,
                                       Set<Equipment116Enums> equipmentSet);

    List<ProjWorkout116DetailVO> query(List<Integer> generateWorkoutIdList,
                                       List<Integer> assembleWorkoutIdList,
                                       ProjPublishCurrentVersionInfoBO versionInfoBO,
                                       boolean downloadJson);

    List<ProjWorkout116DetailVO> queryV3(List<Integer> generateWorkoutIdList,
                                       List<Integer> assembleWorkoutIdList,
                                       ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<ProjWorkout116DetailV4VO> queryV4(List<Integer> generateWorkoutIdList,
                                           List<Integer> assembleWorkoutIdList,
                                           ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type);

    List<ProjWorkout116DetailV4VO> queryV4(Workout116GeneratePlanV4Req planReq, List<Integer> templateIds, ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<ProjWorkout116GenerateConfigVO> listReplacementConfig();
}
