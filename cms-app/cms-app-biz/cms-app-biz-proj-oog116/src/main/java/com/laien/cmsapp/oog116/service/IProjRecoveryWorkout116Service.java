package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Pub;
import com.laien.cmsapp.oog116.requst.ProjRecoveryWorkout116ListReq;
import com.laien.cmsapp.oog116.response.ProjRecoveryWorkout116ListVO;

import java.util.List;

/**
 * <p>
 * Recovery workout116 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
public interface IProjRecoveryWorkout116Service extends IService<ProjWorkout116Pub> {

    /**
     * workout 列表查询
     *
     * @param workout116ListReq workout116ListReq
     * @return list
     */
    List<ProjRecoveryWorkout116ListVO> list(ProjRecoveryWorkout116ListReq req);

    ProjRecoveryWorkout116ListVO convertToWorkoutListVO(ProjWorkout116Pub workout);
}
