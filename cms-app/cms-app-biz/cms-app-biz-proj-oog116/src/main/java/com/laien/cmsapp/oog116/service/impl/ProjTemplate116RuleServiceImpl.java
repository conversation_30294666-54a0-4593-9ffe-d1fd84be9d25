package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ProjTemplate116Rule;
import com.laien.cmsapp.oog116.mapper.ProjTemplate116RuleMapper;
import com.laien.cmsapp.oog116.service.IProjTemplate116RuleService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj_template116_rule 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class ProjTemplate116RuleServiceImpl extends
        ServiceImpl<ProjTemplate116RuleMapper, ProjTemplate116Rule>
        implements IProjTemplate116RuleService {

    @Override
    public List<ProjTemplate116Rule> find(Set<Integer> idSet) {
        if (CollUtil.isEmpty(idSet)) {
            return new ArrayList<>();
        }
        return baseMapper.find(idSet);
    }
}
