package com.laien.cmsapp.oog116.requst;

import com.laien.common.oog116.enums.Equipment116Enums;
import com.laien.common.oog116.enums.Gender116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * note: ProjTemplate116PlanReq
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "oog116 template plan req", description = "oog116 template plan req")
public class Workout116GeneratePlanV3Req extends Workout116GeneratePlanReq{


    @ApiModelProperty(value = "gender")
    private Gender116Enums gender;

    @ApiModelProperty(value = "m3u8Type 传1 代表查询2532 m3u8,传2 代表查询 dynamic m3u8, 默认按1的逻辑")
    private Integer m3u8Type;

    @ApiModelProperty(value = "equipmentSet")
    private Set<Equipment116Enums> equipmentSet;

    @ApiModelProperty(value = "coach gender")
    private Gender116Enums coachGender;

}
