package com.laien.cmsapp.oog116.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ProjRecoveryCategory116ProjWorkout116Pub;
import com.laien.cmsapp.oog116.mapper.ProjRecoveryCategory116ProjWorkout116PubMapper;
import com.laien.cmsapp.oog116.service.IProjRecoveryCategory116ProjWorkout116Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Recovery Category116 与 Workout116 关联表 服务实现类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/07/14
 */
@Slf4j
@Service
public class ProjRecoveryCategory116ProjWorkout116ServiceImpl
        extends ServiceImpl<ProjRecoveryCategory116ProjWorkout116PubMapper, ProjRecoveryCategory116ProjWorkout116Pub>
        implements IProjRecoveryCategory116ProjWorkout116Service {

}
