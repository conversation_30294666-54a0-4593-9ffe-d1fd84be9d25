package com.laien.cmsapp.oog116.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * proj_workout116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjWorkout116 plan对象", description="ProjWorkout116 plan对象")
public class ProjWorkout116GeneratePlanV4VO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "plan preName")
    private String preName;

    @ApiModelProperty(value = "plan lastName")
    private String lastName;

    /**
     * workout列表
     */
    @ApiModelProperty(value = "workout list")
    private List<ProjWorkout116DetailV4VO> workoutList;

}
