package com.laien.cmsapp.oog116.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog116.enums.WorkoutDataType116Enums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Workout116 list(枚举code)
 *
 * <AUTHOR>
 * @since  2025/03/11
 */
@Data
@ApiModel(value = "Workout116 list(枚举code)", description = "Workout116 list(枚举code)")
public class ProjWorkout116EnumCodeListVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "难度： 10-Easy，11-Medium，12-Hard")
    private Integer difficultyCode;

    @ApiModelProperty(value = "position：10-Standing，11-Seated，12-Both")
    private Integer positionCode;

    @ApiModelProperty(value = "限制，多选用英文逗号分隔，Shoulder, Back, Wrist, Knee, Ankle, Hip;")
    private Set<Integer> restrictionCodeSet;

    @ApiModelProperty(value = "gender：10-Female，11-Male,12-Both")
    private Integer genderCode;

    @ApiModelProperty(value = "Exercise Type：10-Chair Yoga,11-Tai Chi,12-Dancing," +
            "13-Gentle Cardio,14-Walking,15-Dumbbell (lightweight),16-Resistance Band")
    private Integer exerciseTypeCode;

    @ApiModelProperty(value = "dataType:10-GENERATE 11-ASSEMBLE")
    private Integer dataTypeCode = WorkoutDataType116Enums.ASSEMBLE.getCode();

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "时长：毫秒")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Boolean subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "categoryNameSet")
    private Set<String> categoryNameSet;

    @ApiModelProperty(value = "categoryTypeSet")
    private Set<Integer> categoryTypeSet;



}
