package com.laien.cmsapp.oog116.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog116.entity.ProjSound116;
import com.laien.cmsapp.oog116.requst.ProjSound116Req;
import com.laien.cmsapp.oog116.response.ProjSound116VO;

import java.util.List;


public interface IProjSound116Service extends IService<ProjSound116> {
    /**
     * 按照类型，子类型 , 性别查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    List<ProjSound116VO> selectSoundList(ProjSound116Req soundReq);

}