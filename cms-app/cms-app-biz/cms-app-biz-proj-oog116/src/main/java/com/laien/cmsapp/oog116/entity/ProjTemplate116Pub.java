package com.laien.cmsapp.oog116.entity;

import com.laien.common.oog116.enums.Template116TypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * template116
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjTemplate116对象", description="template116")
public class ProjTemplate116Pub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "时长区间")
    private String durationRange;

    @ApiModelProperty(value = "模板类型")
    private Template116TypeEnums templateType;

    @ApiModelProperty(value = "语言列表，多个用英文逗号分隔")
    private String languages;

    @ApiModelProperty(value = "生成多少天的")
    private Integer day;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
