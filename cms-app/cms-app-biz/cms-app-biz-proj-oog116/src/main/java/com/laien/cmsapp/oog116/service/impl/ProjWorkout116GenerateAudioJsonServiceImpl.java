package com.laien.cmsapp.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog116.entity.ProjWorkout116GenerateAudioJson;
import com.laien.cmsapp.oog116.mapper.ProjWorkout116GenerateAudioJsonMapper;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateAudioJsonService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 116生成的workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Service
public class ProjWorkout116GenerateAudioJsonServiceImpl extends ServiceImpl<ProjWorkout116GenerateAudioJsonMapper, ProjWorkout116GenerateAudioJson> implements IProjWorkout116GenerateAudioJsonService {

    @Override
    public String findAudioJson(Integer projWorkout116GenerateId) {
        LambdaQueryWrapper<ProjWorkout116GenerateAudioJson> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWorkout116GenerateAudioJson::getProjWorkout116GenerateId, projWorkout116GenerateId);
        return baseMapper.selectOne(wrapper).getAudioJson();
    }
}
