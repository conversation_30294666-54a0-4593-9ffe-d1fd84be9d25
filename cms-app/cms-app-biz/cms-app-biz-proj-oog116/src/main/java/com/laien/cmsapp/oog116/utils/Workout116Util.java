package com.laien.cmsapp.oog116.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.laien.cmsapp.oog116.entity.ProjTemplate116Rule;
import com.laien.cmsapp.oog116.entity.ProjWorkout116GenerateI18n;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.IProjWorkout116GenerateI18nService;
import com.laien.cmsapp.response.ProjPlaylistAppVO;
import com.laien.cmsapp.service.IProjPlaylistService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog116.enums.*;
import com.laien.common.util.MyStringUtil;
import com.laien.common.util.RequestContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.Equipment116Enums.CHAIR;
import static com.laien.common.oog116.enums.Position116Enums.SEATED;
import static com.laien.common.oog116.enums.Position116Enums.getByName;

/**
 * <AUTHOR>
 * @since 2025/3/3
 */
@Slf4j
public class Workout116Util {

    private final static List<Integer> VideoSliceCodes = Lists.newArrayList(ExerciseType116Enums.TAI_CHI.getCode(), ExerciseType116Enums.GENTLE_CHAIR_YOGA.getCode(),
            ExerciseType116Enums.RECOVERY.getCode(), ExerciseType116Enums.DUMBBELL_MIDWEIGHT.getCode());

    public static List<ProjWorkout116DetailV4VO> dataConversion(List<ResVideo116VO> videoList,
                                                                List<ProjWorkout116DetailVO> workoutList,
                                                                List<ExerciseType116Enums> exerciseTypeList,
                                                                List<ProjTemplate116Rule> ruleList, Integer m3u8Type,
                                                                WorkoutDataType116Enums dataType) {

        List<ResVideo116V4VO> videoVOList = createVideoV4VO(videoList);
        Map<Integer, List<ResVideo116V4VO>> videoMap = videoVOList.stream().collect(
                Collectors.groupingBy(ResVideo116V4VO::getWorkoutId));
        List<ProjWorkout116DetailV4VO> detailList = new ArrayList<>();
        Map<String, List<ProjPlaylistAppVO>> playlistMap = new HashMap<>();
        exerciseTypeList.forEach(item -> {
            IProjPlaylistService projPlaylistService = SpringUtil.getBean(IProjPlaylistService.class);
            List<ProjPlaylistAppVO> playlistVOList = projPlaylistService.selectListApp(item.getPlaylistType());
            playlistMap.put(item.getName(), playlistVOList);
        });
        workoutList.forEach(detail -> {
            String exerciseType = detail.getExerciseType();
            Integer restrictionSum = detail.getRestrictionSum();
            List<Restriction116Enums> restrictionEnumList = Restriction116Enums.getRestriction(restrictionSum);
            Set<Integer> restrictionCodeEnumSet = null;
            if (CollectionUtil.isNotEmpty(restrictionEnumList)) {
                restrictionCodeEnumSet = restrictionEnumList.stream().map(Restriction116Enums::getCode).collect(Collectors.toSet());
            }
            List<ResVideo116V4VO> workoutVideoList = videoMap.get(detail.getId());
            ProjWorkout116DetailV4VO workoutDetail = new ProjWorkout116DetailV4VO();

            BeanUtils.copyProperties(detail, workoutDetail);
            String difficulty = detail.getDifficulty();
            String position = detail.getPosition();
            String gender = detail.getGender();
            DifficultyEnums difficultyEnum = DifficultyEnums.getByName(difficulty);
            if (null != difficultyEnum) {
                workoutDetail.setDifficultyCode(difficultyEnum.getCode());
            }
            Position116Enums positionEnum = getByName(position);
            if (null != positionEnum) {
                workoutDetail.setPositionCode(positionEnum.getCode());
            }
            Gender116Enums genderEnum = Gender116Enums.getByName(gender);
            if (null != genderEnum) {
                workoutDetail.setGenderCode(genderEnum.getCode());
            }

            List<ProjPlaylistAppVO> playlistVOList = playlistMap.get(exerciseType);
            if (CollUtil.isEmpty(playlistVOList)) {
                log.error("oog116 playlist not matched, workoutId:{}, exerciseType:{}", workoutDetail.getId(), exerciseType);
            } else {
                workoutDetail.setPlaylistId(playlistVOList.get(GlobalConstant.ZERO).getId());
            }

            List<EquipmentV4VO> equipmentVOList = getEquipmentList(detail, workoutVideoList);
            List<EquipmentV4VO> cardEquipmentList = getCardEquipmentList(detail);
            // 添加默认英语
            List<ProjWorkout116AudioDetailVO> audioList = new ArrayList<>();
            ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
            audioDetailVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
            audioDetailVO.setAudioJsonUrl(detail.getAudioJsonUrl());
            audioList.add(audioDetailVO);
            ExerciseType116Enums exerciseTypeEnums = ExerciseType116Enums.getByName(exerciseType);
            if (null != exerciseTypeEnums) {
                workoutDetail.setExerciseTypeCode(exerciseTypeEnums.getCode());
            }

            workoutDetail.setAudioList(audioList)
                    .setEquipmentList(equipmentVOList)
                    .setCardEquipment(cardEquipmentList)
                    .setRestrictionCodeSet(restrictionCodeEnumSet)
                    .setUnits(getWorkoutUnitsV4(workoutVideoList, ruleList))
                    .setDescriptionList(MyStringUtil.toList(detail.getDescription()))
                    .setDataTypeCode(dataType.getCode());
            tryReplaceM3u8(m3u8Type, workoutDetail);
            detailList.add(workoutDetail);
        });

        return detailList;
    }

    public static void handleWorkout4TaiChi(List<ProjWorkout116DetailV4VO> workoutDetailList) {

        if (CollectionUtil.isEmpty(workoutDetailList)) {
            return;
        }

        workoutDetailList.stream().forEach(workout -> {
            // 只处理太极的, 2025.04.25 新增 GENTLE_CHAIR_YOGA
            if (!VideoSliceCodes.contains(workout.getExerciseTypeCode())) {
                return;
            }

            workout.getUnits().forEach(unit -> {
                List<ResVideo116V4VO> videoList = handleVideo4Circuit(unit.getVideos());
                unit.setVideos(videoList);
            });
        });
    }

    private static List<ResVideo116V4VO> handleVideo4Circuit(List<ResVideo116V4VO> videoList) {

        return videoList.stream().map(video -> {

            if (Objects.isNull(video.getCircuit())) {
                return Collections.singletonList(video);
            }

            List<ResVideo116V4VO> taiChiVideoList = Lists.newArrayList();
            String[] circuitVideoDurations = MyStringUtil.getSplitWithComa(video.getCircuitVideoDuration());
            for (int circuit = 0; circuit < video.getCircuit() ; circuit++) {
                ResVideo116V4VO newVideo = new ResVideo116V4VO();
                BeanUtils.copyProperties(video, newVideo);

                // 避免反复
                newVideo.setCircuit(null);
                newVideo.setVideoPlayIndex(circuit + 1);

                Integer previewDuration = Objects.isNull(video.getGeneratedPreviewDuration()) ? 0 : video.getGeneratedPreviewDuration();
                Integer videoDuration = Objects.isNull(video.getGeneratedVideoDuration()) ? 0 : video.getGeneratedVideoDuration();

                // 第一轮才有preview
                previewDuration = circuit == 0 ? previewDuration : GlobalConstant.ZERO;
                newVideo.setPreviewDuration(previewDuration);

                // 兼容过渡时的数据结构
                videoDuration = computeVideoDuration(circuitVideoDurations, circuit, videoDuration, previewDuration);
                newVideo.setDuration(videoDuration);

                taiChiVideoList.add(newVideo);
            }
            return taiChiVideoList;
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private static Integer computeVideoDuration(String[] circuitVideoDurations, int circuit, Integer generatedVideoDuration, Integer generatedPreviewDuration) {

        // 数组为空或者越界时，说明为老结构，使用generatedVideoDuration
        if (circuitVideoDurations.length <= circuit) {
            return generatedVideoDuration - generatedPreviewDuration;
        }

        String duration = circuitVideoDurations[circuit];
        try {
            return Integer.parseInt(duration);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return generatedVideoDuration - generatedPreviewDuration;
        }
    }


    public static void handleGenerateWorkoutI18n(List<ProjWorkout116DetailV4VO> workoutList) {
        //  多语言处理，workout 不管传的什么语言，audioList都要全量返回，video 按指定语言返回
        String lang = RequestContextUtils.getLanguage();

        List<Integer> workoutIdList = new ArrayList<>();
        for (ProjWorkout116DetailV4VO detailVO : workoutList) {
            if (Objects.equals(WorkoutDataType116Enums.GENERATE.getCode(), detailVO.getDataTypeCode())) {
                workoutIdList.add(detailVO.getId());
            }
        }
        // 查询翻译
        IProjWorkout116GenerateI18nService projWorkout116GenerateI18nService = SpringUtil.getBean(IProjWorkout116GenerateI18nService.class);

        Map<Integer, List<ProjWorkout116GenerateI18n>> workoutBizI18nMap = projWorkout116GenerateI18nService.getAllLanguageListByIds(workoutIdList);
       //翻译内容
        extracted(workoutList, lang);
        for (ProjWorkout116DetailV4VO detailVO : workoutList) {
            if (Objects.equals(WorkoutDataType116Enums.ASSEMBLE.getCode(), detailVO.getDataTypeCode())) {
                continue;
            }
            Integer workoutId = detailVO.getId();
            doHandleImageI18n(detailVO);
            doHandleGenerateWorkoutI18n(detailVO, workoutBizI18nMap, workoutId);
            //翻译后重新处理 instructionList
            if (!Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, lang)){
                for (ProjWorkout116UnitDetailV4VO unit : detailVO.getUnits()) {
                    List<ResVideo116V4VO> videos = unit.getVideos();
                    for (ResVideo116V4VO video : videos) {
                        video.setInstructionList(MyStringUtil.toList(video.getInstructions()));
                    }
                }
            }
        }
    }

    private static void extracted(List<ProjWorkout116DetailV4VO> workoutList, String lang) {
        //创建翻译内容
        ICoreSpeechTaskI18nPubService speechTaskI18nPubService = SpringUtil.getBean(ICoreSpeechTaskI18nPubService.class);
        ICoreTextTaskI18nPubService textTaskI18nPubService = SpringUtil.getBean(ICoreTextTaskI18nPubService.class);
        List<AppTextCoreI18nModel> textCoreI18nModelList = new ArrayList<>();
        List<AppAudioCoreI18nModel> audioCoreI18nModelList = new ArrayList<>();
        textCoreI18nModelList.addAll(workoutList);
        for (ProjWorkout116DetailV4VO detailVO : workoutList) {
            if (!Objects.equals(WorkoutDataType116Enums.ASSEMBLE.getCode(), detailVO.getDataTypeCode())) {
                textCoreI18nModelList.addAll(detailVO.getUnits());
                for (ProjWorkout116UnitDetailV4VO unit : detailVO.getUnits()) {
                    audioCoreI18nModelList.addAll(unit.getVideos());
                }
            }
        }
        textTaskI18nPubService.translate(textCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
        speechTaskI18nPubService.translate(audioCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
    }

    private static void doHandleGenerateWorkoutI18n(ProjWorkout116DetailV4VO detailVO, Map<Integer, List<ProjWorkout116GenerateI18n>> workoutBizI18nMap, Integer workoutId) {
        if (workoutBizI18nMap.containsKey(workoutId)) {
            List<ProjWorkout116GenerateI18n> i18nBizList = workoutBizI18nMap.get(workoutId);
            for (ProjWorkout116GenerateI18n generateI18nBiz : i18nBizList) {
                // 添加其他语言音频
                String audioJsonUrl = generateI18nBiz.getAudioJsonUrl();
                if (StringUtils.isNotBlank(audioJsonUrl)) {
                    ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
                    audioDetailVO.setLanguage(generateI18nBiz.getLanguage());
                    audioDetailVO.setAudioJsonUrl(generateI18nBiz.getAudioJsonUrl());
                    detailVO.getAudioList().add(audioDetailVO);
                }
            }
        }
    }

    private static void doHandleImageI18n(ProjWorkout116DetailV4VO detailVO) {

            String imageDescription = detailVO.getDescription();
            if (StringUtils.isNotBlank(detailVO.getDescription()) && StringUtils.isNotBlank(imageDescription)) {
                detailVO.setDescription(imageDescription);
                detailVO.setDescriptionList(MyStringUtil.toList(imageDescription));
            }

    }


    private static List<EquipmentV4VO> getCardEquipmentList(ProjWorkout116DetailVO detail) {
        String equipment = detail.getEquipment();
        List<Equipment116Enums> equipmentList = Equipment116Enums.getEquipmentList(equipment);
        List<EquipmentV4VO> equipmentVOList = new ArrayList<>();
        if (CollectionUtil.isEmpty(equipmentList)) {
            log.error("card name is empty, workoutId:{}, equipment:{}", detail.getId(), equipment);
        } else {
            if (equipmentList.size() > GlobalConstant.ONE) {
                equipmentList.remove(Equipment116Enums.NONE);
            }
            for (Equipment116Enums equipmentEnums : equipmentList) {
                equipmentVOList.add(new EquipmentV4VO(equipmentEnums.getCode(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
            }
        }
        return equipmentVOList;
    }

    private static List<EquipmentV4VO> getEquipmentList(ProjWorkout116DetailVO detail, List<ResVideo116V4VO> workoutVideoList) {
        List<EquipmentV4VO> equipmentVOList = new ArrayList<>();
        if(CollUtil.isEmpty(workoutVideoList)){
            return equipmentVOList;
        }
        boolean hasChair = false;
        for (ResVideo116V4VO video : workoutVideoList) {
            if (Objects.equals(video.getPositionCode(), SEATED.getCode())) {
                hasChair = true;
                break;
            }
        }
        String equipment = detail.getEquipment();
        if (StringUtils.isNotBlank(equipment)) {
            List<Equipment116Enums> equipmentEnumList = Equipment116Enums.getEquipmentList(equipment);
            if (CollectionUtil.isNotEmpty(equipmentEnumList)) {
                // 排除掉NONE
                equipmentEnumList.remove(Equipment116Enums.NONE);
                if (hasChair) {
                    equipmentEnumList.add(CHAIR);
                }
                for (Equipment116Enums equipmentEnums : equipmentEnumList) {
                    equipmentVOList.add(new EquipmentV4VO(equipmentEnums.getCode(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                }
            }
        }
        return equipmentVOList;
    }

    private static List<ResVideo116V4VO> createVideoV4VO(List<ResVideo116VO> videoList) {
        List<ResVideo116V4VO> videoVOList = new ArrayList<>();
        for (ResVideo116VO video : videoList) {
            Position116Enums positionEnum = getByName(video.getPosition());
            List<String> restrictionNameList = Arrays.stream(MyStringUtil.getSplitWithComa(video.getRestriction())).collect(Collectors.toList());
            Set<Integer> restrictionCodeSet = Restriction116Enums.getCodeByString(restrictionNameList);
            ExerciseType116Enums exerciseTypeEnum = ExerciseType116Enums.getByName(video.getExerciseType());
            Gender116Enums genderEnum = Gender116Enums.getByName(video.getGender());
            ResVideo116V4VO videoVO = new ResVideo116V4VO();
            BeanUtils.copyProperties(video, videoVO);
            if (null != positionEnum) {
                videoVO.setPositionCode(positionEnum.getCode());
            }
            if (null != exerciseTypeEnum) {
                videoVO.setExerciseTypeCode(exerciseTypeEnum.getCode());
            }
            if (null != genderEnum) {
                videoVO.setGenderCode(genderEnum.getCode());
            }
            videoVO.setRestrictionCodeSet(restrictionCodeSet);
            videoVO.setInstructionList(MyStringUtil.toList(video.getInstructions()));
            videoVOList.add(videoVO);
        }
        return videoVOList;
    }

    private static List<ProjWorkout116UnitDetailV4VO> getWorkoutUnitsV4(List<ResVideo116V4VO> workoutVideoList, List<ProjTemplate116Rule> ruleList) {
        if (CollUtil.isEmpty(ruleList)) {
            return getWorkoutUnitsV4(workoutVideoList);
        }

        List<ProjWorkout116UnitDetailV4VO> workoutUnitDetailList = new ArrayList<>();
        Map<Integer, List<ResVideo116V4VO>> workoutVideoMap = workoutVideoList
                .stream()
                .collect(Collectors.groupingBy(ResVideo116V4VO::getRuleId));

        List<ProjTemplate116Rule> ruleListCopy = new ArrayList<>(ruleList);
        for (ProjTemplate116Rule rule : ruleList) {
            if (!workoutVideoMap.containsKey(rule.getId())) {
                ruleListCopy.remove(rule);
            }
        }
        for (ProjTemplate116Rule rule : ruleListCopy) {
            ProjWorkout116UnitDetailV4VO unitDetail = new ProjWorkout116UnitDetailV4VO();
            List<ResVideo116V4VO> unitVideoList = workoutVideoMap.get(rule.getId());
            unitDetail.setRuleId(rule.getId())
                    .setRounds(rule.getRounds())
                    .setVideos(unitVideoList)
                    .setUnitName(rule.getUnitName());
            workoutUnitDetailList.add(unitDetail);
        }
        return workoutUnitDetailList;
    }


    private static List<ProjWorkout116UnitDetailV4VO> getWorkoutUnitsV4(List<ResVideo116V4VO> workoutVideoList) {
        List<ProjWorkout116UnitDetailV4VO> workoutUnitDetailList = new ArrayList<>();
        if (CollUtil.isEmpty(workoutVideoList)) {
            return new ArrayList<>();
        }

        Map<String, List<ResVideo116V4VO>> ruleNameGroup = new HashMap<>();
        List<String> ruleNameList = new ArrayList<>();
        for (ResVideo116V4VO video : workoutVideoList) {
            String unitName = video.getUnitName();
            List<ResVideo116V4VO> videoList = ruleNameGroup.getOrDefault(unitName, new ArrayList<>());
            videoList.add(video);
            ruleNameGroup.put(unitName, videoList);
            if (!ruleNameList.contains(unitName)) {
                ruleNameList.add(unitName);
            }
        }

        for (String ruleName : ruleNameList) {
            ProjWorkout116UnitDetailV4VO unitDetail = new ProjWorkout116UnitDetailV4VO();
            List<ResVideo116V4VO> unitVideoList = ruleNameGroup.get(ruleName);
            ResVideo116V4VO video = unitVideoList.get(0);
            unitDetail.setRuleId(video.getRuleId())
                    .setRounds(video.getRounds())
                    .setVideos(unitVideoList)
                    .setUnitName(ruleName);
            workoutUnitDetailList.add(unitDetail);
        }
        return workoutUnitDetailList;
    }

    private static void tryReplaceM3u8(Integer m3u8Type, ProjWorkout116DetailV4VO detailVO) {
        m3u8Type = m3u8Type == null ? GlobalConstant.ONE : m3u8Type;
        if (m3u8Type != GlobalConstant.TWO && detailVO != null) {
            String workoutVideo2532Url = detailVO.getVideo2532Url();
            if (StrUtil.isNotBlank(workoutVideo2532Url)) {
                detailVO.setVideoUrl(workoutVideo2532Url);
                for (ProjWorkout116UnitDetailV4VO unit : detailVO.getUnits()) {
                    for (ResVideo116V4VO video : unit.getVideos()) {
                        String video2532Url = video.getVideo2532Url();
                        if (StrUtil.isNotBlank(video2532Url)) {
                            video.setVideoUrl(video2532Url);
                        }
                    }
                }
            }
        }
    }

}
