package com.laien.cmsapp.oog116.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjProgram116RelationPub;
import com.laien.cmsapp.oog116.mapper.ProjProgram116RelationPubMapper;
import com.laien.cmsapp.oog116.service.IProjProgram116RelationPubService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/5/20
 */
@Service
public class ProjProgram116RelationPubServiceImpl extends ServiceImpl<ProjProgram116RelationPubMapper, ProjProgram116RelationPub> implements IProjProgram116RelationPubService {

    @Override
    public List<ProjProgram116RelationPub> listProgramRelation(List<Integer> programIds, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        return listByStatusAndVersion(programIds, versionInfoBO);
    }

    private List<ProjProgram116RelationPub> listByStatusAndVersion(Collection<Integer> programIds, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        LambdaQueryWrapper<ProjProgram116RelationPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(programIds), ProjProgram116RelationPub::getProjProgram116Id, programIds);

        queryWrapper.eq(ProjProgram116RelationPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjProgram116RelationPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.orderByAsc(ProjProgram116RelationPub::getId);
        return list(queryWrapper);
    }


}
