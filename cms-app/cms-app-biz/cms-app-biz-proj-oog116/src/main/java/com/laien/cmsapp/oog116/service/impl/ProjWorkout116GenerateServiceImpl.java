package com.laien.cmsapp.oog116.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.FilterWorkoutParamBO;
import com.laien.cmsapp.oog116.config.Oog116Config;
import com.laien.cmsapp.oog116.entity.ProjTemplate116Rule;
import com.laien.cmsapp.oog116.entity.ProjWorkout116Generate;
import com.laien.cmsapp.oog116.entity.ProjWorkout116GenerateI18n;
import com.laien.cmsapp.oog116.mapper.ProjWorkout116GenerateMapper;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanReq;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV3Req;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV4Req;
import com.laien.cmsapp.oog116.response.*;
import com.laien.cmsapp.oog116.service.*;
import com.laien.cmsapp.oog116.utils.Workout116Util;
import com.laien.cmsapp.response.ProjPlaylistAppVO;
import com.laien.cmsapp.service.IProjPlaylistService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreSpeechTaskI18nPubService;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog116.enums.*;
import com.laien.common.util.RequestContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.laien.common.oog116.enums.Equipment116Enums.*;
import static com.laien.common.oog116.enums.ExerciseType116Enums.DANCING;
import static com.laien.common.oog116.enums.Gender116Enums.FEMALE;
import static com.laien.common.oog116.enums.Gender116Enums.MALE;
import static com.laien.common.oog116.enums.Position116Enums.*;
import static com.laien.common.oog116.enums.WorkoutDataType116Enums.GENERATE;


/**
 * <p>
 * 116生成的workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
@Slf4j
public class ProjWorkout116GenerateServiceImpl
        extends ServiceImpl<ProjWorkout116GenerateMapper, ProjWorkout116Generate>
        implements IProjWorkout116GenerateService {


    @Resource
    private IProjTemplate116RuleService projTemplate116RuleService;

    @Resource
    private IProjWorkout116PubService projWorkout116PubService;
    @Resource
    private IProjWorkout116GenerateAudioJsonService projWorkout116GenerateAudioJsonService;
    @Resource
    private IProjPlaylistService projPlaylistService;
    @Resource
    private IProjWorkout116GenerateI18nService projWorkout116GenerateI18nService;
    @Resource
    private Oog116Config oog116Config;
    @Resource
    private  ICoreSpeechTaskI18nPubService speechTaskI18nPubService;
    @Resource
    private ICoreTextTaskI18nPubService textTaskI18nPubService;

    private final String REGULAR_EXERCISE_TYPE = "Regular";


    @Override
    public List<ProjWorkout116DetailVO> query(Workout116GeneratePlanReq planReq,
                                              Integer templateId,
                                              ProjPublishCurrentVersionInfoBO versionInfoBO,
                                              boolean downloadJson,
                                              List<Restriction116Enums> restrictionList) {
        Integer restrictionSum = Restriction116Enums.sum(restrictionList);
        Integer dataVersion = downloadJson ? 0 : 1;
        List<ProjWorkout116DetailVO> workoutList = getWorkoutList(planReq, templateId, restrictionSum, dataVersion);
        if (CollectionUtil.isEmpty(workoutList)) {
            return null;
        }
        List<ResVideo116VO> videoList = baseMapper.queryVideoList(templateId, restrictionSum);
        injectionAttribute(videoList, workoutList, downloadJson);
        return workoutList;
    }

    @Override
    public List<ProjWorkout116DetailVO> query(Workout116GeneratePlanV3Req planReq,
                                              Integer templateId,
                                              ProjPublishCurrentVersionInfoBO versionInfoBO,
                                              List<Restriction116Enums> restrictionList,
                                              Set<Equipment116Enums> equipmentSet) {

        Integer restrictionSum = Restriction116Enums.sum(restrictionList);
        List<ProjWorkout116DetailVO> workoutList = getWorkoutList(planReq, templateId, restrictionSum, equipmentSet);
        if (CollectionUtil.isEmpty(workoutList)) {
            return null;
        }
        List<Integer> workoutIdList = workoutList.stream().map(ProjWorkout116DetailVO::getId).collect(Collectors.toList());
        List<ResVideo116VO> videoList = baseMapper.queryVideoListByIds(workoutIdList);
        injectionAttribute(videoList, workoutList, false);
        return workoutList;
    }

    @Override
    public List<ProjWorkout116DetailV4VO> queryV4(List<Integer> generateWorkoutIdList, List<Integer> assembleWorkoutIdList, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type) {
        Integer dataVersion = 1;
        List<ProjWorkout116DetailV4VO> detailList = new ArrayList<>(30);
        if (CollectionUtil.isNotEmpty(generateWorkoutIdList)) {
            List<ProjWorkout116DetailVO> generateWorkoutList = baseMapper.findByIdList(generateWorkoutIdList, dataVersion);
            Set<ExerciseType116Enums> exerciseTypeSet = new HashSet<>();
            for (ProjWorkout116DetailVO detailVO : generateWorkoutList) {
                ExerciseType116Enums exerciseTypeEnums = ExerciseType116Enums.getByName(detailVO.getExerciseType());
                exerciseTypeSet.add(exerciseTypeEnums);
            }

            if (CollectionUtil.isNotEmpty(generateWorkoutList)) {
                List<ResVideo116VO> videoList = baseMapper.queryVideoListByIds(generateWorkoutIdList);
                Set<Integer> ruleIdSet = videoList.stream().map(ResVideo116VO::getRuleId).collect(Collectors.toSet());
                List<ProjTemplate116Rule> ruleList = projTemplate116RuleService.find(ruleIdSet);
                List<ProjWorkout116DetailV4VO> detailV4List = Workout116Util.dataConversion(videoList, generateWorkoutList, new ArrayList<>(exerciseTypeSet), ruleList, m3u8Type, GENERATE);
                Workout116Util.handleGenerateWorkoutI18n(detailV4List);
                detailList.addAll(detailV4List);
            }
        }
        replaceImage(detailList);
        if (CollectionUtil.isNotEmpty(assembleWorkoutIdList)) {
            List<ProjWorkout116DetailV4VO> assembleWorkoutList = projWorkout116PubService.getDetailByIdList(assembleWorkoutIdList, m3u8Type);
            if (CollectionUtil.isNotEmpty(assembleWorkoutList)) {
                detailList.addAll(assembleWorkoutList);
            }
        }

        // tai chi 类型需要额外处理
        Workout116Util.handleWorkout4TaiChi(detailList);
        return detailList;
    }

    static void replaceImage(List<ProjWorkout116DetailV4VO> detailList) {
        for (ProjWorkout116DetailV4VO detailVO : detailList) {
            if (Gender116Enums.MALE.getCode().equals(detailVO.getGenderCode())) {
                // 如果是男就将CoverImgUrl和DetailImgUrl替换为男性的，前端使用的是coverImgUrl和detailImgUrl
                String coverImgMaleUrl = detailVO.getCoverImgMaleUrl();
                String detailImgMaleUrl = detailVO.getDetailImgMaleUrl();
                if (StrUtil.isNotBlank(coverImgMaleUrl)) {
                    detailVO.setCoverImgUrl(coverImgMaleUrl);
                }
                if (StrUtil.isNotBlank(detailImgMaleUrl)) {
                    detailVO.setDetailImgUrl(detailImgMaleUrl);
                }
            }
        }
    }

    @Override
    public List<ProjWorkout116DetailV4VO> queryV4(Workout116GeneratePlanV4Req planReq, List<Integer> templateIds, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        Set<Integer> restrictionCodeSet = planReq.getRestrictionCodeSet();
        List<Restriction116Enums> restrictionList = new ArrayList<>();
        if (CollUtil.isNotEmpty(restrictionCodeSet)) {
            for (Integer code : restrictionCodeSet) {
                Restriction116Enums restriction116Enum = Restriction116Enums.getByCode(code);
                if (null != restriction116Enum) {
                    restrictionList.add(restriction116Enum);
                }
            }
        }
        Set<Integer> exerciseTypeCodeList = planReq.getExerciseTypeCodeSet();
        List<ExerciseType116Enums> exerciseTypeList = new ArrayList<>();
        if (CollUtil.isNotEmpty(exerciseTypeCodeList)) {
            for (Integer code : exerciseTypeCodeList) {
                ExerciseType116Enums exerciseType = ExerciseType116Enums.getByCode(code);
                if (null == exerciseType) {
                    log.error("exercise code is illegal, has been ignored,code is {}", code);
                } else {
                    exerciseTypeList.add(exerciseType);
                }
            }
        }
        Integer restrictionSum = Restriction116Enums.sum(restrictionList);
        List<ProjWorkout116DetailVO> workoutList = getWorkoutList(planReq, templateIds, restrictionSum, exerciseTypeList);
        if (CollectionUtil.isEmpty(workoutList)) {
            return null;
        }

        for (ProjWorkout116DetailVO detailVO : workoutList) {
            ExerciseType116Enums exerciseType = ExerciseType116Enums.getByName(detailVO.getExerciseType());
            if(Objects.nonNull(exerciseType) && !exerciseTypeList.contains(exerciseType)) {
                exerciseTypeList.add(exerciseType);
            }
        }

        List<Integer> workoutIdList = workoutList.stream().map(ProjWorkout116DetailVO::getId).collect(Collectors.toList());
        List<ResVideo116VO> videoList = baseMapper.queryVideoListByIds(workoutIdList);
        Set<Integer> ruleIdSet = videoList.stream().map(ResVideo116VO::getRuleId).collect(Collectors.toSet());

        List<ProjTemplate116Rule> ruleList = projTemplate116RuleService.find(ruleIdSet);
        List<ProjWorkout116DetailV4VO> workoutDetailList = Workout116Util.dataConversion(videoList, workoutList, exerciseTypeList, ruleList, planReq.getM3u8Type(), GENERATE);

        // tai chi 类型的需要额外处理
        Workout116Util.handleWorkout4TaiChi(workoutDetailList);
        return workoutDetailList;
    }

    @Override
    public List<ProjWorkout116GenerateConfigVO> listReplacementConfig() {

        List<ProjWorkout116GenerateConfigVO> configVOList = Lists.newArrayList();
        Map<WorkoutGenerate116ImagePoint, List<Integer>> femaleWorkoutIdMap = oog116Config.getFemaleWorkoutIdMap();
        if (MapUtils.isNotEmpty(femaleWorkoutIdMap)) {
            femaleWorkoutIdMap.forEach((key, value) -> {
                ProjWorkout116GenerateConfigVO configVO = new ProjWorkout116GenerateConfigVO(key, FEMALE, value);
                configVOList.add(configVO);
            });
        }

        Map<WorkoutGenerate116ImagePoint, List<Integer>> maleWorkoutIdMap = oog116Config.getMaleWorkoutIdMap();
        if (MapUtils.isNotEmpty(maleWorkoutIdMap)) {
            maleWorkoutIdMap.forEach((key, value) -> {
                ProjWorkout116GenerateConfigVO configVO = new ProjWorkout116GenerateConfigVO(key, MALE, value);
                configVOList.add(configVO);
            });
        }

        return configVOList;
    }

    private List<ProjWorkout116DetailVO> getWorkoutList(Workout116GeneratePlanV4Req planReq,
                                                        List<Integer> templateIds,
                                                        Integer restrictionSum,
                                                        List<ExerciseType116Enums> exerciseTypeEnumList) {

        List<ProjWorkout116DetailVO> workoutList = baseMapper.queryByTemplateIds(templateIds, restrictionSum, GlobalConstant.ONE);
        if (CollectionUtil.isEmpty(workoutList)) {
            return new ArrayList<>();
        }
        Gender116Enums coachGenderEnum = Gender116Enums.getByCode(planReq.getCoachGenderCode());
        List<ProjWorkout116DetailVO> allSelectedWorkoutList = filterAndMatchWorkout(planReq, templateIds, restrictionSum, exerciseTypeEnumList);

        // female 和 male 交替处理
        if (coachGenderEnum == FEMALE || coachGenderEnum == MALE) {
            return allSelectedWorkoutList;
        }

        List<ProjWorkout116DetailVO> finalWorkoutList = new ArrayList<>();
        Collections.shuffle(allSelectedWorkoutList);
        Map<String, List<ProjWorkout116DetailVO>> exerciseTypeMap = allSelectedWorkoutList.stream().collect(Collectors.groupingBy(ProjWorkout116DetailVO::getExerciseType));
        // gender 参数转换
        Gender116Enums genderEnum = Gender116Enums.getByCode(planReq.getGenderCode());
        List<Gender116Enums> fixGenderList = new ArrayList<>();
        if (coachGenderEnum == Gender116Enums.BOTH && genderEnum != Gender116Enums.BOTH) {
            fixGenderList.add(genderEnum);
            fixGenderList.add(genderEnum);
        }
        int femaleCount;
        int maleCount;
        Integer maxInterval = 4;
        if (genderEnum == FEMALE) {
            femaleCount = 21 - fixGenderList.size();
            maleCount = 7;
        } else {
            femaleCount = 14;
            maleCount = 14 - fixGenderList.size();
        }
        List<Gender116Enums> sortGenderList = sortGender(femaleCount, maleCount, fixGenderList, maxInterval);
        // 第一层key：exerciseType,第二层key：
        Map<String, Map<String, List<ProjWorkout116DetailVO>>> exerciseTypeGenderMap = new HashMap<>();
        exerciseTypeMap.forEach((exerciseType, workoutVOList) -> {
            Map<String, List<ProjWorkout116DetailVO>> genderMap = workoutVOList.stream().collect(Collectors.groupingBy(ProjWorkout116DetailVO::getGender));
            exerciseTypeGenderMap.put(exerciseType, genderMap);
        });
        int sortGenderSize = sortGenderList.size();
        for (int i = 0; i < sortGenderSize * 2; i++) {
            for (String exerciseType : exerciseTypeGenderMap.keySet()) {
                if (finalWorkoutList.size() >= sortGenderSize || CollUtil.isEmpty(sortGenderList)) {
                    return finalWorkoutList;
                }
                Gender116Enums gender = sortGenderList.get(0);
                Map<String, List<ProjWorkout116DetailVO>> genderMap = exerciseTypeGenderMap.get(exerciseType);
                ProjWorkout116DetailVO detailVO = matchWorkout(gender, genderMap);
                if (null != detailVO) {
                    finalWorkoutList.add(detailVO);
                    sortGenderList.remove(gender);
                }
            }
        }

        return finalWorkoutList;
    }

    private List<ProjWorkout116DetailVO> filterAndMatchWorkout(Workout116GeneratePlanV4Req planReq,
                                       List<Integer> templateIds,
                                       Integer restrictionSum,
                                       List<ExerciseType116Enums> exerciseTypeEnumList){
        List<ProjWorkout116DetailVO> workoutList = baseMapper.queryByTemplateIds(templateIds, restrictionSum, GlobalConstant.ONE);
        if (CollectionUtil.isEmpty(workoutList)) {
            return new ArrayList<>();
        }
        Collections.shuffle(workoutList);
        Integer positionCode = planReq.getPositionCode();
        Position116Enums positionEnum = Position116Enums.getByCode(positionCode);
        // code 转为name
        Set<String> equipmentNameSet = Optional.ofNullable(planReq.getEquipmentCodeSet()).orElse(new HashSet<>()).stream().map(Equipment116Enums::getByCode).filter(Objects::nonNull).map(Equipment116Enums::getName).collect(Collectors.toSet());
        Gender116Enums coachGenderEnum = Gender116Enums.getByCode(planReq.getCoachGenderCode());
        FilterWorkoutParamBO allConditionMatchedParamBO = new FilterWorkoutParamBO(
                exerciseTypeEnumList, workoutList, positionEnum, equipmentNameSet, coachGenderEnum);
        FilterWorkoutParamBO ignoreExerciseTypeMatchedParamBO = new FilterWorkoutParamBO(
                Arrays.asList(ExerciseType116Enums.values()), workoutList, positionEnum, equipmentNameSet, coachGenderEnum);
        FilterWorkoutParamBO ignoreExerciseTypeAndPositionMatchedParamBO = new FilterWorkoutParamBO(
                Arrays.asList(ExerciseType116Enums.values()), workoutList, BOTH, equipmentNameSet, coachGenderEnum);
        int needMatchCount = GlobalConstant.TWENTY_EIGHT;
        List<ProjWorkout116DetailVO> allMatchedWorkout = new ArrayList<>();
        for (FilterWorkoutParamBO filterParam : Arrays.asList(allConditionMatchedParamBO, ignoreExerciseTypeMatchedParamBO, ignoreExerciseTypeAndPositionMatchedParamBO)) {
            List<ProjWorkout116DetailVO> matchedWorkoutList = filterWorkout(filterParam);
            matchedWorkoutList.removeAll(allMatchedWorkout);
            int count = NumberUtil.min(matchedWorkoutList.size(), (needMatchCount - allMatchedWorkout.size()));
            List<ProjWorkout116DetailVO> currentMatchedWorkout = getMatchedWorkout(matchedWorkoutList, count);
            if(CollUtil.isEmpty(currentMatchedWorkout)){
                continue;
            }
            if (coachGenderEnum == FEMALE || coachGenderEnum == MALE) {
                // 完全匹配的放在最前面
                currentMatchedWorkout = handleWorkoutListMaleOrFemale(currentMatchedWorkout, count);
            }
            if(CollUtil.isEmpty(currentMatchedWorkout)){
                continue;
            }
            allMatchedWorkout.addAll(currentMatchedWorkout);
            if(allMatchedWorkout.size() >= needMatchCount){
                return allMatchedWorkout;
            }
        }
        return allMatchedWorkout;
    }

    private List<ProjWorkout116DetailVO> handleWorkoutListMaleOrFemale(List<ProjWorkout116DetailVO> forHandleWorkoutList, int count) {

        if(count <=0 || CollUtil.isEmpty(forHandleWorkoutList)){
            return Collections.emptyList();
        }

        List<ProjWorkout116DetailVO> finalWorkoutList = new ArrayList<>();
        Map<String, List<ProjWorkout116DetailVO>> exerciseTypeMap = forHandleWorkoutList.stream().collect(Collectors.groupingBy(ProjWorkout116DetailVO::getExerciseType));
        for (int i = 0; i < count; i++) {
            for (List<ProjWorkout116DetailVO> detailList : exerciseTypeMap.values()) {
                if (finalWorkoutList.size() >= count) {
                    break;
                }
                if (CollUtil.isNotEmpty(detailList)) {
                    finalWorkoutList.add(detailList.remove(GlobalConstant.ZERO));
                }
            }
        }
        return finalWorkoutList;
    }

    /**
     * <p>此方法保证最终返回的视频数量小于等于28个</p>
     *
     * @param workoutDetailList 待匹配数据
     * @param matchedCount      需要匹配出的数量
     * @return java.util.List<com.laien.cmsapp.response.ProjWorkout116DetailVO>
     * <AUTHOR>
     * @date 2025/4/10 14:47
     */
    private List<ProjWorkout116DetailVO> getMatchedWorkout(List<ProjWorkout116DetailVO> workoutDetailList, int matchedCount) {
        if (CollUtil.isEmpty(workoutDetailList)) {
            return Collections.emptyList();
        }
        //赛选条件，第一层key：exerciseType,第二层key：position,第三层:coach
        Map<String, Map<String, Map<String, List<ProjWorkout116DetailVO>>>> exerciseMap = new HashMap<>();
        Map<String, Integer> exerciseCounterMap = new HashMap<>();
        for (ProjWorkout116DetailVO detailVO : workoutDetailList) {
            String exerciseType = detailVO.getExerciseType();
            exerciseCounterMap.put(exerciseType, 0);
            Map<String, Map<String, List<ProjWorkout116DetailVO>>> positionMap = exerciseMap.getOrDefault(exerciseType, new HashMap<>());
            exerciseMap.put(exerciseType, positionMap);
            String position = detailVO.getPosition();
            Map<String, List<ProjWorkout116DetailVO>> genderMap = positionMap.getOrDefault(position, new HashMap<>());
            positionMap.put(position, genderMap);
            String gender = detailVO.getGender();
            List<ProjWorkout116DetailVO> detailList = genderMap.getOrDefault(gender, new ArrayList<>());
            genderMap.put(gender, detailList);
            detailList.add(detailVO);
        }
        int exerciseCount = exerciseCounterMap.size();
        int day = matchedCount;
        int exerciseTypeNum = (day + exerciseCount - 1) / exerciseCount;
        List<ProjWorkout116DetailVO> detailList = new ArrayList<>();
        for (int i = 0; i < day; i++) {
            for (String exerciseType : exerciseMap.keySet()) {
                Map<String, Map<String, List<ProjWorkout116DetailVO>>> exerciseItemMap = exerciseMap.get(exerciseType);
                for (Map<String, List<ProjWorkout116DetailVO>> positionItemMap : exerciseItemMap.values()) {
                    for (List<ProjWorkout116DetailVO> detailItemList : positionItemMap.values()) {
                        if (detailList.size() >= day) {
                            return detailList;
                        }
                        detailItemList.removeAll(detailList);
                        Integer count = exerciseCounterMap.get(exerciseType);
                        if (CollUtil.isNotEmpty(detailItemList) && count < exerciseTypeNum) {
                            detailList.add(detailItemList.remove(0));
                            count++;
                            exerciseCounterMap.put(exerciseType, count);
                        }
                    }
                }
            }
        }
        return detailList;
    }

    private static List<ProjWorkout116DetailVO> filterWorkout(FilterWorkoutParamBO filterParam) {
        List<ProjWorkout116DetailVO> workoutList = filterParam.getWorkoutList();
        Position116Enums positionEnum = filterParam.getPositionEnum();
        Gender116Enums coachGenderEnum = filterParam.getCoachGenderEnum();
        Set<String> equipmentNameSet = filterParam.getEquipmentNameSet();
        List<ExerciseType116Enums> exerciseTypeEnumList = filterParam.getExerciseTypeEnumList();
        return workoutList.stream().filter(item -> {
                    String exerciseType = item.getExerciseType();
                    ExerciseType116Enums exerciseTypeEnum = ExerciseType116Enums.getByName(exerciseType);
                    return exerciseTypeEnumList.contains(exerciseTypeEnum);
                })
                .filter(item -> {
                    String position = item.getPosition();
                    if (Objects.equals(position, BOTH.getName()) || BOTH == positionEnum) {
                        return true;
                    }
                    String exerciseType = item.getExerciseType();
                    ExerciseType116Enums exerciseTypeEnum = ExerciseType116Enums.getByName(exerciseType);
                    if (null == exerciseTypeEnum) {
                        return true;
                    }
                    List<Position116Enums> positionList = exerciseTypeEnum.getPositionList();
                    if (CollUtil.isEmpty(positionList)) {
                        return true;
                    }
                    if (positionList.size() <= 1) {
                        return true;
                    }
                    return Objects.equals(position, positionEnum.getName());
                })
                .filter(item -> {
                    if (coachGenderEnum == Gender116Enums.BOTH) {
                        return true;
                    }
                    String exerciseType = item.getExerciseType();
                    ExerciseType116Enums exerciseTypeEnum = ExerciseType116Enums.getByName(exerciseType);
                    if (null == exerciseTypeEnum) {
                        return true;
                    }
                    Integer maleDay = exerciseTypeEnum.getMaleDay();
                    if (null == maleDay || maleDay <= 0) {
                        return true;
                    }
                    Integer femaleDay = exerciseTypeEnum.getFemaleDay();
                    if (null == femaleDay || femaleDay <= 0) {
                        return true;
                    }
                    return coachGenderEnum.getName().equals(item.getGender());
                }).collect(Collectors.toList());
    }

    private ProjWorkout116DetailVO matchWorkout(Gender116Enums gender, Map<String, List<ProjWorkout116DetailVO>> genderMap) {
        List<ProjWorkout116DetailVO> workoutGenderList = genderMap.get(gender.getName());
        if (CollUtil.isNotEmpty(workoutGenderList)) {
            return workoutGenderList.remove(GlobalConstant.ZERO);
        } else {
            for (List<ProjWorkout116DetailVO> detailList : genderMap.values()) {
                if (CollUtil.isNotEmpty(detailList)) {
                    return detailList.remove(GlobalConstant.ZERO);
                }
            }
        }
        return null;
    }


    public static boolean checkSort(List<Gender116Enums> list) {
        int count = 1;
        Gender116Enums previous = list.get(0);

        // 从第二个元素开始遍历
        for (int i = 1; i < list.size(); i++) {
            Gender116Enums current = list.get(i);
            if (current == previous) {
                count++;
                if (count > 4) {
                    return false;
                }
            } else {
                previous = current;
                count = 1;
            }
        }

        return true;
    }


    private List<Gender116Enums> sortGender(Integer femaleCount, Integer maleCount, List<Gender116Enums> fixGenderList, Integer maxInterval) {
        List<Gender116Enums> genderList = new ArrayList<>();
        List<Gender116Enums> finalGenderList = new ArrayList<>(fixGenderList);
        for (int i = 0; i < femaleCount; i++) {
            genderList.add(FEMALE);
        }
        for (int i = 0; i < maleCount; i++) {
            genderList.add(MALE);
        }
        int count = femaleCount + maleCount;
        Collections.shuffle(genderList);
        for (int i = 0; i < count; i++) {
            Gender116Enums gender = genderList.get(GlobalConstant.ZERO);
            List<Gender116Enums> allGender = new ArrayList<>();
            allGender.add(MALE);
            allGender.add(FEMALE);
            boolean check = check(femaleCount, maleCount, maxInterval, gender, finalGenderList, genderList);
            if (check) {
                genderList.remove(gender);
            } else {
                allGender.remove(gender);
                Gender116Enums gender116Enums = allGender.get(GlobalConstant.ZERO);
                if (genderList.remove(gender116Enums)) {
                    gender = gender116Enums;
                }
            }
            finalGenderList.add(gender);
            if (gender == FEMALE) {
                femaleCount--;
            } else {
                maleCount--;
            }
        }

        return finalGenderList;
    }

    private static boolean check(Integer femaleCount, Integer maleCount, Integer maxInterval, Gender116Enums gender, List<Gender116Enums> finalGenderList, List<Gender116Enums> genderList) {

        if (gender == FEMALE) {
            --femaleCount;
        } else {
            --maleCount;
        }
        Integer femaleSpaceCount = femaleCount;
        Integer maleSpaceCount = maleCount;
        Gender116Enums lastGender = CollUtil.getLast(genderList);

        if (lastGender == FEMALE) {
            if (--femaleSpaceCount <= 0 && maleCount > maxInterval) {
                return false;
            }
        } else {
            if (--maleSpaceCount <= 0 && femaleCount > maxInterval) {
                return false;
            }
        }
        List<Gender116Enums> lastList = CollUtil.sub(finalGenderList, Math.max(0, finalGenderList.size() - 4), finalGenderList.size());
        lastList.add(gender);
        if (lastList.size() >= 5 && new HashSet<>(lastList).size() <= 1) {
            return false;
        }

        if (femaleSpaceCount <= 0 || maleSpaceCount <= 0) {
            return true;
        }

        if (NumberUtil.div(femaleCount, maleSpaceCount).doubleValue() > maxInterval) {
            return false;
        }
        return !(NumberUtil.div(maleCount, femaleSpaceCount).doubleValue() > maxInterval);
    }

    @Override
    public List<ProjWorkout116DetailVO> queryV3(List<Integer> generateWorkoutIdList,
                                                List<Integer> assembleWorkoutIdList,
                                                ProjPublishCurrentVersionInfoBO versionInfoBO) {
        List<ProjWorkout116DetailVO> detailList = query(generateWorkoutIdList, assembleWorkoutIdList, versionInfoBO, false);
        Map<String, Integer> playlistMap = new HashMap<>();
        Set<String> playlistEmptyExerciseTypeSet = new HashSet<>();
        if (CollectionUtil.isNotEmpty(detailList)) {
            for (ProjWorkout116DetailVO detailVO : detailList) {
                String exerciseType = detailVO.getExerciseType();
                if (MALE.getName().equals(detailVO.getGender()) && GENERATE == detailVO.getDataType()) {
                    // 如果是男就将CoverImgUrl和DetailImgUrl替换为男性的，前端使用的是coverImgUrl和detailImgUrl
                    detailVO.setCoverImgUrl(detailVO.getCoverImgMaleUrl())
                            .setDetailImgUrl(detailVO.getDetailImgMaleUrl());
                }
                Integer playlistId = playlistMap.get(exerciseType);
                if (null != playlistId) {
                    detailVO.setPlaylistId(playlistId);
                    continue;
                }
                if (playlistEmptyExerciseTypeSet.contains(exerciseType)) {
                    continue;
                }

                playlistId = getPlaylistId(exerciseType);
                if (null == playlistId) {
                    playlistEmptyExerciseTypeSet.add(exerciseType);
                    log.error("query v3 oog116 exercise type:{} playlistId is empty", exerciseType);
                    continue;
                }
                detailVO.setPlaylistId(playlistId);
                playlistMap.put(exerciseType, playlistId);
            }
        }

        return detailList;
    }

    @Override
    public List<ProjWorkout116DetailVO> query(List<Integer> generateWorkoutIdList,
                                              List<Integer> assembleWorkoutIdList,
                                              ProjPublishCurrentVersionInfoBO versionInfoBO,
                                              boolean downloadJson) {
        Integer dataVersion = downloadJson ? 0 : 1;
        List<ProjWorkout116DetailVO> detailList = new ArrayList<>(30);
        if (CollectionUtil.isNotEmpty(generateWorkoutIdList)) {
            List<ProjWorkout116DetailVO> generateWorkoutList = baseMapper.findByIdList(generateWorkoutIdList, dataVersion);
            if (CollectionUtil.isNotEmpty(generateWorkoutList)) {
                List<ResVideo116VO> videoList = baseMapper.queryVideoListByIds(generateWorkoutIdList);
                injectionAttribute(videoList, generateWorkoutList, downloadJson);
                detailList.addAll(generateWorkoutList);
            }
        }

        if (CollectionUtil.isNotEmpty(assembleWorkoutIdList)) {
            List<ProjWorkout116DetailVO> assembleWorkoutList = projWorkout116PubService.getDetailByIdList(assembleWorkoutIdList, downloadJson);
            if (CollectionUtil.isNotEmpty(assembleWorkoutList)) {
                detailList.addAll(assembleWorkoutList);
            }
        }
        if (CollectionUtil.isNotEmpty(detailList)) {
            for (ProjWorkout116DetailVO detailVO : detailList) {
                String equipment = detailVO.getEquipment();
                // WorkoutDataType116Enums.ASSEMBLE 已经处理
                if (GENERATE == detailVO.getDataType()) {
                    Integer playlistId = getPlaylistId(detailVO.getExerciseType());
                    detailVO.setPlaylistId(playlistId);

                    if (Objects.equals(detailVO.getRestriction(), ProjWorkout116PubServiceImpl.NONE)) {
                        detailVO.setRestriction(ProjWorkout116PubServiceImpl.ALL_BODY_PARTS);
                    }
                    // 添加默认英语
                    List<ProjWorkout116AudioDetailVO> audioList = new ArrayList<>();
                    ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
                    audioDetailVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
                    audioDetailVO.setAudioJsonUrl(detailVO.getAudioJsonUrl());
                    audioList.add(audioDetailVO);
                    detailVO.setAudioList(audioList);

                    List<Equipment116Enums> equipmentList = Equipment116Enums.getEquipmentList(equipment);
                    if (CollectionUtil.isEmpty(equipmentList)) {
                        log.error("card name is empty, workoutId:{}, equipment:{}", detailVO.getId(), equipment);
                    } else {
                        if (equipmentList.size() > 1) {
                            equipmentList.remove(Equipment116Enums.NONE);
                        }
                        List<EquipmentVO> equipmentVoList = new ArrayList<>(equipmentList.size());
                        for (Equipment116Enums equipmentEnums : equipmentList) {
                            equipmentVoList.add(new EquipmentVO(equipmentEnums.getCardName(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                        }
                        detailVO.setCardEquipment(equipmentVoList);
                    }

                    List<String> myEquipmentList = new ArrayList<>();
                    for (ProjWorkout116UnitDetailVO unit : detailVO.getUnits()) {
                        List<ResVideo116VO> videos = unit.getVideos();
                        if (videos != null) {
                            for (ResVideo116VO video : videos) {
                                if (Objects.equals(video.getPosition(), SEATED.getName())) {
                                    myEquipmentList.add(ProjWorkout116PubServiceImpl.CHAIR);
                                    break;
                                }
                            }
                        }
                    }
                    detailVO.setEquipmentList(myEquipmentList);

                    // setEquipmentV3List
                    List<EquipmentVO> equipmentVoList = new ArrayList<>();
                    if (StringUtils.isNotBlank(equipment)) {
                        List<Equipment116Enums> equipmentEnumList = Equipment116Enums.getEquipmentList(equipment);
                        if (CollectionUtil.isNotEmpty(equipmentEnumList)) {
                            // 排查掉NONE
                            equipmentEnumList.remove(Equipment116Enums.NONE);
                            for (Equipment116Enums equipmentEnums : equipmentEnumList) {
                                equipmentVoList.add(new EquipmentVO(equipmentEnums.getCardName(), equipmentEnums.getSmallImgUrl(), equipmentEnums.getDetailImgUrl()));
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(myEquipmentList)) {
                        // 不是空就肯定是chair
                        equipmentVoList.add(new EquipmentVO(myEquipmentList.get(0), null, "cms/video116/img/chair.png"));
                    }
                    detailVO.setEquipmentV3List(equipmentVoList);

                }
            }
        }

        //  多语言处理，workout 不管传的什么语言，audioList都要全量返回，video 按指定语言返回
        String lang = RequestContextUtils.getLanguage();
        if (!detailList.isEmpty()) {
            List<Integer> workoutIdList = new ArrayList<>();

            for (ProjWorkout116DetailVO detailVO : detailList) {
                if (GENERATE == detailVO.getDataType()) {
                    workoutIdList.add(detailVO.getId());
                }
            }

            // 查询翻译
            Map<Integer, List<ProjWorkout116GenerateI18n>> workoutBizI18nMap = projWorkout116GenerateI18nService.getAllLanguageListByIds(workoutIdList);
            //先进行文本翻译(resImage)
            //将所有内容统一翻译
            Map<String, String> equipmentMap = translateI18n(detailList, lang);
            for (ProjWorkout116DetailVO detailVO : detailList) {
                if (WorkoutDataType116Enums.ASSEMBLE == detailVO.getDataType()) {
                    continue;
                }
                Integer workoutId = detailVO.getId();
                //采用文本翻译
                List<String> equipmentList = detailVO.getEquipmentList();
                List<EquipmentVO> cardEquipment = detailVO.getCardEquipment();
                List<EquipmentVO> equipmentV3List = detailVO.getEquipmentV3List();
                if (CollectionUtil.isNotEmpty(equipmentList)) {
                    List<String> equipmentI18nList = new ArrayList<>();
                    for (String equipment : equipmentList) {
                        equipmentI18nList.add(equipmentMap.get(equipment));
                    }
                    detailVO.setEquipmentList(equipmentI18nList);
                }
                if (workoutBizI18nMap.containsKey(workoutId)) {
                    List<ProjWorkout116GenerateI18n> i18nBizList = workoutBizI18nMap.get(workoutId);
                    for (ProjWorkout116GenerateI18n generateI18nBiz : i18nBizList) {
                        // 添加其他语言音频
                        String audioJsonUrl = generateI18nBiz.getAudioJsonUrl();
                        if (StringUtils.isNotBlank(audioJsonUrl)) {
                            ProjWorkout116AudioDetailVO audioDetailVO = new ProjWorkout116AudioDetailVO();
                            audioDetailVO.setLanguage(generateI18nBiz.getLanguage());
                            audioDetailVO.setAudioJsonUrl(generateI18nBiz.getAudioJsonUrl());
                            detailVO.getAudioList().add(audioDetailVO);
                        }
                    }
                }
            }
        }

        return detailList;
    }

    private Map<String, String> translateI18n(List<ProjWorkout116DetailVO> detailList, String lang) {
        List<AppTextCoreI18nModel> textCoreI18nModelList = new ArrayList<>();
        List<AppAudioCoreI18nModel> audioCoreI18nModelList = new ArrayList<>();
        List<ConstantTranslateVo> constantTranslateVoList = new ArrayList<>();
        for (ProjWorkout116DetailVO detailVO : detailList) {
            if (!Objects.equals(detailVO.getDataType(), WorkoutDataType116Enums.ASSEMBLE)) {
                textCoreI18nModelList.add(detailVO);
                List<String> equipmentList = detailVO.getEquipmentList();
                if (CollectionUtil.isNotEmpty(equipmentList)) {
                    for (String equipment : equipmentList) {
                        ConstantTranslateVo constantTranslateVo = new ConstantTranslateVo(equipment);
                        constantTranslateVoList.add(constantTranslateVo);
                    }
                }
                if (CollUtil.isNotEmpty(detailVO.getCardEquipment()))
                    textCoreI18nModelList.addAll(detailVO.getCardEquipment());
                if (CollUtil.isNotEmpty(detailVO.getEquipmentV3List()))
                    textCoreI18nModelList.addAll(detailVO.getEquipmentV3List());
                if (CollUtil.isNotEmpty(detailVO.getUnits())) textCoreI18nModelList.addAll(detailVO.getUnits());
                for (ProjWorkout116UnitDetailVO unit : detailVO.getUnits()) {
                    if (CollUtil.isNotEmpty(unit.getVideos())) {
                        audioCoreI18nModelList.addAll(unit.getVideos());
                        textCoreI18nModelList.addAll(unit.getVideos());
                    }
                }
            }
        }
        if(!constantTranslateVoList.isEmpty()){
            constantTranslateVoList = new ArrayList<>(new HashSet<>(constantTranslateVoList));
            textCoreI18nModelList.addAll(constantTranslateVoList);
        }

        if (!textCoreI18nModelList.isEmpty())
            textTaskI18nPubService.translate(textCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));
        if (!audioCoreI18nModelList.isEmpty())
            speechTaskI18nPubService.translate(audioCoreI18nModelList, ProjCodeEnums.OOG116, LanguageEnums.getByNameIgnoreCase(lang));

        Map<String, String> equipmentMap = constantTranslateVoList.stream()
                .collect(Collectors.toMap(ConstantTranslateVo::getSource, ConstantTranslateVo::getTarget));
        return equipmentMap;
    }

    private List<ProjWorkout116UnitDetailVO> getWorkoutUnits(List<ResVideo116VO> workoutVideoList,
                                                             List<ProjTemplate116Rule> ruleList) {
        List<ProjWorkout116UnitDetailVO> workoutUnitDetailList = new ArrayList<>();
        Map<Integer, List<ResVideo116VO>> workoutVideoMap = workoutVideoList
                .stream()
                .collect(Collectors.groupingBy(ResVideo116VO::getRuleId));

        List<ProjTemplate116Rule> ruleListCopy = new ArrayList<>(ruleList);
        for (ProjTemplate116Rule rule : ruleList) {
            if (!workoutVideoMap.containsKey(rule.getId())) {
                ruleListCopy.remove(rule);
            }
        }
        for (ProjTemplate116Rule rule : ruleListCopy) {
            ProjWorkout116UnitDetailVO unitDetail = new ProjWorkout116UnitDetailVO();
            List<ResVideo116VO> unitVideoList = workoutVideoMap.get(rule.getId());
            unitDetail.setRuleId(rule.getId())
                    .setRounds(rule.getRounds())
                    .setVideos(unitVideoList)
                    .setUnitName(rule.getUnitName());
            workoutUnitDetailList.add(unitDetail);
        }
        return workoutUnitDetailList;
    }

    private List<ProjWorkout116DetailVO> getWorkoutList(Workout116GeneratePlanReq planReq,
                                                        Integer templateId,
                                                        Integer restrictionSum,
                                                        Integer dataVersion) {
        List<ProjWorkout116DetailVO> workoutList = baseMapper.query(templateId, restrictionSum, dataVersion);
        if (CollectionUtil.isEmpty(workoutList)) {
            return null;
        }
        Map<String, List<ProjWorkout116DetailVO>> workoutMap = workoutList
                .stream()
                .collect(Collectors.groupingBy(ProjWorkout116DetailVO::getPosition));
        Position116Enums position = planReq.getPosition();
        List<ProjWorkout116DetailVO> workoutDetailList;
        if (BOTH == position) {
            List<ProjWorkout116DetailVO> seatedList = workoutMap.get(SEATED.getName());
            List<ProjWorkout116DetailVO> standingList = workoutMap.get(STANDING.getName());
            Collections.shuffle(seatedList);
            Collections.shuffle(standingList);
            workoutDetailList = new ArrayList<>(28);
            workoutDetailList.addAll(seatedList.subList(0, 14));
            workoutDetailList.addAll(standingList.subList(0, 14));
            Collections.shuffle(workoutDetailList);
        } else {
            workoutDetailList = workoutMap.get(position.getName());
            Collections.shuffle(workoutDetailList);
            workoutDetailList = workoutDetailList.subList(0, 28);
        }
        return workoutDetailList;
    }

    private List<ProjWorkout116DetailVO> getWorkoutList(Workout116GeneratePlanV3Req planReq,
                                                        Integer templateId,
                                                        Integer restrictionSum,
                                                        Set<Equipment116Enums> equipmentSet) {

        List<ProjWorkout116DetailVO> workoutList = baseMapper.query(templateId, restrictionSum, 1);
        if (CollectionUtil.isEmpty(workoutList)) {
            return null;
        }
        Map<String, List<ProjWorkout116DetailVO>> workoutMap = workoutList
                .stream()
                .collect(Collectors.groupingBy(ProjWorkout116DetailVO::getPosition));
        Position116Enums position = planReq.getPosition();
        List<ProjWorkout116DetailVO> workoutDetailList;
        Gender116Enums coachGender = planReq.getCoachGender();
        if (BOTH == position) {
            workoutDetailList = handleBothPosition(workoutMap, equipmentSet, coachGender);
        } else {
            workoutDetailList = handleNotBothPosition(workoutMap.get(position.getName()), equipmentSet, coachGender);
        }
        return workoutDetailList;
    }

    /**
     * 处理position只包含站或者坐的情况
     */
    private List<ProjWorkout116DetailVO> handleNotBothPosition(List<ProjWorkout116DetailVO> workoutDetailList,
                                                               Set<Equipment116Enums> equipmentSet, Gender116Enums coachGender) {
        Collections.shuffle(workoutDetailList);
        List<ProjWorkout116DetailVO> finalWorkoutDetailList = new ArrayList<>(GlobalConstant.TWENTY_EIGHT);
        List<ProjWorkout116DetailVO> temporaryWorkoutDetailList = new ArrayList<>(GlobalConstant.TWENTY_EIGHT);
        Map<String, List<ProjWorkout116DetailVO>> equipmentMap = workoutDetailList.stream().collect(
                Collectors.groupingBy(ProjWorkout116DetailVO::getEquipment));
        // equipmentSet可能得情况，后面的逻辑都基于这几种情况进行处理
        // - 当equipment = 哑铃：13哑铃Regular + 5Tai Chi + 5Dancing + 5无器械Regular；
        // - 当equipment = 弹力带：13弹力带Regular + 5Tai Chi + 5个Dancing + 5个无器械Regular；
        // - 当equipment = 哑铃+弹力带：5弹力带Regular + 5哑铃Regular + 5Tai Chi + 5Dancing + 8无器械Regular；
        // - 当equipment = 无：7Tai Chi + 7Dancing + 14无器械Regular；

        List<ProjWorkout116DetailVO> noEquipmentWorkout = equipmentMap.get(NONE.getName());
        Map<String, List<ProjWorkout116DetailVO>> exerciseTypeMap = noEquipmentWorkout.stream().collect(
                Collectors.groupingBy(ProjWorkout116DetailVO::getExerciseType));

        // String taiChiExerciseType = TAI_CHI.getName();
        String dancingExerciseType = DANCING.getName();
        List<ProjWorkout116DetailVO> noEquipmentRegularWorkoutList = workoutSortByGender(
                coachGender, exerciseTypeMap.get(REGULAR_EXERCISE_TYPE));

        if (equipmentSet.size() == GlobalConstant.TWO) {
            temporaryWorkoutDetailList.addAll(equipmentMap.get(RESISTANCE_BAND.getName()).subList(GlobalConstant.ZERO, GlobalConstant.EIGHT));
            temporaryWorkoutDetailList.addAll(equipmentMap.get(DUMBBELL.getName()).subList(GlobalConstant.ZERO, GlobalConstant.EIGHT));
            // finalWorkoutDetailList.addAll(exerciseTypeMap.get(taiChiExerciseType).subList(0, 5));
            if (coachGender == MALE) {
                finalWorkoutDetailList.addAll(noEquipmentRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.TWELVE));
            } else {
                temporaryWorkoutDetailList.addAll(exerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.FIVE));
                finalWorkoutDetailList.addAll(noEquipmentRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SEVEN));
            }

            Collections.shuffle(temporaryWorkoutDetailList);
            finalWorkoutDetailList.addAll(temporaryWorkoutDetailList);
            return finalWorkoutDetailList;
        }

        //处理equipment为单选的情况
        Equipment116Enums equipment = equipmentSet.stream().findFirst().orElse(null);
        if (equipment == RESISTANCE_BAND || equipment == DUMBBELL) {
            temporaryWorkoutDetailList.addAll(equipmentMap.get(equipment.getName()).subList(GlobalConstant.ZERO, GlobalConstant.THIRTEEN));
            // finalWorkoutDetailList.addAll(exerciseTypeMap.get(taiChiExerciseType).subList(0, 5));
            if (coachGender == MALE) {
                finalWorkoutDetailList.addAll(noEquipmentRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.FIFTEEN));
            } else {
                temporaryWorkoutDetailList.addAll(exerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.FIVE));
                finalWorkoutDetailList.addAll(noEquipmentRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.TEN));
            }

        }
        if (equipment == NONE) {
            // finalWorkoutDetailList.addAll(exerciseTypeMap.get(taiChiExerciseType).subList(0, 7));
            if (coachGender == MALE) {
                finalWorkoutDetailList.addAll(noEquipmentRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.TWENTY_EIGHT));
            } else {
                temporaryWorkoutDetailList.addAll(exerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.SEVEN));
                finalWorkoutDetailList.addAll(noEquipmentRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.TWENTY_ONE));
            }

        }
        Collections.shuffle(temporaryWorkoutDetailList);
        finalWorkoutDetailList.addAll(temporaryWorkoutDetailList);
        return finalWorkoutDetailList;
    }

    private static List<ProjWorkout116DetailVO> workoutSortByGender(
            Gender116Enums gender, List<ProjWorkout116DetailVO> noEquipmentRegularWorkoutList) {
        if (Gender116Enums.BOTH == gender) {
            return noEquipmentRegularWorkoutList;
        }
        Map<String, List<ProjWorkout116DetailVO>> noEquipmentRegularWorkoutGenderGroup = noEquipmentRegularWorkoutList
                .stream().collect(Collectors.groupingBy(ProjWorkout116DetailVO::getGender));
        noEquipmentRegularWorkoutList = new ArrayList<>(noEquipmentRegularWorkoutList.size());
        List<ProjWorkout116DetailVO> workoutDetailList = noEquipmentRegularWorkoutGenderGroup.remove(gender.getName());
        if (CollUtil.isNotEmpty(workoutDetailList)) {
            noEquipmentRegularWorkoutList.addAll(workoutDetailList);
        }
        for (List<ProjWorkout116DetailVO> workoutList : noEquipmentRegularWorkoutGenderGroup.values()) {
            noEquipmentRegularWorkoutList.addAll(workoutList);
        }
        return noEquipmentRegularWorkoutList;
    }

    /**
     * 处理position为both类型的
     */
    private List<ProjWorkout116DetailVO> handleBothPosition(Map<String, List<ProjWorkout116DetailVO>> workoutPositionMap,
                                                            Set<Equipment116Enums> equipmentSet, Gender116Enums coachGender) {
        List<ProjWorkout116DetailVO> seatedWorkoutDetailList = workoutPositionMap.get(SEATED.getName());
        List<ProjWorkout116DetailVO> stadingWorkoutDetailList = workoutPositionMap.get(STANDING.getName());
        Collections.shuffle(seatedWorkoutDetailList);
        Collections.shuffle(seatedWorkoutDetailList);
        List<ProjWorkout116DetailVO> finalWorkoutDetailList = new ArrayList<>(GlobalConstant.TWENTY_EIGHT);
        List<ProjWorkout116DetailVO> temporaryWorkoutDetailList = new ArrayList<>(GlobalConstant.TWENTY_EIGHT);

        Map<String, List<ProjWorkout116DetailVO>> seatedEquipmentMap = seatedWorkoutDetailList.stream().collect(
                Collectors.groupingBy(ProjWorkout116DetailVO::getEquipment));
        Map<String, List<ProjWorkout116DetailVO>> stadingEquipmentMap = stadingWorkoutDetailList.stream().collect(
                Collectors.groupingBy(ProjWorkout116DetailVO::getEquipment));

        // equipmentSet可能得情况，后面的逻辑都基于这几种情况进行处理，equipment = 哑铃、弹力带、哑铃+弹力带、无
        List<ProjWorkout116DetailVO> seatedNoEquipmentWorkout = seatedEquipmentMap.get(NONE.getName());
        List<ProjWorkout116DetailVO> standingNoEquipmentWorkout = stadingEquipmentMap.get(NONE.getName());

        Map<String, List<ProjWorkout116DetailVO>> seatedExerciseTypeMap = seatedNoEquipmentWorkout.stream().collect(
                Collectors.groupingBy(ProjWorkout116DetailVO::getExerciseType));
        Map<String, List<ProjWorkout116DetailVO>> standingExerciseTypeMap = standingNoEquipmentWorkout.stream().collect(
                Collectors.groupingBy(ProjWorkout116DetailVO::getExerciseType));

        // String taiChiExerciseType = TAI_CHI.getName();
        String dancingExerciseType = DANCING.getName();

        List<ProjWorkout116DetailVO> noEquipmentSeatedRegularWorkoutList = workoutSortByGender(
                coachGender, seatedExerciseTypeMap.get(REGULAR_EXERCISE_TYPE));

        List<ProjWorkout116DetailVO> noEquipmentStandingRegularWorkoutList = workoutSortByGender(
                coachGender, standingExerciseTypeMap.get(REGULAR_EXERCISE_TYPE));

        if (equipmentSet.size() == GlobalConstant.TWO) {
            temporaryWorkoutDetailList.addAll(stadingEquipmentMap.get(RESISTANCE_BAND.getName()).subList(GlobalConstant.ZERO, GlobalConstant.FOUR));
            temporaryWorkoutDetailList.addAll(seatedEquipmentMap.get(RESISTANCE_BAND.getName()).subList(GlobalConstant.ZERO, GlobalConstant.FOUR));
            temporaryWorkoutDetailList.addAll(stadingEquipmentMap.get(DUMBBELL.getName()).subList(GlobalConstant.ZERO, GlobalConstant.FOUR));
            temporaryWorkoutDetailList.addAll(seatedEquipmentMap.get(DUMBBELL.getName()).subList(GlobalConstant.ZERO, GlobalConstant.FOUR));
            // finalWorkoutDetailList.addAll(standingExerciseTypeMap.get(taiChiExerciseType).subList(0, 3));
            // finalWorkoutDetailList.addAll(seatedExerciseTypeMap.get(taiChiExerciseType).subList(0, 2));
            if (coachGender == MALE) {
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SIX));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SIX));
            } else {
                temporaryWorkoutDetailList.addAll(standingExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.TWO));
                temporaryWorkoutDetailList.addAll(seatedExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.THREE));
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.FOUR));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.THREE));
            }

            Collections.shuffle(finalWorkoutDetailList);
            Collections.shuffle(temporaryWorkoutDetailList);
            finalWorkoutDetailList.addAll(temporaryWorkoutDetailList);
            return finalWorkoutDetailList;
        }

        //处理equipment为单选的情况
        Equipment116Enums equipment = equipmentSet.stream().findFirst().orElse(null);
        if (equipment == DUMBBELL) {
            temporaryWorkoutDetailList.addAll(stadingEquipmentMap.get(DUMBBELL.getName()).subList(GlobalConstant.ZERO, GlobalConstant.SEVEN));
            temporaryWorkoutDetailList.addAll(seatedEquipmentMap.get(DUMBBELL.getName()).subList(GlobalConstant.ZERO, GlobalConstant.FIVE));
            // finalWorkoutDetailList.addAll(standingExerciseTypeMap.get(taiChiExerciseType).subList(0, 3));
            // finalWorkoutDetailList.addAll(seatedExerciseTypeMap.get(taiChiExerciseType).subList(0, 2));
            if (coachGender == MALE) {
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SEVEN));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.NINE));
            } else {
                temporaryWorkoutDetailList.addAll(standingExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.ONE));
                temporaryWorkoutDetailList.addAll(seatedExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.TWO));
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SIX));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SEVEN));
            }
        }

        if (equipment == RESISTANCE_BAND) {
            temporaryWorkoutDetailList.addAll(stadingEquipmentMap.get(RESISTANCE_BAND.getName()).subList(GlobalConstant.ZERO, GlobalConstant.FIVE));
            temporaryWorkoutDetailList.addAll(seatedEquipmentMap.get(RESISTANCE_BAND.getName()).subList(GlobalConstant.ZERO, GlobalConstant.EIGHT));
            // finalWorkoutDetailList.addAll(standingExerciseTypeMap.get(taiChiExerciseType).subList(0, 3));
            // finalWorkoutDetailList.addAll(seatedExerciseTypeMap.get(taiChiExerciseType).subList(0, 2));
            if (coachGender == MALE) {
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.NINE));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SIX));
            } else {
                temporaryWorkoutDetailList.addAll(standingExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.TWO));
                temporaryWorkoutDetailList.addAll(seatedExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.THREE));
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.SEVEN));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.THREE));
            }

        }

        if (equipment == NONE) {
            // finalWorkoutDetailList.addAll(standingExerciseTypeMap.get(taiChiExerciseType).subList(0, 3));
            // finalWorkoutDetailList.addAll(seatedExerciseTypeMap.get(taiChiExerciseType).subList(0, 4));
            if (coachGender == MALE) {
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.FOURTEEN));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.FOURTEEN));
            } else {
                temporaryWorkoutDetailList.addAll(standingExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.FOUR));
                temporaryWorkoutDetailList.addAll(seatedExerciseTypeMap.get(dancingExerciseType).subList(GlobalConstant.ZERO, GlobalConstant.THREE));
                finalWorkoutDetailList.addAll(noEquipmentStandingRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.TEN));
                finalWorkoutDetailList.addAll(noEquipmentSeatedRegularWorkoutList.subList(GlobalConstant.ZERO, GlobalConstant.ELEVEN));
            }

        }
        Collections.shuffle(finalWorkoutDetailList);
        Collections.shuffle(temporaryWorkoutDetailList);
        finalWorkoutDetailList.addAll(temporaryWorkoutDetailList);
        return finalWorkoutDetailList;

    }

    /**
     * 注入ProjWorkout116DetailVO属性
     */
    private void injectionAttribute(List<ResVideo116VO> videoList,
                                    List<ProjWorkout116DetailVO> workoutList,
                                    boolean downloadJson) {
        Set<Integer> ruleIdSet = videoList.stream().map(ResVideo116VO::getRuleId).collect(Collectors.toSet());

        List<ProjTemplate116Rule> ruleList = projTemplate116RuleService.find(ruleIdSet);
        Map<Integer, List<ResVideo116VO>> videoMap = videoList.stream().collect(
                Collectors.groupingBy(ResVideo116VO::getWorkoutId));
        workoutList.forEach(detail -> {
            Integer restrictionSum = detail.getRestrictionSum();
            List<Restriction116Enums> restrictionEnumList = Restriction116Enums.getRestriction(restrictionSum);
            List<String> restrictionList = restrictionEnumList.stream().map(Restriction116Enums::getName).collect(Collectors.toList());
            List<ResVideo116VO> workoutVideoList = videoMap.get(detail.getId());
            if (downloadJson) {
                detail.setAudioJsonList(projWorkout116GenerateAudioJsonService.findAudioJson(detail.getId()));
            }
            String restriction = CollectionUtil.join(restrictionList, ",");
            if (StringUtils.isBlank(restriction)) {
                restriction = "None";
            }
            detail.setUnits(getWorkoutUnits(workoutVideoList, ruleList))
                    .setRestriction(restriction)
                    .setDataType(GENERATE);
        });
    }

    private Integer getPlaylistId(String exerciseType) {
        if (StringUtils.isBlank(exerciseType)) {
            return null;
        }
        ExerciseType116Enums exerciseTypeEnum = ExerciseType116Enums.getByName(exerciseType);
        List<ProjPlaylistAppVO> playlists;
        if (null != exerciseTypeEnum) {
            playlists = projPlaylistService.selectListApp(exerciseTypeEnum.getPlaylistType());
        } else {
            playlists = projPlaylistService.selectListApp("Normal");
        }
        if (CollectionUtil.isNotEmpty(playlists)) {
            return playlists.get(0).getId();
        }
        return null;
    }
}
