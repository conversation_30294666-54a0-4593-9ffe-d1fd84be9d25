package com.laien.cmsapp.oog116.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * ProjWorkout116RecommendListReq
 *
 * <AUTHOR>
 * @since 2025/03/25
 */
@Data
@ApiModel(value = "ProjWorkout116RecommendListReq", description = "ProjWorkout116RecommendListReq")
public class ProjWorkout116RecommendListReq {

    @ApiModelProperty(value = "Workout Type Code（运动类型）：10-Chair Yoga，11-<PERSON>，12-Dancing，13-Gentle Cardio，14-Walking，15-Dumbbell，16-Resistance Band")
    private Set<Integer> workoutTypeCodeSet;

    @ApiModelProperty(value = "Difficulty Code（难度）：10-Easy，11-Medium，12-Hard")
    private Integer difficultyCode;

    @ApiModelProperty(value = "Category Code（锻炼部位）：10-Full Body，11-Belly，12-Legs，13-Back，14-Arms，15-Abs，16-Shoulders")
    private Set<Integer> categoryTypeCodeSet;

    @ApiModelProperty(value = "当前版本app包含的exerciseTypeCodeSet")
    private Set<Integer> containsExerciseCodeSet;

    @ApiModelProperty(value = "是否排除Program中的Workout",hidden = true)
    private Boolean excludeProgramWorkout = false;
}
