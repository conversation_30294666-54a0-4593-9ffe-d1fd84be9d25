package com.laien.cmsapp.oog116.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog116.entity.ProjTemplate116Pub;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanReq;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV3Req;
import com.laien.cmsapp.oog116.requst.Workout116GeneratePlanV4Req;
import com.laien.cmsapp.oog116.response.ProjWorkout116GeneratePlanV4VO;
import com.laien.cmsapp.oog116.response.ProjWorkout116GeneratePlanVO;
import com.laien.common.oog116.enums.Restriction116Enums;

import java.util.List;

/**
 * <p>
 * template116 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface IProjTemplate116Service extends IService<ProjTemplate116Pub> {

    List<Integer> findIdList(Workout116GeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<Integer> findIdList(Integer durationCode, ProjPublishCurrentVersionInfoBO versionInfoBO);

    ProjWorkout116GeneratePlanVO plan(Workout116GeneratePlanReq planReq,
                                      ProjPublishCurrentVersionInfoBO versionInfoBO,
                                      boolean downloadJson,
                                      List<Restriction116Enums> restriction116List);

    ProjWorkout116GeneratePlanVO planV3(Workout116GeneratePlanV3Req planReq,
                                        ProjPublishCurrentVersionInfoBO versionInfoBO,
                                        List<Restriction116Enums> restriction116List);

    ProjWorkout116GeneratePlanV4VO planV4(Workout116GeneratePlanV4Req planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    ProjWorkout116GeneratePlanV4VO planV5(Workout116GeneratePlanV4Req planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);
}
