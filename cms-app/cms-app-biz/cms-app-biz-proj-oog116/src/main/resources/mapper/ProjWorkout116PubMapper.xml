<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog116.mapper.ProjWorkout116PubMapper">


    <!-- 定义resultMap处理TypeHandler字段 -->
    <resultMap id="ProjWorkout116DetailVOMap" type="com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO">
        <result column="region" property="region" typeHandler="com.laien.common.oog116.enums.Region116Enums$TypeHandler"/>
        <result column="focus" property="focus" typeHandler="com.laien.common.oog116.enums.Focus116Enums$TypeHandler"/>
        <result column="support_prop" property="supportProp" typeHandler="com.laien.common.oog116.enums.SupportProp116Enums$TypeHandler"/>
    </resultMap>

    <!-- 定义resultMap处理TypeHandler字段 -->
    <resultMap id="ResVideo116VOMap" type="com.laien.cmsapp.oog116.response.ResVideo116VO">
        <result column="region" property="region" typeHandler="com.laien.common.oog116.enums.Region116Enums$TypeHandler"/>
        <result column="focus" property="focus" typeHandler="com.laien.common.oog116.enums.Focus116Enums$TypeHandler"/>
    </resultMap>

    <select id="queryList" resultType="com.laien.cmsapp.oog116.response.ProjWorkout116ListVO">
        SELECT
            w.id,
            w.`name`,
            w.event_name,
            w.cover_img_url,
            w.difficulty,
            w.position,
            w.restriction,
            w.calorie,
            w.duration,
            w.subscription,
            w.new_start_time,
            w.new_end_time,
            w.equipment,
            w.gender,
            w.exercise_type
        FROM
            proj_workout116_pub w
        <if test="param.categoryId != null">
            INNER JOIN proj_category116_proj_workout116_pub cw ON w.id = cw.proj_workout116_id
        </if>
        WHERE
            w.del_flag = 0
          AND w.`status` = 1
          AND w.proj_id = #{info.projId}
          AND w.version = #{info.currentVersion}
        <if test="param.categoryId != null">
            AND cw.proj_category116_id = #{param.categoryId}
            AND cw.del_flag = 0
            AND cw.version = #{info.currentVersion}
        </if>
        <if test="param.containsExerciseSet != null and param.containsExerciseSet.size > 0">
            AND w.exercise_type IN
            <foreach collection="param.containsExerciseSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="(param.categoryTypeSet != null and param.categoryTypeSet.size > 0)
        or (param.workoutTypeSet != null and param.workoutTypeSet.size > 0)
        or (param.difficulty != null)">
            AND (
            <if test="param.categoryTypeSet != null and param.categoryTypeSet.size > 0">
                EXISTS (
                SELECT 1 FROM proj_category116_proj_workout116_pub cw2
                JOIN proj_category116_pub c ON cw2.proj_category116_id = c.id
                WHERE cw2.proj_workout116_id = w.id
                AND cw2.del_flag = 0
                AND cw2.version = #{info.currentVersion}
                AND c.del_flag = 0
                AND c.version = #{info.currentVersion}
                AND c.type IN
                <foreach collection="param.categoryTypeSet" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="param.categoryTypeSet != null and param.categoryTypeSet.size > 0
                  and ((param.workoutTypeSet != null and param.workoutTypeSet.size > 0)
                       or param.difficulty != null)">
                OR
            </if>

            <if test="param.workoutTypeSet != null and param.workoutTypeSet.size > 0">
                w.exercise_type IN
                <foreach collection="param.workoutTypeSet" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="(param.workoutTypeSet != null and param.workoutTypeSet.size > 0)
                  and param.difficulty != null">
                OR
            </if>

            <if test="param.difficulty != null">
                w.difficulty = #{param.difficulty}
            </if>
            )
        </if>
        <choose>
            <when test="param.orderBy != null and param.orderBy=='Time'">
                ORDER BY w.duration, w.id DESC
            </when>
            <otherwise>
                <if test="param.categoryId == null">
                    ORDER BY w.id DESC
                </if>
                <if test="param.categoryId != null">
                    ORDER BY cw.id
                </if>
            </otherwise>
        </choose>
        <if test="param.limit != null">
            LIMIT #{param.limit}
        </if>
    </select>

    <select id="queryDetailByIdList" resultMap="ProjWorkout116DetailVOMap">
        SELECT
            w.id,
            w.`name`,
            w.event_name,
            w.cover_img_url,
            w.detail_img_url,
            w.difficulty,
            w.position,
            w.restriction,
            w.description,
            w.calorie,
            w.duration,
            w.subscription,
            w.new_start_time,
            w.new_end_time,
            w.video_url,
            w.video2532_url,
            w.audio_json_url,
            w.audio_json as audioJsonList,
            w.equipment,
            w.gender,
            w.exercise_type,
            w.region,
            w.focus,
            w.support_prop
        FROM
            proj_workout116_pub w
        WHERE
            w.del_flag = 0
            AND w.`status` = 1
        <foreach collection="idList" item="id" open="AND w.id in (" close=")" separator=",">
            #{id}
        </foreach>
          AND w.version = #{info.currentVersion}
    </select>

    <select id="queryVideoByIdList" resultMap="ResVideo116VOMap">
        SELECT
            v.id,
            v.`name`,
            v.event_name,
            v.cover_img_url,
            v.type,
            v.position,
            v.restriction,
            v.instructions,
            (v.front_duration + v.side_duration) as previewDuration,
            (2* v.front_duration + v.side_duration) AS duration,
            v.video_url,
            v.video2532_url,
            v.name_audio_url,
            v.guidance_audio_url,
            v.guidance,
            v.instructions_audio_url,
            v.met,
            v.calorie,
            v.exercise_type,
            v.gender,
            v.region,
            v.focus,
            v.support_prop,
            v.core_voice_config_i18n_id,
            wv.id unitId,
            wv.proj_workout116_id AS workoutId,
            wv.circuit,
            wv.preview_duration AS generatedPreviewDuration,
            wv.res_video_duration AS generatedVideoDuration,
            wv.circuit_video_duration AS circuitVideoDuration,
            wv.unit_name,
            wv.rounds
        FROM
            proj_workout116_res_video116_pub wv
        INNER JOIN res_video116 v ON v.id = wv.res_video116_id
        WHERE
            wv.del_flag = 0
          AND v.del_flag = 0
          AND wv.version = #{info.currentVersion}
        <foreach collection="idList" item="id" open="AND wv.proj_workout116_id IN (" close=")" separator=",">
            #{id}
        </foreach>
        order by wv.id
    </select>

    <select id="queryWorkoutCategories" resultType="com.laien.cmsapp.oog116.response.ProjWorkout116CategoriesVO">
        SELECT
            w.id as id,
            GROUP_CONCAT(c.id SEPARATOR ',') as categoryIds,
            GROUP_CONCAT(c.`name` SEPARATOR ',') as categoryNames,
            GROUP_CONCAT(c.`type` SEPARATOR ',') as categoryTypes
        FROM
            proj_workout116_pub w
        INNER JOIN proj_category116_proj_workout116_pub cw ON w.id = cw.proj_workout116_id
        INNER JOIN proj_category116_pub c ON cw.proj_category116_id = c.id
        WHERE
            cw.del_flag = 0
          AND c.del_flag = 0
          AND c.status = 1
          AND w.proj_id = #{info.projId}
          AND w.version = #{info.currentVersion}
          AND cw.version = #{info.currentVersion}
          AND c.version = #{info.currentVersion}
        <foreach collection="idList" item="id" open="AND w.id IN (" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY w.id
    </select>

</mapper>
