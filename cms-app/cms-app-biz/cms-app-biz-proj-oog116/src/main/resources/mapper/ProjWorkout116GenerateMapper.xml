<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog116.mapper.ProjWorkout116GenerateMapper">

    <select id="query" resultType="com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO">
        SELECT
            wg.id,
            wg.position,
            wg.restriction,
            wg.restriction_sum,
            wg.calorie,
            wg.duration,
            wg.video_url,
            wg.video2532_url,
            wg.audio_json_url,
            wg.equipment,
            wg.gender,
            wg.exercise_type,
            CONCAT(i.name,'_',wg.id) AS eventName,
            i.id as imageId,
            i.name,
            i.description,
            i.detail_image AS detailImgUrl,
            i.detail_image_male AS detailImgMaleUrl,
            i.cover_image_male AS coverImgMaleUrl,
            i.cover_image AS coverImgUrl
        FROM
            proj_workout116_generate wg
            JOIN res_image i ON wg.res_image_id = i.id
        WHERE
            wg.proj_template116_id = #{templateId}
          AND wg.restriction_sum = #{restrictionSum}
          <if test="dataVersion == 0">
            AND wg.data_version = #{dataVersion}
          </if>
          <if test="dataVersion != 0">
            AND wg.del_flag = 0
          </if>
    </select>

    <select id="queryByTemplateIds" resultType="com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO">
        SELECT
        wg.id,
        wg.position,
        wg.restriction,
        wg.restriction_sum,
        wg.calorie,
        wg.duration,
        wg.video_url,
        wg.video2532_url,
        wg.audio_json_url,
        wg.equipment,
        wg.gender,
        wg.exercise_type,
        CONCAT(i.name,'_',wg.id) AS eventName,
        i.id as imageId,
        i.name,
        i.description,
        i.detail_image AS detailImgUrl,
        i.detail_image_male AS detailImgMaleUrl,
        i.cover_image_male AS coverImgMaleUrl,
        i.cover_image AS coverImgUrl
        FROM
        proj_workout116_generate wg
        JOIN res_image i ON wg.res_image_id = i.id
        WHERE
        wg.proj_template116_id IN
        <foreach collection="templateIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND wg.restriction_sum = #{restrictionSum}
        <if test="dataVersion == 0">
            AND wg.data_version = #{dataVersion}
        </if>
        <if test="dataVersion != 0">
            AND wg.del_flag = 0
        </if>
    </select>

    <select id="queryVideoList" resultType="com.laien.cmsapp.oog116.response.ResVideo116VO">
        SELECT
            wg.id AS workoutId,
            v.id,
            v.`name`,
            v.event_name,
            v.cover_img_url,
            v.type,
            v.position,
            v.restriction,
            v.instructions,
            v.video_url,
            v.guidance,
            v.video2532_url,
            v.name_audio_url,
            v.guidance_audio_url,
            v.instructions_audio_url,
            v.core_voice_config_i18n_id,
            wgv.circuit,
            (2*v.front_duration + v.side_duration) AS duration,
            (v.front_duration + v.side_duration) AS previewDuration,
            wgv.preview_duration AS generatedPreviewDuration,
            wgv.res_video_duration AS generatedVideoDuration,
            wgv.circuit_video_duration AS circuitVideoDuration,
            wgv.proj_template116_rule_id AS ruleId
        FROM
            proj_workout116_generate wg
                JOIN proj_workout116_generate_res_video116 wgv ON wgv.proj_workout116_generate_id = wg.id
                JOIN res_video116 v ON v.id = wgv.res_video116_id
        WHERE
            wg.proj_template116_id = #{templateId}
          AND wg.restriction_sum = #{restrictionSum}
        ORDER BY
            wgv.id
    </select>
    <select id="findByIdList" resultType="com.laien.cmsapp.oog116.response.ProjWorkout116DetailVO">
        SELECT
            wg.id,
            wg.position,
            wg.restriction,
            wg.restriction_sum,
            wg.calorie,
            wg.duration,
            wg.video_url,
            wg.video2532_url,
            wg.audio_json_url,
            wg.equipment,
            wg.gender,
            wg.exercise_type,
            CONCAT(i.name,'_',wg.id) AS eventName,
            i.id as imageId,
            i.name,
            i.description,
            i.detail_image AS detailImgUrl,
            i.detail_image_male AS detailImgMaleUrl,
            i.cover_image_male AS coverImgMaleUrl,
            i.cover_image AS coverImgUrl
        FROM
            proj_workout116_generate wg
                JOIN res_image i ON wg.res_image_id = i.id
        WHERE
            wg.id IN
                <foreach collection="idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
        <if test="dataVersion == 0">
            AND wg.data_version = #{dataVersion}
        </if>
        <if test="dataVersion != 0">
            AND wg.del_flag = 0
        </if>
    </select>
    <select id="queryVideoListByIds" resultType="com.laien.cmsapp.oog116.response.ResVideo116VO">
        SELECT
            wg.id AS workoutId,
            v.id,
            v.`name`,
            v.event_name,
            v.cover_img_url,
            v.type,
            v.position,
            v.restriction,
            v.instructions,
            v.guidance,
            v.video_url,
            v.video2532_url,
            v.name_audio_url,
            v.guidance_audio_url,
            v.instructions_audio_url,
            v.core_voice_config_i18n_id,
            v.gender,
            wgv.circuit,
            (v.front_duration + v.side_duration) AS previewDuration,
            (2*v.front_duration + v.side_duration) AS duration,
            wgv.preview_duration AS generatedPreviewDuration,
            wgv.res_video_duration AS generatedVideoDuration,
            wgv.circuit_video_duration AS circuitVideoDuration,
            wgv.proj_template116_rule_id AS ruleId
        FROM
            proj_workout116_generate wg
                JOIN proj_workout116_generate_res_video116 wgv ON wgv.proj_workout116_generate_id = wg.id
                JOIN res_video116 v ON v.id = wgv.res_video116_id
        WHERE
            wg.id IN
                <foreach collection="idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
        ORDER BY
            wgv.id
    </select>
</mapper>
