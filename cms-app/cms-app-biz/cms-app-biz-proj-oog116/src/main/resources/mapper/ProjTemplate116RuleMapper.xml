<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog116.mapper.ProjTemplate116RuleMapper">

    <select id="find" resultType="com.laien.cmsapp.oog116.entity.ProjTemplate116Rule">
        SELECT
            id,
            proj_template116_id,
            unit_name,
            video_type,
            count,
            rounds
        FROM proj_template116_rule WHERE
        id IN
        <foreach collection="idSet" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        ORDER BY id
    </select>
</mapper>
