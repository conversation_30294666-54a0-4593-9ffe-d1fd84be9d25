package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2025/1/7 11:32
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjIngredient对象", description="ingredient,配料")
public class ProjFitnessIngredientVO  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "数量")
    private String amount;

    @JsonIgnore
    @ApiModelProperty(value = "proj_unit表数据id")
    private Integer projFitnessUnitId;

    @ApiModelProperty(value = "单位名")
    private String unitName;

}
