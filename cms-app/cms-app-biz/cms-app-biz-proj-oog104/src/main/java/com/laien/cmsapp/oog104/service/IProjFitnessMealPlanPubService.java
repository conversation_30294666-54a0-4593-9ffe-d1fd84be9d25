package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessMealPlanPub;
import com.laien.cmsapp.oog104.response.ProjFitnessMealPlanDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessMealPlanListVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:54
 */
public interface IProjFitnessMealPlanPubService extends IService<ProjFitnessMealPlanPub> {

    List<ProjFitnessMealPlanListVO> listMealPlan(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang);

    ProjFitnessMealPlanDetailVO getMealPlanDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer mealPlanId, String lang);

}
