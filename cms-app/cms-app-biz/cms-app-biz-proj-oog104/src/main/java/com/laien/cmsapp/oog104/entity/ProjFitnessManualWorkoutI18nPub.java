package com.laien.cmsapp.oog104.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessManualWorkoutI18nPub 多语言表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessManualWorkoutI18n对象", description="ProjFitnessManualWorkoutI18nPub 多语言表")
public class ProjFitnessManualWorkoutI18nPub extends BaseModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "workout id")
    private Integer projFitnessManualWorkoutId;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "项目id")
    private Integer projId;
}
