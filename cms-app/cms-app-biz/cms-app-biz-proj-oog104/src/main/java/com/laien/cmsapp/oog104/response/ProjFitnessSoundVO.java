package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.oog104.entity.ProjFitnessSound;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "Sound VO", description = "Sound VO")
public class ProjFitnessSoundVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "文件地址")
    private String soundUrl;

    @ApiModelProperty(value = "文件名称")
    private String soundUrlName;

    public ProjFitnessSoundVO(ProjFitnessSound projFitnessSound) {
        this.id = projFitnessSound.getId();
        this.soundName = projFitnessSound.getSoundName();
        this.soundScript = projFitnessSound.getSoundScript();
        this.soundUrl = projFitnessSound.getUrl();

    }
}
