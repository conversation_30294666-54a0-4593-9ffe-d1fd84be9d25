package com.laien.cmsapp.oog104.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjFitnessDishStepPub extends BaseModel {

    private Integer version;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projFitnessDishId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
