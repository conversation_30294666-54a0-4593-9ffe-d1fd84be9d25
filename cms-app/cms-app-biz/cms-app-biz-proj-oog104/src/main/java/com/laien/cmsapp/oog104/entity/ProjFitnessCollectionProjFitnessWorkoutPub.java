package com.laien.cmsapp.oog104.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_collection_proj_fitness_workout_pub
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCollectionProjFitnessWorkoutPub对象", description="proj_fitness_collection_proj_fitness_workout_pub")
public class ProjFitnessCollectionProjFitnessWorkoutPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "collection id")
    private Integer projFitnessCollectionId;

    @ApiModelProperty(value = "workout id")
    private Integer projFitnessWorkoutId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
