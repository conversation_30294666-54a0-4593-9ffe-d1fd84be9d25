package com.laien.cmsapp.oog104.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog104.entity.ProjFitnessVideoCoursePub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessVideoCoursePubMapper;
import com.laien.cmsapp.oog104.service.IProjFitnessVideoCoursePubService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Fitness VideoCoursePub 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessVideoCoursePubServiceImpl extends ServiceImpl<ProjFitnessVideoCoursePubMapper, ProjFitnessVideoCoursePub> implements IProjFitnessVideoCoursePubService {

}
