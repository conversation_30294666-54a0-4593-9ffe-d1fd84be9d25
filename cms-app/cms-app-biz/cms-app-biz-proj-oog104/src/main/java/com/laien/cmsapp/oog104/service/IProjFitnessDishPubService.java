package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishPub;
import com.laien.cmsapp.oog104.response.ProjFitnessDishDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishListVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:30
 */
public interface IProjFitnessDishPubService extends IService<ProjFitnessDishPub> {

    /**
     * 获取 dish list
     *
     * @param versionInfoBO
     * @param dishIds
     * @param status
     * @param lang
     * @return
     */
    List<ProjFitnessDishListVO> listDish(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> dishIds, Integer status, String lang);

    /**
     * 获取 dish detail
     *
     * @param versionInfoBO
     * @param dishId
     * @param lang
     * @return
     */
    ProjFitnessDishDetailVO getDishDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang, Integer m3u8Type);

}
