package com.laien.cmsapp.oog104.mapstruct;

import com.laien.cmsapp.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.cmsapp.oog104.response.ProjFitnessRefreshVideoVO;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_fitness_exercise_video mapstruct
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Mapper(componentModel = "spring")
public interface  ProjFitnessExerciseVideoMapStruct {

    List<ProjFitnessRefreshVideoVO> toRefreshVideoVO(Collection<ProjFitnessExerciseVideo> exerciseVideos);
}
