package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessIngredientPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessUnitPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessIngredientPubMapper;
import com.laien.cmsapp.oog104.response.ProjFitnessIngredientVO;
import com.laien.cmsapp.oog104.service.IProjFitnessIngredientPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessUnitPubService;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:42
 */
@Service
public class ProjFitnessIngredientPubServiceImpl extends ServiceImpl<ProjFitnessIngredientPubMapper, ProjFitnessIngredientPub> implements IProjFitnessIngredientPubService {

    @Resource
    private IProjFitnessUnitPubService unitPubService;

    @Override
    public List<ProjFitnessIngredientVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang) {

        List<ProjFitnessIngredientPub> ingredientPubList = listByVersionAndDishId(versionInfoBO, dishId);
        if (CollectionUtils.isEmpty(ingredientPubList)) {
            return Collections.emptyList();
        }

        List<ProjFitnessIngredientVO> ingredientVOList = ingredientPubList.stream().map(this::convert2VO).collect(Collectors.toList());
        List<Integer> unitIds = ingredientPubList.stream().map(ProjFitnessIngredientPub::getProjFitnessUnitId).collect(Collectors.toList());
        List<ProjFitnessUnitPub> unitPubList = unitPubService.listByVersionAndIds(versionInfoBO, unitIds);
        if (CollectionUtils.isEmpty(unitPubList)) {
            return ingredientVOList;
        }

        // set unit name
        Map<Integer, String> unitMap = unitPubList.stream().collect(Collectors.toMap(BaseModel::getId, ProjFitnessUnitPub::getName, (k1, k2) -> k1));
        ingredientVOList.forEach(ingredient -> ingredient.setUnitName(unitMap.get(ingredient.getProjFitnessUnitId())));
        return ingredientVOList;
    }

    private ProjFitnessIngredientVO convert2VO(ProjFitnessIngredientPub ProjFitnessIngredientPub) {

        ProjFitnessIngredientVO projFitnessIngredientVO = new ProjFitnessIngredientVO();
        BeanUtils.copyProperties(ProjFitnessIngredientPub, projFitnessIngredientVO);
        return projFitnessIngredientVO;
    }

    private List<ProjFitnessIngredientPub> listByVersionAndDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId) {

        LambdaQueryWrapper<ProjFitnessIngredientPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessIngredientPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessIngredientPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjFitnessIngredientPub::getProjFitnessDishId, dishId);
        return list(queryWrapper);
    }
}
