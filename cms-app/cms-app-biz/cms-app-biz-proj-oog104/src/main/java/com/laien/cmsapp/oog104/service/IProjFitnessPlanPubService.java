package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog104.entity.ProjFitnessPlanPub;
import com.laien.cmsapp.oog104.request.ProjFitnessPlanDetailReq;
import com.laien.cmsapp.requst.LangReq;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanListVO;

import java.util.List;

/**
 * <p>
 * proj_fitness_plan_pub 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface IProjFitnessPlanPubService extends IService<ProjFitnessPlanPub> {

    /**
     * 查询plan列表
     *
     * @param langReq langReq
     * @return list
     */
    List<ProjFitnessPlanListVO> selectPlanlist(LangReq langReq);

    /**
     * 查询plan详情
     *
     * @param id id
     * @param detailReq detailReq
     * @return ProjFitnessPlanDetailVO
     */
    ProjFitnessPlanDetailVO selectPlanDetail(Integer id, ProjFitnessPlanDetailReq detailReq);
}
