package com.laien.cmsapp.oog104.response;

import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness refresh workout unit", description = "Fitness refresh workout unit")
public class ProjFitnessRefreshWorkoutDetailUnitVO {

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "单元名称类型")
    private ManualTypeEnums unitNameType;

    @ApiModelProperty(value = "动作数量")
    private Integer count;

    @ApiModelProperty(value = "循环次数")
    private Integer rounds = 1;

    @ApiModelProperty(value = "video list")
    private List<ProjFitnessRefreshVideoVO> videoList;

}
