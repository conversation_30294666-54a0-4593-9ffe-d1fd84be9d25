package com.laien.cmsapp.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness transcript list", description = "Fitness transcript list")
public class ProjFitnessTranscriptVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "plan类型code")
    private Integer typeCode;

    @ApiModelProperty(value = "ob code,1:lose weight,2:be more active,3:build strength,4:relieve stress,5:learn the basics ")
    private Integer obCode;

    @ApiModelProperty(value = "tags")
    private List<String> tags;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "预期结果")
    private List<String> expectedResults;

}
