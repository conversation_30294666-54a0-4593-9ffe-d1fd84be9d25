package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness plan detail", description = "Fitness plan detail")
public class ProjFitnessPlanDetailVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "plan类型code 1:Full body, 2:Lose weight, 3:Wall pilates")
    private Integer typeCode;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "字体颜色")
    private String fontColor;

    @ApiModelProperty(value = "背景颜色")
    private String bgColor;

    @ApiModelProperty(value = "tags")
    private List<String> tags;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "预期结果")
    private List<String> expectedResults;

    @ApiModelProperty(value = "stage list")
    private List<ProjFitnessPlanDetailStageVO> stageList;



}
