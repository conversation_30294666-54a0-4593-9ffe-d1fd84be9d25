package com.laien.cmsapp.oog104.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog104.entity.ProjFitnessSound;
import com.laien.cmsapp.oog104.mapper.ProjFitnessSoundMapper;
import com.laien.cmsapp.oog104.request.ProjFitnessSoundReq;
import com.laien.cmsapp.oog104.response.ProjFitnessSoundVO;
import com.laien.cmsapp.oog104.service.IProjFitnessSoundService;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.component.AudioTranslateResultModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog104.enums.FitnessGenderEnums;
import com.laien.common.util.FireBaseUrlSubUtils;
import com.laien.common.util.RequestContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessSoundServiceImpl extends ServiceImpl<ProjFitnessSoundMapper, ProjFitnessSound>
        implements IProjFitnessSoundService {

    @Autowired
    private final IProjLmsI18nService projLmsI18nService;
    @Autowired
    private final FileService fileService;

    /**
     * 按照类型，子类型 , 性别查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    @Override
    public List<ProjFitnessSoundVO> selectSoundList(ProjFitnessSoundReq soundReq) {
        if (Objects.isNull(soundReq.getSoundType())) {
            log.error("The soundReq is null");
            return new ArrayList<>();
        }
        //兜底没有传 gender 时,默认给 female
        if (Objects.isNull(soundReq.getGender())) {
            soundReq.setGender(FitnessGenderEnums.FEMALE);
        }
        List<ProjFitnessSoundVO> result = new ArrayList<>();
        LambdaQueryWrapper<ProjFitnessSound> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(soundReq.getSoundType()), ProjFitnessSound::getSoundType, soundReq.getSoundType())
                .eq(ObjUtil.isNotNull(soundReq.getSoundSubType()), ProjFitnessSound::getSoundSubType, soundReq.getSoundSubType())
                .eq(ObjUtil.isNotNull(soundReq.getGender()), ProjFitnessSound::getGender, soundReq.getGender())
                .eq(ProjFitnessSound::getDelFlag, 0)
                .eq(ProjFitnessSound::getStatus, 1);
        query.orderByDesc(ProjFitnessSound::getId);
        List<ProjFitnessSound> soundList = baseMapper.selectList(query);
        if (soundList == null || soundList.isEmpty()) {
            log.error("The soundList is null");
            return result;
        }
        String lang = RequestContextUtils.getLanguage();
        List<ProjFitnessSound> needI18nList = soundList.stream().filter(ProjFitnessSound::getNeedTranslation).collect(Collectors.toList());
        projLmsI18nService.handleSpeechI18nSingle(CollUtil.newArrayList(needI18nList), ProjCodeEnums.OOG104,lang);
        result = soundList.stream()
                .map(projFitnessSound -> {
                    List<AudioTranslateResultModel> i18nResults = projFitnessSound.getResult();
                    AudioTranslateResultModel i18nResult;
                    if (CollUtil.isNotEmpty(i18nResults) && (i18nResult = i18nResults.get(0)) != null) {
                        ProjFitnessSoundVO fitnessSoundVO = new ProjFitnessSoundVO(projFitnessSound);
                        fitnessSoundVO.setSoundUrlName(FireBaseUrlSubUtils.getFileName(i18nResult.getAudioUrl()));
                        fitnessSoundVO.setSoundUrl(fileService.getAbsoluteUrl(i18nResult.getAudioUrl()));
                        fitnessSoundVO.setSoundScript(i18nResult.getText());
                        return fitnessSoundVO;
                    } else {
                        ProjFitnessSoundVO fitnessSoundVO = new ProjFitnessSoundVO(projFitnessSound);
                        fitnessSoundVO.setSoundUrlName(FireBaseUrlSubUtils.getFileName(projFitnessSound.getUrl()));
                        fitnessSoundVO.setSoundUrl(fileService.getAbsoluteUrl(projFitnessSound.getUrl()));
                        return fitnessSoundVO;
                    }
                })
                .collect(Collectors.toList());
        return result;
    }

}
