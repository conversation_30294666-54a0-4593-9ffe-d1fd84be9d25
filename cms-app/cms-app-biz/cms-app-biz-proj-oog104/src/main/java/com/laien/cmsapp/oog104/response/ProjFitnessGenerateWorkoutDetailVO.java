package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class ProjFitnessGenerateWorkoutDetailVO extends ProjFitnessRefreshWorkoutDetailVO {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("封面图片地址")
    @AbsoluteR2Url
    private String coverImage;

    @ApiModelProperty("详情图片地址")
    @AbsoluteR2Url
    private String detailImage;

    @ApiModelProperty("imgId")
    private Integer imgId;
}
