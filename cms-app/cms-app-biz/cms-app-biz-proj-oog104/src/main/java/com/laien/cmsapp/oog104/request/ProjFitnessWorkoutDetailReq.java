package com.laien.cmsapp.oog104.request;

import com.laien.cmsapp.requst.LangReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: workout 详情
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout 详情", description = "workout 详情")
public class ProjFitnessWorkoutDetailReq extends LangReq {

    @ApiModelProperty(value = "1：查询大切片 m3u8; 2：查询小切片（dynamic）m3u8 默认为1")
    Integer m3u8Type;

}
