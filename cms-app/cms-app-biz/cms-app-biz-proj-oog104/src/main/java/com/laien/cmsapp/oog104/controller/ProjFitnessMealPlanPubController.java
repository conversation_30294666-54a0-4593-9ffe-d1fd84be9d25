package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.response.ProjFitnessMealPlanDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessMealPlanListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessMealPlanPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 20:42
 */
@Api(tags = "app端：Fitness MealPlan")
@RestController
@RequestMapping("/{appCode}/fitnessMealPlan")
public class ProjFitnessMealPlanPubController extends ResponseController {

    @Resource
    IProjFitnessMealPlanPubService mealPlanPubService;

    @ApiOperation(value = "获取meal plan 列表, 只获取状态为enable的数据", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessMealPlanListVO>> listMealPlan(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjFitnessMealPlanListVO> mealPlanListVOS = mealPlanPubService.listMealPlan(versionInfoBO, GlobalConstant.STATUS_ENABLE, lang);
        return succ(mealPlanListVOS);
    }

    @ApiOperation(value = "获取一个mealPlan下的Dish列表", tags = {"oog104"})
    @GetMapping("/v1/detail")
    public ResponseResult<ProjFitnessMealPlanDetailVO> listDailyDish(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang,
                                                                     @RequestParam Integer mealPlanId,
                                                                     @RequestParam Integer code) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjFitnessMealPlanDetailVO mealPlanDetailVO = mealPlanPubService.getMealPlanDetail(versionInfoBO, mealPlanId, lang);
        return succ(mealPlanDetailVO);
    }

}
