package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness workout detail", description = "Fitness workout detail")
public class ProjFitnessWorkoutDetailVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面视频地址")
    private String coverVideoUrl;

    @ApiModelProperty(value = "封面视频时长")
    private Integer coverVideoDuration;

    @ApiModelProperty(value = "扩展标签")
    private List<Integer> extraTagCodes;

    @ApiModelProperty(value = "时长/分钟")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @AbsoluteR2Url
    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json list")
    private List<ProjFitnessWorkoutAudioVO> audioJsonList;

    @ApiModelProperty(value = "unit list")
    private List<ProjFitnessWorkoutDetailUnitVO> unitList;

}
