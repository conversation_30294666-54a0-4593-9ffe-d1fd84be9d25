package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishStepTipPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessDishStepTipPubMapper;
import com.laien.cmsapp.oog104.response.ProjFitnessDishStepTipVO;
import com.laien.cmsapp.oog104.service.IProjFitnessDishStepTipPubService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/8 16:43
 */
@Service
public class ProjFitnessDishStepTipPubServiceImpl extends ServiceImpl<ProjFitnessDishStepTipPubMapper, ProjFitnessDishStepTipPub> implements IProjFitnessDishStepTipPubService {

    @Override
    public List<ProjFitnessDishStepTipVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang) {

        LambdaQueryWrapper<ProjFitnessDishStepTipPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessDishStepTipPub::getProjFitnessDishId, dishId);
        queryWrapper.eq(ProjFitnessDishStepTipPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessDishStepTipPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.orderByAsc(ProjFitnessDishStepTipPub::getId);

        List<ProjFitnessDishStepTipPub> dishStepTipPubList = list(queryWrapper);
        if (CollectionUtils.isEmpty(dishStepTipPubList)) {
            return Collections.emptyList();
        }
        return dishStepTipPubList.stream().map(this::convert2VO).collect(Collectors.toList());
    }

    private ProjFitnessDishStepTipVO convert2VO(ProjFitnessDishStepTipPub ProjFitnessDishStepTipPub) {

        ProjFitnessDishStepTipVO projFitnessDishStepTipVO = new ProjFitnessDishStepTipVO();
        BeanUtils.copyProperties(ProjFitnessDishStepTipPub, projFitnessDishStepTipVO);
        return projFitnessDishStepTipVO;
    }
}
