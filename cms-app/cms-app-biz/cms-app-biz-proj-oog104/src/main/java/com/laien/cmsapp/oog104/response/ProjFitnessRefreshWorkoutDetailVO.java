package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualEquipmentEnums;
import com.laien.common.oog104.enums.manual.ManualIntensityEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjFitnessRefreshWorkoutDetailVO extends BaseTableCodeVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "Event Name, auto-generated")
    private String eventName;

    @ApiModelProperty(value = "target code")
    private List<ManualTargetEnums> target;

    @ApiModelProperty(value = "difficulty code")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "equipment code")
    private List<ManualEquipmentEnums> equipment;

    @ApiModelProperty(value = "特殊限制code (多选, 逗号分隔)")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "组成的Exercise的Intensity包含的合集 , 多个用英文逗号分隔")
    private List<ManualIntensityEnums> intensity;

    @ApiModelProperty(value = "时长,毫秒")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @AbsoluteR2Url
    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "audio json list")
    private List<AudioI18nVO> audioJsonList;

    @ApiModelProperty(value = "unit list")
    private List<ProjFitnessRefreshWorkoutDetailUnitVO> unitList;


}
