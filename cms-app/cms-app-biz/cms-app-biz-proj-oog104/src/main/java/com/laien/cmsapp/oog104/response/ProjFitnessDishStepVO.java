package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 11:25
 */
@Data
@ApiModel(value="dish step对象", description="dish step")
public class ProjFitnessDishStepVO  implements AppTextCoreI18nModel {

    @JsonIgnore
    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "描述")
    @JsonProperty(value = "desc")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "tip list")
    private List<ProjFitnessDishStepTipVO> tipList = new ArrayList<>();

}
