package com.laien.cmsapp.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishCollectionRelationPub;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:48
 */
public interface ProjFitnessDishCollectionRelationPubMapper extends BaseMapper<ProjFitnessDishCollectionRelationPub> {

    @Select(value = "select id, version, proj_fitness_dish_collection_id, dish_type, proj_fitness_dish_id from proj_fitness_dish_collection_relation_pub where version = #{version} and proj_fitness_dish_collection_id = #{dishCollectionId} and del_flag = 0")
    List<ProjFitnessDishCollectionRelationPub> listByVersionAndDishCollectionId(Integer version, Integer dishCollectionId);


}
