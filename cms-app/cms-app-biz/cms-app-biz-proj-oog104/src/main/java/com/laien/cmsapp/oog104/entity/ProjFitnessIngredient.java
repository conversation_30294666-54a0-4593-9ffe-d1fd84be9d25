package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * ingredient
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_fitness_ingredient")
@ApiModel(value="ProjIngredient对象", description="ingredient")
public class ProjFitnessIngredient extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private String amount;

    @ApiModelProperty(value = "proj_unit表数据id")
    private Integer projFitnessUnitId;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projFitnessDishId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
