package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ProjFitnessCoachingCoursesVO
 * <AUTHOR>
 * @since 2025/04/18
 */
@Data
@ApiModel(value = "ProjFitnessCoachingCoursesVO", description = "ProjFitnessCoachingCoursesVO")
public class ProjFitnessCoachingCoursesVO extends BaseTableCodeVO{

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "Home 竖版Image")
    @AbsoluteR2Url
    private String courseImgUrl;

    @ApiModelProperty(value = "ageGroups")
    private List<ManualAgeGroupEnums> ageGroups;

    @ApiModelProperty(value = "types")
    private List<FitnessCourseTypeEnums> types;

    @ApiModelProperty(value = "difficulty")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "projFitnessCoachId")
    private Integer projFitnessCoachId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "排序")
    private Integer sorted;

    @ApiModelProperty(value = "courseCount")
    private Integer courseCount;

    @ApiModelProperty(value = "最大时长")
    private Integer maxDuration;

    @ApiModelProperty(value = "最小时长")
    private Integer minDuration;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
