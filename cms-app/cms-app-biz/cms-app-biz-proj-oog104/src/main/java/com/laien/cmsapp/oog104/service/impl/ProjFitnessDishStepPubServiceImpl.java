package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishStepPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessDishStepPubMapper;
import com.laien.cmsapp.oog104.response.ProjFitnessDishStepTipVO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishStepVO;
import com.laien.cmsapp.oog104.service.IProjFitnessDishStepPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessDishStepTipPubService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:23
 */
@Service
public class ProjFitnessDishStepPubServiceImpl extends ServiceImpl<ProjFitnessDishStepPubMapper, ProjFitnessDishStepPub> implements IProjFitnessDishStepPubService {

    @Resource
    private IProjFitnessDishStepTipPubService dishStepTipPubService;

    @Override
    public List<ProjFitnessDishStepVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang) {

        LambdaQueryWrapper<ProjFitnessDishStepPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessDishStepPub::getProjFitnessDishId, dishId);
        queryWrapper.eq(ProjFitnessDishStepPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessDishStepPub::getProjId, versionInfoBO.getProjId());

        List<ProjFitnessDishStepPub> dishStepPubList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(dishStepPubList)) {
            return Collections.emptyList();
        }

        List<ProjFitnessDishStepVO> dishStepVOList = dishStepPubList.stream().map(stepPub -> convert2VO(stepPub)).collect(Collectors.toList());
        List<ProjFitnessDishStepTipVO> dishStepTipVOList = dishStepTipPubService.listByDishId(versionInfoBO, dishId, lang);
        if (CollectionUtils.isEmpty(dishStepTipVOList)) {
            return dishStepVOList;
        }

        Map<Integer, List<ProjFitnessDishStepTipVO>> stepIdAndTipMap = dishStepTipVOList.stream().collect(Collectors.groupingBy(ProjFitnessDishStepTipVO::getProjFitnessDishStepId));
        dishStepVOList.forEach(stepVO -> stepVO.setTipList(stepIdAndTipMap.get(stepVO.getId())));
        return dishStepVOList;
    }

    private ProjFitnessDishStepVO convert2VO(ProjFitnessDishStepPub stepPub) {

        ProjFitnessDishStepVO stepVO = new ProjFitnessDishStepVO();
        BeanUtils.copyProperties(stepPub, stepVO);
        return stepVO;
    }

}
