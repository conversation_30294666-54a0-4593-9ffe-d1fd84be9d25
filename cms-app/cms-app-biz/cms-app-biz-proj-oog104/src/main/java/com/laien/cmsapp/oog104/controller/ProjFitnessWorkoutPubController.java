package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.oog104.enums.M3u8TypeEnums;
import com.laien.cmsapp.oog104.request.ProjFitnessWorkoutDetailReq;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutDetailVO;
import com.laien.cmsapp.oog104.service.IProjFitnessWorkoutPubService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * proj_fitness_workout_pub 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Api(tags = "app端：Fitness Workout")
@RestController
@RequestMapping("/oog104/fitnessWorkout")
public class ProjFitnessWorkoutPubController extends ResponseController {

    @Resource
    private IProjFitnessWorkoutPubService projFitnessWorkoutPubService;

    @ApiOperation(value = "Fitness Workout detail", tags = {"oog104"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjFitnessWorkoutDetailVO> detail(@PathVariable Integer id, ProjFitnessWorkoutDetailReq detailReq) {
        Integer m3u8Type = detailReq.getM3u8Type();
        if (m3u8Type == null) {
            detailReq.setM3u8Type(M3u8TypeEnums.M3U8.getCode());
        }

        List<ProjFitnessWorkoutDetailVO> detailVOList = projFitnessWorkoutPubService.selectDetailByIds(Collections.singletonList(id), m3u8Type);
        ProjFitnessWorkoutDetailVO detailVO = !detailVOList.isEmpty() ? detailVOList.get(GlobalConstant.ZERO) : null;
        return succ(detailVO);
    }

}
