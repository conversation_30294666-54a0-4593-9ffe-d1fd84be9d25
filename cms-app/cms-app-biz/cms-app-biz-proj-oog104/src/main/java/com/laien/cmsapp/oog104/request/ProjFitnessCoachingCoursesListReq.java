package com.laien.cmsapp.oog104.request;

import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/18
 */
@Data
@ApiModel(value = "ProjFitnessCoachingCoursesListReq", description = "ProjFitnessCoachingCoursesListReq")
public class ProjFitnessCoachingCoursesListReq {

    @ApiModelProperty(value = "types")
    private List<FitnessCourseTypeEnums> types;
}

