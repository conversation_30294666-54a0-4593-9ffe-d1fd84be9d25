package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishCollectionPub;
import com.laien.cmsapp.oog104.response.ProjFitnessDishCollectionDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishCollectionListVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:54
 */
public interface IProjFitnessDishCollectionPubService extends IService<ProjFitnessDishCollectionPub> {

    List<ProjFitnessDishCollectionListVO> listDishCollection(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang);

    ProjFitnessDishCollectionDetailVO getDishCollectionDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishPlanId, String lang);

}
