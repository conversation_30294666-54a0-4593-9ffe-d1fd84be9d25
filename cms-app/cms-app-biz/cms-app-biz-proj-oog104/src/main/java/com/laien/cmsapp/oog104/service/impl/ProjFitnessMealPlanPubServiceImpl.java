package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessMealPlanPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessMealPlanRelationPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessMealPlanPubMapper;
import com.laien.cmsapp.oog104.mapper.ProjFitnessMealPlanRelationPubMapper;
import com.laien.cmsapp.oog104.response.ProjFitnessDailyDishListVO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishListVO;
import com.laien.cmsapp.oog104.response.ProjFitnessMealPlanDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessMealPlanListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessDishPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessMealPlanPubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:08
 */
@Service
public class ProjFitnessMealPlanPubServiceImpl extends ServiceImpl<ProjFitnessMealPlanPubMapper, ProjFitnessMealPlanPub> implements IProjFitnessMealPlanPubService {

    @Resource
    private IProjFitnessDishPubService dishPubService;

    @Resource
    private ProjFitnessMealPlanRelationPubMapper mealPlanRelationPubMapper;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessMealPlanListVO> listMealPlan(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang) {

        List<ProjFitnessMealPlanPub> mealPlanPubList = listByStatusAndVersion(versionInfoBO, status);
        if (CollectionUtils.isEmpty(mealPlanPubList)) {
            return Collections.emptyList();
        }

        setI18n4MealPlan(mealPlanPubList, lang);
        return mealPlanPubList.stream().map(this::convert2ListVO).collect(Collectors.toList());
    }

    private void setI18n4MealPlan(List<ProjFitnessMealPlanPub> mealPlanPubList, String lang) {
        projLmsI18nService.handleTextI18n(mealPlanPubList, ProjCodeEnums.OOG104,lang);
    }

    private ProjFitnessMealPlanListVO convert2ListVO(ProjFitnessMealPlanPub mealPlanPub) {

        ProjFitnessMealPlanListVO projFitnessMealPlanListVO = new ProjFitnessMealPlanListVO();
        BeanUtils.copyProperties(mealPlanPub, projFitnessMealPlanListVO);
        return projFitnessMealPlanListVO;
    }

    private List<ProjFitnessMealPlanPub> listByStatusAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status) {

        LambdaQueryWrapper<ProjFitnessMealPlanPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(status), ProjFitnessMealPlanPub::getStatus, status);
        queryWrapper.eq(ProjFitnessMealPlanPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessMealPlanPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.orderByAsc(ProjFitnessMealPlanPub::getSorted);
        return list(queryWrapper);
    }

    private ProjFitnessMealPlanPub getByIdAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer mealPlanId) {

        LambdaQueryWrapper<ProjFitnessMealPlanPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessMealPlanPub::getId, mealPlanId);
        queryWrapper.eq(ProjFitnessMealPlanPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessMealPlanPub::getProjId, versionInfoBO.getProjId());
        return getOne(queryWrapper);
    }

    @Override
    public ProjFitnessMealPlanDetailVO getMealPlanDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer mealPlanId, String lang) {

        ProjFitnessMealPlanPub mealPlanPub = getByIdAndVersion(versionInfoBO, mealPlanId);
        if (Objects.isNull(mealPlanPub)) {
            return null;
        }

        setI18n4MealPlan(Lists.newArrayList(mealPlanPub), lang);
        ProjFitnessMealPlanDetailVO mealPlanDetailVO = new ProjFitnessMealPlanDetailVO();
        BeanUtils.copyProperties(mealPlanPub, mealPlanDetailVO);

        List<ProjFitnessMealPlanRelationPub> planRelationPubList = mealPlanRelationPubMapper.listByVersionAndMealPlanId(versionInfoBO.getCurrentVersion(), mealPlanId);
        if (CollectionUtils.isEmpty(planRelationPubList)) {
            return mealPlanDetailVO;
        }

        Set<Integer> dishIds = planRelationPubList.stream().map(ProjFitnessMealPlanRelationPub::getProjFitnessDishId).collect(Collectors.toSet());
        List<ProjFitnessDishListVO> dishListVOList = dishPubService.listDish(versionInfoBO, dishIds, null, lang);
        if (CollectionUtils.isEmpty(dishListVOList)) {
            return mealPlanDetailVO;
        }

        Map<Integer, ProjFitnessDishListVO> dishIdMap = dishListVOList.stream().collect(Collectors.toMap(ProjFitnessDishListVO::getId, Function.identity()));
        TreeMap<Integer, List<ProjFitnessMealPlanRelationPub>> dayAndRelationMap = planRelationPubList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjFitnessDishId()))
                .sorted(Comparator.comparing(ProjFitnessMealPlanRelationPub::getDay).thenComparing(ProjFitnessMealPlanRelationPub::getId)).collect(Collectors.groupingBy(ProjFitnessMealPlanRelationPub::getDay, TreeMap::new, Collectors.toList()));

        List<ProjFitnessDailyDishListVO> dailyDishList = Lists.newArrayList();
        dayAndRelationMap.forEach((key, value) -> {
            ProjFitnessDailyDishListVO dailyDishListVO = convert2DailyVO(key, value, dishIdMap);
            if (Objects.nonNull(dailyDishListVO)) {
                dailyDishList.add(dailyDishListVO);
            }
        });

        mealPlanDetailVO.setDailyDishList(dailyDishList);
        return mealPlanDetailVO;
    }

    private ProjFitnessDailyDishListVO convert2DailyVO(Integer day, List<ProjFitnessMealPlanRelationPub> relationPubList, Map<Integer, ProjFitnessDishListVO> dishIdMap) {

        List<ProjFitnessDishListVO> dishListVOS = relationPubList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjFitnessDishId()))
                .map(relation -> setType4Dish(dishIdMap.get(relation.getProjFitnessDishId()), relation)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dishListVOS)) {
            return null;
        }

        ProjFitnessDailyDishListVO dailyDishListVO = new ProjFitnessDailyDishListVO();
        dailyDishListVO.setDay(day);
        dailyDishListVO.setDishList(dishListVOS);
        return dailyDishListVO;
    }

    private ProjFitnessDishListVO setType4Dish(ProjFitnessDishListVO dishListVO, ProjFitnessMealPlanRelationPub relationPub) {

        if (Objects.nonNull(relationPub.getDishType())) {
            dishListVO.setDishType(relationPub.getDishType());
        }
        return dishListVO;
    }


}
