package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog104.entity.ProjFitnessCoachingCoursesRelationPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessCoachingCoursesRelationPubMapper;
import com.laien.cmsapp.oog104.service.IProjFitnessCoachingCoursesRelationPubService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * CoachingCoursesRelationPub 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessCoachingCoursesRelationPubServiceImpl
        extends ServiceImpl<ProjFitnessCoachingCoursesRelationPubMapper, ProjFitnessCoachingCoursesRelationPub>
        implements IProjFitnessCoachingCoursesRelationPubService {

}
