package com.laien.cmsapp.oog104.mapstruct;

import com.laien.cmsapp.oog104.entity.ProjFitnessCoachPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessCoachingCoursesPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessVideoCoursePub;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachVO;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesVO;
import com.laien.cmsapp.oog104.response.ProjFitnessVideoCourseDetailVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>'
 * fitnessManualWorkout mapstruct
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Mapper(componentModel = "spring")
public interface ProjFitnessCoachingCourseMapStruct {

    ProjFitnessCoachingCoursesVO toListVO(ProjFitnessCoachingCoursesPub coachingCourses);

    ProjFitnessCoachingCoursesDetailVO toDetailVO(ProjFitnessCoachingCoursesPub coursesPub);

    List<ProjFitnessVideoCourseDetailVO> toVideoCourseDetailVOList(List<ProjFitnessVideoCoursePub> videoCourseList);

    ProjFitnessCoachVO toCoachVO(ProjFitnessCoachPub coachPub);

    ProjFitnessVideoCourseDetailVO toVideoCourseDetailVO(ProjFitnessVideoCoursePub p);
}
