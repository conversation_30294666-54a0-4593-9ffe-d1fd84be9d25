package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.*;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * proj_fitness_manual_workout
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessManualWorkoutPub对象", description = "proj_fitness_manual_workout_pub")
@TableName(autoResultMap = true)
public class ProjFitnessManualWorkoutPub extends BaseModel  implements AppTextCoreI18nModel {

    private static final long serialVersionUID = -3332427129198121023L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "Exercise Name")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_MANUAL_WORKOUT;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "Event Name, auto-generated")
    private String eventName;

    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    private String coverImage;

    @ApiModelProperty(value = "Detail Image, supports png/webp formats")
    private String detailImage;

    @ApiModelProperty(value = "Workout Type")
    private ManualWorkoutTypeEnums workoutType;

    @ApiModelProperty(value = "Age Groups")
    @TableField(typeHandler = ManualAgeGroupEnums.TypeHandler.class)
    private List<ManualAgeGroupEnums> ageGroup;

    @ApiModelProperty(value = "Target Areas")
    @TableField(typeHandler = ManualTargetEnums.TypeHandler.class)
    private List<ManualTargetEnums> target;

    @ApiModelProperty(value = "Difficulty")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "Equipment")
    @TableField(typeHandler = ManualEquipmentEnums.TypeHandler.class)
    private List<ManualEquipmentEnums> equipment;

    @ApiModelProperty(value = "Special Limits")
    @TableField(typeHandler = ExerciseVideoSpecialLimitEnums.TypeHandler.class)
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "Intensity")
    private ManualIntensityEnums intensity;

    @ApiModelProperty(value = "Categories")
    @TableField(typeHandler = ManualCategoryEnums.TypeHandler.class)
    private List<ManualCategoryEnums> category;

    @ApiModelProperty(value = "Description, maximum 1000 characters")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    @ApiModelProperty(value = "Calories Burned")
    private BigDecimal calorie;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;

    @ApiModelProperty(value = "Status")
    private Integer status;

    @ApiModelProperty(value = "File Status: 0-Running, 1-Success, 2-Failed")
    private Integer fileStatus;

    @ApiModelProperty(value = "Failure Message")
    private String failMessage;

    @ApiModelProperty(value = "Audio Languages, comma separated")
    private String audioLanguages;

    @ApiModelProperty(value = "Video URL")
    private String videoUrl;

    @ApiModelProperty(value = "Audio JSON URL")
    private String audioJsonUrl;
}
