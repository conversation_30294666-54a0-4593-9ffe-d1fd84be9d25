package com.laien.cmsapp.oog104.entity;

import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2025/1/7 21:42
 */
@Data
public class ProjFitnessUnitPub extends BaseModel {

    private Integer version;

    @ApiModelProperty(value = "单位名称")
    private String name;

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_UNIT;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

}
