package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessDishPubMapper;
import com.laien.cmsapp.oog104.response.*;
import com.laien.cmsapp.oog104.service.IProjFitnessAllergenPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessDishPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessDishStepPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessIngredientPubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:39
 */
@Slf4j
@Service
public class ProjFitnessDishPubServiceImpl extends ServiceImpl<ProjFitnessDishPubMapper, ProjFitnessDishPub> implements IProjFitnessDishPubService {

    @Resource
    private IProjFitnessDishStepPubService dishStepPubService;

    @Resource
    private IProjFitnessIngredientPubService ingredientPubService;

    @Resource
    private IProjFitnessAllergenPubService allergenPubService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessDishListVO> listDish(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> dishIds, Integer status, String lang) {

        if (CollectionUtils.isEmpty(dishIds)) {
            return Collections.emptyList();
        }

        List<ProjFitnessDishPub> dishPubList = listByStatusAndVersion(dishIds, status, versionInfoBO);
        if (CollectionUtils.isEmpty(dishPubList)) {
            return Collections.emptyList();
        }

        projLmsI18nService.handleTextI18n(dishPubList, ProjCodeEnums.OOG104,lang);
        return dishPubList.stream().map(this::convert2ListVO).collect(Collectors.toList());
    }

    private ProjFitnessDishListVO convert2ListVO(ProjFitnessDishPub ProjFitnessDishPub) {

        if (Objects.isNull(ProjFitnessDishPub)) {
            return null;
        }

        ProjFitnessDishListVO ProjFitnessDishVO = new ProjFitnessDishListVO();
        BeanUtils.copyProperties(ProjFitnessDishPub, ProjFitnessDishVO);
        return ProjFitnessDishVO;
    }

    private List<ProjFitnessDishPub> listByStatusAndVersion(Collection<Integer> dishIds, Integer status, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        LambdaQueryWrapper<ProjFitnessDishPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(dishIds), ProjFitnessDishPub::getId, dishIds);
        queryWrapper.eq(Objects.nonNull(status), ProjFitnessDishPub::getStatus, status);
        queryWrapper.eq(ProjFitnessDishPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessDishPub::getProjId, versionInfoBO.getProjId());
        return list(queryWrapper);
    }

    @Override
    public ProjFitnessDishDetailVO getDishDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang, Integer m3u8Type) {

        List<ProjFitnessDishPub> dishPubList = listByStatusAndVersion(Lists.newArrayList(dishId), null, versionInfoBO);
        if (CollectionUtils.isEmpty(dishPubList)) {
            return null;
        }

        ProjFitnessDishPub ProjFitnessDishPub = dishPubList.get(GlobalConstant.ZERO);
        projLmsI18nService.handleTextI18n(Lists.newArrayList(ProjFitnessDishPub), ProjCodeEnums.OOG104,lang);
        ProjFitnessDishDetailVO dishDetailVO = convert2DetailVO(ProjFitnessDishPub, m3u8Type);

        // 配料
        List<ProjFitnessIngredientVO> ingredientVOList = ingredientPubService.listByDishId(versionInfoBO, dishId, lang);
        if (!CollectionUtils.isEmpty(ingredientVOList)) {
            dishDetailVO.setIngredientList(ingredientVOList);
        }

        // 步骤
        List<ProjFitnessDishStepVO> dishStepVOList = dishStepPubService.listByDishId(versionInfoBO, dishId, lang);
        if (!CollectionUtils.isEmpty(dishStepVOList)) {
            dishDetailVO.setDishStepList(dishStepVOList);
        }

        // 过敏原
        List<String> allergenList = allergenPubService.listByDataId(versionInfoBO, dishId, lang, FitnessAllergenRelationBusinessEnums.FITNESS_DISH);
        if (!CollectionUtils.isEmpty(allergenList)) {
            dishDetailVO.setAllergenList(allergenList);
        }

        setI18n4DishDetail(Lists.newArrayList(dishDetailVO), lang);

        return dishDetailVO;
    }

    private void setI18n4DishDetail(List<ProjFitnessDishDetailVO> details, String lang) {
        List<AppTextCoreI18nModel> modelList = details.stream().map(ProjFitnessDishDetailVO::getIngredientList)
                .flatMap(Collection::stream).collect(Collectors.toList());
        modelList.addAll(details.stream().map(ProjFitnessDishDetailVO::getDishStepList).flatMap(Collection::stream).collect(Collectors.toList()));
        for (ProjFitnessDishDetailVO detail : details) {
            List<ProjFitnessDishStepVO> dishStepList = detail.getDishStepList();
            if (CollUtil.isEmpty(dishStepList)) {
                continue;
            }
            for (ProjFitnessDishStepVO stepVO : dishStepList) {
                List<ProjFitnessDishStepTipVO> tipList = stepVO.getTipList();
                if (CollUtil.isEmpty(tipList)) {
                    continue;
                }
                for (ProjFitnessDishStepTipVO tipVO : tipList) {
                    if (null != tipVO) {
                        modelList.add(tipVO);
                    }
                }
            }
        }
        projLmsI18nService.handleTextI18n(modelList, ProjCodeEnums.OOG104,lang);
    }

    private ProjFitnessDishDetailVO convert2DetailVO(ProjFitnessDishPub ProjFitnessDishPub, Integer m3u8Type) {

        ProjFitnessDishDetailVO dishDetailVO = new ProjFitnessDishDetailVO();
        BeanUtils.copyProperties(ProjFitnessDishPub, dishDetailVO);

        // 为了解决WEB端 Dish Video 可为空（上传再删除）导致的切片问题，这里对原视频做非空校验
        if (!StringUtils.isEmpty(ProjFitnessDishPub.getResourceVideoUrl()) && Objects.equals(GlobalConstant.ONE, m3u8Type)) {
            dishDetailVO.setVideoM3u8Url(ProjFitnessDishPub.getVideo2532Url());
        }
        if (!StringUtils.isEmpty(ProjFitnessDishPub.getResourceVideoUrl()) && Objects.equals(GlobalConstant.TWO, m3u8Type)) {
            dishDetailVO.setVideoM3u8Url(ProjFitnessDishPub.getVideoUrl());
        }
        return dishDetailVO;
    }
}
