package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog104.entity.ProjFitnessVideo;
import com.laien.cmsapp.oog104.mapper.ProjFitnessVideoMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessVideoMapStruct;
import com.laien.cmsapp.oog104.response.ProjFitnessVideoDetailVO;
import com.laien.cmsapp.oog104.service.IProjFitnessVideoService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * proj_fitness_video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
@Slf4j
public class ProjFitnessVideoServiceImpl extends ServiceImpl<ProjFitnessVideoMapper, ProjFitnessVideo> implements IProjFitnessVideoService {

    @Resource
    private ProjFitnessVideoMapStruct projFitnessVideoMapStruct;
    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessVideoDetailVO> selectDetailByIds(Collection<Integer> idCollection, Integer m3u8Type) {
        if (CollectionUtil.isEmpty(idCollection)) {
            return Collections.emptyList();
        }

        List<ProjFitnessVideo> videoList = this.baseMapper.selectListDetailByIds(idCollection);
        this.handleVideoI18n(videoList);

        return projFitnessVideoMapStruct.toVideoDetailVOList(videoList, m3u8Type);
    }


    /**
     * 处理video 多语言
     *
     * @param videoList videoList
     */
    private void handleVideoI18n(List<ProjFitnessVideo> videoList) {
        projLmsI18nService.handleSpeechI18nSingle(CollUtil.newArrayList(videoList), ProjCodeEnums.OOG104);
    }

}
