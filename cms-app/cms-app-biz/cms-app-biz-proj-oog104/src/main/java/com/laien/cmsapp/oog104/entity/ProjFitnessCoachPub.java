package com.laien.cmsapp.oog104.entity;

import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 教练表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCoachPub", description="ProjFitnessCoachPub表")
public class ProjFitnessCoachPub extends BaseModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_COACH;

    @ApiModelProperty(value = "project id")
    private Integer projId;

    @ApiModelProperty(value = "teacher name")
    private String name;

    @ApiModelProperty(value = "photoImgUrl")
    private String photoImgUrl;

    @ApiModelProperty(value = "introduction")
    private String introduction;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;

}
