package com.laien.cmsapp.oog104.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.request.ProjFitnessFastingArticleListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessFastingArticlePubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Fitness Fasting article 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Api(tags = "app端：fitnessFastingArticle")
@RestController
@RequestMapping("/{appCode}/fitnessFastingArticle")
public class ProjFitnessFastingArticleController extends ResponseController {

    @Resource
    private IProjFitnessFastingArticlePubService projFitnessFastingArticlePubService;

    @ApiOperation(value = "fitnessFastingArticle 列表 v1", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessFastingArticleListVO>> list(ProjFitnessFastingArticleListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projFitnessFastingArticlePubService.list(req,versionInfoBO));
    }


    @ApiOperation(value = "fitnessFastingArticle 详情 v1", tags = {"oog104"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjFitnessFastingArticleDetailVO> detail(@PathVariable Integer id,
                                                                    @RequestParam Integer code) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projFitnessFastingArticlePubService.detail(id, versionInfoBO));
    }

}
