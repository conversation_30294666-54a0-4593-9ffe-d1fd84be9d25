package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessAllergenPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessAllergenRelationPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessAllergenPubMapper;
import com.laien.cmsapp.oog104.service.IProjFitnessAllergenPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessAllergenRelationPubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/7 15:07
 */
@Service
public class ProjFitnessAllergenPubServiceImpl extends ServiceImpl<ProjFitnessAllergenPubMapper, ProjFitnessAllergenPub> implements IProjFitnessAllergenPubService {

    @Resource
    IProjFitnessAllergenRelationPubService relationPubService;

    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<String> listByDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, String lang, FitnessAllergenRelationBusinessEnums businessEnum) {

        List<ProjFitnessAllergenRelationPub> allergenRelationPubList = relationPubService.listByVersionAndDataId(versionInfoBO, dataId, businessEnum);
        if (CollectionUtils.isEmpty(allergenRelationPubList)) {
            return Collections.emptyList();
        }

        List<Integer> allergenIds = allergenRelationPubList.stream().map(ProjFitnessAllergenRelationPub::getProjFitnessAllergenId).collect(Collectors.toList());
        List<ProjFitnessAllergenPub> ProjFitnessAllergenPubList = listByVersionAndDataId(versionInfoBO, allergenIds);
        if (CollectionUtils.isEmpty(ProjFitnessAllergenPubList)) {
            return Collections.emptyList();
        }

        projLmsI18nService.handleTextI18n(ProjFitnessAllergenPubList, ProjCodeEnums.OOG104,lang);
        Map<Integer, String> allergenIdMap = ProjFitnessAllergenPubList.stream().collect(Collectors.toMap(ProjFitnessAllergenPub::getId, ProjFitnessAllergenPub::getName));
        return allergenIds.stream().filter(allergenIdMap::containsKey).map(allergenIdMap::get).collect(Collectors.toList());
    }


    private List<ProjFitnessAllergenPub> listByVersionAndDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> allergenIds) {

        LambdaQueryWrapper<ProjFitnessAllergenPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessAllergenPub::getId, allergenIds);
        queryWrapper.eq(ProjFitnessAllergenPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessAllergenPub::getProjId, versionInfoBO.getProjId());
        return list(queryWrapper);
    }

}
