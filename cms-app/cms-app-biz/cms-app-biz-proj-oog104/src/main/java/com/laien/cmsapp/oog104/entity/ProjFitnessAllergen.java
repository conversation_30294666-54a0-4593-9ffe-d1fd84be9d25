package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * allergen
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_fitness_allergen")
@ApiModel(value="ProjAllergen对象", description="allergen")
public class ProjFitnessAllergen extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "过敏源名称")
    private String name;

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_ALLERGEN;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;


}
