package com.laien.cmsapp.oog104.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * proj_fitness_collection_pub
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCollectionPub对象", description="proj_fitness_collection_pub")
public class ProjFitnessCollectionPub extends BaseModel  implements AppTextCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "难度code")
    private Integer difficultyCode;

    @ApiModelProperty(value = "器械code")
    private String equipmentCodes;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "简介")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
