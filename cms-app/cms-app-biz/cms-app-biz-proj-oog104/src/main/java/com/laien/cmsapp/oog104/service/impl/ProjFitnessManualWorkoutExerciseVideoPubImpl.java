package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutExerciseVideoPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessManualWorkoutExerciseVideoPubMapper;
import com.laien.cmsapp.oog104.service.IProjFitnessManualWorkoutExerciseVideoPubService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *     ProjFitnessManualWorkoutExerciseVideoPubService 实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessManualWorkoutExerciseVideoPubImpl extends ServiceImpl<ProjFitnessManualWorkoutExerciseVideoPubMapper, ProjFitnessManualWorkoutExerciseVideoPub>
        implements IProjFitnessManualWorkoutExerciseVideoPubService {

}
