package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutExerciseVideoPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutI18nPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutPub;
import com.laien.cmsapp.oog104.entity.i18n.ManualTypeEnumsI18n;
import com.laien.cmsapp.oog104.mapper.ProjFitnessManualWorkoutPubMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessManualWorkoutMapStruct;
import com.laien.cmsapp.oog104.request.ProjFitnessManualWorkoutListReq;
import com.laien.cmsapp.oog104.response.*;
import com.laien.cmsapp.oog104.service.IProjFitnessExerciseVideoService;
import com.laien.cmsapp.oog104.service.IProjFitnessManualWorkoutExerciseVideoPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessManualWorkoutI18nPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessManualWorkoutPubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import com.laien.common.oog104.enums.manual.ManualWorkoutTypeEnums;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * ProjFitnessManualWorkoutPubServiceImpl 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessManualWorkoutPubServiceImpl  extends ServiceImpl<ProjFitnessManualWorkoutPubMapper, ProjFitnessManualWorkoutPub>
        implements IProjFitnessManualWorkoutPubService {
    private static final String CATEGORY ="category";
    private static final String AGE_GROUP ="age_group";
    private static final String WORKOUT_TYPE ="workout_type";

    private final IProjFitnessManualWorkoutExerciseVideoPubService exerciseVideoPubService;
    private final IProjFitnessManualWorkoutI18nPubService i18nPubService;
    private final IProjFitnessExerciseVideoService exerciseVideoService;
    private final ProjFitnessManualWorkoutMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessManualWorkoutDetailVO> selectDetailsByIds(Collection<Integer> idCollection) {

        if (CollUtil.isEmpty(idCollection)) return new ArrayList<>();
        List<ProjFitnessManualWorkoutDetailVO> result = new ArrayList<>();
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();

        //query manualWorkoutPub
        LambdaQueryWrapper<ProjFitnessManualWorkoutPub> queryWrapper = getWorkoutPubLambdaQueryWrapper(idCollection, version);
        queryWrapper.orderByAsc(ProjFitnessManualWorkoutPub::getId);
        List<ProjFitnessManualWorkoutPub> workouts = this.list(queryWrapper);
        if (CollUtil.isEmpty(workouts)) return result;

        //query relation
        List<Integer> workoutIds = workouts.stream().map(ProjFitnessManualWorkoutPub::getId).collect(Collectors.toList());
        LambdaQueryWrapper<ProjFitnessManualWorkoutExerciseVideoPub> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.in(ProjFitnessManualWorkoutExerciseVideoPub::getProjFitnessManualWorkoutId, workoutIds);
        relationQueryWrapper.eq(ProjFitnessManualWorkoutExerciseVideoPub::getDelFlag, GlobalConstant.NO);
        relationQueryWrapper.eq(ProjFitnessManualWorkoutExerciseVideoPub::getVersion, version.getCurrentVersion());
        relationQueryWrapper.orderByAsc(ProjFitnessManualWorkoutExerciseVideoPub::getId);
        List<ProjFitnessManualWorkoutExerciseVideoPub> relationList = exerciseVideoPubService.list(relationQueryWrapper);
        Map<Integer,List<ProjFitnessManualWorkoutExerciseVideoPub>> relationMap = relationList.stream().
                collect(Collectors.groupingBy(ProjFitnessManualWorkoutExerciseVideoPub::getProjFitnessManualWorkoutId));

        //query exerciseVideo
        Set<Integer> exerciseVideoIds = relationList.stream().map(ProjFitnessManualWorkoutExerciseVideoPub::getProjFitnessExerciseVideoId).collect(Collectors.toSet());
        Map<Integer, ProjFitnessRefreshVideoVO> exerciseVideoDetailVOMap = exerciseVideoService.getRefreshVideoMapByIds(exerciseVideoIds);

        //query i18n audio Info
        Map<Integer, List<ProjFitnessManualWorkoutI18nPub>> audioI18nMap = i18nPubService.list(new LambdaQueryWrapper<ProjFitnessManualWorkoutI18nPub>()
                .in(ProjFitnessManualWorkoutI18nPub::getProjFitnessManualWorkoutId, idCollection)
                .eq(ProjFitnessManualWorkoutI18nPub::getVersion, version.getCurrentVersion())
        ).stream().collect(Collectors.groupingBy(ProjFitnessManualWorkoutI18nPub::getProjFitnessManualWorkoutId));

        //handel workout i18n
        projLmsI18nService.handleTextI18n(workouts, ProjCodeEnums.OOG104);

        // 一次性调用 i18n 处理
        exerciseVideoService.handleVideoI18n(exerciseVideoDetailVOMap.values());

        //assemble result
        for (ProjFitnessManualWorkoutPub workout : workouts) {
            ProjFitnessManualWorkoutDetailVO vo = mapStruct.toManualWorkoutDetailVO(workout);
            List<ProjFitnessManualWorkoutExerciseVideoPub> relationListByWorkout = relationMap.get(workout.getId());
            //assemble unitList
            vo.setUnitList(this.assembleUnitList(relationListByWorkout, exerciseVideoDetailVOMap));
            //assemble audioJsonList
            vo.setAudioJsonList(this.getAudioI18nVOS(workout,audioI18nMap.getOrDefault(workout.getId(),new ArrayList<>())));
            result.add(vo);
        }
        return result;
    }

    private LambdaQueryWrapper<ProjFitnessManualWorkoutPub> getWorkoutPubLambdaQueryWrapper(Collection<Integer> idCollection, ProjPublishCurrentVersionInfoBO version) {
        LambdaQueryWrapper<ProjFitnessManualWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(idCollection),ProjFitnessManualWorkoutPub::getId, idCollection);
        queryWrapper.eq(ProjFitnessManualWorkoutPub::getDelFlag, GlobalConstant.NO);
        queryWrapper.eq(ProjFitnessManualWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ProjFitnessManualWorkoutPub::getProjId, version.getProjId());
        queryWrapper.eq(ProjFitnessManualWorkoutPub::getVersion, version.getCurrentVersion());
        return queryWrapper;
    }

    private List<AudioI18nVO> getAudioI18nVOS(ProjFitnessManualWorkoutPub workout, List<ProjFitnessManualWorkoutI18nPub> projFitnessManualWorkoutI18nPubs) {
        List<AudioI18nVO> audioJsonList = new ArrayList<>();
        //other language
        projFitnessManualWorkoutI18nPubs.forEach(i18n -> {
            AudioI18nVO audioI18nVO = new AudioI18nVO();
            audioI18nVO.setLanguage(i18n.getLanguage()).setAudioJsonUrl(i18n.getAudioJsonUrl());
            audioJsonList.add(audioI18nVO);
        });
        return audioJsonList;
    }

    private List<ProjFitnessRefreshWorkoutDetailUnitVO> assembleUnitList(List<ProjFitnessManualWorkoutExerciseVideoPub> relationListByWorkout,
                                                                         Map<Integer, ProjFitnessRefreshVideoVO> exerciseVideoDetailVOMap) {
        // Step 1: 映射并设置 exerciseCircuit，同时收集所有非空视频
        List<ProjFitnessRefreshVideoVO> allUsedVideos = relationListByWorkout.stream()
                .map(pub -> {
                    ProjFitnessRefreshVideoVO video = exerciseVideoDetailVOMap.get(pub.getProjFitnessExerciseVideoId());
                    if (video != null) {
                        video.setExerciseCircuit(pub.getExerciseCircuit());
                        // videoDuration减掉previewDuration时长
                        video.setVideoDuration(pub.getVideoDuration()-pub.getPreviewDuration());
                        video.setPreviewDuration(pub.getPreviewDuration());
                    }
                    return video;
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> unitNameI18nMap = getUnitNameI18nMap();
        // Step 3: 按 unitName 分组并构造返回结构
        return relationListByWorkout.stream()
                .collect(Collectors.groupingBy(
                        ProjFitnessManualWorkoutExerciseVideoPub::getUnitName,
                        Collectors.mapping(
                                pub -> exerciseVideoDetailVOMap.get(pub.getProjFitnessExerciseVideoId()),
                                Collectors.toList()
                        )
                ))
                .entrySet().stream()
                .map(entry -> {
                    ProjFitnessRefreshWorkoutDetailUnitVO unitVO = new ProjFitnessRefreshWorkoutDetailUnitVO();
                    ManualTypeEnums typeEnums = entry.getKey();
                    String unitName = typeEnums.getName();
                    unitVO.setUnitName(unitNameI18nMap.getOrDefault(unitName, unitName));
                    unitVO.setUnitNameType(typeEnums);
                    unitVO.setVideoList(entry.getValue());
                    unitVO.setCount(entry.getValue().size());
                    return unitVO;
                })
                .sorted(Comparator.comparingInt(unit ->
                        unit.getUnitNameType().ordinal()))
                .collect(Collectors.toList());
    }

    private Map<String, String> getUnitNameI18nMap() {
        List<ManualTypeEnumsI18n> i18ns = Arrays.stream(ManualTypeEnums.values()).map(ManualTypeEnumsI18n::new).collect(Collectors.toList());
        projLmsI18nService.handleTextI18n(i18ns, ProjCodeEnums.OOG104);
        return i18ns.stream().collect(Collectors.toMap(v->v.getUniqueKey().toString(), ManualTypeEnumsI18n::getName));
    }

    @Override
    public List<ProjFitnessManualWorkoutListVO> selectList(ProjFitnessManualWorkoutListReq req) {
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjFitnessManualWorkoutPub> queryWrapper = this.getWorkoutPubLambdaQueryWrapper(null, version);
        if (req.getCategory() != null) {
            this.multipleSearchContainsOne(queryWrapper,Collections.singletonList(req.getCategory()),CATEGORY);
        }
        if (ObjUtil.isNotNull(req.getOrderByDescColumn())) {
            queryWrapper.orderByDesc(req.getOrderByDescColumn());
        }
        List<ProjFitnessManualWorkoutPub> list = this.list(queryWrapper);
        projLmsI18nService.handleTextI18n(list, ProjCodeEnums.OOG104);
        return mapStruct.toVOList(list);
    }

    private List<ProjFitnessManualWorkoutPub> processTopPicks(ProjFitnessManualWorkoutListReq req, List<ProjFitnessManualWorkoutPub> list) {
        if (ObjUtil.equal(req.getCategory(), ManualCategoryEnums.TOP_PICKS) && ObjUtil.isNotNull(req.getAgeGroup())) {
            List<ManualWorkoutTypeEnums> userWorkoutTypes = req.getWorkoutType();
            //handle top picks order
            List<ManualWorkoutTypeEnums> preferredWorkoutTypes = req.getAgeGroup().getPreferredWorkoutTypes();
            List<ManualWorkoutTypeEnums> userPickedWorkoutTypes = preferredWorkoutTypes.stream()
                    .filter(w -> CollUtil.isNotEmpty(userWorkoutTypes) && userWorkoutTypes.contains(w))
                    .collect(Collectors.toList());
            //resort list by user picked types first, then preferred workout types
            List<ManualWorkoutTypeEnums> sortedWorkoutTypes = Stream.concat(
                    userPickedWorkoutTypes.stream(),
                    preferredWorkoutTypes.stream().filter(w -> !userPickedWorkoutTypes.contains(w))
            ).collect(Collectors.toList());
            // 1) Group workouts by type
            Map<ManualWorkoutTypeEnums, List<ProjFitnessManualWorkoutPub>> groupedByType =
                list.stream().collect(Collectors.groupingBy( ProjFitnessManualWorkoutPub::getWorkoutType));
            // 2) For each type in sortedWorkoutTypes, randomly pick exactly one workout (if available), up to 3 total
            Random random = new Random();
            int picks = 0;
            List<ProjFitnessManualWorkoutPub> chosenWorkouts = new ArrayList<>();
            for (ManualWorkoutTypeEnums type : sortedWorkoutTypes) {
                if (picks >= 3) {
                    break;
                }
                List<ProjFitnessManualWorkoutPub> candidates = groupedByType.get(type);
                if (candidates != null && !candidates.isEmpty()) {
                    ProjFitnessManualWorkoutPub chosen = candidates.get(random.nextInt(candidates.size()));
                    chosenWorkouts.add(chosen);
                    picks++;
                }
            }
            // 3) Build the final list: chosen workouts first, then the remainder in original order
            Set<Integer> chosenIds = chosenWorkouts.stream()
                .map(ProjFitnessManualWorkoutPub::getId)
                .collect(Collectors.toSet());
            list = Stream.concat(
                chosenWorkouts.stream(),
                list.stream().filter(w -> !chosenIds.contains(w.getId()))
            ).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<ProjFitnessManualWorkoutPub> selectTopPickList(ManualCategoryEnums category) {
        return null;
    }

    /**
     * 多选查询处理 有交集为真
     *
     * @param wrapper wrapper
     * @param options options
     * @param tableField tableField
     */
    private void multipleSearchContainsOne(LambdaQueryWrapper<ProjFitnessManualWorkoutPub> wrapper, List<? extends com.laien.common.core.enums.IEnumBase> options, String tableField) {
        if(CollUtil.isEmpty(options)){
            return;
        }
        wrapper.and(orWrapper ->
                options.forEach(equipment ->
                        orWrapper.or().apply("FIND_IN_SET({0},"+tableField+")", equipment.getCode())));
    }
}
