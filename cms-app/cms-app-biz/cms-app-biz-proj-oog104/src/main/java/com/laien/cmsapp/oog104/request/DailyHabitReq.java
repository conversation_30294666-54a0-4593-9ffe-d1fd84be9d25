package com.laien.cmsapp.oog104.request;

import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@Data
public class DailyHabitReq {

    @ApiModelProperty(value = "特殊限制")
    private Set<ExerciseVideoSpecialLimitEnums> specialLimitSet;

    @ApiModelProperty(value = "difficulty")
    private ManualDifficultyEnums difficulty;

}
