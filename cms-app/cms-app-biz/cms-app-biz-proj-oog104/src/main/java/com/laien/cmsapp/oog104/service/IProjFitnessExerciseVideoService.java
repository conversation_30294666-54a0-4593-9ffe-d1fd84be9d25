package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.cmsapp.oog104.response.ProjFitnessExerciseVideoDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessRefreshVideoVO;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * proj_fitness_exercise_video 服务类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/18
 */
public interface IProjFitnessExerciseVideoService extends IService<ProjFitnessExerciseVideo> {

    /**
     * getRefreshVideoMapByIds
     * @param exerciseVideoIds exerciseVideoIds
     * @return
     */
    Map<Integer, ProjFitnessRefreshVideoVO> getRefreshVideoMapByIds(Set<Integer> exerciseVideoIds);

    void handleVideoI18n(Collection<? extends ProjFitnessExerciseVideoDetailVO> videoList);
}
