package com.laien.cmsapp.oog104.mapstruct;

import com.laien.cmsapp.oog104.entity.ProjFitnessFastingArticlePub;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>'
 * ProjFitnessFastingArticleMapStruct
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Mapper(componentModel = "spring")
public interface ProjFitnessFastingArticleMapStruct {

    List<ProjFitnessFastingArticleListVO> toVOList(List<ProjFitnessFastingArticlePub> fastingArticleList);

    ProjFitnessFastingArticleDetailVO toDetailVO(ProjFitnessFastingArticlePub fastingArticle);
}
