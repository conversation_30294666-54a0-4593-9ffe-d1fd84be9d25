package com.laien.cmsapp.oog104.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_plan_pub
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessPlanPub对象", description="proj_fitness_plan_pub")
public class ProjFitnessPlanPub extends BaseModel implements AppTextCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "plan类型code")
    private Integer typeCode;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "Stage Counts")
    private String stageCounts;

    @ApiModelProperty(value = "字体颜色")
    private String fontColor;

    @ApiModelProperty(value = "背景颜色")
    private String bgColor;

    @ApiModelProperty(value = "tags")
    @AppTextTranslateField
    private String tags;

    @ApiModelProperty(value = "描述")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "预期结果")
    @AppTextTranslateField
    private String expectedResults;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
