package com.laien.cmsapp.oog104.request;

import com.laien.common.oog104.enums.FitnessGenderEnums;
import com.laien.common.oog104.enums.FitnessSoundSubTypeEnums;
import com.laien.common.oog104.enums.FitnessSoundTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "声音请求参数", description = "声音请求参数")
public class ProjFitnessSoundReq {

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private FitnessSoundTypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private FitnessSoundSubTypeEnums soundSubType;

    @ApiModelProperty(value = "声音源(female|male 默认female")
    private FitnessGenderEnums gender;
}
