package com.laien.cmsapp.oog104.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjFitnessDishStepTipPub extends BaseModel {

    private Integer version;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projFitnessDishId;

    @ApiModelProperty(value = "proj_dish_step表数据id")
    private Integer projFitnessDishStepId;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "介绍")
    private String intro;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
