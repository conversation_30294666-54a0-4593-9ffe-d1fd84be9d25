package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutPub;
import com.laien.cmsapp.oog104.request.CommonRefreshReq;
import com.laien.cmsapp.oog104.request.CommonRefreshWrapperReq;
import com.laien.cmsapp.oog104.request.ProjFitnessManualWorkoutListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessManualWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessManualWorkoutListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessManualWorkoutPubService;
import com.laien.common.controller.ResponseController;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * app端：fitnessManualWorkout controller
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Api(tags = "app端：fitnessManualWorkout")
@RestController
@RequestMapping("/oog104/fitnessManualWorkout")
@AllArgsConstructor
public class ProjFitnessManualWorkoutController extends ResponseController {

    private final IProjFitnessManualWorkoutPubService projFitnessManualWorkoutService;

    @ApiOperation(value = "fitnessManualWorkout detail v1")
    @GetMapping("/v1/detailList")
    public ResponseResult<List<ProjFitnessManualWorkoutDetailVO>> detailList(CommonRefreshWrapperReq req) {
        return succ(projFitnessManualWorkoutService.selectDetailsByIds(req.getReq().stream().map(CommonRefreshReq::getId).collect(Collectors.toList())));
    }

    @ApiOperation(value = "fitnessManualWorkout list v1")
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessManualWorkoutListVO>> list(ProjFitnessManualWorkoutListReq req) {
        return succ(projFitnessManualWorkoutService.selectList(req));
    }

    @ApiOperation(value = "fitnessManualWorkout popularList v1")
    @GetMapping("/v1/popularList")
    public ResponseResult<List<ProjFitnessManualWorkoutListVO>> popularList() {
        ProjFitnessManualWorkoutListReq req = new ProjFitnessManualWorkoutListReq();
        req.setCategory(ManualCategoryEnums.POPULAR);
        req.setOrderByDescColumn(ProjFitnessManualWorkoutPub::getNewTimeStart);
        return succ(projFitnessManualWorkoutService.selectList(req));
    }

    @ApiOperation(value = "fitnessManualWorkout topPicksList v1")
    @GetMapping("/v1/topPicksList")
    public ResponseResult<List<ProjFitnessManualWorkoutListVO>> topPicksList() {
        ProjFitnessManualWorkoutListReq req = new ProjFitnessManualWorkoutListReq();
        req.setCategory(ManualCategoryEnums.TOP_PICKS);
        req.setOrderByDescColumn(ProjFitnessManualWorkoutPub::getNewTimeStart);
        return succ(projFitnessManualWorkoutService.selectList(req));
    }
}
