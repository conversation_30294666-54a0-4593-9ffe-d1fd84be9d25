package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutPub;
import com.laien.cmsapp.oog104.request.ProjFitnessManualWorkoutListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessManualWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessManualWorkoutListVO;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_fitness_manual_workout_pub 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/18
 */
public interface IProjFitnessManualWorkoutPubService extends IService<ProjFitnessManualWorkoutPub> {

    /**
     *  根据id查询 ManualWorkoutDetails
     * @param idCollection id集合
     * @return
     */
    List<ProjFitnessManualWorkoutDetailVO> selectDetailsByIds(Collection<Integer> idCollection);


    /**
     * 查询所有的ManualWorkout
     * @return 所有的ManualWorkout
     */
    List<ProjFitnessManualWorkoutListVO> selectList(ProjFitnessManualWorkoutListReq req);

    /**
     *
     * @param category
     * @return
     */
    List<ProjFitnessManualWorkoutPub> selectTopPickList(ManualCategoryEnums category);
}
