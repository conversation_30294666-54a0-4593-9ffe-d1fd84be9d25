package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.request.ProjFitnessCoachingCoursesListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesVO;
import com.laien.cmsapp.oog104.service.IProjFitnessCoachingCoursesPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * note: Coaching Courses
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Api(value = "/{appCode}/fitnessCoachingCourses", tags = {"app端：Fitness Coaching Courses"})
@RestController
@RequestMapping("/{appCode}/fitnessCoachingCourses")
public class ProjFitnessCoachingCoursesController extends ResponseController {

    @Resource
    private IProjFitnessCoachingCoursesPubService service;


    @ApiOperation(value = "Fitness Coaching Courses 列表 v1", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessCoachingCoursesVO>> list(ProjFitnessCoachingCoursesListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(service.list(versionInfoBO, req));
    }

    @ApiOperation(value = "Fitness Coaching Courses 详情 v1", tags = {"oog104"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjFitnessCoachingCoursesDetailVO> detail(@PathVariable Integer id,
                                                                     @RequestParam(required = false, defaultValue = "1") Integer m3u8Type,
                                                                     @RequestParam Integer code) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ( service.detail(id, versionInfoBO,m3u8Type));
    }

}
