package com.laien.cmsapp.oog104.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessWorkout对象", description="proj_fitness_workout")
public class ProjFitnessWorkout extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "封面视频地址")
    private String coverVideoUrl;

    @ApiModelProperty(value = "封面视频时长")
    private Integer coverVideoDuration;

    @ApiModelProperty(value = "扩展标签")
    private String extraTagCodes;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private Integer calorie;

    @ApiModelProperty(value = "视频m3u8地址")
    private String videoUrl;

    @ApiModelProperty(value = "视频dynamic m3u8地址")
    private String videoDynamicUrl;

    @ApiModelProperty(value = "audio json地址")
    private String audioJsonUrl;

    @ApiModelProperty(value = "简介")
    private String description;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "已生成Audio的语言，多个逗号分隔")
    private String audioLanguages;

    @ApiModelProperty(value = "生成m3u8文件的状态 运行中 0, 成功 1, 失败 2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败信息")
    private String failMessage;


}
