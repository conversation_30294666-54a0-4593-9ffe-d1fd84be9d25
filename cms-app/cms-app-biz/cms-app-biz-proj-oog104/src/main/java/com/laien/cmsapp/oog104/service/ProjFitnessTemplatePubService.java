package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessTemplatePub;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.common.oog104.enums.DailyTypeMappingEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_fitness_template(proj_fitness_template)】的数据库操作Service
* @createDate 2025-03-11 17:01:12
*/
public interface ProjFitnessTemplatePubService extends IService<ProjFitnessTemplatePub> {

    List<ProjFitnessTemplatePub> list(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<ProjFitnessTemplatePub> list(DailyHabitReq req, DailyTypeMappingEnums dailyTypeMapping, TemplateTypeEnums templateType, ProjPublishCurrentVersionInfoBO versionInfoBO);
}
