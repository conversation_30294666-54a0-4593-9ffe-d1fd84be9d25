package com.laien.cmsapp.oog104.request;

import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@Data
public class FitnessWorkoutGeneratePlanReq {

    //v8.3.0新增templateType字段
    @ApiModelProperty("模版类型字段")
    private TemplateTypeEnums templateType;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

    @ApiModelProperty("时间范围")
    private WorkoutDurationRangeEnums durationRange;

    @ApiModelProperty("年龄段")
    private ManualAgeGroupEnums ageGroup;

    @ApiModelProperty(value = "difficulty")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "特殊限制")
    private Set<ExerciseVideoSpecialLimitEnums> specialLimitSet;

    @ApiModelProperty("锻炼部位")
    private Set<ManualTargetEnums> targetSet;

}
