package com.laien.cmsapp.oog104.mapstruct;

import com.laien.cmsapp.oog104.entity.ProjFitnessCollectionPub;
import com.laien.cmsapp.oog104.response.ProjFitnessCollectionListVO;
import com.laien.cmsapp.util.TimeConvertUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * collection 对象转换接口
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper(componentModel = "spring", imports = TimeConvertUtil.class)
public interface ProjFitnessCollectionMapStruct {

    @Mapping(target = "duration", expression = "java(TimeConvertUtil.millisToMinutes(collection.getDuration()))")
    ProjFitnessCollectionListVO toCollectionVO(ProjFitnessCollectionPub collection);

    List<ProjFitnessCollectionListVO> toCollectionVOList(List<ProjFitnessCollectionPub> collectionList);

}
