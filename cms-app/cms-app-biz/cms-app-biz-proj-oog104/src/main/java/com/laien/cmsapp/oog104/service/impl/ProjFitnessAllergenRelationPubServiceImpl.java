package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessAllergenRelationPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessAllergenRelationPubMapper;
import com.laien.cmsapp.oog104.service.IProjFitnessAllergenRelationPubService;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2025/1/8 17:28
 */
@Service
public class ProjFitnessAllergenRelationPubServiceImpl extends ServiceImpl<ProjFitnessAllergenRelationPubMapper, ProjFitnessAllergenRelationPub> implements IProjFitnessAllergenRelationPubService {

    @Override
    public List<ProjFitnessAllergenRelationPub> listByVersionAndDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, FitnessAllergenRelationBusinessEnums businessEnum) {

        LambdaQueryWrapper<ProjFitnessAllergenRelationPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessAllergenRelationPub::getDataId, dataId);
        queryWrapper.eq(ProjFitnessAllergenRelationPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjFitnessAllergenRelationPub::getVersion, versionInfoBO.getCurrentVersion());

        if (Objects.nonNull(businessEnum)) {
            queryWrapper.eq(ProjFitnessAllergenRelationPub::getBusinessType, businessEnum);
        }
        return list(queryWrapper);
    }
}
