package com.laien.cmsapp.oog104.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_workout_proj_fitness_video_pub
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessWorkoutProjFitnessVideoPub对象", description="proj_fitness_workout_proj_fitness_video_pub")
public class ProjFitnessWorkoutProjFitnessVideoPub extends BaseModel  implements AppTextCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "workout id")
    private Integer projFitnessWorkoutId;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

    @ApiModelProperty(value = "正式时长")
    private Integer mainDuration;

    @ApiModelProperty(value = "单元名称")
    @AppTextTranslateField
    private String unitName;

    @ApiModelProperty(value = "exercise id")
    private Integer projFitnessVideoId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
