/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.cmsapp.oog104.service;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.cmsapp.oog104.request.PlanWorkoutRefreshReq;
import com.laien.cmsapp.oog104.response.ProjFitnessDailyHabitWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessGenerateWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessGenerateWorkoutPlanVO;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;

import java.util.List;

/**
 * <p>ProjFitnessGenerateWorkout service </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface IProjFitnessWorkoutGenerateService {

    List<ProjFitnessGenerateWorkoutDetailVO> detailList(List<PlanWorkoutRefreshReq> req);

    ProjFitnessGenerateWorkoutPlanVO plan(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<ProjFitnessDailyHabitWorkoutDetailVO> dailyHabitList(DailyHabitReq req, ProjPublishCurrentVersionInfoBO versionInfoBO, ExclusiveTypeEnums exclusiveType);
}
