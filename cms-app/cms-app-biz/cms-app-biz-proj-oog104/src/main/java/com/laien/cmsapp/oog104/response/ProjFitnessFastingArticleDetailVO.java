package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog104.enums.FitnessFastingArticleEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * Fitness Fasting article
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessFastingArticlePub对象", description="Fitness Fasting article")
public class ProjFitnessFastingArticleDetailVO extends BaseTableCodeVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String titleName;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型")
    private FitnessFastingArticleEnums type;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "参考文档")
    private String reference;

    @ApiModelProperty(value = "是否收费 0不收费 1收费,允许为空")
    private Integer subscription;

}
