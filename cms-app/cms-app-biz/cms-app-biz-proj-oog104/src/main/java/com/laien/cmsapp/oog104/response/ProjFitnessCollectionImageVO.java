package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness collection list", description = "Fitness collection list")
public class ProjFitnessCollectionImageVO {

    @ApiModelProperty(value = "名称")
    private String name;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImage;

    @AbsoluteR2Url
    @ApiModelProperty(value = "Spare封面图")
    private String coverSpareImage;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImage;

    @ApiModelProperty(value = "描述")
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "collection list")
    private List<ProjFitnessCollectionListVO> collectionList;

}
