package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness video detail", description = "Fitness video detail")
public class ProjFitnessVideoDetailVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "动作名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "说明")
    private String instructions;

    @AbsoluteR2Url
    @ApiModelProperty(value = "名称音频地址")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer instructionsAudioDuration;

    @ApiModelProperty(value = "如何做")
    private String howToDo;

    @AbsoluteR2Url
    @ApiModelProperty(value = "如何做音频地址")
    private String howToDoAudioUrl;

    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

    @ApiModelProperty(value = "正式时长")
    private Integer mainDuration;
}
