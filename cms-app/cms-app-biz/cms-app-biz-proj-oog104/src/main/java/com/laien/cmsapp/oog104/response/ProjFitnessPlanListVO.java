package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness plan list", description = "Fitness plan list")
public class ProjFitnessPlanListVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "plan类型code 1:Full body, 2:Lose weight, 3:Wall pilates")
    private Integer typeCode;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "workout 数量")
    private Integer workoutCount;

    @ApiModelProperty(value = "Stage Counts")
    private List<Integer> stageCounts;

    @ApiModelProperty(value = "字体颜色")
    private String fontColor;

    @ApiModelProperty(value = "背景颜色")
    private String bgColor;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "预期结果")
    private List<String> expectedResults;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
