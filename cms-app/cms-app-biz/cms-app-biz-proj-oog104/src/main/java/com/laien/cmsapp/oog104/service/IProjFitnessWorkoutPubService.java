package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutPub;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutListVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_fitness_workout_pub 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface IProjFitnessWorkoutPubService extends IService<ProjFitnessWorkoutPub> {

    /**
     * 根据id列表查询workout详情
     *
     * @param idCollection idCollection
     * @param m3u8Type m3u8Type
     * @return list
     */
    List<ProjFitnessWorkoutDetailVO> selectDetailByIds(Collection<Integer> idCollection, Integer m3u8Type);

    /**
     * 根据id列表查询workout信息
     *
     * @param idCollection idCollection
     * @return list
     */
    List<ProjFitnessWorkoutListVO> selectListByIds(Collection<Integer> idCollection);

}
