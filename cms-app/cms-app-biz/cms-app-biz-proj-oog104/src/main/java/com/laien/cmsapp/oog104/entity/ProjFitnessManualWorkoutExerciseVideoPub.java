package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_manual_workout_exercise_video
 * <p>
 *
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjFitnessManualWorkoutExerciseVideoPub对象", description = "proj_fitness_manual_workout_exercise_video_pub")
@TableName(autoResultMap = true)
public class ProjFitnessManualWorkoutExerciseVideoPub extends BaseModel {

    private static final long serialVersionUID = 5482676644841105728L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "proj_fitness_manual_workout_id")
    private Integer projFitnessManualWorkoutId;

    @ApiModelProperty(value = "proj_fitness_exercise_video_id")
    private Integer projFitnessExerciseVideoId;

    @ApiModelProperty(value = "Exercise Circuit")
    private Integer exerciseCircuit;

    @ApiModelProperty(value = "Unit Name")
    private ManualTypeEnums unitName;

    @ApiModelProperty(value = "preview_duration")
    private Integer previewDuration;

    @ApiModelProperty(value = "video_duration")
    private Integer videoDuration;
}
