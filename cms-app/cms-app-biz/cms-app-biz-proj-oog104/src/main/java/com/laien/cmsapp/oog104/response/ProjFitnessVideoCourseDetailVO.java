package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog104.enums.course.FitnessCoursePlayTypeEnums;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * Dish
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjFitnessVideoCourseDetailVO extends BaseTableCodeVO  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "name")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "playType")
    private FitnessCoursePlayTypeEnums playType;

    @ApiModelProperty(value = "type")
    private FitnessCourseTypeEnums type;

    @ApiModelProperty(value = "courseType")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "m3u8地址")
    @AbsoluteR2Url
    private String videoUrl;

    @ApiModelProperty(value = "视频时长（毫秒）")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;
}
