package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog104.enums.dish.FitnessDishTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:31
 */
@Data
public class ProjFitnessDishListVO extends BaseTableCodeVO{

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "饮食类型, 100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT")
    private FitnessDishTypeEnums dishType;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

}
