package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessPlanProjFitnessWorkoutPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessPlanPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessPlanPubMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessPlanMapStruct;
import com.laien.cmsapp.oog104.request.ProjFitnessPlanDetailReq;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanDetailStageVO;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanListVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessPlanProjFitnessWorkoutPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessPlanPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessWorkoutPubService;
import com.laien.cmsapp.requst.LangReq;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_plan_pub 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
public class ProjFitnessPlanPubServiceImpl extends ServiceImpl<ProjFitnessPlanPubMapper, ProjFitnessPlanPub> implements IProjFitnessPlanPubService {

    @Resource
    private IProjFitnessPlanProjFitnessWorkoutPubService projFitnessPlanProjFitnessWorkoutPubService;
    @Resource
    private IProjFitnessWorkoutPubService projFitnessWorkoutPubService;
    @Resource
    private ProjFitnessPlanMapStruct projFitnessPlanMapStruct;
    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessPlanListVO> selectPlanlist(LangReq langReq) {
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjFitnessPlanPub> planList = this.list(new LambdaQueryWrapper<ProjFitnessPlanPub>()
                .eq(ProjFitnessPlanPub::getProjId, version.getProjId())
                .eq(ProjFitnessPlanPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjFitnessPlanPub::getVersion, version.getCurrentVersion()));
        projLmsI18nService.handleTextI18n(planList, ProjCodeEnums.OOG104);
        return projFitnessPlanMapStruct.toPlanListVO(planList);
    }

    @Override
    public ProjFitnessPlanDetailVO selectPlanDetail(Integer id, ProjFitnessPlanDetailReq detailReq) {
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjFitnessPlanPub plan = this.getOne(new LambdaQueryWrapper<ProjFitnessPlanPub>()
                .eq(ProjFitnessPlanPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjFitnessPlanPub::getVersion, version.getCurrentVersion())
                .eq(ProjFitnessPlanPub::getId, id));
        if (Objects.isNull(plan)) {
            return null;
        }
        projLmsI18nService.handleTextI18n(Collections.singletonList(plan), ProjCodeEnums.OOG104);

        // 获取workoutIds
        List<Integer> workoutIds = this.getWorkoutIds(id, version);

        // 去重复的workoutId
        Set<Integer> workoutIdSet = new HashSet<>(workoutIds);
        // 获取plan所有workout
        List<ProjFitnessWorkoutListVO> allWorkoutList = projFitnessWorkoutPubService.selectListByIds(workoutIdSet);

        // 按plan顺序排列workoutList
        List<ProjFitnessWorkoutListVO> planWorkoutList = this.arrangeByPlanWorkout(workoutIds, allWorkoutList);

        // 阶段数量分配
        List<Integer> stageCounts = Arrays.stream(plan.getStageCounts().split(StringPool.COMMA))
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        // 转换为VO
        ProjFitnessPlanDetailVO planDetailVO = projFitnessPlanMapStruct.toPlanDetailVO(plan);
        // 设置各个阶段的workout
        return planDetailVO.setStageList(this.splitToStageList(planWorkoutList, stageCounts));
    }

    /**
     * 获取plan workout ids
     * @param id id
     * @param version version
     * @return list
     */
    private List<Integer> getWorkoutIds(Integer id, ProjPublishCurrentVersionInfoBO version) {
        return projFitnessPlanProjFitnessWorkoutPubService.list(
                        new LambdaQueryWrapper<ProjFitnessPlanProjFitnessWorkoutPub>()
                                .select(ProjFitnessPlanProjFitnessWorkoutPub::getProjFitnessWorkoutId)
                                .eq(ProjFitnessPlanProjFitnessWorkoutPub::getVersion, version.getCurrentVersion())
                                .eq(ProjFitnessPlanProjFitnessWorkoutPub::getProjFitnessPlanId, id))
                .stream()
                .map(ProjFitnessPlanProjFitnessWorkoutPub::getProjFitnessWorkoutId)
                .collect(Collectors.toList());
    }

    /**
     * 根据plan workout id 顺序排列workoutList
     *
     * @param workoutIds workoutIds
     * @param allWorkoutList allWorkoutList
     */
    private List<ProjFitnessWorkoutListVO> arrangeByPlanWorkout(List<Integer> workoutIds,
                                                                  List<ProjFitnessWorkoutListVO> allWorkoutList) {
        Map<Integer, ProjFitnessWorkoutListVO> workoutListMap = allWorkoutList.stream()
                .collect(Collectors.toMap(ProjFitnessWorkoutListVO::getId, item -> item));
        List<ProjFitnessWorkoutListVO> workoutList = new ArrayList<>();
        for (Integer workoutId : workoutIds) {
            ProjFitnessWorkoutListVO workoutVO = workoutListMap.get(workoutId);
            if (workoutVO != null) {
                workoutList.add(workoutVO);
            }
        }

        return workoutList;
    }

    /**
     * 将workoutList按stageCounts拆分
     *
     * @param workoutList workoutList
     * @param stageCounts stageCounts
     * @return list
     */
    private List<ProjFitnessPlanDetailStageVO> splitToStageList(List<ProjFitnessWorkoutListVO> workoutList,
                                                                List<Integer> stageCounts) {
        List<ProjFitnessPlanDetailStageVO> stageList = new ArrayList<>();
        if (workoutList.isEmpty()) {
            return stageList;
        }
        int size = workoutList.size();
        int start = 0, end;
        for (Integer count : stageCounts) {
            end = Math.min(start + count, size);
            stageList.add(new ProjFitnessPlanDetailStageVO()
                    .setWorkoutList(workoutList.subList(start, end)));
            start = end;
        }

        return stageList;
    }


}
