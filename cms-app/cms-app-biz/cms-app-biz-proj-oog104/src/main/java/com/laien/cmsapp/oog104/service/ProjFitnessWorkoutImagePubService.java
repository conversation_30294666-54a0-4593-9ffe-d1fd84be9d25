package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutImagePub;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.common.oog104.enums.DailyTypeMappingEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【proj_fitness_workout_image(proj_fitness_workout_image)】的数据库操作Service
* @createDate 2025-03-17 17:33:55
*/
public interface ProjFitnessWorkoutImagePubService extends IService<ProjFitnessWorkoutImagePub> {

    List<ProjFitnessWorkoutImagePub> match(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<ProjFitnessWorkoutImagePub> match(DailyHabitReq req, DailyTypeMappingEnums dailyTypeMapping, ProjPublishCurrentVersionInfoBO versionInfoBO, ExclusiveTypeEnums exclusiveType);
}
