package com.laien.cmsapp.oog104.controller;


import com.laien.cmsapp.oog104.request.ProjFitnessSoundReq;
import com.laien.cmsapp.oog104.response.ProjFitnessSoundVO;
import com.laien.cmsapp.oog104.service.IProjFitnessSoundService;
import com.laien.common.frame.controller.ResponseController;
import com.laien.common.frame.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "app端:Fitness sound")
@RestController
@RequestMapping("/oog104/fitnessSound")
public class ProjFitnessSoundController extends ResponseController {

    @Resource
    private IProjFitnessSoundService soundService;

    @ApiOperation(value = "fitness Sound list v1", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessSoundVO>> list(ProjFitnessSoundReq soundReq) {
        List<ProjFitnessSoundVO> soundList = soundService.selectSoundList(soundReq);
        return succ(soundList);
    }

}