package com.laien.cmsapp.oog104.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjFitnessIngredientPub extends BaseModel {

    private Integer version;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private String amount;

    @ApiModelProperty(value = "proj_unit表数据id")
    private Integer projFitnessUnitId;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projFitnessDishId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
