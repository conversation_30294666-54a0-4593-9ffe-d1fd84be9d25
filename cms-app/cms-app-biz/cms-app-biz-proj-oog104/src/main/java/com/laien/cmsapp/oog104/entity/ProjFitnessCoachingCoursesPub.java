package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.course.FitnessCourseTypeEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  ProjFitnessCoachingCoursesPub
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(autoResultMap = true)
@ApiModel(value = "ProjFitnessCoachingCoursesPub对象", description = "ProjFitnessCoachingCoursesPub")
public class ProjFitnessCoachingCoursesPub extends BaseModel  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_COACHING_COURSES;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "name")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "Home 竖版Image")
    private String courseImgUrl;

    @ApiModelProperty(value = "ageGroups")
    @TableField(typeHandler = ManualAgeGroupEnums.TypeHandler.class)
    private List<ManualAgeGroupEnums> ageGroups;

    @ApiModelProperty(value = "types")
    @TableField(typeHandler = FitnessCourseTypeEnums.TypeHandler.class)
    private List<FitnessCourseTypeEnums> types;

    @ApiModelProperty(value = "difficulty")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "description")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "projFitnessCoachId")
    private Integer projFitnessCoachId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer sorted;
}
