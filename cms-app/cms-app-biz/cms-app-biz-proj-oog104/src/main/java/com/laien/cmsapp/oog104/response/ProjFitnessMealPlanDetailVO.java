package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/9 13:44
 */
@Data
public class ProjFitnessMealPlanDetailVO extends BaseTableCodeVO{

    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "eventName")
    private String eventName;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "标签值，以英文逗号做分隔，如A,B,C 可为空")
    private String keywords;

    @ApiModelProperty(value = "描述")
    @JsonProperty(value = "desc")
    private String description;

    @ApiModelProperty(value = "plan天数")
    private Integer days;

    @ApiModelProperty(value = "daily dish list")
    private List<ProjFitnessDailyDishListVO> dailyDishList = Collections.emptyList();

}
