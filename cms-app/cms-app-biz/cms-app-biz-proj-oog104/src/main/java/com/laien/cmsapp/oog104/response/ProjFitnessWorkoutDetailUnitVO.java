package com.laien.cmsapp.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness workout unit", description = "Fitness workout unit")
public class ProjFitnessWorkoutDetailUnitVO {

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "动作数量")
    private Integer count;

    @ApiModelProperty(value = "video list")
    private List<ProjFitnessVideoDetailVO> videoList;

}
