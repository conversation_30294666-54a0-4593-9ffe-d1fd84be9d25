package com.laien.cmsapp.oog104.mapstruct;

import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutGenerate;
import com.laien.cmsapp.oog104.response.ProjFitnessRefreshWorkoutDetailVO;
import com.laien.cmsapp.util.TimeConvertUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collections;
import java.util.List;

/**
 * workout 对象转换接口
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper(componentModel = "spring", imports = TimeConvertUtil.class)
public interface ProjFitnessWorkoutGenerateMapStruct {

    /**
     * <p>entity 转 refresh VO</p>
     *
     * @param entity entity
     * @return com.laien.cmsapp.response.ProjFitnessRefreshWorkoutDetailVO
     * <AUTHOR>
     * @date 2025/3/20 11:19
     */
    @Mapping(ignore = true, target = "audioJsonList")
    @Mapping(ignore = true, target = "unitList")
    @Mapping(target = "target",source = "target",qualifiedByName = "toList")
    @Mapping(target = "equipment", source = "equipment", qualifiedByName = "toList")
    ProjFitnessRefreshWorkoutDetailVO toRefreshWorkoutDetailVO(ProjFitnessWorkoutGenerate entity);

    @Named("toList")
    default <T>List<T> toList(T source) {
        return  Collections.singletonList(source);
    }
}
