package com.laien.cmsapp.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog104.entity.ProjFitnessCoachingCoursesPub;
import com.laien.common.response.IdAndCountsAndMaxMinRes;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * ProjFitnessCoachingCoursesPubMapper
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
public interface ProjFitnessCoachingCoursesPubMapper extends BaseMapper<ProjFitnessCoachingCoursesPub> {

    @Select(" SELECT " +
                    "      ccr.proj_fitness_coaching_courses_id AS id, " +
                    "      count(*) counts, " +
                    "      max(v.duration) `maxValue`, " +
                    "      min(v.duration) `minValue` " +
                    "  FROM " +
                    "      proj_fitness_coaching_courses_pub cc " +
                    "          INNER JOIN proj_fitness_coaching_courses_relation_pub ccr ON cc.id = ccr.proj_fitness_coaching_courses_id " +
                    "          INNER JOIN proj_fitness_video_course_pub v ON v.id = ccr.proj_fitness_video_course_id " +
                    "  WHERE " +
                    "      cc.proj_id = #{projId} " +
                    "    AND cc.version = #{version} " +
                    "    AND cc.`status` = 1 " +
                    "    AND cc.del_flag = 0 " +
                    "    AND ccr.del_flag = 0 " +
                    "    AND ccr.version = #{version} " +
                    "    AND v.del_flag = 0 " +
                    "    AND v.`status` = 1 " +
                    "    AND v.`version` = #{version} " +
                    "  GROUP BY " +
                    "      ccr.proj_fitness_coaching_courses_id")
    List<IdAndCountsAndMaxMinRes> selectCoachingCoursesCounts(Integer projId, Integer version);
}
