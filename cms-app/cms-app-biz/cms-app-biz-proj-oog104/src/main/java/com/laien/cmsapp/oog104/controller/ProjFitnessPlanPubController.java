package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.oog104.enums.M3u8TypeEnums;
import com.laien.cmsapp.oog104.request.ProjFitnessPlanDetailReq;
import com.laien.cmsapp.requst.LangReq;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessPlanPubService;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * proj_fitness_plan_pub 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Api(tags = "app端：Fitness Plan")
@RestController
@RequestMapping("/oog104/fitnessPlan")
public class ProjFitnessPlanPubController extends ResponseController {

    @Resource
    private IProjFitnessPlanPubService projFitnessPlanPubService;

    @ApiOperation(value = "Fitness Plan list", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessPlanListVO>> list(LangReq langReq) {
        return succ(projFitnessPlanPubService.selectPlanlist(langReq));
    }

    @ApiOperation(value = "Fitness Plan detail", tags = {"oog104"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjFitnessPlanDetailVO> detail(@PathVariable Integer id, ProjFitnessPlanDetailReq detailReq) {
        Integer m3u8Type = detailReq.getM3u8Type();
        if (m3u8Type == null) {
            detailReq.setM3u8Type(M3u8TypeEnums.M3U8.getCode());
        }

        return succ(projFitnessPlanPubService.selectPlanDetail(id, detailReq));
    }

}
