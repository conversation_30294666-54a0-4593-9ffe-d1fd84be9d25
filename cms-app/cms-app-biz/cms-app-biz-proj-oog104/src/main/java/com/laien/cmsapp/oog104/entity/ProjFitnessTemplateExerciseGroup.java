package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * proj_fitness_template_exercise_group
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="proj_fitness_template_exercise_group",autoResultMap = true)
@Data
public class ProjFitnessTemplateExerciseGroup extends BaseModel  implements AppTextCoreI18nModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_TEMPLATE_EXERCISE_GROUP;

   /**
    * 项目ID
    */
    @ApiModelProperty("项目ID")
    private Integer projId;

   /**
    * 模板ID
    */
    @ApiModelProperty("模板ID")
    private Integer projFitnessTemplateId;

   /**
    * exercise组名称
    */
    @ApiModelProperty("exercise组名称")
    @AppTextTranslateField
    private String groupName;

   /**
    * exercise组类型
    */
    @ApiModelProperty("exercise组类型")
    private ManualTypeEnums groupType;

   /**
    * 数量
    */
    @ApiModelProperty("数量")
    private Integer count;

   /**
    * 播放循环次数
    */
    @ApiModelProperty("播放循环次数")
    private Integer rounds;

}
