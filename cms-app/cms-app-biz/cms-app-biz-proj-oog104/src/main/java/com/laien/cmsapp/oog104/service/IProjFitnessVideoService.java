package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog104.entity.ProjFitnessVideo;
import com.laien.cmsapp.oog104.response.ProjFitnessVideoDetailVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_fitness_video 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface IProjFitnessVideoService extends IService<ProjFitnessVideo> {

    /**
     * 根据id 列表 查询详情
     *
     * @param idCollection idCollection
     * @param m3u8Type m3u8Type
     * @return list
     */
    List<ProjFitnessVideoDetailVO> selectDetailByIds(Collection<Integer> idCollection, Integer m3u8Type);

}
