package com.laien.cmsapp.oog104.response;

import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/3/17
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class ProjFitnessDailyHabitWorkoutDetailVO extends ProjFitnessGenerateWorkoutDetailVO {

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

}
