package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutImagePub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessWorkoutImagePubMapper;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.cmsapp.oog104.service.ProjFitnessWorkoutImagePubService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog104.enums.DailyTypeMappingEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.plan.SpecialPeriodTargetDistributionEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【proj_fitness_workout_image(proj_fitness_workout_image)】的数据库操作Service实现
 * @createDate 2025-03-17 17:33:55
 */
@Slf4j
@Service
public class ProjFitnessWorkoutImagePubServiceImpl extends ServiceImpl<ProjFitnessWorkoutImagePubMapper, ProjFitnessWorkoutImagePub>
        implements ProjFitnessWorkoutImagePubService {

    public static final Integer PLAN_DAY = 21;
    public static final Integer MAX_DAILY_HABIT_COUNT = 30;
    public static final Integer MIN_DAILY_HABIT_COUNT = 16;

    @Override
    public List<ProjFitnessWorkoutImagePub> match(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        List<ProjFitnessWorkoutImagePub> imageList = list(planReq, versionInfoBO);
        if (CollUtil.isEmpty(imageList)) {
            return imageList;
        }
        Collections.shuffle(imageList);
        List<ProjFitnessWorkoutImagePub> matchedImageList;
        if (ExclusiveTypeEnums.NORMAL == planReq.getExclusiveType()) {
            matchedImageList = matchNormal(imageList, planReq);
        }else {
            matchedImageList = matchSpecialPeriod(imageList, planReq);
        }
        return sort(planReq, matchedImageList);
    }

    @Override
    public List<ProjFitnessWorkoutImagePub> match(DailyHabitReq req, DailyTypeMappingEnums dailyTypeMapping, ProjPublishCurrentVersionInfoBO versionInfoBO, ExclusiveTypeEnums exclusiveType) {
        LambdaQueryWrapper<ProjFitnessWorkoutImagePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutImagePub::getExclusiveType, exclusiveType)
                .eq(ProjFitnessWorkoutImagePub::getTarget, dailyTypeMapping.getTarget())
                .eq(ProjFitnessWorkoutImagePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjFitnessWorkoutImagePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjFitnessWorkoutImagePub::getVersion, versionInfoBO.getCurrentVersion());
        List<ProjFitnessWorkoutImagePub> imageList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(imageList)) {
            log.error("daily habit count is less than min daily habit count, req: {},count: 0, exclusiveType: {}", req, exclusiveType);
            return imageList;
        }
        Collections.shuffle(imageList);
        int size = imageList.size();
        if (size > MAX_DAILY_HABIT_COUNT) {
            imageList = CollUtil.sub(imageList, 0, MAX_DAILY_HABIT_COUNT);
        }
        if (size < MIN_DAILY_HABIT_COUNT) {
            log.error("daily habit count is less than min daily habit count, req: {}, count: {}", req, size);
        }
        return imageList;
    }

    private List<ProjFitnessWorkoutImagePub> sort(FitnessWorkoutGeneratePlanReq planReq, List<ProjFitnessWorkoutImagePub> imageList) {
        Set<ManualTargetEnums> targetSet = planReq.getTargetSet();
        Map<ManualTargetEnums, List<ProjFitnessWorkoutImagePub>> targetGroup = imageList.stream().collect(Collectors.groupingBy(ProjFitnessWorkoutImagePub::getTarget));
        List<ProjFitnessWorkoutImagePub> fullBodyImageList = targetGroup.getOrDefault(ManualTargetEnums.FULL_BODY, new ArrayList<>());
        targetGroup.remove(ManualTargetEnums.FULL_BODY);

        if(ExclusiveTypeEnums.NORMAL != planReq.getExclusiveType()){
            return specialPeriodSort(targetGroup, fullBodyImageList);
        }
        if (CollUtil.isEmpty(targetSet) || targetSet.size() == 5 || targetSet.contains(ManualTargetEnums.FULL_BODY)) {
            // full body的和special period的排序一致，直接复用
            return specialPeriodSort(targetGroup, fullBodyImageList);
        }
        return normalSort(targetGroup, fullBodyImageList);
    }

    private static List<ProjFitnessWorkoutImagePub> specialPeriodSort(Map<ManualTargetEnums, List<ProjFitnessWorkoutImagePub>> targetGroup, List<ProjFitnessWorkoutImagePub> fullBodyImageList) {
        List<ProjFitnessWorkoutImagePub> sortedImageList = new ArrayList<>();
        List<ManualTargetEnums> targetList = new ArrayList<>(targetGroup.keySet());
        Collections.shuffle(targetList);

        for (int i = 0; i < PLAN_DAY; i++) {
            for (ManualTargetEnums target : targetList) {
                List<ProjFitnessWorkoutImagePub> targetImageList = targetGroup.getOrDefault(target, new ArrayList<>());
                if(CollUtil.isNotEmpty(fullBodyImageList)) {
                    sortedImageList.add(fullBodyImageList.remove(0));
                }
                if(CollUtil.isNotEmpty(targetImageList)){
                    sortedImageList.add(targetImageList.remove(0));
                }
            }
        }
        return sortedImageList;
    }

    private static List<ProjFitnessWorkoutImagePub> normalSort(Map<ManualTargetEnums, List<ProjFitnessWorkoutImagePub>> targetGroup, List<ProjFitnessWorkoutImagePub> fullBodyImageList) {
        List<ProjFitnessWorkoutImagePub> sortedImageList = new ArrayList<>();
        List<ManualTargetEnums> targetList = new ArrayList<>(targetGroup.keySet());
        Collections.shuffle(targetList);

        for (int i = 0; i < PLAN_DAY; i++) {
            if (CollUtil.isNotEmpty(fullBodyImageList)) {
                sortedImageList.add(fullBodyImageList.remove(0));
            }
            for (ManualTargetEnums target : targetList) {
                List<ProjFitnessWorkoutImagePub> targetImageList = targetGroup.getOrDefault(target, new ArrayList<>());
                if (CollUtil.isNotEmpty(targetImageList)) {
                    sortedImageList.add(targetImageList.remove(0));
                }
            }
        }
        return sortedImageList;
    }


    private List<ProjFitnessWorkoutImagePub> matchNormal(List<ProjFitnessWorkoutImagePub> imageList, FitnessWorkoutGeneratePlanReq planReq) {
        Set<ManualTargetEnums> targetSet = planReq.getTargetSet();
        Map<ManualTargetEnums, List<ProjFitnessWorkoutImagePub>> targetGroup = imageList.stream().collect(Collectors.groupingBy(ProjFitnessWorkoutImagePub::getTarget));
        List<ProjFitnessWorkoutImagePub> workoutImageList = new ArrayList<>();
        List<ProjFitnessWorkoutImagePub> targetImageList = targetGroup.getOrDefault(ManualTargetEnums.FULL_BODY, new ArrayList<>());
        if(CollUtil.isEmpty(targetSet) || targetSet.size() == 5 || targetSet.contains(ManualTargetEnums.FULL_BODY) ||  targetSet.contains(ManualTargetEnums.NONE)) {
            workoutImageList.addAll(CollUtil.sub(targetImageList, 0, 12));

            targetImageList = targetGroup.getOrDefault(ManualTargetEnums.BUTT, new ArrayList<>());
            workoutImageList.addAll(CollUtil.sub(targetImageList, 0, 2));

            targetImageList = targetGroup.getOrDefault(ManualTargetEnums.LEGS, new ArrayList<>());
            workoutImageList.addAll(CollUtil.sub(targetImageList, 0, 2));

            targetImageList = targetGroup.getOrDefault(ManualTargetEnums.ABS, new ArrayList<>());
            workoutImageList.addAll(CollUtil.sub(targetImageList, 0, 5));
            return workoutImageList;
        }
        int fullBodyCount;
        int size = targetSet.size();
        if (1 == size) {
            fullBodyCount = 12;
        } else if (2 == size) {
            fullBodyCount = 9;
        } else if (3 == size) {
            fullBodyCount = 9;
        } else {
            fullBodyCount = 5;
        }
        workoutImageList.addAll(CollUtil.sub(targetImageList, 0, fullBodyCount));
        int commonCount = (PLAN_DAY - fullBodyCount) / size;
        for (ManualTargetEnums target : targetSet) {
            targetImageList = targetGroup.getOrDefault(target, new ArrayList<>());
            workoutImageList.addAll(CollUtil.sub(targetImageList, 0, commonCount));
        }
        return workoutImageList;
    }

    private List<ProjFitnessWorkoutImagePub> matchSpecialPeriod(List<ProjFitnessWorkoutImagePub> imageList, FitnessWorkoutGeneratePlanReq planReq) {
        SpecialPeriodTargetDistributionEnums targetDistribution = SpecialPeriodTargetDistributionEnums.get(planReq.getExclusiveType());
        Map<ManualTargetEnums, List<ProjFitnessWorkoutImagePub>> targetGroup = imageList.stream().collect(Collectors.groupingBy(ProjFitnessWorkoutImagePub::getTarget));

        List<ProjFitnessWorkoutImagePub> matchedImageList = new ArrayList<>();
        for (SpecialPeriodTargetDistributionEnums.TargetCount targetCount : targetDistribution.getTargetCountList()) {
            List<ProjFitnessWorkoutImagePub> workoutImageList = targetGroup.getOrDefault(targetCount.getTarget(), new ArrayList<>());
            matchedImageList.addAll(CollUtil.sub(workoutImageList, 0, targetCount.getCount()));
        }
        return matchedImageList;

    }

    private List<ProjFitnessWorkoutImagePub> list(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFitnessWorkoutImagePub> wrapper = new LambdaQueryWrapper<>();
        ExclusiveTypeEnums exclusiveType = planReq.getExclusiveType();
        wrapper.eq(ProjFitnessWorkoutImagePub::getExclusiveType, exclusiveType)
                //新过滤 template type 字段
                .eq(ProjFitnessWorkoutImagePub :: getTemplateType,planReq.getTemplateType())
                .eq(ProjFitnessWorkoutImagePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjFitnessWorkoutImagePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjFitnessWorkoutImagePub::getVersion, versionInfoBO.getCurrentVersion())
                .orderByAsc(ProjFitnessWorkoutImagePub::getSortNo)
                .orderByDesc(BaseModel::getId);
        List<ProjFitnessWorkoutImagePub> imageList = list(wrapper);
        if (ExclusiveTypeEnums.NORMAL != exclusiveType) {
            return imageList;
        }
        ManualAgeGroupEnums ageGroup = planReq.getAgeGroup();
        return imageList.stream()
                .filter(item -> item.getAgeGroup().contains(ageGroup))
                .collect(Collectors.toList());
    }
}




