package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishCollectionDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishCollectionListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessDishCollectionPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 20:42
 */
@Api(tags = "app端：Fitness DishCollection")
@RestController
@RequestMapping("/{appCode}/fitnessDishCollection")
public class ProjFitnessDishCollectionPubController extends ResponseController {

    @Resource
    IProjFitnessDishCollectionPubService dishCollectionPubService;

    @ApiOperation(value = "获取dish collection 列表, 只获取状态为enable的数据", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessDishCollectionListVO>> listDishCollection(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjFitnessDishCollectionListVO> dishCollections = dishCollectionPubService.listDishCollection(versionInfoBO, GlobalConstant.STATUS_ENABLE, lang);
        return succ(dishCollections);
    }

    @ApiOperation(value = "获取一个dish collection下的Dish列表", tags = {"oog104"})
    @GetMapping("/v1/detail")
    public ResponseResult<ProjFitnessDishCollectionDetailVO> listDailyDish(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang,
                                                                           @RequestParam Integer dishCollectionId,
                                                                           @RequestParam Integer code) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjFitnessDishCollectionDetailVO dishListVOS = dishCollectionPubService.getDishCollectionDetail(versionInfoBO, dishCollectionId, lang);
        return succ(dishListVOS);
    }

}
