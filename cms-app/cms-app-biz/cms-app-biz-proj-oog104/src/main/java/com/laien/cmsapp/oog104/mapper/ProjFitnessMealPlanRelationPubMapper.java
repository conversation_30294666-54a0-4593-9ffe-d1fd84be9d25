package com.laien.cmsapp.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog104.entity.ProjFitnessMealPlanRelationPub;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:51
 */
public interface ProjFitnessMealPlanRelationPubMapper extends BaseMapper<ProjFitnessMealPlanRelationPub> {

    @Select(value = "select id, version, proj_fitness_meal_plan_id, `day`, dish_type, proj_fitness_dish_id from proj_fitness_meal_plan_relation_pub where version = #{version} and proj_fitness_meal_plan_id = #{mealPlanId} and del_flag = 0")
    List<ProjFitnessMealPlanRelationPub> listByVersionAndMealPlanId(Integer version, Integer mealPlanId);

}
