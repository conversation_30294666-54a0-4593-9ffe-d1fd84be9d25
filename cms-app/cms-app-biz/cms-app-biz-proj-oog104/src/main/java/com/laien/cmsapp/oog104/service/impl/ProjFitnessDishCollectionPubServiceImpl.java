package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishCollectionPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessDishCollectionRelationPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessDishCollectionPubMapper;
import com.laien.cmsapp.oog104.mapper.ProjFitnessDishCollectionRelationPubMapper;
import com.laien.cmsapp.oog104.response.ProjFitnessDishCollectionDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishCollectionListVO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessDishCollectionPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessDishPubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:09
 */
@Service
public class ProjFitnessDishCollectionPubServiceImpl extends ServiceImpl<ProjFitnessDishCollectionPubMapper, ProjFitnessDishCollectionPub> implements IProjFitnessDishCollectionPubService {

    @Resource
    private IProjFitnessDishPubService dishPubService;

    @Resource
    private ProjFitnessDishCollectionRelationPubMapper collectionRelationPubMapper;

    @Resource
    private IProjLmsI18nService projLmsI18nService;
    @Override
    public List<ProjFitnessDishCollectionListVO> listDishCollection(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang) {

        List<ProjFitnessDishCollectionPub> dishCollectionPubList = listByStatusAndVersion(versionInfoBO, status);
        if (CollectionUtils.isEmpty(dishCollectionPubList)) {
            return Collections.emptyList();
        }

        projLmsI18nService.handleTextI18n(dishCollectionPubList, ProjCodeEnums.OOG104,lang);
        return dishCollectionPubList.stream().map(this::convert2ListVO).collect(Collectors.toList());
    }


    private ProjFitnessDishCollectionListVO convert2ListVO(ProjFitnessDishCollectionPub dishCollectionPub) {

        ProjFitnessDishCollectionListVO ProjFitnessDishCollectionListVO = new ProjFitnessDishCollectionListVO();
        BeanUtils.copyProperties(dishCollectionPub, ProjFitnessDishCollectionListVO);
        return ProjFitnessDishCollectionListVO;
    }

    private List<ProjFitnessDishCollectionPub> listByStatusAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status) {

        LambdaQueryWrapper<ProjFitnessDishCollectionPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(status), ProjFitnessDishCollectionPub::getStatus, status);
        queryWrapper.eq(ProjFitnessDishCollectionPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessDishCollectionPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.orderByAsc(ProjFitnessDishCollectionPub::getSorted);
        return list(queryWrapper);
    }

    private ProjFitnessDishCollectionPub getByIdAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishCollectionId) {

        LambdaQueryWrapper<ProjFitnessDishCollectionPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessDishCollectionPub::getId, dishCollectionId);
        queryWrapper.eq(ProjFitnessDishCollectionPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjFitnessDishCollectionPub::getProjId, versionInfoBO.getProjId());
        return getOne(queryWrapper);
    }

    @Override
    public ProjFitnessDishCollectionDetailVO getDishCollectionDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishPlanId, String lang) {

        ProjFitnessDishCollectionPub dishCollectionPub = getByIdAndVersion(versionInfoBO, dishPlanId);
        if (Objects.isNull(dishCollectionPub)) {
            return null;
        }

        projLmsI18nService.handleTextI18n(Lists.newArrayList(dishCollectionPub), ProjCodeEnums.OOG104,lang);
        ProjFitnessDishCollectionDetailVO detailVO = new ProjFitnessDishCollectionDetailVO();
        BeanUtils.copyProperties(dishCollectionPub, detailVO);

        List<ProjFitnessDishCollectionRelationPub> relationPubList = collectionRelationPubMapper.listByVersionAndDishCollectionId(versionInfoBO.getCurrentVersion(), dishPlanId);
        if (CollectionUtils.isEmpty(relationPubList)) {
            return detailVO;
        }

        Set<Integer> dishIds = relationPubList.stream().map(ProjFitnessDishCollectionRelationPub::getProjFitnessDishId).collect(Collectors.toSet());
        List<ProjFitnessDishListVO> dishListVOList = dishPubService.listDish(versionInfoBO, dishIds, null, lang);
        if (CollectionUtils.isEmpty(dishListVOList)) {
            return detailVO;
        }

        Map<Integer, ProjFitnessDishListVO> dishIdMap = dishListVOList.stream().collect(Collectors.toMap(ProjFitnessDishListVO::getId, Function.identity()));
        List<ProjFitnessDishListVO> dishList = relationPubList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjFitnessDishId())).map(relation -> setType4Dish(dishIdMap.get(relation.getProjFitnessDishId()), relation)).collect(Collectors.toList());
        detailVO.setDishList(dishList);
        return detailVO;
    }

    private ProjFitnessDishListVO setType4Dish(ProjFitnessDishListVO dishListVO, ProjFitnessDishCollectionRelationPub relationPub) {

        if (Objects.nonNull(relationPub.getDishType())) {
            dishListVO.setDishType(relationPub.getDishType());
        }
        return dishListVO;
    }

}
