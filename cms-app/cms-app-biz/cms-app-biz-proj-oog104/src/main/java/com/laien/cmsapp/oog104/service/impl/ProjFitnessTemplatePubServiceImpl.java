package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessTemplatePub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessTemplatePubMapper;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.cmsapp.oog104.service.ProjFitnessTemplatePubService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog104.enums.DailyTypeMappingEnums;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【proj_fitness_template(proj_fitness_template)】的数据库操作Service实现
 * @createDate 2025-03-11 17:01:12
 */
@Service
public class ProjFitnessTemplatePubServiceImpl extends ServiceImpl<ProjFitnessTemplatePubMapper, ProjFitnessTemplatePub>
        implements ProjFitnessTemplatePubService {

    @Override
    public List<ProjFitnessTemplatePub> list(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFitnessTemplatePub> wrapper = new LambdaQueryWrapper<>();
        Set<ExerciseVideoSpecialLimitEnums> specialLimitSet = planReq.getSpecialLimitSet();
        ExclusiveTypeEnums exclusiveType = planReq.getExclusiveType();
        ManualDifficultyEnums difficulty = planReq.getDifficulty();
        if (exclusiveType == ExclusiveTypeEnums.POSTPARTUM) {
            difficulty = ManualDifficultyEnums.BEGINNER;
        } else if (exclusiveType == ExclusiveTypeEnums.INJURY) {
            difficulty = ManualDifficultyEnums.NEWBIE;
        }
        specialLimitSet = null == specialLimitSet ? new HashSet<>() : specialLimitSet;
        if (exclusiveType == ExclusiveTypeEnums.PREGNANT || exclusiveType == ExclusiveTypeEnums.POSTPARTUM) {
            specialLimitSet.add(ExerciseVideoSpecialLimitEnums.ABS);
        }

        //v8.3.0添加过滤条件 template type
        wrapper.eq(ProjFitnessTemplatePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjFitnessTemplatePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjFitnessTemplatePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjFitnessTemplatePub::getDurationRange, planReq.getDurationRange())
                .eq(ProjFitnessTemplatePub::getLevel, difficulty)
                .eq(ProjFitnessTemplatePub::getTemplateType,planReq.getTemplateType());

        List<ProjFitnessTemplatePub> templateList = list(wrapper);
        Set<ExerciseVideoSpecialLimitEnums> specialLimitSetFinal = specialLimitSet;
        //当 special limit 为空时,返回所有数据
        if(CollUtil.isEmpty(specialLimitSet)) {
             return templateList;
        }
        //排除Special Limit为空的Workout,删除判定逻辑CollUtil.isEmpty(templatePub.getSpecialLimit())
        return templateList.stream()
                .filter(templatePub -> CollUtil.containsAll(templatePub.getSpecialLimit(), specialLimitSetFinal))
                .collect(Collectors.toList());
    }

    @Override
    public List<ProjFitnessTemplatePub> list(DailyHabitReq req, DailyTypeMappingEnums dailyTypeMapping, TemplateTypeEnums templateType, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFitnessTemplatePub> wrapper = new LambdaQueryWrapper<>();

        List<ManualDifficultyEnums> difficultyList = dailyTypeMapping.getDifficultyList();
        if(CollUtil.isEmpty(difficultyList)){
            difficultyList = Collections.singletonList(req.getDifficulty());
        }
        wrapper.eq(ProjFitnessTemplatePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjFitnessTemplatePub::getTemplateType, templateType)
                .in(ProjFitnessTemplatePub::getLevel, difficultyList)
                .in(ProjFitnessTemplatePub::getDurationRange, dailyTypeMapping.getDurationRangeList())
                .eq(ProjFitnessTemplatePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjFitnessTemplatePub::getStatus, GlobalConstant.STATUS_ENABLE);
        return baseMapper.selectList(wrapper);
    }
}




