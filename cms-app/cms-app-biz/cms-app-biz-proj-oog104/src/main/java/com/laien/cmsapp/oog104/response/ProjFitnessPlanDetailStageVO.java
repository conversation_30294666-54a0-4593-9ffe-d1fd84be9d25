package com.laien.cmsapp.oog104.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness plan detail stage", description = "Fitness plan detail stage")
public class ProjFitnessPlanDetailStageVO {

    @ApiModelProperty(value = "workout list")
    private List<ProjFitnessWorkoutListVO> workoutList;

}
