package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessAllergenPub;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:59
 */
public interface IProjFitnessAllergenPubService extends IService<ProjFitnessAllergenPub> {

    List<String> listByDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, String lang, FitnessAllergenRelationBusinessEnums businessEnum);

}
