package com.laien.cmsapp.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog104.entity.ProjFitnessVideo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_fitness_video Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface ProjFitnessVideoMapper extends BaseMapper<ProjFitnessVideo> {

    /**
     * 根据idList查询详情list
     *
     * @param idCollection idCollection
     * @return list
     */
    List<ProjFitnessVideo> selectListDetailByIds(@Param("idCollection") Collection<Integer> idCollection);

}
