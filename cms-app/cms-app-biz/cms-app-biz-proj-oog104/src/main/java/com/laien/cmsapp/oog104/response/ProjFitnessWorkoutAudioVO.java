package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "Fitness workout audioJson", description = "Fitness workout audioJson")
public class ProjFitnessWorkoutAudioVO {

    @ApiModelProperty(value = "语言")
    private String language;

    @AbsoluteR2Url
    @ApiModelProperty(value = "音频文件地址")
    private String audioJsonUrl;

}
