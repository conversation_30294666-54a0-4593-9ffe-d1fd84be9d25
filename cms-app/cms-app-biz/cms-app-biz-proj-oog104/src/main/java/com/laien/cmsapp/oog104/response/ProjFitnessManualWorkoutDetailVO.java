package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.oog104.enums.manual.ManualWorkoutTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  manual workout detail VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjFitnessManualWorkoutDetailVO extends ProjFitnessRefreshWorkoutDetailVO {

    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    @AbsoluteR2Url
    private String coverImage;

    @ApiModelProperty(value = "Detail Image, supports png/webp formats")
    @AbsoluteR2Url
    private String detailImage;

    @ApiModelProperty(value = "Description, maximum 1000 characters")
    private String description;

    @ApiModelProperty(value = "Workout Type")
    private ManualWorkoutTypeEnums workoutType;

    @ApiModelProperty(value = "Age Groups")
    private List<ManualAgeGroupEnums> ageGroup;

    @ApiModelProperty(value = "Categories")
    private List<ManualCategoryEnums> category;

    @ApiModelProperty(value = "workout Name")
    private String name;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;
}
