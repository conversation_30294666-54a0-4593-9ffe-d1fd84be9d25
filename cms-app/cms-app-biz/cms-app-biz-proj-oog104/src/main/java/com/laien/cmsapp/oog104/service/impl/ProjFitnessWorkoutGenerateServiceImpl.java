/*
 * Copyright © 2025 laien.io 成都莱嗯信息技术有限公司  All rights reserved.
 */
package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.*;
import com.laien.cmsapp.oog104.mapper.ProjFitnessExerciseVideoMapper;
import com.laien.cmsapp.oog104.mapper.ProjFitnessTemplateExerciseGroupMapper;
import com.laien.cmsapp.oog104.mapper.ProjFitnessWorkoutGenerateExerciseVideoMapper;
import com.laien.cmsapp.oog104.mapper.ProjFitnessWorkoutGenerateMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessExerciseVideoMapStruct;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessWorkoutGenerateMapStruct;
import com.laien.cmsapp.oog104.request.DailyHabitReq;
import com.laien.cmsapp.oog104.request.FitnessWorkoutGeneratePlanReq;
import com.laien.cmsapp.oog104.request.PlanWorkoutRefreshReq;
import com.laien.cmsapp.oog104.response.*;
import com.laien.cmsapp.oog104.service.*;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.oog104.enums.DailyTypeMappingEnums;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.mybatisplus.config.BaseModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.laien.common.oog104.enums.template.TemplateTypeEnums.REGULAR_FITNESS;

/**
 * <p>TODO </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessWorkoutGenerateServiceImpl extends ServiceImpl<ProjFitnessWorkoutGenerateMapper, ProjFitnessWorkoutGenerate>
        implements IProjFitnessWorkoutGenerateService {

    private final ProjFitnessExerciseVideoMapper exerciseVideoMapper;

    private final ProjFitnessWorkoutGenerateExerciseVideoMapper projFitnessWorkoutGenerateExerciseVideoMapper;

    private final ProjFitnessExerciseVideoMapStruct exerciseVideoMapStruct;

    private final ProjFitnessWorkoutGenerateMapStruct projFitnessWorkoutGenerateMapStruct;

    private final ProjFitnessTemplateExerciseGroupMapper projFitnessTemplateExerciseGroupMapper;

    private final ProjFitnessWorkoutImagePubService projFitnessWorkoutImagePubService;

    private final ProjFitnessTemplatePubService projFitnessTemplatePubService;

    private final IProjFitnessWorkoutGenerateI18nService i18nService;

    private final IProjFitnessExerciseVideoService videoService;

    private final ICoreTextTaskI18nPubService textTaskI18nPubService;

    private final IProjLmsI18nService projLmsI18nService;



    @Override
    public List<ProjFitnessGenerateWorkoutDetailVO> detailList(List<PlanWorkoutRefreshReq> req) {
        // 按table code 区分查询不同的表
        Map<Integer, List<PlanWorkoutRefreshReq>> tableCodeEnumsListMap = req.stream().collect(Collectors.groupingBy(PlanWorkoutRefreshReq::getCode));
        return tableCodeEnumsListMap.values().stream().map(list -> {
            TableCodeEnums tableCodeEnums = TableCodeEnums.get(list.get(0).getCode());
            if (Objects.equals(TableCodeEnums.PROJ_FITNESS_WORKOUT_GENERATE, tableCodeEnums)) {
                Map<Integer,Integer> idMap = list.stream().collect(Collectors.toMap(PlanWorkoutRefreshReq::getId,PlanWorkoutRefreshReq::getImgId));
                return this.selectDetailsByIdMap(idMap);
            }
            log.warn("table code is not support");
            return new ArrayList<ProjFitnessGenerateWorkoutDetailVO>();
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public ProjFitnessGenerateWorkoutPlanVO plan(FitnessWorkoutGeneratePlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        ProjFitnessGenerateWorkoutPlanVO plan = new ProjFitnessGenerateWorkoutPlanVO();
        List<ProjFitnessGenerateWorkoutDetailVO> workoutDetailList = new ArrayList<>();
        plan.setWorkoutDetailList(workoutDetailList);
        //判断 template type 是否为空,兜底默认 regular fitness
        if(Objects.isNull(planReq.getTemplateType()))
            planReq.setTemplateType(REGULAR_FITNESS);

        List<ProjFitnessWorkoutImagePub> imageList = projFitnessWorkoutImagePubService.match(planReq, versionInfoBO);
        if (CollUtil.isEmpty(imageList)) {
            log.error("fitness generate workout plan imageList is empty, planReq:{}", planReq);
            return plan;
        }
        projLmsI18nService.handleTextI18n(imageList, ProjCodeEnums.OOG104);
        List<ProjFitnessTemplatePub> templateList = projFitnessTemplatePubService.list(planReq, versionInfoBO);
        if (CollUtil.isEmpty(templateList)) {
            log.error("fitness generate workout plan template list is empty, planReq:{}", planReq);
            return plan;
        }
        Set<Integer> templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjFitnessWorkoutGenerate> workoutList = list(planReq, templateIdSet);
        if (CollUtil.isEmpty(workoutList)) {
            log.error("fitness generate workout list is empty, planReq:{}", planReq);
            return plan;
        }
        workoutDetailList = match(planReq, templateIdSet, imageList);
        plan.setWorkoutDetailList(workoutDetailList);
        return plan;
    }

    @Override
    public List<ProjFitnessDailyHabitWorkoutDetailVO> dailyHabitList(DailyHabitReq req, ProjPublishCurrentVersionInfoBO versionInfoBO, ExclusiveTypeEnums exclusiveType) {

        DailyTypeMappingEnums dailyTypeMapping = DailyTypeMappingEnums.get(exclusiveType);
        List<ProjFitnessWorkoutImagePub> imageList = projFitnessWorkoutImagePubService.match(req,dailyTypeMapping , versionInfoBO, exclusiveType);
        if(CollUtil.isEmpty(imageList)){
            log.error("daily habit image is empty, req:{}", req);
            return new ArrayList<>();
        }
        projLmsI18nService.handleTextI18n(imageList, ProjCodeEnums.OOG104);

        List<ProjFitnessTemplatePub> templateList = projFitnessTemplatePubService.list(req, dailyTypeMapping, REGULAR_FITNESS, versionInfoBO);
        if (CollUtil.isEmpty(templateList)) {
            log.error("daily habit template list is empty, req:{}", req);
            return new ArrayList<>();
        }
        Set<Integer> templateIdSet = templateList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjFitnessWorkoutGenerate> workoutList = match(templateIdSet, req, dailyTypeMapping);
        if(CollUtil.isEmpty(workoutList)){
            log.error("daily habit workout is empty, req:{}", req);
            return new ArrayList<>();
        }
        Collections.shuffle(workoutList);
        List<ProjFitnessGenerateWorkoutDetailVO> generateWorkoutList = toGenerateWorkoutDetailVO(imageList, workoutList);
        List<ProjFitnessDailyHabitWorkoutDetailVO> dailyHabitWorkoutList = new ArrayList<>();
        if (CollUtil.isEmpty(generateWorkoutList)) {
            return dailyHabitWorkoutList;
        }
        for (ProjFitnessGenerateWorkoutDetailVO workout : generateWorkoutList) {
            ProjFitnessDailyHabitWorkoutDetailVO dailyHabitWorkout = new ProjFitnessDailyHabitWorkoutDetailVO();
            BeanUtils.copyProperties(workout, dailyHabitWorkout);
            dailyHabitWorkout.setExclusiveType(exclusiveType);
            dailyHabitWorkoutList.add(dailyHabitWorkout);
        }
        return dailyHabitWorkoutList;
    }

    private List<ProjFitnessWorkoutGenerate> match(Set<Integer> templateIdSet, DailyHabitReq req, DailyTypeMappingEnums dailyTypeMapping) {
        List<ManualDifficultyEnums> difficultyList = dailyTypeMapping.getDifficultyList();
        if(CollUtil.isEmpty(difficultyList)){
            difficultyList = Collections.singletonList(req.getDifficulty());
        }
        Set<ExerciseVideoSpecialLimitEnums> specialLimitSet = req.getSpecialLimitSet();
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessWorkoutGenerate::getDifficulty, difficultyList)
                .eq(ProjFitnessWorkoutGenerate::getTarget, dailyTypeMapping.getTarget())
                .in(ProjFitnessWorkoutGenerate::getProjFitnessTemplateId, templateIdSet);
        List<ProjFitnessWorkoutGenerate> workoutList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(workoutList)){
            return workoutList;
        }

        return workoutList.stream()
                .filter(item -> CollUtil.containsAll(item.getSpecialLimit(), specialLimitSet) || CollUtil.isEmpty(item.getSpecialLimit()))
                .collect(Collectors.toList());
    }

    private List<ProjFitnessGenerateWorkoutDetailVO> match(FitnessWorkoutGeneratePlanReq planReq, Set<Integer> templateIdSet,List<ProjFitnessWorkoutImagePub> imageList) {
        List<ProjFitnessWorkoutGenerate> workoutList = list(planReq, templateIdSet);
        if(CollUtil.isEmpty(workoutList)){
            return new ArrayList<>();
        }
        Collections.shuffle(workoutList);
        return toGenerateWorkoutDetailVO(imageList, workoutList);
    }

    private List<ProjFitnessGenerateWorkoutDetailVO> toGenerateWorkoutDetailVO(List<ProjFitnessWorkoutImagePub> imageList, List<ProjFitnessWorkoutGenerate> workoutList) {
        Map<ManualTargetEnums, List<ProjFitnessWorkoutGenerate>> workoutTargetGroup = workoutList.stream().collect(Collectors.groupingBy(ProjFitnessWorkoutGenerate::getTarget));

        List<ProjFitnessWorkoutGenerate> matchedWorkoutList = new ArrayList<>();
        for (ProjFitnessWorkoutImagePub image : imageList) {
            List<ProjFitnessWorkoutGenerate> workoutGenerateList = workoutTargetGroup.getOrDefault(image.getTarget(), Collections.emptyList());
            if(CollUtil.isNotEmpty(workoutGenerateList)){
                ProjFitnessWorkoutGenerate workout = workoutGenerateList.remove(0);
                workout.setProjFitnessWorkoutImageId(image.getId());
                matchedWorkoutList.add(workout);
            }
        }
        Map<Integer, ProjFitnessWorkoutImagePub> imageIdGroup = imageList.stream()
                .collect(
                        Collectors.toMap(BaseModel::getId, Function.identity(),
                                (oldValue,
                                 newValue) -> newValue)
                );
        List<ProjFitnessRefreshWorkoutDetailVO> matchedRefreshWorkoutList = covertEntityList2voList(matchedWorkoutList);
        Map<Integer, ProjFitnessRefreshWorkoutDetailVO> idWorkoutGroup = matchedRefreshWorkoutList.stream()
                .collect(
                        Collectors.toMap(ProjFitnessRefreshWorkoutDetailVO::getId,
                                Function.identity(),
                                (oldValue, newValue) -> newValue )
                );
        List<ProjFitnessGenerateWorkoutDetailVO> workoutDetailList = new ArrayList<>();
        for (ProjFitnessWorkoutGenerate item : matchedWorkoutList) {
            ProjFitnessRefreshWorkoutDetailVO detailVO = idWorkoutGroup.get(item.getId());
            ProjFitnessGenerateWorkoutDetailVO generateWorkoutDetailVO = new ProjFitnessGenerateWorkoutDetailVO();
            BeanUtils.copyProperties(detailVO, generateWorkoutDetailVO);
            ProjFitnessWorkoutImagePub image = imageIdGroup.get(item.getProjFitnessWorkoutImageId());
            generateWorkoutDetailVO.setName(image.getName())
                    .setImgId(image.getId())
                    .setCoverImage(image.getCoverImage())
                    .setDetailImage(image.getDetailImage())
                    .setEventName("generate workout-" + detailVO.getId());
            workoutDetailList.add(generateWorkoutDetailVO);
        }
        return workoutDetailList;
    }

    private List<ProjFitnessWorkoutGenerate> list(FitnessWorkoutGeneratePlanReq planReq, Set<Integer> templateIdSet) {
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjFitnessWorkoutGenerate::getProjFitnessTemplateId, templateIdSet);
        return list(wrapper);
    }

    /**
     * <p>查询自动生成的workout详情</p>
     *
     * @param idMap workoutId-imgId
     * @return java.util.List<com.laien.cmsapp.response.ProjFitnessRefreshWorkoutDetailVO>
     * <AUTHOR>
     * @date 2025/3/20 10:11
     */
    private List<ProjFitnessGenerateWorkoutDetailVO> selectDetailsByIdMap(Map<Integer, Integer> idMap) {
        // 查询workout id
        LambdaQueryWrapper<ProjFitnessWorkoutGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjFitnessWorkoutGenerate::getId, idMap.keySet());
        queryWrapper.eq(ProjFitnessWorkoutGenerate::getDelFlag, GlobalConstant.NO);
        queryWrapper.eq(ProjFitnessWorkoutGenerate::getStatus, GlobalConstant.STATUS_ENABLE);

        List<ProjFitnessWorkoutGenerate> workoutList = this.list(queryWrapper);
        if (CollUtil.isEmpty(workoutList)) {
            return new ArrayList<>();
        }
        List<ProjFitnessRefreshWorkoutDetailVO> projFitnessRefreshWorkoutDetailVOS = covertEntityList2voList(workoutList);
        return this.fillImgInfo(projFitnessRefreshWorkoutDetailVOS,idMap);
    }

    private List<ProjFitnessGenerateWorkoutDetailVO> fillImgInfo(List<ProjFitnessRefreshWorkoutDetailVO> details, Map<Integer, Integer> idMap) {
        //query img info
        Map<Integer, ProjFitnessWorkoutImagePub> imgIdMap = new HashMap<>();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        if (CollUtil.isNotEmpty(idMap)) {
            Set<Integer> imgIdSet = new HashSet<>(idMap.values());
            List<ProjFitnessWorkoutImagePub> imagePubs = projFitnessWorkoutImagePubService.list(
                    new LambdaQueryWrapper<ProjFitnessWorkoutImagePub>().in(ProjFitnessWorkoutImagePub::getId,imgIdSet)
                            .eq(ProjFitnessWorkoutImagePub::getVersion,versionInfoBO.getCurrentVersion())
            );
            projLmsI18nService.handleTextI18n(imagePubs, ProjCodeEnums.OOG104);
            imgIdMap.putAll(imagePubs.stream().collect(Collectors.toMap(ProjFitnessWorkoutImagePub::getId, Function.identity(),
                             (oldValue, newValue) -> newValue )));
        }
        List<ProjFitnessGenerateWorkoutDetailVO> workoutDetailList = new ArrayList<>();
        for (ProjFitnessRefreshWorkoutDetailVO detailVO : details) {
            ProjFitnessGenerateWorkoutDetailVO generateWorkoutDetailVO = new ProjFitnessGenerateWorkoutDetailVO();
            BeanUtils.copyProperties(detailVO, generateWorkoutDetailVO);
            if (idMap != null && idMap.containsKey(detailVO.getId()) && imgIdMap.containsKey(idMap.get(detailVO.getId()))) {
                ProjFitnessWorkoutImagePub image = imgIdMap.get(idMap.get(detailVO.getId()));
                generateWorkoutDetailVO.setName(image.getName())
                        .setImgId(image.getId())
                        .setCoverImage(image.getCoverImage())
                        .setDetailImage(image.getDetailImage())
                        .setEventName("generate workout-" + detailVO.getId());
            }
            workoutDetailList.add(generateWorkoutDetailVO);
        }
        return workoutDetailList;
    }

    private List<ProjFitnessRefreshWorkoutDetailVO> covertEntityList2voList(List<ProjFitnessWorkoutGenerate> workoutList) {
        // 查询workout 和 video 关联关系
        List<Integer> workoutIdList = workoutList.stream().map(BaseModel::getId).collect(Collectors.toList());
        LambdaQueryWrapper<ProjFitnessWorkoutGenerateExerciseVideo> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.in(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessWorkoutGenerateId, workoutIdList);
        relationQueryWrapper.eq(ProjFitnessWorkoutGenerateExerciseVideo::getDelFlag, GlobalConstant.NO);
        relationQueryWrapper.orderByAsc(ProjFitnessWorkoutGenerateExerciseVideo::getId);

        List<ProjFitnessWorkoutGenerateExerciseVideo> relationList = projFitnessWorkoutGenerateExerciseVideoMapper.selectList(relationQueryWrapper);
        Set<Integer> videoIdList = relationList.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessExerciseVideoId).collect(Collectors.toSet());
        // 关联表对应的group，以便于填充group相关数据
        Set<Integer> groupIdSet = relationList.stream().map(ProjFitnessWorkoutGenerateExerciseVideo::getProjFitnessTemplateExerciseGroupId).collect(Collectors.toSet());
        List<ProjFitnessTemplateExerciseGroup> exerciseGroupList = projFitnessTemplateExerciseGroupMapper.selectBatchIds(groupIdSet);
        projLmsI18nService.handleTextI18n(exerciseGroupList, ProjCodeEnums.OOG104);
        // 查询video
        List<ProjFitnessExerciseVideo> exerciseVideoList = exerciseVideoMapper.selectBatchIds(videoIdList);
        List<ProjFitnessRefreshVideoVO> refreshVideoVOList = exerciseVideoMapStruct.toRefreshVideoVO(exerciseVideoList);
        videoService.handleVideoI18n(refreshVideoVOList);
        //query i18n audio Info
        Map<Integer, List<ProjFitnessWorkoutGenerateI18n>> audioI18nMap = i18nService.list(new LambdaQueryWrapper<ProjFitnessWorkoutGenerateI18n>()
                .in(ProjFitnessWorkoutGenerateI18n::getProjFitnessWorkoutGenerateId, workoutIdList)
        ).stream().collect(Collectors.groupingBy(ProjFitnessWorkoutGenerateI18n::getProjFitnessWorkoutGenerateId));
        // 组装数据
        return workoutList.stream().map(entity -> {
            ProjFitnessRefreshWorkoutDetailVO refreshWorkoutDetailVO = projFitnessWorkoutGenerateMapStruct.toRefreshWorkoutDetailVO(entity);
            // 过滤出该workout对应模板下的所有group
            List<ProjFitnessRefreshWorkoutDetailUnitVO> unitVOList = exerciseGroupList.stream()
                    .filter(group -> Objects.equals(group.getProjFitnessTemplateId(), entity.getProjFitnessTemplateId()))
                    .map(group -> {
                        // 填充unit
                        ProjFitnessRefreshWorkoutDetailUnitVO unitVO = new ProjFitnessRefreshWorkoutDetailUnitVO();
                        unitVO.setUnitName(group.getGroupName());
                        unitVO.setUnitNameType(group.getGroupType());
                        unitVO.setCount(group.getCount());
                        unitVO.setRounds(group.getRounds());
                        // 填充视频
                        List<ProjFitnessRefreshVideoVO> refreshVideoList = relationList.stream()
                                // workout下该分组的视频ID
                                .filter(relation -> Objects.equals(relation.getProjFitnessWorkoutGenerateId(), entity.getId()) && Objects.equals(relation.getProjFitnessTemplateExerciseGroupId(), group.getId()))
                                // 该ID对应的视频
                                .map(relation ->
                                        refreshVideoVOList.stream().filter(videoVO ->
                                                Objects.equals(relation.getProjFitnessExerciseVideoId(), videoVO.getId())).findAny().map(video -> {
                                                    //返回的video duration只算正式锻炼时间（排除preview duration时长）
                                                    video.setVideoDuration(relation.getVideoDuration() - relation.getPreviewDuration());
                                                    video.setPreviewDuration(relation.getPreviewDuration());
                                                    return video;
                                        }))
                                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
                        unitVO.setVideoList(refreshVideoList);
                        return unitVO;
                    }).collect(Collectors.toList());
            refreshWorkoutDetailVO.setUnitList(unitVOList);
            //other language
            List<ProjFitnessWorkoutGenerateI18n> i18nAudios = audioI18nMap.getOrDefault(entity.getId(), Collections.emptyList());
            List<AudioI18nVO> audioJsonList = new ArrayList();
            i18nAudios.forEach(i18n -> {
                AudioI18nVO audioI18nVO = new AudioI18nVO();
                audioI18nVO.setLanguage(i18n.getLanguage()).setAudioJsonUrl(i18n.getAudioJsonUrl());
                audioJsonList.add(audioI18nVO);
            });
            refreshWorkoutDetailVO.setAudioJsonList(audioJsonList);
            return refreshWorkoutDetailVO;
        }).collect(Collectors.toList());
    }
}
