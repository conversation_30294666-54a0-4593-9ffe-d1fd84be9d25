package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog104.entity.ProjFitnessExerciseVideo;
import com.laien.cmsapp.oog104.mapper.ProjFitnessExerciseVideoMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessExerciseVideoMapStruct;
import com.laien.cmsapp.oog104.response.ProjFitnessExerciseVideoDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessRefreshVideoVO;
import com.laien.cmsapp.oog104.service.IProjFitnessExerciseVideoService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *     proj_fitness_exercise_video 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjFitnessExerciseVideoServiceImpl extends ServiceImpl<ProjFitnessExerciseVideoMapper, ProjFitnessExerciseVideo>
        implements IProjFitnessExerciseVideoService {

    private final ProjFitnessExerciseVideoMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;

    @Override
    public Map<Integer, ProjFitnessRefreshVideoVO> getRefreshVideoMapByIds(Set<Integer> exerciseVideoIds) {
        Collection<ProjFitnessExerciseVideo> exerciseVideos = super.listByIds(exerciseVideoIds);
        List<ProjFitnessRefreshVideoVO> refreshVideoDetailVOS = mapStruct.toRefreshVideoVO(exerciseVideos);
        return refreshVideoDetailVOS.stream().collect(
                Collectors.toMap(ProjFitnessExerciseVideoDetailVO::getId, Function.identity()));
    }

    @Override
    public void handleVideoI18n(Collection<? extends ProjFitnessExerciseVideoDetailVO> videoList) {
        projLmsI18nService.handleSpeechI18nSingle(CollUtil.newArrayList(videoList), ProjCodeEnums.OOG104);
    }

}
