package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.response.ProjFitnessDishDetailVO;
import com.laien.cmsapp.oog104.service.IProjFitnessDishPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Author:  hhl
 * Date:  2025/1/7 20:42
 */
@Api(tags = "app端：Fitness Dish")
@RestController
@RequestMapping("/{appCode}/fitnessDish")
public class ProjFitnessDishPubController extends ResponseController {

    @Resource
    IProjFitnessDishPubService dishPubService;

    @ApiOperation(value = "获取一个dish的详情数据", tags = {"oog104"})
    @GetMapping("/v1/detail")
    public ResponseResult<ProjFitnessDishDetailVO> getDishDetail(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang,
                                                                 @RequestParam Integer dishId,
                                                                 @RequestParam Integer code) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjFitnessDishDetailVO dishListVOS = dishPubService.getDishDetail(versionInfoBO, dishId, lang, GlobalConstant.ONE);
        return succ(dishListVOS);
    }

}
