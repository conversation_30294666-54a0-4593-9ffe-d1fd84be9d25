package com.laien.cmsapp.oog104.entity;

import com.laien.common.domain.annotation.AppAudioSingleTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj_fitness_video
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjFitnessVideo对象", description="proj_fitness_video")
public class ProjFitnessVideo extends BaseModel  implements AppAudioCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作名称")
    @AppAudioSingleTranslateField(
            urlFieldName = "nameAudioUrl",
            durationFieldName = "nameAudioDuration")
    private String name;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "类型code")
    private String typeCodes;

    @ApiModelProperty(value = "难度code")
    private Integer difficultyCode;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "部位code")
    private Integer positionCode;

    @ApiModelProperty(value = "fit类型code")
    private String fitTypeCodes;

    @ApiModelProperty(value = "目标code")
    private String targetCodes;

    @ApiModelProperty(value = "器械code")
    private String equipmentCodes;

    @ApiModelProperty(value = "特殊限制code")
    private Integer specialLimitCode;

    @ApiModelProperty(value = "正机位mp4视频地址")
    private String frontVideoMp4Url;

    @ApiModelProperty(value = "正机位视频地址")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正机位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧机位mp4视频地址")
    private String sideVideoMp4Url;

    @ApiModelProperty(value = "侧机位视频地址")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧机位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "视频地址(正机位dynamic m3u8)")
    private String videoDynamicUrl;

    @ApiModelProperty(value = "名称音频地址")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "说明")
    @AppAudioSingleTranslateField(
            urlFieldName = "instructionsAudioUrl",
            durationFieldName = "instructionsAudioDuration")
    private String instructions;

    @ApiModelProperty(value = "名称音频地址")
    private String instructionsAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer instructionsAudioDuration;

    @ApiModelProperty(value = "如何做")
    @AppAudioSingleTranslateField(
            urlFieldName = "howToDoAudioUrl",
            durationFieldName = "howToDoAudioDuration")
    private String howToDo;

    @ApiModelProperty(value = "如何做音频地址")
    private String howToDoAudioUrl;

    @ApiModelProperty(value = "如何做音频时长")
    private Integer howToDoAudioDuration;

    @ApiModelProperty(value = "met")
    private Integer met;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;
}
