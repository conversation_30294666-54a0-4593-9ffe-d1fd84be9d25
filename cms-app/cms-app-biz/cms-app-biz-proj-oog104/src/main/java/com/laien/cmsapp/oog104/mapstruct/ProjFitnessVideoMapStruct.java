package com.laien.cmsapp.oog104.mapstruct;

import com.laien.cmsapp.oog104.entity.ProjFitnessVideo;
import com.laien.cmsapp.oog104.enums.M3u8TypeEnums;
import com.laien.cmsapp.oog104.response.ProjFitnessVideoDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * video 对象转换接口
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper(componentModel = "spring")
public abstract class ProjFitnessVideoMapStruct {

    @Mapping(target = "videoUrl", expression = "java(this.setVideoUrl(video.getVideoUrl(), video.getVideoDynamicUrl(), m3u8Type))")
    public abstract ProjFitnessVideoDetailVO toVideoDetailVO(ProjFitnessVideo video, @Context Integer m3u8Type);

    public abstract List<ProjFitnessVideoDetailVO> toVideoDetailVOList(List<ProjFitnessVideo> videoList, @Context Integer m3u8Type);

    public abstract ProjFitnessVideoDetailVO toVideoDetailVO(ProjFitnessVideoDetailVO videoDetailVO);


    /**
     * PRD m3u8Type参数说明：
     * 参数值=1：查询大切片m3u8；
     * 参数值=2：查询小切片（dynamic）m3u8；
     * 参数值=null或者不传：统一按传1的逻辑返回；
     * 如果没有大切片 m3u8，则返回 dynamic m3u8；
     *
     * @param videoUrl 大切片m3u8地址
     * @param videoDynamicUrl dynamic m3u8地址
     * @param m3u8Type m3u8类型，1大切片，2小切片
     * @return String
     */
    public String setVideoUrl(String videoUrl, String videoDynamicUrl, Integer m3u8Type) {
        // 参数不为空，且为2，则返回dynamic, 其他返回大切片, 大切片不存在，则返回dynamic
        if (m3u8Type != null && m3u8Type == M3u8TypeEnums.M3U8_DYNAMIC.getCode()) {
            return videoDynamicUrl;
        }

        if (StringUtils.isNotBlank(videoUrl)) {
            return videoUrl;
        }

        return videoDynamicUrl;
    }

}
