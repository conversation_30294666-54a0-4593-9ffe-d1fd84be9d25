package com.laien.cmsapp.oog104.request;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutPub;
import com.laien.cmsapp.requst.LangReq;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.oog104.enums.manual.ManualUserTypeEnums;
import com.laien.common.oog104.enums.manual.ManualWorkoutTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *  ProjFitnessManualWorkoutListReq
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Data
@ApiModel(value = "manual workout list 请求", description = "manual workout list 请求")
public class ProjFitnessManualWorkoutListReq extends LangReq {

    @ApiModelProperty(value = "Category: TOP_PICKS, POPULAR",
            allowableValues = "TOP_PICKS, POPULAR")
    private ManualCategoryEnums category;

    @ApiModelProperty(value = "Workout Type",
            allowableValues = "DANCING, HIIT, BOXING, CLASSIC_YOGA, PILATES, WALL_PILATES, POSTURE_CORRECTION, LOW_IMPACT, GENTLE_CARDIO, CHAIR_YOGA")
    private List<ManualWorkoutTypeEnums> workoutType;

    @ApiModelProperty(value = "Age Group",
            allowableValues = "UNDER_18, AGE_18_TO_24, AGE_25_TO_34, AGE_35_TO_44, AGE_45_TO_54, AGE_55_AND_ABOVE")
    private ManualAgeGroupEnums ageGroup;

    @ApiModelProperty(value = "User Type",
            allowableValues = "NEW, OLD")
    private ManualUserTypeEnums userType;

    /**
     * 用于内部排序的字段，不暴露给Swagger
     */
    @ApiModelProperty(hidden = true)
    private SFunction<ProjFitnessManualWorkoutPub, ?> orderByDescColumn;

}
