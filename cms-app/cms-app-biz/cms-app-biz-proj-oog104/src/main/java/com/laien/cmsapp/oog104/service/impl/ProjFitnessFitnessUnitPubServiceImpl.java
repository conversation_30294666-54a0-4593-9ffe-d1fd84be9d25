package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessUnitPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessUnitPubMapper;
import com.laien.cmsapp.oog104.service.IProjFitnessUnitPubService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/8 16:08
 */
@Service
public class ProjFitnessFitnessUnitPubServiceImpl extends ServiceImpl<ProjFitnessUnitPubMapper, ProjFitnessUnitPub> implements IProjFitnessUnitPubService {

    @Override
    public List<ProjFitnessUnitPub> listByVersionAndIds(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> ids) {

        LambdaQueryWrapper<ProjFitnessUnitPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjFitnessUnitPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjFitnessUnitPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.in(ProjFitnessUnitPub::getId, ids);
        return list(queryWrapper);
    }
}
