package com.laien.cmsapp.oog104.mapstruct;

import com.laien.cmsapp.oog104.entity.ProjFitnessManualWorkoutPub;
import com.laien.cmsapp.oog104.response.ProjFitnessManualWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessManualWorkoutListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <p>'
 * fitnessManualWorkout mapstruct
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Mapper(componentModel = "spring")
public interface ProjFitnessManualWorkoutMapStruct {

    //单独映射字段
    @Mapping(target = "intensity", expression = "java(workout.getIntensity() != null " +
            "? java.util.Collections.singletonList(workout.getIntensity()) " +
            ": java.util.Collections.emptyList())")
    ProjFitnessManualWorkoutDetailVO toManualWorkoutDetailVO(ProjFitnessManualWorkoutPub workout);

    List<ProjFitnessManualWorkoutListVO> toVOList(List<ProjFitnessManualWorkoutPub> list);
}
