package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.template.ExclusiveTypeEnums;
import com.laien.common.oog104.enums.template.TemplateTypeEnums;
import com.laien.common.oog104.enums.template.WorkoutDurationRangeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * proj_fitness_template
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="proj_fitness_template_pub",autoResultMap = true)
@Data
@Accessors(chain = true)
public class ProjFitnessTemplatePub extends BaseModel {

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("表标识")
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_TEMPLATE;

    @ApiModelProperty(value = "版本")
    private Integer version;

   /**
    * 模板名称
    */
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("针对人群类型")
    private ExclusiveTypeEnums exclusiveType;

   /**
    * 类型：1 Regular Workout；
    */
    @ApiModelProperty("类型：1 Regular Workout；")
    private TemplateTypeEnums templateType;

   /**
    * 时间范围：1 5-10min；2 10-15min；3 15-20min；4 20-30min；
    */
    @ApiModelProperty("时间范围：1 5-10min；2 10-15min；3 15-20min；4 20-30min；")
    private WorkoutDurationRangeEnums durationRange;

   /**
    * 生成多少天的天数
    */
    @ApiModelProperty("生成多少天的天数")
    private Integer days;

   /**
    * 难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；
    */
    @ApiModelProperty("难度等级：1 Newbie；2 Beginner；3 Intermediate；4 Advance；")
    private ManualDifficultyEnums level;

   /**
    * 特殊限制：1 Back；2 Knee；3 None；
    */
    @ApiModelProperty("特殊限制")
    @TableField(typeHandler = ExerciseVideoSpecialLimitEnums.TypeHandler.class)
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

   /**
    * 状态
    */
    @ApiModelProperty("状态")
    private Integer status;

   /**
    * 项目id
    */
    @ApiModelProperty("项目id")
    private Integer projId;

}
