package com.laien.cmsapp.oog104.mapstruct;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.laien.cmsapp.oog104.entity.ProjFitnessPlanPub;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessPlanListVO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * plan 对象转换接口
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper(componentModel = "spring")
public interface ProjFitnessPlanMapStruct {

    @Mapping(source = "stageCounts", target = "stageCounts", qualifiedByName = "stageCountsConvert")
    @Mapping(source = "expectedResults", target = "expectedResults", qualifiedByName = "convertToListWithNewline")
    ProjFitnessPlanListVO toPlanListVO(ProjFitnessPlanPub plan);

    List<ProjFitnessPlanListVO> toPlanListVO(List<ProjFitnessPlanPub> planList);

    @Mapping(source = "expectedResults", target = "expectedResults", qualifiedByName = "convertToListWithNewline")
    @Mapping(source = "tags", target = "tags", qualifiedByName = "convertToListWithNewline")
    ProjFitnessPlanDetailVO toPlanDetailVO(ProjFitnessPlanPub plan);


    @Named("stageCountsConvert")
    default List<Integer> extraTagCodesConvert(String stageCounts) {
        return Arrays.stream(stageCounts.split(StringPool.COMMA))
                .filter(StringUtils::isNotBlank)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

    @Named("convertToListWithNewline")
    default List<String> convertToListWithNewline(String str) {
        return Arrays.stream(str.split(StringPool.NEWLINE))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

}
