package com.laien.cmsapp.oog104.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutPub;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * proj_fitness_workout_pub Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface ProjFitnessWorkoutPubMapper extends BaseMapper<ProjFitnessWorkoutPub> {

    /**
     * 根据版本和idList查询详情list
     *
     * @param version version
     * @param idCollection idCollection
     * @return list
     */
    List<ProjFitnessWorkoutPub> selectListDetailByIds(@Param("version") Integer version, @Param("idCollection") Collection<Integer> idCollection);

}
