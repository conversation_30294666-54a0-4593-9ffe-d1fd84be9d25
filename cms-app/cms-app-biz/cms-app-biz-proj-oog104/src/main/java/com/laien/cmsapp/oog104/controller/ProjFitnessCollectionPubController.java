package com.laien.cmsapp.oog104.controller;


import com.laien.cmsapp.requst.LangReq;
import com.laien.cmsapp.oog104.response.ProjFitnessCollectionImageVO;
import com.laien.cmsapp.oog104.service.IProjFitnessCollectionPubService;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * proj_fitness_collection_pub 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Api(tags = "app端：Fitness Collection")
@RestController
@RequestMapping("/oog104/fitnessCollection")
public class ProjFitnessCollectionPubController extends ResponseController {

    @Resource
    private IProjFitnessCollectionPubService projFitnessCollectionPubService;

    @ApiOperation(value = "Fitness Collection list", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<ProjFitnessCollectionImageVO> list(LangReq langReq) {
        return succ(projFitnessCollectionPubService.selectCollectionImage());
    }

}
