package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2025/1/7 11:25
 */
@Data
@ApiModel(value="dish step tip对象", description="dish step tip")
public class ProjFitnessDishStepTipVO  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "proj_dish表数据id")
    @JsonIgnore
    private Integer projFitnessDishId;

    @JsonIgnore
    @ApiModelProperty(value = "proj_dish_step表数据id")
    private Integer projFitnessDishStepId;

    @ApiModelProperty(value = "图片路径")
    @AbsoluteR2Url
    private String imgUrl;

    @ApiModelProperty(value = "介绍")
    @AppTextTranslateField
    private String intro;

}
