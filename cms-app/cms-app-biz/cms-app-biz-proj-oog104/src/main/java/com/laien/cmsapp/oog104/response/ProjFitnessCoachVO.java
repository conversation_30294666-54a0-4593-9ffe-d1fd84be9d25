package com.laien.cmsapp.oog104.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ProjFitnessCoachVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFitnessCoachVO", description="ProjFitnessCoachVO")
public class ProjFitnessCoachVO extends BaseTableCodeVO  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "teacher name")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "photoImgUrl")
    @AbsoluteR2Url
    private String photoImgUrl;

    @ApiModelProperty(value = "introduction")
    @AppTextTranslateField
    private String introduction;
}
