package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessCoachingCoursesPub;
import com.laien.cmsapp.oog104.request.ProjFitnessCoachingCoursesListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesVO;

import java.util.List;

/**
 * <p>
 * Fitness CoachingCourses Pub 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
public interface IProjFitnessCoachingCoursesPubService extends IService<ProjFitnessCoachingCoursesPub> {

    List<ProjFitnessCoachingCoursesVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO, ProjFitnessCoachingCoursesListReq req);

    ProjFitnessCoachingCoursesDetailVO detail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type);
}
