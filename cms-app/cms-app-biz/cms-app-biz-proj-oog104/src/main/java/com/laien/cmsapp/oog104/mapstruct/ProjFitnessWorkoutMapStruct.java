package com.laien.cmsapp.oog104.mapstruct;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutI18nPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutPub;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutAudioVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutListVO;
import com.laien.cmsapp.util.TimeConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * workout 对象转换接口
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper(componentModel = "spring", imports = TimeConvertUtil.class)
public abstract class ProjFitnessWorkoutMapStruct {

    @Resource
    protected ProjFitnessVideoMapStruct projFitnessVideoMapStruct;

    @Mapping(target = "unitList", ignore = true)
    @Mapping(target = "audioJsonList", ignore = true)
    @Mapping(target = "videoUrl", expression = "java(projFitnessVideoMapStruct.setVideoUrl(workout.getVideoUrl(), workout.getVideoDynamicUrl(), m3u8Type))")
    @Mapping(source = "extraTagCodes", target = "extraTagCodes", qualifiedByName = "extraTagCodesConvert")
    @Mapping(target = "duration", expression = "java(TimeConvertUtil.millisToMinutes(workout.getDuration()))")
    public abstract ProjFitnessWorkoutDetailVO toWorkoutDetailVO(ProjFitnessWorkoutPub workout, @Context Integer m3u8Type);

    public abstract List<ProjFitnessWorkoutDetailVO> toWorkoutDetailListVO(List<ProjFitnessWorkoutPub> workoutList, @Context Integer m3u8Type);

    public abstract ProjFitnessWorkoutAudioVO toWorkoutI18nVO(ProjFitnessWorkoutI18nPub workoutI18n);

    public abstract List<ProjFitnessWorkoutAudioVO> toWorkoutI18nListVO(List<ProjFitnessWorkoutI18nPub> workoutI18nList);

    @Mapping(target = "duration", expression = "java(TimeConvertUtil.millisToMinutes(workout.getDuration()))")
    public abstract ProjFitnessWorkoutListVO toWorkoutListVO(ProjFitnessWorkoutPub workout);

    public abstract List<ProjFitnessWorkoutListVO> toWorkoutListVO(List<ProjFitnessWorkoutPub> workoutList);

    @Named("extraTagCodesConvert")
    public List<Integer> extraTagCodesConvert(String extraTagCodes) {
        return Arrays.stream(extraTagCodes.split(StringPool.COMMA))
                .filter(StringUtils::isNotBlank)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

}
