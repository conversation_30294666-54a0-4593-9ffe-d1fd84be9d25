package com.laien.cmsapp.oog104.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessCoachPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessCoachingCoursesPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessCoachingCoursesRelationPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessVideoCoursePub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessCoachingCoursesPubMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessCoachingCourseMapStruct;
import com.laien.cmsapp.oog104.request.ProjFitnessCoachingCoursesListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessCoachingCoursesVO;
import com.laien.cmsapp.oog104.response.ProjFitnessVideoCourseDetailVO;
import com.laien.cmsapp.oog104.service.IProjFitnessCoachPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessCoachingCoursesPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessCoachingCoursesRelationPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessVideoCoursePubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.response.IdAndCountsAndMaxMinRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Fitness CoachingCoursesPub 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessCoachingCoursesPubServiceImpl extends ServiceImpl<ProjFitnessCoachingCoursesPubMapper, ProjFitnessCoachingCoursesPub>
        implements IProjFitnessCoachingCoursesPubService {

    private final ProjFitnessCoachingCourseMapStruct mapStruct;
    private final IProjFitnessCoachingCoursesRelationPubService relationPubService;
    private final IProjFitnessVideoCoursePubService videoCoursePubService;
    private final IProjFitnessCoachPubService coachPubService;
    private final IProjLmsI18nService projLmsI18nService;


    @Override
    public List<ProjFitnessCoachingCoursesVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO, ProjFitnessCoachingCoursesListReq req) {
        LambdaQueryWrapper<ProjFitnessCoachingCoursesPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessCoachingCoursesPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjFitnessCoachingCoursesPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjFitnessCoachingCoursesPub::getStatus, GlobalConstant.STATUS_ENABLE);

        List<ProjFitnessCoachingCoursesPub> coursesList = this.list(wrapper);
        if (coursesList.isEmpty()) {
            return new ArrayList<>();
        }
        this.handleI18n(coursesList);
        // 查询包含的class video 数量, 数量为0不返回
        List<IdAndCountsAndMaxMinRes> coachingCoursesCountList = this.baseMapper.selectCoachingCoursesCounts(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion());
        Map<Integer,IdAndCountsAndMaxMinRes> countsMap = coachingCoursesCountList.stream().collect(
                Collectors.toMap(IdAndCountsAndMaxMinRes::getId, idAndCountsAndMaxMinRes -> idAndCountsAndMaxMinRes));

        List<ProjFitnessCoachingCoursesVO> coachingCoursesResultList = new ArrayList<>(coursesList.size());
        List<ProjFitnessCoachingCoursesVO> matchedNewTimeAndTypeList = new ArrayList<>(coursesList.size());
        List<ProjFitnessCoachingCoursesVO> matchedNewTimeList = new ArrayList<>(coursesList.size());
        List<ProjFitnessCoachingCoursesVO> matchedTypeList = new ArrayList<>(coursesList.size());
        List<ProjFitnessCoachingCoursesVO> matchNoneList = new ArrayList<>(coursesList.size());
        LocalDateTime now = LocalDateTime.now();

        for (ProjFitnessCoachingCoursesPub coachingCourses : coursesList) {
            ProjFitnessCoachingCoursesVO listVO = mapStruct.toListVO(coachingCourses);

            IdAndCountsAndMaxMinRes countsRes = countsMap.get(coachingCourses.getId());
            if (countsRes != null) {
                listVO.setCourseCount(countsRes.getCounts());
                listVO.setMaxDuration(countsRes.getMaxValue().intValue());
                listVO.setMinDuration(countsRes.getMinValue().intValue());
            } else {
                listVO.setCourseCount(0);
                listVO.setMaxDuration(0);
                listVO.setMinDuration(0);
            }

            LocalDateTime newEndTime = listVO.getNewEndTime();
            LocalDateTime newStartTime = listVO.getNewStartTime();
            if (Objects.isNull(newEndTime) || Objects.isNull(newStartTime)) {
                if (CollUtil.containsAny(req.getTypes(), coachingCourses.getTypes())) {
                    matchedTypeList.add(listVO);
                } else {
                    matchNoneList.add(listVO);
                }
            } else {
                if (now.isAfter(newStartTime) && now.isBefore(newEndTime)) {
                    if (CollUtil.containsAny(req.getTypes(), coachingCourses.getTypes())) {
                        matchedNewTimeAndTypeList.add(listVO);
                    } else {
                        matchedNewTimeList.add(listVO);
                    }
                } else {
                    if (CollUtil.containsAny(req.getTypes(), coachingCourses.getTypes())) {
                        matchedTypeList.add(listVO);
                    } else {
                        matchNoneList.add(listVO);
                    }
                }
            }
        }
        matchedNewTimeAndTypeList.sort(Comparator.comparing(ProjFitnessCoachingCoursesVO::getNewStartTime).reversed());
        matchedNewTimeList.sort(Comparator.comparing(ProjFitnessCoachingCoursesVO::getNewStartTime).reversed());
        matchedTypeList.sort(Comparator.comparing(
                        ProjFitnessCoachingCoursesVO::getNewStartTime,
                        Comparator.nullsLast(Comparator.reverseOrder())));
        matchNoneList.sort(Comparator.comparing(
                        ProjFitnessCoachingCoursesVO::getNewStartTime,
                        Comparator.nullsLast(Comparator.reverseOrder())));
        coachingCoursesResultList.addAll(matchedNewTimeAndTypeList);
        coachingCoursesResultList.addAll(matchedNewTimeList);
        coachingCoursesResultList.addAll(matchedTypeList);
        coachingCoursesResultList.addAll(matchNoneList);
        return coachingCoursesResultList;
    }

    @Override
    public ProjFitnessCoachingCoursesDetailVO detail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type) {
        LambdaQueryWrapper<ProjFitnessCoachingCoursesPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessCoachingCoursesPub::getId, id)
                .eq(ProjFitnessCoachingCoursesPub::getVersion, versionInfoBO.getCurrentVersion());
        ProjFitnessCoachingCoursesPub coursesPub = this.getOne(wrapper);
        this.handleI18n(Collections.singletonList(coursesPub));
        ProjFitnessCoachingCoursesDetailVO detailVO = mapStruct.toDetailVO(coursesPub);
        if (detailVO != null) {
            //query coach
            if (ObjUtil.isNotNull(coursesPub.getProjFitnessCoachId())){
                ProjFitnessCoachPub coachPub = queryFitnessCoach(versionInfoBO, coursesPub);
                detailVO.setCoach(mapStruct.toCoachVO(coachPub));
            }
            //query relation
            List<ProjFitnessCoachingCoursesRelationPub> relationList = queryRelation(id, versionInfoBO);
            if (CollUtil.isEmpty(relationList)) return detailVO;
            //query videoCourse
            List<Integer> videoCourseIdList = relationList.stream().map(ProjFitnessCoachingCoursesRelationPub::getProjFitnessVideoCourseId).collect(Collectors.toList());
            List<ProjFitnessVideoCoursePub> videoCourseList = queryVideoCourse(versionInfoBO, videoCourseIdList);
            if (CollUtil.isEmpty(videoCourseList)) return detailVO;
            List<ProjFitnessVideoCourseDetailVO> courseDetailVO = handleM3u8TypeAndSort(m3u8Type, videoCourseList, videoCourseIdList);
            detailVO.setVideoCourses(courseDetailVO);
            handleRelatedI18n(detailVO);
        }
        return detailVO;
    }

    private List<ProjFitnessVideoCourseDetailVO> handleM3u8TypeAndSort(Integer m3u8Type,
                                                                       List<ProjFitnessVideoCoursePub> videoCourseList,
                                                                       List<Integer> videoCourseIdList) {
        //按videoCourseIds的顺序排序
        return videoCourseList.stream().map(p -> {
                    ProjFitnessVideoCourseDetailVO vo = mapStruct.toVideoCourseDetailVO(p);
                    if (Objects.equals(GlobalConstant.ONE, m3u8Type)) {
                        vo.setVideoUrl(p.getVideo2532Url());
                    }
                    if (Objects.equals(GlobalConstant.TWO, m3u8Type)) {
                        vo.setVideoUrl(p.getVideoUrl());
                    }
                    return vo;
                }).sorted(Comparator.comparingInt(videoCourse -> videoCourseIdList.indexOf(videoCourse.getId())))
                .collect(Collectors.toList());
    }

    private ProjFitnessCoachPub queryFitnessCoach(ProjPublishCurrentVersionInfoBO versionInfoBO, ProjFitnessCoachingCoursesPub coursesPub) {
        LambdaQueryWrapper<ProjFitnessCoachPub> coachWrapper = new LambdaQueryWrapper<>();
        coachWrapper.eq(ProjFitnessCoachPub::getId, coursesPub.getProjFitnessCoachId())
              .eq(ProjFitnessCoachPub::getVersion, versionInfoBO.getCurrentVersion());
        return coachPubService.getOne(coachWrapper);
    }

    private List<ProjFitnessVideoCoursePub> queryVideoCourse(ProjPublishCurrentVersionInfoBO versionInfoBO, List<Integer> videoCourseIdList) {
        LambdaQueryWrapper<ProjFitnessVideoCoursePub> videoCourseWrapper = new LambdaQueryWrapper<>();
        videoCourseWrapper.in(ProjFitnessVideoCoursePub::getId, videoCourseIdList)
               .eq(ProjFitnessVideoCoursePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjFitnessVideoCoursePub::getStatus, GlobalConstant.ONE);
        return videoCoursePubService.list(videoCourseWrapper);
    }

    private List<ProjFitnessCoachingCoursesRelationPub> queryRelation(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFitnessCoachingCoursesRelationPub> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.eq(ProjFitnessCoachingCoursesRelationPub::getProjFitnessCoachingCoursesId, id)
                .eq(ProjFitnessCoachingCoursesRelationPub::getVersion, versionInfoBO.getCurrentVersion());
        return relationPubService.list(relationWrapper);
    }

    private void handleI18n(List<ProjFitnessCoachingCoursesPub> entities) {
        projLmsI18nService.handleTextI18n(entities, ProjCodeEnums.OOG104);
    }


    private void handleRelatedI18n(ProjFitnessCoachingCoursesDetailVO detailVO) {
        List<AppTextCoreI18nModel> modelList = new ArrayList<>();
        modelList.add(detailVO.getCoach());
        modelList.addAll(detailVO.getVideoCourses());
        projLmsI18nService.handleTextI18n(modelList, ProjCodeEnums.OOG104);
    }

}
