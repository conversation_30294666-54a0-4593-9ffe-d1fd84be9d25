package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessFastingArticlePub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessFastingArticlePubMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessFastingArticleMapStruct;
import com.laien.cmsapp.oog104.request.ProjFitnessFastingArticleListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessFastingArticlePubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * FitnessFasting article 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjFitnessFastingArticlePubServiceImpl extends
        ServiceImpl<ProjFitnessFastingArticlePubMapper, ProjFitnessFastingArticlePub>
        implements IProjFitnessFastingArticlePubService {

    private final ProjFitnessFastingArticleMapStruct mapStruct;
    private final IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessFastingArticleListVO> list(ProjFitnessFastingArticleListReq req, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFitnessFastingArticlePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessFastingArticlePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjFitnessFastingArticlePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjFitnessFastingArticlePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(req.getType()!=null,ProjFitnessFastingArticlePub::getType, req.getType())
                .orderByAsc(ProjFitnessFastingArticlePub::getSorted)
                .orderByDesc(ProjFitnessFastingArticlePub::getId);
        List<ProjFitnessFastingArticlePub> fastingArticleList = this.list(wrapper);
        if (CollUtil.isEmpty(fastingArticleList)) {
            return new ArrayList<>();
        }
        projLmsI18nService.handleTextI18n(fastingArticleList, ProjCodeEnums.OOG104);
        return mapStruct.toVOList(fastingArticleList);
    }

    @Override
    public ProjFitnessFastingArticleDetailVO detail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFitnessFastingArticlePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFitnessFastingArticlePub::getVersion, versionInfoBO.getCurrentVersion());
        wrapper.eq(ProjFitnessFastingArticlePub::getId, id);
        ProjFitnessFastingArticlePub fastingArticle = this.getOne(wrapper);
        if (Objects.isNull(fastingArticle)) {
            return null;
        }
        projLmsI18nService.handleTextI18n(Collections.singletonList(fastingArticle), ProjCodeEnums.OOG104);
        return mapStruct.toDetailVO(fastingArticle);
    }

}
