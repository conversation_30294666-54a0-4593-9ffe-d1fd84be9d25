package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.common.oog104.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Author:  hhl
 * Date:  2024/12/31 14:21
 */
@Accessors(chain = true)
@Data
public class ProjFitnessMealPlanPub extends BaseModel  implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_FITNESS_MEAL_PLAN;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "meal plan中含有多少天的计划")
    private Integer days;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "排序")
    private Integer sorted;

    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "用于APP端，显示一个固定的标签")
    private Boolean replacementTag;

    @ApiModelProperty(value = "标签值，以英文逗号做分隔，如A,B,C")
    @AppTextTranslateField
    private String keywords;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "描述")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
