package com.laien.cmsapp.oog104.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  ProjFitnessCoachingCoursesRelationPub
 * <p>
 *
 * <AUTHOR>
 * @since 2025/04/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(autoResultMap = true)
@ApiModel(value = "ProjFitnessCoachingCoursesRelationPub对象", description = "ProjFitnessCoachingCoursesRelationPub对象")
public class ProjFitnessCoachingCoursesRelationPub extends BaseModel {

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "关联VideoCourseId")
    private Integer projFitnessVideoCourseId;

    @ApiModelProperty(value = "关联CoachingCoursesId")
    private Integer projFitnessCoachingCoursesId;
}
