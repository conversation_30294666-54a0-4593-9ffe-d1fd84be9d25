package com.laien.cmsapp.oog104.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.constant.ResImageConstant;
import com.laien.cmsapp.entity.ResImage;
import com.laien.cmsapp.oog104.entity.ProjFitnessCollectionProjFitnessWorkoutPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessCollectionPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessCollectionPubMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessCollectionMapStruct;
import com.laien.cmsapp.oog104.response.ProjFitnessCollectionImageVO;
import com.laien.cmsapp.oog104.response.ProjFitnessCollectionListVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessCollectionProjFitnessWorkoutPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessCollectionPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessWorkoutPubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.service.IResImageService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_collection_pub 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class ProjFitnessCollectionPubServiceImpl extends ServiceImpl<ProjFitnessCollectionPubMapper, ProjFitnessCollectionPub> implements IProjFitnessCollectionPubService {

    @Resource
    private IResImageService resImageService;
    @Resource
    private IProjFitnessCollectionProjFitnessWorkoutPubService projFitnessCollectionProjFitnessWorkoutPubService;
    @Resource
    private IProjFitnessWorkoutPubService projFitnessWorkoutPubService;
    @Resource
    private ProjFitnessCollectionMapStruct projFitnessCollectionMapStruct;
    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public ProjFitnessCollectionImageVO selectCollectionImage() {
        // 查询image 相关
        ProjFitnessCollectionImageVO collectionImageVO = this.selectCollectionImageVO();
        // 查询collection list
        List<ProjFitnessCollectionListVO> collectionList = this.selectCollectionWithWorkoutList();
        collectionImageVO.setCollectionList(collectionList);
        if (!collectionList.isEmpty()) {
            ProjFitnessCollectionListVO collectionListVO = collectionList.get(GlobalConstant.ZERO);
            collectionImageVO.setNewStartTime(collectionListVO.getNewStartTime());
            collectionImageVO.setNewEndTime(collectionListVO.getNewEndTime());
        }
        return collectionImageVO;
    }


    /**
     * 查询image 相关
     *
     * @return ProjFitnessCollectionImageVO
     */
    private ProjFitnessCollectionImageVO selectCollectionImageVO() {
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjFitnessCollectionImageVO collectionImageVO = new ProjFitnessCollectionImageVO();
        List<ResImage> images = resImageService.findForI18n(version.getAppCode(), ResImageConstant.FUNCTION_NEW_YEAR, ResImageConstant.POINT_NEW_YEAR, GlobalConstant.ONE);
        if (!images.isEmpty()) {
            ResImage image = images.get(GlobalConstant.ZERO);
            collectionImageVO.setName(image.getName());
            collectionImageVO.setCoverImage(image.getCoverImage());
            collectionImageVO.setCoverSpareImage(image.getCoverSpareImage());
            collectionImageVO.setDetailImage(image.getDetailImage());
            collectionImageVO.setDescription(image.getDescription());
        }

        return collectionImageVO;
    }

    private List<ProjFitnessCollectionListVO> selectCollectionWithWorkoutList() {
        List<ProjFitnessCollectionPub> collectionPubList = this.selectCollectionList();
        if (collectionPubList.isEmpty()) {
            return Collections.emptyList();
        }

        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<Integer> collectionIds = collectionPubList.stream().map(ProjFitnessCollectionPub::getId).collect(Collectors.toList());
        Set<Integer> workoutIdSet = new HashSet<>();
        Map<Integer, List<ProjFitnessCollectionProjFitnessWorkoutPub>> workoutUnitMap = projFitnessCollectionProjFitnessWorkoutPubService.list(
                        new LambdaQueryWrapper<ProjFitnessCollectionProjFitnessWorkoutPub>()
                                .in(ProjFitnessCollectionProjFitnessWorkoutPub::getProjFitnessCollectionId, collectionIds)
                                .eq(ProjFitnessCollectionProjFitnessWorkoutPub::getVersion, version.getCurrentVersion())
                                .orderByAsc(ProjFitnessCollectionProjFitnessWorkoutPub::getId)
                ).stream()
                .peek(item -> workoutIdSet.add(item.getProjFitnessWorkoutId()))
                .collect(Collectors.groupingBy(ProjFitnessCollectionProjFitnessWorkoutPub::getProjFitnessCollectionId));

        // 获取collection所有workout
        List<ProjFitnessWorkoutListVO> allWorkoutList = projFitnessWorkoutPubService.selectListByIds(workoutIdSet);
        Map<Integer, ProjFitnessWorkoutListVO> workoutListMap = allWorkoutList.stream()
                .collect(Collectors.toMap(ProjFitnessWorkoutListVO::getId, item -> item));

        List<ProjFitnessCollectionListVO> collectionVOList = projFitnessCollectionMapStruct.toCollectionVOList(collectionPubList);
        for (ProjFitnessCollectionListVO projFitnessCollectionListVO : collectionVOList) {
            Integer id = projFitnessCollectionListVO.getId();
            List<ProjFitnessWorkoutListVO> workoutItemList = new ArrayList<>();
            List<ProjFitnessCollectionProjFitnessWorkoutPub> fitnessCollectionProjFitnessWorkoutPubList = workoutUnitMap.get(id);
            if (fitnessCollectionProjFitnessWorkoutPubList != null) {
                for (ProjFitnessCollectionProjFitnessWorkoutPub fitnessCollectionProjFitnessWorkoutPub : fitnessCollectionProjFitnessWorkoutPubList) {
                    ProjFitnessWorkoutListVO workoutListVO = workoutListMap.get(fitnessCollectionProjFitnessWorkoutPub.getProjFitnessWorkoutId());
                    if (workoutListVO != null) {
                        workoutItemList.add(workoutListVO);
                    }
                }
            }

            projFitnessCollectionListVO.setWorkoutList(workoutItemList);
        }

        return collectionVOList;
    }

    /**
     * 查询collection list
     *
     * @return list
     */
    private List<ProjFitnessCollectionPub> selectCollectionList() {
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjFitnessCollectionPub> collectionPubList = this.list(new LambdaQueryWrapper<ProjFitnessCollectionPub>()
                .eq(ProjFitnessCollectionPub::getProjId, version.getProjId())
                .eq(ProjFitnessCollectionPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjFitnessCollectionPub::getVersion, version.getCurrentVersion())
                .orderByAsc(ProjFitnessCollectionPub::getSortNo)
                .orderByDesc(ProjFitnessCollectionPub::getId)
        );
        projLmsI18nService.handleTextI18n(collectionPubList, ProjCodeEnums.OOG104);
        return collectionPubList;
    }

}
