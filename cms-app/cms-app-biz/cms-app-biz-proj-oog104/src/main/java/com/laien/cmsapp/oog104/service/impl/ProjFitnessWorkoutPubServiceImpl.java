package com.laien.cmsapp.oog104.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutI18nPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutProjFitnessVideoPub;
import com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutPub;
import com.laien.cmsapp.oog104.mapper.ProjFitnessWorkoutPubMapper;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessVideoMapStruct;
import com.laien.cmsapp.oog104.mapstruct.ProjFitnessWorkoutMapStruct;
import com.laien.cmsapp.oog104.response.ProjFitnessVideoDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutDetailUnitVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessWorkoutListVO;
import com.laien.cmsapp.oog104.service.IProjFitnessVideoService;
import com.laien.cmsapp.oog104.service.IProjFitnessWorkoutI18nPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessWorkoutProjFitnessVideoPubService;
import com.laien.cmsapp.oog104.service.IProjFitnessWorkoutPubService;
import com.laien.cmsapp.service.IProjLmsI18nService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.RequestContextUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj_fitness_workout_pub 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
public class ProjFitnessWorkoutPubServiceImpl extends ServiceImpl<ProjFitnessWorkoutPubMapper, ProjFitnessWorkoutPub> implements IProjFitnessWorkoutPubService {

    @Resource
    private IProjFitnessWorkoutI18nPubService projFitnessWorkoutI18nPubService;
    @Resource
    private IProjFitnessWorkoutProjFitnessVideoPubService projFitnessWorkoutProjFitnessVideoPubService;
    @Resource
    private IProjFitnessVideoService projFitnessVideoService;
    @Resource
    private ProjFitnessWorkoutMapStruct projFitnessWorkoutMapStruct;
    @Resource
    private ProjFitnessVideoMapStruct projFitnessVideoMapStruct;
    @Resource
    private IProjLmsI18nService projLmsI18nService;

    @Override
    public List<ProjFitnessWorkoutDetailVO> selectDetailByIds(Collection<Integer> idCollection, Integer m3u8Type) {
        if (CollectionUtil.isEmpty(idCollection)) {
            return Collections.emptyList();
        }
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();

        // 查询workout详情列表基本信息
        List<ProjFitnessWorkoutPub> workoutList = this.baseMapper.selectListDetailByIds(version.getCurrentVersion(), idCollection);
        projLmsI18nService.handleTextI18n(workoutList, ProjCodeEnums.OOG104);
        if (CollectionUtil.isEmpty(workoutList)) {
            return Collections.emptyList();
        }

        // 转换为workoutVO
        List<ProjFitnessWorkoutDetailVO> workoutVOList = projFitnessWorkoutMapStruct.toWorkoutDetailListVO(workoutList, m3u8Type);

        // 查询所有workout的音频多语言并按照workoutId分组
        Map<Integer, List<ProjFitnessWorkoutI18nPub>> audioI18nMap = projFitnessWorkoutI18nPubService.list(new LambdaQueryWrapper<ProjFitnessWorkoutI18nPub>()
                .in(ProjFitnessWorkoutI18nPub::getProjFitnessWorkoutId, idCollection)
                .eq(ProjFitnessWorkoutI18nPub::getVersion, version.getCurrentVersion())
        ).stream().collect(Collectors.groupingBy(ProjFitnessWorkoutI18nPub::getProjFitnessWorkoutId));

        // 查询所有workout unit并按照workoutId分组
        Set<Integer> videoIdSet = new HashSet<>();
        Map<Integer, List<ProjFitnessWorkoutProjFitnessVideoPub>> workoutUnitMap = projFitnessWorkoutProjFitnessVideoPubService.list(
                new LambdaQueryWrapper<ProjFitnessWorkoutProjFitnessVideoPub>()
                        .in(ProjFitnessWorkoutProjFitnessVideoPub::getProjFitnessWorkoutId, idCollection)
                        .eq(ProjFitnessWorkoutProjFitnessVideoPub::getVersion, version.getCurrentVersion())
        ).stream()
                .peek(item -> {
                    videoIdSet.add(item.getProjFitnessVideoId());
                })
                .collect(Collectors.groupingBy(ProjFitnessWorkoutProjFitnessVideoPub::getProjFitnessWorkoutId));

        // 查询video 详情列表并转换为map
        List<ProjFitnessVideoDetailVO> videoList = projFitnessVideoService.selectDetailByIds(videoIdSet, m3u8Type);
        Map<Integer, ProjFitnessVideoDetailVO> videoListMap = videoList.stream()
                .collect(Collectors.toMap(ProjFitnessVideoDetailVO::getId, item -> item));

        String lang = RequestContextUtils.getLanguage();

        // 数据为空或默认语种不需要处理
        if (StringUtils.isNotBlank(lang) && !Objects.equals(lang, GlobalConstant.DEFAULT_LANGUAGE)
                && !workoutUnitMap.isEmpty()) {
            projLmsI18nService.handleTextI18n(workoutUnitMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()),
                    ProjCodeEnums.OOG104,lang);
        }

        workoutVOList.forEach(item -> {
            Integer workoutId = item.getId();

            // 音频多语种全部返回
            List<ProjFitnessWorkoutI18nPub> audioI18nList = audioI18nMap.get(workoutId);
            item.setAudioJsonList(projFitnessWorkoutMapStruct.toWorkoutI18nListVO(audioI18nList));

            // 按照unitName 分成多组, 使用LinkedHashMap 保证分组后顺序不变
            List<ProjFitnessWorkoutProjFitnessVideoPub> workoutVideoList = workoutUnitMap.get(workoutId);
            LinkedHashMap<String, List<ProjFitnessWorkoutProjFitnessVideoPub>> videoListGroup = workoutVideoList.stream()
                    .collect(Collectors.groupingBy(
                            ProjFitnessWorkoutProjFitnessVideoPub::getUnitName,
                            LinkedHashMap::new,
                            Collectors.toList()));
            List<ProjFitnessWorkoutDetailUnitVO> unitList = new ArrayList<>();
            for (Map.Entry<String, List<ProjFitnessWorkoutProjFitnessVideoPub>> entry : videoListGroup.entrySet()) {
                List<ProjFitnessWorkoutProjFitnessVideoPub> videoUnitList = entry.getValue();
                // 设置unit的videoList
                List<ProjFitnessVideoDetailVO> unitVideoList = new ArrayList<>();
                for (ProjFitnessWorkoutProjFitnessVideoPub workoutVideo : videoUnitList) {
                    ProjFitnessVideoDetailVO videoDetailVO = videoListMap.get(workoutVideo.getProjFitnessVideoId());
                    if (videoDetailVO != null) {
                        // 使用新对象, video 在不同的unitName里, 时长不一致
                        ProjFitnessVideoDetailVO newVideoDetailVO = projFitnessVideoMapStruct.toVideoDetailVO(videoDetailVO);
                        newVideoDetailVO.setPreviewDuration(workoutVideo.getPreviewDuration());
                        newVideoDetailVO.setMainDuration(workoutVideo.getMainDuration());
                        unitVideoList.add(newVideoDetailVO);
                    }
                }

                String unitName = entry.getKey();
                unitList.add(new ProjFitnessWorkoutDetailUnitVO()
                        .setUnitName(unitName)
                        .setCount(unitVideoList.size())
                        .setVideoList(unitVideoList)
                );
            }
            item.setUnitList(unitList);
        });

        return workoutVOList;
    }


    @Override
    public List<ProjFitnessWorkoutListVO> selectListByIds(Collection<Integer> idCollection) {
        if (CollectionUtil.isEmpty(idCollection)) {
            return Collections.emptyList();
        }
        ProjPublishCurrentVersionInfoBO version = RequestContextAppUtils.getPublishCurrentVersionInfo();

        // 查询workout详情列表基本信息
        List<ProjFitnessWorkoutPub> workoutList = this.baseMapper.selectListDetailByIds(version.getCurrentVersion(), idCollection);
        projLmsI18nService.handleTextI18n(workoutList, ProjCodeEnums.OOG104);
        return projFitnessWorkoutMapStruct.toWorkoutListVO(workoutList);
    }
}
