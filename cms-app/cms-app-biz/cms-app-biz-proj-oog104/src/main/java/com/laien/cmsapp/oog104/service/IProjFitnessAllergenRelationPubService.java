package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessAllergenRelationPub;
import com.laien.common.oog104.enums.dish.FitnessAllergenRelationBusinessEnums;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/8 17:28
 */
public interface IProjFitnessAllergenRelationPubService extends IService<ProjFitnessAllergenRelationPub> {

    List<ProjFitnessAllergenRelationPub> listByVersionAndDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, FitnessAllergenRelationBusinessEnums businessEnum);

}
