package com.laien.cmsapp.oog104.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.oog104.enums.manual.ExerciseVideoSpecialLimitEnums;
import com.laien.common.oog104.enums.manual.ManualAgeGroupEnums;
import com.laien.common.oog104.enums.manual.ManualCategoryEnums;
import com.laien.common.oog104.enums.manual.ManualDifficultyEnums;
import com.laien.common.oog104.enums.manual.ManualEquipmentEnums;
import com.laien.common.oog104.enums.manual.ManualIntensityEnums;
import com.laien.common.oog104.enums.manual.ManualTargetEnums;
import com.laien.common.oog104.enums.manual.ManualWorkoutTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  manual workout List VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjFitnessManualWorkoutListVO extends BaseTableCodeVO {

    @ApiModelProperty(value = "workout id")
    private Integer id;

    @ApiModelProperty(value = "workout Name")
    private String name;

    @ApiModelProperty(value = "Event Name")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "Cover Image, supports png/webp formats")
    private String coverImage;

    @ApiModelProperty(value = "Workout Type")
    private ManualWorkoutTypeEnums workoutType;

    @ApiModelProperty(value = "Age Groups")
    private List<ManualAgeGroupEnums> ageGroup;

    @ApiModelProperty(value = "Categories")
    private List<ManualCategoryEnums> category;

    @ApiModelProperty(value = "target code")
    private List<ManualTargetEnums> target;

    @ApiModelProperty(value = "difficulty code")
    private ManualDifficultyEnums difficulty;

    @ApiModelProperty(value = "equipment code")
    private List<ManualEquipmentEnums> equipment;

    @ApiModelProperty(value = "特殊限制code (多选, 逗号分隔)")
    private List<ExerciseVideoSpecialLimitEnums> specialLimit;

    @ApiModelProperty(value = "intensity")
    private ManualIntensityEnums intensity;

    @ApiModelProperty(value = "Duration in milliseconds")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;
    @AbsoluteR2Url
    @ApiModelProperty(value = "video的m3u8地址")
    private String videoUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period Start")
    private LocalDateTime newTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "Time Period End")
    private LocalDateTime newTimeEnd;

    @ApiModelProperty(value = "Subscription 0-No, 1-Yes")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
