package com.laien.cmsapp.oog104.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.entity.ProjFitnessFastingArticlePub;
import com.laien.cmsapp.oog104.request.ProjFitnessFastingArticleListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleDetailVO;
import com.laien.cmsapp.oog104.response.ProjFitnessFastingArticleListVO;

import java.util.List;

/**
 * <p>
 * FitnessFasting article 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
public interface IProjFitnessFastingArticlePubService extends IService<ProjFitnessFastingArticlePub> {

    List<ProjFitnessFastingArticleListVO> list(ProjFitnessFastingArticleListReq req, ProjPublishCurrentVersionInfoBO versionInfoBO);

    ProjFitnessFastingArticleDetailVO detail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO);
}
