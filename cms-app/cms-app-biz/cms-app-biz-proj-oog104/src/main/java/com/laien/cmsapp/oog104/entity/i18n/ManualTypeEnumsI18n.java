package com.laien.cmsapp.oog104.entity.i18n;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.BaseTextI18nModel;
import com.laien.common.oog104.enums.manual.ManualTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * ManualTypeEnumsI18n
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ManualTypeEnumsI18n", description = "ManualTypeEnumsI18n")
@TableName(autoResultMap = true)
public class ManualTypeEnumsI18n extends BaseTextI18nModel {

    @AppTextTranslateField
    @ApiModelProperty(value = "Exercise Name")
    private String name;

    public ManualTypeEnumsI18n(ManualTypeEnums enums) {
        super.setUniqueKey(enums.getName());
        this.name = enums.getName();
    }
}
