<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.laien.cmsapp.oog104.mapper.ProjFitnessVideoMapper">

    <select id="selectListDetailByIds" resultType="com.laien.cmsapp.oog104.entity.ProjFitnessVideo">
        SELECT
            id,
            `name`,
            core_voice_config_i18n_id,
            event_name,
            cover_img_url,
            video_url,
            video_dynamic_url,
            instructions,
            instructions_audio_url,
            instructions_audio_duration,
            how_to_do,
            how_to_do_audio_url,
            how_to_do_audio_duration
        FROM
            proj_fitness_video
        WHERE id IN
        <foreach collection="idCollection" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>
