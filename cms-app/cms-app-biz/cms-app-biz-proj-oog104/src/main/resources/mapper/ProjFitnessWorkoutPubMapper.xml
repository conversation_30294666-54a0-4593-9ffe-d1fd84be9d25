<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.laien.cmsapp.oog104.mapper.ProjFitnessWorkoutPubMapper">

    <select id="selectListDetailByIds" resultType="com.laien.cmsapp.oog104.entity.ProjFitnessWorkoutPub">
        SELECT
            id,
            `name`,
            event_name,
            cover_img_url,
            detail_img_url,
            cover_video_url,
            cover_video_duration,
            extra_tag_codes,
            duration,
            calorie,
            video_url,
            video_dynamic_url,
            audio_json_url,
            description
        FROM
            proj_fitness_workout_pub
        WHERE `version` = #{version}
            AND id IN
        <foreach collection="idCollection" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>
