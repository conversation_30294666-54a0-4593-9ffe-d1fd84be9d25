package com.laien.cmsapp.util;

import cn.hutool.extra.spring.SpringUtil;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.common.constant.GlobalConstant;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * redis工具类，会自动用应用名做前缀，也就是说不同应用，key肯定不一样
 * <AUTHOR>
 */
public class CmsAppRedisUtil {
    private static final RedissonClient CLIENT = SpringUtil.getBean(RedissonClient.class);

    private static final String PREFIX = SpringUtil.getApplicationName();

    /**
     * key的默认过期时间，默认300秒
     */
    private final static Long DEFAULT_EXPIRED = 300L;
    private final static TimeUnit DEFAULT_EXPIRED_UNIT = TimeUnit.SECONDS;

    /**
     * 读取缓存
     *
     * @param key 缓存key
     * @param <T> T
     * @return 缓存返回值
     */
//    public static <T> T get(String key) {
//        RBucket<T> bucket = CLIENT.getBucket(getKeyWithPrefix(key));
//        return bucket.get();
//    }

    public static <T> T get(String key, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        if (Objects.equals(versionInfoBO.getCurrentVersion(), GlobalConstant.VERSION_PRE_RELEASE)) {
            return null;
        }
        RBucket<T> bucket = CLIENT.getBucket(getKeyWithPrefix(key, versionInfoBO));
        return bucket.get();
    }


    /**
     * 设置缓存，永不过期（注：redisson会自动选择序列化反序列化方式）
     *
     * @param key   缓存key
     * @param value 缓存值
     * @param <T> T
     */
//    public static <T> void set(String key, T value) {
//        CLIENT.getBucket(getKeyWithPrefix(key)).set(value, DEFAULT_EXPIRED, DEFAULT_EXPIRED_UNIT);
//    }

    public static <T> void set(String key, T value, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        if (Objects.equals(versionInfoBO.getCurrentVersion(), GlobalConstant.VERSION_PRE_RELEASE)) {
            return;
        }
        CLIENT.getBucket(getKeyWithPrefix(key, versionInfoBO)).set(value, DEFAULT_EXPIRED, DEFAULT_EXPIRED_UNIT);
    }


    /**
     * 设置缓存
     *
     * @param key     key
     * @param value   value
     * @param expired 过期时间
     * @param unit    过期时间单位
     */
//    public static <T> void set(String key, T value, long expired, TimeUnit unit) {
//        CLIENT.getBucket(getKeyWithPrefix(key)).set(value, expired, unit);
//    }

    public static <T> void set(String key, T value, long expired, TimeUnit unit, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        if (Objects.equals(versionInfoBO.getCurrentVersion(), GlobalConstant.VERSION_PRE_RELEASE)) {
            return;
        }
        CLIENT.getBucket(getKeyWithPrefix(key, versionInfoBO)).set(value, expired, unit);
    }


//    private static String getKeyWithPrefix(String key) {
//        return String.format("%s:%s", PREFIX, key);
//    }

    public static RLock getLock(String key, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        return CLIENT.getLock(getLockKey(key, versionInfoBO));
    }

//    public static void lock(String key, ProjPublishCurrentVersionInfoBO versionInfoBO) {
//        CLIENT.getLock(getLockKey(key, versionInfoBO)).lock();
//    }
//
//    public static void unlock(String key, ProjPublishCurrentVersionInfoBO versionInfoBO) {
//        CLIENT.getLock(getLockKey(key, versionInfoBO)).unlock();
//    }

    private static String getKeyWithPrefix(String key, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        return String.format("%s:%s:p-%s:v-%s", PREFIX, key, versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion());
    }

    private static String getLockKey(String key, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        return String.format("%s:%s:lock:p-%s:v-%s", PREFIX, key, versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion());
    }

}
