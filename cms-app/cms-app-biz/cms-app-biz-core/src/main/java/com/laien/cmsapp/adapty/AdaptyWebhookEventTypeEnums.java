package com.laien.cmsapp.adapty;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AdaptyWebhookEventTypeEnums {

    SUBSCRIPTION_STARTED("subscription_started"),
    SUBSCRIPTION_RENEWED("subscription_renewed"),
    SUBSCRIPTION_RENEWAL_CANCELLED("subscription_renewal_cancelled"),
    SUBSCRIPTION_RENEWAL_REACTIVATED("subscription_renewal_reactivated"),
    SUBSCRIPTION_EXPIRED("subscription_expired"),
    SUBSCRIPTION_PAUSED("subscription_paused"),
    NON_SUBSCRIPTION_PURCHASE("non_subscription_purchase"),
    TRIAL_STARTED("trial_started"),
    TRIAL_CONVERTED("trial_converted"),
    TRIAL_RENEWAL_CANCELLED("trial_renewal_cancelled"),
    TRIAL_RENEWAL_REACTIVATED("trial_renewal_reactivated"),
    TRIAL_EXPIRED("trial_expired"),
    ENTERED_GRACE_PERIOD("entered_grace_period"),
    BILLING_ISSUE_DETECTED("billing_issue_detected"),
    SUBSCRIPTION_REFUNDED("subscription_refunded"),
    NON_SUBSCRIPTION_PURCHASE_REFUNDED("non_subscription_purchase_refunded"),
    ACCESS_LEVEL_UPDATED("access_level_updated");

    private String name;

}
