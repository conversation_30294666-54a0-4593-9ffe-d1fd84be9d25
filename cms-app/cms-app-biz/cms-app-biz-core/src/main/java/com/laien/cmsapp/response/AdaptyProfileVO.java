package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/12
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdaptyProfileVO {

    private AdaptyProfileBase data;

    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdaptyProfileBase {

        private String app_id;

        private String profile_id;

        private long timestamp;

        private List<AdaptyProfileSub> subscriptions;
    }

    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdaptyProfileSub {

        private String store;

        private String store_product_id;

        private String store_transaction_id;

        private OffsetDateTime purchased_at;

        private OffsetDateTime expires_at;
    }

}
