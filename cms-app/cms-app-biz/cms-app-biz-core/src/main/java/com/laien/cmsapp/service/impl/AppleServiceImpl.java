package com.laien.cmsapp.service.impl;

import com.laien.cmsapp.client.AppleClient;
import com.laien.cmsapp.response.AppleSubGroupVO;
import com.laien.cmsapp.service.AppleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Slf4j
@Service
public class AppleServiceImpl implements AppleService {

    @Resource
    private AppleClient appleClient;

    @Override
    public AppleSubGroupVO listSubGroup(Integer groupId, String token) {

        try{
            return appleClient.listSubGroup(groupId, token);
        } catch (Exception e) {
            log.error(String.format("[Apple Client] Can't fetch sub group list, group id is %s, token is %s.", groupId, token), e);
            return null;
        }
    }


}
