package com.laien.cmsapp.service;

/**
 * 文件 service
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * base 路径
     *
     * @return String
     */
    String getBaseUrl();

    /**
     * cloudflare R2 路径
     *
     * @return String
     */
    String getBaseR2Url();

    /**
     * 获取完整url
     *
     * @param fileRelativeUrl fileRelativeUrl
     * @return String
     */
    String getAbsoluteUrl(String fileRelativeUrl);

    /**
     * 获取完整cloudflare R2 url
     *
     * @param fileRelativeUrl fileRelativeUrl
     * @return String
     */
    String getAbsoluteR2Url(String fileRelativeUrl);


    /**
     * 获取json文件的内容
     *
     * @param path json文件路径
     * @return json文件的内容
     */
    String getJsonContent(String path);

}
