package com.laien.cmsapp.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Publish Current Version info
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Publish Current Version info", description = "Publish Current Version info")
public class ProjPublishCurrentVersionInfoBO {

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "项目code")
    private String appCode;

    @ApiModelProperty(value = "当期版本")
    private Integer currentVersion;

}
