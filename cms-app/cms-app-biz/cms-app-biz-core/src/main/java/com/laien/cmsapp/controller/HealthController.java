package com.laien.cmsapp.controller;


import cn.hutool.core.date.SystemClock;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务健康检测
 *
 * <AUTHOR>
 */
@Api(tags = {"app端：health","core"})
@RestController
@RequestMapping("/health")
public class HealthController extends ResponseController {

    @ApiOperation(value = "health check")
    @GetMapping("/check")
    public ResponseResult<String> check() {
        return succ(SystemClock.nowDate());
    }

}
