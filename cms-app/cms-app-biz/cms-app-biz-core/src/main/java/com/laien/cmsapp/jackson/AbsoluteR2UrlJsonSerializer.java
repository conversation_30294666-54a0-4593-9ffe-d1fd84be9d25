package com.laien.cmsapp.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.cmsapp.service.FileService;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
public class AbsoluteR2UrlJsonSerializer extends JsonSerializer<String> implements ContextualSerializer {
    @Resource
    private FileService fileService;
    @Override
    public void serialize(String s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeString(fileService.getAbsoluteR2Url(s));
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        AbsoluteR2Url annotation = beanProperty.getAnnotation(AbsoluteR2Url.class);
        if (Objects.nonNull(annotation) && Objects.equals(String.class, beanProperty.getType().getRawClass())) {
            return this;
        }
        return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
    }
}
