package com.laien.cmsapp.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * AppUtilHelper
 *
 * <AUTHOR>
 * @since 2024/10/23 17:42
 **/
public class AppUtilHelper {
    /**
     * 当Collection valueCollection
     * 不为空：执行function
     * 为空：直接返回空List
     *
     * @param valueCollection 待校验的集合
     * @param function        执行的方法
     * @param <T>             传参泛型
     * @param <R>             结果泛型
     */
    public static <T, R> List<R> toList(Collection<T> valueCollection, Function<Collection<T>, List<R>> function) {
        return CollectionUtils.isNotEmpty(valueCollection) ? function.apply(valueCollection) : new ArrayList<>();
    }

    /**
     * 如果value不为空，执行consumer
     *
     * @param value    待校验的集合
     * @param consumer 执行的方法
     * @param <T>      泛型
     */
    public static <T> void checkNull(T value, Consumer<T> consumer) {
        if (value != null) {
            consumer.accept(value);
        }
    }

    /**
     * 如果List values不为空，执行consumer
     *
     * @param values   待校验的集合
     * @param consumer 执行的方法
     * @param <T>      泛型
     */
    public static <T> void checkList(List<T> values, Consumer<List<T>> consumer) {
        if (CollectionUtils.isNotEmpty(values)) {
            consumer.accept(values);
        }
    }

    public static <T> void checkList(Supplier<List<T>> supplier, Consumer<List<T>> consumer) {
        List<T> values = supplier.get();
        if (CollectionUtils.isNotEmpty(values)) {
            consumer.accept(values);
        }
    }
}
