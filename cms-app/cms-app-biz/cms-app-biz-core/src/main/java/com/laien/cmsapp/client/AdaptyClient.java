package com.laien.cmsapp.client;

import com.laien.cmsapp.response.AdaptyProfileVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @author: hhl
 * @date: 2025/6/12
 */
@FeignClient(name = "adaptyClient", url = "https://api.adapty.io")
public interface AdaptyClient {

    @GetMapping(value = "/api/v2/server-side-api/profile/", consumes = MediaType.APPLICATION_JSON_VALUE)
    AdaptyProfileVO getProfile(@RequestHeader(value = "adapty-profile-id") String profileId, @RequestHeader(value = "Authorization") String token);

}
