package com.laien.cmsapp.service.impl;

import com.laien.cmsapp.client.AdaptyClient;
import com.laien.cmsapp.response.AdaptyProfileVO;
import com.laien.cmsapp.service.AdaptyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Slf4j
@Service
public class AdaptyServiceImpl implements AdaptyService {

    @Resource
    private AdaptyClient adaptyClient;

    @Override
    public AdaptyProfileVO getProfile(String profileId, String token) {

        try{
            return adaptyClient.getProfile(profileId, token);
        } catch (Exception e) {
            log.error(String.format("[Adapty Client] Can't fetch profile info, profile id is %s.", profileId), e);
            return null;
        }
    }
}
