package com.laien.cmsapp.util;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * note:
 *
 * <AUTHOR>
 */
public class RequestContextAppUtils {

    public static final String PROJ_PUBLISH_INFO = "proj_publish_info";

    /**
     * 获取当期版本
     *
     * @return ProjPublishCurrentVersionInfoBO
     */
    public static ProjPublishCurrentVersionInfoBO getPublishCurrentVersionInfo() {
        HttpServletRequest request = getRequest();
        return (ProjPublishCurrentVersionInfoBO)request.getAttribute(PROJ_PUBLISH_INFO);
    }

    /**
     * 获取request
     *
     * @return HttpServletRequest
     */
    private static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // get the request
        return requestAttributes.getRequest();
    }

}
