package com.laien.cmsapp.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.laien.cmsapp.annotation.UrlFileName;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class JsonProcessUtils {

    private static final String BASE_PACKAGE = "com.laien.cmsapp.response";
    private static final String RESOURCE_PATTERN = "/**/*.class";

    /**
     * 初始化时 加载所有需要转换的类的信息缓存 便于提升后续转换速度，减少空遍历次数
     */
    private static Map<String, Set<String>> covertClassAndFieldNames = Maps.newHashMap();

    static {
        //spring工具类，可以获取指定路径下的全部类
        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        try {
            String pattern = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX +
                    ClassUtils.convertClassNameToResourcePath(BASE_PACKAGE) + RESOURCE_PATTERN;
            Resource[] resources = resourcePatternResolver.getResources(pattern);
            //MetadataReader 的工厂类
            MetadataReaderFactory readerfactory = new CachingMetadataReaderFactory(resourcePatternResolver);
            //第一次扫描 获取所有包含此类注解的类
            for (Resource resource : resources) {
                //用于读取类信息
                MetadataReader reader = readerfactory.getMetadataReader(resource);
                //扫描到的class
                String classname = reader.getClassMetadata().getClassName();
                Class<?> clazz = Class.forName(classname);
                //判断是否有指定主解
                Field[] declaredFields = clazz.getDeclaredFields();
                if (declaredFields.length > 0) {
                    for (Field declaredField : declaredFields) {
                        if (declaredField.getAnnotation(UrlFileName.class) != null) {
                            addClassAndFiledName(clazz.getSimpleName(), declaredField.getName());
                        }
                    }
                }
            }
            //第二次扫描 获取所有依赖以上类的类
            while (true) {
                boolean isAdded = false;
                for (Resource resource : resources) {
                    //用于读取类信息
                    MetadataReader reader = readerfactory.getMetadataReader(resource);
                    //扫描到的class
                    String classname = reader.getClassMetadata().getClassName();
                    Class<?> clazz = Class.forName(classname);
                    //判断是否有指定主解
                    Field[] declaredFields = clazz.getDeclaredFields();
                    if (declaredFields.length > 0) {
                        for (Field declaredField : declaredFields) {
                            Class<?> type = declaredField.getType();
                            if (List.class.isAssignableFrom(type)) {
                                ParameterizedType pt = (ParameterizedType) declaredField.getGenericType();
                                // 得到泛型里的class类型对象
                                Class<?> actualTypeArgument = (Class<?>) pt.getActualTypeArguments()[0];
                                if (covertClassAndFieldNames.containsKey(actualTypeArgument.getSimpleName())) {
                                    if (!hasClassAndFieldName(clazz.getSimpleName(), declaredField.getName())) {
                                        addClassAndFiledName(clazz.getSimpleName(), declaredField.getName());
                                        isAdded = true;
                                    }
                                }
                            } else {
                                if (covertClassAndFieldNames.containsKey(type.getSimpleName())) {
                                    if (!hasClassAndFieldName(clazz.getSimpleName(), declaredField.getName())) {
                                        addClassAndFiledName(clazz.getSimpleName(), declaredField.getName());
                                        isAdded = true;
                                    }
                                }
                            }
                        }
                    }
                }
                if (!isAdded) {
                    break;
                }
            }

        } catch (IOException | ClassNotFoundException e) {
        }

    }

    private static void addClassAndFiledName(String classSimpleName, String fieldName) {
        Set<String> fieldNames = covertClassAndFieldNames.get(classSimpleName);
        if (fieldNames == null) {
            fieldNames = Sets.newHashSet();
            covertClassAndFieldNames.put(classSimpleName, fieldNames);
        }
        fieldNames.add(fieldName);
    }

    private static boolean hasClassAndFieldName(String classSimpleName, String fieldName) {
        Set<String> fieldNames = covertClassAndFieldNames.get(classSimpleName);
        if (fieldNames == null) {
            return false;
        }
        if (fieldNames.contains(fieldName)) {
            return true;
        }
        return false;
    }


    public static <T> T covertUrlName(T obj, int maxDeep) {
        long startTime = System.currentTimeMillis();
        try {
            listProcess(obj, 0, maxDeep);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }

    private static <T> void listProcess(T obj, int currentDeep, int maxDeep) throws NoSuchFieldException, IllegalAccessException {
        if (obj != null) {
            if (List.class.isAssignableFrom(obj.getClass())) {
                List list = (List) obj;
                for (Object o : list) {
                    covertObj(o, currentDeep, maxDeep);
                }
            } else {
                covertObj(obj, currentDeep, maxDeep);
            }
        }
    }


    private static void covertObj(Object o, int currentDeep, int maxDeep) throws NoSuchFieldException, IllegalAccessException {
        currentDeep++;
        if (currentDeep > maxDeep) {
            return;
        }
        if (o != null) {
            Class<?> aClass1 = o.getClass();
            if (List.class.isAssignableFrom(aClass1)) {
                listProcess(o, currentDeep, maxDeep);
            } else {
                String simpleClssName = aClass1.getSimpleName();
                Set<String> fieldNames = covertClassAndFieldNames.get(simpleClssName);
                if (!CollectionUtils.isEmpty(fieldNames)) {
                    for (String fieldName : fieldNames) {
                        Field declaredField = aClass1.getDeclaredField(fieldName);
                        Class<?> type = declaredField.getType();
                        declaredField.setAccessible(true);
                        if (!isBaseType(type.getSimpleName())) {
                            listProcess(declaredField.get(o), currentDeep, maxDeep);
                        } else {
                            if (isStringType(type.getSimpleName())) {
                                UrlFileName annotation = declaredField.getAnnotation(UrlFileName.class);
                                if (annotation != null) {
                                    Field urlFiel = getUrlField(aClass1, declaredField);
                                    urlFiel.setAccessible(true);
                                    Object o1 = urlFiel.get(o);
                                    if (o1 != null) {
                                        String url = (String) o1;
                                        String name = StringUtils.substringAfterLast(StringUtils.substringBeforeLast(url, "?"), "%2F");
                                        declaredField.set(o, name);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 获取对应的url属性(规则可以修改)
     *
     * @param covertClass
     * @param nameField
     * @return
     * @throws NoSuchFieldException
     */
    private static Field getUrlField(Class covertClass, Field nameField) throws NoSuchFieldException {
        String urlFieldName = StringUtils.substringBefore(nameField.getName(), "Name");
        Field urlField = covertClass.getDeclaredField(urlFieldName);
        return urlField;
    }

    private static boolean isBaseType(String simpleClssName) {
        if (Lists.newArrayList("Integer", "String", "Double", "Long", "Boolean", "LocalDateTime").contains(simpleClssName)) {
            return true;
        }
        return false;
    }

    private static boolean isStringType(String simpleClssName) {
        if (Lists.newArrayList("String").contains(simpleClssName)) {
            return true;
        }
        return false;
    }
}
