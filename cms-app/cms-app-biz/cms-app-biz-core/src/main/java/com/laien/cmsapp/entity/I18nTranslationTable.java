package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 翻译的表配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="I18nTranslationTable对象", description="翻译的表配置")
public class I18nTranslationTable extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "翻译表名")
    private String tableI18nName;

    @ApiModelProperty(value = "字段名列表(逗号分隔)")
    private String columnNames;

    @ApiModelProperty(value = "常量字段名列表 (逗号分隔)")
    private String constantColumnNames;

    @ApiModelProperty(value = "音频字段名列表 (文本@音频地址, 逗号分隔, 格式: 文本字段一@音频地址字段一,文本字段二@音频地址字段二)")
    private String audioColumnNames;

}
