package com.laien.cmsapp.client;

import com.laien.cmsapp.response.AppleSubGroupVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @author: hhl
 * @date: 2025/6/12
 */
@FeignClient(name = "appleClient", url = "https://api.appstoreconnect.apple.com")
public interface AppleClient {

    @GetMapping(value = "/v1/subscriptionGroups/{Id}/subscriptions", consumes = MediaType.APPLICATION_JSON_VALUE)
    AppleSubGroupVO listSubGroup(@PathVariable(value = "Id") Integer Id, @RequestHeader(value = "Authorization") String token);

}
