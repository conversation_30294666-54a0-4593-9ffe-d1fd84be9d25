package com.laien.common.exception;

import com.laien.common.response.setting.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;
import java.util.List;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {HttpRequestMethodNotSupportedException.class})
    public ResponseResult<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.error("不支持的请求方式!", e);

        return ResponseResult.fail(e.getMethod() + " Request not supported");
    }

    @ExceptionHandler(value = {BizException.class})
    public ResponseResult<Void> handleBizException(BizException e) {
        log.warn("业务异常：", e);

        return ResponseResult.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(value = {UnsupportedOperationException.class})
    public ResponseResult<Void> handleUnsupportedOperationException(UnsupportedOperationException e) {
        log.error("系统发生异常", e);

        return ResponseResult.fail(e.getMessage());
    }


    @ExceptionHandler(value = {Exception.class})
    public ResponseResult<Void> handleException(Exception e) {
        log.error("系统发生异常", e);

        return ResponseResult.fail("System error");
    }

    @ExceptionHandler(value = AdaptyCallbackException.class)
    public ResponseEntity<String> handleAdaptyCallbackException(AdaptyCallbackException e) {

        return ResponseEntity.status(HttpStatus.SC_INTERNAL_SERVER_ERROR).body("System error");
    }

    @ExceptionHandler(value = {BindException.class})
    public ResponseResult<Void> handleBindException(BindException e) {
        log.error("字段校验失败", e);
        return ResponseResult.fail("field error：" + e.getFieldError().getDefaultMessage());
    }

    /**
     * @Title: handleConstraintViolationException
     * @Description: Post方式参数验证异常
     * <AUTHOR>
     * @date 2019-11-17 16:33:49
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseResult<Void> handleConstraintViolationException(MethodArgumentNotValidException ex) throws IOException {
        //获取所有错误异常
        List<ObjectError> allErrors = ex.getBindingResult().getAllErrors();
        //只返回第一个信息
        ObjectError error = allErrors.get(0);
        return ResponseResult.fail(error.getDefaultMessage());
    }

}
