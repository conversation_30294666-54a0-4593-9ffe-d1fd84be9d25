package com.laien.cmsapp.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标注翻译字段映射关系
 *
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TranslationField {

    /**
     * 表名
     *
     * @return String
     */
    String table();

    /**
     * 列名
     *
     * @return String
     */
    String column();

    /**
     * 是否是主键，默认false
     *
     * @return bool
     */
    boolean primaryKey() default false;

}
