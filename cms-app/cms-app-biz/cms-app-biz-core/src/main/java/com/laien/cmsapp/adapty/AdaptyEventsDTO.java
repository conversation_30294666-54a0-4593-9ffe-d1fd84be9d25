package com.laien.cmsapp.adapty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdaptyEventsDTO {

    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("customer_user_id")
    private String customerUserId;
    @JsonProperty("idfv")
    private String idfv;
    @JsonProperty("idfa")
    private String idfa;
    @JsonProperty("advertising_id")
    private String advertisingId;
    @JsonProperty("profile_install_datetime")
    private String profileInstallDatetime;
    @JsonProperty("user_agent")
    private String userAgent;
    @JsonProperty("email")
    private String email;
    @JsonProperty("event_type")
    private String eventType;
    @JsonProperty("event_datetime")
    private String eventDatetime;
    @JsonProperty("event_properties")
    private EventPropertiesDTO eventProperties;
    @JsonProperty("event_api_version")
    private Integer eventApiVersion;
    @JsonProperty("profiles_sharing_access_level")
    private List<ProfilesSharingAccessLevelDTO> profilesSharingAccessLevel;
    @JsonProperty("attributions")
    private Map<String, Map<String, Object>> attributions;
    @JsonProperty("user_attributes")
    private UserAttributesDTO userAttributes;
    @JsonProperty("integration_ids")
    private IntegrationIdsDTO integrationIds;

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EventPropertiesDTO {
        @JsonProperty("store")
        private String store;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("price_usd")
        private Double priceUsd;
        @JsonProperty("profile_id")
        private String profileId;
        @JsonProperty("cohort_name")
        private String cohortName;
        @JsonProperty("environment")
        private String environment;
        @JsonProperty("price_local")
        private Double priceLocal;
        @JsonProperty("base_plan_id")
        private String basePlanId;
        @JsonProperty("developer_id")
        private String developerId;
        @JsonProperty("paywall_name")
        private String paywallName;
        @JsonProperty("proceeds_usd")
        private Double proceedsUsd;
        @JsonProperty("variation_id")
        private String variationId;
        @JsonProperty("purchase_date")
        private String purchaseDate;
        @JsonProperty("store_country")
        private String storeCountry;
        @JsonProperty("event_datetime")
        private String eventDatetime;
        @JsonProperty("proceeds_local")
        private Double proceedsLocal;
        @JsonProperty("tax_amount_usd")
        private Integer taxAmountUsd;
        @JsonProperty("transaction_id")
        private String transactionId;
        @JsonProperty("net_revenue_usd")
        private Double netRevenueUsd;
        @JsonProperty("profile_country")
        private String profileCountry;
        @JsonProperty("paywall_revision")
        private String paywallRevision;
        @JsonProperty("profile_event_id")
        private String profileEventId;
        @JsonProperty("tax_amount_local")
        private Integer taxAmountLocal;
        @JsonProperty("net_revenue_local")
        private Double netRevenueLocal;
        @JsonProperty("vendor_product_id")
        private String vendorProductId;
        @JsonProperty("profile_ip_address")
        private String profileIpAddress;
        @JsonProperty("consecutive_payments")
        private Integer consecutivePayments;
        @JsonProperty("rate_after_first_year")
        private Boolean rateAfterFirstYear;
        @JsonProperty("original_purchase_date")
        private String originalPurchaseDate;
        @JsonProperty("original_transaction_id")
        private String originalTransactionId;
        @JsonProperty("subscription_expires_at")
        private String subscriptionExpiresAt;
        @JsonProperty("profile_has_access_level")
        private Boolean profileHasAccessLevel;
        @JsonProperty("profile_total_revenue_usd")
        private Double profileTotalRevenueUsd;
    }

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttributionsDTO {
//        @JsonProperty("appsflyer")
//        private AppsflyerDTO appsflyer;


        @NoArgsConstructor
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class AppsflyerDTO {
            @JsonProperty("ad_set")
            private String adSet;
            @JsonProperty("status")
            private String status;
            @JsonProperty("channel")
            private String channel;
            @JsonProperty("ad_group")
            private Object adGroup;
            @JsonProperty("campaign")
            private String campaign;
            @JsonProperty("creative")
            private Object creative;
            @JsonProperty("created_at")
            private String createdAt;
            @JsonProperty("network_user_id")
            private String networkUserId;
        }
    }

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserAttributesDTO {
        @JsonProperty("Favourite_color")
        private String favouriteColor;
        @JsonProperty("Pet_name")
        private String petName;
        @JsonProperty("app_user_id")
        private String appUserId;
        @JsonProperty("invite_code")
        private String inviteCode;
    }

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IntegrationIdsDTO {
        @JsonProperty("firebase_app_instance_id")
        private String firebaseAppInstanceId;
        @JsonProperty("branch_id")
        private String branchId;
        @JsonProperty("one_signal_player_id")
        private String oneSignalPlayerId;
        @JsonProperty("appsflyer_id")
        private String appsflyerId;
    }

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProfilesSharingAccessLevelDTO {
        @JsonProperty("profile_id")
        private String profileId;
        @JsonProperty("customer_user_id")
        private String customerUserId;
    }
}
