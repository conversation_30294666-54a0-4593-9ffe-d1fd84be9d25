package com.laien.cmsapp.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: hhl
 * @date: 2025/6/12
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleSubVO {

    private String id;

    private String type;

    private SubAttribute attributes;

    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SubAttribute {
        private String name;
        private String productId;
        private Boolean familySharable;
        private String state;
        private String subscriptionPeriod;
        private String reviewNote;
        private Integer groupLevel;
    }

}
