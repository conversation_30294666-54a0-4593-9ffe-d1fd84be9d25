package com.laien.cmsapp.controller;

import com.laien.cmsapp.service.FileService;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * note:
 *
 * <AUTHOR>
 */
@Api(tags = {"app端：file","core"})
@RestController
@RequestMapping("/file")
@RefreshScope
public class FileController extends ResponseController {

    @Resource
    private FileService fileService;

    @ApiOperation(value = "baseUrl")
    @GetMapping("/baseUrl")
    public ResponseResult<String> baseUrl() {
        return succ(fileService.getBaseUrl());
    }

}
