<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.mapper.ResSoundMapper">

    <select id="selectSoundAppList" resultType="com.laien.cmsapp.response.ResSoundListAppVO">
        SELECT
            id,
            sound_name,
            sound_script,
            female_url,
            female_robot_url,
            male_url,
            male_robot_url
        FROM
            res_sound
        WHERE
            del_flag = 0
          AND `status` = 1
          AND sound_type = #{soundType}
        ORDER by id DESC
    </select>

    <select id="selectSoundList" resultType="com.laien.cmsapp.response.ResSoundListVO">
        SELECT
            id,
            sound_name,
            sound_script,
            female_url,
            female_robot_url,
            male_url,
            male_robot_url
        FROM
            res_sound
        WHERE
            del_flag = 0
          AND `status` = 1
          <if test="soundType != null and soundSubType != ''">
              AND sound_type = #{soundType}
          </if>
          <if test="soundSubType != null and soundSubType != ''">
              AND sound_sub_type = #{soundSubType}
          </if>
        ORDER by id DESC
    </select>


</mapper>