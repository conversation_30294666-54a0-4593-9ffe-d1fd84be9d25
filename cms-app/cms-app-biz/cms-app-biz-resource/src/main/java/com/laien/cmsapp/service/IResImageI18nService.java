package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ResImageI18n;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * res_image i18n 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface IResImageI18nService extends IService<ResImageI18n> {

    /**
     * 根据ID查询多语言
     *
     * @param ids ids
     * @return map
     */
    Map<Integer, ResImageI18n> getByIds(List<Integer> ids);

}
