package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * res_image i18n
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResImageI18n对象", description="res_image i18n")
public class ResImageI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;


}
