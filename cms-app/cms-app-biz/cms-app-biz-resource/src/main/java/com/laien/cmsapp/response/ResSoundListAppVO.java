package com.laien.cmsapp.response;

import com.laien.cmsapp.annotation.UrlFileName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Sound list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Sound list", description = "Sound list")
public class ResSoundListAppVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "女声文件地址")
    private String femaleUrl;

    @UrlFileName
    @ApiModelProperty(value = "女声文件名称")
    private String femaleUrlName;

    @ApiModelProperty(value = "机器女声文件地址")
    private String femaleRobotUrl;

    @UrlFileName
    @ApiModelProperty(value = "机器女声文件名称")
    private String femaleRobotUrlName;

    @ApiModelProperty(value = "男声文件地址")
    private String maleUrl;

    @UrlFileName
    @ApiModelProperty(value = "男声文件名称")
    private String maleUrlName;

    @ApiModelProperty(value = "机器男声文件地址")
    private String maleRobotUrl;

    @UrlFileName
    @ApiModelProperty(value = "机器男声文件名称")
    private String maleRobotUrlName;


}
