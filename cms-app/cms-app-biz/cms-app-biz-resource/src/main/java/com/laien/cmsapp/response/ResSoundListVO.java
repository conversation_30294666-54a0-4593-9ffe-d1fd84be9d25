package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Sound list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Sound list", description = "Sound list")
public class ResSoundListVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "女声文件地址")
    private String femaleUrl;

    @ApiModelProperty(value = "机器女声文件地址")
    private String femaleRobotUrl;

    @ApiModelProperty(value = "男声文件地址")
    private String maleUrl;

    @ApiModelProperty(value = "机器男声文件地址")
    private String maleRobotUrl;


}
