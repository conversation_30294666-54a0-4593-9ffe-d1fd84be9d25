package com.laien.cmsapp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * res_image
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResImage对象", description="res_image")
public class ResImage extends BaseModel implements AppTextCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "封面图（默认和女）")
    private String coverImage;

    @ApiModelProperty(value = "详情图（默认和女）")
    private String detailImage;

    @ApiModelProperty(value = "封面图(male)")
    private String coverImageMale;

    @ApiModelProperty(value = "详情图(male)")
    private String detailImageMale;

    @ApiModelProperty(value = "备用封面图")
    private String coverSpareImage;

    @ApiModelProperty(value = "complete_image")
    private String completeImage;

    @TableField("`function`")
    @ApiModelProperty(value = "图片用途，template、template-workout")
    private String function;
    @TableField("`point`")
    @ApiModelProperty(value = "图片用途，Butt、Full body")
    private String point;

    @ApiModelProperty(value = "多个appCode用英文逗号分隔")
    private String appCode;

    @ApiModelProperty(value = "描述")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "状态0 草稿、未启用 1 启用 2禁用")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "取值：Female、Male")
    private String gender;
}
