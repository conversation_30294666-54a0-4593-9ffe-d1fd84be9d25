package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.entity.ResImageI18n;
import com.laien.cmsapp.mapper.ResImageI18nMapper;
import com.laien.cmsapp.service.IResImageI18nService;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * res_image i18n 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Service
public class ResImageI18nServiceImpl extends ServiceImpl<ResImageI18nMapper, ResImageI18n> implements IResImageI18nService {

    @Override
    public Map<Integer, ResImageI18n> getByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<ResImageI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResImageI18n::getLanguage, RequestContextUtils.getLanguage())
                .in(ResImageI18n::getId, ids);
        return this.list(queryWrapper).stream().collect(Collectors.toMap(BaseModel::getId, o -> o));
    }

}
