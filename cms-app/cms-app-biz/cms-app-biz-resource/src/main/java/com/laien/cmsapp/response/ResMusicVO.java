package com.laien.cmsapp.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音乐表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ResMusic对象", description="音乐表")
public class ResMusicVO {


    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "音频")
    @AbsoluteR2Url
    private String audio;

    @ApiModelProperty(value = "音频总时长")
    private Integer audioDuration;


}
