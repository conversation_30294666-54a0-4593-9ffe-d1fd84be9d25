package com.laien.cmsapp.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: 声音请求参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "声音请求参数", description = "声音请求参数")
public class ResSoundReq {

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private String soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private String soundSubType;

    @ApiModelProperty(value = "声音源(female|male|female_robot|male_robot) 默认male_robot")
    private String soundSource;


}
