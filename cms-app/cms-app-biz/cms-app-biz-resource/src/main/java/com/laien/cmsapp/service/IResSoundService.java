package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ResSound;
import com.laien.cmsapp.requst.ResSoundReq;
import com.laien.cmsapp.response.ResSoundListAppVO;
import com.laien.cmsapp.response.ResSoundVO;

import java.util.List;

/**
 * <p>
 * 声音表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface IResSoundService extends IService<ResSound> {

    /**
     * 按照类型查找sound 列表
     *
     * @param soundType 声音类型
     * @param soundSource soundSource
     * @param returnFull returnFull
     * @return list
     */
    List<ResSoundListAppVO> selectSoundAppList(String soundType, String soundSource, boolean returnFull);

    /**
     * 按照类型，子类型 声音源查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    List<ResSoundVO> selectSoundList(ResSoundReq soundReq);

}
