package com.laien.cmsapp.controller;


import com.laien.cmsapp.requst.ResSoundReq;
import com.laien.cmsapp.response.ResSoundListAppVO;
import com.laien.cmsapp.response.ResSoundVO;
import com.laien.cmsapp.service.IResSoundService;
import com.laien.common.constant.SoundSource;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 声音表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Api(tags = {"app端：sound"})
@RestController
public class ResSoundController extends ResponseController {

    @Resource
    private IResSoundService resSoundService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "soundSource", value = "声音源默认值female")
    })
    @ApiOperation(value = "Exercise Flow sound列表v1", tags = {"oog117"})
    @GetMapping("/sound/v1/flowNormalList")
    public ResponseResult<List<ResSoundListAppVO>> flowNormalList(String soundSource) {
        String soundType = "Exercise Flow";
        if (StringUtils.isBlank(soundSource)) {
            soundSource= SoundSource.FEMALE;
        }
        List<ResSoundListAppVO> resSoundListAppVOList = resSoundService.selectSoundAppList(soundType, soundSource, false);
        return succ(resSoundListAppVOList);
    }

    /**
     *
     * com.laien.cmsapp.controller.ResSoundController#flowNormalList(java.lang.String) v2
     *
     * @param soundSource soundSource
     * @return ResponseResult
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "soundSource", value = "声音源默认值female")
    })
    @ApiOperation(value = "Exercise Flow列表v2", tags = {"oog117", "oog308"})
    @GetMapping("/{appCode}/sound/v2/flowNormalList")
    public ResponseResult<List<ResSoundListAppVO>> flowNormalListV2(String soundSource) {
        String soundType = "Exercise Flow";
        if (StringUtils.isBlank(soundSource)) {
            soundSource= SoundSource.FEMALE;
        }
        List<ResSoundListAppVO> resSoundListAppVOList = resSoundService.selectSoundAppList(soundType, soundSource, true);
        return succ(resSoundListAppVOList);
    }


    @ApiOperation(value = "列表v1", tags = {"oog118", "oog200", "oog105", "oog117","oog111", "oog106", "oog104"})
    @GetMapping("/{appCode}/sound/v1/list")
    public ResponseResult<List<ResSoundVO>> list(ResSoundReq soundReq) {
        List<ResSoundVO> soundList = resSoundService.selectSoundList(soundReq);
        return succ(soundList);
    }

}
