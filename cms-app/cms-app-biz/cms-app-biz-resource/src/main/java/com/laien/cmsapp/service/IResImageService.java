package com.laien.cmsapp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ResImage;

import java.util.List;

/**
 * <p>
 * res_image 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public interface IResImageService extends IService<ResImage> {

    /**
     * 根据参数查询image，为null不参与查询
     *
     * @param appCode appCode
     * @param function function
     * @param point point
     * @param limit 获取条数限制
     * @return list
     */
    List<ResImage> find(String appCode, String function, String point, Integer limit);

    /**
     * 根据参数查询image，为null不参与查询, 多语种走中台查询
     *
     * @param appCode appCode
     * @param function function
     * @param point point
     * @param limit 获取条数限制
     * @return list
     */
    List<ResImage> findForI18n(String appCode, String function, String point, Integer limit);

    /**
     * 根据参数查询image，并做i18n转换
     *
     * @param appCode
     * @param function
     * @param point
     * @param limit
     * @return
     */
    List<ResImage> find4I18n(String appCode, String function, String point, Integer limit);
}
