package com.laien.cmsapp.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Sound VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Sound VO", description = "Sound VO")
public class ResSoundVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "文件地址")
    private String soundUrl;

    @ApiModelProperty(value = "文件名称")
    private String soundUrlName;

}
