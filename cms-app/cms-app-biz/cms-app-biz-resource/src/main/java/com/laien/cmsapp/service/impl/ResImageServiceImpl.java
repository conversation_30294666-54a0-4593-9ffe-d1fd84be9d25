package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.entity.ResImage;
import com.laien.cmsapp.mapper.ResImageMapper;
import com.laien.cmsapp.service.IResImageService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.core.enums.LanguageEnums;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.lms.client.service.ICoreTextTaskI18nPubService;
import com.laien.common.util.RequestContextUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * res_image 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Service
public class ResImageServiceImpl extends ServiceImpl<ResImageMapper, ResImage> implements IResImageService {

    @Resource
    private ICoreTextTaskI18nPubService textTaskI18nPubService;

    @Override
    public List<ResImage> find(String appCode, String function, String point, Integer limit) {
        LambdaQueryWrapper<ResImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(appCode),ResImage::getAppCode, appCode)
                .eq(StringUtils.isNotBlank(function),ResImage::getFunction, function)
                .eq(StringUtils.isNotBlank(point),ResImage::getPoint, point)
                .eq(ResImage::getStatus, GlobalConstant.STATUS_ENABLE)
                .orderByAsc(ResImage::getSort)
                .orderByDesc(ResImage::getId);
        if (Objects.nonNull(limit)) {
            wrapper.last(" limit " + limit);
        }
        return this.list(wrapper);
    }

    @Override
    public List<ResImage> findForI18n(String appCode, String function, String point, Integer limit) {
        List<ResImage> resImages = this.find(appCode, function, point, limit);

        // 多语言处理
        String lang = RequestContextUtils.getLanguage();
        // 数据为空或默认语种不需要处理
        if (StringUtils.isBlank(lang) || Objects.equals(lang, GlobalConstant.DEFAULT_LANGUAGE)
                || resImages.isEmpty()) {
            return resImages;
        }

        textTaskI18nPubService.translate(resImages, ProjCodeEnums.COMMON, LanguageEnums.getByNameIgnoreCase(RequestContextUtils.getLanguage()));
        return resImages;
    }

    @Override
    public List<ResImage> find4I18n(String appCode, String function, String point, Integer limit) {

        List<ResImage> resImageList = find(appCode, function, point, limit);
        if (CollectionUtils.isEmpty(resImageList)) {
            return Collections.emptyList();
        }

        textTaskI18nPubService.translate(resImageList, ProjCodeEnums.COMMON, LanguageEnums.getByNameIgnoreCase(RequestContextUtils.getLanguage()));
        return resImageList;
    }


}
