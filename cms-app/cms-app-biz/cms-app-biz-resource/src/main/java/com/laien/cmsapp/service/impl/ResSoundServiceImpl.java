package com.laien.cmsapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ResSound;
import com.laien.cmsapp.entity.i18n.ResSoundI18n;
import com.laien.cmsapp.mapper.ResSoundMapper;
import com.laien.cmsapp.requst.ResSoundReq;
import com.laien.cmsapp.response.ResSoundListAppVO;
import com.laien.cmsapp.response.ResSoundListVO;
import com.laien.cmsapp.response.ResSoundVO;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IResSoundService;
import com.laien.cmsapp.util.CmsAppRedisUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.constant.RedisKeyConstant;
import com.laien.common.constant.SoundSource;
import com.laien.common.util.FireBaseUrlSubUtils;
import com.laien.common.util.RequestContextUtils;
import com.laien.app.common.i18n.client.service.IMiddleI18nDataService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 声音表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Service
public class ResSoundServiceImpl extends ServiceImpl<ResSoundMapper, ResSound> implements IResSoundService {

    @Resource
    private FileService fileService;
    @Resource
    private IMiddleI18nDataService middleI18nDataService;

    @Override
    public List<ResSoundListAppVO> selectSoundAppList(String soundType, String soundSource, boolean returnFull) {
        String key = RedisKeyConstant.APP_SOUND_FLOW_KEY;
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ResSoundListAppVO> list = CmsAppRedisUtil.get(key, versionInfoBO);
        if (list == null) {
            list = this.baseMapper.selectSoundAppList(soundType);
            CmsAppRedisUtil.set(key, list, versionInfoBO);
        }

        if (soundSource.equals(SoundSource.FEMALE)) {
            for (ResSoundListAppVO vo : list) {
                vo.setFemaleUrlName(FireBaseUrlSubUtils.getFileName(vo.getFemaleUrl()));
                if (returnFull) {
                    vo.setFemaleUrl(fileService.getAbsoluteUrl(vo.getFemaleUrl()));
                }
                vo.setFemaleRobotUrl(null);
                vo.setFemaleRobotUrlName(null);
                vo.setMaleUrl(null);
                vo.setMaleUrlName(null);
                vo.setMaleRobotUrl(null);
                vo.setMaleRobotUrlName(null);
            }
        } else if(soundSource.equals(SoundSource.FEMALE_ROBOT)) {
            for (ResSoundListAppVO vo : list) {
                vo.setFemaleRobotUrlName(FireBaseUrlSubUtils.getFileName(vo.getFemaleRobotUrl()));
                if (returnFull) {
                    vo.setFemaleRobotUrl(fileService.getAbsoluteUrl(vo.getFemaleRobotUrl()));
                }
                vo.setFemaleUrl(null);
                vo.setFemaleUrlName(null);
                vo.setMaleUrl(null);
                vo.setMaleUrlName(null);
                vo.setMaleRobotUrl(null);
                vo.setMaleRobotUrlName(null);
            }
        } else if(soundSource.equals(SoundSource.MALE)) {
            for (ResSoundListAppVO vo : list) {
                vo.setMaleUrlName(FireBaseUrlSubUtils.getFileName(vo.getMaleUrl()));
                if (returnFull) {
                    vo.setMaleUrl(fileService.getAbsoluteUrl(vo.getMaleUrl()));
                }
                vo.setFemaleUrl(null);
                vo.setFemaleUrlName(null);
                vo.setFemaleRobotUrl(null);
                vo.setFemaleRobotUrlName(null);
                vo.setMaleRobotUrl(null);
                vo.setMaleRobotUrlName(null);
            }
        } else {
            for (ResSoundListAppVO vo : list) {
                vo.setMaleRobotUrlName(FireBaseUrlSubUtils.getFileName(vo.getMaleRobotUrl()));
                if (returnFull) {
                    vo.setMaleRobotUrl(fileService.getAbsoluteUrl(vo.getMaleRobotUrl()));
                }
                vo.setFemaleUrl(null);
                vo.setFemaleUrlName(null);
                vo.setFemaleRobotUrl(null);
                vo.setFemaleRobotUrlName(null);
                vo.setMaleUrl(null);
                vo.setMaleUrlName(null);
            }
        }
        return list;
    }

    @Override
    public List<ResSoundVO> selectSoundList(ResSoundReq soundReq) {
        if (Objects.isNull(soundReq.getSoundType())) {
            return new ArrayList<>();
        }

        List<ResSoundListVO> list = this.baseMapper.selectSoundList(soundReq.getSoundType(), soundReq.getSoundSubType());
        List<ResSoundVO> soundVOList = new ArrayList<>();
        String soundSource = soundReq.getSoundSource();
        if (StringUtils.isBlank(soundSource)) {
            soundSource= SoundSource.FEMALE;
        }
        List<Integer> idList = new ArrayList<>();
        for (ResSoundListVO vo : list) {
            String soundUrl;
            if (soundSource.equals(SoundSource.FEMALE)) {
                soundUrl = vo.getFemaleUrl();
            } else if(soundSource.equals(SoundSource.FEMALE_ROBOT)) {
                soundUrl= vo.getFemaleRobotUrl();
            } else if(soundSource.equals(SoundSource.MALE)) {
                soundUrl = vo.getMaleUrl();
            } else {
                soundUrl = vo.getMaleRobotUrl();
            }

            if (StringUtils.isNotBlank(soundUrl)) {
                ResSoundVO soundVO = new ResSoundVO();
                soundVO.setId(vo.getId());
                soundVO.setSoundName(vo.getSoundName());
                soundVO.setSoundScript(vo.getSoundScript());
                soundVO.setSoundUrlName(FireBaseUrlSubUtils.getFileName(soundUrl));
                soundVO.setSoundUrl(fileService.getAbsoluteUrl(soundUrl));
                soundVOList.add(soundVO);
                idList.add(soundVO.getId());
            }
        }

        String language = RequestContextUtils.getLanguage();
        if (!Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, language)) {
            Map<Integer, ResSoundI18n> i18nMap = middleI18nDataService.getI18nDataMap(ResSound.class, ResSoundI18n.class, idList, language);
            if (Objects.nonNull(i18nMap)) {
                for (ResSoundVO soundVO : soundVOList) {
                    Integer id = soundVO.getId();
                    if (i18nMap.containsKey(id)) {
                        ResSoundI18n soundI18n = i18nMap.get(id);
                        if (StringUtils.isNotBlank(soundVO.getSoundScript()) && StringUtils.isNotBlank(soundI18n.getSoundScript())) {
                            soundVO.setSoundScript(soundI18n.getSoundScript());
                        }

                        if(soundSource.equals(SoundSource.MALE) || soundSource.equals(SoundSource.MALE_ROBOT)) {
                            if (StringUtils.isNotBlank(soundVO.getSoundUrl()) && StringUtils.isNotBlank(soundI18n.getSoundScriptMale())) {
                                soundVO.setSoundUrl(fileService.getAbsoluteUrl(soundI18n.getSoundScriptMale()));
                                soundVO.setSoundUrlName(FireBaseUrlSubUtils.getFileName(soundI18n.getSoundScriptMale()));
                            }
                        } else {
                            if (StringUtils.isNotBlank(soundVO.getSoundUrl()) && StringUtils.isNotBlank(soundI18n.getSoundScriptFemale())) {
                                soundVO.setSoundUrl(fileService.getAbsoluteUrl(soundI18n.getSoundScriptFemale()));
                                soundVO.setSoundUrlName(FireBaseUrlSubUtils.getFileName(soundI18n.getSoundScriptFemale()));
                            }
                        }
                    }
                }
            }
        }



        return soundVOList;
    }

}
