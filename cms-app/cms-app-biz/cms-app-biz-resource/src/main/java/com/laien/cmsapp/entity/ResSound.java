package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 声音表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResSound对象", description="声音表")
public class ResSound extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private String soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private String soundSubType;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "女声")
    private String femaleUrl;

    @ApiModelProperty(value = "机器女声")
    private String femaleRobotUrl;

    @ApiModelProperty(value = "男声")
    private String maleUrl;

    @ApiModelProperty(value = "机器男声")
    private String maleRobotUrl;

    @ApiModelProperty(value = "female时长")
    private Integer femaleDuration;

    @ApiModelProperty(value = "female robot时长")
    private Integer femaleRobotDuration;

    @ApiModelProperty(value = "male时长")
    private Integer maleDuration;

    @ApiModelProperty(value = "male robot时长")
    private Integer maleRobotDuration;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "压缩状态 1压缩中 2成功 3失败")
    private Integer compressionStatus;


}
