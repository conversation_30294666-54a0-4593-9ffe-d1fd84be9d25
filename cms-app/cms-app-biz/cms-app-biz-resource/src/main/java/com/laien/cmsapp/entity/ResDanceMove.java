package com.laien.cmsapp.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResDanceMove对象", description="")
public class ResDanceMove extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作名称")
    private String eventName;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "是否rest动作，true：是，false：否")
    private Boolean restFlag;

    @ApiModelProperty(value = "图片")
    private String thumbnailUrl;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "视频")
    private String videoUrl;

    @ApiModelProperty(value = "时长（毫秒）")
    private Integer duration;


}
