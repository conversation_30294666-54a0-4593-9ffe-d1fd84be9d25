package com.laien.cmsapp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.entity.ResSound;
import com.laien.cmsapp.response.ResSoundListAppVO;
import com.laien.cmsapp.response.ResSoundListVO;

import java.util.List;

/**
 * <p>
 * 声音表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
public interface ResSoundMapper extends BaseMapper<ResSound> {

    /**
     * 按照类型查找sound 列表
     *
     * @param soundType 声音类型
     * @return list
     */
    List<ResSoundListAppVO> selectSoundAppList(String soundType);

    /**
     * 按照类型，子类型查找 sound 列表
     *
     * @param soundType soundType
     * @param soundSubType soundSubType
     * @return list
     */
    List<ResSoundListVO> selectSoundList(String soundType, String soundSubType);

}
