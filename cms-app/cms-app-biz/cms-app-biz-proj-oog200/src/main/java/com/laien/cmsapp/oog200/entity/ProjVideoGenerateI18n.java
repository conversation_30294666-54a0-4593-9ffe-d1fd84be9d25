package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * video generate 多语言
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjVideoGenerateI18n对象", description="video generate 多语言")
public class ProjVideoGenerateI18n extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "generate id")
    private Integer generateId;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "视频标题字幕（srt文件）")
    private String titleSubtitleUrl;

    @ApiModelProperty(value = "视频详细指导字幕url（srt文件）")
    private String guidanceDefaultUrl;

    @ApiModelProperty(value = "视频详细指导音频url（m3u8文件）")
    private String guidanceDefaultAudioUrl;

    @ApiModelProperty(value = "视频简略指导字幕url（srt文件）")
    private String guidanceLeastUrl;

    @ApiModelProperty(value = "视频简略指导音频url（m3u8文件）")
    private String guidanceLeastAudioUrl;


}
