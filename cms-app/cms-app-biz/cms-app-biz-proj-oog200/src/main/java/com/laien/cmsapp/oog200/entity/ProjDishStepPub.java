package com.laien.cmsapp.oog200.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjDishStepPub extends BaseModel implements AppTextCoreI18nModel {

    private Integer version;

    @ApiModelProperty(value = "描述")
    @AppTextTranslateField
    private String description;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projDishId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
