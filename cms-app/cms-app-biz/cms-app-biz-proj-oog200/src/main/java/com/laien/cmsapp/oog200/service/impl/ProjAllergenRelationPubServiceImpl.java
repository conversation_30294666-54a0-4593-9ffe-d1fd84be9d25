package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjAllergenRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjAllergenRelationPubMapper;
import com.laien.cmsapp.oog200.service.IProjAllergenRelationPubService;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Author:  hhl
 * Date:  2025/1/8 17:28
 */
@Service
public class ProjAllergenRelationPubServiceImpl extends ServiceImpl<ProjAllergenRelationPubMapper, ProjAllergenRelationPub> implements IProjAllergenRelationPubService {

    @Override
    public List<ProjAllergenRelationPub> listByVersionAndDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, AllergenRelationBusinessEnum businessEnum) {

        LambdaQueryWrapper<ProjAllergenRelationPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjAllergenRelationPub::getDataId, dataId);
        queryWrapper.eq(ProjAllergenRelationPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjAllergenRelationPub::getVersion, versionInfoBO.getCurrentVersion());

        if (Objects.nonNull(businessEnum)) {
            queryWrapper.eq(ProjAllergenRelationPub::getBusinessType, businessEnum);
        }
        return list(queryWrapper);
    }
}
