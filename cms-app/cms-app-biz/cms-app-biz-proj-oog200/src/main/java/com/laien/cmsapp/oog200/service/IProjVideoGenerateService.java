package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjVideoGenerate;
import com.laien.cmsapp.oog200.requst.*;
import com.laien.cmsapp.oog200.response.*;

import java.util.List;

/**
 * <p>
 * video generate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface IProjVideoGenerateService extends IService<ProjVideoGenerate> {

    /**
     * 随机获取一个视频
     *
     * @param randomReq randomReq
     * @return ProjVideoGenerateVO
     */
    ProjVideoGenerateVO getRandomOne(ProjVideoGenerateRandomReq randomReq);

    /**
     * 随机获取一个视频
     *
     * @param randomReq randomReq
     * @return ProjVideoGenerateV2VO
     */
    ProjVideoGenerateV2VO getRandomOneV2(ProjVideoGenerateRandomReq randomReq);

    /**
     * 随机获取一个视频
     *
     * @param randomReq randomReq
     * @return ProjVideoGenerateV3VO
     */
    ProjVideoGenerateV3VO getRandomOneV3(ProjVideoGenerateRandomReq randomReq);

    /**
     * 随机获取一个视频
     *
     * @param randomReq randomReq
     * @return ProjVideoGenerateV4VO
     */
    ProjVideoGenerateV4VO getRandomOneV4(ProjVideoGenerateRandomV4Req randomReq);

    /**
     * 生成21天的workout组成一个plan
     *
     * @param planReq planReq
     * @return list
     */
    List<ProjVideoGeneratePlanVO> selectVideoPlan(ProjVideoGeneratePlanReq planReq);

    /**
     * 生成21天的workout组成一个plan
     *
     * @param planReq planReq
     * @return list
     */
    List<ProjVideoGeneratePlanV2VO> selectVideoPlanV2(ProjVideoGeneratePlanV2Req planReq);


    /**
     * 根据workout id列表 获取最新的workout信息
     *
     * @param refreshPlanReq refreshPlanReq
     * @return list
     */
    List<ProjVideoGenerateRefreshPlanVO> selectVideoPlanByWorkoutIds(ProjVideoGenerateRefreshPlanReq refreshPlanReq);

    /**
     * 根据workout id列表 获取最新的workout信息
     *
     * @param refreshPlanReq refreshPlanReq
     * @return list
     */
    List<ProjVideoGenerateRefreshPlanV2VO> selectVideoPlanByWorkoutIdsV2(ProjVideoGenerateRefreshPlanV2Req refreshPlanReq);

}
