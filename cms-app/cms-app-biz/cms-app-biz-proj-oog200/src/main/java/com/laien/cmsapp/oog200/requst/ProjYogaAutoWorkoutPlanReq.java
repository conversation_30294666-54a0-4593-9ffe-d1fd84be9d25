package com.laien.cmsapp.oog200.requst;

import com.laien.cmsapp.requst.LangReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * note: yogaAutoWorkout plan
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "yogaAutoWorkout plan", description = "yogaAutoWorkout plan")
public class ProjYogaAutoWorkoutPlanReq extends LangReq {

    @ApiModelProperty(value = "时长标签 -10|10-20|20-，格式（开始-结束）", required = true)
    private String duration;

    @ApiModelProperty(value = "difficulty Newbie|Beginner|Intermediate|Advanced,为空或不存在的值默认为Beginner",example = "Beginner")
    private String difficulty;

    @ApiModelProperty(value = "goal basic|weight-loss|flexibility|mindfulness", example = "basic")
    private String goal;

    @ApiModelProperty(value = "random 20以内整数")
    private Integer random;

    @ApiModelProperty(value = "传1 查询2532 m3u8, 传2 查询dynamic m3u8, 默认1")
    private Integer m3u8Type = 1;

}
