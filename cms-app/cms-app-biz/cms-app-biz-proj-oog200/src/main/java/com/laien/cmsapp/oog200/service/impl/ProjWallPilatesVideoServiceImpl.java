package com.laien.cmsapp.oog200.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesVideo;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesVideoResource;
import com.laien.cmsapp.oog200.mapper.ProjWallPilatesVideoMapper;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IProjWallPilatesVideoResourceService;
import com.laien.cmsapp.oog200.service.IProjWallPilatesVideoService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.RequestContextUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * wall pilates video资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Service
public class ProjWallPilatesVideoServiceImpl extends ServiceImpl<ProjWallPilatesVideoMapper, ProjWallPilatesVideo> implements IProjWallPilatesVideoService {

    @Resource
    private IProjWallPilatesVideoResourceService projWallPilatesVideoResourceService;
    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ResYogaVideoDetailVO> queryByWallPilatesRegularWorkoutIdSet(Set<Integer> wallPilatesRegularWorkoutIdSet) {
        if (CollUtil.isEmpty(wallPilatesRegularWorkoutIdSet)) {
            return new ArrayList<>();
        }
        List<ResYogaVideoDetailVO> videoList = baseMapper.queryByWallPilatesRegularWorkoutIdSet(wallPilatesRegularWorkoutIdSet);
        if (CollUtil.isEmpty(videoList)) {
            return videoList;
        }
        i18nUtil.translate4Speech(videoList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return handleDetailVO(videoList);
    }

    @Override
    public List<ResYogaVideoDetailVO> find(Set<Integer> wallPilatesAutoWorkoutIdSet) {
        if (CollUtil.isEmpty(wallPilatesAutoWorkoutIdSet)) {
            return new ArrayList<>();
        }

        List<ResYogaVideoDetailVO> videoList = baseMapper.find(wallPilatesAutoWorkoutIdSet);
        i18nUtil.translate4Speech(videoList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return handleDetailVO(videoList);
    }

    private List<ResYogaVideoDetailVO> handleDetailVO(List<ResYogaVideoDetailVO> videoList) {
        if (CollUtil.isEmpty(videoList)) {
            return videoList;
        }
        List<Integer> videoIdList = videoList.stream().map(ResYogaVideoDetailVO::getId).collect(Collectors.toList());

        List<ProjWallPilatesVideoResource> resourceList = projWallPilatesVideoResourceService.findByWallPilatesVideoIdList(videoIdList);
        Map<Integer, ProjWallPilatesVideoResource> videoResourceMap = resourceList.stream()
                .collect(Collectors.toMap(ProjWallPilatesVideoResource::getProjWallPilatesVideoId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );
        for (ResYogaVideoDetailVO video : videoList) {
            ProjWallPilatesVideoResource resource = videoResourceMap.get(video.getId());
            if(null != resource) {
                Integer frontVideoDuration = resource.getFrontVideoDuration();
                Integer sideVideoDuration = resource.getSideVideoDuration();
                Integer duration = frontVideoDuration * 3 + sideVideoDuration;
                video.setPreviewVideoDuration(frontVideoDuration);
                video.setRealVideoDuration(duration);
            }
        }
        return videoList;
    }
}
