package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.requst.ProjYogaPoseLevelListReq;
import com.laien.cmsapp.oog200.response.ProjYogaPoseLevelDetailVO;
import com.laien.cmsapp.oog200.service.IProjYogaPoseLevelService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;

/**
 * <p>
 * proj yoga pose grouping 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Api(tags = "app端：yogaPoseLevel")
@RestController
@RequestMapping(value = {"/oog200/yogaPoseLevel", "/OOG200/yogaPoseLevel"})
public class ProjYogaPoseLevelController extends ResponseController {
    @Resource
    private IProjYogaPoseLevelService projYogaPoseLevelService;

    @ApiOperation(value = "detail", tags = {"oog200"})
    @GetMapping("/v1/detail")
    public ResponseResult<ProjYogaPoseLevelDetailVO> list(ProjYogaPoseLevelListReq listReq) {
        Integer m3u8Type = listReq.getM3u8Type();
        if (null == m3u8Type) {
            m3u8Type = 1;
        }
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projYogaPoseLevelService.findDetail(listReq, versionInfoBO, m3u8Type));
    }

}
