package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * note: Workout116 audio
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Workout116 audio", description = "Workout116 audio")
public class ProjWorkoutAudioDetailVO116 {

    @ApiModelProperty(value = "language")
    private String language;

    @ApiModelProperty(value = "audio json url")
    @AbsoluteR2Url
    private String audioJsonUrl;

}
