package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjMealPlanPub;
import com.laien.cmsapp.oog200.entity.ProjMealPlanRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjMealPlanPubMapper;
import com.laien.cmsapp.oog200.mapper.ProjMealPlanRelationPubMapper;
import com.laien.cmsapp.oog200.response.ProjDailyDishListVO;
import com.laien.cmsapp.oog200.response.ProjDishListVO;
import com.laien.cmsapp.oog200.response.ProjMealPlanDetailVO;
import com.laien.cmsapp.oog200.response.ProjMealPlanListVO;
import com.laien.cmsapp.oog200.service.IProjDishPubService;
import com.laien.cmsapp.oog200.service.IProjMealPlanPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:08
 */
@Service
public class ProjMealPlanPubServiceImpl extends ServiceImpl<ProjMealPlanPubMapper, ProjMealPlanPub> implements IProjMealPlanPubService {

    @Resource
    private IProjDishPubService dishPubService;

    @Resource
    private ProjMealPlanRelationPubMapper mealPlanRelationPubMapper;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjMealPlanListVO> listMealPlan(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang) {

        List<ProjMealPlanPub> mealPlanPubList = listByStatusAndVersion(versionInfoBO, status);
        if (CollectionUtils.isEmpty(mealPlanPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(mealPlanPubList, ProjCodeEnums.OOG200, lang);
        List<ProjMealPlanListVO> mealPlanListVOS = mealPlanPubList.stream().map(mealPlan -> convert2ListVO(mealPlan)).collect(Collectors.toList());
        return mealPlanListVOS;
    }

    private ProjMealPlanListVO convert2ListVO(ProjMealPlanPub mealPlanPub) {

        ProjMealPlanListVO projMealPlanListVO = new ProjMealPlanListVO();
        BeanUtils.copyProperties(mealPlanPub, projMealPlanListVO);
        return projMealPlanListVO;
    }

    private List<ProjMealPlanPub> listByStatusAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status) {

        LambdaQueryWrapper<ProjMealPlanPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(status), ProjMealPlanPub::getStatus, status);
        queryWrapper.eq(ProjMealPlanPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjMealPlanPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.orderByAsc(ProjMealPlanPub::getSorted);
        return list(queryWrapper);
    }

    private ProjMealPlanPub getByIdAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer mealPlanId) {

        LambdaQueryWrapper<ProjMealPlanPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjMealPlanPub::getId, mealPlanId);
        queryWrapper.eq(ProjMealPlanPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjMealPlanPub::getProjId, versionInfoBO.getProjId());
        return getOne(queryWrapper);
    }

    @Override
    public ProjMealPlanDetailVO getMealPlanDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer mealPlanId, String lang) {

        ProjMealPlanPub mealPlanPub = getByIdAndVersion(versionInfoBO, mealPlanId);
        if (Objects.isNull(mealPlanPub)) {
            return null;
        }

        i18nUtil.translate(Lists.newArrayList(mealPlanPub), ProjCodeEnums.OOG200, lang);
        ProjMealPlanDetailVO mealPlanDetailVO = new ProjMealPlanDetailVO();
        BeanUtils.copyProperties(mealPlanPub, mealPlanDetailVO);

        List<ProjMealPlanRelationPub> planRelationPubList = mealPlanRelationPubMapper.listByVersionAndMealPlanId(versionInfoBO.getCurrentVersion(), mealPlanId);
        if (CollectionUtils.isEmpty(planRelationPubList)) {
            return mealPlanDetailVO;
        }

        Set<Integer> dishIds = planRelationPubList.stream().map(ProjMealPlanRelationPub::getProjDishId).collect(Collectors.toSet());
        List<ProjDishListVO> dishListVOList = dishPubService.listDish(versionInfoBO, dishIds, null, lang);
        if (CollectionUtils.isEmpty(dishListVOList)) {
            return mealPlanDetailVO;
        }

        Map<Integer, ProjDishListVO> dishIdMap = dishListVOList.stream().collect(Collectors.toMap(ProjDishListVO::getId, Function.identity()));
        TreeMap<Integer, List<ProjMealPlanRelationPub>> dayAndRelationMap = planRelationPubList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjDishId()))
                .sorted(Comparator.comparing(ProjMealPlanRelationPub::getDay).thenComparing(ProjMealPlanRelationPub::getId)).collect(Collectors.groupingBy(ProjMealPlanRelationPub::getDay, TreeMap::new, Collectors.toList()));

        List<ProjDailyDishListVO> dailyDishList = Lists.newArrayList();
        dayAndRelationMap.entrySet().forEach(entry -> {
            ProjDailyDishListVO dailyDishListVO = convert2DailyVO(entry.getKey(), entry.getValue(), dishIdMap);
            if (Objects.nonNull(dailyDishListVO)) {
                dailyDishList.add(dailyDishListVO);
            }
        });

        mealPlanDetailVO.setDailyDishList(dailyDishList);
        return mealPlanDetailVO;
    }

    private ProjDailyDishListVO convert2DailyVO(Integer day, List<ProjMealPlanRelationPub> relationPubList, Map<Integer, ProjDishListVO> dishIdMap) {

        List<ProjDishListVO> dishListVOS = relationPubList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjDishId()))
                .map(relation -> setType4Dish(dishIdMap.get(relation.getProjDishId()), relation)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dishListVOS)) {
            return null;
        }

        ProjDailyDishListVO dailyDishListVO = new ProjDailyDishListVO();
        dailyDishListVO.setDay(day);
        dailyDishListVO.setDishList(dishListVOS);
        return dailyDishListVO;
    }

    private ProjDishListVO setType4Dish(ProjDishListVO dishListVO, ProjMealPlanRelationPub relationPub) {

        if (Objects.nonNull(relationPub.getDishType())) {
            dishListVO.setDishTypeCode(relationPub.getDishType().getCode());
        }
        return dishListVO;
    }


}
