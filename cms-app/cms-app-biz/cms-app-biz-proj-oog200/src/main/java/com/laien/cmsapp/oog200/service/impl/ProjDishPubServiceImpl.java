package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjDishPub;
import com.laien.cmsapp.oog200.mapper.ProjDishPubMapper;
import com.laien.cmsapp.oog200.response.ProjDishDetailVO;
import com.laien.cmsapp.oog200.response.ProjDishListVO;
import com.laien.cmsapp.oog200.response.ProjDishStepVO;
import com.laien.cmsapp.oog200.response.ProjIngredientVO;
import com.laien.cmsapp.oog200.service.IProjAllergenPubService;
import com.laien.cmsapp.oog200.service.IProjDishPubService;
import com.laien.cmsapp.oog200.service.IProjDishStepPubService;
import com.laien.cmsapp.oog200.service.IProjIngredientPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:39
 */
@Slf4j
@Service
public class ProjDishPubServiceImpl extends ServiceImpl<ProjDishPubMapper, ProjDishPub> implements IProjDishPubService {

    @Resource
    private IProjDishStepPubService dishStepPubService;

    @Resource
    private IProjIngredientPubService ingredientPubService;

    @Resource
    private IProjAllergenPubService allergenPubService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjDishListVO> listDish(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> dishIds, Integer status, String lang) {

        if (CollectionUtils.isEmpty(dishIds)) {
            return Collections.emptyList();
        }

        List<ProjDishPub> dishPubList = listByStatusAndVersion(dishIds, status, versionInfoBO);
        if (CollectionUtils.isEmpty(dishPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(dishPubList, ProjCodeEnums.OOG200, lang);
        return dishPubList.stream().map(dish -> convert2ListVO(dish)).collect(Collectors.toList());
    }

    private ProjDishListVO convert2ListVO(ProjDishPub projDishPub) {

        if (Objects.isNull(projDishPub)) {
            return null;
        }

        ProjDishListVO projDishVO = new ProjDishListVO();
        BeanUtils.copyProperties(projDishPub, projDishVO);
        return projDishVO;
    }

    private List<ProjDishPub> listByStatusAndVersion(Collection<Integer> dishIds, Integer status, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        LambdaQueryWrapper<ProjDishPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(dishIds), ProjDishPub::getId, dishIds);
        queryWrapper.eq(Objects.nonNull(status), ProjDishPub::getStatus, status);
        queryWrapper.eq(ProjDishPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjDishPub::getProjId, versionInfoBO.getProjId());
        return list(queryWrapper);
    }

    @Override
    public ProjDishDetailVO getDishDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang, Integer m3u8Type) {

        List<ProjDishPub> dishPubList = listByStatusAndVersion(Lists.newArrayList(dishId), null, versionInfoBO);
        if (CollectionUtils.isEmpty(dishPubList)) {
            return null;
        }

        ProjDishPub projDishPub = dishPubList.get(GlobalConstant.ZERO);
        i18nUtil.translate(Lists.newArrayList(projDishPub), ProjCodeEnums.OOG200, lang);
        ProjDishDetailVO dishDetailVO = convert2DetailVO(projDishPub, m3u8Type);

        // 配料
        List<ProjIngredientVO> ingredientVOList = ingredientPubService.listByDishId(versionInfoBO, dishId, lang);
        if (!CollectionUtils.isEmpty(ingredientVOList)) {
            dishDetailVO.setIngredientList(ingredientVOList);
        }

        // 步骤
        List<ProjDishStepVO> dishStepVOList = dishStepPubService.listByDishId(versionInfoBO, dishId, lang);
        if (!CollectionUtils.isEmpty(dishStepVOList)) {
            dishDetailVO.setDishStepList(dishStepVOList);
        }

        // 过敏原
        List<String> allergenList = allergenPubService.listByDataId(versionInfoBO, dishId, lang, AllergenRelationBusinessEnum.DISH);
        if (!CollectionUtils.isEmpty(allergenList)) {
            dishDetailVO.setAllergenList(allergenList);
        }

        return dishDetailVO;
    }

    private ProjDishDetailVO convert2DetailVO(ProjDishPub projDishPub, Integer m3u8Type) {

        ProjDishDetailVO dishDetailVO = new ProjDishDetailVO();
        BeanUtils.copyProperties(projDishPub, dishDetailVO);

        // 为了解决WEB端 Dish Video 可为空（上传再删除）导致的切片问题，这里对原视频做非空校验
        if (!StringUtils.isEmpty(projDishPub.getResourceVideoUrl()) && Objects.equals(GlobalConstant.ONE, m3u8Type)) {
            dishDetailVO.setVideoM3u8Url(projDishPub.getVideo2532Url());
        }
        if (!StringUtils.isEmpty(projDishPub.getResourceVideoUrl()) && Objects.equals(GlobalConstant.TWO, m3u8Type)) {
            dishDetailVO.setVideoM3u8Url(projDishPub.getVideoUrl());
        }
        return dishDetailVO;
    }
}
