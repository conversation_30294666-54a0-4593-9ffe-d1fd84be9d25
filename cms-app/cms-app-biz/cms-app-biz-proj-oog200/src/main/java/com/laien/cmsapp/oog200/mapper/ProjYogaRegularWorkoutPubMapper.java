package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutPub;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * oog200 workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
public interface ProjYogaRegularWorkoutPubMapper extends BaseMapper<ProjYogaRegularWorkoutPub> {

    /**
     * 查询workout video
     *
     * @param versionInfoBO versionInfoBO
     * @param id id
     * @return list
     */
    List<ResYogaVideoDetailVO> selectWorkoutVideoList(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, Integer id);

    /**
     * 查询workout 关联的音频列表
     *
     * @param versionInfoBO
     * @param idList
     * @return
     */
    List<ResYogaVideoDetailVO> listByIdsAndVersion(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> idList);

}
