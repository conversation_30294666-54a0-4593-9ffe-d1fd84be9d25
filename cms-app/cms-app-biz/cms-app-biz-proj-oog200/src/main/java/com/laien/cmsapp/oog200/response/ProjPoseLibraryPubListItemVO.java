package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "Pose library list item res", description = "Pose library list item res")
@AllArgsConstructor
public class ProjPoseLibraryPubListItemVO {

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "Pose library 列表")
    private List<ProjPoseLibraryPubListItemSubVO> poseLibraryList;
}
