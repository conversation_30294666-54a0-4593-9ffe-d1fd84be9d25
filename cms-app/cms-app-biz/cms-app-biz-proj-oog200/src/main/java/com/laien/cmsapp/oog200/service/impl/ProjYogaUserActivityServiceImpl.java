package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjYogaUserActivity;
import com.laien.cmsapp.oog200.mapper.ProjYogaUserActivityMapper;
import com.laien.cmsapp.oog200.mapper.ProjYogaUserAwardMapper;
import com.laien.cmsapp.oog200.response.ProjYogaAwardVO;
import com.laien.cmsapp.oog200.response.ProjYogaUserInviteResultVO;
import com.laien.cmsapp.oog200.service.IProjYogaUserActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: hhl
 * @date: 2025/6/6
 */
@Service
public class ProjYogaUserActivityServiceImpl extends ServiceImpl<ProjYogaUserActivityMapper, ProjYogaUserActivity> implements IProjYogaUserActivityService {

    @Resource
    private ProjYogaUserActivityMapper userActivityMapper;

    @Resource
    private ProjYogaUserAwardMapper userAwardMapper;

    @Override
    public ProjYogaUserInviteResultVO getByUserId(Integer userId) {

        if (Objects.isNull(userId)) {
            return new ProjYogaUserInviteResultVO(userId, 0, Collections.emptyList());
        }

        List<ProjYogaUserActivity> activityList = userActivityMapper.listByUserId(userId);
        if (CollectionUtils.isEmpty(activityList)) {
            return new ProjYogaUserInviteResultVO(userId, 0, Collections.emptyList());
        }

        List<ProjYogaAwardVO> awardList = userAwardMapper.listByUserId(userId);
        return new ProjYogaUserInviteResultVO(userId, activityList.size(), awardList);
    }

    @Override
    public List<ProjYogaUserActivity> listByUserIds(Collection<Integer> userIds) {

        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaUserActivity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaUserActivity::getUserId, userIds);
        return list(queryWrapper);
    }
}
