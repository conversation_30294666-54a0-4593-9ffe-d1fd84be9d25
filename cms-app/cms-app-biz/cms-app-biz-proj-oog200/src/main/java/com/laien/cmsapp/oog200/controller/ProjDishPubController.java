package com.laien.cmsapp.oog200.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjDishDetailVO;
import com.laien.cmsapp.oog200.service.IProjDishPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Author:  hhl
 * Date:  2025/1/7 20:42
 */
@Api(tags = "app端：dish")
@RestController
@RequestMapping("/{appCode}/dish")
public class ProjDishPubController extends ResponseController {

    @Resource
    IProjDishPubService dishPubService;

    @ApiOperation(value = "获取一个dish的详情数据", tags = {"oog200"})
    @GetMapping("/v1/detail")
    public ResponseResult<ProjDishDetailVO> getDishDetail(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang,
                                                          @RequestParam(required = false, defaultValue = "1") Integer m3u8Type,
                                                          @RequestParam Integer dishId) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjDishDetailVO dishListVOS = dishPubService.getDishDetail(versionInfoBO, dishId, lang, m3u8Type);
        return succ(dishListVOS);
    }

}
