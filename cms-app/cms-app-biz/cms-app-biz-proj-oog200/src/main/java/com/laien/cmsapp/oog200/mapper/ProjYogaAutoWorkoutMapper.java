package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkout;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * oog200 workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
public interface ProjYogaAutoWorkoutMapper extends BaseMapper<ProjYogaAutoWorkout> {

    /**
     * 查询workout video list
     *
     * @param versionInfoBO versionInfoBO
     * @param idList idList
     * @return list
     */
    List<ResYogaVideoDetailVO> selectWorkoutVideoListByIds(@Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO, @Param("idList") Collection<Integer> idList);

}
