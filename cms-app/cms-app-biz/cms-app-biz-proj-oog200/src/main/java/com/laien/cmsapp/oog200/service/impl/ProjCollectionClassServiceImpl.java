package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjCollectionClassPub;
import com.laien.cmsapp.oog200.mapper.ProjCollectionClassMapper;
import com.laien.cmsapp.oog200.requst.CollectionClassListReq;
import com.laien.cmsapp.oog200.service.IProjCollectionClassService;
import com.laien.cmsapp.response.ProjCollectionClassDetailTeacherVO;
import com.laien.cmsapp.response.ProjCollectionClassDetailVO;
import com.laien.cmsapp.response.ProjCollectionClassListVO;
import com.laien.cmsapp.response.ResVideoClassVO;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.service.IProjCollectionTeacherPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.common.oog200.enums.YogaTypeEnum;
import com.laien.common.response.IdAndCountsAndMaxMinRes;
import com.laien.common.util.MyStringUtil;
import com.laien.common.util.RequestContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * proj collection class 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Slf4j
@Service
public class ProjCollectionClassServiceImpl extends ServiceImpl<ProjCollectionClassMapper, ProjCollectionClassPub> implements IProjCollectionClassService {

    @Resource
    private IProjCollectionTeacherPubService projCollectionTeacherPubService;

    @Resource
    private FileService fileService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjCollectionClassListVO> selectCollectionClassList(ProjPublishCurrentVersionInfoBO versionInfoBO, CollectionClassListReq req, List<YogaAutoWorkoutTemplateEnum> typeList) {
        LambdaQueryWrapper<ProjCollectionClassPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjCollectionClassPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjCollectionClassPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjCollectionClassPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(CollUtil.isNotEmpty(typeList), ProjCollectionClassPub::getType, typeList);
        List<ProjCollectionClassPub> collectionClassList = baseMapper.selectList(wrapper);
        if (collectionClassList.isEmpty()) {
            return new ArrayList<>();
        }

        i18nUtil.translate(collectionClassList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());

        // 查询包含的class video 数量, 数量为0不返回
        List<IdAndCountsAndMaxMinRes> collectionClassCountList = this.baseMapper.selectCollectionClassCounts(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion());
        List<ProjCollectionClassListVO> collectionClassResultList = new ArrayList<>(collectionClassList.size());

        List<ProjCollectionClassListVO> matchedNewTimeAndGoalList = new ArrayList<>(collectionClassList.size());
        List<ProjCollectionClassListVO> matchedNewTimeList = new ArrayList<>(collectionClassList.size());
        List<ProjCollectionClassListVO> unmatchedNewTimeList = new ArrayList<>(collectionClassList.size());
        List<ProjCollectionClassListVO> nullNewTimeList = new ArrayList<>(collectionClassList.size());
        LocalDateTime now = LocalDateTime.now();

        List<ProjCollectionClassListVO> allCollectionClassList = new ArrayList<>(collectionClassList.size());
        for (ProjCollectionClassPub collectionClass : collectionClassList) {
            ProjCollectionClassListVO listVO = new ProjCollectionClassListVO();
            BeanUtils.copyProperties(collectionClass, listVO);
            // 处理 yoga Type 逗号分隔字符串改为code数组
            if(StringUtils.isNotBlank(collectionClass.getYogaType())){
                List<Integer> yogaTypeCodeList = Stream.of(collectionClass.getYogaType().split(GlobalConstant.COMMA))
                        .map(YogaTypeEnum::getCodeByName).filter(Objects::nonNull).collect(Collectors.toList());
                listVO.setYogaTypeCodeList(yogaTypeCodeList);
            }
            // video type 转为 code
            if(Objects.nonNull(collectionClass.getType())){
                listVO.setTypeCode(collectionClass.getType().getCode());
            }

            allCollectionClassList.add(listVO);


            List<Integer> workoutGoalCodeList = new ArrayList<>();
            for (String goalCodeString : MyStringUtil.getSplitWithComa(collectionClass.getGoal())) {

                if (NumberUtil.isInteger(goalCodeString)) {
                    workoutGoalCodeList.add(NumberUtil.parseInt(goalCodeString));
                }
            }

            listVO.setGoalCodeList(workoutGoalCodeList);

            for (IdAndCountsAndMaxMinRes countsRes : collectionClassCountList) {
                if (Objects.equals(listVO.getId(), countsRes.getId())) {
                    listVO.setClassCount(countsRes.getCounts());
                    listVO.setMaxDuration(TimeConvertUtil.millisToMinutes(countsRes.getMaxValue().intValue()));
                    listVO.setMinDuration(TimeConvertUtil.millisToMinutes(countsRes.getMinValue().intValue()));
                    break;
                }
            }

            LocalDateTime newEndTime = listVO.getNewEndTime();
            LocalDateTime newStartTime = listVO.getNewStartTime();
            if (Objects.isNull(newEndTime) || Objects.isNull(newStartTime)) {
                nullNewTimeList.add(listVO);
            } else {
                if (now.isAfter(newStartTime) && now.isBefore(newEndTime)) {
                    if (CollUtil.containsAny(req.getGoalCodeList(), workoutGoalCodeList)) {
                        matchedNewTimeAndGoalList.add(listVO);
                    } else {
                        matchedNewTimeList.add(listVO);
                    }
                } else {
                    unmatchedNewTimeList.add(listVO);
                }
            }
        }

        Collections.sort(matchedNewTimeAndGoalList, Comparator.comparing(ProjCollectionClassListVO::getNewStartTime).reversed());
        Collections.sort(matchedNewTimeList, Comparator.comparing(ProjCollectionClassListVO::getNewStartTime).reversed());
        Collections.sort(unmatchedNewTimeList, Comparator.comparing(ProjCollectionClassListVO::getNewStartTime).reversed());

        collectionClassResultList.addAll(matchedNewTimeAndGoalList);
        collectionClassResultList.addAll(matchedNewTimeList);
        collectionClassResultList.addAll(unmatchedNewTimeList);
        collectionClassResultList.addAll(nullNewTimeList);

        return collectionClassResultList;
    }

    @Override
    public ProjCollectionClassDetailVO selectCollectionClassDetail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        ProjCollectionClassDetailVO detailVO = this.baseMapper.selectCollectionClassDetail(id, versionInfoBO);
        if (Objects.isNull(detailVO)) {
            return null;
        }

        // 处理 yoga Type 逗号分隔字符串改为code数组
        if(StringUtils.isNotBlank(detailVO.getYogaType())){
            List<Integer> yogaTypeCodeList = Stream.of(detailVO.getYogaType().split(GlobalConstant.COMMA))
                    .map(YogaTypeEnum::getCodeByName).filter(Objects::nonNull).collect(Collectors.toList());
            detailVO.setYogaTypeCodeList(yogaTypeCodeList);
        }

        i18nUtil.translate(Collections.singletonList(detailVO), ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());

        if (detailVO != null) {
            List<ResVideoClassVO> classes = this.baseMapper.selectCollectionClassVideoClasses(id, versionInfoBO);
            i18nUtil.translate(classes, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
            for (ResVideoClassVO vo : classes) {
                vo.setDuration(TimeConvertUtil.millisToMinutes(vo.getDuration()));
                //替换m3u8Url为video2532M3u8Url
                if (StringUtils.isNotBlank(vo.getVideo2532M3u8Url())) {
                    vo.setVideoUrl(fileService.getAbsoluteR2Url(vo.getVideo2532M3u8Url()));
                } else {
                    vo.setVideoUrl(fileService.getAbsoluteR2Url(vo.getVideoUrl()));
                }
            }
            detailVO.setClasses(classes);
            //query teacher
            Optional.ofNullable(projCollectionTeacherPubService.getOneByVersion(detailVO.getTeacherId(), versionInfoBO.getCurrentVersion())).ifPresent(teacher -> {
                ProjCollectionClassDetailTeacherVO projCollectionClassDetailTeacherVO = new ProjCollectionClassDetailTeacherVO();
                BeanUtils.copyProperties(teacher, projCollectionClassDetailTeacherVO);
                projCollectionClassDetailTeacherVO.setAvatar(teacher.getAvatarUrl());
                detailVO.setTeacher(projCollectionClassDetailTeacherVO);
            });
        }

        return detailVO;
    }

}
