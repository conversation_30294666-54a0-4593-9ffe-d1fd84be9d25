package com.laien.cmsapp.oog200.entity;

import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2024/12/17 16:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaProgramLevelRelation对象", description="proj yoga program level relation")
public class ProjYogaProgramLevelRelationPub extends BaseModel {

    private Integer version;

    private Integer projYogaProgramLevelId;

    private Integer projYogaRegularWorkoutId;

    private YogaAutoWorkoutTemplateEnum videoType;

    private Integer projId;

}
