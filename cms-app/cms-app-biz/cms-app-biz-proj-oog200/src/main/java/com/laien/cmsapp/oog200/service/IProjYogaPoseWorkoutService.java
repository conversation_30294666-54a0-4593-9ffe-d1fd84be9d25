package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseWorkoutPub;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutListVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj yoga pose workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
public interface IProjYogaPoseWorkoutService extends IService<ProjYogaPoseWorkoutPub> {

    List<ProjYogaPoseWorkoutListVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang);

    ProjYogaPoseWorkoutDetailVO findDetail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type, String lang);

    List<ProjYogaPoseWorkoutPub> listByPoseVideoIds(ProjPublishCurrentVersionInfoBO versionInfoBO, List<Integer> poseVideoIds);

    List<ProjYogaPoseWorkoutDetailVO> findByYogaPoseGroupId(Set<Integer> yogaPoseGroupIdSet, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type);
}
