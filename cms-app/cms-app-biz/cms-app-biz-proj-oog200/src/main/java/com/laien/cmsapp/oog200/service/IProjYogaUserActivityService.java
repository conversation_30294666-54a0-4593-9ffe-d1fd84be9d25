package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjYogaUserActivity;
import com.laien.cmsapp.oog200.response.ProjYogaUserInviteResultVO;

import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/6
 */
public interface IProjYogaUserActivityService extends IService<ProjYogaUserActivity> {

    ProjYogaUserInviteResultVO getByUserId(Integer userId);

    List<ProjYogaUserActivity> listByUserIds(Collection<Integer> userIds);

}
