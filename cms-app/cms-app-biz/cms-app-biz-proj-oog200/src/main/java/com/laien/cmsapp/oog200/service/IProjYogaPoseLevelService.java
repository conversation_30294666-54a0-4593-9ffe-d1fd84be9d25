package com.laien.cmsapp.oog200.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseLevelPub;
import com.laien.cmsapp.oog200.requst.ProjYogaPoseLevelListReq;
import com.laien.cmsapp.oog200.response.ProjYogaPoseLevelDetailVO;

/**
 * <p>
 * proj yoga pose grouping 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
public interface IProjYogaPoseLevelService extends IService<ProjYogaPoseLevelPub> {

    ProjYogaPoseLevelDetailVO findDetail(ProjYogaPoseLevelListReq listReq, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type);

}
