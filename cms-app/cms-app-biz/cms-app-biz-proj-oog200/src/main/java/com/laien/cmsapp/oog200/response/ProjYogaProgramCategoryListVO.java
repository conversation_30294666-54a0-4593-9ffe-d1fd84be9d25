package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:57
 */
@Data
public class ProjYogaProgramCategoryListVO {

    private Integer id;

    private String name;

    private String eventName;

    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "101,BROWSE_BY_GOAL; 201,BROWSE_BY_STYLE; 301,FEATURED; 401,BEGINNER_PATH")
    private Integer categoryTypeCode;

    @ApiModelProperty(value = "包含的program 数据列表")
    private List<ProjYogaProgramListVO> programList;

}
