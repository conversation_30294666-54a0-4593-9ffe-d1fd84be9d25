package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjFastingArticlePub;
import com.laien.cmsapp.oog200.response.ProjFastingArticleDetailVO;
import com.laien.cmsapp.oog200.response.ProjFastingArticleListVO;

import java.util.List;

/**
 * <p>
 * Fasting article 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface IProjFastingArticlePubService extends IService<ProjFastingArticlePub> {

    List<ProjFastingArticleListVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO);

    ProjFastingArticleDetailVO find(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO);
}
