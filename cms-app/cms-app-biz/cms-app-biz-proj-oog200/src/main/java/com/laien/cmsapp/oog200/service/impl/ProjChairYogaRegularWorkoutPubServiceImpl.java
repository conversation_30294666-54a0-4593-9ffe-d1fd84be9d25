package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.WorkoutCategoryRelationBO;
import com.laien.cmsapp.oog200.entity.ProjChairYogaRegularWorkoutPub;
import com.laien.cmsapp.oog200.mapper.ProjChairYogaRegularWorkoutPubMapper;
import com.laien.cmsapp.oog200.properties.Oog200Properties;
import com.laien.cmsapp.oog200.response.*;
import com.laien.cmsapp.oog200.service.IProjChairYogaRegularWorkoutPubService;
import com.laien.cmsapp.oog200.service.IProjChairYogaVideoService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularCategoryService;
import com.laien.cmsapp.oog200.util.YogaRegularWorkoutUtil;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.common.oog200.enums.YogaTypeEnum;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author:  hhl
 * Date:  2024/11/7 16:10
 */
@Slf4j
@Service
public class ProjChairYogaRegularWorkoutPubServiceImpl extends ServiceImpl<ProjChairYogaRegularWorkoutPubMapper, ProjChairYogaRegularWorkoutPub> implements IProjChairYogaRegularWorkoutPubService {

    @Resource
    private ProjChairYogaRegularWorkoutPubMapper workoutPubMapper;

    @Resource
    private IProjChairYogaVideoService chairYogaVideoService;

    @Resource
    private Oog200Properties oog200Properties;

    @Resource
    private IProjYogaRegularCategoryService projYogaRegularCategoryService;

    @Resource
    I18nUtil i18nUtil;

    @Override
    public List<ProjYogaRegularWorkoutListVO> listYogaRegularWorkout(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang, Set<Integer> categoryCodeSet) {

        List<WorkoutCategoryRelationBO> workoutCategoryRelationList = projYogaRegularCategoryService.findWorkoutCategoryRelationList(categoryCodeSet, YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, versionInfoBO);
        if (CollUtil.isEmpty(workoutCategoryRelationList) && CollUtil.isNotEmpty(categoryCodeSet)) {
            log.error("list chair yoga regular workout not found");
            return new ArrayList<>();
        }
        Map<Integer, WorkoutCategoryRelationBO> workoutCategoryRelationMap = workoutCategoryRelationList.stream().collect(Collectors.toMap(WorkoutCategoryRelationBO::getWorkoutId, o-> o));

        LambdaQueryWrapper<ProjChairYogaRegularWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(categoryCodeSet), BaseModel::getId, workoutCategoryRelationMap.keySet())
                .eq(ProjChairYogaRegularWorkoutPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjChairYogaRegularWorkoutPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjChairYogaRegularWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .orderByDesc(ProjChairYogaRegularWorkoutPub::getId);

        List<ProjChairYogaRegularWorkoutPub> regularWorkoutPubList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(regularWorkoutPubList)) {
            return Collections.emptyList();
        }

        Map<Integer, Integer> workoutIdAndDifficultyCodeMap = getWorkoutIdAndDifficultyCodeMap(regularWorkoutPubList);
        List<ProjYogaRegularWorkoutListVO> workoutListVOList =  regularWorkoutPubList.stream().map(pub -> {
            ProjYogaRegularWorkoutListVO vo = new ProjYogaRegularWorkoutListVO();
            BeanUtils.copyProperties(pub, vo);
            WorkoutCategoryRelationBO workoutCategoryRelation = workoutCategoryRelationMap.get(pub.getId());
            if(null != workoutCategoryRelation) {
                Set<Integer> codeSet = workoutCategoryRelation.getCategoryCodeSet();
                if (CollUtil.isNotEmpty(codeSet)) {
                    vo.setCategoryCodeList(new ArrayList<>(codeSet));
                }
            }
            vo.setDifficultyCode(workoutIdAndDifficultyCodeMap.get(pub.getId()));
            vo.setDuration(TimeConvertUtil.millisToMinutes(pub.getDuration()));
            vo.setWorkoutTypeCode(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getCode());
            // 设置yoga type
            if(StringUtils.isNotBlank(pub.getYogaType())){
                List<Integer> yogaTypeCodeList = Stream.of(pub.getYogaType().split(GlobalConstant.COMMA)).map(YogaTypeEnum::getCodeByName).filter(Objects::nonNull).collect(Collectors.toList());
                vo.setYogaTypeCodeList(yogaTypeCodeList);
            }
            return vo;
        }).collect(Collectors.toList());

        // 翻译i18n字段
        i18nUtil.translate(workoutListVOList, ProjCodeEnums.OOG200, lang);
        return workoutListVOList;
    }

    private Map<Integer, Integer> getWorkoutIdAndDifficultyCodeMap(List<ProjChairYogaRegularWorkoutPub> regularWorkoutPubList) {

        if (CollectionUtils.isEmpty(regularWorkoutPubList)) {
            return Collections.emptyMap();
        }

        return regularWorkoutPubList.stream().filter(workoutPub -> Objects.nonNull(DifficultyEnum.getByName(workoutPub.getDifficulty())))
                .collect(Collectors.toMap(ProjChairYogaRegularWorkoutPub::getId, workoutPub -> DifficultyEnum.getByName(workoutPub.getDifficulty()).getCode()));
    }

    @Override
    public ProjYogaRegularWorkoutDetailVO findDetailById(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer workoutId, Integer m3u8Type, String lang) {

        LambdaQueryWrapper<ProjChairYogaRegularWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjChairYogaRegularWorkoutPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjChairYogaRegularWorkoutPub::getId, workoutId);
        ProjChairYogaRegularWorkoutPub yogaRegularWorkoutFind = this.getOne(queryWrapper);

        if (Objects.isNull(yogaRegularWorkoutFind)) {
            return null;
        }

        // 翻译i18n字段
        i18nUtil.translate(Lists.newArrayList(yogaRegularWorkoutFind), ProjCodeEnums.OOG200, lang);
        Map<Integer, Integer> workoutAndDifficultyMap = getWorkoutIdAndDifficultyCodeMap(Lists.newArrayList(yogaRegularWorkoutFind));
        ProjYogaRegularWorkoutDetailVO detailVO = convertEntity2Detail(yogaRegularWorkoutFind, m3u8Type, workoutAndDifficultyMap);

        // 获取video列表
        List<ResYogaVideoDetailVO> videoList = chairYogaVideoService.listVideo4ResAndRegularWorkout(Lists.newArrayList(yogaRegularWorkoutFind.getId()));
        detailVO.setVideos(videoList);
        return detailVO;
    }

    @Override
    public List<ProjYogaRegularWorkoutDetailVO> listByIdsAndStatus(Set<Integer> workoutIds, String language, Integer status, Integer m3u8Type, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return Collections.emptyList();
        }

        // 获取指定的workout list
        LambdaQueryWrapper<ProjChairYogaRegularWorkoutPub> queryWrapper = wrapQueryByIdsAndStatus(versionInfoBO, status, workoutIds);
        List<ProjChairYogaRegularWorkoutPub> regularWorkouts = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(regularWorkouts)) {
            return Collections.emptyList();
        }

        // 封装返回结果
        Map<Integer, Integer> workoutIdAndDifficultyCodeMap = getWorkoutIdAndDifficultyCodeMap(regularWorkouts);
        List<ProjYogaRegularWorkoutDetailVO> resultList = regularWorkouts.stream().map(workout -> convertEntity2Detail(workout, m3u8Type, workoutIdAndDifficultyCodeMap)).collect(Collectors.toList());

        // 获取workout对应的video list
        Set<Integer> idList = regularWorkouts.stream().map(ProjChairYogaRegularWorkoutPub::getId).collect(Collectors.toSet());
        List<ResYogaVideoDetailVO> yogaVideoDetailVOList = chairYogaVideoService.listVideo4ResAndRegularWorkout(idList);
        if (CollectionUtils.isEmpty(yogaVideoDetailVOList)) {
            return resultList;
        }

        Map<Integer, List<ResYogaVideoDetailVO>> workoutIdAndVideoDetailMap = yogaVideoDetailVOList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));
        resultList.forEach(result -> {
            if (workoutIdAndVideoDetailMap.containsKey(result.getId())) {
                List<ResYogaVideoDetailVO> videoDetailVOList = workoutIdAndVideoDetailMap.get(result.getId());
                videoDetailVOList.sort(Comparator.comparing(ResYogaVideoDetailVO::getSortId));
                result.setVideos(videoDetailVOList);
            }
        });

        i18nUtil.translate(resultList, ProjCodeEnums.OOG200, language);
        return resultList;
    }

    private ProjYogaRegularWorkoutDetailVO convertEntity2Detail(ProjChairYogaRegularWorkoutPub workoutPub, Integer m3u8Type, Map<Integer, Integer> workoutAndDifficultyMap) {

        ProjYogaRegularWorkoutDetailVO detailVO = new ProjYogaRegularWorkoutDetailVO();
        BeanUtils.copyProperties(workoutPub, detailVO);
        detailVO.setDifficultyCode(workoutAndDifficultyMap.get(workoutPub.getId()));

        detailVO.setDuration4Millis(workoutPub.getDuration());
        detailVO.setDuration(TimeConvertUtil.millisToMinutes(workoutPub.getDuration()));
        detailVO.setWorkoutTypeCode(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getCode());

        YogaRegularWorkoutUtil.setVideoUrl(detailVO, m3u8Type);
        return detailVO;
    }

    private LambdaQueryWrapper<ProjChairYogaRegularWorkoutPub> wrapQueryByIdsAndStatus(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, Set<Integer> workoutIds) {

        LambdaQueryWrapper<ProjChairYogaRegularWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjChairYogaRegularWorkoutPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.in(ProjChairYogaRegularWorkoutPub::getId, workoutIds);

        if (Objects.nonNull(status)) {
            queryWrapper.eq(ProjChairYogaRegularWorkoutPub::getStatus, status);
        }
        return queryWrapper;
    }

    @Override
    public List<ProjWorkoutDetailVO116> listWorkoutFor116(String language, Integer m3u8Type, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        List<Integer> configChairWorkoutIds = oog200Properties.getChairYogaWorkoutIdList();
        if (CollectionUtils.isEmpty(configChairWorkoutIds)) {
            return Collections.emptyList();
        }

        Set<Integer> workoutIds = configChairWorkoutIds.stream().collect(Collectors.toSet());
        List<ProjYogaRegularWorkoutDetailVO> workout200DetailVOList = listByIdsAndStatus(workoutIds, language, null, m3u8Type, versionInfoBO);
        if (CollectionUtils.isEmpty(workout200DetailVOList)) {
            return Collections.emptyList();
        }

        Integer playlistId = oog200Properties.getPlaylistId4ChairWorkout();
        Map<Integer, ProjYogaRegularWorkoutDetailVO> workoutIdAndDetailMap = workout200DetailVOList.stream().collect(Collectors.toMap(detail -> detail.getId(), Function.identity(), (k1, k2) -> k1));
        List<ProjWorkoutDetailVO116> workout116DetailVOS = configChairWorkoutIds.stream().filter(workoutIdAndDetailMap::containsKey).map(workoutId -> convertToWorkout116DetailVO(workoutIdAndDetailMap.get(workoutId), playlistId)).collect(Collectors.toList());
        return workout116DetailVOS;
    }

    private ProjWorkoutDetailVO116 convertToWorkout116DetailVO(ProjYogaRegularWorkoutDetailVO workout200DetailVO, Integer playlistId) {

        ProjWorkoutDetailVO116 projWorkout116DetailVO = new ProjWorkoutDetailVO116();
        BeanUtils.copyProperties(workout200DetailVO, projWorkout116DetailVO);
        projWorkout116DetailVO.setSubscription(integerToBoolean(workout200DetailVO.getSubscription()));
        projWorkout116DetailVO.setDuration(workout200DetailVO.getDuration4Millis());
        projWorkout116DetailVO.setVideoUrl(workout200DetailVO.getVideoM3u8Url());
        projWorkout116DetailVO.setPlaylistId(playlistId);

        List<ProjWorkoutAudioDetailVO116> audioList = Lists.newArrayList(new ProjWorkoutAudioDetailVO116(GlobalConstant.DEFAULT_LANGUAGE, workout200DetailVO.getAudioLongJson()));
        projWorkout116DetailVO.setAudioList(audioList);
        return projWorkout116DetailVO;
    }

    private boolean integerToBoolean(Integer num) {
        return num != null && num != 0;
    }
}
