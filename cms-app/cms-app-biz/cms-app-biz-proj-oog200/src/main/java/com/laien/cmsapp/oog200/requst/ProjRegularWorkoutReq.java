package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yogaAutoWorkout id 列表查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "autoWorkout id 列表查询", description = "autoWorkout id 列表查询")
public class ProjRegularWorkoutReq {

    @ApiModelProperty(value = "workoutId",example = "0")
    private Integer id;

    @ApiModelProperty(value = "workTypeCode, 0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private Integer code;

}
