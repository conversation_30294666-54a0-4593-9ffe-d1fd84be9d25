package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Author:  hhl
 * Date:  2025/1/7 11:32
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjIngredient对象", description="ingredient,配料")
public class ProjIngredientVO {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "数量")
    private String amount;

    @JsonIgnore
    @ApiModelProperty(value = "proj_unit表数据id")
    private Integer projUnitId;

    @ApiModelProperty(value = "单位名")
    private String unitName;

}
