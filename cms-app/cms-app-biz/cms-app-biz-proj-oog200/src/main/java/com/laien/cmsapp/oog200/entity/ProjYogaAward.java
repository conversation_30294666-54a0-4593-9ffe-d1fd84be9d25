package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * @author: hhl
 * @date: 2025/6/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_award")
@ApiModel(value="ProjYogaAward对象", description="yoga award")
public class ProjYogaAward extends BaseModel {

    @ApiModelProperty(value = "产品Id")
    private String productId;

    @ApiModelProperty(value = "兑换链接")
    private String awardLink;

    @ApiModelProperty(value = "兑换截止时间")
    private LocalDateTime expiredTime;

    @ApiModelProperty(value = "持续时间，以月为单位")
    private Integer duration;

    @ApiModelProperty(value = "0表示未使用，1表示已使用")
    private Integer useFlag;

    @ApiModelProperty(value = "项目Id")
    private Integer projId;

}
