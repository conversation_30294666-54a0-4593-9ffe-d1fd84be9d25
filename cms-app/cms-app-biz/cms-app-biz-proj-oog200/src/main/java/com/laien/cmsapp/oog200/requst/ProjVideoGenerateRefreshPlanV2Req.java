package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: video plan 更新v1
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video plan 更新v2", description = "video plan 更新v2")
public class ProjVideoGenerateRefreshPlanV2Req {

    @ApiModelProperty(value = "id list", required = true)
    private List<Integer> idList;

    @ApiModelProperty(value = "difficulty 为空或不存在的值默认为Newbie")
    private String difficulty;

    @ApiModelProperty(value = "Default | Least ,不填则查全部")
    private String introType;

}
