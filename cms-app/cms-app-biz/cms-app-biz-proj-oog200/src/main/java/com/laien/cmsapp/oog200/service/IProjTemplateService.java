package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjTemplatePub;

import java.util.List;

/**
 * <p>
 * template 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-26
 */
public interface IProjTemplateService extends IService<ProjTemplatePub> {

    /**
     * 查询启用的template 列表
     *
     * @param duration duration
     * @return
     */
    List<Integer> selectTemplateIds(Integer duration);

    /**
     * 根据时长区间查询启用的template 列表
     *
     * @param beginDuration beginDuration
     * @param endDuration endDuration
     * @return list
     */
    List<Integer> selectTemplateIdsByDuration(Integer beginDuration,Integer endDuration);

}
