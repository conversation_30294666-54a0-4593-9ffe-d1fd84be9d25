package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutTemplatePub;
import com.laien.cmsapp.oog200.mapper.ProjYogaAutoWorkoutTemplatePubMapper;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutTemplatePubService;
import com.laien.common.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * yoga auto workout生成模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Service
public class ProjYogaAutoWorkoutTemplatePubServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutTemplatePubMapper, ProjYogaAutoWorkoutTemplatePub> implements IProjYogaAutoWorkoutTemplatePubService {

    @Override
    public List<ProjYogaAutoWorkoutTemplatePub> list(String type, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutTemplatePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaAutoWorkoutTemplatePub::getType, type)
                .eq(ProjYogaAutoWorkoutTemplatePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaAutoWorkoutTemplatePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjYogaAutoWorkoutTemplatePub::getVersion, versionInfoBO.getCurrentVersion());
        return baseMapper.selectList(wrapper);
    }
}
