package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaProgramPubMapper;
import com.laien.cmsapp.oog200.mapper.ProjYogaProgramTypeRelationPubMapper;
import com.laien.cmsapp.oog200.response.ProjYogaProgramListVO;
import com.laien.cmsapp.oog200.response.ProjYogaProgramTypeRelationListVO;
import com.laien.cmsapp.oog200.service.IProjYogaProgramPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/24 20:07
 */
@Slf4j
@Service
public class ProjYogaProgramPubServiceImpl extends ServiceImpl<ProjYogaProgramPubMapper, ProjYogaProgramPub> implements IProjYogaProgramPubService {

    @Resource
    private I18nUtil i18nUtil;

    @Resource
    private ProjYogaProgramTypeRelationPubMapper programTypeRelationPubMapper;

    @Override
    public List<ProjYogaProgramListVO> listVOByIds(Collection<Integer> programIds, Integer version, Integer status, String lang) {

        List<ProjYogaProgramPub> programPubList = listByStatusAndVersion(programIds, status, version);
        if (CollectionUtils.isEmpty(programPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(programPubList, ProjCodeEnums.OOG200, lang);
        List<ProjYogaProgramListVO> programVOList = programPubList.stream().map(this::convert2ListVO).collect(Collectors.toList());
        setType4Program(programVOList, version);
        return programVOList;
    }

    private void setType4Program(Collection<ProjYogaProgramListVO> programPubList, Integer version) {

        List<Integer> programIds = programPubList.stream().map(ProjYogaProgramListVO::getId).collect(Collectors.toList());
        List<ProjYogaProgramTypeRelationListVO> programTypeRelationPubs = programTypeRelationPubMapper.listByProgramIdAndVersion(programIds, version);
        if (CollectionUtils.isEmpty(programTypeRelationPubs)) {
            return;
        }

        Map<Integer, Set<Integer>> programAndTypeCodeMap = programTypeRelationPubs.stream().collect(Collectors.groupingBy(ProjYogaProgramTypeRelationListVO::getProjYogaProgramId, Collectors.mapping(ProjYogaProgramTypeRelationListVO::getProjYogaProgramTypeCode, Collectors.toSet())));
        programPubList.stream().filter(program -> programAndTypeCodeMap.containsKey(program.getId())).forEach(program -> program.setProgramTypeCode(programAndTypeCodeMap.get(program.getId())));
    }

    private ProjYogaProgramListVO convert2ListVO(ProjYogaProgramPub projYogaProgramPub) {

        ProjYogaProgramListVO listVO = new ProjYogaProgramListVO();
        BeanUtils.copyProperties(projYogaProgramPub, listVO);
        listVO.setDifficultyCode(projYogaProgramPub.getDifficulty().getCode());
        return listVO;
    }

    private List<ProjYogaProgramPub> listByStatusAndVersion(Collection<Integer> programIds, Integer status, Integer version) {

        LambdaQueryWrapper<ProjYogaProgramPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(programIds), ProjYogaProgramPub::getId, programIds);
        queryWrapper.eq(Objects.nonNull(status), ProjYogaProgramPub::getStatus, status);
        queryWrapper.eq(ProjYogaProgramPub::getVersion, version);
        return list(queryWrapper);
    }

}
