package com.laien.cmsapp.oog200.service.impl;


import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjSound;
import com.laien.cmsapp.oog200.mapper.ProjSoundMapper;
import com.laien.cmsapp.oog200.requst.ProjSoundReq;
import com.laien.cmsapp.oog200.response.ProjSoundVO;
import com.laien.cmsapp.oog200.service.IProjSoundService;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.GenderEnums;
import com.laien.common.util.RequestContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjSoundServiceImpl extends ServiceImpl<ProjSoundMapper, ProjSound>
        implements IProjSoundService {

    @Autowired
    private final I18nUtil i18nUtil;

    @Autowired
    private final FileService fileService;

    /**
     * 按照类型，子类型 , 性别查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    @Override
    public List<ProjSoundVO> selectSoundList(ProjSoundReq soundReq) {
        if (Objects.isNull(soundReq.getSoundType())) {
            log.error("The soundReq is null");
            return new ArrayList<>();
        }
        //兜底没有传 gender 时,默认给 female
        if (Objects.isNull(soundReq.getGender())) {
            soundReq.setGender(GenderEnums.FEMALE);
        }

        LambdaQueryWrapper<ProjSound> query = new LambdaQueryWrapper<>();
        query.eq(ObjUtil.isNotNull(soundReq.getSoundType()), ProjSound::getSoundType, soundReq.getSoundType())
                .eq(ObjUtil.isNotNull(soundReq.getSoundSubType()), ProjSound::getSoundSubType, soundReq.getSoundSubType())
                .eq(ObjUtil.isNotNull(soundReq.getGender()), ProjSound::getGender, soundReq.getGender())
                .eq(ProjSound::getDelFlag, 0)
                .eq(ProjSound::getStatus, 1);
        query.orderByDesc(ProjSound::getId);
        List<ProjSound> soundList = baseMapper.selectList(query);
        if (soundList == null || soundList.isEmpty()) {
            log.error("The soundList is null");
            return Collections.emptyList();
        }

        String lang = RequestContextUtils.getLanguage();
        List<ProjSound> needI18nList = soundList.stream().filter(ProjSound::getNeedTranslation).collect(Collectors.toList());
        i18nUtil.translate4Speech(needI18nList, ProjCodeEnums.OOG200, lang);

        List<ProjSoundVO> result = soundList.stream().map(sound -> new ProjSoundVO(sound)).collect(Collectors.toList());
        return result;
    }

}
