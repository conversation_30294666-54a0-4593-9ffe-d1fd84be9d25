package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * video generate
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjVideoGenerate对象", description="video generate")
public class ProjVideoGenerate extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "template id")
    private Integer templateId;

    @ApiModelProperty(value = "task id")
    private Integer taskId;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "真实时长")
    private Integer realDuration;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "机位1视频（m3u8文件）")
    private String video1Url;

    @ApiModelProperty(value = "机位1视频和详细指导音频（m3u8文件）")
    private String video1DefaultUrl;

    @ApiModelProperty(value = "机位1视频和简略指导音频（m3u8文件）")
    private String video1LeastUrl;

    @ApiModelProperty(value = "机位2视频（m3u8文件）")
    private String video2Url;

    @ApiModelProperty(value = "机位2视频和详细指导音频（m3u8文件）")
    private String video2DefaultUrl;

    @ApiModelProperty(value = "机位2视频和简略指导音频（m3u8文件）")
    private String video2LeastUrl;

    @ApiModelProperty(value = "视频标题字幕（srt文件）")
    private String titleSubtitleUrl;

    @ApiModelProperty(value = "视频详细指导字幕url（srt文件）")
    private String guidanceDefaultUrl;

    @ApiModelProperty(value = "视频简略指导字幕url（srt文件）")
    private String guidanceLeastUrl;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "数据版本")
    private Integer dataVersion;


}
