package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjUnitPub;
import com.laien.cmsapp.oog200.mapper.ProjUnitPubMapper;
import com.laien.cmsapp.oog200.service.IProjUnitPubService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/8 16:08
 */
@Service
public class ProjUnitPubServiceImpl extends ServiceImpl<ProjUnitPubMapper, ProjUnitPub> implements IProjUnitPubService {

    @Override
    public List<ProjUnitPub> listByVersionAndIds(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> ids) {

        LambdaQueryWrapper<ProjUnitPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjUnitPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjUnitPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.in(ProjUnitPub::getId, ids);
        return list(queryWrapper);
    }
}
