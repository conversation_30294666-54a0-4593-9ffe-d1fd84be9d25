package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: yogaAutoWorkout id 列表查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjYogaRegularWorkoutListReq 列表查询", description = "ProjYogaRegularWorkoutListReq 列表查询")
public class ProjYogaRegularWorkoutListReq {

    @ApiModelProperty(name = "yogaTypeCodeList", value = "0:Classic Yoga、1:Lazy Yoga、2:Somatic Yoga、3:Chair Yoga、4:Wall Pilates、5:Other")
    private List<Integer> yogaTypeCodeList;

    @ApiModelProperty(name = "specialLimitCodeList", value = "0: All Good,1: Sensitive Wrist,2: Back Pain,3: Knee Issues,4: Overweight,5: Elderly No,6: No plank,7: Pregnancy or postpartum ")
    private List<Integer> specialLimitCodeList;

}
