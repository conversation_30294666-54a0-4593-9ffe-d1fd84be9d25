package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseGroup对象", description="proj yoga pose grouping")
public class ProjYogaPoseGroup extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "eventName")
    private String eventName;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "分组用途，例如today pose、training path")
    private String type;

    @ApiModelProperty(value = "group 彩色封面")
    private String groupImgLightUrl;

    @ApiModelProperty(value = "group 黑色封面")
    private String groupImgDarkUrl;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "status")
    private Integer status;

}
