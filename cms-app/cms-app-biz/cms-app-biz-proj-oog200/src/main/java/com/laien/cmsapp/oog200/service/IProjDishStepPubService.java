package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjDishStepPub;
import com.laien.cmsapp.oog200.response.ProjDishStepVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:21
 */
public interface IProjDishStepPubService extends IService<ProjDishStepPub> {

    List<ProjDishStepVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang);

}
