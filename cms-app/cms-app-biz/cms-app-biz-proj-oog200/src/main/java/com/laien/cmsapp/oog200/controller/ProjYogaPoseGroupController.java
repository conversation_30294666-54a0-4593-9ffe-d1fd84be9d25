package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseGroupDetailVO;
import com.laien.cmsapp.oog200.service.IProjYogaPoseGroupService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * proj yoga pose grouping 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Api(tags = "app端：yogaPoseGroup")
@RestController
@RequestMapping(value = {"/oog200/yogaPoseGroup", "/OOG200/yogaPoseGroup"})
public class ProjYogaPoseGroupController extends ResponseController {

    @Resource
    private IProjYogaPoseGroupService projYogaPoseGroupService;

    @ApiOperation(value = "list", tags = {"oog200"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjYogaPoseGroupDetailVO>> list(@RequestParam(required = false, defaultValue = "1") Integer m3u8Type,
                                                                @RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projYogaPoseGroupService.list(m3u8Type, versionInfoBO, lang));
    }

}
