package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutAudioI18nPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaRegularWorkoutAudioI18nPubMapper;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutGuidanceItemVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutGuidanceVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.service.IProjYogaRegularWorkoutAudioI18nPubService;
import com.laien.common.constant.GlobalConstant;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/11/25 15:11
 */
@Service
public class IProjYogaRegularWorkoutAudioI18nPubServiceImpl extends ServiceImpl<ProjYogaRegularWorkoutAudioI18nPubMapper, ProjYogaRegularWorkoutAudioI18nPub> implements IProjYogaRegularWorkoutAudioI18nPubService {

    @Override
    public void setAudioI18n4Workout(List<ProjYogaRegularWorkoutDetailVO> workoutList, ProjPublishCurrentVersionInfoBO versionInfo) {

        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        Map<Integer, List<ProjYogaRegularWorkoutDetailVO>> workoutTypeGroup = workoutList.stream().collect(Collectors.groupingBy(ProjYogaRegularWorkoutDetailVO::getWorkoutTypeCode));
        workoutTypeGroup.entrySet().forEach(entry -> setAudioI18n4WorkoutByType(entry.getValue(), entry.getKey(), versionInfo.getCurrentVersion()));
    }

    private void setDefaultGuidance(List<ProjYogaRegularWorkoutDetailVO> workoutList) {

        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        for (ProjYogaRegularWorkoutDetailVO workoutDetailVO : workoutList) {
            ProjYogaAutoWorkoutGuidanceVO guidanceVO = ProjYogaAutoWorkoutGuidanceVO.buildGuidance(workoutDetailVO.getAudioLongJson(), workoutDetailVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE);
            workoutDetailVO.setGuidance(guidanceVO);
        }
    }

    private void setAudioI18n4WorkoutByType(List<ProjYogaRegularWorkoutDetailVO> workoutList, Integer workoutType, Integer version) {

        Set<Integer> workoutIds = workoutList.stream().map(ProjYogaRegularWorkoutDetailVO::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<ProjYogaRegularWorkoutAudioI18nPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaRegularWorkoutAudioI18nPub::getWorkoutId, workoutIds);
        queryWrapper.eq(ProjYogaRegularWorkoutAudioI18nPub::getVersion, version);
        queryWrapper.eq(ProjYogaRegularWorkoutAudioI18nPub::getWorkoutType, workoutType);

        List<ProjYogaRegularWorkoutAudioI18nPub> workoutAudioI18nList = list(queryWrapper);
        if (CollectionUtils.isEmpty(workoutAudioI18nList)) {
            setDefaultGuidance(workoutList);
            return;
        }

        Map<Integer, List<ProjYogaRegularWorkoutAudioI18nPub>> workoutAudioI18nMap = workoutAudioI18nList.stream().collect(Collectors.groupingBy(ProjYogaRegularWorkoutAudioI18nPub::getWorkoutId));
        for (ProjYogaRegularWorkoutDetailVO workoutListVO : workoutList) {

            if (!workoutAudioI18nMap.containsKey(workoutListVO.getId())) {
                ProjYogaAutoWorkoutGuidanceVO guidanceVO = ProjYogaAutoWorkoutGuidanceVO.buildGuidance(workoutListVO.getAudioLongJson(), workoutListVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE);
                workoutListVO.setGuidance(guidanceVO);
                continue;
            }

            // 兼容安卓，将en排在最前面
            Function<ProjYogaRegularWorkoutAudioI18nPub, Integer> audioSort = workout -> {
                if (Objects.equals(workout.getLanguage(), GlobalConstant.DEFAULT_LANGUAGE)) {
                    return GlobalConstant.ZERO;
                } else {
                    return GlobalConstant.ONE;
                }
            };

            List<ProjYogaRegularWorkoutAudioI18nPub> workoutAudioI18nPubs = workoutAudioI18nMap.get(workoutListVO.getId());
            List<ProjYogaAutoWorkoutGuidanceItemVO> longAudioItems = workoutAudioI18nPubs.stream().filter(audio -> !StringUtils.isEmpty(audio.getAudioLongJsonUrl())).sorted(Comparator.comparing(audioSort)).map(audio -> ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(audio.getAudioLongJsonUrl(), audio.getLanguage())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(longAudioItems)) {
                longAudioItems = Lists.newArrayList(ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(workoutListVO.getAudioLongJson(), GlobalConstant.DEFAULT_LANGUAGE));
            }

            List<ProjYogaAutoWorkoutGuidanceItemVO> shortAudioItems = workoutAudioI18nPubs.stream().filter(audio -> !StringUtils.isEmpty(audio.getAudioShortJsonUrl())).sorted(Comparator.comparing(audioSort)).map(audio -> ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(audio.getAudioShortJsonUrl(), audio.getLanguage())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shortAudioItems)) {
                shortAudioItems = Lists.newArrayList(ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(workoutListVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE));
            }

            ProjYogaAutoWorkoutGuidanceVO guidanceVO = new ProjYogaAutoWorkoutGuidanceVO();
            guidanceVO.setDefault_(longAudioItems);
            guidanceVO.setLeast(shortAudioItems);
            workoutListVO.setGuidance(guidanceVO);
        }
    }
}
