package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yogaAutoWorkout id 列表查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "autoWorkout id 列表查询", description = "autoWorkout id 列表查询")
public class ProjAutoWorkoutReq {

    @ApiModelProperty(value = "id",example = "0")
    private Integer id;

    @ApiModelProperty(value = "0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private Integer planTypeCode;

    @ApiModelProperty(value = "infoId")
    private Integer infoId;
}
