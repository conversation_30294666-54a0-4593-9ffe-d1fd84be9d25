package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjDishCollectionRelationPub;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:48
 */
public interface ProjDishCollectionRelationPubMapper extends BaseMapper<ProjDishCollectionRelationPub> {

    @Select(value = "select id, version, proj_dish_collection_id, dish_type, proj_dish_id from proj_dish_collection_relation_pub where version = #{version} and proj_dish_collection_id = #{dishCollectionId} and del_flag = 0")
    List<ProjDishCollectionRelationPub> listByVersionAndDishCollectionId(Integer version, Integer dishCollectionId);


}
