package com.laien.cmsapp.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.DurationEnum;
import com.laien.cmsapp.oog200.requst.*;
import com.laien.cmsapp.oog200.service.*;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutListVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutRefreshVO;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum.*;

/**
 * <p>
 * oog200 workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Slf4j
@Api(tags = "app端：yogaAutoWorkout")
@RestController
@RequestMapping("/{appCode}/yogaAutoWorkout")
public class ProjYogaAutoWorkoutController extends ResponseController {

    @Resource
    private IProjYogaAutoWorkoutService projYogaAutoWorkoutService;
    @Resource
    private IProjWallPilatesAutoWorkoutService projWallPilatesAutoWorkoutService;
    @Resource
    private IProjChairYogaAutoWorkoutService projChairYogaAutoWorkoutService;
    @Resource
    private IProjYogaAutoWorkoutAudioI18nService workoutAudioI18nService;

    @Resource
    private IProjAutoWorkoutBasicInfoService projAutoWorkoutBasicInfoService;

    /**
     * 获取plan
     *
     * @param planReq planReq
     * @return workout列表
     */
    @ApiOperation(value = "yogaAutoWorkout plan v1", tags = {"oog200"})
    @GetMapping("/v1/plan")
    public ResponseResult<List<ProjYogaAutoWorkoutListVO>> plan(ProjYogaAutoWorkoutPlanReq planReq) {

        List<ProjYogaAutoWorkoutListVO> workoutListVOList = projYogaAutoWorkoutService.getPlan(planReq);
        return succ(workoutListVOList);
    }

    /**
     * 根据id列表查询
     */
    @ApiOperation(value = "yogaAutoWorkout 列表v1", tags = {"oog200"})
    @GetMapping("/v1/listByIds")
    public ResponseResult<List<ProjYogaAutoWorkoutRefreshVO>> listByIds(ProjYogaAutoWorkoutListReq workoutListReq) {
        return succ(projYogaAutoWorkoutService.findList(workoutListReq));
    }

    /**
     * 获取plan
     *
     * @param planReq planReq
     * @return workout列表
     */
    @ApiOperation(value = "yogaAutoWorkout plan v2", tags = {"oog200"})
    @GetMapping("/v2/plan")
    public ResponseResult<List<ProjPlanWorkoutListVO>> planV2(ProjWorkoutPlanReq planReq) {
        Integer planType = planReq.getPlanTypeCode();
        if (null == planType) {
            planReq.setPlanTypeCode(CLASSIC_YOGA.getCode());
            log.error("oog200 plan typeCode illegal, use default value 0, planReq: {}", planReq);
        }
        DurationEnum durationEnum = DurationEnum.getByCode(planReq.getDurationCode());
        if (null == durationEnum) {
            durationEnum = DurationEnum.MIN_0_10;
            planReq.setDurationCode(durationEnum.getCode());
            log.error("oog200 plan durationCode illegal, use default value 1, planReq: {}", planReq);
        }

        List<ProjPlanWorkoutListVO> workoutList = Lists.newArrayList();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        if (CLASSIC_YOGA.getCode().equals(planType) || LAZY_YOGA.getCode().equals(planType) || SOMATIC_YOGA.getCode().equals(planType)) {
            workoutList = projYogaAutoWorkoutService.getPlan(planReq, versionInfoBO);
        }
        if (WALL_PILATES.getCode().equals(planType)) {
            workoutList = projWallPilatesAutoWorkoutService.getPlan(planReq, versionInfoBO, RequestContextUtils.getLanguage());
        }

        if (CHAIR_YOGA.getCode().equals(planType)) {
            workoutList = projChairYogaAutoWorkoutService.getPlan(planReq, versionInfoBO);
        }

        workoutAudioI18nService.setAudioI18n4Workout(workoutList);
        return succ(workoutList);
    }

    /**
     * 获取plan
     *
     * @param planReq planReq
     * @return workout列表
     */
    @ApiOperation(value = "yogaAutoWorkout plan v3", notes = "在v2的基础上，chair yoga plan的生成由Target匹配修改为Difficulty匹配", tags = {"oog200"})
    @GetMapping("/v3/plan")
    public ResponseResult<List<ProjPlanWorkoutListVO>> planV3(ProjWorkoutPlanReq planReq) {

        Integer planType = planReq.getPlanTypeCode();
        if (null == planType) {
            planReq.setPlanTypeCode(CLASSIC_YOGA.getCode());
            log.error("oog200 plan typeCode illegal, use default value 0, planReq: {}", planReq);
        }

        DurationEnum durationEnum = DurationEnum.getByCode(planReq.getDurationCode());
        if (null == durationEnum) {
            durationEnum = DurationEnum.MIN_0_10;
            planReq.setDurationCode(durationEnum.getCode());
            log.error("oog200 plan durationCode illegal, use default value 1, planReq: {}", planReq);
        }

        List<ProjPlanWorkoutListVO> workoutList = Lists.newArrayList();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        if (CLASSIC_YOGA.getCode().equals(planType) || LAZY_YOGA.getCode().equals(planType) || SOMATIC_YOGA.getCode().equals(planType)) {
            workoutList = projYogaAutoWorkoutService.getPlan(planReq, versionInfoBO);
        }
        if (WALL_PILATES.getCode().equals(planType)) {
            workoutList = projWallPilatesAutoWorkoutService.getPlan(planReq, versionInfoBO, RequestContextUtils.getLanguage());
        }

        if (CHAIR_YOGA.getCode().equals(planType)) {
            workoutList = projChairYogaAutoWorkoutService.getPlanV2(planReq, versionInfoBO);
        }

        workoutAudioI18nService.setAudioI18n4Workout(workoutList);
        return succ(workoutList);
    }

    /**
     * 根据id列表查询
     */
    @ApiOperation(value = "yogaAutoWorkout 列表v2", tags = {"oog200"})
    @GetMapping("/v2/listByIds")
    public ResponseResult<List<ProjPlanWorkoutListVO>> listByIdsV2(ProjAutoWorkoutListReq workoutListReq) {
        List<ProjAutoWorkoutReq> workoutReqList = workoutListReq.getWorkoutReqList();
        if (CollUtil.isEmpty(workoutReqList)) {
            return succ(new ArrayList<>());
        }
        Integer m3u8Type = workoutListReq.getM3u8Type();
        // key：playTypeCode,value: workoutId
        Map<Integer, List<Integer>> workoutReqMap = new HashMap<>();
        for (ProjAutoWorkoutReq workoutReq : workoutReqList) {
            Integer planTypeCode = workoutReq.getPlanTypeCode();
            if (null == planTypeCode) {
                // 老接口只会返回classic yoga，返回结构没有playTypeCode,所以为null的当classic yoga处理
                planTypeCode = CLASSIC_YOGA.getCode();
            }
            List<Integer> workoutIdList = workoutReqMap.getOrDefault(planTypeCode, new ArrayList<>());
            workoutIdList.add(workoutReq.getId());
            workoutReqMap.put(planTypeCode, workoutIdList);
        }

        List<ProjPlanWorkoutListVO> workoutList = new ArrayList<>(workoutReqList.size());
        workoutList.addAll(getClassicYogaList(workoutListReq, workoutReqMap, m3u8Type, SOMATIC_YOGA.getCode()));
        workoutList.addAll(getClassicYogaList(workoutListReq, workoutReqMap, m3u8Type, CLASSIC_YOGA.getCode()));
        workoutList.addAll(getClassicYogaList(workoutListReq, workoutReqMap, m3u8Type, LAZY_YOGA.getCode()));
        String language = RequestContextUtils.getLanguage();
        workoutList.addAll(projWallPilatesAutoWorkoutService.query(workoutReqMap.get(WALL_PILATES.getCode()), m3u8Type, language));
        workoutList.addAll(projChairYogaAutoWorkoutService.query(workoutReqMap.get(CHAIR_YOGA.getCode()), m3u8Type, language));

        workoutAudioI18nService.setAudioI18n4Workout(workoutList);
        projAutoWorkoutBasicInfoService.setIntoWorkout(workoutList,workoutListReq);
        return succ(workoutList);
    }

    private List<ProjPlanWorkoutListVO> getClassicYogaList(ProjAutoWorkoutListReq workoutListReq, Map<Integer, List<Integer>> workoutReqMap, Integer m3u8Type, Integer planTypeCode) {
        ProjYogaAutoWorkoutListReq yogaAutoWorkoutListReq = new ProjYogaAutoWorkoutListReq();
        yogaAutoWorkoutListReq.setIdList(workoutReqMap.get(planTypeCode));
        yogaAutoWorkoutListReq.setLang(workoutListReq.getLang());
        yogaAutoWorkoutListReq.setM3u8Type(m3u8Type);
        DifficultyEnum difficultyEnum = DifficultyEnum.getByCode(workoutListReq.getDifficultyCode());
        if (null == difficultyEnum) {
            difficultyEnum = DifficultyEnum.NEWBIE;
        }
        yogaAutoWorkoutListReq.setDifficulty(difficultyEnum.getName());
        List<ProjPlanWorkoutListVO> workoutList = new ArrayList<>();
        workoutList.addAll(projYogaAutoWorkoutService.findListV2(yogaAutoWorkoutListReq, false, planTypeCode));
        return workoutList;
    }

}
