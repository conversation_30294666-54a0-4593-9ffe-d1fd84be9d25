package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjYogaAward;
import com.laien.cmsapp.oog200.mapper.ProjYogaAwardMapper;
import com.laien.cmsapp.oog200.service.IProjYogaAwardService;
import com.laien.common.constant.GlobalConstant;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Service
public class ProjYogaAwardServiceImpl extends ServiceImpl<ProjYogaAwardMapper, ProjYogaAward> implements IProjYogaAwardService {

    @Override
    public List<ProjYogaAward> listUnusedAward(String productId, int limit, LocalDateTime expireTime) {

        LambdaQueryWrapper<ProjYogaAward> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAward::getProductId, productId);
        queryWrapper.eq(ProjYogaAward::getUseFlag, GlobalConstant.ZERO);
        queryWrapper.ge(ProjYogaAward::getExpiredTime, expireTime);
        queryWrapper.last("limit " + limit);
        return list(queryWrapper);
    }

    @Override
    public void setUsed(Collection<Integer> ids) {

        LambdaUpdateWrapper<ProjYogaAward> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjYogaAward::getId, ids);
        updateWrapper.set(ProjYogaAward::getUseFlag, GlobalConstant.ONE);
        this.update(updateWrapper);
    }
}
