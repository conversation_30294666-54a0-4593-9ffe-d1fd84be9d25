package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaPoseWorkoutBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseWorkoutPub;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * proj yoga pose workout Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
public interface ProjYogaPoseWorkoutMapper extends BaseMapper<ProjYogaPoseWorkoutPub> {

    List<ProjYogaPoseWorkoutBO> findByYogaPoseGroupId(@Param("yogaPoseGroupIdSet") Set<Integer> yogaPoseGroupIdSet,
                                                      @Param("versionInfoBO") ProjPublishCurrentVersionInfoBO versionInfoBO);
}
