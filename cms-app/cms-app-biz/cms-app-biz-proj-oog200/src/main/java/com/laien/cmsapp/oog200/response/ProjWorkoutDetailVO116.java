package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * note: Workout116 Detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Workout116 Detail", description = "Workout116 Detail")
@Accessors(chain = true)
public class ProjWorkoutDetailVO116 {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "名字")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @JsonIgnore
    private String description;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Boolean subscription = false;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "video的m3u8地址")
    @AbsoluteR2Url
    private String videoUrl;

    @ApiModelProperty(value = "playlistId")
    private Integer playlistId;

    @ApiModelProperty(value = "audio 列表")
    private List<ProjWorkoutAudioDetailVO116> audioList;

    @ApiModelProperty(value = "单元组")
    private List<ResYogaVideoDetailVO> videos;

}
