package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.entity.ResVideoClass;
import com.laien.cmsapp.oog200.mapper.ResVideoClassMapper;
import com.laien.cmsapp.oog200.service.IResVideoClassService;
import com.laien.cmsapp.response.ResVideoClassVO;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.RequestContextUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * res video class 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Service
public class ResVideoClassServiceImpl extends ServiceImpl<ResVideoClassMapper, ResVideoClass> implements IResVideoClassService {

    @Resource
    private FileService fileService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ResVideoClassVO> selectEnableByIds(Integer projId, Integer version, List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayListWithCapacity(0);
        }

        List<ResVideoClass> videoClassList = getBaseMapper().selectIdsByProj(projId, version, ids);
        if (CollUtil.isEmpty(videoClassList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(videoClassList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        List<ResVideoClassVO> videoClassVOS = videoClassList.stream().map(v -> {
            ResVideoClassVO resVideoClassVO = new ResVideoClassVO();
            BeanUtils.copyProperties(v, resVideoClassVO);
            resVideoClassVO.setDuration(TimeConvertUtil.millisToMinutes(resVideoClassVO.getDuration()));
            //替换m3u8Url为video2532M3u8Url
            if (StringUtils.isNotBlank(v.getVideo2532M3u8Url())) {
                resVideoClassVO.setVideoUrl(fileService.getAbsoluteR2Url(v.getVideo2532M3u8Url()));
            } else {
                resVideoClassVO.setVideoUrl(fileService.getAbsoluteR2Url(v.getVideoUrl()));
            }
            return resVideoClassVO;
        }).collect(Collectors.toList());

        return videoClassVOS;
    }

    @Override
    public List<ResVideoClassVO> listAll(String lang) {

        LambdaQueryWrapper<ResVideoClass> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResVideoClass::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ResVideoClass::getDelFlag, GlobalConstant.NO);

        List<ResVideoClass> videoClassList = list(queryWrapper);
        if (CollUtil.isEmpty(videoClassList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(videoClassList, ProjCodeEnums.OOG200, lang);
        List<ResVideoClassVO> videoList = videoClassList.stream().map(video -> {
            ResVideoClassVO resVideoClassVO = new ResVideoClassVO();
            BeanUtils.copyProperties(video, resVideoClassVO);
            resVideoClassVO.setDuration(TimeConvertUtil.millisToMinutes(resVideoClassVO.getDuration()));
            //替换m3u8Url为video2532M3u8Url
            if (StringUtils.isNotBlank(video.getVideo2532M3u8Url())) {
                resVideoClassVO.setVideoUrl(fileService.getAbsoluteR2Url(video.getVideo2532M3u8Url()));
            } else {
                resVideoClassVO.setVideoUrl(fileService.getAbsoluteR2Url(resVideoClassVO.getVideoUrl()));
            }
            return resVideoClassVO;
        }).collect(Collectors.toList());

        return videoList;
    }

}
