package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video v3
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video v3", description = "video v3")
public class ProjVideoV3VO {

    @JsonIgnore
    @ApiModelProperty(value = "generate id", hidden = true)
    private Integer generateId;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

}
