package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjAllergenPub;
import com.laien.cmsapp.oog200.entity.ProjAllergenRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjAllergenPubMapper;
import com.laien.cmsapp.oog200.service.IProjAllergenPubService;
import com.laien.cmsapp.oog200.service.IProjAllergenRelationPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/7 15:07
 */
@Service
public class ProjAllergenPubServiceImpl extends ServiceImpl<ProjAllergenPubMapper, ProjAllergenPub> implements IProjAllergenPubService {

    @Resource
    IProjAllergenRelationPubService relationPubService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<String> listByDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, String lang, AllergenRelationBusinessEnum businessEnum) {

        List<ProjAllergenRelationPub> allergenRelationPubList = relationPubService.listByVersionAndDataId(versionInfoBO, dataId, businessEnum);
        if (CollectionUtils.isEmpty(allergenRelationPubList)) {
            return Collections.emptyList();
        }

        List<Integer> allergenIds = allergenRelationPubList.stream().map(p -> p.getProjAllergenId()).collect(Collectors.toList());
        List<ProjAllergenPub> projAllergenPubList = listByVersionAndDataId(versionInfoBO, allergenIds);
        if (CollectionUtils.isEmpty(projAllergenPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(projAllergenPubList, ProjCodeEnums.OOG200, lang);
        Map<Integer, String> allergenIdMap = projAllergenPubList.stream().collect(Collectors.toMap(ProjAllergenPub::getId, ProjAllergenPub::getName));
        return allergenIds.stream().filter(allergenIdMap::containsKey).map(allergenIdMap::get).collect(Collectors.toList());
    }

    private List<ProjAllergenPub> listByVersionAndDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> allergenIds) {

        LambdaQueryWrapper<ProjAllergenPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjAllergenPub::getId, allergenIds);
        queryWrapper.eq(ProjAllergenPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjAllergenPub::getProjId, versionInfoBO.getProjId());
        return list(queryWrapper);
    }

}
