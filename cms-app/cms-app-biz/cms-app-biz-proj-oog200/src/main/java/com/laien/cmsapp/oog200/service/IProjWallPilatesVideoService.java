package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesVideo;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;

import java.util.List;
import java.util.Set;


/**
 * <p>
 * wall pilates video资源 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public interface IProjWallPilatesVideoService extends IService<ProjWallPilatesVideo> {

    List<ResYogaVideoDetailVO> find(Set<Integer> wallPilatesAutoWorkoutIdSet);

    List<ResYogaVideoDetailVO> queryByWallPilatesRegularWorkoutIdSet(Set<Integer> wallPilatesRegularWorkoutIdSet);

}
