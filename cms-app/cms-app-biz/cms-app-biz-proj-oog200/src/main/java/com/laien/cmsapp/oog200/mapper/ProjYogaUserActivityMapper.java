package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjYogaUserActivity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/6
 */

public interface ProjYogaUserActivityMapper extends BaseMapper<ProjYogaUserActivity> {

    @Select(value = "select user_ac.* from proj_yoga_user user " +
            "inner join proj_yoga_user_activity user_ac on user.invite_code = user_ac.invite_code " +
            "where user.id = #{userId} and user.del_flag = 0")
    List<ProjYogaUserActivity> listByUserId(Integer userId);

}
