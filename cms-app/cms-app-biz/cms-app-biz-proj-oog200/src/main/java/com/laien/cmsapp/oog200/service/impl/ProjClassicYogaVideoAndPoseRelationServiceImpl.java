package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjClassicYogaVideoPoseRelation;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseWorkoutPub;
import com.laien.cmsapp.oog200.mapper.ProjClassicYogaVideoPoseRelationMapper;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IProjClassicYogaVideoAndPoseRelationService;
import com.laien.cmsapp.oog200.service.IProjYogaPoseWorkoutService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/11/5 16:47
 */
@Service
@Slf4j
public class ProjClassicYogaVideoAndPoseRelationServiceImpl extends ServiceImpl<ProjClassicYogaVideoPoseRelationMapper, ProjClassicYogaVideoPoseRelation> implements IProjClassicYogaVideoAndPoseRelationService {

    @Resource
    private IProjYogaPoseWorkoutService poseWorkoutService;

    @Override
    public void setRelationPoseWorkoutInfo(List<ResYogaVideoDetailVO> classicYogaVideos, Integer workoutType) {

        if (CollectionUtils.isEmpty(classicYogaVideos)) {
            return;
        }

        List<Integer> yogaVideoIds = classicYogaVideos.stream().map(ResYogaVideoDetailVO::getId).collect(Collectors.toList());
        List<ProjClassicYogaVideoPoseRelation> poseRelationList = listRelationByYogaVideoIds(yogaVideoIds);
        if (CollectionUtils.isEmpty(poseRelationList)) {
            return;
        }

        List<Integer> poseVideoIds = poseRelationList.stream().map(relation -> relation.getProjYogaPoseVideoId()).collect(Collectors.toList());
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjYogaPoseWorkoutPub> poseWorkoutList = poseWorkoutService.listByPoseVideoIds(versionInfoBO, poseVideoIds);
        if (CollectionUtils.isEmpty(poseWorkoutList)) {
            return;
        }

        Map<Integer, Integer> yogaAndPoseIdMap = poseRelationList.stream().collect(Collectors.toMap(ProjClassicYogaVideoPoseRelation::getResYogaVideoId, ProjClassicYogaVideoPoseRelation::getProjYogaPoseVideoId, (k1, k2) -> k1));
        Map<Integer, Integer> poseVideoAndWorkoutIdMap = poseWorkoutList.stream().collect(Collectors.toMap(poseWorkout -> poseWorkout.getProjYogaPoseVideoId(), poseWorkout -> poseWorkout.getId()));

        classicYogaVideos.forEach(yogaVideoDetailVO -> {
            if (yogaAndPoseIdMap.containsKey(yogaVideoDetailVO.getId()) && poseVideoAndWorkoutIdMap.containsKey(yogaAndPoseIdMap.get(yogaVideoDetailVO.getId()))) {
                yogaVideoDetailVO.setPoseWorkoutId(poseVideoAndWorkoutIdMap.get(yogaAndPoseIdMap.get(yogaVideoDetailVO.getId())));
                yogaVideoDetailVO.setPoseWorkoutTypeCode(workoutType);
            }
        });
    }

    @Override
    public List<ProjClassicYogaVideoPoseRelation> listRelationByYogaVideoIds(Collection<Integer> yogaVideoIds) {

        if (CollectionUtils.isEmpty(yogaVideoIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjClassicYogaVideoPoseRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjClassicYogaVideoPoseRelation::getResYogaVideoId, yogaVideoIds);
        return list(queryWrapper);
    }
}
