package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaPoseWorkoutBO;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutAudioI18n;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseWorkoutPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaPoseWorkoutMapper;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutGuidanceItemVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutGuidanceVO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutListVO;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutAudioI18nService;
import com.laien.cmsapp.oog200.service.IProjYogaPoseWorkoutService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.FocusEnum;
import com.laien.common.oog200.enums.PositionEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.common.util.MyStringUtil;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * proj yoga pose workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Slf4j
@Service
public class ProjYogaPoseWorkoutServiceImpl extends ServiceImpl<ProjYogaPoseWorkoutMapper, ProjYogaPoseWorkoutPub>
        implements IProjYogaPoseWorkoutService {

    @Resource
    private IProjYogaAutoWorkoutAudioI18nService projYogaAutoWorkoutAudioI18nService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjYogaPoseWorkoutListVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang) {
        LambdaQueryWrapper<ProjYogaPoseWorkoutPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseWorkoutPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjYogaPoseWorkoutPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaPoseWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .orderByDesc(BaseModel::getId);
        List<ProjYogaPoseWorkoutPub> workoutList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(workoutList)) {
            log.error("yoga pose workout list not find, appCode:{}", versionInfoBO.getAppCode());
            return null;
        }
        i18nUtil.translate(workoutList, ProjCodeEnums.OOG200, lang);
        List<ProjYogaPoseWorkoutListVO> finalWorkoutList = new ArrayList<>(workoutList.size());
        for (ProjYogaPoseWorkoutPub workout : workoutList) {
            ProjYogaPoseWorkoutListVO workoutVO = new ProjYogaPoseWorkoutListVO();
            BeanUtils.copyProperties(workout, workoutVO);
            DifficultyEnum difficultyEnum = DifficultyEnum.getByName(workout.getDifficulty());
            FocusEnum focusEnum = FocusEnum.getByName(workout.getFocus());
            PositionEnum positionEnum = PositionEnum.getByName(workout.getPosition());

            if (null != difficultyEnum) {
                workoutVO.setDifficultyCode(difficultyEnum.getCode());
            }
            if (null != focusEnum) {
                workoutVO.setFocusCode(focusEnum.getCode());
            }
            if (null != positionEnum) {
                workoutVO.setPositionCode(positionEnum.getCode());
            }
            finalWorkoutList.add(workoutVO);
        }
        return finalWorkoutList;
    }

    @Override
    public List<ProjYogaPoseWorkoutDetailVO> findByYogaPoseGroupId(Set<Integer> yogaPoseGroupIdSet,
                                                                   ProjPublishCurrentVersionInfoBO versionInfoBO,
                                                                   Integer m3u8Type) {
        List<ProjYogaPoseWorkoutBO> poseWorkoutList = baseMapper.findByYogaPoseGroupId(yogaPoseGroupIdSet, versionInfoBO);
        if (CollUtil.isEmpty(poseWorkoutList)) {
            return new ArrayList<>();
        }
        i18nUtil.translate(poseWorkoutList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        Set<Integer> workoutIdSet = poseWorkoutList.stream().map(ProjYogaPoseWorkoutBO::getId).collect(Collectors.toSet());
        Map<Integer, List<ProjYogaAutoWorkoutAudioI18n>> audioI18nMap = projYogaAutoWorkoutAudioI18nService.getAudioI18n4Workout(workoutIdSet, YogaAutoWorkoutTemplateEnum.POSE_LIBRARY.getCode()).stream().collect(Collectors.groupingBy(ProjYogaAutoWorkoutAudioI18n::getWorkoutId));
        List<ProjYogaPoseWorkoutDetailVO> detailList = new ArrayList<>(poseWorkoutList.size());
        for (ProjYogaPoseWorkoutBO workoutBO : poseWorkoutList) {
            ProjYogaPoseWorkoutPub workout = new ProjYogaPoseWorkoutPub();
            BeanUtils.copyProperties(workoutBO, workout);
            ProjYogaPoseWorkoutDetailVO detailVO = convertToDetailVO(m3u8Type, workout, audioI18nMap.get(workoutBO.getId()));
            detailVO.setYogaPoseGroupId(workoutBO.getYogaPoseGroupId());
            detailList.add(detailVO);
        }
        return detailList;
    }

    @Override
    public ProjYogaPoseWorkoutDetailVO findDetail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type, String lang) {
        LambdaQueryWrapper<ProjYogaPoseWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPoseWorkoutPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjYogaPoseWorkoutPub::getId, id);
        ProjYogaPoseWorkoutPub yogaPoseWorkout = baseMapper.selectOne(queryWrapper);

        if (Objects.nonNull(yogaPoseWorkout)) {
            i18nUtil.translate(Lists.newArrayList(yogaPoseWorkout), ProjCodeEnums.OOG200, lang);
        }
        Map<Integer, List<ProjYogaAutoWorkoutAudioI18n>> audioI18nMap = projYogaAutoWorkoutAudioI18nService.getAudioI18n4Workout(Sets.newHashSet(id), YogaAutoWorkoutTemplateEnum.POSE_LIBRARY.getCode()).stream().collect(Collectors.groupingBy(ProjYogaAutoWorkoutAudioI18n::getWorkoutId));

        ProjYogaPoseWorkoutDetailVO workoutDetailVO = convertToDetailVO(m3u8Type, yogaPoseWorkout, audioI18nMap.get(id));
        // 替换video mask
        if (audioI18nMap.containsKey(yogaPoseWorkout.getId())) {
            audioI18nMap.get(yogaPoseWorkout.getId()).stream()
                    .filter(audioI18n -> Objects.equals(audioI18n.getLanguage(), lang))
                    .filter(audioI18n -> StringUtils.isNotBlank(audioI18n.getVideoMaskJson())).findFirst()
                    .ifPresent(audioI18n -> workoutDetailVO.setVideoMaskJson(audioI18n.getVideoMaskJson()));
        }
        return workoutDetailVO;
    }

    @Override
    public List<ProjYogaPoseWorkoutPub> listByPoseVideoIds(ProjPublishCurrentVersionInfoBO versionInfoBO, List<Integer> poseVideoIds) {

        LambdaQueryWrapper<ProjYogaPoseWorkoutPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseWorkoutPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjYogaPoseWorkoutPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaPoseWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjYogaPoseWorkoutPub::getProjYogaPoseVideoId, poseVideoIds)
                .orderByDesc(BaseModel::getId);

        return baseMapper.selectList(wrapper);
    }

    private ProjYogaPoseWorkoutDetailVO convertToDetailVO(Integer m3u8Type, ProjYogaPoseWorkoutPub yogaPoseWorkout, List<ProjYogaAutoWorkoutAudioI18n> audioI18nList) {
        if (null == yogaPoseWorkout) {
            return null;
        }
        ProjYogaPoseWorkoutDetailVO detailVO = new ProjYogaPoseWorkoutDetailVO();
        BeanUtils.copyProperties(yogaPoseWorkout, detailVO);
        DifficultyEnum difficultyEnum = DifficultyEnum.getByName(yogaPoseWorkout.getDifficulty());
        FocusEnum focusEnum = FocusEnum.getByName(yogaPoseWorkout.getFocus());
        PositionEnum positionEnum = PositionEnum.getByName(yogaPoseWorkout.getPosition());
        if (null != difficultyEnum) {
            detailVO.setDifficultyCode(difficultyEnum.getCode());
        }
        if (null != focusEnum) {
            detailVO.setFocusCode(focusEnum.getCode());
        }
        if (null != positionEnum) {
            detailVO.setPositionCode(positionEnum.getCode());
        }
        if (ObjUtil.isNotNull(detailVO) && ObjectUtil.notEqual(GlobalConstant.TWO, m3u8Type)) {
            String video2532Url = yogaPoseWorkout.getVideo2532Url();
            if (StrUtil.isNotBlank(video2532Url)) {
                detailVO.setVideoM3u8Url(video2532Url);
            }
        }
        String instructions = yogaPoseWorkout.getInstructions();
        if (StringUtils.isNotBlank(instructions)) {
            detailVO.setInstructionsList(MyStringUtil.toList(instructions));
        }
        String benefits = yogaPoseWorkout.getBenefits();
        if (StringUtils.isNotBlank(benefits)) {
            detailVO.setBenefitsList(MyStringUtil.toList(benefits));
        }
        String chairVariationTips = yogaPoseWorkout.getChairVariationTips();
        if (StringUtils.isNotBlank(chairVariationTips)) {
            detailVO.setChairVariationTipsList(MyStringUtil.toList(chairVariationTips));
        }
        // 填充默认语言音频数据
        ProjYogaAutoWorkoutGuidanceVO guidanceVO = new ProjYogaAutoWorkoutGuidanceVO();
        List<ProjYogaAutoWorkoutGuidanceItemVO> defaultList = new ArrayList<>();
        List<ProjYogaAutoWorkoutGuidanceItemVO> leastLest = new ArrayList<>();
        ProjYogaAutoWorkoutGuidanceItemVO detailLongVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailLongVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
        detailLongVO.setAudioJsonUrl(detailVO.getAudioLongJson());

        ProjYogaAutoWorkoutGuidanceItemVO detailShortVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailShortVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
        detailShortVO.setAudioJsonUrl(detailVO.getAudioShortJson());
        defaultList.add(detailLongVO);
        leastLest.add(detailShortVO);

        guidanceVO.setDefault_(defaultList);
        guidanceVO.setLeast(leastLest);
        detailVO.setGuidance(guidanceVO);
        if (CollUtil.isEmpty(audioI18nList)) {
            return detailVO;
        }
        // 上一步已经填充了默认语言，这里填充其他语言数据
        audioI18nList.stream()
                .filter(audioI18n -> !Objects.equals(GlobalConstant.DEFAULT_LANGUAGE, audioI18n.getLanguage()))
                .forEach(audioI18n -> {
                    ProjYogaAutoWorkoutGuidanceItemVO longVO = new ProjYogaAutoWorkoutGuidanceItemVO();
                    longVO.setLanguage(audioI18n.getLanguage());
                    longVO.setAudioJsonUrl(audioI18n.getAudioLongJsonUrl());
                    defaultList.add(longVO);

                    ProjYogaAutoWorkoutGuidanceItemVO shortVO = new ProjYogaAutoWorkoutGuidanceItemVO();
                    shortVO.setLanguage(audioI18n.getLanguage());
                    shortVO.setAudioJsonUrl(audioI18n.getAudioShortJsonUrl());
                    leastLest.add(shortVO);
                });

        return detailVO;
    }
}
