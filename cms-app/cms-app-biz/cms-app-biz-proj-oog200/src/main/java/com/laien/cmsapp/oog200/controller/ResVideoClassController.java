package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.response.ResVideoClassVO;
import com.laien.cmsapp.oog200.service.IResVideoClassService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * video class 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Api(tags = "app端：videoClass")
@RestController
public class ResVideoClassController extends ResponseController {

    @Resource
    private IResVideoClassService resVideoClassService;

    @ApiOperation(value = "按id集合查询列表", tags = {"oog200"})
    @GetMapping("/{appCode}/videoClass/v1/listByIds")
    public ResponseResult<List<ResVideoClassVO>> listByIds(@ApiParam(value = "id集合") @RequestParam List<Integer> ids) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(resVideoClassService.selectEnableByIds(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion(), ids));
    }

    @ApiOperation(value = "获取所有未删除的VideoClass", tags = {"oog200"})
    @GetMapping("/{appCode}/videoClass/v1/listAll")
    public ResponseResult<List<ResVideoClassVO>> listAll(@RequestParam(defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {

        return succ(resVideoClassService.listAll(lang));
    }

}
