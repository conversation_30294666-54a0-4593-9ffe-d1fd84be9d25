package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaUser;
import com.laien.cmsapp.oog200.requst.ProjYogaUserReq;
import com.laien.cmsapp.oog200.response.ProjYogaUserInviteLinkVO;
import com.laien.cmsapp.oog200.response.ProjYogaUserVO;

import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/6
 */

public interface IProjYogaUserService extends IService<ProjYogaUser> {

    ProjYogaUserVO createUser(ProjYogaUserReq yogaUserReq, ProjPublishCurrentVersionInfoBO versionInfoBO, int createCount);

    ProjYogaUserInviteLinkVO createInviteLink(Integer userId);

    List<ProjYogaUser> listByInviteCode(Collection<String> inviteCodes);

    ProjYogaUser getByAdaptyId(String adaptyId);

}
