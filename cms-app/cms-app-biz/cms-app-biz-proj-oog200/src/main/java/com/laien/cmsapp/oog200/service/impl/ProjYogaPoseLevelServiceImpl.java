package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseLevelPub;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.cmsapp.oog200.mapper.ProjYogaPoseLevelMapper;
import com.laien.cmsapp.oog200.requst.ProjYogaPoseLevelListReq;
import com.laien.cmsapp.oog200.response.ProjYogaPoseGroupDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseLevelDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.cmsapp.oog200.service.IProjYogaPoseGroupService;
import com.laien.cmsapp.oog200.service.IProjYogaPoseLevelService;
import com.laien.common.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * proj yoga pose grouping 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Slf4j
@Service
public class ProjYogaPoseLevelServiceImpl extends ServiceImpl<ProjYogaPoseLevelMapper, ProjYogaPoseLevelPub>
        implements IProjYogaPoseLevelService {

    @Resource
    private IProjYogaPoseGroupService projYogaPoseGroupService;

    @Override
    public ProjYogaPoseLevelDetailVO findDetail(ProjYogaPoseLevelListReq listReq,
                                                ProjPublishCurrentVersionInfoBO versionInfoBO,
                                                Integer m3u8Type) {
        DifficultyEnum difficultyEnum = DifficultyEnum.getByCode(listReq.getDifficultyCode());
        if (null == difficultyEnum) {
            log.error("invalid parameter, req: {}, appCode: {}", listReq, versionInfoBO.getAppCode());
            return null;
        }
        LambdaQueryWrapper<ProjYogaPoseLevelPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseLevelPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjYogaPoseLevelPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaPoseLevelPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjYogaPoseLevelPub::getDifficulty, difficultyEnum.getName());
        List<ProjYogaPoseLevelPub> poseLevelList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(poseLevelList)) {
            log.error("pose level no data available, req: {}, appCode: {}", listReq, versionInfoBO.getAppCode());
            return null;
        }
        ProjYogaPoseLevelPub poseLevel = poseLevelList.get(GlobalConstant.ZERO);
        ProjYogaPoseLevelDetailVO poseLevelVO = new ProjYogaPoseLevelDetailVO();
        BeanUtils.copyProperties(poseLevel, poseLevelVO);
        DifficultyEnum difficulty = DifficultyEnum.getByName(poseLevel.getDifficulty());
        if (null != difficulty) {
            poseLevelVO.setDifficultyCode(difficulty.getCode());
        }
        List<ProjYogaPoseGroupDetailVO> poseGroupList = projYogaPoseGroupService.findByYogaPoseLevelId(poseLevelVO.getId(), versionInfoBO, m3u8Type, listReq.getLang());
        if (CollUtil.isEmpty(poseGroupList)) {
            return poseLevelVO;
        }
        List<ProjYogaPoseWorkoutDetailVO> workoutList = new ArrayList<>(GlobalConstant.HUNDRED);
        for (ProjYogaPoseGroupDetailVO group : poseGroupList) {
            List<ProjYogaPoseWorkoutDetailVO> groupWorkoutList = group.getWorkoutList();
            if (CollUtil.isNotEmpty(groupWorkoutList)) {
                workoutList.addAll(groupWorkoutList);
            }
        }
        poseLevelVO.setYogaPoseWorkoutList(workoutList);
        return poseLevelVO;
    }

}
