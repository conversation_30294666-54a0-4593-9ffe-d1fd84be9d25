package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjCollectionClassPub;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.cmsapp.oog200.requst.CollectionClassListReq;
import com.laien.cmsapp.response.ProjCollectionClassDetailVO;
import com.laien.cmsapp.response.ProjCollectionClassListVO;

import java.util.List;

/**
 * <p>
 * proj collection class 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public interface IProjCollectionClassService extends IService<ProjCollectionClassPub> {

    /**
     * 查询CollectionClass 列表
     *
     * @param versionInfoBO    versionInfoBO
     * @param typeList
     * @return list
     */
    List<ProjCollectionClassListVO> selectCollectionClassList(ProjPublishCurrentVersionInfoBO versionInfoBO, CollectionClassListReq req, List<YogaAutoWorkoutTemplateEnum> typeList);

    /**
     * 查询CollectionClass 详情
     *
     * @param versionInfoBO versionInfoBO
     * @return ProjCollectionClassDetailVO
     */
    ProjCollectionClassDetailVO selectCollectionClassDetail(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO);
}
