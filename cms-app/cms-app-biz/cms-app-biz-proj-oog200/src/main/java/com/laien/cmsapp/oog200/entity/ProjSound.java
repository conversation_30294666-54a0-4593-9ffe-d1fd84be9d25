package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppAudioSingleTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import com.laien.common.oog200.enums.GenderEnums;
import com.laien.common.oog200.enums.SoundSubTypeEnums;
import com.laien.common.oog200.enums.SoundTypeEnums;
import com.laien.common.oog200.enums.TableCodeEnums;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * proj__sound
 * <p>
 *
 * <AUTHOR>
 * @since 2025/06/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "Proj7MSound对象", description = "proj__sound")
@TableName(autoResultMap = true)
public class ProjSound extends BaseModel implements AppAudioCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表标识")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private TableCodeEnums tableCode = TableCodeEnums.PROJ_SOUND;

    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private SoundTypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private SoundSubTypeEnums soundSubType;

    @ApiModelProperty(value = "声音脚本")
    @AppAudioSingleTranslateField(urlFieldName = "url", durationFieldName = "duration")
    private String soundScript;

    @ApiModelProperty(value = "声音")
    @AbsoluteR2Url
    private String url;

    @ApiModelProperty(value = "duration")
    private Integer duration;

    @ApiModelProperty(value = "性别 1-female 2-male")
    private GenderEnums gender;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "是否需要翻译")
    private Boolean needTranslation;

}
