package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * wall pilates video资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_wall_pilates_video")
@ApiModel(value="ProjWallPilatesVideo对象", description="wall pilates video资源")
public class ProjWallPilatesVideo extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "projId")
    private Integer projId;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "动作体位 ：Standing、Lying")
    private String position;

    @ApiModelProperty(value = "动作类型 ： Warm Up、Main 、Cool Down")
    private String type;

    @ApiModelProperty(value = "当前动作方向 Left、Right、Central")
    private String direction;

    @ApiModelProperty(value = "关联左右动作id")
    private Integer leftRightId;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "视频地址(正机位m3u8)")
    private String videoUrl;

    @ApiModelProperty(value = "视频地址(正机位 2532 m3u8)")
    private String video2532Url;


}
