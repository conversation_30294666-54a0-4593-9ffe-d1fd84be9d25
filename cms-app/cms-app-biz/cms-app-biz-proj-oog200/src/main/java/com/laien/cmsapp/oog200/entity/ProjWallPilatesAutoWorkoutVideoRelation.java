package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_wall_pilates_auto_workout_video_relation")
@ApiModel(value="ProjWallPilatesAutoWorkoutVideoRelation对象", description="Wall pilates auto workout")
public class ProjWallPilatesAutoWorkoutVideoRelation extends BaseModel {

    private static final long serialVersionUID = 1L;

    private Integer projYogaAutoWorkoutTemplateId;

    private Integer projWallPilatesAutoWorkoutId;

    private Integer projWallPilatesVideoId;


}
