package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "video guidance item", description = "video guidance item")
public class ProjYogaAutoWorkoutGuidanceItemVO {

    @ApiModelProperty(value = "language")
    private String language;

    @AbsoluteR2Url
    @ApiModelProperty(value = "audio json url")
    private String audioJsonUrl;

}
