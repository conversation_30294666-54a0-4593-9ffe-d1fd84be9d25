package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjYogaAward;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
public interface IProjYogaAwardService extends IService<ProjYogaAward> {

    List<ProjYogaAward> listUnusedAward(String productId, int limit, LocalDateTime expireTime);

    void setUsed(Collection<Integer> ids);
}
