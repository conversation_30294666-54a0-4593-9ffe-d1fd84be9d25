package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutAudioI18n;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.cmsapp.oog200.mapper.ProjYogaAutoWorkoutAudioI18nMapper;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutGuidanceItemVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutGuidanceVO;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutAudioI18nService;
import com.laien.common.constant.GlobalConstant;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/11/25 11:30
 */
@Service
public class ProjYogaAutoWorkoutAudioI18nServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutAudioI18nMapper, ProjYogaAutoWorkoutAudioI18n> implements IProjYogaAutoWorkoutAudioI18nService {

    @Override
    public void setAudioI18n4Workout(List<ProjPlanWorkoutListVO> workoutList) {

        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        Map<Integer, List<ProjPlanWorkoutListVO>> workoutTypeGroup = workoutList.stream().collect(Collectors.groupingBy(ProjPlanWorkoutListVO::getPlanTypeCode));
        workoutTypeGroup.entrySet().forEach(entry -> setAudioI18n4WorkoutByType(entry.getValue(), entry.getKey()));
    }

    @Override
    public List<ProjYogaAutoWorkoutAudioI18n> getAudioI18n4Workout(Set<Integer> workoutIdSet,Integer workoutType) {

        LambdaQueryWrapper<ProjYogaAutoWorkoutAudioI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaAutoWorkoutAudioI18n::getWorkoutId, workoutIdSet);
        queryWrapper.eq(ProjYogaAutoWorkoutAudioI18n::getWorkoutType, workoutType);

        return list(queryWrapper);
    }

    @Override
    public List<ProjYogaAutoWorkoutAudioI18n> getAudioI18n4Workout(Set<Integer> workoutIdSet, Integer workoutType, String lang) {
        LambdaQueryWrapper<ProjYogaAutoWorkoutAudioI18n> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaAutoWorkoutAudioI18n::getWorkoutId, workoutIdSet);
        queryWrapper.eq(ProjYogaAutoWorkoutAudioI18n::getWorkoutType, workoutType);
        queryWrapper.eq(ProjYogaAutoWorkoutAudioI18n::getLanguage, lang);

        return list(queryWrapper);
    }

    private void setAudioI18n4WorkoutByType(List<ProjPlanWorkoutListVO> workoutList, Integer workoutType) {

        // 截至2024/11/25，SOMATIC_YOGA和LAZY_YOGA使用的是CLASSIC_YOGA类型的workout
        if (Objects.equals(workoutType, YogaAutoWorkoutTemplateEnum.SOMATIC_YOGA.getCode()) || Objects.equals(workoutType, YogaAutoWorkoutTemplateEnum.LAZY_YOGA.getCode())) {
            workoutType = YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode();
        }

        Set<Integer> workoutIds = workoutList.stream().map(ProjPlanWorkoutListVO::getId).collect(Collectors.toSet());
        List<ProjYogaAutoWorkoutAudioI18n> workoutAudioI18nList = getAudioI18n4Workout(workoutIds, workoutType);
        if (CollectionUtils.isEmpty(workoutAudioI18nList)) {
            setDefaultGuidance(workoutList);
            return;
        }

        Map<Integer, List<ProjYogaAutoWorkoutAudioI18n>> workoutAudioI18nMap = workoutAudioI18nList.stream().collect(Collectors.groupingBy(ProjYogaAutoWorkoutAudioI18n::getWorkoutId));
        for (ProjPlanWorkoutListVO workoutListVO : workoutList) {

            if (!workoutAudioI18nMap.containsKey(workoutListVO.getId())) {
                ProjYogaAutoWorkoutGuidanceVO guidanceVO = ProjYogaAutoWorkoutGuidanceVO.buildGuidance(workoutListVO.getAudioLongJson(), workoutListVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE);
                workoutListVO.setGuidance(guidanceVO);
                continue;
            }

            // 兼容安卓，将en排在最前面
            Function<ProjYogaAutoWorkoutAudioI18n, Integer> audioSort = workout -> {
                if (Objects.equals(workout.getLanguage(), GlobalConstant.DEFAULT_LANGUAGE)) {
                    return GlobalConstant.ZERO;
                } else {
                    return GlobalConstant.ONE;
                }
            };

            List<ProjYogaAutoWorkoutAudioI18n> workoutAudioI18ns = workoutAudioI18nMap.get(workoutListVO.getId());
            List<ProjYogaAutoWorkoutGuidanceItemVO> longAudioItems = workoutAudioI18ns.stream().filter(audio -> !StringUtils.isEmpty(audio.getAudioLongJsonUrl())).sorted(Comparator.comparing(audioSort)).map(audio -> ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(audio.getAudioLongJsonUrl(), audio.getLanguage())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(longAudioItems)) {
                longAudioItems = Lists.newArrayList(ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(workoutListVO.getAudioLongJson(), GlobalConstant.DEFAULT_LANGUAGE));
            }

            List<ProjYogaAutoWorkoutGuidanceItemVO> shortAudioItems = workoutAudioI18ns.stream().filter(audio -> !StringUtils.isEmpty(audio.getAudioShortJsonUrl())).sorted(Comparator.comparing(audioSort)).map(audio -> ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(audio.getAudioShortJsonUrl(), audio.getLanguage())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shortAudioItems)) {
                shortAudioItems = Lists.newArrayList(ProjYogaAutoWorkoutGuidanceVO.buildGuidanceItem(workoutListVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE));
            }

            ProjYogaAutoWorkoutGuidanceVO guidanceVO = new ProjYogaAutoWorkoutGuidanceVO();
            guidanceVO.setLeast(shortAudioItems);
            guidanceVO.setDefault_(longAudioItems);
            workoutListVO.setGuidance(guidanceVO);
        }
    }

    private void setDefaultGuidance(List<ProjPlanWorkoutListVO> workoutList) {

        for (ProjPlanWorkoutListVO workoutListVO : workoutList) {
            ProjYogaAutoWorkoutGuidanceVO guidanceVO = ProjYogaAutoWorkoutGuidanceVO.buildGuidance(workoutListVO.getAudioLongJson(), workoutListVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE);
            workoutListVO.setGuidance(guidanceVO);
        }
    }

}
