package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjAllergenPub;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:59
 */
public interface IProjAllergenPubService extends IService<ProjAllergenPub> {

    List<String> listByDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, String lang, AllergenRelationBusinessEnum businessEnum);

}
