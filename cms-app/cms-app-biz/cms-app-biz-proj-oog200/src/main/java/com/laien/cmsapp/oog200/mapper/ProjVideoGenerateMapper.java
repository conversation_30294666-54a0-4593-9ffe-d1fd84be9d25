package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjVideoGenerate;
import com.laien.cmsapp.oog200.response.ProjVideoV2VO;
import com.laien.cmsapp.oog200.response.ProjVideoV3VO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * video generate Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface ProjVideoGenerateMapper extends BaseMapper<ProjVideoGenerate> {

    /**
     * 根据id查询视频列表
     *
     * @param id id
     * @return list
     */
    List<ProjVideoV2VO> selectVideosById(Integer id);

    /**
     * 根据id查询视频列表
     *
     * @param id id
     * @return list
     */
    List<ProjVideoV3VO> selectVideosV3ById(Integer id);

    /**
     * 根据id查询视频列表
     *
     * @param idList idList
     * @return list
     */
    List<ProjVideoV3VO> selectVideosV3ByIds(@Param("idList") List<Integer> idList);


    /**
     * 根据templateId集合和自身id集合查询指定版本的数据（可查询删除数据）
     *
     * @param templateIds
     * @param ids
     * @param dataVersion
     * @return
     */
    List<ProjVideoGenerate> selectVideosByTempAndIds(@Param("templateIds") List<Integer> templateIds, @Param("ids") List<Integer> ids, @Param("dataVersion") Integer dataVersion);

}
