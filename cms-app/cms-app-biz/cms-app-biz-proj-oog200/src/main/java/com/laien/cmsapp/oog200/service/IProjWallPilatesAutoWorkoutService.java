package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesAutoWorkout;
import com.laien.cmsapp.oog200.requst.ProjWorkoutPlanReq;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;

import java.util.List;

/**
 * <p>
 * Wall pilates auto workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public interface IProjWallPilatesAutoWorkoutService extends IService<ProjWallPilatesAutoWorkout> {

    List<ProjPlanWorkoutListVO> getPlan(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO, String lang);

    List<ProjPlanWorkoutListVO> query(List<Integer> idList, Integer m3u8Type, String lang);

}
