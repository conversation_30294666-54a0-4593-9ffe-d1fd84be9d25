package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaMusicPub;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/21 12:14
 */
public interface IProjYogaMusicPubService extends IService<ProjYogaMusicPub> {

    List<ProjYogaMusicPub> listByStatusAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> ids, Integer status, String lang);

}
