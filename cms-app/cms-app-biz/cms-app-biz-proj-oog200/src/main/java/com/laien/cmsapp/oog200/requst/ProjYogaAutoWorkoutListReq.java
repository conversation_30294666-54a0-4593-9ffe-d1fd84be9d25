package com.laien.cmsapp.oog200.requst;

import com.laien.cmsapp.requst.LangReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: yogaAutoWorkout id 列表查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yogaAutoWorkout id 列表查询", description = "yogaAutoWorkout id 列表查询")
public class ProjYogaAutoWorkoutListReq extends LangReq {

    @ApiModelProperty(value = "id list", required = true)
    private List<Integer> idList;

    @ApiModelProperty(value = "difficulty 保留200原有逻辑，传什么返回什么")
    private String difficulty;

    @ApiModelProperty(value = "传1 查询2532 m3u8, 传2 查询dynamic m3u8, 默认1")
    private Integer m3u8Type = 1;
}
