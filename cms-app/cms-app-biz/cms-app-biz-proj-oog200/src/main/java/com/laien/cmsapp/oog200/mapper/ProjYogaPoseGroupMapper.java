package com.laien.cmsapp.oog200.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseGroupPub;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
public interface ProjYogaPoseGroupMapper extends BaseMapper<ProjYogaPoseGroupPub> {

    List<ProjYogaPoseGroupPub> findByYogaPoseLevelId(@Param("yogaPoseLevelId") Integer yogaPoseLevelId,
                                                        @Param("versionInfoBO") ProjPublishCurrentVersionInfoBO versionInfoBO);
}
