package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkout;
import com.laien.cmsapp.oog200.requst.ProjWorkoutPlanReq;
import com.laien.cmsapp.oog200.requst.ProjYogaAutoWorkoutListReq;
import com.laien.cmsapp.oog200.requst.ProjYogaAutoWorkoutPlanReq;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutListVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutRefreshVO;

import java.util.List;

/**
 * <p>
 * oog200 workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
public interface IProjYogaAutoWorkoutService extends IService<ProjYogaAutoWorkout> {

    /**
     * 获取plan
     *
     * @param planReq planReq
     * @return list
     */
    List<ProjYogaAutoWorkoutListVO> getPlan(ProjYogaAutoWorkoutPlanReq planReq);

    List<ProjPlanWorkoutListVO> getPlan(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    /**
     * 根据id列表查询
     *
     * @param workoutListReq workoutListReq
     * @param isV1
     * @param planTypeCode
     * @return list
     */
    List<ProjPlanWorkoutListVO> findListV2(ProjYogaAutoWorkoutListReq workoutListReq, boolean isV1, Integer planTypeCode);

    List<ProjYogaAutoWorkoutRefreshVO> findList(ProjYogaAutoWorkoutListReq workoutListReq);

    /**
     *
     * 按照条件替换workout list中前三个workout
     *  当Difficulty参数 = Newbie时，替换前3个Workout：
     * （1）duration参数 = 5~10mins，替换为ABC（用于替换的workout id待定）;
     * （2）duration参数 = 10~20mins，替换为ABC（用于替换的workout id待定）;
     * （3）duration参数 = 20mins+，替换为ABC（用于替换的workout id待定）;
     * （4）m3u8Type = 1时，覆盖为2532的m3u8地址
     *
     * @param planReq
     * @param workoutListVOList
     * @return
     */
    List<ProjYogaAutoWorkoutListVO> replaceWorkout(ProjYogaAutoWorkoutPlanReq planReq, List<ProjYogaAutoWorkoutListVO> workoutListVOList);
}
