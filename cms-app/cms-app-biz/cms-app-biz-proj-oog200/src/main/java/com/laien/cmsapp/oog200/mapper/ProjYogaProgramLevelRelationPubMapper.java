package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramLevelRelationPub;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:47
 */
public interface ProjYogaProgramLevelRelationPubMapper extends BaseMapper<ProjYogaProgramLevelRelationPub> {

    @Select("<script>" +
            "SELECT * FROM proj_yoga_program_level_relation_pub " +
            "WHERE del_flag = 0 and version = #{version} and proj_yoga_program_level_id IN " +
            "<foreach collection='programLevelIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<ProjYogaProgramLevelRelationPub> listByProgramLevelIdAndStatus(@Param("programLevelIds") Collection<Integer> programLevelIds, Integer version);

}
