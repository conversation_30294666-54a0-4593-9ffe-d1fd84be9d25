package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaRegularWorkoutListBO;
import com.laien.cmsapp.oog200.bo.WorkoutCategoryRelationBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaRegularWorkoutPubMapper;
import com.laien.cmsapp.oog200.requst.ProjYogaRegularWorkoutListReq;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutGuidanceVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IProjClassicYogaVideoAndPoseRelationService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularCategoryService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularWorkoutPubService;
import com.laien.cmsapp.oog200.service.IResYogaVideoService;
import com.laien.cmsapp.oog200.util.YogaRegularWorkoutUtil;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.*;
import com.laien.common.util.MyStringUtil;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * oog200 workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Service
public class ProjYogaRegularWorkoutPubServiceImpl extends ServiceImpl<ProjYogaRegularWorkoutPubMapper, ProjYogaRegularWorkoutPub> implements IProjYogaRegularWorkoutPubService {

    @Resource
    private IResYogaVideoService yogaVideoService;

    @Resource
    private IProjClassicYogaVideoAndPoseRelationService poseVideoRelationService;

    @Resource
    private IProjYogaRegularCategoryService projYogaRegularCategoryService;

    @Resource
    I18nUtil i18nUtil;

    @Override
    public List<ProjYogaRegularWorkoutListVO> listYogaRegularWorkout(Set<Integer> categoryCodeSet) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();

        List<WorkoutCategoryRelationBO> workoutCategoryRelationList = projYogaRegularCategoryService.findWorkoutCategoryRelationList(categoryCodeSet, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA, versionInfoBO);
        if (CollUtil.isEmpty(workoutCategoryRelationList) && CollUtil.isNotEmpty(categoryCodeSet)) {
            log.error("list yoga regular workout not found");
            return new ArrayList<>();
        }
        Map<Integer, WorkoutCategoryRelationBO> workoutCategoryRelationMap = workoutCategoryRelationList.stream().collect(Collectors.toMap(WorkoutCategoryRelationBO::getWorkoutId, o-> o));

        LambdaQueryWrapper<ProjYogaRegularWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(categoryCodeSet), BaseModel::getId,workoutCategoryRelationMap.keySet())
                .eq(ProjYogaRegularWorkoutPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjYogaRegularWorkoutPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaRegularWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .orderByDesc(ProjYogaRegularWorkoutPub::getId);
        List<ProjYogaRegularWorkoutPub> list = this.list(queryWrapper);

        Map<Integer, Integer> workoutIdAndDifficultyCodeMap = getWorkoutIdAndDifficultyCodeMap(list);

        Map<String, SpecialLimitEnum> specialLimitMap = Arrays.stream(SpecialLimitEnum.values()).collect(Collectors.toMap(
                SpecialLimitEnum::getName,
                specialLimit -> specialLimit,
                (existing, replacement) -> existing
        ));
        Map<String, YogaTypeEnum> yogaTypeMap = Arrays.stream(YogaTypeEnum.values())
                .collect(Collectors.toMap(
                        YogaTypeEnum::getName,
                        yogaTypeEnum -> yogaTypeEnum,
                        (existing, replacement) -> existing
                ));

        List<ProjYogaRegularWorkoutListVO> workoutListVOList =  list.stream().map(pub -> {
            ProjYogaRegularWorkoutListVO vo = new ProjYogaRegularWorkoutListVO();
            BeanUtils.copyProperties(pub, vo);
            WorkoutCategoryRelationBO workoutCategoryRelation = workoutCategoryRelationMap.get(pub.getId());
            if(null != workoutCategoryRelation) {
                Set<Integer> codeSet = workoutCategoryRelation.getCategoryCodeSet();
                if (CollUtil.isNotEmpty(codeSet)) {
                    vo.setCategoryCodeList(new ArrayList<>(codeSet));
                }
            }
            vo.setDifficultyCode(workoutIdAndDifficultyCodeMap.get(pub.getId()));
            vo.setDuration(TimeConvertUtil.millisToMinutes(pub.getDuration()));
            List<Integer> yogaTypeCodeList = new ArrayList<>();
            for (String yogaType : MyStringUtil.getSplitWithComa(pub.getYogaType())) {
                YogaTypeEnum yogaTypeEnum = yogaTypeMap.getOrDefault(yogaType, null);
                if (null != yogaTypeEnum) {
                    yogaTypeCodeList.add(yogaTypeEnum.getCode());
                }
            }

            vo.setYogaTypeCodeList(yogaTypeCodeList);
            List<Integer> specialLimitCodeList = new ArrayList<>();
            for (String specialLimit : MyStringUtil.getSplitWithComa(pub.getSpecialLimit())) {
                SpecialLimitEnum specialLimitEnum = specialLimitMap.getOrDefault(specialLimit, null);
                if (null != specialLimitEnum) {
                    specialLimitCodeList.add(specialLimitEnum.getCode());
                }
            }
            vo.setSpecialLimitCodeList(specialLimitCodeList);
            return vo;
        }).collect(Collectors.toList());

        // 翻译i18n字段
        i18nUtil.translate(workoutListVOList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return workoutListVOList;
    }

    @Override
    public List<ProjYogaRegularWorkoutListVO> listYogaRegularWorkoutV2(Set<Integer> categoryCodeSet) {
        List<ProjYogaRegularWorkoutListVO> workoutListVOList = listYogaRegularWorkout(categoryCodeSet);
        if (!CollectionUtils.isEmpty(workoutListVOList)) {
            workoutListVOList.forEach(workout -> workout.setWorkoutTypeCode(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode()));
        }
        return workoutListVOList;
    }

    @Override
    public List<ProjYogaRegularWorkoutListVO> list(ProjYogaRegularWorkoutListReq workoutListReq) {
        Set<Integer> categoryCodeSet = new HashSet<>();
        categoryCodeSet.add(GlobalConstant.TOP_PICKS_CATEGORY_CODE);
        List<ProjYogaRegularWorkoutListVO> workoutList = listYogaRegularWorkout(categoryCodeSet);
        if (CollUtil.isEmpty(workoutList)) {
            return workoutList;
        }
        List<Integer> yogaTypeCodeList = workoutListReq.getYogaTypeCodeList();
        List<Integer> specialLimitCodeList = workoutListReq.getSpecialLimitCodeList();
        if (CollUtil.isEmpty(yogaTypeCodeList) && CollUtil.isEmpty(specialLimitCodeList)) {
            return workoutList;
        }
        ProjYogaRegularWorkoutListBO matchedYogaTypeWorkout = new ProjYogaRegularWorkoutListBO();
        matchedYogaTypeWorkout.setMatchedSpecialLimitWorkoutList(new ArrayList<>());
        matchedYogaTypeWorkout.setUnmatchedSpecialLimitWorkoutList(new ArrayList<>());
        ProjYogaRegularWorkoutListBO unmatchedYogaTypeWorkoutList = new ProjYogaRegularWorkoutListBO();
        unmatchedYogaTypeWorkoutList.setMatchedSpecialLimitWorkoutList(new ArrayList<>());
        unmatchedYogaTypeWorkoutList.setUnmatchedSpecialLimitWorkoutList(new ArrayList<>());
        for (ProjYogaRegularWorkoutListVO workout : workoutList) {
            if (CollUtil.containsAny(yogaTypeCodeList, workout.getYogaTypeCodeList())) {
                if (CollUtil.intersection(specialLimitCodeList, workout.getSpecialLimitCodeList()).isEmpty()) {
                    matchedYogaTypeWorkout.getUnmatchedSpecialLimitWorkoutList().add(workout);
                } else {
                    matchedYogaTypeWorkout.getMatchedSpecialLimitWorkoutList().add(workout);
                }
            } else {
                if (CollUtil.intersection(specialLimitCodeList, workout.getSpecialLimitCodeList()).isEmpty()) {
                    unmatchedYogaTypeWorkoutList.getUnmatchedSpecialLimitWorkoutList().add(workout);
                } else {
                    unmatchedYogaTypeWorkoutList.getMatchedSpecialLimitWorkoutList().add(workout);
                }
            }
        }
        // Yoga Type相同数据中，不含用户选择的Special Limit排在前面；Yoga Type不相同数据中，不含用户选择的Special Limit排在前面；
        List<ProjYogaRegularWorkoutListVO> finalWorkoutList = new ArrayList<>(workoutList.size());
        finalWorkoutList.addAll(matchedYogaTypeWorkout.getUnmatchedSpecialLimitWorkoutList());
        finalWorkoutList.addAll(matchedYogaTypeWorkout.getMatchedSpecialLimitWorkoutList());

        finalWorkoutList.addAll(unmatchedYogaTypeWorkoutList.getUnmatchedSpecialLimitWorkoutList());
        finalWorkoutList.addAll(unmatchedYogaTypeWorkoutList.getMatchedSpecialLimitWorkoutList());
        return YogaRegularWorkoutUtil.filterByDataSource(YogaDataSourceEnum.TOP_PICKS, finalWorkoutList);
    }

    private Map<Integer, Integer> getWorkoutIdAndDifficultyCodeMap(List<ProjYogaRegularWorkoutPub> regularWorkoutPubList) {

        if (CollectionUtils.isEmpty(regularWorkoutPubList)) {
            return Collections.emptyMap();
        }

        return regularWorkoutPubList.stream().filter(workoutPub -> Objects.nonNull(DifficultyEnum.getByName(workoutPub.getDifficulty())))
                .collect(Collectors.toMap(ProjYogaRegularWorkoutPub::getId, workoutPub -> DifficultyEnum.getByName(workoutPub.getDifficulty()).getCode()));
    }

    @Override
    public ProjYogaRegularWorkoutDetailVO findDetailById(Integer id, Integer m3u8Type) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjYogaRegularWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaRegularWorkoutPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjYogaRegularWorkoutPub::getId, id);
        ProjYogaRegularWorkoutPub yogaRegularWorkoutFind = this.getOne(queryWrapper);

        if (Objects.isNull(yogaRegularWorkoutFind)) {
            return null;
        }

        Map<Integer, Integer> workoutIdAndDifficultyCodeMap = getWorkoutIdAndDifficultyCodeMap(Lists.newArrayList(yogaRegularWorkoutFind));

        ProjYogaRegularWorkoutDetailVO detailVO = new ProjYogaRegularWorkoutDetailVO();
        BeanUtils.copyProperties(yogaRegularWorkoutFind, detailVO);
        detailVO.setDifficultyCode(workoutIdAndDifficultyCodeMap.get(yogaRegularWorkoutFind.getId()));
        detailVO.setDuration(TimeConvertUtil.millisToMinutes(yogaRegularWorkoutFind.getDuration()));

        // 获取video列表
        List<ResYogaVideoDetailVO> videoList = yogaVideoService.listYogaVideo4RegularWorkoutAndSupportI18n(Collections.singletonList(id));
        videoList = handleVideoList(videoList);
        detailVO.setVideos(videoList);

        //替换m3u8地址
        YogaRegularWorkoutUtil.setVideoUrl(detailVO, m3u8Type);

        setGuidance(Lists.newArrayList(detailVO));
        // 翻译i18n字段
        i18nUtil.translate(Collections.singletonList(detailVO), ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return detailVO;
    }

    @Override
    public ProjYogaRegularWorkoutDetailVO findDetailByIdV2(Integer id, Integer m3u8Type) {

        ProjYogaRegularWorkoutDetailVO detailVO = findDetailById(id, m3u8Type);
        if (Objects.nonNull(detailVO) && !CollectionUtils.isEmpty(detailVO.getVideos())) {
            poseVideoRelationService.setRelationPoseWorkoutInfo(detailVO.getVideos(), YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode());
        }
        return detailVO;
    }

    @Override
    public List<ProjYogaRegularWorkoutDetailVO> listByIdsAndStatusV2(Set<Integer> workoutIds, String language, Integer status, Integer m3u8Type) {

        List<ProjYogaRegularWorkoutDetailVO> workoutDetailVOList = listByIdsAndStatus(workoutIds, language, status, m3u8Type);
        if (!CollectionUtils.isEmpty(workoutDetailVOList)) {
            workoutDetailVOList.forEach(workoutDetailVO -> workoutDetailVO.setWorkoutTypeCode(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode()));
            List<ResYogaVideoDetailVO> videoDetailVOList = workoutDetailVOList.stream().filter(detailVO -> !CollectionUtils.isEmpty(detailVO.getVideos())).map(ProjYogaRegularWorkoutDetailVO::getVideos).flatMap(Collection::stream).collect(Collectors.toList());
            poseVideoRelationService.setRelationPoseWorkoutInfo(videoDetailVOList, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode());
        }
        return workoutDetailVOList;
    }

    @Override
    public List<ProjYogaRegularWorkoutDetailVO> listByIdsAndStatus(Set<Integer> workoutIds, String language, Integer status, Integer m3u8Type) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return Collections.emptyList();
        }

        // 获取指定的workout list
        LambdaQueryWrapper<ProjYogaRegularWorkoutPub> queryWrapper = wrapQueryByIdsAndStatus(status, workoutIds);
        List<ProjYogaRegularWorkoutPub> regularWorkouts = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(regularWorkouts)) {
            return Collections.emptyList();
        }

        Map<Integer, Integer> workoutIdAndDifficultyCodeMap = getWorkoutIdAndDifficultyCodeMap(regularWorkouts);

        // 封装返回结果
        List<ProjYogaRegularWorkoutDetailVO> resultList = regularWorkouts.stream().map(workout -> {
            ProjYogaRegularWorkoutDetailVO detailVO = new ProjYogaRegularWorkoutDetailVO();
            BeanUtils.copyProperties(workout, detailVO);
            detailVO.setDifficultyCode(workoutIdAndDifficultyCodeMap.get(workout.getId()));
            detailVO.setDuration(TimeConvertUtil.millisToMinutes(workout.getDuration()));
            return detailVO;
        }).collect(Collectors.toList());

        // 获取workout对应的video list
        Set<Integer> idList = regularWorkouts.stream().map(ProjYogaRegularWorkoutPub::getId).collect(Collectors.toSet());
        List<ResYogaVideoDetailVO> yogaVideoDetailVOList = yogaVideoService.listYogaVideo4RegularWorkoutAndSupportI18n(idList);
        Map<Integer, List<ResYogaVideoDetailVO>> workoutIdVideoMap = yogaVideoDetailVOList.stream()
                .collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));

        resultList.forEach(result -> {
            if (workoutIdVideoMap.containsKey(result.getId())) {
                List<ResYogaVideoDetailVO> videoDetailVOList = workoutIdVideoMap.get(result.getId());
                videoDetailVOList.sort(Comparator.comparing(ResYogaVideoDetailVO::getSortId));
                result.setVideos(handleVideoList(videoDetailVOList));
            }
        });

        //替换m3u8地址
        resultList.forEach(result -> {
            YogaRegularWorkoutUtil.setVideoUrl(result, m3u8Type);
        });

        setGuidance(resultList);
        i18nUtil.translate(resultList, ProjCodeEnums.OOG200, language);
        return resultList;
    }

    private void setGuidance(List<ProjYogaRegularWorkoutDetailVO> workoutList) {

        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        for (ProjYogaRegularWorkoutDetailVO workoutListVO : workoutList) {
            ProjYogaAutoWorkoutGuidanceVO guidanceVO = ProjYogaAutoWorkoutGuidanceVO.buildGuidance(workoutListVO.getAudioLongJson(), workoutListVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE);
            workoutListVO.setGuidance(guidanceVO);
        }
    }

    private LambdaQueryWrapper<ProjYogaRegularWorkoutPub> wrapQueryByIdsAndStatus(Integer status, Set<Integer> workoutIds) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjYogaRegularWorkoutPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaRegularWorkoutPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.in(ProjYogaRegularWorkoutPub::getId, workoutIds);

        if (Objects.nonNull(status)) {
            queryWrapper.eq(ProjYogaRegularWorkoutPub::getStatus, status);
        }
        return queryWrapper;
    }

    private List<ProjYogaRegularWorkoutPub> filterByLang(List<ProjYogaRegularWorkoutPub> regularWorkoutPubList, String language) {

        return regularWorkoutPubList.stream().filter(ex -> {

            if (StringUtils.isEmpty(ex.getLanguage())) {
                return false;
            }

            List<String> langList = Arrays.asList(ex.getLanguage().split(GlobalConstant.COMMA));
            if (langList.contains(language) || langList.contains(GlobalConstant.DEFAULT_LANGUAGE)) {
                return true;
            }

            return false;
        }).collect(Collectors.toList());
    }

    private List<ResYogaVideoDetailVO> handleVideoList(List<ResYogaVideoDetailVO> videoList) {
        for (int i = videoList.size() - 1; i >= 0; i--) {
            if (i > 0) {
                videoList.get(i).setRealVideoDuration(videoList.get(i).getRealVideoDuration() + videoList.get(i - 1).getRealTransitionDuration());
                videoList.get(i).setRealTransitionDuration(videoList.get(i - 1).getRealTransitionDuration());
            } else {
                videoList.get(i).setRealTransitionDuration(0);
            }
        }
        return videoList;
    }

}
