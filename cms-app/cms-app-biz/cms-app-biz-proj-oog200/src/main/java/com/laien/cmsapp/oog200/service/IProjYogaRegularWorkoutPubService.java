package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutPub;
import com.laien.cmsapp.oog200.requst.ProjYogaRegularWorkoutListReq;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * oog200 workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
public interface IProjYogaRegularWorkoutPubService extends IService<ProjYogaRegularWorkoutPub> {

    /**
     * regular workout列表v1
     *
     * @return list
     */
    List<ProjYogaRegularWorkoutListVO> listYogaRegularWorkout(Set<Integer> categoryCodeSet);

    List<ProjYogaRegularWorkoutListVO> listYogaRegularWorkoutV2(Set<Integer> categoryCodeSet);

    List<ProjYogaRegularWorkoutListVO> list(ProjYogaRegularWorkoutListReq workoutListReq);

    /**
     * regular workout detail
     *
     * @param id       id
     * @param m3u8Type m3u8展示类型
     * @return ProjYogaRegularWorkoutDetailVO
     */
    ProjYogaRegularWorkoutDetailVO findDetailById(Integer id, Integer m3u8Type);

    ProjYogaRegularWorkoutDetailVO findDetailByIdV2(Integer id, Integer m3u8Type);

    List<ProjYogaRegularWorkoutDetailVO> listByIdsAndStatusV2(Set<Integer> workoutIds, String language, Integer status, Integer m3u8Type);


    /**
     *
     * 返回指定状态的workout 列表
     * lang是一个过滤，现阶段还没支持多语言，默认全是英语
     * 后续支持多语言时需要重写本方法
     *
     * @param workoutIds
     * @param language
     * @param status
     * @param m3u8Type m3u8展示类型
     * @return
     */
    List<ProjYogaRegularWorkoutDetailVO> listByIdsAndStatus(Set<Integer> workoutIds, String language, Integer status, Integer m3u8Type);

}
