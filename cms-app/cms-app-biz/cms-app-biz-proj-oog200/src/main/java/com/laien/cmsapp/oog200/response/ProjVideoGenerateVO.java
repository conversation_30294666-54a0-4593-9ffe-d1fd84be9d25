package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video", description = "video")
public class ProjVideoGenerateVO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "video url")
    private String videoUrl;
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;
    @ApiModelProperty(value = "title subtitles")
    private List<ProjVideoGenerateI18nVO> titleSubtitles;
    @ApiModelProperty(value = "guidance subtitles")
    private List<ProjVideoGenerateI18nVO> guidanceSubtitles;
}
