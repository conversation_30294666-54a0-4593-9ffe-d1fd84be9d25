package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog200.enums.YogaRegularCategoryTypeEnum;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * yoga regular category
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_regular_category_pub")
@ApiModel(value="ProjYogaRegularCategoryPub对象", description="yoga regular category")
public class ProjYogaRegularCategoryPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "yoga regular category类型")
    private YogaRegularCategoryTypeEnum type;

    @ApiModelProperty(value = "category code，小于1000的预留给python用")
    private Integer categoryCode;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
