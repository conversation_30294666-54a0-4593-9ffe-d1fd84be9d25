package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目poseLibrary
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjPoseLibraryPub对象", description="项目poseLibrary")
public class ProjPoseLibraryPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "pose library id")
    private Integer poseLibraryId;

    @ApiModelProperty(value = "启用状态 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "排序编号")
    private Integer sortNo;


}
