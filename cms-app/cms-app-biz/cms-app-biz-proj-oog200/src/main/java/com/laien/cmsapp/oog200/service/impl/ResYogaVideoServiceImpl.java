package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ResYogaVideo;
import com.laien.cmsapp.oog200.mapper.ProjYogaAutoWorkoutMapper;
import com.laien.cmsapp.oog200.mapper.ProjYogaRegularWorkoutPubMapper;
import com.laien.cmsapp.oog200.mapper.ResYogaVideoMapper;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IResYogaVideoService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.RequestContextUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 瑜伽视频 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Service
public class ResYogaVideoServiceImpl extends ServiceImpl<ResYogaVideoMapper, ResYogaVideo> implements IResYogaVideoService {

    @Resource
    ProjYogaAutoWorkoutMapper autoWorkoutMapper;

    @Resource
    ProjYogaRegularWorkoutPubMapper regularWorkoutPubMapper;

    @Resource
    I18nUtil i18nUtil;

    @Override
    public List<ResYogaVideoDetailVO> listYogaVideo4AutoWorkoutAndSupportI18n(Collection<Integer> workoutIdList) {

        if (CollectionUtils.isEmpty(workoutIdList)) {
            return Collections.emptyList();
        }

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ResYogaVideoDetailVO> videoDetailVOList = autoWorkoutMapper.selectWorkoutVideoListByIds(versionInfoBO, workoutIdList);

        if (CollectionUtils.isEmpty(videoDetailVOList)) {
            return Collections.emptyList();
        }
        i18nUtil.translate4Speech(videoDetailVOList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return videoDetailVOList;
    }

    @Override
    public List<ResYogaVideoDetailVO> listYogaVideo4RegularWorkoutAndSupportI18n(Collection<Integer> workoutIdList) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ResYogaVideoDetailVO> videoDetailVOList = regularWorkoutPubMapper.listByIdsAndVersion(versionInfoBO, workoutIdList);

        if (CollectionUtils.isEmpty(videoDetailVOList)) {
            return Collections.emptyList();
        }
        i18nUtil.translate4Speech(videoDetailVOList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return videoDetailVOList;
    }

}
