package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramPub;
import com.laien.cmsapp.oog200.response.ProjYogaProgramListVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:48
 */
public interface IProjYogaProgramPubService extends IService<ProjYogaProgramPub> {

    List<ProjYogaProgramListVO> listVOByIds(Collection<Integer> programIds, Integer version, Integer status, String lang);

}
