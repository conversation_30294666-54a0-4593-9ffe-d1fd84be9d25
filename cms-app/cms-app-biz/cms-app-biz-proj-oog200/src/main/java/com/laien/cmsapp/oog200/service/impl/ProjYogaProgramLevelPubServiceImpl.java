package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramLevelPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaProgramLevelPubMapper;
import com.laien.cmsapp.oog200.response.ProjYogaProgramLevelListVO;
import com.laien.cmsapp.oog200.service.IProjYogaProgramLevelPubService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/24 20:08
 */
@Slf4j
@Service
public class ProjYogaProgramLevelPubServiceImpl extends ServiceImpl<ProjYogaProgramLevelPubMapper, ProjYogaProgramLevelPub> implements IProjYogaProgramLevelPubService {

    @Override
    public List<ProjYogaProgramLevelListVO> listVOByIds(Collection<Integer> programLevelIds, Integer version, Integer status, String lang) {

        List<ProjYogaProgramLevelPub> programLevelPubList = listByStatusAndVersion(programLevelIds, status, version);
        if (CollectionUtils.isEmpty(programLevelPubList)) {
            return Collections.emptyList();
        }

        return programLevelPubList.stream().map(this::convert2ListVO).collect(Collectors.toList());
    }

    private ProjYogaProgramLevelListVO convert2ListVO(ProjYogaProgramLevelPub yogaProgramLevelPub) {

        ProjYogaProgramLevelListVO listVO = new ProjYogaProgramLevelListVO();
        BeanUtils.copyProperties(yogaProgramLevelPub, listVO);
        return listVO;
    }

    private List<ProjYogaProgramLevelPub> listByStatusAndVersion(Collection<Integer> programIds, Integer status, Integer version) {

        LambdaQueryWrapper<ProjYogaProgramLevelPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(programIds), ProjYogaProgramLevelPub::getId, programIds);
        queryWrapper.eq(Objects.nonNull(status), ProjYogaProgramLevelPub::getStatus, status);
        queryWrapper.eq(ProjYogaProgramLevelPub::getVersion, version);
        return list(queryWrapper);
    }
}
