package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppAudioSingleTranslateField;
import com.laien.common.domain.component.AppAudioCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yoga video info
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yoga video info", description = "yoga video info")
public class ResYogaVideoDetailVO implements AppAudioCoreI18nModel {

    @ApiModelProperty(value = "workout id 不返回", hidden = true)
    @JsonIgnore
    private Integer workoutId;

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @JsonIgnore
    @ApiModelProperty(value = "coreVoiceConfigI18nId")
    private Integer coreVoiceConfigI18nId;

    @ApiModelProperty(value = "video 对应的 poseWorkout id")
    private Integer poseWorkoutId;

    @ApiModelProperty(value = "0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private Integer poseWorkoutTypeCode;

    @JsonProperty("videoName")
    @ApiModelProperty(value = "动作展示名称")
    @AppAudioSingleTranslateField
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "真实的视频播放时长")
    private Integer realVideoDuration;

    @ApiModelProperty(value = "预览时长")
    private Integer previewVideoDuration;

    @ApiModelProperty(value = "真实的过渡时长")
    private Integer realTransitionDuration;

    @ApiModelProperty(value = "排序字段，根据业务需要使用不同的字段")
    @JsonIgnore
    private Integer sortId;

}
