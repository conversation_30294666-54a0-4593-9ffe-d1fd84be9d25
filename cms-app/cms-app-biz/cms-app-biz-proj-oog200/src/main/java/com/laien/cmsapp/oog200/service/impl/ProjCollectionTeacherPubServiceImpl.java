package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.entity.ProjCollectionTeacherPub;
import com.laien.cmsapp.mapper.ProjCollectionTeacherPubMapper;
import com.laien.cmsapp.service.IProjCollectionTeacherPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.RequestContextUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;

/**
 * <p>
 * 教练表(发布数据) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Service
public class ProjCollectionTeacherPubServiceImpl extends ServiceImpl<ProjCollectionTeacherPubMapper, ProjCollectionTeacherPub> implements IProjCollectionTeacherPubService {

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public ProjCollectionTeacherPub getOneByVersion(Integer id, Integer version) {
        if (id == null) {
            return null;
        }
        LambdaQueryWrapper<ProjCollectionTeacherPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjCollectionTeacherPub::getId, id);
        queryWrapper.eq(ProjCollectionTeacherPub::getVersion, version);
        queryWrapper.last("limit 1");

        ProjCollectionTeacherPub teacherPub = getOne(queryWrapper);
        if (Objects.isNull(teacherPub)) {
            return null;
        }
        i18nUtil.translate(Collections.singletonList(teacherPub), ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return teacherPub;
    }

}
