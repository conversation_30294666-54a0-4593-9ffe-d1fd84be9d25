package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjClassicYogaVideoPoseRelation;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/11/5 16:44
 */
public interface IProjClassicYogaVideoAndPoseRelationService extends IService<ProjClassicYogaVideoPoseRelation> {

    void setRelationPoseWorkoutInfo(List<ResYogaVideoDetailVO> classicYogaVideos, Integer workoutType);

    List<ProjClassicYogaVideoPoseRelation> listRelationByYogaVideoIds(Collection<Integer> yogaVideoIds);

}
