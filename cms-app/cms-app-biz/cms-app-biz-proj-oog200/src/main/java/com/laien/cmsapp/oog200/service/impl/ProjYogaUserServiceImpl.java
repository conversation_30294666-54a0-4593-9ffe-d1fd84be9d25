package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaUser;
import com.laien.cmsapp.oog200.mapper.ProjYogaUserMapper;
import com.laien.cmsapp.oog200.properties.Oog200Properties;
import com.laien.cmsapp.oog200.requst.ProjYogaUserReq;
import com.laien.cmsapp.oog200.response.ProjYogaUserInviteLinkVO;
import com.laien.cmsapp.oog200.response.ProjYogaUserVO;
import com.laien.cmsapp.oog200.service.IProjYogaUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: hhl
 * @date: 2025/6/6
 */
@Service
public class ProjYogaUserServiceImpl extends ServiceImpl<ProjYogaUserMapper, ProjYogaUser> implements IProjYogaUserService {

    @Resource
    private Oog200Properties oog200Properties;

    private final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private final int CODE_LENGTH = 6;
    private final SecureRandom RANDOM = new SecureRandom();

    @Override
    public ProjYogaUser getByAdaptyId(String adaptyId) {

        LambdaQueryWrapper<ProjYogaUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaUser::getAdaptyId, adaptyId);
        queryWrapper.last("limit 1");

        ProjYogaUser yogaUser = getOne(queryWrapper);
        return yogaUser;
    }

    @Override
    public List<ProjYogaUser> listByInviteCode(Collection<String> inviteCodes) {

        if (CollectionUtils.isEmpty(inviteCodes)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ProjYogaUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaUser::getInviteCode, inviteCodes);
        return list(queryWrapper);
    }

    @Override
    public ProjYogaUserVO createUser(ProjYogaUserReq req, ProjPublishCurrentVersionInfoBO versionInfoBO, int createCount) {

        ProjYogaUser existUser = getByAdaptyId(req.getThirdPartyId());
        if (Objects.nonNull(existUser)) {
            return convert2VO(existUser);
        }

        try{
            ProjYogaUser newUser = wrapUserInfo(req, versionInfoBO);
            save(newUser);
            return convert2VO(newUser);
        } catch (DuplicateKeyException e) {
            log.warn(e.getMessage());
            if (createCount > 3) {
                return null;
            } else {
                createCount += 1;
                return createUser(req, versionInfoBO, createCount);
            }
        }
    }

    private ProjYogaUser wrapUserInfo(ProjYogaUserReq req, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        ProjYogaUser newUser = new ProjYogaUser();
        newUser.setAdaptyId(req.getThirdPartyId());
        newUser.setInviteCode(generateInviteCode());

        newUser.setCreateTime(LocalDateTime.now());
        newUser.setCreateUser("System");
        newUser.setProjId(versionInfoBO.getProjId());
        return newUser;
    }

    private ProjYogaUserVO convert2VO(ProjYogaUser yogaUser) {

        ProjYogaUserVO yogaUserVO = new ProjYogaUserVO();
        yogaUserVO.setId(yogaUser.getId());
        return yogaUserVO;
    }

    @Override
    public ProjYogaUserInviteLinkVO createInviteLink(Integer userId) {

        if (StringUtils.isBlank(oog200Properties.getAppsFlyerLink())) {
            log.error("Miss appsFlyerLink config for oog200, please check.");
            return null;
        }

        ProjYogaUser yogaUser = getById(userId);
        if (Objects.isNull(yogaUser)) {
            return null;
        }

        String inviteLink = oog200Properties.getAppsFlyerLink() + yogaUser.getInviteCode();
        return new ProjYogaUserInviteLinkVO(inviteLink);
    }

    private String generateInviteCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = RANDOM.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(index));
        }
        return code.toString();
    }

}
