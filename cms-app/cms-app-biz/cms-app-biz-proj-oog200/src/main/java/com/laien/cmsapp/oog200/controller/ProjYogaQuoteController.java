package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjYogaQuoteVO;
import com.laien.cmsapp.oog200.service.IProjYogaQuotePubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * yoga名言警句 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */

@Api(tags = "app端：yogaQuote")
@RestController
@RequestMapping(value = {"/oog200/yogaQuote", "/OOG200/yogaQuote"})
public class ProjYogaQuoteController extends ResponseController {

    @Resource
    private IProjYogaQuotePubService projYogaQuotePubService;


    @ApiOperation(value = "list")
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjYogaQuoteVO>> list() {
        String language = RequestContextUtils.getLanguage();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projYogaQuotePubService.list(versionInfoBO, language));
    }
}
