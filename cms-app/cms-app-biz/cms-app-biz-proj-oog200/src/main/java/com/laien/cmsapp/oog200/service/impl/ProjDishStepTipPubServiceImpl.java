package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjDishStepTipPub;
import com.laien.cmsapp.oog200.mapper.ProjDishStepTipPubMapper;
import com.laien.cmsapp.oog200.response.ProjDishStepTipVO;
import com.laien.cmsapp.oog200.service.IProjDishStepTipPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/8 16:43
 */
@Service
public class ProjDishStepTipPubServiceImpl extends ServiceImpl<ProjDishStepTipPubMapper, ProjDishStepTipPub> implements IProjDishStepTipPubService {

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjDishStepTipVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang) {

        LambdaQueryWrapper<ProjDishStepTipPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjDishStepTipPub::getProjDishId, dishId);
        queryWrapper.eq(ProjDishStepTipPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjDishStepTipPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.orderByAsc(ProjDishStepTipPub::getId);

        List<ProjDishStepTipPub> dishStepTipPubList = list(queryWrapper);
        if (CollectionUtils.isEmpty(dishStepTipPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(dishStepTipPubList, ProjCodeEnums.OOG200, lang);
        return dishStepTipPubList.stream().map(step -> convert2VO(step)).collect(Collectors.toList());
    }

    private ProjDishStepTipVO convert2VO(ProjDishStepTipPub projDishStepTipPub) {

        ProjDishStepTipVO projDishStepTipVO = new ProjDishStepTipVO();
        BeanUtils.copyProperties(projDishStepTipPub, projDishStepTipVO);
        return projDishStepTipVO;
    }
}
