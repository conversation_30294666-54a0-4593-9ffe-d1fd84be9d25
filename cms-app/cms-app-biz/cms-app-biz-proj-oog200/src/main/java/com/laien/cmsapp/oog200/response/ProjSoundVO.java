package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.cmsapp.oog200.entity.ProjSound;
import com.laien.common.util.FireBaseUrlSubUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "Sound VO", description = "Sound VO")
public class ProjSoundVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "声音名称")
    private String soundName;

    @ApiModelProperty(value = "声音脚本")
    private String soundScript;

    @ApiModelProperty(value = "文件地址")
    @AbsoluteR2Url
    private String soundUrl;

    @ApiModelProperty(value = "文件名称")
    private String soundUrlName;

    public ProjSoundVO(ProjSound projSound) {
        this.id = projSound.getId();
        this.soundName = projSound.getSoundName();
        this.soundScript = projSound.getSoundScript();
        this.soundUrl = projSound.getUrl();
        this.soundUrlName = FireBaseUrlSubUtils.getFileName(projSound.getUrl());
    }
}
