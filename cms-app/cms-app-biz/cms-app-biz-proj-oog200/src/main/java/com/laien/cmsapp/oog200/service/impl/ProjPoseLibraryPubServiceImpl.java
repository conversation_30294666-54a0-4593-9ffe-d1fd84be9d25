package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjPoseLibraryPub;
import com.laien.cmsapp.oog200.mapper.ProjPoseLibraryPubMapper;
import com.laien.cmsapp.oog200.service.IProjPoseLibraryPubService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 项目poseLibrary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Service
public class ProjPoseLibraryPubServiceImpl extends ServiceImpl<ProjPoseLibraryPubMapper, ProjPoseLibraryPub> implements IProjPoseLibraryPubService {

}
