package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value = "Pose library list res", description = "Pose library list res")
public class ProjPoseLibraryPubListVO {

//    @ApiModelProperty(value = "分类列表")
//    private List<ProjPoseLibraryPubListItemVO> items;

    @ApiModelProperty(value = "pose Library列表")
    private List<ProjPoseLibraryPubListItemSubVO> poseLibrarys;

}
