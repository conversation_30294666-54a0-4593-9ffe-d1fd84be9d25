package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjChairYogaVideo;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/10/14 14:57
 */
public interface IProjChairYogaVideoService extends IService<ProjChairYogaVideo> {

    List<ResYogaVideoDetailVO> listVideo4Res(Collection<Integer> workoutIds);

    List<ResYogaVideoDetailVO> listVideo4ResAndRegularWorkout(Collection<Integer> workoutIds);

}
