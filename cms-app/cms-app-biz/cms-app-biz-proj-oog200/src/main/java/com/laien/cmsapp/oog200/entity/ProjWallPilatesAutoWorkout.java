package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * Wall pilates auto workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_wall_pilates_auto_workout")
@ApiModel(value="ProjWallPilatesAutoWorkout对象", description="Wall pilates auto workout")
public class ProjWallPilatesAutoWorkout extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "projYogaAutoWorkoutTemplateId")
    private Integer projYogaAutoWorkoutTemplateId;

    @ApiModelProperty(value = "projYogaAutoWorkoutTaskId")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "Upper Body、Abs & Core、Lower Body")
    private String target;

    @ApiModelProperty(value = "Standing、Lying")
    private String position;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "Video 的2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioShortJson;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
