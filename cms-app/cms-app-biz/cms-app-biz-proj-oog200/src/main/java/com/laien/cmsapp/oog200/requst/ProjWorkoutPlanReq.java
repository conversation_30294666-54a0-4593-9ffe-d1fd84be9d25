package com.laien.cmsapp.oog200.requst;

import com.laien.cmsapp.requst.LangReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.LinkedHashSet;

/**
 * note: yogaAutoWorkout plan
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "yogaAutoWorkout plan", description = "yogaAutoWorkout plan")
public class ProjWorkoutPlanReq extends LangReq {

    @ApiModelProperty(value = "1:0-10分钟，2:10-20分钟，3:20分钟以上", required = true)
    private Integer durationCode;

    @ApiModelProperty(value = "0:Newbie,1:Beginner,2:Intermediate,3:Advanced",example = "0")
    private Integer difficultyCode;

    @ApiModelProperty(value = "0:goal basic,1:weight-loss,2:flexibility,3:mindfulness", example = "0")
    private Integer goalCode;

    @ApiModelProperty(value = "random 20以内整数")
    private Integer random;

    @ApiModelProperty(value = "传1 查询2532 m3u8, 传2 查询dynamic m3u8, 默认1")
    private Integer m3u8Type = 1;

    @ApiModelProperty(value = "1:Upper Body,2:Abs & Core,3:Lower Body")
    private LinkedHashSet<Integer> targetCodeSet;

    @ApiModelProperty(value = "0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga", required = true)
    private Integer planTypeCode;

}
