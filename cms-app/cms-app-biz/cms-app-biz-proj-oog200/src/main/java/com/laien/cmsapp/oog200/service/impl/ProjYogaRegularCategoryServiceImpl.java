package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.WorkoutCategoryRelationBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularCategoryPub;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutCategoryRelationPub;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.cmsapp.oog200.mapper.ProjYogaRegularCategoryPubMapper;
import com.laien.cmsapp.oog200.service.IProjYogaRegularCategoryService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularWorkoutCategoryRelationService;
import com.laien.common.constant.GlobalConstant;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * yoga regular category 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
public class ProjYogaRegularCategoryServiceImpl extends ServiceImpl<ProjYogaRegularCategoryPubMapper, ProjYogaRegularCategoryPub> implements IProjYogaRegularCategoryService {

    @Resource
    private IProjYogaRegularWorkoutCategoryRelationService projYogaRegularWorkoutCategoryRelationService;
    @Override
    public List<WorkoutCategoryRelationBO> findWorkoutCategoryRelationList(Set<Integer> categoryCodeSet, YogaAutoWorkoutTemplateEnum typeEnum, ProjPublishCurrentVersionInfoBO versionInfo) {
        List<ProjYogaRegularCategoryPub> categoryList = query(categoryCodeSet, versionInfo);
        if (CollUtil.isEmpty(categoryList)) {
            return new ArrayList<>();
        }
        Set<Integer> categoryIdSet = categoryList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(categoryIdSet)) {
            return new ArrayList<>();
        }
        List<ProjYogaRegularWorkoutCategoryRelationPub> relationList = projYogaRegularWorkoutCategoryRelationService.query(categoryIdSet, typeEnum, versionInfo);
        if (CollUtil.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        Set<Integer> workoutIdSet = relationList.stream().map(ProjYogaRegularWorkoutCategoryRelationPub::getWorkoutId).collect(Collectors.toSet());
        List<ProjYogaRegularWorkoutCategoryRelationPub> allRelationList = projYogaRegularWorkoutCategoryRelationService.queryByWorkoutId(workoutIdSet, typeEnum, versionInfo);
        List<ProjYogaRegularCategoryPub> allCategoryList = query(null, versionInfo);
        Map<Integer, ProjYogaRegularCategoryPub> categoryMap = allCategoryList.stream().collect(Collectors.toMap(BaseModel::getId, o-> o));
        // allCategoryList.stream().
        Map<Integer, WorkoutCategoryRelationBO> categoryRelationMap = new HashMap<>();
        Map<Integer, List<ProjYogaRegularWorkoutCategoryRelationPub>> relationMap = allRelationList.stream().collect(Collectors.groupingBy(ProjYogaRegularWorkoutCategoryRelationPub::getWorkoutId));
        for (Integer workoutId : workoutIdSet) {
            WorkoutCategoryRelationBO categoryRelationBO = categoryRelationMap.getOrDefault(workoutId, new WorkoutCategoryRelationBO());
            categoryRelationBO.setWorkoutId(workoutId);
            Set<Integer> codeSet = categoryRelationBO.getCategoryCodeSet();
            if (CollUtil.isEmpty(codeSet)) {
                codeSet = new HashSet<>();
            }
            List<ProjYogaRegularWorkoutCategoryRelationPub> categoryRelationList = relationMap.get(workoutId);
            for (ProjYogaRegularWorkoutCategoryRelationPub relation : categoryRelationList) {
                ProjYogaRegularCategoryPub category = categoryMap.get(relation.getProjYogaRegularCategoryId());
                if (null != category && null != category.getCategoryCode()) {
                    codeSet.add(category.getCategoryCode());
                }
            }
            categoryRelationBO.setCategoryCodeSet(codeSet);
            categoryRelationMap.put(workoutId, categoryRelationBO);
        }
        return new ArrayList<>(categoryRelationMap.values());
    }

    private List<ProjYogaRegularCategoryPub> query(Set<Integer> categoryCodeSet, ProjPublishCurrentVersionInfoBO versionInfo) {
        LambdaQueryWrapper<ProjYogaRegularCategoryPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaRegularCategoryPub::getVersion, versionInfo.getCurrentVersion())
                .eq(ProjYogaRegularCategoryPub::getProjId, versionInfo.getProjId())
                .in(CollUtil.isNotEmpty(categoryCodeSet), ProjYogaRegularCategoryPub::getCategoryCode, categoryCodeSet)
                .eq(ProjYogaRegularCategoryPub::getStatus, GlobalConstant.STATUS_ENABLE);
        return baseMapper.selectList(wrapper);
    }
}
