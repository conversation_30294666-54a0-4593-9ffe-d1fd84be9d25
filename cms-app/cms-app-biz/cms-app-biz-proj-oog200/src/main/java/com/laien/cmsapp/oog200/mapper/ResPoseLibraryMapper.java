package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ResPoseLibrary;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * pose表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public interface ResPoseLibraryMapper extends BaseMapper<ResPoseLibrary> {

    @Select("select * from res_pose_library where id in (${idsStr}) order by pose_name")
    List<ResPoseLibrary> selectListByIds(@Param("idsStr") String idsStr);
}
