package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaMusicPub;
import com.laien.cmsapp.oog200.entity.ProjYogaPlaylistPub;
import com.laien.cmsapp.oog200.entity.ProjYogaPlaylistRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaPlaylistPubMapper;
import com.laien.cmsapp.oog200.response.ProjYogaPlaylistRelationVO;
import com.laien.cmsapp.oog200.response.ProjYogaPlaylistVO;
import com.laien.cmsapp.oog200.service.IProjYogaMusicPubService;
import com.laien.cmsapp.oog200.service.IProjYogaPlaylistPubService;
import com.laien.cmsapp.oog200.service.IProjYogaPlaylistRelationPubService;
import com.laien.cmsapp.oog200.util.YogaRegularWorkoutUtil;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.FireBaseUrlSubUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/2/21 12:00
 */
@Service
public class ProjYogaPlaylistPubServiceImpl extends ServiceImpl<ProjYogaPlaylistPubMapper, ProjYogaPlaylistPub> implements IProjYogaPlaylistPubService {

    @Resource
    private IProjYogaMusicPubService musicPubService;

    @Resource
    private IProjYogaPlaylistRelationPubService playlistRelationPubService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjYogaPlaylistVO> listByPlaylistCodes(ProjPublishCurrentVersionInfoBO versionInfoBO, List<Integer> playlistCodes, Integer status, String lang) {

        List<ProjYogaPlaylistPub> playlistPubList = listByStatusAndVersion(versionInfoBO, playlistCodes, status);
        if (CollectionUtils.isEmpty(playlistPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(playlistPubList, ProjCodeEnums.OOG200, lang);
        List<ProjYogaPlaylistVO> playlistVOList = playlistPubList.stream().map(this::convert2ListVO).collect(Collectors.toList());

        // 查找关联
        List<Integer> playlistIds = playlistPubList.stream().map(ProjYogaPlaylistPub::getId).collect(Collectors.toList());
        List<ProjYogaPlaylistRelationPub> relationPubList = playlistRelationPubService.listByPlaylistIds(versionInfoBO, playlistIds);
        if (CollectionUtils.isEmpty(relationPubList)) {
            return playlistVOList;
        }

        // 查找Music
        Set<Integer> musicIds = relationPubList.stream().map(ProjYogaPlaylistRelationPub::getProjYogaMusicId).collect(Collectors.toSet());
        List<ProjYogaMusicPub> yogaMusicPubList = musicPubService.listByStatusAndVersion(versionInfoBO, musicIds, GlobalConstant.STATUS_ENABLE, lang);
        if (CollectionUtils.isEmpty(yogaMusicPubList)) {
            return playlistVOList;
        }

        // 组装歌单
        Map<Integer, ProjYogaMusicPub> musicPubMap = yogaMusicPubList.stream().collect(Collectors.toMap(ProjYogaMusicPub::getId, p -> p));
        Map<Integer, List<ProjYogaPlaylistRelationPub>> playlistAndRelationMap = relationPubList.stream().collect(Collectors.groupingBy(ProjYogaPlaylistRelationPub::getProjYogaPlaylistId));
        playlistVOList.stream().forEach(playlistVO -> {
            if (!playlistAndRelationMap.containsKey(playlistVO.getId())) {
                return;
            }

            List<ProjYogaPlaylistRelationVO> musicList = playlistAndRelationMap.get(playlistVO.getId()).stream().filter(relation -> musicPubMap.containsKey(relation.getProjYogaMusicId()))
                    .map(relation -> convert2RelationVO(relation, musicPubMap.get(relation.getProjYogaMusicId()))).collect(Collectors.toList());
            playlistVO.setMusicList(musicList);
        });

        return playlistVOList;
    }

    private List<ProjYogaPlaylistPub> listByStatusAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, List<Integer> playlistCodes, Integer status) {

        LambdaQueryWrapper<ProjYogaPlaylistPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPlaylistPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjYogaPlaylistPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjYogaPlaylistPub::getStatus, status);

        queryWrapper.in(ProjYogaPlaylistPub::getPlaylistType, playlistCodes);
        queryWrapper.orderByDesc(ProjYogaPlaylistPub::getSortNo);
        queryWrapper.orderByDesc(ProjYogaPlaylistPub::getId);
        return list(queryWrapper);
    }

    ProjYogaPlaylistRelationVO convert2RelationVO(ProjYogaPlaylistRelationPub relationPub, ProjYogaMusicPub musicPub) {

        ProjYogaPlaylistRelationVO relationVO = new ProjYogaPlaylistRelationVO();
        BeanUtils.copyProperties(musicPub, relationVO);
        relationVO.setMusicId(musicPub.getId());
        relationVO.setAudioName(FireBaseUrlSubUtils.getFileName(musicPub.getAudio()));

        relationVO.setRelationId(relationPub.getId());
        relationVO.setShortLink(relationPub.getShortLink());
        relationVO.setDisplayName(relationPub.getDisplayName());
        relationVO.setSubscription(YogaRegularWorkoutUtil.setSubscription(relationPub.getSubscription()));
        return relationVO;
    }


    private ProjYogaPlaylistVO convert2ListVO(ProjYogaPlaylistPub projYogaPlaylistPub) {

        ProjYogaPlaylistVO projYogaPlaylistVO = new ProjYogaPlaylistVO();
        BeanUtils.copyProperties(projYogaPlaylistPub, projYogaPlaylistVO);
        projYogaPlaylistVO.setPlaylistTypeCode(projYogaPlaylistPub.getPlaylistType().getCode());
        projYogaPlaylistVO.setSubscription(YogaRegularWorkoutUtil.setSubscription(projYogaPlaylistPub.getSubscription()));
        return projYogaPlaylistVO;
    }
}
