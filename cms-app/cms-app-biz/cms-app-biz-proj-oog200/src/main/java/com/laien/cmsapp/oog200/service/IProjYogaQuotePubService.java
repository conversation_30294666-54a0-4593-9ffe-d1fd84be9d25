package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaQuotePub;
import com.laien.cmsapp.oog200.response.ProjYogaQuoteVO;

import java.util.List;

/**
 * <p>
 * yoga名言警句 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface IProjYogaQuotePubService extends IService<ProjYogaQuotePub> {

    List<ProjYogaQuoteVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO,String language);

}
