package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.constant.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * note: yogaAutoWorkout id 查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "yogaAutoWorkout id 查询", description = "yogaAutoWorkout id 查询")
public class ProjYogaAutoWorkoutRefreshVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "难度")
    private String difficulty;

    @ApiModelProperty(value = "0: Newbie,1: <PERSON><PERSON><PERSON>,2: Intermediate,3: Advanced")
    private Integer difficultyCode;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长 分钟")
    private Integer duration;

    @JsonProperty("videoUrl")
    @AbsoluteR2Url
    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @JsonIgnore
    @ApiModelProperty(value = "video的2532 m3u8地址")
    private String video2532Url;

    @JsonIgnore
    @ApiModelProperty(value = "音频json，仅guidance", hidden = true)
    private String audioLongJson;

    @JsonIgnore
    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称", hidden = true)
    private String audioShortJson;

    @ApiModelProperty(value = "video list")
    private List<ResYogaVideoDetailVO> videos;

    @ApiModelProperty(value = "guidance object")
    private ProjYogaAutoWorkoutGuidanceVO guidance;

    // 转换为移动端的需要的结构
    public ProjYogaAutoWorkoutGuidanceVO getGuidance() {
        ProjYogaAutoWorkoutGuidanceVO guidanceVO = new ProjYogaAutoWorkoutGuidanceVO();
        List<ProjYogaAutoWorkoutGuidanceItemVO> default_ = new ArrayList<>();
        List<ProjYogaAutoWorkoutGuidanceItemVO> least = new ArrayList<>();
        ProjYogaAutoWorkoutGuidanceItemVO detailLongVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailLongVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
        detailLongVO.setAudioJsonUrl(audioLongJson);

        ProjYogaAutoWorkoutGuidanceItemVO detailShortVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailShortVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
        detailShortVO.setAudioJsonUrl(audioShortJson);
        default_.add(detailLongVO);
        least.add(detailShortVO);

        guidanceVO.setDefault_(default_);
        guidanceVO.setLeast(least);
        return guidanceVO;
    }
}
