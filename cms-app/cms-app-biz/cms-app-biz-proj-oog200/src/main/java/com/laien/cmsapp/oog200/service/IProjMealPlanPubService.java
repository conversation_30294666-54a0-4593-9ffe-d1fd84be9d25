package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjMealPlanPub;
import com.laien.cmsapp.oog200.response.ProjMealPlanDetailVO;
import com.laien.cmsapp.oog200.response.ProjMealPlanListVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:54
 */
public interface IProjMealPlanPubService extends IService<ProjMealPlanPub> {

    List<ProjMealPlanListVO> listMealPlan(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang);

    ProjMealPlanDetailVO getMealPlanDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer mealPlanId, String lang);

}
