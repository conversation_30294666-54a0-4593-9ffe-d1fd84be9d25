package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjFastingArticlePub;
import com.laien.cmsapp.oog200.mapper.ProjFastingArticlePubMapper;
import com.laien.cmsapp.oog200.response.ProjFastingArticleDetailVO;
import com.laien.cmsapp.oog200.response.ProjFastingArticleListVO;
import com.laien.cmsapp.oog200.service.IProjFastingArticlePubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.FastingArticleEnum;
import com.laien.common.util.RequestContextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Fasting article 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
public class ProjFastingArticlePubServiceImpl extends ServiceImpl<ProjFastingArticlePubMapper, ProjFastingArticlePub> implements IProjFastingArticlePubService {

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjFastingArticleListVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFastingArticlePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFastingArticlePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjFastingArticlePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjFastingArticlePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .orderByAsc(ProjFastingArticlePub::getSorted)
                .orderByDesc(ProjFastingArticlePub::getId);
        List<ProjFastingArticlePub> fastingArticleList = baseMapper.selectList(wrapper);
        List<ProjFastingArticleListVO> articleVOList = new ArrayList<>();
        if (CollUtil.isEmpty(fastingArticleList)) {
            return articleVOList;
        }

        i18nUtil.translate(fastingArticleList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        for (ProjFastingArticlePub article : fastingArticleList) {
            ProjFastingArticleListVO articleVO = new ProjFastingArticleListVO();
            BeanUtils.copyProperties(article, articleVO);
            FastingArticleEnum type = article.getType();
            if (null != type) {
                articleVO.setTypeCode(type.getCode());
            }
            articleVOList.add(articleVO);
        }
        return articleVOList;
    }

    @Override
    public ProjFastingArticleDetailVO find(Integer id, ProjPublishCurrentVersionInfoBO versionInfoBO) {
        LambdaQueryWrapper<ProjFastingArticlePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjFastingArticlePub::getVersion, versionInfoBO.getCurrentVersion());
        wrapper.eq(ProjFastingArticlePub::getId, id);
        ProjFastingArticlePub fastingArticle = baseMapper.selectOne(wrapper);
        if (Objects.isNull(fastingArticle)) {
            return null;
        }
        i18nUtil.translate(Lists.newArrayList(fastingArticle), ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        ProjFastingArticleDetailVO articleDetailVO = new ProjFastingArticleDetailVO();
        BeanUtils.copyProperties(fastingArticle, articleDetailVO);
        FastingArticleEnum type = fastingArticle.getType();
        if (null != type) {
            articleDetailVO.setTypeCode(type.getCode());
        }
        return articleDetailVO;

    }
}
