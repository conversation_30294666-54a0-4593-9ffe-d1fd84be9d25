package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * note: workout Video detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout Video detail", description = "workout Video detail")
public class ProjYogaRegularWorkoutListVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private Integer workoutTypeCode;

    @AbsoluteR2Url
    @ApiModelProperty(value = "多分辨率m3u8视频地址，用于IOS端下载")
    private String multiVideoUrl;

    @JsonIgnore
    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "难度")
    @AppTextTranslateField
    private String difficulty;

    @ApiModelProperty(value = "0: Newbie,1: Beginner,2: Intermediate,3: Advanced")
    private Integer difficultyCode;

    @ApiModelProperty(value = "workout中包含的视频个数")
    private Integer videoCount;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长 分钟")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(name = "yogaTypeCodeList", value = "0:Classic Yoga、1:Lazy Yoga、2:Somatic Yoga、3:Chair Yoga、4:Wall Pilates、5:Other")
    private List<Integer> yogaTypeCodeList;

    @ApiModelProperty(name = "specialLimitCodeList", value = "0: All Good,1: Sensitive Wrist,2: Back Pain,3: Knee Issues,4: Overweight,5: Elderly No,6: No plank,7: Pregnancy or postpartum ")
    private List<Integer> specialLimitCodeList;

    @JsonIgnore
    @ApiModelProperty(value = "数据使用范围，多个用英文逗号分隔,0:Top Picks,1:Program")
    private String dataSources;

    @ApiModelProperty(value = "category code集合")
    private List<Integer> categoryCodeList;

}
