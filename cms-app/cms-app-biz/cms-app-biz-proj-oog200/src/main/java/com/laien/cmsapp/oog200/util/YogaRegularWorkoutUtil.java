package com.laien.cmsapp.oog200.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/23
 */
public class YogaRegularWorkoutUtil {

    /**
     * @param dataSourceEnum
     * @param workoutList
     * @return
     */
    public static List<ProjYogaRegularWorkoutListVO> filterByDataSource(YogaDataSourceEnum dataSourceEnum, List<ProjYogaRegularWorkoutListVO> workoutList) {
        if (CollUtil.isEmpty(workoutList)) {
            return workoutList;
        }
        if (null == dataSourceEnum) {
            return workoutList;
        }
        ArrayList<ProjYogaRegularWorkoutListVO> finalWorkout = new ArrayList<>(workoutList.size());
        for (ProjYogaRegularWorkoutListVO workout : workoutList) {
            String dataSources = workout.getDataSources();
            List<YogaDataSourceEnum> yogaDataSourceEnumList = YogaDataSourceEnum.convertToYogaDataSourceEnum(dataSources);
            if (yogaDataSourceEnumList.contains(dataSourceEnum)) {
                finalWorkout.add(workout);
            }
        }
        return finalWorkout;
    }

    /**
     * 根据m3u8Type设置合适的VideoUrl
     *
     * @param workoutDetailVO
     * @param m3u8Type
     */
    public static void setVideoUrl(ProjYogaRegularWorkoutDetailVO workoutDetailVO, Integer m3u8Type) {

        if (Objects.isNull(workoutDetailVO)) {
            return;
        }

        // 设置多分辨率M3u8，用于IOS下载
        workoutDetailVO.setMultiVideoUrl(workoutDetailVO.getVideoM3u8Url());

        // 设置2532分辨率M3u8,用于IOS播放
        if (ObjectUtil.notEqual(GlobalConstant.TWO, m3u8Type) && StrUtil.isNotBlank(workoutDetailVO.getVideo2532Url())) {
            workoutDetailVO.setVideoM3u8Url(workoutDetailVO.getVideo2532Url());
        }
    }

    /**
     * 设置MultiVideoUrl
     *
     * @param workoutDetailVO
     * @param m3u8Type
     */
    public static void setMultiVideoUrl(List<ProjYogaRegularWorkoutListVO> workoutListVOS) {

        if (CollectionUtils.isEmpty(workoutListVOS)) {
            return;
        }

        // 设置多分辨率M3u8，用于IOS下载
        workoutListVOS.forEach(workoutVO -> workoutVO.setMultiVideoUrl(workoutVO.getVideoM3u8Url()));
    }

    public static Boolean setSubscription(Integer subscription) {

        if (Objects.isNull(subscription)) {
            return Boolean.FALSE;
        }

        return Objects.equals(subscription, GlobalConstant.ONE);
    }

}
