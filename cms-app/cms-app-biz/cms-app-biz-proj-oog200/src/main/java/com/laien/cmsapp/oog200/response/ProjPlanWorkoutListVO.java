package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * note: yogaAutoWorkout plan 查询
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "workout plan 查询", description = "workout plan 查询")
public class ProjPlanWorkoutListVO extends ProjYogaAutoWorkoutListVO {

    @ApiModelProperty(value = "1:Upper Body,2:Abs & Core,3:Lower Body")
    private Integer targetCode;

    @ApiModelProperty(value = "0:Standing,1:Seated,2:<PERSON><PERSON>,3:Prone,4:Arm & Leg Support,5:Lying")
    private Integer positionCode;

    @ApiModelProperty(value = "0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private Integer planTypeCode;

    @ApiModelProperty(value = "infoId")
    private Integer infoId;
}
