package com.laien.cmsapp.oog200.service;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjWorkoutDetailVO116;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;

import java.util.List;
import java.util.Set;

/**
 * Author:  hhl
 * Date:  2024/11/7 15:37
 */
public interface IProjChairYogaRegularWorkoutPubService {

    List<ProjYogaRegularWorkoutListVO> listYogaRegularWorkout(ProjPublishCurrentVersionInfoBO versionInfoBO, String lang, Set<Integer> categoryCodeSet);

    ProjYogaRegularWorkoutDetailVO findDetailById(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer workoutId, Integer m3u8Type, String lang);

    List<ProjYogaRegularWorkoutDetailVO> listByIdsAndStatus(Set<Integer> workoutIds, String language, Integer status, Integer m3u8Type, ProjPublishCurrentVersionInfoBO versionInfoBO);

    /**
     * 将200项目现成的chair yoga workout 给116项目做实验性的使用
     *
     * @param language
     * @param m3u8Type
     * @param versionInfoBO
     * @return
     */
    List<ProjWorkoutDetailVO116> listWorkoutFor116(String language, Integer m3u8Type, ProjPublishCurrentVersionInfoBO versionInfoBO);

}
