package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * proj yoga pose workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseWorkoutPub对象", description="proj yoga pose workout")
public class ProjYogaPoseWorkoutListVO {


    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "sanskrit name")
    private String sanskritName;

    @ApiModelProperty(value = "workout 封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "workout 详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "0: Newbie,1: Beginner,2: Intermediate,3: Advanced")
    private Integer difficultyCode;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "0:Flexibility,1:Balance,2:Strength,3:Relaxation")
    private Integer focusCode;

    @ApiModelProperty(value = "0:Standing,1:Seated,2:Supine,3:Prone,4:Arm & Leg Support")
    private Integer positionCode;


}
