package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPlaylistRelationPub;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/21 13:49
 */
public interface IProjYogaPlaylistRelationPubService extends IService<ProjYogaPlaylistRelationPub> {

    List<ProjYogaPlaylistRelationPub> listByPlaylistIds(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> playlistIds);

}
