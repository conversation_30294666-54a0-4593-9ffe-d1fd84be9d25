package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramRelationPub;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:36
 */
public interface ProjYogaProgramRelationPubMapper extends BaseMapper<ProjYogaProgramRelationPub> {

    @Select("<script>" +
            "SELECT * FROM proj_yoga_program_relation_pub " +
            "WHERE del_flag = 0 and version = #{version} and proj_yoga_program_id IN " +
            "<foreach collection='programIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<ProjYogaProgramRelationPub> listByProgramIdAndVersion(@Param("programIds") Collection<Integer> programIds, Integer version);

}
