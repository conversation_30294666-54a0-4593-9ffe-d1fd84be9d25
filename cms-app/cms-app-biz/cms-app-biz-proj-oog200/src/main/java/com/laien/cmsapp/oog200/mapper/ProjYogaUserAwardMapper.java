package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjYogaUserAward;
import com.laien.cmsapp.oog200.response.ProjYogaAwardVO;
import com.laien.cmsapp.oog200.response.ProjYogaUserAwardVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/9
 */

public interface ProjYogaUserAwardMapper extends BaseMapper<ProjYogaUserAward> {

    @Select(value = "select award.expired_time, award.award_link, award.duration from proj_yoga_user_award ua " +
            "inner join proj_yoga_award award on ua.award_id = award.id " +
            "where ua.user_id = #{userId} and ua.del_flag = 0 ")
    List<ProjYogaAwardVO> listByUserId(Integer userId);

    @Select("<script> " +
            "select ua.user_id,ua.award_id,u.invite_code from proj_yoga_user_award ua " +
            "inner join proj_yoga_user u on ua.user_id = u.id " +
            "where ua.del_flag = 0 and u.invite_code in " +
            "<foreach collection='inviteCodes' item='code' open='(' separator=',' close=')'> " +
            "#{code}" +
            "</foreach> "+
            "</script>")
    List<ProjYogaUserAwardVO> listByInviteCodes(@Param("inviteCodes") Collection<String> inviteCodes);

}
