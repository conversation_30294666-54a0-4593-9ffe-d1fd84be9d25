package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: video subtitles v3
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video subtitles v4", description = "video subtitles v4")
public class ProjVideoGenerateI18nV4VO {

    @ApiModelProperty(value = "default guidanceList")
    @JsonProperty("default")
    private List<ProjVideoGenerateI18nV4ItemVO> default_;

    @ApiModelProperty(value = "least guidanceList")
    private List<ProjVideoGenerateI18nV4ItemVO> least;
}
