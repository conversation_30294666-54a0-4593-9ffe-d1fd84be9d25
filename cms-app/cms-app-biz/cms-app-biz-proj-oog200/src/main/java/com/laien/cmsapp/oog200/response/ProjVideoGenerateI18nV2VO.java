package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video subtitles v2
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video subtitles v2", description = "video subtitles v2")
public class ProjVideoGenerateI18nV2VO {

    @ApiModelProperty(value = "language")
    private String language;

    @ApiModelProperty(value = "subtitles url")
    private String subtitlesUrl;
}
