package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutListVO;
import com.laien.cmsapp.oog200.service.IProjYogaPoseWorkoutService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * proj yoga pose workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Api(tags = "app端：yogaPoseWorkout")
@RestController
@RequestMapping(value = {"/oog200/yogaPoseWorkout", "/OOG200/yogaPoseWorkout"})
public class ProjYogaPoseWorkoutController extends ResponseController {

    @Resource
    private IProjYogaPoseWorkoutService projYogaPoseWorkoutService;

    @ApiOperation(value = "list", tags = {"oog200"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjYogaPoseWorkoutListVO>> list(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projYogaPoseWorkoutService.list(versionInfoBO, lang));
    }

    @ApiOperation(value = "detail", tags = {"oog200"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjYogaPoseWorkoutDetailVO> detail(@PathVariable Integer id,
                                                              @RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang,
                                                              @RequestParam(required = false, defaultValue = "1") Integer m3u8Type) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projYogaPoseWorkoutService.findDetail(id, versionInfoBO, m3u8Type, lang));
    }

}
