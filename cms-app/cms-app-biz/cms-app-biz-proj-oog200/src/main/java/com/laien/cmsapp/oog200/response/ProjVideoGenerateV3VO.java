package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video v3
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video v3", description = "video v3")
public class ProjVideoGenerateV3VO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "video url")
    private String videoUrl;
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;
    @ApiModelProperty(value = "guidance list")
    private List<ProjVideoGenerateI18nV3VO> guidanceList;
    @ApiModelProperty(value = "videos")
    private List<ProjVideoV3VO> videos;

}
