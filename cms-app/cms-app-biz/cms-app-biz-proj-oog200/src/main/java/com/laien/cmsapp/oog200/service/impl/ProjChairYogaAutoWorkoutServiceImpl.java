package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.ProjAutoWorkoutBasicInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaAutoWorkoutPlanBO;
import com.laien.cmsapp.oog200.entity.ProjChairYogaAutoWorkout;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutTemplatePub;
import com.laien.cmsapp.oog200.mapper.ProjChairYogaAutoWorkoutMapper;
import com.laien.cmsapp.oog200.requst.ProjWorkoutPlanReq;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IProjAutoWorkoutBasicInfoService;
import com.laien.cmsapp.oog200.service.IProjChairYogaAutoWorkoutService;
import com.laien.cmsapp.oog200.service.IProjChairYogaVideoService;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutTemplatePubService;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog200.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/10/12 12:01
 */
@Slf4j
@Service
public class ProjChairYogaAutoWorkoutServiceImpl extends ServiceImpl<ProjChairYogaAutoWorkoutMapper, ProjChairYogaAutoWorkout> implements IProjChairYogaAutoWorkoutService {

    @Resource
    private IProjYogaAutoWorkoutTemplatePubService projYogaAutoWorkoutTemplatePubService;

    @Resource
    private IProjChairYogaVideoService chairYogaVideoService;

    @Resource
    private IProjAutoWorkoutBasicInfoService workoutBasicInfoService;

    private static final String WORKOUT_NOT_FOUND_FORMAT = "Not found chair yoga auto workout template, planReq : {}.";

    @Override
    public List<ProjPlanWorkoutListVO> getPlan(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        YogaAutoWorkoutTemplateEnum templateTypeEnum = YogaAutoWorkoutTemplateEnum.getByCode(planReq.getPlanTypeCode());
        List<ProjYogaAutoWorkoutTemplatePub> templateList = projYogaAutoWorkoutTemplatePubService.list(templateTypeEnum.getName(), versionInfoBO);
        if (CollectionUtils.isEmpty(templateList)) {
            log.error(WORKOUT_NOT_FOUND_FORMAT, planReq);
            return Collections.emptyList();
        }

        List<TargetEnum> targetList = convertTarget(planReq.getTargetCodeSet());
        if (CollectionUtils.isEmpty(targetList)) {
            targetList = Arrays.stream(TargetEnum.values()).collect(Collectors.toList());
            log.error("oog200 chair yoga workout plan target code illegal, use default value 1,2,3 planReq: {}", planReq);
        }

        Set<Integer> templateIds = templateList.stream().map(ProjYogaAutoWorkoutTemplatePub::getId).collect(Collectors.toSet());
        List<ProjChairYogaAutoWorkout> workoutList = listWorkout(templateIds);
        if (CollectionUtils.isEmpty(workoutList)) {
            log.error("Not found chair yoga auto workout, planReq : {}.", planReq);
            return Collections.emptyList();
        }

        // 生成一个21天的plan
        List<ProjPlanWorkoutListVO> planWorkouts = generatePlan(planReq, workoutList, targetList);
        return planWorkouts;
    }

    private List<ProjPlanWorkoutListVO> convert2ListVO(List<ProjChairYogaAutoWorkout> workouts, String lang, Integer m3u8Type) {

        if (CollectionUtils.isEmpty(workouts)) {
            return Collections.emptyList();
        }

        List<Integer> workoutIds = workouts.stream().map(ProjChairYogaAutoWorkout::getId).collect(Collectors.toList());
        List<ResYogaVideoDetailVO> videoDetailVOList = chairYogaVideoService.listVideo4Res(workoutIds);

        Map<Integer, List<ResYogaVideoDetailVO>> workoutIdAndVideoDetailMap = videoDetailVOList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));
        List<ProjPlanWorkoutListVO> planListVO = workouts.stream().map(workout -> convert2ListVO(workout, m3u8Type, workoutIdAndVideoDetailMap)).collect(Collectors.toList());
        return planListVO;
    }

    private ProjPlanWorkoutListVO convert2ListVO(ProjChairYogaAutoWorkout workout, Integer m3u8Type, Map<Integer, List<ResYogaVideoDetailVO>> workoutIdAndVideoDetailMap) {

        ProjPlanWorkoutListVO listVO = new ProjPlanWorkoutListVO();
        BeanUtils.copyProperties(workout, listVO);
        listVO.setEventName(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getName() + " Auto Workout Id " + listVO.getId());
        if (!Objects.equals(GlobalConstant.TWO, m3u8Type) && StringUtils.isNotBlank(workout.getVideo2532Url())) {
            listVO.setVideoM3u8Url(workout.getVideo2532Url());
        }

        listVO.setPlanTypeCode(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getCode());
        listVO.setDuration(TimeConvertUtil.millisToMinutes(workout.getDuration()));
        TargetEnum targetEnum = TargetEnum.getByName(workout.getTarget());
        if (Objects.nonNull(targetEnum)) {
            listVO.setTargetCode(targetEnum.getCode());
        }

        DifficultyEnum difficultyEnum = DifficultyEnum.getByName(workout.getDifficulty());
        if (Objects.nonNull(difficultyEnum)) {
            listVO.setDifficultyCode(difficultyEnum.getCode());
        }

        listVO.setVideos(workoutIdAndVideoDetailMap.get(workout.getId()));
        return listVO;
    }

    private List<ProjPlanWorkoutListVO> generatePlan(ProjWorkoutPlanReq planReq, List<ProjChairYogaAutoWorkout> allWorkouts, List<TargetEnum> targetList) {

        if (CollectionUtils.isEmpty(allWorkouts)) {
            return Collections.emptyList();
        }

        BasicInfoPointTargetMappingEnum pointTargetMappingEnum = BasicInfoPointTargetMappingEnum.getByTargetList(targetList);
        DifficultyEnum difficultyEnum = null;
        List<ProjAutoWorkoutBasicInfoBO> configImageAndWorkoutList = workoutBasicInfoService.listEnable(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, pointTargetMappingEnum.getPoint(), difficultyEnum);
        ProjYogaAutoWorkoutPlanBO workoutPlanBO = wrapPlanBO(allWorkouts, configImageAndWorkoutList, planReq, targetList);
        List<ProjPlanWorkoutListVO> imageAndWorkoutPlan = workoutBasicInfoService.generatePlan(workoutPlanBO);
        if (CollectionUtils.isEmpty(imageAndWorkoutPlan)) {
            log.error("Can't generate plan for chair yoga auto workout, planReq : {}.", planReq);
            return Collections.emptyList();
        }

        return convertImage2Workout(imageAndWorkoutPlan, planReq, allWorkouts);
    }

    private List<ProjPlanWorkoutListVO> generatePlanWithDifficult(ProjWorkoutPlanReq planReq, List<ProjChairYogaAutoWorkout> allWorkouts, DifficultyEnum difficultyEnum) {

        if (CollectionUtils.isEmpty(allWorkouts)) {
            return Collections.emptyList();
        }

        List<ProjAutoWorkoutBasicInfoBO> configImageAndWorkoutList = workoutBasicInfoService.listEnable(YogaAutoWorkoutTemplateEnum.CHAIR_YOGA, null, difficultyEnum);
        ProjYogaAutoWorkoutPlanBO workoutPlanBO = wrapPlanBOWithDifficult(allWorkouts, configImageAndWorkoutList, planReq, difficultyEnum);

        List<ProjPlanWorkoutListVO> imageAndWorkoutPlan = workoutBasicInfoService.generatePlan(workoutPlanBO);
        if (CollectionUtils.isEmpty(imageAndWorkoutPlan)) {
            log.error("Can't generate plan for chair yoga auto workout, planReq : {}.", planReq);
            return Collections.emptyList();
        }

        return convertImage2Workout(imageAndWorkoutPlan, planReq, allWorkouts);
    }

    private List<ProjPlanWorkoutListVO> convertImage2Workout(List<ProjPlanWorkoutListVO> imageAndWorkoutPlan, ProjWorkoutPlanReq planReq,
                                                             List<ProjChairYogaAutoWorkout> allWorkouts) {

        List<Integer> workoutIds = imageAndWorkoutPlan.stream().map(ProjPlanWorkoutListVO::getId).collect(Collectors.toList());
        Map<Integer, List<ResYogaVideoDetailVO>> workoutIdAndVideoDetailMap = getWorkoutIdAndVideoDetailMap(workoutIds, planReq.getLang());
        Map<Integer, ProjChairYogaAutoWorkout> autoWorkoutMap = allWorkouts.stream().collect(Collectors.toMap(ProjChairYogaAutoWorkout::getId, Function.identity()));

        return imageAndWorkoutPlan.stream().map(planWorkout -> {
            ProjChairYogaAutoWorkout autoWorkout = autoWorkoutMap.get(planWorkout.getId());
            ProjPlanWorkoutListVO listVO = convert2ListVO(autoWorkout, planReq.getM3u8Type(), workoutIdAndVideoDetailMap);
            listVO.setName(planWorkout.getName());
            listVO.setInfoId(planWorkout.getInfoId());
            listVO.setCoverImgUrl(planWorkout.getCoverImgUrl());
            return listVO;
        }).collect(Collectors.toList());
    }

    private ProjYogaAutoWorkoutPlanBO wrapPlanBOWithDifficult(List<ProjChairYogaAutoWorkout> allWorkouts, List<ProjAutoWorkoutBasicInfoBO> configImageAndWorkoutList, ProjWorkoutPlanReq planReq, DifficultyEnum difficulty) {

        Set<Integer> allMatchWorkouts = Sets.newHashSet();
        Set<Integer> matchDurationWorkouts = Sets.newHashSet();
        Set<Integer> matchOtherWorkouts = Sets.newHashSet();
        splitWorkoutWithDifficult(allWorkouts, allMatchWorkouts, matchDurationWorkouts, matchOtherWorkouts, planReq.getDurationCode(), difficulty);

        ProjYogaAutoWorkoutPlanBO workoutPlanBO = new ProjYogaAutoWorkoutPlanBO();
        workoutPlanBO.setBasicInfoBOList(configImageAndWorkoutList);
        workoutPlanBO.setMatchAllWorkoutIds(allMatchWorkouts);
        workoutPlanBO.setMatchDurationWorkoutIds(matchDurationWorkouts);

        workoutPlanBO.setMatchOtherWorkoutIds(matchOtherWorkouts);
        workoutPlanBO.setLang(planReq.getLang());
        return workoutPlanBO;
    }

    private void splitWorkoutWithDifficult(List<ProjChairYogaAutoWorkout> allWorkouts, Set<Integer> allMatchWorkouts, Set<Integer> matchDurationWorkouts,
                                           Set<Integer> matchOtherWorkouts, Integer durationCode, DifficultyEnum difficulty) {

        DurationEnum durationEnum = DurationEnum.getByCode(durationCode);
        Function<ProjChairYogaAutoWorkout, Boolean> matchDurationFunction = workout -> workout.getDuration() >= durationEnum.getMin() && workout.getDuration() <= durationEnum.getMax();
        Function<ProjChairYogaAutoWorkout, Boolean> matchDifficultFunction = workout -> Objects.equals(difficulty.getName(), workout.getDifficulty());

        for (ProjChairYogaAutoWorkout workout : allWorkouts) {
            if (matchDurationFunction.apply(workout) && matchDifficultFunction.apply(workout)) {
                allMatchWorkouts.add(workout.getId());
                continue;
            }
            if (matchDurationFunction.apply(workout)) {
                matchDurationWorkouts.add(workout.getId());
                continue;
            }
            if (matchDifficultFunction.apply(workout)) {
                matchOtherWorkouts.add(workout.getId());
            }
        }
    }

    private ProjYogaAutoWorkoutPlanBO wrapPlanBO(List<ProjChairYogaAutoWorkout> allWorkouts, List<ProjAutoWorkoutBasicInfoBO> configImageAndWorkoutList, ProjWorkoutPlanReq planReq, List<TargetEnum> targetList) {

        Set<Integer> allMatchWorkouts = Sets.newHashSet();
        Set<Integer> matchDurationWorkouts = Sets.newHashSet();
        Set<Integer> matchOtherWorkouts = Sets.newHashSet();
        splitWorkout(allWorkouts, allMatchWorkouts, matchDurationWorkouts, matchOtherWorkouts, planReq.getDurationCode(), targetList);

        ProjYogaAutoWorkoutPlanBO workoutPlanBO = new ProjYogaAutoWorkoutPlanBO();
        workoutPlanBO.setBasicInfoBOList(configImageAndWorkoutList);
        workoutPlanBO.setMatchAllWorkoutIds(allMatchWorkouts);
        workoutPlanBO.setMatchDurationWorkoutIds(matchDurationWorkouts);

        workoutPlanBO.setMatchOtherWorkoutIds(matchOtherWorkouts);
        workoutPlanBO.setLang(planReq.getLang());
        return workoutPlanBO;
    }

    private Map<Integer, List<ResYogaVideoDetailVO>> getWorkoutIdAndVideoDetailMap(List<Integer> workoutIds, String lang) {

        List<ResYogaVideoDetailVO> videoDetailVOList = chairYogaVideoService.listVideo4Res(workoutIds);
        Map<Integer, List<ResYogaVideoDetailVO>> workoutIdAndVideoDetailMap = videoDetailVOList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));
        return workoutIdAndVideoDetailMap;
    }


    private void splitWorkout(List<ProjChairYogaAutoWorkout> allWorkouts, Set<Integer> allMatchWorkouts, Set<Integer> matchDurationWorkouts, Set<Integer> matchTargetWorkouts, Integer durationCode, List<TargetEnum> targetList) {

        DurationEnum durationEnum = DurationEnum.getByCode(durationCode);
        Function<ProjChairYogaAutoWorkout, Boolean> matchDurationFunction = workout -> workout.getDuration() >= durationEnum.getMin() && workout.getDuration() <= durationEnum.getMax();

        List<String> targetNameList = targetList.stream().map(TargetEnum::getName).collect(Collectors.toList());
        Function<ProjChairYogaAutoWorkout, Boolean> matchTargetFunction = workout -> targetNameList.contains(workout.getTarget());

        for (ProjChairYogaAutoWorkout workout : allWorkouts) {
            if (matchDurationFunction.apply(workout) && matchTargetFunction.apply(workout)) {
                allMatchWorkouts.add(workout.getId());
                continue;
            }
            if (matchDurationFunction.apply(workout)) {
                matchDurationWorkouts.add(workout.getId());
                continue;
            }
            if (matchTargetFunction.apply(workout)) {
                matchTargetWorkouts.add(workout.getId());
            }
        }
    }

    private List<ProjChairYogaAutoWorkout> listWorkout(Set<Integer> templateIds) {

        LambdaQueryWrapper<ProjChairYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjChairYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIds);

        queryWrapper.eq(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ProjChairYogaAutoWorkout::getDelFlag, GlobalConstant.NO);
        return list(queryWrapper);
    }

    private List<TargetEnum> convertTarget(Collection<Integer> targets) {

        if (CollectionUtils.isEmpty(targets)) {
            return Collections.emptyList();
        }

        List<TargetEnum> targetList = Lists.newArrayList();
        for (Integer code : targets) {
            TargetEnum targetEnum = TargetEnum.getByCode(code);
            if (Objects.nonNull(targetEnum)) {
                targetList.add(targetEnum);
            }
        }
        return targetList;
    }

    @Override
    public List<ProjPlanWorkoutListVO> getPlanV2(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        YogaAutoWorkoutTemplateEnum templateTypeEnum = YogaAutoWorkoutTemplateEnum.getByCode(planReq.getPlanTypeCode());
        List<ProjYogaAutoWorkoutTemplatePub> templateList = projYogaAutoWorkoutTemplatePubService.list(templateTypeEnum.getName(), versionInfoBO);
        if (CollectionUtils.isEmpty(templateList)) {
            log.error(WORKOUT_NOT_FOUND_FORMAT, planReq);
            return Collections.emptyList();
        }

        DifficultyEnum difficulty = DifficultyEnum.getByCode(planReq.getDifficultyCode());
        if (Objects.isNull(difficulty)) {
            difficulty = DifficultyEnum.BEGINNER;
            log.error("oog200 chair yoga workout plan difficulty code illegal, use default value 1, planReq: {}", planReq);
        }

        // 暂时没有ADVANCED资源，使用INTERMEDIATE替代
        if (Objects.equals(difficulty, DifficultyEnum.ADVANCED)) {
            difficulty = DifficultyEnum.INTERMEDIATE;
        }

        Set<Integer> templateIds = templateList.stream().map(ProjYogaAutoWorkoutTemplatePub::getId).collect(Collectors.toSet());
        List<ProjChairYogaAutoWorkout> workoutList = listWorkout(templateIds);
        if (CollectionUtils.isEmpty(workoutList)) {
            log.error("Not found chair yoga auto workout, planReq : {}.", planReq);
            return Collections.emptyList();
        }

        // 生成一个21天的plan
        List<ProjPlanWorkoutListVO> planWorkouts = generatePlanWithDifficult(planReq, workoutList, difficulty);
        return planWorkouts;
    }

    @Override
    public List<ProjPlanWorkoutListVO> query(List<Integer> idList, Integer m3u8Type, String language) {

        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }

        List<ProjChairYogaAutoWorkout> workoutList = listEnableByIds(idList);
        if (CollectionUtils.isEmpty(workoutList)) {
            return Collections.emptyList();
        }

        return convert2ListVO(workoutList, language, m3u8Type);
    }

    private List<ProjChairYogaAutoWorkout> listEnableByIds(List<Integer> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ProjChairYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(ProjChairYogaAutoWorkout::getId, idList);

        queryWrapper.eq(ProjChairYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.eq(ProjChairYogaAutoWorkout::getDelFlag, GlobalConstant.NO);
        return list(queryWrapper);
    }

}
