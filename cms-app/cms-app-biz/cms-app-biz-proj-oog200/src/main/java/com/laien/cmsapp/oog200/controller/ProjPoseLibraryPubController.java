package com.laien.cmsapp.oog200.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjPoseLibraryPub;
import com.laien.cmsapp.oog200.entity.ResPoseLibrary;
import com.laien.cmsapp.oog200.response.ProjPoseLibraryPubListItemSubVO;
import com.laien.cmsapp.oog200.response.ProjPoseLibraryPubListVO;
import com.laien.cmsapp.oog200.service.IProjPoseLibraryPubService;
import com.laien.cmsapp.oog200.service.IResPoseLibraryService;
import com.laien.cmsapp.service.FileService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目poseLibrary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Api(tags = "app端：poseLibrary")
@RestController
@RequestMapping("/{appCode}")
public class ProjPoseLibraryPubController extends ResponseController {

    private static final String ALL = "All";
    private static final String BASIC = "Basic";
    private static final List<String> DIFFICULTYNAMES = Lists.newArrayList("Beginner", "Intermediate", "Advanced");
    @Resource
    private IProjPoseLibraryPubService projPoseLibraryPubService;

    @Resource
    private IResPoseLibraryService resPoseLibraryService;

    @Resource
    private FileService fileService;

    @Resource
    private I18nUtil i18nUtil;

    private List<String> poseNames = Lists.newArrayList("Cat Cow Pose", "Child Pose", "Cobra Pose", "Downward Facing Dog Pose", "Easy Pose", "Mountain Pose", "Bridge Pose", "Corpse pose", "Crescent Low Lunge Pose", "Seated Forward Bend Pose", "Sage Marichi Pose C Variation", "Staff Pose", "Standing Forward Fold Pose", "Tree Pose", "Warrior Pose I", "Extended Triangle Pose", "Plank Pose", "Four Limbed Staff Pose");

    @ApiOperation(value = "查询列表v1", tags = {"oog200"})
    @GetMapping("/v1/poseLibrary/list")
    public ResponseResult<ProjPoseLibraryPubListVO> v1Page(Boolean fetchSection, String lang) {
        return  this.page(fetchSection, null);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "fetchSection", value = "不传默认为false，为false时返回【项目-pose library】中数据;为true时返回【项目-pose library】中数据与【设定pose数据】的交集")
    })
    @ApiOperation(value = "查询列表v2", tags = {"oog200"})
    @GetMapping("/poseLibrary/v2/list")
    public ResponseResult<ProjPoseLibraryPubListVO> page(Boolean fetchSection, String lang) {
        //响应内容
        ProjPoseLibraryPubListVO result = new ProjPoseLibraryPubListVO();
        //获取项目信息
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        //查询项目关联的pose library id
        LambdaQueryWrapper<ProjPoseLibraryPub> pubLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pubLambdaQueryWrapper.eq(ProjPoseLibraryPub::getProjId, versionInfoBO.getProjId());
        pubLambdaQueryWrapper.eq(ProjPoseLibraryPub::getVersion, versionInfoBO.getCurrentVersion());
        List<ProjPoseLibraryPub> projPoseLibraryPubs = projPoseLibraryPubService.list(pubLambdaQueryWrapper);
        List<Integer> resPoseLibraryIds = projPoseLibraryPubs.stream().map(ProjPoseLibraryPub::getPoseLibraryId).collect(Collectors.toList());
        //查询所有的res pose library
        List<ResPoseLibrary> resPoseLibraries = resPoseLibraryService.listByIds(resPoseLibraryIds);
        i18nUtil.translate(resPoseLibraries, ProjCodeEnums.OOG200, lang);

        //ResPoseLibrary转为ProjPoseLibraryPubListItemVO
        List<ProjPoseLibraryPubListItemSubVO> projPoseLibraryPubListItemSubVOList = Lists.newArrayList();
        resPoseLibraries.stream().forEach(resPoseLibrary -> {
            ProjPoseLibraryPubListItemSubVO projPoseLibraryPubListItemSubVO = new ProjPoseLibraryPubListItemSubVO();
            BeanUtils.copyProperties(resPoseLibrary, projPoseLibraryPubListItemSubVO, "description");
            projPoseLibraryPubListItemSubVO.setDescription(descriptionToList(resPoseLibrary.getDescription()));

            String difficulty = projPoseLibraryPubListItemSubVO.getDifficulty();
            if (Objects.equals(difficulty, "Intermediate")) {
                projPoseLibraryPubListItemSubVO.setDifficultyCode(GlobalConstant.TWO);
            } else if (Objects.equals(difficulty, "Advanced")) {
                projPoseLibraryPubListItemSubVO.setDifficultyCode(GlobalConstant.THREE);
            } else {
                projPoseLibraryPubListItemSubVO.setDifficultyCode(GlobalConstant.ONE);
            }
            if (fetchSection != null && fetchSection) {
                //查询部分数据
                if (poseNames.contains(resPoseLibrary.getPoseName().trim())) {
                    projPoseLibraryPubListItemSubVOList.add(projPoseLibraryPubListItemSubVO);
                }
            } else {
                projPoseLibraryPubListItemSubVOList.add(projPoseLibraryPubListItemSubVO);
            }
        });

        projPoseLibraryPubListItemSubVOList.stream().forEach(pose -> {
            // 修改focus格式
            if (StringUtils.isNotBlank(pose.getFocus())) {
                pose.setFocus(
                        Joiner.on(",").skipNulls().join(
                                Splitter.on("|").omitEmptyStrings().trimResults().split(pose.getFocus())
                        )
                );
            }
            //地址改为全路径
            pose.setAnimationUrl(fileService.getAbsoluteUrl(pose.getAnimationUrl()));
        });

        result.setPoseLibrarys(projPoseLibraryPubListItemSubVOList);

        //组装basic
//        List<ProjPoseLibraryPubListItemSubVO> basicList = projPoseLibraryPubListItemSubVOList.stream().filter(resPoseLibrary -> resPoseLibrary.getBasic() == GlobalConstant.ONE).collect(Collectors.toList());
//        result.getItems().add(new ProjPoseLibraryPubListItemVO(BASIC, basicList));
        //按difficulty分组
//        Map<String, List<ProjPoseLibraryPubListItemSubVO>> difficultyGroup = projPoseLibraryPubListItemSubVOList.stream().collect(Collectors.groupingBy(ProjPoseLibraryPubListItemSubVO::getDifficulty));
//        DIFFICULTYNAMES.stream().forEach(difficultyName -> {
//            List<ProjPoseLibraryPubListItemSubVO> difficultyPoseList = Optional.ofNullable(difficultyGroup.get(difficultyName)).filter(CollectionUtils::isNotEmpty).orElse(Lists.newArrayList());
//            result.getItems().add(new ProjPoseLibraryPubListItemVO(difficultyName, difficultyPoseList));
//        });

        return succ(result);
    }

    /**
     * description 按照换行符拆分成数组
     *
     * @param description description
     * @return list
     */
    private static List<String> descriptionToList(String description) {
        List<String> stringList = new ArrayList<>();
        String[] arr = description.split("\n");
        for (String s : arr) {
            if (StringUtils.isNotBlank(s)) {
                stringList.add(s);
            }
        }
        return stringList;
    }
}
