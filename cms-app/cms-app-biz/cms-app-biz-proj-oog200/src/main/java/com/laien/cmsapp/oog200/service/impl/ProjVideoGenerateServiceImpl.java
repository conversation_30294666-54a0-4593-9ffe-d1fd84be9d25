package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.config.Oog200BizConfig;
import com.laien.cmsapp.oog200.entity.ProjVideoGenerate;
import com.laien.cmsapp.oog200.entity.ProjVideoGenerateI18n;
import com.laien.cmsapp.entity.ResImage;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.cmsapp.oog200.mapper.ProjVideoGenerateMapper;
import com.laien.cmsapp.oog200.requst.*;
import com.laien.cmsapp.oog200.response.*;
import com.laien.cmsapp.oog200.service.IProjTemplateService;
import com.laien.cmsapp.oog200.service.IProjVideoGenerateI18nService;
import com.laien.cmsapp.oog200.service.IProjVideoGenerateService;
import com.laien.cmsapp.service.*;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * video generate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Service
public class ProjVideoGenerateServiceImpl extends ServiceImpl<ProjVideoGenerateMapper, ProjVideoGenerate> implements IProjVideoGenerateService {

    @Resource
    private FileService fileService;
    @Resource
    private IProjVideoGenerateI18nService projVideoGenerateI18nService;
    @Resource
    private IProjTemplateService projTemplateService;
    @Resource
    private IResImageService resImageService;
    @Resource
    private Oog200BizConfig cmsAppBizConfig;

    @Override
    public ProjVideoGenerateVO getRandomOne(ProjVideoGenerateRandomReq randomReq) {
        List<Integer> templateIds = projTemplateService.selectTemplateIds(randomReq.getDuration());
        if (templateIds.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjVideoGenerate::getDuration, randomReq.getDuration());
        queryWrapper.in(ProjVideoGenerate::getTemplateId, templateIds);
        queryWrapper.eq(ProjVideoGenerate::getDataVersion, GlobalConstant.ONE);
        List<ProjVideoGenerate> list = this.list(queryWrapper);

        ProjVideoGenerate videoGenerate;
        int size = list.size();
        if (size > GlobalConstant.ZERO) {
            videoGenerate = list.get(new Random().nextInt(size));
        } else {
            return null;
        }

        LambdaQueryWrapper<ProjVideoGenerateI18n> i18nQueryWrapper = new LambdaQueryWrapper<>();
        i18nQueryWrapper.eq(ProjVideoGenerateI18n::getGenerateId, videoGenerate.getId());
        List<ProjVideoGenerateI18n> i18nList = projVideoGenerateI18nService.list(i18nQueryWrapper);

        String least = "Least";
        String introType = randomReq.getIntroType();
        ProjVideoGenerateVO generateVO = new ProjVideoGenerateVO();
        generateVO.setId(videoGenerate.getId());
        List<ProjVideoGenerateI18nVO> titleSubtitles = new ArrayList<>(i18nList.size());
        List<ProjVideoGenerateI18nVO> guidanceSubtitles = new ArrayList<>(i18nList.size());
        if (Objects.equals(introType, least)) {
            if (new Random().nextInt(GlobalConstant.TEN) % GlobalConstant.TWO == GlobalConstant.ZERO) {
                generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo2LeastUrl()));
            } else {
                generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1LeastUrl()));
            }
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (StringUtils.isNotBlank(i18n.getTitleSubtitleUrl())) {
                    ProjVideoGenerateI18nVO title = new ProjVideoGenerateI18nVO();
                    title.setLanguage(i18n.getLanguage());
                    title.setSubtitlesUrl(getFullUrl(i18n.getTitleSubtitleUrl()));
                    titleSubtitles.add(title);
                }

                if (StringUtils.isNotBlank(i18n.getGuidanceLeastUrl())) {
                    ProjVideoGenerateI18nVO guidance = new ProjVideoGenerateI18nVO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceLeastUrl()));
                    guidanceSubtitles.add(guidance);
                }
            }
        } else {
            if (new Random().nextInt(GlobalConstant.TEN) % GlobalConstant.TWO == GlobalConstant.ZERO) {
                generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo2DefaultUrl()));
            } else {
                generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1DefaultUrl()));
            }
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (StringUtils.isNotBlank(i18n.getTitleSubtitleUrl())) {
                    ProjVideoGenerateI18nVO title = new ProjVideoGenerateI18nVO();
                    title.setLanguage(i18n.getLanguage());
                    title.setSubtitlesUrl(getFullUrl(i18n.getTitleSubtitleUrl()));
                    titleSubtitles.add(title);
                }

                if (StringUtils.isNotBlank(i18n.getGuidanceDefaultUrl())) {
                    ProjVideoGenerateI18nVO guidance = new ProjVideoGenerateI18nVO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceDefaultUrl()));
                    guidanceSubtitles.add(guidance);
                }
            }
        }
        BigDecimal calorie = videoGenerate.getCalorie();
        if (Objects.nonNull(calorie)) {
            calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
        } else {
            calorie = BigDecimal.ZERO;
        }
        generateVO.setCalorie(calorie);
        generateVO.setTitleSubtitles(titleSubtitles);
        generateVO.setGuidanceSubtitles(guidanceSubtitles);
        return generateVO;
    }

    @Override
    public ProjVideoGenerateV2VO getRandomOneV2(ProjVideoGenerateRandomReq randomReq) {
        List<Integer> templateIds = projTemplateService.selectTemplateIds(randomReq.getDuration());
        if (templateIds.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjVideoGenerate::getDuration, randomReq.getDuration());
        String difficulty = randomReq.getDifficulty();
        queryWrapper.eq(StringUtils.isNotBlank(difficulty), ProjVideoGenerate::getDifficulty, difficulty);
        queryWrapper.in(ProjVideoGenerate::getTemplateId, templateIds);
        queryWrapper.eq(ProjVideoGenerate::getDataVersion, GlobalConstant.TWO);
        List<ProjVideoGenerate> list = this.list(queryWrapper);

        ProjVideoGenerate videoGenerate;
        int size = list.size();
        if (size > GlobalConstant.ZERO) {
            videoGenerate = list.get(new Random().nextInt(size));
        } else {
            return null;
        }

        Integer id = videoGenerate.getId();
        LambdaQueryWrapper<ProjVideoGenerateI18n> i18nQueryWrapper = new LambdaQueryWrapper<>();
        i18nQueryWrapper.eq(ProjVideoGenerateI18n::getGenerateId, id);
        List<ProjVideoGenerateI18n> i18nList = projVideoGenerateI18nService.list(i18nQueryWrapper);

        String least = "Least";
        String introType = randomReq.getIntroType();
        ProjVideoGenerateV2VO generateVO = new ProjVideoGenerateV2VO();
        generateVO.setId(videoGenerate.getId());
        List<ProjVideoGenerateI18nV2VO> guidanceSubtitles = new ArrayList<>(i18nList.size());
        if (Objects.equals(introType, least)) {
            generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1LeastUrl()));
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (StringUtils.isNotBlank(i18n.getGuidanceLeastUrl())) {
                    ProjVideoGenerateI18nV2VO guidance = new ProjVideoGenerateI18nV2VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceLeastUrl()));
                    guidanceSubtitles.add(guidance);
                }
            }
        } else {
            generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1DefaultUrl()));
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (StringUtils.isNotBlank(i18n.getGuidanceDefaultUrl())) {
                    ProjVideoGenerateI18nV2VO guidance = new ProjVideoGenerateI18nV2VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceDefaultUrl()));
                    guidanceSubtitles.add(guidance);
                }
            }
        }
        List<ProjVideoV2VO> videos = this.baseMapper.selectVideosById(id);
        BigDecimal calorie = videoGenerate.getCalorie();
        if (Objects.nonNull(calorie)) {
            calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
        } else {
            calorie = BigDecimal.ZERO;
        }
        generateVO.setCalorie(calorie);
        generateVO.setGuidanceSubtitles(guidanceSubtitles);
        generateVO.setVideos(videos);
        return generateVO;
    }

    @Override
    public ProjVideoGenerateV3VO getRandomOneV3(ProjVideoGenerateRandomReq randomReq) {
        List<Integer> templateIds = projTemplateService.selectTemplateIds(randomReq.getDuration());
        if (templateIds.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjVideoGenerate::getDuration, randomReq.getDuration());
        String difficulty = randomReq.getDifficulty();
        queryWrapper.eq(StringUtils.isNotBlank(difficulty), ProjVideoGenerate::getDifficulty, difficulty);
        queryWrapper.in(ProjVideoGenerate::getTemplateId, templateIds);
        queryWrapper.eq(ProjVideoGenerate::getDataVersion, GlobalConstant.THREE);
        List<ProjVideoGenerate> list = this.list(queryWrapper);

        ProjVideoGenerate videoGenerate;
        int size = list.size();
        if (size > GlobalConstant.ZERO) {
            videoGenerate = list.get(new Random().nextInt(size));
        } else {
            return null;
        }

        Integer id = videoGenerate.getId();
        LambdaQueryWrapper<ProjVideoGenerateI18n> i18nQueryWrapper = new LambdaQueryWrapper<>();
        i18nQueryWrapper.eq(ProjVideoGenerateI18n::getGenerateId, id);
        List<ProjVideoGenerateI18n> i18nList = projVideoGenerateI18nService.list(i18nQueryWrapper);

        String least = "Least";
        String introType = randomReq.getIntroType();
        ProjVideoGenerateV3VO generateVO = new ProjVideoGenerateV3VO();
        generateVO.setId(videoGenerate.getId());
        List<ProjVideoGenerateI18nV3VO> guidanceList = new ArrayList<>(i18nList.size());
        if (Objects.equals(introType, least)) {
            generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (StringUtils.isNotBlank(i18n.getGuidanceLeastUrl())) {
                    ProjVideoGenerateI18nV3VO guidance = new ProjVideoGenerateI18nV3VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceLeastUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceLeastAudioUrl()));
                    guidanceList.add(guidance);
                }
            }
        } else {
            generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (StringUtils.isNotBlank(i18n.getGuidanceDefaultUrl())) {
                    ProjVideoGenerateI18nV3VO guidance = new ProjVideoGenerateI18nV3VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceDefaultUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceDefaultAudioUrl()));
                    guidanceList.add(guidance);
                }
            }
        }
        List<ProjVideoV3VO> videos = this.baseMapper.selectVideosV3ById(id);
        BigDecimal calorie = videoGenerate.getCalorie();
        if (Objects.nonNull(calorie)) {
            calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
        } else {
            calorie = BigDecimal.ZERO;
        }
        generateVO.setCalorie(calorie);
        generateVO.setGuidanceList(guidanceList);
        generateVO.setVideos(videos);
        return generateVO;
    }

    @Override
    public ProjVideoGenerateV4VO getRandomOneV4(ProjVideoGenerateRandomV4Req randomReq) {
        Integer maxDuration = 30;
        Integer duration = randomReq.getDuration();
        if (duration > maxDuration) {
            duration = maxDuration;
        }
        List<Integer> templateIds = projTemplateService.selectTemplateIds(duration);
        if (templateIds.isEmpty()) {
            return null;
        }
        String realDifficulty = randomReq.getDifficulty();
        if(DifficultyEnum.ADVANCED.getName().equals(randomReq.getDifficulty())){
            randomReq.setDifficulty(DifficultyEnum.INTERMEDIATE.getName());
        }
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjVideoGenerate::getDuration, duration);
        String difficulty = randomReq.getDifficulty();
        queryWrapper.eq(StringUtils.isNotBlank(difficulty), ProjVideoGenerate::getDifficulty, difficulty);
        queryWrapper.in(ProjVideoGenerate::getTemplateId, templateIds);
        queryWrapper.eq(ProjVideoGenerate::getDataVersion, GlobalConstant.THREE);
        List<ProjVideoGenerate> list = this.list(queryWrapper);

        ProjVideoGenerate videoGenerate;
        int size = list.size();
        if (size > GlobalConstant.ZERO) {
            videoGenerate = list.get(new Random().nextInt(size));
        } else {
            return null;
        }

        Integer id = videoGenerate.getId();
        LambdaQueryWrapper<ProjVideoGenerateI18n> i18nQueryWrapper = new LambdaQueryWrapper<>();
        i18nQueryWrapper.eq(ProjVideoGenerateI18n::getGenerateId, id);
        List<ProjVideoGenerateI18n> i18nList = projVideoGenerateI18nService.list(i18nQueryWrapper);
        ProjVideoGenerateV4VO generateVO = new ProjVideoGenerateV4VO();
        String introType = randomReq.getIntroType();
        generateVO.setId(videoGenerate.getId());
        generateVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
        generateVO.setGuidance(getGuidence(i18nList, introType));
        generateVO.setDifficulty(realDifficulty);
        generateVO.setDuration(TimeConvertUtil.millisToMinutes(videoGenerate.getRealDuration()));
        List<ProjVideoV3VO> videos = this.baseMapper.selectVideosV3ById(id);
        BigDecimal calorie = videoGenerate.getCalorie();
        if (Objects.nonNull(calorie)) {
            calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
        } else {
            calorie = BigDecimal.ZERO;
        }
        generateVO.setCalorie(calorie);
        generateVO.setVideos(videos);
        return generateVO;
    }

    /**
     * 2024-03-26 组装guidence对象
     *
     * @param i18nList
     * @param introType
     * @return
     */
    private ProjVideoGenerateI18nV4VO getGuidence(List<ProjVideoGenerateI18n> i18nList, String introType) {
        String least = "Least";
        String default_ = "Default";
        boolean isHasLeast = Objects.equals(introType, least) || StrUtil.isBlank(introType);
        boolean isHasDefault = Objects.equals(introType, default_) || StrUtil.isBlank(introType);
        List<ProjVideoGenerateI18nV4ItemVO> leastList = CollUtil.newArrayList();
        List<ProjVideoGenerateI18nV4ItemVO> defaultList = CollUtil.newArrayList();
        for (ProjVideoGenerateI18n i18n : i18nList) {
            if (isHasLeast) {
                if (StringUtils.isNotBlank(i18n.getGuidanceLeastUrl())) {
                    ProjVideoGenerateI18nV4ItemVO guidance = new ProjVideoGenerateI18nV4ItemVO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceLeastUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceLeastAudioUrl()));
                    leastList.add(guidance);
                }
            }
            if (isHasDefault) {
                if (StringUtils.isNotBlank(i18n.getGuidanceDefaultUrl())) {
                    ProjVideoGenerateI18nV4ItemVO guidance = new ProjVideoGenerateI18nV4ItemVO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceDefaultUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceDefaultAudioUrl()));
                    defaultList.add(guidance);
                }
            }
        }
        ProjVideoGenerateI18nV4VO projVideoGenerateI18nV4VO = new ProjVideoGenerateI18nV4VO();
        projVideoGenerateI18nV4VO.setLeast(leastList);
        projVideoGenerateI18nV4VO.setDefault_(defaultList);
        return projVideoGenerateI18nV4VO;
    }

    private String getFullUrl(String url) {
        return fileService.getAbsoluteR2Url(url);
    }

    @Override
    public List<ProjVideoGeneratePlanVO> selectVideoPlan(ProjVideoGeneratePlanReq planReq) {
        /*
         * 数据来源：Template生成的、时长在10～20mins的Workout（部分老版本Template生成的Workout不可用，排除方式为：仅查询data_version = 3的 Workout）；
         * Day 1, Day 2, Day3：指定Workout，待定；
         * Day4 - Day 21：当difficulty = Newbie 或 Beginner时，随机从所有Newbie&Beginner难度的Workout中进行挑选；
         * 当difficulty = Intermediate 或 Advanced时，随机从所有Intermediate & Advanced难度的Workout中进行挑选；
         */
        List<Integer> templateIds = projTemplateService.selectTemplateIdsByDuration(10, 20);
        if (templateIds.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        String newbie = "Newbie", beginner = "Beginner", intermediate = "Intermediate", advanced = "Advanced";
        String difficulty = planReq.getDifficulty();
        // 为空默认 newbie
        if (difficulty == null) {
            difficulty = newbie;
        }
        if (Objects.equals(difficulty, intermediate) || Objects.equals(difficulty, advanced)) {
            queryWrapper.eq(ProjVideoGenerate::getDifficulty, intermediate);
        } else {
            queryWrapper.in(ProjVideoGenerate::getDifficulty, Arrays.asList(newbie, beginner));
        }
        queryWrapper.in(ProjVideoGenerate::getTemplateId, templateIds);
        queryWrapper.eq(ProjVideoGenerate::getDataVersion, GlobalConstant.THREE);
        List<ProjVideoGenerate> list = this.list(queryWrapper);
        int returnSize = 21;
        int size = list.size();
        if (size > GlobalConstant.ZERO) {
            // 不足21个按照实际数量返回
            if (size < returnSize) {
                returnSize = list.size();
            }
        } else {
            return new ArrayList<>(GlobalConstant.ZERO);
        }

        // 最终生成plan使用的数据
        List<ProjVideoGenerate> dataUseList = new ArrayList<>(returnSize);
        List<Integer> ids = new ArrayList<>(returnSize);
        Map<String, List<Integer>> workoutVideo200DefaultFillIdMap = cmsAppBizConfig.getWorkoutVideo200DefaultFillIdMap();
        List<Integer> fillIds = workoutVideo200DefaultFillIdMap.get(difficulty);
        if (fillIds != null) {
            for (Integer fillId : fillIds) {
                for (ProjVideoGenerate videoGenerate : list) {
                    if (Objects.equals(fillId, videoGenerate.getId())) {
                        ids.add(fillId);
                        dataUseList.add(videoGenerate);
                        break;
                    }
                }
            }
            if (!dataUseList.isEmpty()) {
                // 删除不参与后续随机
                list.removeAll(dataUseList);
            }
        }

        int currentSize = dataUseList.size();
        if (currentSize < returnSize) {
            // list 打乱随机获取
            Collections.shuffle(list);
            int needSize = returnSize - currentSize;
            for (int i = 0; i < needSize; i++) {
                ProjVideoGenerate videoGenerate = list.get(i);
                ids.add(videoGenerate.getId());
                dataUseList.add(videoGenerate);
            }
        }

        LambdaQueryWrapper<ProjVideoGenerateI18n> i18nQueryWrapper = new LambdaQueryWrapper<>();
        i18nQueryWrapper.in(ProjVideoGenerateI18n::getGenerateId, ids);
        Map<Integer, List<ProjVideoGenerateI18n>> i18nMap = projVideoGenerateI18nService.list(i18nQueryWrapper)
                .stream().collect(Collectors.groupingBy(ProjVideoGenerateI18n::getGenerateId));

        // 查询video
        Map<Integer, List<ProjVideoV3VO>> videosMap = this.baseMapper.selectVideosV3ByIds(ids)
                .stream().collect(Collectors.groupingBy(ProjVideoV3VO::getGenerateId));

        String point = "Intermediate | Advanced";
        if (Objects.equals(difficulty, newbie) || Objects.equals(difficulty, beginner)) {
            point = "Newbie | Beginner";
        }
        List<ResImage> imageList = resImageService.find("oog200", "template-workout", point, returnSize);
        int imageIndex = 0;
        int imageSize = imageList.size();
        String least = "Least";
        String introType = planReq.getIntroType();
        List<ProjVideoGeneratePlanVO> planVOList = new ArrayList<>(returnSize);
        for (ProjVideoGenerate videoGenerate : dataUseList) {
            ProjVideoGeneratePlanVO planVO = new ProjVideoGeneratePlanVO();
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            List<ProjVideoGenerateI18n> i18nList = i18nMap.get(videoGenerate.getId());
            List<ProjVideoGenerateI18nV3VO> guidanceList = new ArrayList<>(i18nList.size());
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (Objects.equals(introType, least) && StringUtils.isNotBlank(i18n.getGuidanceLeastUrl())) {
                    ProjVideoGenerateI18nV3VO guidance = new ProjVideoGenerateI18nV3VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceLeastUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceLeastAudioUrl()));
                    guidanceList.add(guidance);
                } else if (StringUtils.isNotBlank(i18n.getGuidanceDefaultUrl())) {
                    ProjVideoGenerateI18nV3VO guidance = new ProjVideoGenerateI18nV3VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceDefaultUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceDefaultAudioUrl()));
                    guidanceList.add(guidance);
                }
            }
            int id = videoGenerate.getId();
            List<ProjVideoV3VO> videos = videosMap.get(id);
            BigDecimal calorie = videoGenerate.getCalorie();
            if (Objects.nonNull(calorie)) {
                calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
            } else {
                calorie = BigDecimal.ZERO;
            }

            // image 轮流赋值
            if (imageSize != GlobalConstant.ZERO) {
                ResImage resImage = imageList.get(imageIndex);
                planVO.setName(resImage.getName());
                planVO.setCoverImg(getFullUrl(resImage.getCoverImage()));
                if (imageIndex < imageSize - 1) {
                    imageIndex++;
                } else {
                    imageIndex = GlobalConstant.ZERO;
                }
            }

            planVO.setId(id);
            planVO.setDuration(TimeConvertUtil.millisToMinutes(videoGenerate.getRealDuration()));
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            planVO.setCalorie(calorie);
            planVO.setDifficulty(difficulty);
            planVO.setGuidanceList(guidanceList);
            planVO.setVideos(videos);
            planVOList.add(planVO);
        }

        return planVOList;
    }

    @Override
    public List<ProjVideoGeneratePlanV2VO> selectVideoPlanV2(ProjVideoGeneratePlanV2Req planReq) {
        /*
         * 数据来源：Template生成的、时长在10～20mins的Workout（部分老版本Template生成的Workout不可用，排除方式为：仅查询data_version = 3的 Workout）；
         * Day 1, Day 2, Day3：指定Workout，待定；
         * Day4 - Day 21：当difficulty = Newbie 或 Beginner时，随机从所有Newbie&Beginner难度的Workout中进行挑选；
         * 当difficulty = Intermediate 或 Advanced时，随机从所有Intermediate & Advanced难度的Workout中进行挑选；
         */
        List<Integer> templateIds = projTemplateService.selectTemplateIdsByDuration(10, 20);
        if (templateIds.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        String newbie = "Newbie", beginner = "Beginner", intermediate = "Intermediate", advanced = "Advanced";
        String difficulty = planReq.getDifficulty();
        // 为空默认 newbie
        if (difficulty == null) {
            difficulty = newbie;
        }
        if (Objects.equals(difficulty, intermediate) || Objects.equals(difficulty, advanced)) {
            queryWrapper.eq(ProjVideoGenerate::getDifficulty, intermediate);
        } else {
            queryWrapper.in(ProjVideoGenerate::getDifficulty, Arrays.asList(newbie, beginner));
        }
        queryWrapper.in(ProjVideoGenerate::getTemplateId, templateIds);
        queryWrapper.eq(ProjVideoGenerate::getDataVersion, GlobalConstant.THREE);
        List<ProjVideoGenerate> list = this.list(queryWrapper);
        int returnSize = 21;
        int size = list.size();
        if (size > GlobalConstant.ZERO) {
            // 不足21个按照实际数量返回
            if (size < returnSize) {
                returnSize = list.size();
            }
        } else {
            return new ArrayList<>(GlobalConstant.ZERO);
        }

        // 最终生成plan使用的数据
        List<ProjVideoGenerate> dataUseList = new ArrayList<>(returnSize);
        List<Integer> ids = new ArrayList<>(returnSize);
        Map<String, List<Integer>> workoutVideo200DefaultFillIdMap = cmsAppBizConfig.getWorkoutVideo200DefaultFillIdMap();
        List<Integer> fillIds = workoutVideo200DefaultFillIdMap.get(difficulty);
        if (fillIds != null) {
            for (Integer fillId : fillIds) {
                for (ProjVideoGenerate videoGenerate : list) {
                    if (Objects.equals(fillId, videoGenerate.getId())) {
                        ids.add(fillId);
                        dataUseList.add(videoGenerate);
                        break;
                    }
                }
            }
            if (!dataUseList.isEmpty()) {
                // 删除不参与后续随机
                list.removeAll(dataUseList);
            }
        }

        int currentSize = dataUseList.size();
        if (currentSize < returnSize) {
            // list 打乱随机获取
            Collections.shuffle(list);
            int needSize = returnSize - currentSize;
            for (int i = 0; i < needSize; i++) {
                ProjVideoGenerate videoGenerate = list.get(i);
                ids.add(videoGenerate.getId());
                dataUseList.add(videoGenerate);
            }
        }

        LambdaQueryWrapper<ProjVideoGenerateI18n> i18nQueryWrapper = new LambdaQueryWrapper<>();
        i18nQueryWrapper.in(ProjVideoGenerateI18n::getGenerateId, ids);
        Map<Integer, List<ProjVideoGenerateI18n>> i18nMap = projVideoGenerateI18nService.list(i18nQueryWrapper)
                .stream().collect(Collectors.groupingBy(ProjVideoGenerateI18n::getGenerateId));

        // 查询video
        Map<Integer, List<ProjVideoV3VO>> videosMap = this.baseMapper.selectVideosV3ByIds(ids)
                .stream().collect(Collectors.groupingBy(ProjVideoV3VO::getGenerateId));

        String point = "Intermediate | Advanced";
        if (Objects.equals(difficulty, newbie) || Objects.equals(difficulty, beginner)) {
            point = "Newbie | Beginner";
        }
        List<ResImage> imageList = resImageService.find("oog200", "template-workout", point, returnSize);
        int imageIndex = 0;
        int imageSize = imageList.size();
        String introType = planReq.getIntroType();
        List<ProjVideoGeneratePlanV2VO> planVOList = new ArrayList<>(returnSize);
        for (ProjVideoGenerate videoGenerate : dataUseList) {
            ProjVideoGeneratePlanV2VO planVO = new ProjVideoGeneratePlanV2VO();
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            List<ProjVideoGenerateI18n> i18nList = i18nMap.get(videoGenerate.getId());
            planVO.setGuidance(getGuidence(i18nList, introType));
            int id = videoGenerate.getId();
            List<ProjVideoV3VO> videos = videosMap.get(id);
            BigDecimal calorie = videoGenerate.getCalorie();
            if (Objects.nonNull(calorie)) {
                calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
            } else {
                calorie = BigDecimal.ZERO;
            }

            // image 轮流赋值
            if (imageSize != GlobalConstant.ZERO) {
                ResImage resImage = imageList.get(imageIndex);
                planVO.setName(resImage.getName());
                planVO.setCoverImg(getFullUrl(resImage.getCoverImage()));
                if (imageIndex < imageSize - 1) {
                    imageIndex++;
                } else {
                    imageIndex = GlobalConstant.ZERO;
                }
            }

            planVO.setId(id);
            planVO.setDuration(TimeConvertUtil.millisToMinutes(videoGenerate.getRealDuration()));
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            planVO.setCalorie(calorie);
            planVO.setDifficulty(difficulty);
            planVO.setVideos(videos);
            planVOList.add(planVO);
        }

        return planVOList;
    }

    @Override
    public List<ProjVideoGenerateRefreshPlanVO> selectVideoPlanByWorkoutIds(ProjVideoGenerateRefreshPlanReq refreshPlanReq) {
        List<Integer> idList = refreshPlanReq.getIdList();
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        List<Integer> templateIds = projTemplateService.selectTemplateIdsByDuration(10, 20);
        if (templateIds.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        LambdaQueryWrapper<ProjVideoGenerate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjVideoGenerate::getTemplateId, templateIds);
        queryWrapper.in(ProjVideoGenerate::getId, idList);
        queryWrapper.eq(ProjVideoGenerate::getDataVersion, GlobalConstant.THREE);
        List<ProjVideoGenerate> list = this.list(queryWrapper);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        int size = list.size();
        // 最终生成plan使用的数据
        List<Integer> ids = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            ProjVideoGenerate videoGenerate = list.get(i);
            ids.add(videoGenerate.getId());
        }

        LambdaQueryWrapper<ProjVideoGenerateI18n> i18nQueryWrapper = new LambdaQueryWrapper<>();
        i18nQueryWrapper.in(ProjVideoGenerateI18n::getGenerateId, ids);
        Map<Integer, List<ProjVideoGenerateI18n>> i18nMap = projVideoGenerateI18nService.list(i18nQueryWrapper)
                .stream().collect(Collectors.groupingBy(ProjVideoGenerateI18n::getGenerateId));
        String least = "Least";
        String introType = refreshPlanReq.getIntroType();
        List<ProjVideoGenerateRefreshPlanVO> planVOList = new ArrayList<>(size);
        // 查询video
        Map<Integer, List<ProjVideoV3VO>> videosMap = this.baseMapper.selectVideosV3ByIds(ids)
                .stream().collect(Collectors.groupingBy(ProjVideoV3VO::getGenerateId));
        for (ProjVideoGenerate videoGenerate : list) {
            ProjVideoGenerateRefreshPlanVO planVO = new ProjVideoGenerateRefreshPlanVO();
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            List<ProjVideoGenerateI18n> i18nList = i18nMap.get(videoGenerate.getId());
            List<ProjVideoGenerateI18nV3VO> guidanceList = new ArrayList<>(i18nList.size());
            for (ProjVideoGenerateI18n i18n : i18nList) {
                if (Objects.equals(introType, least) && StringUtils.isNotBlank(i18n.getGuidanceLeastUrl())) {
                    ProjVideoGenerateI18nV3VO guidance = new ProjVideoGenerateI18nV3VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceLeastUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceLeastAudioUrl()));
                    guidanceList.add(guidance);
                } else if (StringUtils.isNotBlank(i18n.getGuidanceDefaultUrl())) {
                    ProjVideoGenerateI18nV3VO guidance = new ProjVideoGenerateI18nV3VO();
                    guidance.setLanguage(i18n.getLanguage());
                    guidance.setSubtitlesUrl(getFullUrl(i18n.getGuidanceDefaultUrl()));
                    guidance.setAudioJsonUrl(getFullUrl(i18n.getGuidanceDefaultAudioUrl()));
                    guidanceList.add(guidance);
                }
            }
            int id = videoGenerate.getId();
            List<ProjVideoV3VO> videos = videosMap.get(id);
            BigDecimal calorie = videoGenerate.getCalorie();
            if (Objects.nonNull(calorie)) {
                calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
            } else {
                calorie = BigDecimal.ZERO;
            }
            planVO.setId(id);
            planVO.setDuration(TimeConvertUtil.millisToMinutes(videoGenerate.getRealDuration()));
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            planVO.setCalorie(calorie);
            planVO.setDifficulty(refreshPlanReq.getDifficulty());
            planVO.setGuidanceList(guidanceList);
            planVO.setVideos(videos);
            planVOList.add(planVO);
        }

        return planVOList;
    }

    @Override
    public List<ProjVideoGenerateRefreshPlanV2VO> selectVideoPlanByWorkoutIdsV2(ProjVideoGenerateRefreshPlanV2Req refreshPlanReq) {
        List<Integer> idList = refreshPlanReq.getIdList();
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        List<Integer> templateIds = projTemplateService.selectTemplateIdsByDuration(10, 20);
        if (templateIds.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        List<ProjVideoGenerate> list = getBaseMapper().selectVideosByTempAndIds(templateIds, idList, GlobalConstant.THREE);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>(GlobalConstant.ZERO);
        }
        int size = list.size();
        // 最终生成plan使用的数据
        List<Integer> ids = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            ProjVideoGenerate videoGenerate = list.get(i);
            ids.add(videoGenerate.getId());
        }
        Map<Integer, List<ProjVideoGenerateI18n>> i18nMap = projVideoGenerateI18nService.listByGenerateIdsIncludeDel(ids)
                .stream().collect(Collectors.groupingBy(ProjVideoGenerateI18n::getGenerateId));
        String introType = refreshPlanReq.getIntroType();
        List<ProjVideoGenerateRefreshPlanV2VO> planVOList = new ArrayList<>(size);
        // 查询video
        Map<Integer, List<ProjVideoV3VO>> videosMap = this.baseMapper.selectVideosV3ByIds(ids)
                .stream().collect(Collectors.groupingBy(ProjVideoV3VO::getGenerateId));
        for (ProjVideoGenerate videoGenerate : list) {
            ProjVideoGenerateRefreshPlanV2VO planVO = new ProjVideoGenerateRefreshPlanV2VO();
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            List<ProjVideoGenerateI18n> i18nList = i18nMap.get(videoGenerate.getId());
            planVO.setGuidance(getGuidence(i18nList, introType));
            int id = videoGenerate.getId();
            List<ProjVideoV3VO> videos = videosMap.get(id);
            BigDecimal calorie = videoGenerate.getCalorie();
            if (Objects.nonNull(calorie)) {
                calorie = calorie.setScale(GlobalConstant.ZERO, BigDecimal.ROUND_HALF_UP);
            } else {
                calorie = BigDecimal.ZERO;
            }
            planVO.setId(id);
            planVO.setDuration(TimeConvertUtil.millisToMinutes(videoGenerate.getRealDuration()));
            planVO.setVideoUrl(getFullUrl(videoGenerate.getVideo1Url()));
            planVO.setCalorie(calorie);
            planVO.setDifficulty(refreshPlanReq.getDifficulty());
            planVO.setVideos(videos);
            planVOList.add(planVO);
        }

        return planVOList;
    }


}
