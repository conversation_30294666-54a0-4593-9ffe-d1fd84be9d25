package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjYogaUserInviteResultVO {

    @ApiModelProperty(value = "用户Id")
    private Integer userId;

    @ApiModelProperty(value = "邀请成功的人数")
    private Integer inviteSuccessNum;

    @ApiModelProperty(value = "奖品列表")
    private List<ProjYogaAwardVO> inviteAwardList;

}
