package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.ProjAutoWorkoutBasicInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaAutoWorkoutBO;
import com.laien.cmsapp.oog200.bo.ProjYogaAutoWorkoutPlanBO;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesAutoWorkout;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutTemplatePub;
import com.laien.cmsapp.oog200.mapper.ProjWallPilatesAutoWorkoutMapper;
import com.laien.cmsapp.oog200.requst.ProjWorkoutPlanReq;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;
import com.laien.cmsapp.oog200.response.ProjYogaAutoWorkoutListVO;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IProjAutoWorkoutBasicInfoService;
import com.laien.cmsapp.oog200.service.IProjWallPilatesAutoWorkoutService;
import com.laien.cmsapp.oog200.service.IProjWallPilatesVideoService;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutTemplatePubService;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.oog200.enums.*;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * Wall pilates auto workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Slf4j
@Service
public class ProjWallPilatesAutoWorkoutServiceImpl extends ServiceImpl<ProjWallPilatesAutoWorkoutMapper, ProjWallPilatesAutoWorkout> implements IProjWallPilatesAutoWorkoutService {

    @Resource
    private IProjYogaAutoWorkoutTemplatePubService projYogaAutoWorkoutTemplatePubService;

    @Resource
    private IProjWallPilatesVideoService projWallPilatesVideoService;

    @Resource
    private IProjAutoWorkoutBasicInfoService workoutBasicInfoService;

    @Override
    public List<ProjPlanWorkoutListVO> getPlan(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO, String lang) {
        YogaAutoWorkoutTemplateEnum templateTypeEnum = YogaAutoWorkoutTemplateEnum.getByCode(planReq.getPlanTypeCode());
        DurationEnum durationEnum = DurationEnum.getByCode(planReq.getDurationCode());
        Set<Integer> targetCodeSet = planReq.getTargetCodeSet();
        List<TargetEnum> targetEnumList = new ArrayList<>();
        if (CollUtil.isNotEmpty(targetCodeSet)) {
            for (Integer code : targetCodeSet) {
                TargetEnum targetEnum = TargetEnum.getByCode(code);
                if (null != targetEnum) {
                    targetEnumList.add(targetEnum);
                }
            }
        }
        List<ProjYogaAutoWorkoutTemplatePub> templateList = projYogaAutoWorkoutTemplatePubService.list(templateTypeEnum.getName(), versionInfoBO);
        if (CollUtil.isEmpty(templateList)) {
            String error = "not found wall pilates auto workout template, planReq:{}";
            log.error(error, planReq);
            return new ArrayList<>();
        }
        assert durationEnum != null;
        return matchWorkoutList(templateList, durationEnum, targetEnumList, planReq);
    }


    @Override
    public List<ProjPlanWorkoutListVO> query(List<Integer> idList, Integer m3u8Type, String lang) {
        if(CollUtil.isEmpty(idList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseModel::getId, idList)
                .eq(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjWallPilatesAutoWorkout> workoutList = baseMapper.selectList(wrapper);
        return toPlanWorkoutListVO(workoutList,m3u8Type,lang);
    }

    private List<ProjPlanWorkoutListVO> matchWorkoutList(List<ProjYogaAutoWorkoutTemplatePub> templateList, DurationEnum durationEnum, List<TargetEnum> targetEnumList, ProjWorkoutPlanReq planReq) {
        String lang = planReq.getLang();
        List<Integer> templateIdList = templateList.stream().map(BaseModel::getId).collect(Collectors.toList());
        LambdaQueryWrapper<ProjWallPilatesAutoWorkout> wrapper = new LambdaQueryWrapper<>();
        assert durationEnum != null;
        Integer min = durationEnum.getMin();
        Integer max = durationEnum.getMax();
        List<String> targetList = targetEnumList.stream().map(TargetEnum::getName).collect(Collectors.toList());
        wrapper.eq(ProjWallPilatesAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE)
                .in(ProjWallPilatesAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIdList);
        List<ProjWallPilatesAutoWorkout> allWorkout = baseMapper.selectList(wrapper);
        //duration、target都匹配的
        Set<Integer> matchedBothWorkoutIdSet = new HashSet<>();
        //duration匹配，target不匹配
        Set<Integer> matchedDurationWorkoutIdSet = new HashSet<>();
        //target匹配，duration不匹配
        Set<Integer> matchedTargetWorkoutIdSet = new HashSet<>();
        //target、duration任意匹配一个
        Set<Integer> matchedAnyWorkoutIdSet = new HashSet<>();

        for (ProjWallPilatesAutoWorkout workout : allWorkout) {
            String target = workout.getTarget();
            Integer id = workout.getId();
            Integer duration = workout.getDuration();
            if (duration < max && duration >= min) {
                if (targetList.contains(target)) {
                    matchedBothWorkoutIdSet.add(id);
                    matchedAnyWorkoutIdSet.add(id);
                } else {
                    matchedDurationWorkoutIdSet.add(id);
                    matchedAnyWorkoutIdSet.add(id);
                }
            } else if (targetList.contains(target)) {
                matchedTargetWorkoutIdSet.add(id);
                matchedAnyWorkoutIdSet.add(id);
            }
        }

        BasicInfoPointTargetMappingEnum mappingEnum = BasicInfoPointTargetMappingEnum.getByTargetList(targetEnumList);
        if (null == mappingEnum) {
            mappingEnum = BasicInfoPointTargetMappingEnum.FULL_BODY_MAPPING;
            log.error("target list mapping not found, target list is: {}", targetList);
        }
        Map<Integer, ProjWallPilatesAutoWorkout> workoutMap = allWorkout.stream()
                .collect(Collectors.toMap(ProjWallPilatesAutoWorkout::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );
        DifficultyEnum difficultyEnum = null;
        List<ProjAutoWorkoutBasicInfoBO> basicInfoList = workoutBasicInfoService.listEnable(YogaAutoWorkoutTemplateEnum.WALL_PILATES, mappingEnum.getPoint(), difficultyEnum);
        for (ProjAutoWorkoutBasicInfoBO infoBO : basicInfoList) {
            Set<Integer> matchWorkoutIdList = new HashSet<>(CollUtil.intersection(infoBO.getWorkoutIds(), matchedAnyWorkoutIdSet));
            List<ProjYogaAutoWorkoutBO> autoWorkoutList = new ArrayList<>();
            for (Integer id : matchWorkoutIdList) {
                ProjYogaAutoWorkoutBO workoutBO = new ProjYogaAutoWorkoutBO();
                workoutBO.setWorkoutId(id);
                infoBO.setMatchWorkoutList(autoWorkoutList);
            }
        }
        ProjYogaAutoWorkoutPlanBO autoWorkoutPlanBO = new ProjYogaAutoWorkoutPlanBO();
        autoWorkoutPlanBO.setLang(lang);
        autoWorkoutPlanBO.setBasicInfoBOList(basicInfoList);
        autoWorkoutPlanBO.setMatchAllWorkoutIds(matchedBothWorkoutIdSet);
        autoWorkoutPlanBO.setMatchDurationWorkoutIds(matchedDurationWorkoutIdSet);
        autoWorkoutPlanBO.setMatchOtherWorkoutIds(matchedTargetWorkoutIdSet);

        List<ProjPlanWorkoutListVO> matchedWorkoutVOList = workoutBasicInfoService.generatePlan(autoWorkoutPlanBO);
        Set<Integer> matchedWorkoutIdSet = matchedWorkoutVOList.stream().map(ProjYogaAutoWorkoutListVO::getId).collect(Collectors.toSet());

        Map<Integer, List<ResYogaVideoDetailVO>> videoDetailI18nVO = getVideoDetailI18nVO(matchedWorkoutIdSet, lang);
        for (ProjPlanWorkoutListVO workout : matchedWorkoutVOList) {
            ProjWallPilatesAutoWorkout autoWorkout = workoutMap.get(workout.getId());
            toPlanWorkoutVO(autoWorkout, workout, videoDetailI18nVO, planReq.getM3u8Type());
        }
        if (CollUtil.isEmpty(matchedWorkoutVOList) || matchedWorkoutVOList.size() < GlobalConstant.TWENTY_ONE) {
            log.error("wall pilates auto workout count less than 21,planRes: {}", planReq);
        }
        return matchedWorkoutVOList;
    }



    /**
     * 获取翻译后的vide detail,key：workout,value：videoDetail
     */
    private Map<Integer, List<ResYogaVideoDetailVO>> getVideoDetailI18nVO(Set<Integer> workoutIdSet, String language){
        List<ResYogaVideoDetailVO> videoList = projWallPilatesVideoService.find(workoutIdSet);
        return videoList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));
    }

    private List<ProjPlanWorkoutListVO> toPlanWorkoutListVO(List<ProjWallPilatesAutoWorkout> workoutList, Integer m3u8Type, String language) {
        if (CollUtil.isEmpty(workoutList)) {
            return new ArrayList<>();
        }
        Set<Integer> workoutIdSet = workoutList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjPlanWorkoutListVO> workoutVOList = new ArrayList<>(workoutList.size());
        Map<Integer, List<ResYogaVideoDetailVO>> videoMap = getVideoDetailI18nVO(workoutIdSet, language);

        for (ProjWallPilatesAutoWorkout autoWorkout : workoutList) {
            ProjPlanWorkoutListVO workoutVO = new ProjPlanWorkoutListVO();
            workoutVOList.add(workoutVO);
            toPlanWorkoutVO(autoWorkout, workoutVO, videoMap, m3u8Type);
        }
        return workoutVOList;
    }

    private static void toPlanWorkoutVO(ProjWallPilatesAutoWorkout source, ProjPlanWorkoutListVO target, Map<Integer, List<ResYogaVideoDetailVO>> videoMap, Integer m3u8Type) {
        String eventNameTemplate = "%s-auto-%s";
        YogaAutoWorkoutTemplateEnum planType = YogaAutoWorkoutTemplateEnum.WALL_PILATES;
        BeanUtils.copyProperties(source, target);
        target.setPlanTypeCode(planType.getCode());
        target.setDuration(TimeConvertUtil.millisToMinutes(source.getDuration()));
        target.setVideos(videoMap.get(source.getId()));
        target.setEventName(String.format(eventNameTemplate, planType.getName(), target.getId()));
        TargetEnum targetEnum = TargetEnum.getByName(source.getTarget());
        if (null != targetEnum) {
            target.setTargetCode(targetEnum.getCode());
        }
        PositionEnum positionEnum = PositionEnum.getByName(source.getPosition());
        if (null != positionEnum) {
            target.setPositionCode(positionEnum.getCode());
        }
        //替换m3u8地址
        if (ObjectUtil.notEqual(GlobalConstant.TWO, m3u8Type) && StrUtil.isNotBlank(target.getVideo2532Url())) {
            target.setVideoM3u8Url(target.getVideo2532Url());
        }
    }
}
