package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * proj yoga pose workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseWorkoutPub对象", description="proj yoga pose workout")
public class ProjYogaPoseWorkoutDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "sanskrit name")
    private String sanskritName;

    @ApiModelProperty(value = "workout 封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "workout 详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "pose 彩色图像")
    @AbsoluteR2Url
    private String poseLightImgUrl;

    @ApiModelProperty(value = "pose 黑白图像")
    @AbsoluteR2Url
    private String poseDarkImgUrl;

    @ApiModelProperty(value = "0: Newbie,1: Beginner,2: Intermediate,3: Advanced")
    private Integer difficultyCode;

    @ApiModelProperty(value = "pose详细介绍")
    private List<String> instructionsList;

    @ApiModelProperty(value = "pose的优势")
    private List<String> benefitsList;

    @ApiModelProperty(value = "chair variation for seniors")
    private String chairVariation;

    @ApiModelProperty(value = "chair variation image")
    @AbsoluteR2Url
    private String chairVariationImgUrl;

    @ApiModelProperty(value = "chair variation tips for beginner")
    private List<String> chairVariationTipsList;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer flexibility;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer balance;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer strength;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer relaxation;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @AbsoluteR2Url
    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @AbsoluteR2Url
    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @AbsoluteR2Url
    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称")
    private String audioShortJson;

    @ApiModelProperty(value = "视频播放弹窗时间点")
    private String videoMaskJson;

    @ApiModelProperty(value = "0:Flexibility,1:Balance,2:Strength,3:Relaxation")
    private Integer focusCode;

    @ApiModelProperty(value = "0:Standing,1:Seated,2:Supine,3:Prone,4:Arm & Leg Support")
    private Integer positionCode;

    @JsonIgnore
    @ApiModelProperty(value = "yogaPoseGroupId")
    private Integer yogaPoseGroupId;

    @ApiModelProperty(value = "guidance object")
    private ProjYogaAutoWorkoutGuidanceVO guidance;
}
