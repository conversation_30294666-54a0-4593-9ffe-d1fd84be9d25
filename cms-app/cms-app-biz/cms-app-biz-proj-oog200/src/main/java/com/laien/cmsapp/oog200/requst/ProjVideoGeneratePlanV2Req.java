package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video random
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video plan v2", description = "video plan v2")
public class ProjVideoGeneratePlanV2Req {

    @ApiModelProperty(value = "difficulty 为空或不存在的值默认为Newbie")
    private String difficulty;

    @ApiModelProperty(value = "Default | Least ,不填则查全部")
    private String introType;

}
