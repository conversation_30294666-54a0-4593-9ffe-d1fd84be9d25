package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * Fasting article
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjFastingArticlePub对象", description="Fasting article")
public class ProjFastingArticleDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String titleName;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "类型100:Fasting Basics,101:Fasting Hacks and Tips")
    private Integer typeCode;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "参考文档")
    private String reference;

    @ApiModelProperty(value = "是否收费 0不收费 1收费,允许为空")
    private Integer subscription;

}
