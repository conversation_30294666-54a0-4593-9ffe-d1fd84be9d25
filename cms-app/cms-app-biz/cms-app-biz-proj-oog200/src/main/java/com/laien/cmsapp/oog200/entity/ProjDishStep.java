package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Dish step
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_dish_step")
@ApiModel(value="ProjDishStep对象", description="Dish step")
public class ProjDishStep extends BaseModel {


    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projDishId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
