package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: video plan 更新v1
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video plan 更新v1", description = "video plan 更新v1")
public class ProjVideoGenerateRefreshPlanReq extends ProjVideoGeneratePlanReq{

    @ApiModelProperty(value = "id list", required = true)
    private List<Integer> idList;

}
