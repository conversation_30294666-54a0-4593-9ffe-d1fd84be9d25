package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "video guidance", description = "video guidance")
public class ProjYogaAutoWorkoutGuidanceVO {

    @ApiModelProperty(value = "default guidanceList")
    @JsonProperty("default")
    private List<ProjYogaAutoWorkoutGuidanceItemVO> default_;

    @ApiModelProperty(value = "least guidanceList")
    private List<ProjYogaAutoWorkoutGuidanceItemVO> least;


    public static ProjYogaAutoWorkoutGuidanceVO buildGuidance(String audioLongJson, String audioShortJson, String language) {

        ProjYogaAutoWorkoutGuidanceVO guidanceVO = new ProjYogaAutoWorkoutGuidanceVO();
        List<ProjYogaAutoWorkoutGuidanceItemVO> default_ = new ArrayList<>();
        List<ProjYogaAutoWorkoutGuidanceItemVO> least = new ArrayList<>();

        ProjYogaAutoWorkoutGuidanceItemVO detailLongVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailLongVO.setLanguage(language);
        detailLongVO.setAudioJsonUrl(audioLongJson);
        default_.add(detailLongVO);

        ProjYogaAutoWorkoutGuidanceItemVO detailShortVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailShortVO.setLanguage(language);
        detailShortVO.setAudioJsonUrl(audioShortJson);
        least.add(detailShortVO);

        guidanceVO.setDefault_(default_);
        guidanceVO.setLeast(least);
        return guidanceVO;
    }

    public static ProjYogaAutoWorkoutGuidanceItemVO buildGuidanceItem(String audioJson, String language) {

        ProjYogaAutoWorkoutGuidanceItemVO itemVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        itemVO.setLanguage(language);
        itemVO.setAudioJsonUrl(audioJson);
        return itemVO;
    }

}
