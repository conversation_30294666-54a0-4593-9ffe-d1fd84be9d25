package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseGroupPub对象", description="proj yoga pose grouping")
public class ProjYogaPoseGroupDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "event_name")
    private String eventName;

    @ApiModelProperty(value = "descriptionList")
    private List<String> descriptionList;

    @AbsoluteR2Url
    @ApiModelProperty(value = "group 彩色封面")
    private String groupImgLightUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "group 黑色封面")
    private String groupImgDarkUrl;

    @ApiModelProperty(value = "workoutList")
    private List<ProjYogaPoseWorkoutDetailVO> workoutList;

}
