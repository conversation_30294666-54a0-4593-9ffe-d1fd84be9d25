package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjAutoWorkoutBasicInfoWorkoutRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjAutoWorkoutBasicInfoWorkoutRelationMapper;
import com.laien.cmsapp.oog200.service.IProjAutoWorkoutBasicInfoWorkoutRelationService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/10/22 14:14
 */
@Service
public class ProjAutoWorkoutBasicInfoWorkoutRelationServiceImpl extends ServiceImpl<ProjAutoWorkoutBasicInfoWorkoutRelationMapper, ProjAutoWorkoutBasicInfoWorkoutRelationPub> implements IProjAutoWorkoutBasicInfoWorkoutRelationService {


    @Override
    public List<ProjAutoWorkoutBasicInfoWorkoutRelationPub> listByBasicInfoIds(Collection<Integer> basicInfoIds) {
        if (CollectionUtils.isEmpty(basicInfoIds)) {
            return Collections.emptyList();
        }
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfoWorkoutRelationPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjAutoWorkoutBasicInfoWorkoutRelationPub::getVersion, versionInfoBO.getCurrentVersion())
                .in(ProjAutoWorkoutBasicInfoWorkoutRelationPub::getProjAutoWorkoutBasicInfoId, basicInfoIds);
        return list(queryWrapper);
    }
}
