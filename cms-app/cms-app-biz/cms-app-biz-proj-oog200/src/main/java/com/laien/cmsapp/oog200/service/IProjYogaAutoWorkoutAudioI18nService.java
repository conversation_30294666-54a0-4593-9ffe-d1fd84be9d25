package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutAudioI18n;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;

import java.util.List;
import java.util.Set;

/**
 * Author:  hhl
 * Date:  2024/11/25 11:24
 */
public interface IProjYogaAutoWorkoutAudioI18nService extends IService<ProjYogaAutoWorkoutAudioI18n> {

    List<ProjYogaAutoWorkoutAudioI18n> getAudioI18n4Workout(Set<Integer> workoutIdSet, Integer workoutType);

    List<ProjYogaAutoWorkoutAudioI18n> getAudioI18n4Workout(Set<Integer> workoutIdSet, Integer workoutType, String lang);

    void setAudioI18n4Workout(List<ProjPlanWorkoutListVO> workoutList);
}
