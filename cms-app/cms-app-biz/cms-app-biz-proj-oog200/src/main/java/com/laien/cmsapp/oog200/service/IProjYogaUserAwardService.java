package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjYogaUserAward;
import com.laien.cmsapp.oog200.response.ProjYogaUserAwardVO;

import java.util.Collection;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/12
 */
public interface IProjYogaUserAwardService extends IService<ProjYogaUserAward> {

    List<ProjYogaUserAwardVO> listByInviteCode(Collection<String> inviteCodes);

}
