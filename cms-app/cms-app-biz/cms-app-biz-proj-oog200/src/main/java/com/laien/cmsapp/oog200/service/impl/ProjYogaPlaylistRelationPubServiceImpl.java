package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPlaylistRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaPlaylistRelationPubMapper;
import com.laien.cmsapp.oog200.service.IProjYogaPlaylistRelationPubService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/21 14:48
 */
@Service
public class ProjYogaPlaylistRelationPubServiceImpl extends ServiceImpl<ProjYogaPlaylistRelationPubMapper, ProjYogaPlaylistRelationPub> implements IProjYogaPlaylistRelationPubService {

    @Override
    public List<ProjYogaPlaylistRelationPub> listByPlaylistIds(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> playlistIds) {

        LambdaQueryWrapper<ProjYogaPlaylistRelationPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaPlaylistRelationPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjYogaPlaylistRelationPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.in(ProjYogaPlaylistRelationPub::getProjYogaPlaylistId, playlistIds);
        queryWrapper.orderByAsc(ProjYogaPlaylistRelationPub::getId);

        List<ProjYogaPlaylistRelationPub> relationPubList = list(queryWrapper);
        return relationPubList;
    }

}
