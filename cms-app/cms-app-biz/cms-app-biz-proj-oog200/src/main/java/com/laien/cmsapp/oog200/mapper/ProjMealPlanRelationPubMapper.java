package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjMealPlanRelationPub;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:51
 */
public interface ProjMealPlanRelationPubMapper extends BaseMapper<ProjMealPlanRelationPub> {

    @Select(value = "select id, version, proj_meal_plan_id, `day`, dish_type, proj_dish_id from proj_meal_plan_relation_pub where version = #{version} and proj_meal_plan_id = #{mealPlanId} and del_flag = 0")
    List<ProjMealPlanRelationPub> listByVersionAndMealPlanId(Integer version, Integer mealPlanId);

}
