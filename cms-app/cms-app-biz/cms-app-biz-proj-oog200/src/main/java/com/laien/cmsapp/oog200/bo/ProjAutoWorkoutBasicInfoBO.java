package com.laien.cmsapp.oog200.bo;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/10/22 14:27
 */
@Data
public class ProjAutoWorkoutBasicInfoBO implements AppTextCoreI18nModel {


    private Integer id;

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "详情图（默认女）")
    private String detailImage;

    @ApiModelProperty(value = "封面图(male)")
    private String coverImageMale;

    @ApiModelProperty(value = "complete_image")
    private String completeImage;

    @ApiModelProperty(value = "封面图（默认和女）")
    private String coverImage;

    @ApiModelProperty(value = "配置中：Image对应的workout Ids")
    private List<Integer> workoutIds;

    private List<ProjYogaAutoWorkoutBO> matchWorkoutList;
}
