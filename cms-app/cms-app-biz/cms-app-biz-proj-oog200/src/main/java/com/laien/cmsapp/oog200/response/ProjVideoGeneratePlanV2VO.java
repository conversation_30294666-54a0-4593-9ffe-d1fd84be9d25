package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video plan v1
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video plan v2", description = "video plan v2")
public class ProjVideoGeneratePlanV2VO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "时长")
    private Integer duration;
    @ApiModelProperty(value = "video url")
    private String videoUrl;
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "图片路径")
    private String coverImg;

    @ApiModelProperty(value = "guidance object")
    private ProjVideoGenerateI18nV4VO guidance;
    @ApiModelProperty(value = "videos")
    private List<ProjVideoV3VO> videos;

}
