package com.laien.cmsapp.oog200.entity;

import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2025/1/8 17:25
 */
@Data
public class ProjAllergenRelationPub extends BaseModel {

    private Integer version;

    @ApiModelProperty(value = "业务表数据id")
    private Integer dataId;

    @ApiModelProperty(value = "proj_allergen表数据id")
    private Integer projAllergenId;

    @ApiModelProperty(value = "业务类型")
    private AllergenRelationBusinessEnum businessType;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
