package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 普拉提workout和video关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_wall_pilates_regular_workout_video_relation_pub")
@ApiModel(value="ProjWallPilatesRegularWorkoutVideoRelationPub对象", description="普拉提workout和video关系表")
public class ProjWallPilatesRegularWorkoutVideoRelationPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "普拉提workout id")
    private Integer projWallPilatesRegularWorkoutId;

    @ApiModelProperty(value = "普拉提video id")
    private Integer projWallPilatesVideoId;


}
