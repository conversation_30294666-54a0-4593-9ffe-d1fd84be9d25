package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjChairYogaAutoWorkout;
import com.laien.cmsapp.oog200.requst.ProjWorkoutPlanReq;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/10/12 11:56
 */
public interface IProjChairYogaAutoWorkoutService extends IService<ProjChairYogaAutoWorkout> {

    /**
     * 根据 planType + target 生成 plan
     * @param planReq
     * @param versionInfoBO
     * @return
     */
    List<ProjPlanWorkoutListVO> getPlan(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    /**
     * 根据planType + difficult 生成plan
     * @param planReq
     * @param versionInfoBO
     * @return
     */
    List<ProjPlanWorkoutListVO> getPlanV2(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO);

    List<ProjPlanWorkoutListVO> query(List<Integer> idList, Integer m3u8Type, String language);

}
