package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_adapty_event")
@ApiModel(value="ProjYogaAdaptyEvent对象", description="yoga adapty event")
public class ProjYogaAdaptyEvent extends BaseModel {

    @ApiModelProperty(value = "被邀请人 user id")
    private Integer userId;

    @ApiModelProperty(value = "邀请人的邀请码")
    private String inviteCode;

    @ApiModelProperty(value = "所订阅的产品Id")
    private String productId;

    @ApiModelProperty(value = "事务Id")
    private String transactionId;

    @ApiModelProperty(value = "事务Id")
    private String originalTransactionId;

    @ApiModelProperty(value = "profile Id")
    private String profileId;

    @ApiModelProperty(value = "购买日期")
    private String purchaseDate;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "处理标识 0 未处理，1已处理")
    private Integer handleFlag;

}
