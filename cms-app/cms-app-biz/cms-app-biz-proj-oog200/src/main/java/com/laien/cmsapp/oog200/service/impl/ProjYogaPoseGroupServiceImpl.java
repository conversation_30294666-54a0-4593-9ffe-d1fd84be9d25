package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutAudioI18n;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseGroupPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaPoseGroupMapper;
import com.laien.cmsapp.oog200.response.ProjYogaPoseGroupDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaPoseWorkoutDetailVO;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutAudioI18nService;
import com.laien.cmsapp.oog200.service.IProjYogaPoseGroupService;
import com.laien.cmsapp.oog200.service.IProjYogaPoseWorkoutService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.common.oog200.enums.YogaPoseGroupTypeEnum;
import com.laien.common.util.MyStringUtil;
import com.laien.mybatisplus.config.BaseModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * proj yoga pose grouping 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Service
public class ProjYogaPoseGroupServiceImpl extends ServiceImpl<ProjYogaPoseGroupMapper, ProjYogaPoseGroupPub>
        implements IProjYogaPoseGroupService {

    @Resource
    private IProjYogaPoseWorkoutService projYogaPoseWorkoutService;

    @Resource
    private I18nUtil i18nUtil;

    @Resource
    private IProjYogaAutoWorkoutAudioI18nService projYogaAutoWorkoutAudioI18nService;

    @Override
    public List<ProjYogaPoseGroupDetailVO> findByYogaPoseLevelId(Integer yogaPoseLevelId, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type, String lang) {

        List<ProjYogaPoseGroupPub> poseGroupList = baseMapper.findByYogaPoseLevelId(yogaPoseLevelId, versionInfoBO);
        i18nUtil.translate(poseGroupList, ProjCodeEnums.OOG200, lang);
        return covertToDetailVO(lang,versionInfoBO, m3u8Type, poseGroupList);
    }

    @Override
    public List<ProjYogaPoseGroupDetailVO> list(Integer m3u8Type, ProjPublishCurrentVersionInfoBO versionInfoBO, String lang) {

        LambdaQueryWrapper<ProjYogaPoseGroupPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaPoseGroupPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjYogaPoseGroupPub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaPoseGroupPub::getType, YogaPoseGroupTypeEnum.TRAINING_PATH.getName())
                .eq(BaseModel::getDelFlag, GlobalConstant.NO)
                .eq(ProjYogaPoseGroupPub::getStatus, GlobalConstant.STATUS_ENABLE)
                .orderByAsc(ProjYogaPoseGroupPub::getSort)
                .orderByDesc(BaseModel::getId);
        List<ProjYogaPoseGroupPub> poseGroupList = baseMapper.selectList(wrapper);

        i18nUtil.translate(poseGroupList, ProjCodeEnums.OOG200, lang);
        return covertToDetailVO(lang,versionInfoBO, m3u8Type, poseGroupList);
    }

    private List<ProjYogaPoseGroupDetailVO> covertToDetailVO(String lang,ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type, List<ProjYogaPoseGroupPub> poseGroupList) {
        if (CollUtil.isEmpty(poseGroupList)) {
            return null;
        }
        Set<Integer> idSet = poseGroupList.stream().map(BaseModel::getId).collect(Collectors.toSet());
        List<ProjYogaPoseWorkoutDetailVO> poseWorkoutList = projYogaPoseWorkoutService.findByYogaPoseGroupId(idSet, versionInfoBO, m3u8Type);
        // 查询多语言音频数据，为其替换 video mask
        Set<Integer> poseVideoWorkoutIdSet = poseWorkoutList.stream().map(ProjYogaPoseWorkoutDetailVO::getId).collect(Collectors.toSet());
        Map<Integer, ProjYogaAutoWorkoutAudioI18n> audioI18nMap = projYogaAutoWorkoutAudioI18nService.getAudioI18n4Workout(poseVideoWorkoutIdSet, YogaAutoWorkoutTemplateEnum.POSE_LIBRARY.getCode(), lang).stream().collect(Collectors.toMap(ProjYogaAutoWorkoutAudioI18n::getWorkoutId, Function.identity(), (oldValue, newValue) -> oldValue));
        poseWorkoutList.stream()
                .filter(workout -> audioI18nMap.containsKey(workout.getId()) && StringUtils.isNotBlank(audioI18nMap.get(workout.getId()).getVideoMaskJson()))
                .forEach(workout -> {
            workout.setVideoMaskJson(audioI18nMap.get(workout.getId()).getVideoMaskJson());

        });

        Map<Integer, List<ProjYogaPoseWorkoutDetailVO>> workoutMap = poseWorkoutList.stream().collect(Collectors.groupingBy(ProjYogaPoseWorkoutDetailVO::getYogaPoseGroupId));
        List<ProjYogaPoseGroupDetailVO> poseGroupVOList = new ArrayList<>(poseGroupList.size());
        for (ProjYogaPoseGroupPub poseGroup : poseGroupList) {
            ProjYogaPoseGroupDetailVO poseGroupVO = new ProjYogaPoseGroupDetailVO();
            BeanUtils.copyProperties(poseGroup, poseGroupVO);
            String description = poseGroup.getDescription();
            if (StringUtils.isNotBlank(description)) {
                poseGroupVO.setDescriptionList(MyStringUtil.toList(description));
            }
            List<ProjYogaPoseWorkoutDetailVO> workoutDetailList = workoutMap.get(poseGroup.getId());
            poseGroupVO.setWorkoutList(workoutDetailList);
            poseGroupVOList.add(poseGroupVO);

        }
        return poseGroupVOList;
    }
}
