package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.bo.ProjAutoWorkoutBasicInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaAutoWorkoutPlanBO;
import com.laien.cmsapp.oog200.entity.ProjAutoWorkoutBasicInfoPub;
import com.laien.common.oog200.enums.AutoWorkoutBasicInfoPointEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.cmsapp.oog200.requst.ProjAutoWorkoutListReq;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;

import java.util.List;

/**
 * <p>
 * proj_image 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface IProjAutoWorkoutBasicInfoService extends IService<ProjAutoWorkoutBasicInfoPub> {

    List<ProjPlanWorkoutListVO> generatePlan(ProjYogaAutoWorkoutPlanBO workoutPlanBO);

    List<ProjAutoWorkoutBasicInfoBO> listEnable(YogaAutoWorkoutTemplateEnum planType, AutoWorkoutBasicInfoPointEnum point, DifficultyEnum difficulty);

    List<ProjPlanWorkoutListVO> setIntoWorkout(List<ProjPlanWorkoutListVO> workoutList, ProjAutoWorkoutListReq workoutListReq);
}
