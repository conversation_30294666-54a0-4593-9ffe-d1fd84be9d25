package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramLevelPub;
import com.laien.cmsapp.oog200.response.ProjYogaProgramLevelListVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:50
 */
public interface IProjYogaProgramLevelPubService extends IService<ProjYogaProgramLevelPub> {

    List<ProjYogaProgramLevelListVO> listVOByIds(Collection<Integer> programLevelIds, Integer version, Integer status, String lang);

}
