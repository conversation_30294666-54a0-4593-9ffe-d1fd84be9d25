package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseLevelPub对象", description="proj yoga pose grouping")
public class ProjYogaPoseLevelDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "level name")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "description")
    private String description;

    @ApiModelProperty(value = "0: Newbie,1: Beginner,2: Intermediate,3: Advanced")
    private Integer difficultyCode;

    @ApiModelProperty(value = "yogaPoseWorkoutList")
    private List<ProjYogaPoseWorkoutDetailVO> yogaPoseWorkoutList;

}
