package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjYogaPlaylistVO;
import com.laien.cmsapp.oog200.service.IProjYogaPlaylistPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 项目播放列表表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-09
 */
@Slf4j
@Api(tags = "app端：yoga playlist")
@RestController
@RequestMapping(value = {"/{appCode}/yogaPlaylist"})
public class ProjYogaPlaylistController extends ResponseController {

    @Resource
    private IProjYogaPlaylistPubService playlistPubService;

    @ApiOperation(value = "根据歌单类型获取歌单列表", tags ={"oog200"},
            notes = "类型可传多个，例如 playlistTypeCodes: 103,104。code和值的对应关系为101: Plan_Classic and Chair，102: Animation, 103: Meditation, 104: Soundscape, 105: Pose Library ")
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjYogaPlaylistVO>> listByPlaylistType(@RequestParam List<Integer> playlistTypeCodes,
                                                                       @RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjYogaPlaylistVO> projPlaylistAppVOList = playlistPubService.listByPlaylistCodes(versionInfoBO, playlistTypeCodes, GlobalConstant.STATUS_ENABLE, lang);
        return succ(projPlaylistAppVOList);
    }

}
