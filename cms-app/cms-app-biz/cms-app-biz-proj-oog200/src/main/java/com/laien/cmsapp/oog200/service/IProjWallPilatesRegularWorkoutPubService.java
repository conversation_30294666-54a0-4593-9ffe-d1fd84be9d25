package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesRegularWorkoutPub;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * oog200 workout 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface IProjWallPilatesRegularWorkoutPubService extends IService<ProjWallPilatesRegularWorkoutPub> {

    List<ProjYogaRegularWorkoutDetailVO> list(Set<Integer> idSet, String lang, Integer m3u8Type, Integer status, ProjPublishCurrentVersionInfoBO versionInfo);

    List<ProjYogaRegularWorkoutListVO> queryList(String lang, ProjPublishCurrentVersionInfoBO versionInfo, Set<Integer> categoryCodeSet);

}
