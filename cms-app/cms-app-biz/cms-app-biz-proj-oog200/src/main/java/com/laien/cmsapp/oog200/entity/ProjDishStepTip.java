package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Dish step tip
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_dish_step_tip")
@ApiModel(value="ProjDishStepTip对象", description="Dish step tip")
public class ProjDishStepTip extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projDishId;

    @ApiModelProperty(value = "proj_dish_step表数据id")
    private Integer projDishStepId;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "介绍")
    private String intro;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
