package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Author:  hhl
 * Date:  2024/12/31 16:46
 */
@Data
public class ProjMealPlanListVO {

    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "eventName")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "是否收费 0不收费 1收费, 可为空")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间, 可为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间, 可为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "排序")
    @JsonIgnore
    private Integer sorted;

    @ApiModelProperty(value = "用于APP端，显示一个固定的标签")
    private Boolean replacementTag;

    @ApiModelProperty(value = "标签值，以英文逗号做分隔，如A,B,C 可为空")
    private String keywords;

    @ApiModelProperty(value = "描述")
    @JsonProperty(value = "desc")
    private String description;

    @ApiModelProperty(value = "plan天数")
    private Integer days;
}
