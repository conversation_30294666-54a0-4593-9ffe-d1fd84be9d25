package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * note: workout Video detail
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "workout Video detail", description = "workout Video detail")
public class ProjYogaRegularWorkoutDetailVO implements AppTextCoreI18nModel {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名字")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "0:Classic Yoga,1:Wall Pilates,2:Chair Yoga")
    private Integer workoutTypeCode;

    @AbsoluteR2Url
    @ApiModelProperty(value = "封面图")
    private String coverImgUrl;

    @AbsoluteR2Url
    @ApiModelProperty(value = "详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "难度")
    @AppTextTranslateField
    private String difficulty;

    @ApiModelProperty(value = "0: Newbie,1: Beginner,2: Intermediate,3: Advanced")
    private Integer difficultyCode;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长 分钟")
    private Integer duration;

    @ApiModelProperty(value = "总时长 毫秒")
    @JsonIgnore
    private Integer duration4Millis;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new开始时间")
    private LocalDateTime newStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "new结束时间")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "描述")
    @AppTextTranslateField
    private String description;

    @JsonProperty("videoUrl")
    @AbsoluteR2Url
    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @JsonIgnore
    @ApiModelProperty(value = "video的2532 m3u8地址")
    private String video2532Url;

    @AbsoluteR2Url
    @ApiModelProperty(value = "多分辨率m3u8视频地址，用于IOS端下载")
    private String multiVideoUrl;

    @JsonIgnore
    @ApiModelProperty(value = "音频json，仅guidance", hidden = true)
    private String audioLongJson;

    @JsonIgnore
    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称", hidden = true)
    private String audioShortJson;

    @ApiModelProperty(value = "video list")
    private List<ResYogaVideoDetailVO> videos;

    @ApiModelProperty(value = "guidance object")
    private ProjYogaAutoWorkoutGuidanceVO guidance;

    // 转换为移动端的需要的结构
  /*  public ProjYogaAutoWorkoutGuidanceVO getGuidance() {
        ProjYogaAutoWorkoutGuidanceVO guidanceVO = new ProjYogaAutoWorkoutGuidanceVO();
        List<ProjYogaAutoWorkoutGuidanceItemVO> default_ = new ArrayList<>();
        List<ProjYogaAutoWorkoutGuidanceItemVO> least = new ArrayList<>();
        ProjYogaAutoWorkoutGuidanceItemVO detailLongVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailLongVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
        detailLongVO.setAudioJsonUrl(audioLongJson);

        ProjYogaAutoWorkoutGuidanceItemVO detailShortVO = new ProjYogaAutoWorkoutGuidanceItemVO();
        detailShortVO.setLanguage(GlobalConstant.DEFAULT_LANGUAGE);
        detailShortVO.setAudioJsonUrl(audioShortJson);
        default_.add(detailLongVO);
        least.add(detailShortVO);

        guidanceVO.setDefault_(default_);
        guidanceVO.setLeast(least);
        return guidanceVO;
    }*/

}
