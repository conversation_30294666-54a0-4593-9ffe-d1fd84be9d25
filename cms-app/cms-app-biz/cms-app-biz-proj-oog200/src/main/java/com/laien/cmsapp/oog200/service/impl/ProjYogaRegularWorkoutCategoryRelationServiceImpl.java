package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutCategoryRelationPub;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.cmsapp.oog200.mapper.ProjYogaRegularWorkoutCategoryRelationPubMapper;
import com.laien.cmsapp.oog200.service.IProjYogaRegularWorkoutCategoryRelationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * regular workout和category关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
public class ProjYogaRegularWorkoutCategoryRelationServiceImpl extends ServiceImpl<ProjYogaRegularWorkoutCategoryRelationPubMapper, ProjYogaRegularWorkoutCategoryRelationPub> implements IProjYogaRegularWorkoutCategoryRelationService {
    @Override
    public List<ProjYogaRegularWorkoutCategoryRelationPub> query(Set<Integer> categoryIdSet, YogaAutoWorkoutTemplateEnum typeEnum, ProjPublishCurrentVersionInfoBO versionInfo) {
        LambdaQueryWrapper<ProjYogaRegularWorkoutCategoryRelationPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(categoryIdSet), ProjYogaRegularWorkoutCategoryRelationPub::getProjYogaRegularCategoryId, categoryIdSet)
                .eq(ProjYogaRegularWorkoutCategoryRelationPub::getVersion, versionInfo.getCurrentVersion())
                .eq(ProjYogaRegularWorkoutCategoryRelationPub::getProjId, versionInfo.getProjId())
                .eq(ProjYogaRegularWorkoutCategoryRelationPub::getWorkoutType, typeEnum);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<ProjYogaRegularWorkoutCategoryRelationPub> queryByWorkoutId(Set<Integer> workoutIdSet, YogaAutoWorkoutTemplateEnum typeEnum, ProjPublishCurrentVersionInfoBO versionInfo) {
        LambdaQueryWrapper<ProjYogaRegularWorkoutCategoryRelationPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(workoutIdSet), ProjYogaRegularWorkoutCategoryRelationPub::getWorkoutId, workoutIdSet)
                .eq(ProjYogaRegularWorkoutCategoryRelationPub::getVersion, versionInfo.getCurrentVersion())
                .eq(ProjYogaRegularWorkoutCategoryRelationPub::getProjId, versionInfo.getProjId())
                .eq(ProjYogaRegularWorkoutCategoryRelationPub::getWorkoutType, typeEnum);
        return baseMapper.selectList(wrapper);
    }
}
