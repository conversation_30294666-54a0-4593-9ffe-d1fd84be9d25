package com.laien.cmsapp.oog200.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjDishStepTipPub extends BaseModel implements AppTextCoreI18nModel {

    private Integer version;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projDishId;

    @ApiModelProperty(value = "proj_dish_step表数据id")
    private Integer projDishStepId;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "介绍")
    @AppTextTranslateField
    private String intro;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
