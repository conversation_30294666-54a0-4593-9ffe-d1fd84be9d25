package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 瑜伽视频
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_wall_pilates_video_resource")
@ApiModel(value="ProjWallPilatesVideoResource对象", description="wall pilates视频资源")
public class ProjWallPilatesVideoResource extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "projWallPilatesVideoId")
    private Integer projWallPilatesVideoId;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "front  2532")
    private String frontM3u8Text2532;

    @ApiModelProperty(value = "front 2k对应的m3u8内容")
    private String frontM3u8Text2k;

    @ApiModelProperty(value = "front 1080对应的m3u8内容")
    private String frontM3u8Text1080p;

    @ApiModelProperty(value = "front 720对应的m3u8内容")
    private String frontM3u8Text720p;

    @ApiModelProperty(value = "front 480对应的m3u8内容")
    private String frontM3u8Text480p;

    @ApiModelProperty(value = "front 360对应的m3u8内容")
    private String frontM3u8Text360p;

    @ApiModelProperty(value = "side  2532")
    private String sideM3u8Text2532;

    @ApiModelProperty(value = "side 2k对应的m3u8内容")
    private String sideM3u8Text2k;

    @ApiModelProperty(value = "side 1080对应的m3u8内容")
    private String sideM3u8Text1080p;

    @ApiModelProperty(value = "side 720对应的m3u8内容")
    private String sideM3u8Text720p;

    @ApiModelProperty(value = "side 480对应的m3u8内容")
    private String sideM3u8Text480p;

    @ApiModelProperty(value = "side 360对应的m3u8内容")
    private String sideM3u8Text360p;

    @ApiModelProperty(value = "front的多分辨率m3u8")
    private String videoUrl;

}
