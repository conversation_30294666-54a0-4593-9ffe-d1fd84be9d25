package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjTemplatePub;
import com.laien.cmsapp.oog200.mapper.ProjTemplateMapper;
import com.laien.cmsapp.oog200.service.IProjTemplateService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * template 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-26
 */
@Service
public class ProjTemplateServiceImpl extends ServiceImpl<ProjTemplateMapper, ProjTemplatePub> implements IProjTemplateService {

    @Override
    public List<Integer> selectTemplateIds(Integer duration) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return this.baseMapper.selectTemplateIds(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion(), duration);
    }

    @Override
    public List<Integer> selectTemplateIdsByDuration(Integer beginDuration, Integer endDuration) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return this.baseMapper.selectTemplateIdsByDuration(versionInfoBO.getProjId(), versionInfoBO.getCurrentVersion(), beginDuration, endDuration);
    }

}
