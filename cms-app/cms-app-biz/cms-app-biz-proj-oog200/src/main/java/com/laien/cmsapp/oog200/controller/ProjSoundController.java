package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.oog200.requst.ProjSoundReq;
import com.laien.cmsapp.oog200.response.ProjSoundVO;
import com.laien.cmsapp.oog200.service.IProjSoundService;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "app端: sound 200")
@RestController
@RequestMapping("/oog200/sound200")
public class ProjSoundController extends ResponseController {

    @Resource
    private IProjSoundService soundService;

    @ApiOperation(value = " Sound200 list v1", tags = {"oog200"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjSoundVO>> list(ProjSoundReq soundReq) {
        List<ProjSoundVO> soundList = soundService.selectSoundList(soundReq);
        return succ(soundList);
    }

}