package com.laien.cmsapp.oog200.properties;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "cms-app.oog200")
@Data
@RefreshScope
@Slf4j
public class Oog200Properties {

    private List<Integer> fiveAndTenWorkoutIdList;

    private List<Integer> tenAndTwentyWorkoutIdList;

    private List<Integer> twentyPlusWorkoutIdList;

    private List<Integer> chairYogaWorkoutIdList;

    private Integer playlistId4ChairWorkout;

    @ApiModelProperty(value = "访问adapty的凭证")
    private String adaptyKey;

    @ApiModelProperty(value = "App的分享链接")
    private String appsFlyerLink;

    @ApiModelProperty(value = "apple privateKey")
    private String appleKey;

    @ApiModelProperty(value = "被邀请人订阅的产品Id列表")
    private List<String> inviteSubProductIdList;

    @ApiModelProperty(value = "apple 后台上配置的产品组Id")
    private Integer appleSubGroupId;

    @ApiModelProperty(value = "项目默认配置使用的product id")
    private String appleDefaultProductId;

    /**
     * 示例配置
     *     preTranslateConfig:
     *       Newbie:
     *         en: niubi
     *         ru: wula
     */
    private JSONObject preTranslateConfig;

}
