package com.laien.cmsapp.oog200.controller;


import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjFastingArticleDetailVO;
import com.laien.cmsapp.oog200.response.ProjFastingArticleListVO;
import com.laien.cmsapp.oog200.service.IProjFastingArticlePubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Fasting article 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Api(tags = "app端：fastingArticle")
@RestController
@RequestMapping("/oog200/fastingArticle")
public class ProjFastingArticlePubController extends ResponseController {

    @Resource
    private IProjFastingArticlePubService projFastingArticlePubService;

    @ApiOperation(value = "fastingArticle 列表 v1")
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFastingArticleListVO>> list() {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projFastingArticlePubService.list(versionInfoBO));
    }


    @ApiOperation(value = "fastingArticle 详情 v1")
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjFastingArticleDetailVO> detail(@PathVariable Integer id) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(projFastingArticlePubService.find(id, versionInfoBO));
    }

}
