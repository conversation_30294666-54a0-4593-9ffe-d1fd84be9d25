package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video subtitles v3
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video subtitles v4 item", description = "video subtitles v4 item")
public class ProjVideoGenerateI18nV4ItemVO {

    @ApiModelProperty(value = "language")
    private String language;

    @ApiModelProperty(value = "subtitles url")
    private String subtitlesUrl;

    @ApiModelProperty(value = "audio json url")
    private String audioJsonUrl;
}
