package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaMusicPub;
import com.laien.cmsapp.oog200.mapper.ProjYogaMusicPubMapper;
import com.laien.cmsapp.oog200.service.IProjYogaMusicPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/21 15:00
 */
@Service
public class ProjYogaMusicPubServiceImpl extends ServiceImpl<ProjYogaMusicPubMapper, ProjYogaMusicPub> implements IProjYogaMusicPubService {

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjYogaMusicPub> listByStatusAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> ids, Integer status, String lang) {

        LambdaQueryWrapper<ProjYogaMusicPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaMusicPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjYogaMusicPub::getProjId, versionInfoBO.getProjId());

        queryWrapper.eq(ProjYogaMusicPub::getStatus, status);
        queryWrapper.in(ProjYogaMusicPub::getId, ids);
        queryWrapper.orderByAsc(ProjYogaMusicPub::getId);

        List<ProjYogaMusicPub> musicPubList = list(queryWrapper);
        i18nUtil.translate(musicPubList, ProjCodeEnums.OOG200, lang);
        return musicPubList;
    }

}
