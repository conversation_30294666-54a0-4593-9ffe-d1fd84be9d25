package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaQuotePub;
import com.laien.cmsapp.oog200.mapper.ProjYogaQuotePubMapper;
import com.laien.cmsapp.oog200.response.ProjYogaQuoteVO;
import com.laien.cmsapp.oog200.service.IProjYogaQuotePubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * yoga名言警句 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
public class ProjYogaQuotePubServiceImpl extends ServiceImpl<ProjYogaQuotePubMapper, ProjYogaQuotePub> implements IProjYogaQuotePubService {

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjYogaQuoteVO> list(ProjPublishCurrentVersionInfoBO versionInfoBO, String language) {
        LambdaQueryWrapper<ProjYogaQuotePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaQuotePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjYogaQuotePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaQuotePub::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjYogaQuotePub> quoteList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(quoteList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(quoteList, ProjCodeEnums.OOG200, language);

        List<ProjYogaQuoteVO> yogaQuoteList = new ArrayList<>(quoteList.size());
        for (ProjYogaQuotePub quote : quoteList) {
            ProjYogaQuoteVO quoteVO = new ProjYogaQuoteVO();
            BeanUtils.copyProperties(quote, quoteVO);
            yogaQuoteList.add(quoteVO);
        }
        return yogaQuoteList;
    }
}
