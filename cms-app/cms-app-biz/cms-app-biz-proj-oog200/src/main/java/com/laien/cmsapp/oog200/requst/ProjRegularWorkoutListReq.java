package com.laien.cmsapp.oog200.requst;

import com.laien.cmsapp.requst.LangReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * note: yogaRegularWorkout id 列表查询
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "regularWorkout id 列表查询", description = "regularWorkout id 列表查询")
public class ProjRegularWorkoutListReq extends LangReq {

    @ApiModelProperty(value = "workoutReqList", required = true)
    private List<ProjRegularWorkoutReq> reqs;

    @ApiModelProperty(value = "传1 查询2532 m3u8, 传2 查询dynamic m3u8, 默认1")
    private Integer m3u8Type = 1;
}
