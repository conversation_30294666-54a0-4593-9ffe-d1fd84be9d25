package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: hhl
 * @date: 2025/6/9
 */
@Data
public class ProjYogaAwardVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "兑换截止时间")
    private LocalDateTime expiredTime;

    @ApiModelProperty(value = "持续时间，以月为单位")
    private Integer duration;

    @ApiModelProperty(value = "兑换链接")
    private String awardLink;

}
