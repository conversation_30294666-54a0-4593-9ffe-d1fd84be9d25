package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjUnitPub;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/8 11:06
*/
public interface IProjUnitPubService extends IService<ProjUnitPub> {

    List<ProjUnitPub> listByVersionAndIds(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> ids);

}
