package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video v2
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video v2", description = "video v2")
public class ProjVideoV2VO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    @ApiModelProperty(value = "预览时长")
    private Integer previewDuration;

}
