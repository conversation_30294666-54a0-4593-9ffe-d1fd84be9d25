package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjDishPub;
import com.laien.cmsapp.oog200.response.ProjDishDetailVO;
import com.laien.cmsapp.oog200.response.ProjDishListVO;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:30
 */
public interface IProjDishPubService extends IService<ProjDishPub> {

    /**
     * 获取 dish list
     *
     * @param versionInfoBO
     * @param dishIds
     * @param status
     * @param lang
     * @return
     */
    List<ProjDishListVO> listDish(ProjPublishCurrentVersionInfoBO versionInfoBO, Collection<Integer> dishIds, Integer status, String lang);

    /**
     * 获取 dish detail
     *
     * @param versionInfoBO
     * @param dishId
     * @param lang
     * @return
     */
    ProjDishDetailVO getDishDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang, Integer m3u8Type);

}
