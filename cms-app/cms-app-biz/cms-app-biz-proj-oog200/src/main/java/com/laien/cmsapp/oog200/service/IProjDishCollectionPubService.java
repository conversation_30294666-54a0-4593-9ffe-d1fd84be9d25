package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjDishCollectionPub;
import com.laien.cmsapp.oog200.response.ProjDishCollectionDetailVO;
import com.laien.cmsapp.oog200.response.ProjDishCollectionListVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/6 17:54
 */
public interface IProjDishCollectionPubService extends IService<ProjDishCollectionPub> {

    List<ProjDishCollectionListVO> listDishCollection(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang);

    ProjDishCollectionDetailVO getDishCollectionDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishPlanId, String lang);

}
