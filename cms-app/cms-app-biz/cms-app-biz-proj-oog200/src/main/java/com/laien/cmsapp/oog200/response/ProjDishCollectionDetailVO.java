package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/9 13:50
 */
@Data
public class ProjDishCollectionDetailVO {

    private Integer id;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "标签值，以英文逗号做分隔，如A,B,C 可为空")
    private String keywords;

    @ApiModelProperty(value = "描述")
    @JsonProperty(value = "desc")
    private String description;

    @ApiModelProperty(value = "dish list")
    private List<ProjDishListVO> dishList;
}
