package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * note: Playlist list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Playlist list", description = "Playlist list")
public class ProjYogaPlaylistVO {

    @ApiModelProperty(value = "yoga playlist id")
    private Integer id;

    @ApiModelProperty(value = "列表名称")
    private String playlistName;

    @ApiModelProperty(value = "歌单类型")
    private Integer playlistTypeCode;

    @ApiModelProperty(value = "手机封面图")
    @AbsoluteR2Url
    private String phoneCoverImgUrl;

    @ApiModelProperty(value = "平板封面图")
    @AbsoluteR2Url
    private String tabletCoverImgUrl;

    @ApiModelProperty(value = "手机详情图")
    @AbsoluteR2Url
    private String phoneDetailImgUrl;

    @ApiModelProperty(value = "平板详情图")
    @AbsoluteR2Url
    private String tabletDetailImgUrl;

    @ApiModelProperty(value = "是否收费")
    private Boolean subscription;

    @ApiModelProperty(value = "music list")
    private List<ProjYogaPlaylistRelationVO> musicList;

}
