package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 瑜伽视频
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ResYogaVideo对象", description="瑜伽视频")
public class ResYogaVideo extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "视频图片")
    private String imageUrl;

    @ApiModelProperty(value = "动作类型 数组 Start、Main、End、CoolDown")
    private String type;

    @ApiModelProperty(value = "动作难度 Newbie、Beginner、Intermediate、Advanced")
    private String difficulty;

    @ApiModelProperty(value = "动作体位 Steated、Standing、Prone、Supine、Arm & Leg Support")
    private String position;

    @ApiModelProperty(value = "瑜伽派别 数组 Strength、Balancing、Relaxation、Flexbility")
    private String focus;

    @ApiModelProperty(value = "特殊人群不可使用的 数组 Sensitive Wrist、Back Pain、Knee Issues、Overweight、Elderly No、No plank、Pregnancy or postpartum")
    private String specialLimit;

    @ApiModelProperty(value = "动作时长 单位毫秒")
    private Integer poseTime;

    @ApiModelProperty(value = "正位视频")
    private String frontVideoUrl;

    @ApiModelProperty(value = "正位视频时长")
    private Integer frontVideoDuration;

    @ApiModelProperty(value = "侧位视频")
    private String sideVideoUrl;

    @ApiModelProperty(value = "侧位视频时长")
    private Integer sideVideoDuration;

    @ApiModelProperty(value = "名称音频")
    private String nameAudioUrl;

    @ApiModelProperty(value = "名称音频时长")
    private Integer nameAudioDuration;

    @ApiModelProperty(value = "解说音频")
    private String guidanceAudioUrl;

    @ApiModelProperty(value = "解说音频时长")
    private Integer guidanceAudioDuration;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "批量导入的id")
    private Integer importId;


}
