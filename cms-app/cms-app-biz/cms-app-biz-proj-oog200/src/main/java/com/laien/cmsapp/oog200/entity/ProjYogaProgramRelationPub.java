package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Author:  hhl
 * Date:  2024/12/17 19:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaProgramRelation对象", description="proj yoga program relation")
public class ProjYogaProgramRelationPub extends BaseModel {

    private Integer projYogaProgramId;

    private Integer projYogaProgramLevelId;

    private Integer projId;

    @ApiModelProperty(value = "版本")
    private Integer version;
}
