package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.WorkoutCategoryRelationBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularCategoryPub;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * yoga regular category 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
public interface IProjYogaRegularCategoryService extends IService<ProjYogaRegularCategoryPub> {


    List<WorkoutCategoryRelationBO> findWorkoutCategoryRelationList(Set<Integer> categoryCodeSet, YogaAutoWorkoutTemplateEnum typeEnum, ProjPublishCurrentVersionInfoBO versionInfo);

}
