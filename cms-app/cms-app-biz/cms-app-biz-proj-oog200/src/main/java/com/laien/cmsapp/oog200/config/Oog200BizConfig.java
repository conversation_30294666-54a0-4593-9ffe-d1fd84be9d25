package com.laien.cmsapp.oog200.config;

import cn.hutool.core.map.MapUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * note: cms 移动端 业务相关配置
 *
 * <AUTHOR>
 */

@Configuration
@Data
public class Oog200BizConfig {

    @ConfigurationProperties(prefix = "cms-app.biz.workout-video200-default-fill-id-map")
    @Bean
    public Map<String, List<Integer>> getWorkoutVideo200DefaultFillIdMap() {
        return MapUtil.newHashMap();
    }
}
