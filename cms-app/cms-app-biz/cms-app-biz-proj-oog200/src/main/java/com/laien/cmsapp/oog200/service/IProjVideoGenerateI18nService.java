package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjVideoGenerateI18n;

import java.util.List;

/**
 * <p>
 * video generate 多语言 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface IProjVideoGenerateI18nService extends IService<ProjVideoGenerateI18n> {


    /**
     * 根据id批量查询，包含删除的数据
     *
     * @param ids
     * @return
     */
    List<ProjVideoGenerateI18n> listByGenerateIdsIncludeDel(List<Integer> ids);

}
