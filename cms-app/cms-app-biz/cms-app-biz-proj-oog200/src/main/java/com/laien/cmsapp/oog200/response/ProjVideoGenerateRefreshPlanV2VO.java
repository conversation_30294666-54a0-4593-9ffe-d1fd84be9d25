package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video plan 更新v1
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video plan 更新v2", description = "video plan 更新v2")
public class ProjVideoGenerateRefreshPlanV2VO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "时长")
    private Integer duration;
    @ApiModelProperty(value = "video url")
    private String videoUrl;
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "guidance object")
    private ProjVideoGenerateI18nV4VO guidance;
    @ApiModelProperty(value = "videos")
    private List<ProjVideoV3VO> videos;

}
