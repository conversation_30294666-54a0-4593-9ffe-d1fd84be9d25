package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjYogaUserAward;
import com.laien.cmsapp.oog200.mapper.ProjYogaUserAwardMapper;
import com.laien.cmsapp.oog200.response.ProjYogaUserAwardVO;
import com.laien.cmsapp.oog200.service.IProjYogaUserAwardService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/12
 */
@Service
public class ProjYogaUserAwardServiceImpl extends ServiceImpl<ProjYogaUserAwardMapper, ProjYogaUserAward> implements IProjYogaUserAwardService {

    @Resource
    private ProjYogaUserAwardMapper userAwardMapper;


    @Override
    public List<ProjYogaUserAwardVO> listByInviteCode(Collection<String> inviteCodes) {

        if (CollectionUtils.isEmpty(inviteCodes)) {
            return Collections.emptyList();
        }

        return userAwardMapper.listByInviteCodes(inviteCodes);
    }

}
