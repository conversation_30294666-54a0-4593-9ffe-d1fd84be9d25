package com.laien.cmsapp.oog200.requst;

import com.laien.cmsapp.requst.LangReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * note: yogaAutoWorkout id 列表查询
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "autoWorkout id 列表查询", description = "autoWorkout id 列表查询")
public class ProjAutoWorkoutListReq extends LangReq {

    @ApiModelProperty(value = "workoutReqList", required = true)
    private List<ProjAutoWorkoutReq> workoutReqList;

    @ApiModelProperty(value = "0: Newbie,1: Beginner,2: Intermediate,3: Advanced.difficulty 保留200原有逻辑，传什么返回什么")
    private Integer difficultyCode;

    @ApiModelProperty(value = "传1 查询2532 m3u8, 传2 查询dynamic m3u8, 默认1")
    private Integer m3u8Type = 1;
}
