package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutCategoryRelationPub;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * regular workout和category关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
public interface IProjYogaRegularWorkoutCategoryRelationService extends IService<ProjYogaRegularWorkoutCategoryRelationPub> {

    List<ProjYogaRegularWorkoutCategoryRelationPub> query(Set<Integer> categoryIdSet, YogaAutoWorkoutTemplateEnum typeEnum, ProjPublishCurrentVersionInfoBO versionInfo);

    List<ProjYogaRegularWorkoutCategoryRelationPub> queryByWorkoutId(Set<Integer> workoutIdSet, YogaAutoWorkoutTemplateEnum typeEnum, ProjPublishCurrentVersionInfoBO versionInfo);
}
