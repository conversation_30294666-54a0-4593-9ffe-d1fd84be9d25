package com.laien.cmsapp.oog200.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjDishCollectionDetailVO;
import com.laien.cmsapp.oog200.response.ProjDishCollectionListVO;
import com.laien.cmsapp.oog200.service.IProjDishCollectionPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 20:42
 */
@Api(tags = "app端：dishCollection")
@RestController
@RequestMapping("/{appCode}/dishCollection")
public class ProjDishCollectionPubController extends ResponseController {

    @Resource
    IProjDishCollectionPubService dishCollectionPubService;

    @ApiOperation(value = "获取dish collection 列表, 只获取状态为enable的数据", tags = {"oog200"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjDishCollectionListVO>> listDishCollection(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjDishCollectionListVO> dishCollections = dishCollectionPubService.listDishCollection(versionInfoBO, GlobalConstant.STATUS_ENABLE, lang);
        return succ(dishCollections);
    }

    @ApiOperation(value = "获取一个dish collection下的Dish列表", tags = {"oog200"})
    @GetMapping("/v1/detail")
    public ResponseResult<ProjDishCollectionDetailVO> listDailyDish(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang,
                                                                    @RequestParam Integer dishCollectionId) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjDishCollectionDetailVO dishListVOS = dishCollectionPubService.getDishCollectionDetail(versionInfoBO, dishCollectionId, lang);
        return succ(dishListVOS);
    }

}
