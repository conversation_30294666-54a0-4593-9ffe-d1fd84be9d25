package com.laien.cmsapp.oog200.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * oog200 workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaAutoWorkout对象", description="oog200 workout")
public class ProjYogaAutoWorkout extends BaseModel implements AppTextCoreI18nModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "video的2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "开始视频ID")
    private Integer resYogaStartVideoId;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称")
    private String audioShortJson;

    @ApiModelProperty(value = "难度")
    @AppTextTranslateField
    private String difficulty;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "生成模版id")
    private Integer projYogaAutoWorkoutTemplateId;

    @ApiModelProperty(value = "生成任务id")
    private Integer projYogaAutoWorkoutTaskId;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
