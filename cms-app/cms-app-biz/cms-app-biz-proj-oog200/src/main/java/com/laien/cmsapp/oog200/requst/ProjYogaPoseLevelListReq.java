package com.laien.cmsapp.oog200.requst;

import com.laien.common.constant.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: yogaAutoWorkout id 列表查询
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjYogaPoseLevel 列表查询", description = "ProjYogaPoseLevel 列表查询")
public class ProjYogaPoseLevelListReq {

    @ApiModelProperty(value = "0: Newbie,1: Beginner,2: Intermediate,3: Advanced", required = true)
    private Integer difficultyCode;

    @ApiModelProperty(value = "语言，默认en")
    private String lang = GlobalConstant.DEFAULT_LANGUAGE;

    @ApiModelProperty(value = "m3u8Type, 传1 查询2532 m3u8, 传2 查询dynamic m3u8, 默认1")
    private Integer m3u8Type;

}
