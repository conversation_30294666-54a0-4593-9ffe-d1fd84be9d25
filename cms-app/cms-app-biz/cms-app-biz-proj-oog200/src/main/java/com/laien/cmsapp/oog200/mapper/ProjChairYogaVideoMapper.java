package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjChairYogaVideo;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/9/23 17:32
 */
public interface ProjChairYogaVideoMapper extends BaseMapper<ProjChairYogaVideo> {

    List<ResYogaVideoDetailVO> listVideo4Res(@Param("workoutIds") Collection<Integer> workoutIds);

    List<ResYogaVideoDetailVO> listVideo4ResAndRegularWorkout(@Param("workoutIds") Collection<Integer> workoutIds);
}
