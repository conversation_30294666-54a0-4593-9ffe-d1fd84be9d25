package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.laien.cmsapp.oog200.entity.ResPoseLibrary;
import com.laien.cmsapp.oog200.mapper.ResPoseLibraryMapper;
import com.laien.cmsapp.oog200.service.IResPoseLibraryService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.RequestContextUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * pose表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Service
public class ResPoseLibraryServiceImpl extends ServiceImpl<ResPoseLibraryMapper, ResPoseLibrary> implements IResPoseLibraryService {

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ResPoseLibrary> listByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayListWithCapacity(0);
        }

        List<ResPoseLibrary> poseLibraryList = getBaseMapper().selectListByIds(Joiner.on(",").join(ids));
        if (CollectionUtils.isEmpty(poseLibraryList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(poseLibraryList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return poseLibraryList;
    }
}
