package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramCategoryPub;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramCategoryRelationPub;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramLevelRelationPub;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramRelationPub;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.cmsapp.oog200.mapper.ProjYogaProgramCategoryPubMapper;
import com.laien.cmsapp.oog200.mapper.ProjYogaProgramCategoryRelationPubMapper;
import com.laien.cmsapp.oog200.mapper.ProjYogaProgramLevelRelationPubMapper;
import com.laien.cmsapp.oog200.mapper.ProjYogaProgramRelationPubMapper;
import com.laien.cmsapp.oog200.response.ProjYogaProgramCategoryListVO;
import com.laien.cmsapp.oog200.response.ProjYogaProgramLevelListVO;
import com.laien.cmsapp.oog200.response.ProjYogaProgramListVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;
import com.laien.cmsapp.oog200.service.IProjChairYogaRegularWorkoutPubService;
import com.laien.cmsapp.oog200.service.IProjWallPilatesRegularWorkoutPubService;
import com.laien.cmsapp.oog200.service.IProjYogaProgramCategoryPubService;
import com.laien.cmsapp.oog200.service.IProjYogaProgramLevelPubService;
import com.laien.cmsapp.oog200.service.IProjYogaProgramPubService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularWorkoutPubService;
import com.laien.common.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/12/24 14:55
 */
@Slf4j
@Service
public class ProjYogaProgramCategoryPubServiceImpl extends ServiceImpl<ProjYogaProgramCategoryPubMapper, ProjYogaProgramCategoryPub> implements IProjYogaProgramCategoryPubService {

    @Resource
    ProjYogaProgramCategoryRelationPubMapper programCategoryRelationPubMapper;

    @Resource
    ProjYogaProgramRelationPubMapper programRelationPubMapper;

    @Resource
    ProjYogaProgramLevelRelationPubMapper programLevelRelationPubMapper;

    @Resource
    IProjYogaProgramPubService programPubService;

    @Resource
    IProjYogaProgramLevelPubService programLevelPubService;

    @Resource
    IProjYogaRegularWorkoutPubService regularWorkoutPubService;

    @Resource
    IProjChairYogaRegularWorkoutPubService chairYogaRegularWorkoutPubService;

    @Resource
    IProjWallPilatesRegularWorkoutPubService wallPilatesRegularWorkoutPubService;


    @Override
    public List<ProjYogaProgramCategoryListVO> listProgramCategory(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, Integer m3u8Type, String lang) {

        List<ProjYogaProgramCategoryPub> categoryPubList = listByStatusAndVersion(status, versionInfoBO);
        if (CollectionUtils.isEmpty(categoryPubList)) {
            return Lists.newArrayList();
        }

        // program category
        List<ProjYogaProgramCategoryListVO> categoryListVOList = convertEntity2ListVO(categoryPubList);
        List<Integer> categoryIdList = categoryPubList.stream().map(ProjYogaProgramCategoryPub::getId).collect(Collectors.toList());
        List<ProjYogaProgramCategoryRelationPub> categoryRelationPubList = programCategoryRelationPubMapper.listByCategoryAndVersion(categoryIdList, versionInfoBO.getCurrentVersion());
        if (CollectionUtils.isEmpty(categoryRelationPubList)) {
            return categoryListVOList;
        }

        // program
        List<Integer> programIdList = categoryRelationPubList.stream().map(ProjYogaProgramCategoryRelationPub::getProjYogaProgramId).collect(Collectors.toList());
        List<ProjYogaProgramListVO> programListVOList = programPubService.listVOByIds(programIdList, versionInfoBO.getCurrentVersion(), status, lang);
        if (CollectionUtils.isEmpty(programListVOList)) {
            return categoryListVOList;
        }

        setProgram4Category(categoryListVOList, categoryRelationPubList, programListVOList);
        programIdList = programListVOList.stream().map(ProjYogaProgramListVO::getId).collect(Collectors.toList());
        List<ProjYogaProgramRelationPub> programRelationPubs = programRelationPubMapper.listByProgramIdAndVersion(programIdList, versionInfoBO.getCurrentVersion());
        if (CollectionUtils.isEmpty(programRelationPubs)) {
            return categoryListVOList;
        }

        // program level
        List<Integer> programLevelIdList = programRelationPubs.stream().map(ProjYogaProgramRelationPub::getProjYogaProgramLevelId).collect(Collectors.toList());
        List<ProjYogaProgramLevelListVO> programLevelListVOList = programLevelPubService.listVOByIds(programLevelIdList, versionInfoBO.getCurrentVersion(), status, lang);
        if (CollectionUtils.isEmpty(programLevelListVOList)) {
            return categoryListVOList;
        }

        setLevel4Program(programListVOList, programRelationPubs, programLevelListVOList);
        programLevelIdList = programLevelListVOList.stream().map(ProjYogaProgramLevelListVO::getId).collect(Collectors.toList());
        List<ProjYogaProgramLevelRelationPub> programLevelRelationPubs = programLevelRelationPubMapper.listByProgramLevelIdAndStatus(programLevelIdList, versionInfoBO.getCurrentVersion());
        if (CollectionUtils.isEmpty(programLevelRelationPubs)) {
            return categoryListVOList;
        }

        // program workout
        List<ProjYogaRegularWorkoutListVO> regularWorkoutListVOList = listRegularWorkoutVO(programLevelRelationPubs, lang, status, m3u8Type, versionInfoBO);
        if (CollectionUtils.isEmpty(regularWorkoutListVOList)) {
            return categoryListVOList;
        }

        setWorkout4ProgramLevel(programLevelListVOList, programLevelRelationPubs, regularWorkoutListVOList);
        return categoryListVOList;
    }

    private void setProgram4Category(List<ProjYogaProgramCategoryListVO> categoryListVOList, List<ProjYogaProgramCategoryRelationPub> relationPubList,
                                     List<ProjYogaProgramListVO> programListVOList) {

        Map<Integer, List<Integer>> categoryAndProgramMap = relationPubList.stream().collect(Collectors.groupingBy(ProjYogaProgramCategoryRelationPub::getProjYogaProgramCategoryId, Collectors.mapping(ProjYogaProgramCategoryRelationPub::getProjYogaProgramId, Collectors.toList())));
        Map<Integer, ProjYogaProgramListVO> programMap = programListVOList.stream().collect(Collectors.toMap(ProjYogaProgramListVO::getId, Function.identity(), (k1, k2) -> k2));
        categoryListVOList.forEach(listVO -> {
            if (!categoryAndProgramMap.containsKey(listVO.getId())) {
                return;
            }

            List<ProjYogaProgramListVO> programListVOS = categoryAndProgramMap.get(listVO.getId()).stream().filter(programMap::containsKey).map(programMap::get).collect(Collectors.toList());
            listVO.setProgramList(programListVOS);
        });
    }

    private void setLevel4Program(List<ProjYogaProgramListVO> programListVOList, List<ProjYogaProgramRelationPub> programRelationPubs,
                                  List<ProjYogaProgramLevelListVO> programLevelListVOList) {
        Map<Integer, List<Integer>> programAndLevelMap = programRelationPubs.stream().collect(Collectors.groupingBy(ProjYogaProgramRelationPub::getProjYogaProgramId, Collectors.mapping(ProjYogaProgramRelationPub::getProjYogaProgramLevelId, Collectors.toList())));
        Map<Integer, ProjYogaProgramLevelListVO> programLevelMap = programLevelListVOList.stream().collect(Collectors.toMap(ProjYogaProgramLevelListVO::getId, Function.identity(), (k1, k2) -> k2));
        programListVOList.forEach(listVO -> {
            if (!programAndLevelMap.containsKey(listVO.getId())) {
                return;
            }

            List<ProjYogaProgramLevelListVO> programListVOS = programAndLevelMap.get(listVO.getId()).stream().filter(programLevelMap::containsKey).map(programLevelMap::get).collect(Collectors.toList());
            listVO.setProgramLevelList(programListVOS);
        });
    }

    private void setWorkout4ProgramLevel(List<ProjYogaProgramLevelListVO> programLevelListVOList, List<ProjYogaProgramLevelRelationPub> programLevelRelationPubs,
                                         List<ProjYogaRegularWorkoutListVO> regularWorkoutListVOList) {

        Map<Integer, List<ProjYogaProgramLevelRelationPub>> programLevelMap = programLevelRelationPubs.stream().collect(Collectors.groupingBy(ProjYogaProgramLevelRelationPub::getProjYogaProgramLevelId));
        Table<Integer, Integer, ProjYogaRegularWorkoutListVO> typeAndWorkoutMap = HashBasedTable.create();
        regularWorkoutListVOList.forEach(listVO -> {
            typeAndWorkoutMap.put(listVO.getWorkoutTypeCode(), listVO.getId(), listVO);
        });

        programLevelListVOList.forEach(listVO -> {
            if (!programLevelMap.containsKey(listVO.getId())) {
                return;
            }

            List<ProjYogaRegularWorkoutListVO> workoutListVOList = programLevelMap.get(listVO.getId()).stream().filter(relation -> typeAndWorkoutMap.contains(relation.getVideoType().getCode(), relation.getProjYogaRegularWorkoutId())).map(relation -> typeAndWorkoutMap.get(relation.getVideoType().getCode(), relation.getProjYogaRegularWorkoutId())).collect(Collectors.toList());
            listVO.setProgramWorkoutList(workoutListVOList);
        });
    }

    private List<ProjYogaProgramCategoryPub> listByStatusAndVersion(Integer status, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        LambdaQueryWrapper<ProjYogaProgramCategoryPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(status), ProjYogaProgramCategoryPub::getStatus, status);
        queryWrapper.eq(ProjYogaProgramCategoryPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjYogaProgramCategoryPub::getVersion, versionInfoBO.getCurrentVersion());
        return list(queryWrapper);
    }

    private List<ProjYogaProgramCategoryListVO> convertEntity2ListVO(List<ProjYogaProgramCategoryPub> categoryPubList) {

        List<ProjYogaProgramCategoryListVO> categoryListVOS = categoryPubList.stream().map(categoryPub -> {
            ProjYogaProgramCategoryListVO listVO = new ProjYogaProgramCategoryListVO();
            BeanUtils.copyProperties(categoryPub, listVO);
            listVO.setCategoryTypeCode(parseCategoryCode(categoryPub));
            return listVO;
        } ).collect(Collectors.toList());

        return categoryListVOS;
    }

    private Integer parseCategoryCode(ProjYogaProgramCategoryPub categoryPub) {

        if (StringUtils.isEmpty(categoryPub.getProgramType())) {
            return null;
        }

        String[] programTypes = StringUtils.split(categoryPub.getProgramType(), GlobalConstant.COMMA);
        if (Objects.isNull(programTypes)) {
            return Integer.parseInt(categoryPub.getProgramType());
        }

        Integer programTypeCode = Integer.parseInt(programTypes[0]);
        return programTypeCode;
    }

    private List<ProjYogaRegularWorkoutListVO> listRegularWorkoutVO(List<ProjYogaProgramLevelRelationPub> programLevelRelationPubs, String lang, Integer status, Integer m3u8Type, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        Map<YogaAutoWorkoutTemplateEnum, Set<Integer>> workoutTypeAndIdsMap = programLevelRelationPubs.stream().collect(Collectors.groupingBy(ProjYogaProgramLevelRelationPub::getVideoType, Collectors.mapping(ProjYogaProgramLevelRelationPub::getProjYogaRegularWorkoutId, Collectors.toSet())));
        List<ProjYogaRegularWorkoutDetailVO> workoutDetailVOList = Lists.newArrayList();

        workoutTypeAndIdsMap.entrySet().forEach(entry -> {
            Set<Integer> workoutIds = entry.getValue();
            if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA)) {
                List<ProjYogaRegularWorkoutDetailVO> classicWorkouts = regularWorkoutPubService.listByIdsAndStatus(workoutIds, lang, status, m3u8Type);
                if (!CollectionUtils.isEmpty(classicWorkouts)) {
                    classicWorkouts.forEach(workoutDetailVO -> workoutDetailVO.setWorkoutTypeCode(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode()));
                    workoutDetailVOList.addAll(classicWorkouts);
                }
            }
            if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.WALL_PILATES)) {
                List<ProjYogaRegularWorkoutDetailVO> wallPilatesWorkouts = wallPilatesRegularWorkoutPubService.list(workoutIds, lang, m3u8Type, status, versionInfoBO);
                if (!CollectionUtils.isEmpty(wallPilatesWorkouts)) {
                    workoutDetailVOList.addAll(wallPilatesWorkouts);
                }
            }
            if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA)) {
                List<ProjYogaRegularWorkoutDetailVO> chairWorkouts = chairYogaRegularWorkoutPubService.listByIdsAndStatus(workoutIds, lang, status, m3u8Type, versionInfoBO);
                if (!CollectionUtils.isEmpty(chairWorkouts)) {
                    workoutDetailVOList.addAll(chairWorkouts);
                }
            }
        });

        return workoutDetailVOList.stream().map(detailVO -> {
            ProjYogaRegularWorkoutListVO listVO = new ProjYogaRegularWorkoutListVO();
            BeanUtils.copyProperties(detailVO, listVO);

            if (!CollectionUtils.isEmpty(detailVO.getVideos())) {
                listVO.setVideoCount(detailVO.getVideos().size());
            }
            return listVO;
        }).collect(Collectors.toList());
    }

}
