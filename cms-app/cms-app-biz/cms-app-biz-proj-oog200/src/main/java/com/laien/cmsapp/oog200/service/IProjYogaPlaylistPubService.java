package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPlaylistPub;
import com.laien.cmsapp.oog200.response.ProjYogaPlaylistVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/2/21 11:54
 */
public interface IProjYogaPlaylistPubService extends IService<ProjYogaPlaylistPub> {

    /**
     * 本地设置
     *
     * @param playlistCodes
     * @return
     */
    List<ProjYogaPlaylistVO> listByPlaylistCodes(ProjPublishCurrentVersionInfoBO versionInfoBO, List<Integer> playlistCodes, Integer status, String lang);

}
