package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjCollectionClassPub;
import com.laien.cmsapp.response.ProjCollectionClassDetailVO;
import com.laien.cmsapp.response.ResVideoClassVO;
import com.laien.common.response.IdAndCountsAndMaxMinRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * proj collection class Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public interface ProjCollectionClassMapper extends BaseMapper<ProjCollectionClassPub> {

    /**
     * 查询collection class列表
     *
     * @param projId projId
     * @param version version
     * @return list
     */
    List<ProjCollectionClassPub> selectCollectionClassList(@Param("projId") Integer projId, @Param("version") Integer version);

    /**
     * 查询collection class 的 video class 数量
     *
     * @param projId projId
     * @param version version
     * @return list
     */
    List<IdAndCountsAndMaxMinRes> selectCollectionClassCounts(@Param("projId") Integer projId, @Param("version") Integer version);

    /**
     * 查询collection class详情
     *
     * @param id id
     * @param versionInfoBO versionInfoBO
     * @return ProjCollectionClassDetailVO
     */
    ProjCollectionClassDetailVO selectCollectionClassDetail(Integer id, @Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO);

    /**
     * 查询collection class 的video class列表
     *
     * @param id id
     * @param versionInfoBO versionInfoBO
     * @return list
     */
    List<ResVideoClassVO> selectCollectionClassVideoClasses(Integer id, @Param("info") ProjPublishCurrentVersionInfoBO versionInfoBO);

}
