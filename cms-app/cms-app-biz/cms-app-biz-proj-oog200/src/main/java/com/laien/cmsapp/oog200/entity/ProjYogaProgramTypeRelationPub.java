package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * regular workout和category关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_program_type_relation")
@ApiModel(value="ProjYogaProgramTypeRelation", description="yoga program和program type关系表")
public class ProjYogaProgramTypeRelationPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "proj_yoga_program表id")
    private Integer projYogaProgramId;

    @ApiModelProperty(value = "proj_yoga_program_type表id")
    private Integer projYogaProgramTypeId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;


}
