package com.laien.cmsapp.oog200.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaPoseGroupPub;
import com.laien.cmsapp.oog200.response.ProjYogaPoseGroupDetailVO;

import java.util.List;

/**
 * <p>
 * proj yoga pose grouping 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
public interface IProjYogaPoseGroupService extends IService<ProjYogaPoseGroupPub> {

    List<ProjYogaPoseGroupDetailVO> findByYogaPoseLevelId(Integer yogaPoseLevelId, ProjPublishCurrentVersionInfoBO versionInfoBO, Integer m3u8Type, String lang);

    List<ProjYogaPoseGroupDetailVO> list(Integer m3u8Type, ProjPublishCurrentVersionInfoBO versionInfoBO, String lang);

}
