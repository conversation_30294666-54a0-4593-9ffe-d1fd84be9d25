package com.laien.cmsapp.oog200.controller;

import com.laien.cmsapp.oog200.service.YogaWebhookService;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.laien.common.response.setting.ResponseResult.succ;

/**
 * @author: hhl
 * @date: 2025/6/5
 */
@Slf4j
@Api(tags = "app端：callback 200")
@RestController
@RequestMapping("/oog200/adapty")
public class AdaptyWebhookController {

    @Resource
    private YogaWebhookService yogaWebhookService;

    @ApiOperation(value = "接收Adapty事件回调")
    @PostMapping("/v1/webhook")
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> result(@RequestBody String body) {

        log.warn("[200 adapty callback] Received callback msg: " + body);
        yogaWebhookService.adaptyCallback(body);
        return succ();
    }

}
