package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video v3
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video v4", description = "video v4")
public class ProjVideoGenerateV4VO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "video url")
    private String videoUrl;
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;
    @ApiModelProperty(value = "guidance Object")
    private ProjVideoGenerateI18nV4VO guidance;
    @ApiModelProperty(value = "videos")
    private List<ProjVideoV3VO> videos;

    @ApiModelProperty(value = "时长")
    private Integer duration;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

}
