package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: Playlist music list
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Playlist music list", description = "Playlist music list")
public class ProjYogaPlaylistRelationVO {

    @JsonProperty("resId")
    @ApiModelProperty(value = "yoga music id，用于分享时的资源定位，因为创建分享链接时")
    private Integer musicId;

    @ApiModelProperty(value = "yoga playlist relation id")
    @JsonIgnore
    private Integer relationId;

    @ApiModelProperty(value = "是否收费")
    private Boolean subscription;

    @ApiModelProperty(value = "音乐名称")
    private String musicName;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "音频")
    @AbsoluteR2Url
    private String audio;

    @ApiModelProperty(value = "音频文件名称")
    private String audioName;

    @ApiModelProperty(value = "音频总时长, 毫秒")
    private Integer audioDuration;

    @ApiModelProperty(value = "讲述者，用于Meditation类型")
    private String instructor;

    @ApiModelProperty(value = "音乐类型")
    private String musicType;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "app短连接")
    private String shortLink;

}
