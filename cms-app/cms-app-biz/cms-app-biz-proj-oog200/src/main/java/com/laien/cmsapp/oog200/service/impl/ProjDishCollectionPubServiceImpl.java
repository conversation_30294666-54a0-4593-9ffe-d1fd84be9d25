package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjDishCollectionPub;
import com.laien.cmsapp.oog200.entity.ProjDishCollectionRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjDishCollectionPubMapper;
import com.laien.cmsapp.oog200.mapper.ProjDishCollectionRelationPubMapper;
import com.laien.cmsapp.oog200.response.ProjDishCollectionDetailVO;
import com.laien.cmsapp.oog200.response.ProjDishCollectionListVO;
import com.laien.cmsapp.oog200.response.ProjDishListVO;
import com.laien.cmsapp.oog200.service.IProjDishCollectionPubService;
import com.laien.cmsapp.oog200.service.IProjDishPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/6 18:09
 */
@Service
public class ProjDishCollectionPubServiceImpl extends ServiceImpl<ProjDishCollectionPubMapper, ProjDishCollectionPub> implements IProjDishCollectionPubService {

    @Resource
    private IProjDishPubService dishPubService;

    @Resource
    private ProjDishCollectionRelationPubMapper collectionRelationPubMapper;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjDishCollectionListVO> listDishCollection(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, String lang) {

        List<ProjDishCollectionPub> dishCollectionPubList = listByStatusAndVersion(versionInfoBO, status);
        if (CollectionUtils.isEmpty(dishCollectionPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(dishCollectionPubList, ProjCodeEnums.OOG200, lang);
        List<ProjDishCollectionListVO> dishCollectionListVOS = dishCollectionPubList.stream().map(mealPlan -> convert2ListVO(mealPlan)).collect(Collectors.toList());
        return dishCollectionListVOS;
    }

    private ProjDishCollectionListVO convert2ListVO(ProjDishCollectionPub dishCollectionPub) {

        ProjDishCollectionListVO projDishCollectionListVO = new ProjDishCollectionListVO();
        BeanUtils.copyProperties(dishCollectionPub, projDishCollectionListVO);
        return projDishCollectionListVO;
    }

    private List<ProjDishCollectionPub> listByStatusAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status) {

        LambdaQueryWrapper<ProjDishCollectionPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(status), ProjDishCollectionPub::getStatus, status);
        queryWrapper.eq(ProjDishCollectionPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjDishCollectionPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.orderByAsc(ProjDishCollectionPub::getSorted);
        return list(queryWrapper);
    }

    private ProjDishCollectionPub getByIdAndVersion(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishCollectionId) {

        LambdaQueryWrapper<ProjDishCollectionPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjDishCollectionPub::getId, dishCollectionId);
        queryWrapper.eq(ProjDishCollectionPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjDishCollectionPub::getProjId, versionInfoBO.getProjId());
        return getOne(queryWrapper);
    }

    @Override
    public ProjDishCollectionDetailVO getDishCollectionDetail(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishPlanId, String lang) {

        ProjDishCollectionPub dishCollectionPub = getByIdAndVersion(versionInfoBO, dishPlanId);
        if (Objects.isNull(dishCollectionPub)) {
            return null;
        }

        i18nUtil.translate(Lists.newArrayList(dishCollectionPub), ProjCodeEnums.OOG200, lang);
        ProjDishCollectionDetailVO detailVO = new ProjDishCollectionDetailVO();
        BeanUtils.copyProperties(dishCollectionPub, detailVO);

        List<ProjDishCollectionRelationPub> relationPubList = collectionRelationPubMapper.listByVersionAndDishCollectionId(versionInfoBO.getCurrentVersion(), dishPlanId);
        if (CollectionUtils.isEmpty(relationPubList)) {
            return detailVO;
        }

        Set<Integer> dishIds = relationPubList.stream().map(ProjDishCollectionRelationPub::getProjDishId).collect(Collectors.toSet());
        List<ProjDishListVO> dishListVOList = dishPubService.listDish(versionInfoBO, dishIds, null, lang);
        if (CollectionUtils.isEmpty(dishListVOList)) {
            return detailVO;
        }

        Map<Integer, ProjDishListVO> dishIdMap = dishListVOList.stream().collect(Collectors.toMap(ProjDishListVO::getId, Function.identity()));
        List<ProjDishListVO> dishList = relationPubList.stream().filter(relation -> dishIdMap.containsKey(relation.getProjDishId())).map(relation -> setType4Dish(dishIdMap.get(relation.getProjDishId()), relation)).collect(Collectors.toList());
        detailVO.setDishList(dishList);
        return detailVO;
    }

    private ProjDishListVO setType4Dish(ProjDishListVO dishListVO, ProjDishCollectionRelationPub relationPub) {

        if (Objects.nonNull(relationPub.getDishType())) {
            dishListVO.setDishTypeCode(relationPub.getDishType().getCode());
        }
        return dishListVO;
    }

}
