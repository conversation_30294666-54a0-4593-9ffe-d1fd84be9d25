package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjAllergenRelationPub;
import com.laien.common.oog200.enums.AllergenRelationBusinessEnum;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/8 17:28
 */
public interface IProjAllergenRelationPubService extends IService<ProjAllergenRelationPub> {

    List<ProjAllergenRelationPub> listByVersionAndDataId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dataId, AllergenRelationBusinessEnum businessEnum);

}
