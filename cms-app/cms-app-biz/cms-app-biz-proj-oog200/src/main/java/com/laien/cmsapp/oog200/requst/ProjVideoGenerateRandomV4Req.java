package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video random
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video random v4", description = "video random v4")
public class ProjVideoGenerateRandomV4Req {

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "duration", required = true)
    private Integer duration;

    @ApiModelProperty(value = "Default | Least ，不填则查全部")
    private String introType;

}
