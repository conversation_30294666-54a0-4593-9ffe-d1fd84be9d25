package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 生成的workout的audio json的内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_regular_workout_audio_i18n_pub")
@ApiModel(value="ProjYogaRegularWorkoutAudioI18n对象", description="生成的workout的audio json的内容")
public class ProjYogaRegularWorkoutAudioI18nPub extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "语言")
    private String language;

    private Integer workoutId;

    private Integer projId;

    @ApiModelProperty(value = "workout类型,0:Classic Yoga,1:Wall Pilates,2:Chair Yoga,3:Face Yoga,4:Lazy Yoga,5:Somatic Yoga")
    private YogaAutoWorkoutTemplateEnum workoutType;

    private String audioLongJsonUrl;

    private String audioShortJsonUrl;

}
