package com.laien.cmsapp.oog200.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.cmsapp.oog200.requst.CollectionClassListReq;
import com.laien.cmsapp.response.ProjCollectionClassDetailVO;
import com.laien.cmsapp.response.ProjCollectionClassListVO;
import com.laien.cmsapp.oog200.service.IProjCollectionClassService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * note: Collection Class
 *
 * <AUTHOR>
 */
@Api(value = "/{appCode}/collectionClass", tags = {"app端：Collection Class"})
@RestController
@RequestMapping("/{appCode}/collectionClass")
public class ProjCollectionClassController extends ResponseController {

    @Resource
    private IProjCollectionClassService projCollectionClassService;


    @ApiOperation(value = "Collection Class 列表 v1", tags = {"oog200"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjCollectionClassListVO>> list(CollectionClassListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjCollectionClassListVO> collectionClassList = projCollectionClassService.selectCollectionClassList(versionInfoBO, req, Collections.singletonList(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA));
        return succ(collectionClassList);
    }

    @ApiOperation(value = "Collection Class 列表 v2", tags = {"oog200"})
    @GetMapping("/v2/list")
    public ResponseResult<List<ProjCollectionClassListVO>> listV2(CollectionClassListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjCollectionClassListVO> collectionClassList = projCollectionClassService.selectCollectionClassList(versionInfoBO, req, null);
        return succ(collectionClassList);
    }

    @ApiOperation(value = "Collection Class 详情 v1", tags = {"oog200"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjCollectionClassDetailVO> detail(@PathVariable Integer id) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        ProjCollectionClassDetailVO detailVO = projCollectionClassService.selectCollectionClassDetail(id, versionInfoBO);
        return succ(detailVO);
    }

}
