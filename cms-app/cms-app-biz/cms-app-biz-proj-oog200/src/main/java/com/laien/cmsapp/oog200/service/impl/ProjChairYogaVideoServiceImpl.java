package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjChairYogaVideo;
import com.laien.cmsapp.oog200.mapper.ProjChairYogaVideoMapper;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IProjChairYogaVideoService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.util.RequestContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/10/14 14:59
 */
@Slf4j
@Service
public class ProjChairYogaVideoServiceImpl extends ServiceImpl<ProjChairYogaVideoMapper, ProjChairYogaVideo> implements IProjChairYogaVideoService {

    @Resource
    ProjChairYogaVideoMapper chairYogaVideoMapper;

    @Resource
    I18nUtil i18nUtil;

    @Override
    public List<ResYogaVideoDetailVO> listVideo4Res(Collection<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return Collections.emptyList();
        }

        List<ResYogaVideoDetailVO> resVideos = chairYogaVideoMapper.listVideo4Res(workoutIds);
        i18nUtil.translate4Speech(resVideos, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return resVideos;
    }

    @Override
    public List<ResYogaVideoDetailVO> listVideo4ResAndRegularWorkout(Collection<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return Collections.emptyList();
        }

        List<ResYogaVideoDetailVO> resVideos = chairYogaVideoMapper.listVideo4ResAndRegularWorkout(workoutIds);
        i18nUtil.translate4Speech(resVideos, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return resVideos;
    }

}
