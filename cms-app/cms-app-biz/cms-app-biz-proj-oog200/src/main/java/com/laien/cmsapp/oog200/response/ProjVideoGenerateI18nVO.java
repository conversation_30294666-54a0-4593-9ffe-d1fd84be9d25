package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video subtitles
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video subtitles", description = "video subtitles")
public class ProjVideoGenerateI18nVO {

    @ApiModelProperty(value = "language")
    private String language;

    @ApiModelProperty(value = "subtitles url")
    private String subtitlesUrl;
}
