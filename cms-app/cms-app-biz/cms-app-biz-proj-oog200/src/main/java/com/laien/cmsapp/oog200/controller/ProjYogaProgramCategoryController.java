package com.laien.cmsapp.oog200.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.response.ProjYogaProgramCategoryListVO;
import com.laien.cmsapp.oog200.service.IProjYogaProgramCategoryPubService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 09:58
 */
@Api(tags = "app端：yogaProgramCategory")
@RestController
@RequestMapping("/{appCode}/yogaProgramCategory")
public class ProjYogaProgramCategoryController extends ResponseController {

    @Resource
    IProjYogaProgramCategoryPubService programCategoryPubService;

    @ApiOperation(value = "yoga program category 列表, 只获取状态为enable的数据", tags = {"oog200"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjYogaProgramCategoryListVO>> listProgramCategory(@RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang,
                                                                                   @RequestParam(required = false, defaultValue = "1") Integer m3u8Type) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjYogaProgramCategoryListVO> categoryListVOList = programCategoryPubService.listProgramCategory(versionInfoBO, GlobalConstant.STATUS_ENABLE, m3u8Type, lang);
        return succ(categoryListVOList);
    }

}
