package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.entity.ResVideoClass;
import com.laien.cmsapp.response.ResVideoClassVO;

import java.util.List;

/**
 * <p>
 * videoClass 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
public interface IResVideoClassService extends IService<ResVideoClass> {

    List<ResVideoClassVO> selectEnableByIds(Integer projId,Integer version,List<Integer> ids);

    List<ResVideoClassVO> listAll(String lang);

}
