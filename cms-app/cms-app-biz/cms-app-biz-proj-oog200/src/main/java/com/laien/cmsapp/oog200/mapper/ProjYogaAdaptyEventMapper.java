package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjYogaAdaptyEvent;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
public interface ProjYogaAdaptyEventMapper extends BaseMapper<ProjYogaAdaptyEvent> {

    @Select(value = "select id, user_id, invite_code, product_id, event_type from proj_yoga_adapty_event where handle_flag = 0 and del_flag = 0 limit 100")
    List<ProjYogaAdaptyEvent> listUnHandleEvent();
}
