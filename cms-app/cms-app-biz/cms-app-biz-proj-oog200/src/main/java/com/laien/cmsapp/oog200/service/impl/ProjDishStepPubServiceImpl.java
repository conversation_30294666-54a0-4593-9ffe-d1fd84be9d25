package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjDishStepPub;
import com.laien.cmsapp.oog200.mapper.ProjDishStepPubMapper;
import com.laien.cmsapp.oog200.response.ProjDishStepTipVO;
import com.laien.cmsapp.oog200.response.ProjDishStepVO;
import com.laien.cmsapp.oog200.service.IProjDishStepPubService;
import com.laien.cmsapp.oog200.service.IProjDishStepTipPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:23
 */
@Service
public class ProjDishStepPubServiceImpl extends ServiceImpl<ProjDishStepPubMapper, ProjDishStepPub> implements IProjDishStepPubService {

    @Resource
    private IProjDishStepTipPubService dishStepTipPubService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjDishStepVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang) {

        LambdaQueryWrapper<ProjDishStepPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjDishStepPub::getProjDishId, dishId);
        queryWrapper.eq(ProjDishStepPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjDishStepPub::getProjId, versionInfoBO.getProjId());

        List<ProjDishStepPub> dishStepPubList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(dishStepPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(dishStepPubList, ProjCodeEnums.OOG200, lang);
        List<ProjDishStepVO> dishStepVOList = dishStepPubList.stream().map(stepPub -> convert2VO(stepPub)).collect(Collectors.toList());
        List<ProjDishStepTipVO> dishStepTipVOList = dishStepTipPubService.listByDishId(versionInfoBO, dishId, lang);
        if (CollectionUtils.isEmpty(dishStepTipVOList)) {
            return dishStepVOList;
        }

        Map<Integer, List<ProjDishStepTipVO>> stepIdAndTipMap = dishStepTipVOList.stream().collect(Collectors.groupingBy(ProjDishStepTipVO::getProjDishStepId));
        dishStepVOList.forEach(stepVO -> stepVO.setTipList(stepIdAndTipMap.get(stepVO.getId())));
        return dishStepVOList;
    }

    private ProjDishStepVO convert2VO(ProjDishStepPub stepPub) {

        ProjDishStepVO stepVO = new ProjDishStepVO();
        BeanUtils.copyProperties(stepPub, stepVO);
        return stepVO;
    }

}
