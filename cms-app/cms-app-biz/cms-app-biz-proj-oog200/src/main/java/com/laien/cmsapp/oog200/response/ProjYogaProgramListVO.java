package com.laien.cmsapp.oog200.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 14:16
 */
@Data
public class ProjYogaProgramListVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "视频图片")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "视频图片")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "难度, 0: Newbie,1: <PERSON><PERSON><PERSON>,2: Intermediate,3: Advanced")
    private Integer difficultyCode;

    @ApiModelProperty(value = "program的类型，可多选，可不选")
    private Collection<Integer> programTypeCode = Collections.emptyList();

    @ApiModelProperty(value = "持续周数")
    private Integer duration;

    @ApiModelProperty(value = "playlist id")
    private Integer playlistId;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "new开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newStartTime;

    @ApiModelProperty(value = "new结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime newEndTime;

    @ApiModelProperty(value = "描述")
    private String description;

    private List<ProjYogaProgramLevelListVO> programLevelList;

}
