package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramTypeRelationPub;
import com.laien.cmsapp.oog200.response.ProjYogaProgramTypeRelationListVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:43
 */
public interface ProjYogaProgramTypeRelationPubMapper extends BaseMapper<ProjYogaProgramTypeRelationPub> {

    @Select("<script>" +
            "SELECT relation.proj_yoga_program_id, relation.proj_yoga_program_type_id, relation.proj_id, type.code as projYogaProgramTypeCode FROM proj_yoga_program_type_relation_pub relation " +
            "INNER JOIN proj_yoga_program_type_pub type on type.id = relation.proj_yoga_program_type_id " +
            "WHERE relation.del_flag = 0 and relation.version = #{version} and relation.proj_yoga_program_id IN " +
            "<foreach collection='programIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<ProjYogaProgramTypeRelationListVO> listByProgramIdAndVersion(@Param("programIds") Collection<Integer> programIds, Integer version);

}
