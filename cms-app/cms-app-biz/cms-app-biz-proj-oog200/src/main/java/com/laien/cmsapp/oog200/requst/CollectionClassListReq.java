package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/6
 */
@Data
@ApiModel(value = "CollectionClassListReq", description = "CollectionClassListReq")
public class CollectionClassListReq {

    @ApiModelProperty(name = "goalCodeList", value = "0:goal basic,1:weight-loss,2:flexibility,3:mindfulness")
    private List<Integer> goalCodeList;
}

