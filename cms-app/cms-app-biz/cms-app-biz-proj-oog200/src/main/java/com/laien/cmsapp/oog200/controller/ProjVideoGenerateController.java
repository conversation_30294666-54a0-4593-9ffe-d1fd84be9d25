package com.laien.cmsapp.oog200.controller;

import com.laien.cmsapp.oog200.requst.*;
import com.laien.cmsapp.oog200.response.*;
import com.laien.cmsapp.oog200.service.IProjVideoGenerateService;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * note: video
 *
 * <AUTHOR>
 */
@Api(tags = "app端：video")
@RestController
@RequestMapping("/{appCode}")
public class ProjVideoGenerateController extends ResponseController {

    @Resource
    private IProjVideoGenerateService projVideoGenerateService;

    @ApiOperation(value = "随机获取一个视频v1", tags = {"oog200"})
    @GetMapping("/v1/video/randomOne")
    public ResponseResult<ProjVideoGenerateVO> randomOne(ProjVideoGenerateRandomReq randomReq) {
        return succ(projVideoGenerateService.getRandomOne(randomReq));
    }

    @ApiOperation(value = "随机获取一个视频v2", tags = {"oog200"})
    @GetMapping("/v2/video/randomOne")
    public ResponseResult<ProjVideoGenerateV2VO> randomOneV2(ProjVideoGenerateRandomReq randomReq) {
        return succ(projVideoGenerateService.getRandomOneV2(randomReq));
    }

    @ApiOperation(value = "随机获取一个视频v3", tags = {"oog200"})
    @GetMapping("/video/v3/randomOne")
    public ResponseResult<ProjVideoGenerateV3VO> randomOneV3(ProjVideoGenerateRandomReq randomReq) {
        return succ(projVideoGenerateService.getRandomOneV3(randomReq));
    }

    @ApiOperation(value = "随机获取一个视频v4", tags = {"oog200"})
    @GetMapping("/video/v4/randomOne")
    public ResponseResult<ProjVideoGenerateV4VO> randomOneV4(ProjVideoGenerateRandomV4Req randomReq) {
        return succ(projVideoGenerateService.getRandomOneV4(randomReq));
    }

    @ApiOperation(value = "plan v1", tags = {"oog200"})
    @GetMapping("/video/v1/plan")
    public ResponseResult<List<ProjVideoGeneratePlanVO>> plan(ProjVideoGeneratePlanReq planReq) {
        List<ProjVideoGeneratePlanVO> planVOList = projVideoGenerateService.selectVideoPlan(planReq);
        return succ(planVOList);
    }

    @ApiOperation(value = "plan v2", tags = {"oog200"})
    @GetMapping("/video/v2/plan")
    public ResponseResult<List<ProjVideoGeneratePlanV2VO>> planV2(ProjVideoGeneratePlanV2Req planReq) {
        return succ(projVideoGenerateService.selectVideoPlanV2(planReq));
    }

    @ApiOperation(value = "plan workoutByIds v1", tags = {"oog200"})
    @GetMapping("/video/v1/workoutByIds")
    public ResponseResult<List<ProjVideoGenerateRefreshPlanVO>> workoutByIds(ProjVideoGenerateRefreshPlanReq refreshPlanReq) {
        List<ProjVideoGenerateRefreshPlanVO> planVOList = projVideoGenerateService.selectVideoPlanByWorkoutIds(refreshPlanReq);
        return succ(planVOList);
    }

    @ApiOperation(value = "plan workoutByIds v2", tags = {"oog200"})
    @GetMapping("/video/v2/workoutByIds")
    public ResponseResult<List<ProjVideoGenerateRefreshPlanV2VO>> workoutByIdsV2(ProjVideoGenerateRefreshPlanV2Req refreshPlanReq) {
        return succ(projVideoGenerateService.selectVideoPlanByWorkoutIdsV2(refreshPlanReq));
    }

}
