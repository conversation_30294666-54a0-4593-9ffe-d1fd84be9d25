package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ResYogaVideo;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 瑜伽视频 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
public interface IResYogaVideoService extends IService<ResYogaVideo> {

    /**
     * 获取auto workout 对应的 video 列表
     *
     * @param workoutIdList
     * @return
     */
    List<ResYogaVideoDetailVO> listYogaVideo4AutoWorkoutAndSupportI18n(Collection<Integer> workoutIdList);

    /**
     * 获取 regular workout 对应的 video 列表
     *
     * @param workoutIdList
     * @return
     */
    List<ResYogaVideoDetailVO> listYogaVideo4RegularWorkoutAndSupportI18n(Collection<Integer> workoutIdList);

}
