package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * proj yoga pose workout
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ProjYogaPoseWorkout对象", description="proj yoga pose workout")
public class ProjYogaPoseWorkout extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "event name")
    private String eventName;

    @ApiModelProperty(value = "sanskrit name")
    private String sanskritName;

    @ApiModelProperty(value = "workout 封面图")
    private String coverImgUrl;

    @ApiModelProperty(value = "workout 详情图")
    private String detailImgUrl;

    @ApiModelProperty(value = "pose 彩色图像")
    private String poseLightImgUrl;

    @ApiModelProperty(value = "pose 黑白图像")
    private String poseDarkImgUrl;

    @ApiModelProperty(value = "难度Newbie, Beginner, Intermediate, Advanced")
    private String difficulty;

    @ApiModelProperty(value = "pose详细介绍")
    private String instructions;

    @ApiModelProperty(value = "pose的优势")
    private String benefits;

    @ApiModelProperty(value = "chair variation for seniors")
    private String chairVariation;

    @ApiModelProperty(value = "chair variation image")
    private String chairVariationImgUrl;

    @ApiModelProperty(value = "chair variation tips for beginner")
    private String chairVariationTips;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer flexibility;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer balance;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer strength;

    @ApiModelProperty(value = "难度等级，0，1，2，3，4，5")
    private Integer relaxation;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "总时长")
    private Integer duration;

    @ApiModelProperty(value = "是否收费 0不收费 1收费")
    private Integer subscription;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用 3未就绪")
    private Integer status;

    @ApiModelProperty(value = "资源更新状态 0成功 1更新中 2失败")
    private Integer updateStatus;

    @ApiModelProperty(value = "m3u8视频")
    private String videoM3u8Url;

    @ApiModelProperty(value = "Video 的2532 m3u8地址")
    private String video2532Url;

    @ApiModelProperty(value = "音频json，仅guidance")
    private String audioLongJson;

    @ApiModelProperty(value = "拼系统音-音频，仅系统音+名称")
    private String audioShortJson;

    @ApiModelProperty(value = "视频播放弹窗时间点")
    private String videoMaskJson;

    @ApiModelProperty(value = "取值Flexibility、Balance、Strength、Relaxation")
    private String focus;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "projYogaPoseVideoId")
    private Integer projYogaPoseVideoId;

    @ApiModelProperty(value = "Standing，Seated，Supine，Prone，Arm & Leg Support")
    private String position;

}
