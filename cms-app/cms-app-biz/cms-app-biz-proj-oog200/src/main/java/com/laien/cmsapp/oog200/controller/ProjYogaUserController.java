package com.laien.cmsapp.oog200.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.requst.ProjYogaUserReq;
import com.laien.cmsapp.oog200.response.ProjYogaUserInviteLinkVO;
import com.laien.cmsapp.oog200.response.ProjYogaUserInviteResultVO;
import com.laien.cmsapp.oog200.response.ProjYogaUserVO;
import com.laien.cmsapp.oog200.service.IProjYogaUserActivityService;
import com.laien.cmsapp.oog200.service.IProjYogaUserService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: hhl
 * @date: 2025/6/5
 */
@Slf4j
@Api(tags = "app端：yoga user")
@RestController
@RequestMapping("/oog200/yogaUser")
public class ProjYogaUserController extends ResponseController {

    @Resource
    private IProjYogaUserService yogaUserService;

    @Resource
    private IProjYogaUserActivityService userActivityService;

    @ApiOperation(value = "创建用户", notes = "thirdPartyId表示第三方平台ID，必填")
    @PostMapping
    public ResponseResult<ProjYogaUserVO> generateUser(ProjYogaUserReq userReq) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        return succ(yogaUserService.createUser(userReq, versionInfoBO, 1));
    }

    @ApiOperation(value = "根据用户信息，创建分享链接", notes = "分享链接中的af_sub1字段值是邀请码")
    @GetMapping(value = "/inviteLink")
    public ResponseResult<ProjYogaUserInviteLinkVO> generateInviteLink(@RequestParam Integer userId) {

        return succ(yogaUserService.createInviteLink(userId));
    }

    @ApiOperation(value = "根据用户信息，获取其邀请结果", notes = "包含用户邀请购买的人数，用户所获奖品列表")
    @GetMapping(value = "/invite/detail")
    public ResponseResult<ProjYogaUserInviteResultVO> getInviteResult(@RequestParam Integer userId, @RequestParam double timeStamp) {

        return succ(userActivityService.getByUserId(userId));
    }
}
