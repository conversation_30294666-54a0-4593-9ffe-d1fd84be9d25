package com.laien.cmsapp.oog200.response;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "Pose library list item res", description = "Pose library list item res")
public class ProjPoseLibraryPubListItemSubVO {

    @ApiModelProperty(value = "名称,最多100个字符")
    private String poseName;

    @ApiModelProperty(value = "梵文名称，最多100个字符")
    private String sanskritName;

    @ApiModelProperty(value = "pose图 url地址")
    private String animationUrl;

    @ApiModelProperty(value = "是否为basic 1是 0否")
    private Integer basic;

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "difficulty code")
    private Integer difficultyCode;

    @ApiModelProperty(value = "position")
    private String position;

    @ApiModelProperty(value = "focus")
    private String focus;

    @ApiModelProperty(value = "简介，最多1000字符，去除前后空格")
    private List<String> description;

    @ApiModelProperty(value = "第三方视频链接")
    private String videoLinkUrl;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    @JsonIgnore
    private Integer status;

    @ApiModelProperty(value = "数据id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "删除标识 0 未删除，1已删除")
    @TableLogic
    @JsonIgnore
    private Integer delFlag;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    @JsonIgnore
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonIgnore
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonIgnore
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonIgnore
    private LocalDateTime updateTime;
}
