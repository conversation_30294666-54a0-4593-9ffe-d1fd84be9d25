package com.laien.cmsapp.oog200.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.oog200.entity.ProjSound;
import com.laien.cmsapp.oog200.requst.ProjSoundReq;
import com.laien.cmsapp.oog200.response.ProjSoundVO;

import java.util.List;

public interface IProjSoundService extends IService<ProjSound> {
    /**
     * 按照类型，子类型 , 性别查找 sound 列表
     *
     * @param soundReq soundReq
     * @return list
     */
    List<ProjSoundVO> selectSoundList(ProjSoundReq soundReq);

}