package com.laien.cmsapp.oog200.entity;

import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Author:  hhl
 * Date:  2025/1/7 21:42
 */
@Data
public class ProjUnitPub extends BaseModel {

    private Integer version;

    @ApiModelProperty(value = "单位名称")
    private String name;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "启用状态 0草稿 1启用 2停用")
    private Integer status;

}
