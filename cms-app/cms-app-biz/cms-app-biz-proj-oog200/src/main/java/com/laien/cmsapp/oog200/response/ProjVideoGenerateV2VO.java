package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * note: video v2
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video v2", description = "video v2")
public class ProjVideoGenerateV2VO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "video url")
    private String videoUrl;
    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;
    @ApiModelProperty(value = "guidance subtitles")
    private List<ProjVideoGenerateI18nV2VO> guidanceSubtitles;
    @ApiModelProperty(value = "videos")
    private List<ProjVideoV2VO> videos;

}
