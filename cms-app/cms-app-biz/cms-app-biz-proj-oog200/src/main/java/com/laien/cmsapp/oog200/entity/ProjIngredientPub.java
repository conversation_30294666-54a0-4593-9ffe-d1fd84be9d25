package com.laien.cmsapp.oog200.entity;

import com.laien.common.domain.annotation.AppTextTranslateField;
import com.laien.common.domain.component.AppTextCoreI18nModel;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjIngredientPub extends BaseModel implements AppTextCoreI18nModel {

    private Integer version;

    @ApiModelProperty(value = "名称")
    @AppTextTranslateField
    private String name;

    @ApiModelProperty(value = "数量")
    private String amount;

    @ApiModelProperty(value = "proj_unit表数据id")
    private Integer projUnitId;

    @ApiModelProperty(value = "proj_dish表数据id")
    private Integer projDishId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

}
