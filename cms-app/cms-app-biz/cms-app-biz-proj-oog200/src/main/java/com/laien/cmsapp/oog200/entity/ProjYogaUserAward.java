package com.laien.cmsapp.oog200.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.laien.mybatisplus.config.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @author: hhl
 * @date: 2025/6/9
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("proj_yoga_user_award")
@ApiModel(value="ProjYogaUserAward对象", description="yoga user award")
public class ProjYogaUserAward extends BaseModel {

    private Integer userId;

    private Integer awardId;

}
