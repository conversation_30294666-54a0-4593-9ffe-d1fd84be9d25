package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramCategoryPub;
import com.laien.cmsapp.oog200.response.ProjYogaProgramCategoryListVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:49
 */
public interface IProjYogaProgramCategoryPubService extends IService<ProjYogaProgramCategoryPub> {

    List<ProjYogaProgramCategoryListVO> listProgramCategory(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer status, Integer m3u8Type, String lang);

}
