package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.entity.ResImage;
import com.laien.cmsapp.oog200.bo.ProjAutoWorkoutBasicInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaAutoWorkoutPlanBO;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkout;
import com.laien.cmsapp.oog200.entity.ProjYogaAutoWorkoutTemplatePub;
import com.laien.cmsapp.oog200.mapper.ProjYogaAutoWorkoutMapper;
import com.laien.cmsapp.oog200.properties.Oog200Properties;
import com.laien.cmsapp.oog200.requst.ProjWorkoutPlanReq;
import com.laien.cmsapp.oog200.requst.ProjYogaAutoWorkoutListReq;
import com.laien.cmsapp.oog200.requst.ProjYogaAutoWorkoutPlanReq;
import com.laien.cmsapp.oog200.response.*;
import com.laien.cmsapp.oog200.service.IProjAutoWorkoutBasicInfoService;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutService;
import com.laien.cmsapp.oog200.service.IProjYogaAutoWorkoutTemplatePubService;
import com.laien.cmsapp.oog200.service.IResYogaVideoService;
import com.laien.cmsapp.service.IResImageService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.exception.BizException;
import com.laien.common.oog200.enums.*;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * oog200 workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Slf4j
@Service
public class ProjYogaAutoWorkoutServiceImpl extends ServiceImpl<ProjYogaAutoWorkoutMapper, ProjYogaAutoWorkout> implements IProjYogaAutoWorkoutService {

    @Resource
    private IProjYogaAutoWorkoutTemplatePubService projYogaAutoWorkoutTemplatePubService;
    @Resource
    private IResImageService resImageService;
    @Resource
    private Oog200Properties oog200Properties;
    @Resource
    private IResYogaVideoService yogaVideoService;
    @Resource
    private IProjAutoWorkoutBasicInfoService workoutBasicInfoService;
    @Resource
    private I18nUtil i18nUtil;

    public static final int PLAN_DAY_COUNT = 21;
    public static final String EVENT_NAME_PREFIX = "YogaAutoWorkout_";

    /**
     * 根据goal 获取 point
     * @param goal goal
     * @return String
     */
    private String getPoint(String goal) {
        if (Objects.equals("weight-loss", goal)) {
            return "Weight Loss";
        } else if(Objects.equals("flexibility", goal)) {
            return "Improve Flexibility";
        } else if(Objects.equals("mindfulness", goal)) {
            return "Mindfulness";
        } else {
            return "Learn Yoga Basics";
        }
    }

    @Override
    public List<ProjPlanWorkoutListVO> getPlan(ProjWorkoutPlanReq planReq, ProjPublishCurrentVersionInfoBO versionInfoBO) {

        DifficultyEnum difficultyEnum = DifficultyEnum.getByCode(planReq.getDifficultyCode());
        if (Objects.isNull(difficultyEnum)) {
            difficultyEnum = DifficultyEnum.NEWBIE;
            planReq.setDifficultyCode(difficultyEnum.getCode());
            log.error("oog200 plan difficultyCode illegal, use default value 1, planReq: {}", planReq);
        }


        GoalEnum goalEnum = GoalEnum.getByCode(planReq.getGoalCode());
        if (Objects.isNull(goalEnum)) {
            goalEnum = GoalEnum.GOAL_BASIC;
            planReq.setGoalCode(goalEnum.getCode());
            log.error("oog200 plan goal code illegal, use default value 0, planReq: {}", planReq);
        }

        DurationEnum durationEnum = DurationEnum.getByCode(planReq.getDurationCode());
        if (Objects.isNull(durationEnum)) {
            durationEnum = DurationEnum.MIN_0_10;
            planReq.setDurationCode(durationEnum.getCode());
            log.error("oog200 plan duration code illegal, use default value 1, planReq: {}", planReq);
        }

        YogaAutoWorkoutTemplateEnum templateTypeEnum = YogaAutoWorkoutTemplateEnum.getByCode(planReq.getPlanTypeCode());

        List<ProjYogaAutoWorkoutTemplatePub> templateList = projYogaAutoWorkoutTemplatePubService.list(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getName(), versionInfoBO);
        if (CollectionUtils.isEmpty(templateList)) {
            log.error("Not found classic yoga auto workout template, planReq : {}.", planReq);
            return Collections.emptyList();
        }

        List<Integer> templateIds = templateList.stream().map(ProjYogaAutoWorkoutTemplatePub::getId).collect(Collectors.toList());
        List<ProjYogaAutoWorkout> workoutList = listEnableByTemplateIds(templateIds);
        if (CollectionUtils.isEmpty(workoutList)) {
            log.error("Not found classic yoga auto workout record, planReq : {}.", planReq);
            return Collections.emptyList();
        }

        AutoWorkoutBasicInfoPointEnum pointEnum = goalEnum.getPointEnum();
        DifficultyEnum difficultyEnum4UnMatchClassic = difficultyEnum;
        if (templateTypeEnum == YogaAutoWorkoutTemplateEnum.SOMATIC_YOGA || templateTypeEnum == YogaAutoWorkoutTemplateEnum.LAZY_YOGA) {
            pointEnum = null;
            difficultyEnum4UnMatchClassic = null;
        }
        List<ProjAutoWorkoutBasicInfoBO> configImageAndWorkoutList = workoutBasicInfoService.listEnable(templateTypeEnum, pointEnum, difficultyEnum4UnMatchClassic);
        // 针对classic yoga，在image数据未准备好时，走兜底逻辑
        if (CollectionUtils.isEmpty(configImageAndWorkoutList)) {
            configImageAndWorkoutList = workoutBasicInfoService.listEnable(templateTypeEnum, pointEnum, null);
        }
        ProjYogaAutoWorkoutPlanBO workoutPlanBO = wrapPlanBO(workoutList, configImageAndWorkoutList, durationEnum, difficultyEnum, planReq.getLang());
        List<ProjPlanWorkoutListVO> imageAndWorkoutPlan = workoutBasicInfoService.generatePlan(workoutPlanBO);
        if (CollectionUtils.isEmpty(imageAndWorkoutPlan)) {
            log.error("Can't generate plan for classic yoga auto workout, planReq : {}.", planReq);
            return Collections.emptyList();
        }

        List<Integer> matchWorkoutIdList = imageAndWorkoutPlan.stream().map(workout -> workout.getId()).collect(Collectors.toList());
        List<ResYogaVideoDetailVO> yogaVideoDetailVOList = yogaVideoService.listYogaVideo4AutoWorkoutAndSupportI18n(matchWorkoutIdList);
        Map<Integer, List<ResYogaVideoDetailVO>> videoListMap = yogaVideoDetailVOList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));

        Integer difficultyCode = difficultyEnum.getCode();
        Map<Integer, ProjYogaAutoWorkout> workoutIdMap = workoutList.stream().collect(Collectors.toMap(ProjYogaAutoWorkout::getId, Function.identity()));
        List<ProjPlanWorkoutListVO> finalWorkout = imageAndWorkoutPlan.stream().map(workoutPlan -> {
            ProjYogaAutoWorkout workout = workoutIdMap.get(workoutPlan.getId());
            ProjPlanWorkoutListVO listVO = convert2ListVO(workout, planReq.getM3u8Type(), difficultyCode, videoListMap);
            listVO.setName(workoutPlan.getName());
            listVO.setInfoId(workoutPlan.getInfoId());
            listVO.setCoverImgUrl(workoutPlan.getCoverImgUrl());
            listVO.setPlanTypeCode(templateTypeEnum.getCode());
            return listVO;
        }).collect(Collectors.toList());
        for (ProjPlanWorkoutListVO workout : finalWorkout) {
            handleVideoList(workout.getVideos());
        }
        return finalWorkout;
    }

    private ProjYogaAutoWorkoutPlanBO wrapPlanBO(List<ProjYogaAutoWorkout> allWorkouts, List<ProjAutoWorkoutBasicInfoBO> configImageAndWorkoutList, DurationEnum durationEnum, DifficultyEnum difficultyEnum, String lang) {

        Set<Integer> allMatchWorkouts = Sets.newHashSet();
        Set<Integer> matchDurationWorkouts = Sets.newHashSet();
        Set<Integer> matchOtherWorkouts = Sets.newHashSet();
        splitWorkout(allWorkouts, allMatchWorkouts, matchDurationWorkouts, matchOtherWorkouts, difficultyEnum, durationEnum);

        ProjYogaAutoWorkoutPlanBO workoutPlanBO = new ProjYogaAutoWorkoutPlanBO();
        workoutPlanBO.setBasicInfoBOList(configImageAndWorkoutList);
        workoutPlanBO.setMatchAllWorkoutIds(allMatchWorkouts);
        workoutPlanBO.setMatchDurationWorkoutIds(matchDurationWorkouts);

        workoutPlanBO.setMatchOtherWorkoutIds(matchOtherWorkouts);
        workoutPlanBO.setLang(lang);
        return workoutPlanBO;
    }

    private List<ProjYogaAutoWorkout> listEnableByTemplateIds(List<Integer> templateIdList) {

        LambdaQueryWrapper<ProjYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIdList);
        return list(queryWrapper);
    }

    private void splitWorkout(List<ProjYogaAutoWorkout> workoutList, Set<Integer> allMatchWorkouts, Set<Integer> matchDurationWorkouts, Set<Integer> matchDifficultyWorkouts, DifficultyEnum difficultyEnum, DurationEnum durationEnum) {

        Function<ProjYogaAutoWorkout, Boolean> matchDuration = workout -> workout.getDuration() >= durationEnum.getMin() && workout.getDuration() <= durationEnum.getMax();
        Function<ProjYogaAutoWorkout, Boolean> matchDifficulty = workout -> Objects.equals(workout.getDifficulty(), difficultyEnum.getName());

        for (ProjYogaAutoWorkout workout : workoutList) {

            if (matchDuration.apply(workout) && matchDifficulty.apply(workout)) {
                allMatchWorkouts.add(workout.getId());
                continue;
            }

            if (matchDuration.apply(workout)) {
                matchDurationWorkouts.add(workout.getId());
                continue;
            }

            if (matchDifficulty.apply(workout)) {
                matchDifficultyWorkouts.add(workout.getId());
            }
        }
    }

    private ProjPlanWorkoutListVO convert2ListVO(ProjYogaAutoWorkout workout, Integer m3u8Type, Integer difficultyCode, Map<Integer, List<ResYogaVideoDetailVO>> videoListMap) {

        ProjPlanWorkoutListVO listVO = new ProjPlanWorkoutListVO();
        BeanUtils.copyProperties(workout, listVO);
        listVO.setVideos(videoListMap.get(listVO.getId()));
        listVO.setDifficultyCode(difficultyCode);
        listVO.setDuration(TimeConvertUtil.millisToMinutes(workout.getDuration()));

        listVO.setEventName(EVENT_NAME_PREFIX + workout.getId());
        listVO.setPlanTypeCode(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode());
        if (!Objects.equals(GlobalConstant.TWO, m3u8Type)) {
            listVO.setVideoM3u8Url(listVO.getVideo2532Url());
        }
        return listVO;
    }

    @Override
    public List<ProjYogaAutoWorkoutListVO> getPlan(ProjYogaAutoWorkoutPlanReq planReq) {
        String duration = planReq.getDuration();
        if (StringUtils.isBlank(duration)) {
            throw new BizException("duration required");
        }
        String pattern = "\\d*-\\d*";
        if (!duration.matches(pattern)) {
            throw new BizException("duration format error");
        }
        Integer startDuration = null, endDuration = null;
        String startStr = duration.substring(0, duration.indexOf("-"));
        String endStr = duration.substring(duration.indexOf("-") + 1);
        if (StringUtils.isNotBlank(startStr)) {
            startDuration = Integer.parseInt(startStr) * 60 * 1000;
        }
        if (StringUtils.isNotBlank(endStr)) {
            endDuration = Integer.parseInt(endStr)  * 60 * 1000;
        }

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjYogaAutoWorkoutTemplatePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjYogaAutoWorkoutTemplatePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaAutoWorkoutTemplatePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjYogaAutoWorkoutTemplatePub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjYogaAutoWorkoutTemplatePub::getType, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode());
        List<Integer> templateIdList = projYogaAutoWorkoutTemplatePubService.list(wrapper)
                .stream().map(BaseModel::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(templateIdList)) {
            return CollectionUtil.newArrayList();
        }
        String difficulty = planReq.getDifficulty();
        String newbie = "Newbie", beginner = "Beginner", intermediate = "Intermediate", advanced = "Advanced";
        // 为空默认 beginner
        if (difficulty == null) {
            difficulty = beginner;
        }
        LambdaQueryWrapper<ProjYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.equals(difficulty, intermediate) || Objects.equals(difficulty, advanced)) {
            queryWrapper.eq(ProjYogaAutoWorkout::getDifficulty, intermediate);
        } else if (Objects.equals(difficulty, newbie)) {
            queryWrapper.in(ProjYogaAutoWorkout::getDifficulty, Lists.newArrayList(newbie, beginner));
        } else {
            queryWrapper.eq(ProjYogaAutoWorkout::getDifficulty, beginner);
        }
        queryWrapper.ge(Objects.nonNull(startDuration), ProjYogaAutoWorkout::getDuration, startDuration);
        queryWrapper.lt(Objects.nonNull(endDuration), ProjYogaAutoWorkout::getDuration, endDuration);
        queryWrapper.eq(ProjYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId, templateIdList);
        List<ProjYogaAutoWorkout> list = this.list(queryWrapper);
        if (list == null || list.isEmpty()) {
            return CollectionUtil.newArrayList();
        }
        // 打乱数据
        list = randomCombine(list, difficulty, newbie);
        int needCount = 21;
        Map<Integer, List<ProjYogaAutoWorkout>> workoutMap = new HashMap<>(needCount);
        List<Integer> startVideoIdList = new ArrayList<>(needCount);
        for (ProjYogaAutoWorkout autoWorkout : list) {
            Integer startVideoId = autoWorkout.getResYogaStartVideoId();
            if (workoutMap.containsKey(startVideoId)) {
                workoutMap.get(startVideoId).add(autoWorkout);
            } else {
                List<ProjYogaAutoWorkout> workoutList = new ArrayList<>();
                workoutList.add(autoWorkout);
                workoutMap.put(startVideoId, workoutList);
                startVideoIdList.add(startVideoId);
            }

            // 已有21个key，不需要更多数据
            if (workoutMap.keySet().size()>= needCount) {
                break;
            }
        }

        List<ProjYogaAutoWorkout> workoutSelectList = new ArrayList<>(needCount);
        List<Integer> idList = new ArrayList<>();
        int start = 0;
        for (int i = 0; i < needCount; i++) {
            Integer startId = startVideoIdList.get(start);
            List<ProjYogaAutoWorkout> projYogaAutoWorkouts = workoutMap.get(startId);
            ProjYogaAutoWorkout workout = projYogaAutoWorkouts.get(0);
            workoutSelectList.add(workout);
            idList.add(workout.getId());
            projYogaAutoWorkouts.remove(workout);
            if (projYogaAutoWorkouts.isEmpty()) {
                startVideoIdList.remove(startId);
            }

            int startSize = startVideoIdList.size();
            if (startSize == 0) {
                break;
            }

            start ++;
            if (start >= startSize) {
                start = 0;
            }
        }

        // 为每一个workout 设置image、video
        List<ProjYogaAutoWorkoutListVO> workoutList = convertWorkout2VOAndSupportI18n(planReq, workoutSelectList);
        DifficultyEnum difficultyEnum = DifficultyEnum.getByName(difficulty);
        if (null != difficultyEnum) {
            for (ProjYogaAutoWorkoutListVO workout : workoutList) {
                workout.setDifficultyCode(difficultyEnum.getCode());
            }
        }

        workoutList = replaceWorkout(planReq, workoutList);
        setGuidance(workoutList);
        i18nUtil.translate(workoutList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return workoutList;
    }

    private void setGuidance(List<ProjYogaAutoWorkoutListVO> workoutList) {

        if (CollectionUtils.isEmpty(workoutList)) {
            return;
        }

        for (ProjYogaAutoWorkoutListVO workoutListVO : workoutList) {
            ProjYogaAutoWorkoutGuidanceVO guidanceVO = ProjYogaAutoWorkoutGuidanceVO.buildGuidance(workoutListVO.getAudioLongJson(), workoutListVO.getAudioShortJson(), GlobalConstant.DEFAULT_LANGUAGE);
            workoutListVO.setGuidance(guidanceVO);
        }
    }

    /**
     * 对 workoutList 列表做随机排列打乱顺序
     * （针对Newbie类型的数据，如果不够组成一个plan，需要使用beginner的数据，
     * 只在Newbie类型刚上线时存在这种情况）
     *
     * @param workoutList
     * @param difficulty
     * @param newbie
     * @return
     */
    private List<ProjYogaAutoWorkout> randomCombine(List<ProjYogaAutoWorkout> workoutList, String difficulty, String newbie) {

        // 非Newbie，不做处理
        if (!Objects.equals(difficulty, newbie)) {
            Collections.shuffle(workoutList);
            return workoutList;
        }

        // 如果Newbie可以组成一个plan，返回列表
        Predicate<ProjYogaAutoWorkout> isNewbie = workout -> Objects.equals(newbie, workout.getDifficulty());
        List<ProjYogaAutoWorkout> newbieList = workoutList.stream().filter(isNewbie).collect(Collectors.toList());
        if (newbieList.size() >= PLAN_DAY_COUNT) {
            Collections.shuffle(newbieList);
            return newbieList;
        }

        // Newbie不能组成一个plan，需要加入一些Beginner，保持Newbie在前，Beginner在后
        List<ProjYogaAutoWorkout> combineList = new ArrayList<>(workoutList.size());
        Collections.shuffle(newbieList);
        combineList.addAll(newbieList);

        List<ProjYogaAutoWorkout> beginnerList = workoutList.stream().filter(isNewbie.negate()).collect(Collectors.toList());
        Collections.shuffle(beginnerList);
        combineList.addAll(beginnerList);
        return combineList;
    }

    @Override
    public List<ProjYogaAutoWorkoutRefreshVO> findList(ProjYogaAutoWorkoutListReq workoutListReq) {
        List<ProjPlanWorkoutListVO> workoutList = findListV2(workoutListReq, true, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode());
        List<ProjYogaAutoWorkoutRefreshVO> workoutRefreshList = new ArrayList<>(workoutList.size());
        for (ProjPlanWorkoutListVO workout : workoutList) {
            ProjYogaAutoWorkoutRefreshVO workoutRefreshVO = new ProjYogaAutoWorkoutRefreshVO();
            BeanUtils.copyProperties(workout, workoutRefreshVO);
            workoutRefreshList.add(workoutRefreshVO);
        }
        return workoutRefreshList;
    }

    @Override
    public List<ProjPlanWorkoutListVO> findListV2(ProjYogaAutoWorkoutListReq workoutListReq, boolean isV1, Integer planTypeCode) {

        if (CollectionUtils.isEmpty(workoutListReq.getIdList())) {
            return Collections.emptyList();
        }

        // 获取指定的 yoga workout
        LambdaQueryWrapper<ProjYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        queryWrapper.in(ProjYogaAutoWorkout::getId, workoutListReq.getIdList());
        List<ProjYogaAutoWorkout> yogaAutoWorkoutList = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(yogaAutoWorkoutList)) {
            return Collections.emptyList();
        }

        // 通过Pub Template对workout进行过滤
        Set<Integer> templateIdEnableSet = listEnableYogaAutoWorkoutTemplatePub(yogaAutoWorkoutList);
        yogaAutoWorkoutList = yogaAutoWorkoutList.stream().filter(
                workout -> templateIdEnableSet.contains(workout.getProjYogaAutoWorkoutTemplateId())).collect(Collectors.toList());

        // 多语言转换
        i18nUtil.translate(yogaAutoWorkoutList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());

        // 获取workout对应的i18n video信息
        List<Integer> workoutIdList = yogaAutoWorkoutList.stream().map(ProjYogaAutoWorkout::getId).collect(Collectors.toList());
        List<ResYogaVideoDetailVO> yogaVideoDetailVOList = yogaVideoService.listYogaVideo4AutoWorkoutAndSupportI18n(workoutIdList);
        Map<Integer, List<ResYogaVideoDetailVO>> videoListMap = yogaVideoDetailVOList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));

        List<ProjPlanWorkoutListVO> workoutList = new ArrayList<>();
        String difficulty = workoutListReq.getDifficulty();

        // 注释后使用最新翻译
//        if (isV1) {
//            difficulty = translateDifficulty(difficulty);
//        }
        DifficultyEnum difficultyEnum = DifficultyEnum.getByName(workoutListReq.getDifficulty());
        Integer difficultyCode = null;
        if (null != difficultyEnum) {
            difficultyCode = difficultyEnum.getCode();
        }
        for (ProjYogaAutoWorkout workout : yogaAutoWorkoutList) {
//            if (templateIdEnableSet.contains(workout.getProjYogaAutoWorkoutTemplateId())) {
            ProjPlanWorkoutListVO workoutRefreshVO = new ProjPlanWorkoutListVO();
                BeanUtils.copyProperties(workout, workoutRefreshVO);
                workoutRefreshVO.setDuration(TimeConvertUtil.millisToMinutes(workout.getDuration()));
                workoutRefreshVO.setDifficulty(difficulty);
                workoutRefreshVO.setEventName(EVENT_NAME_PREFIX + workout.getId());
            if (null != difficultyEnum) {
                workoutRefreshVO.setDifficultyCode(difficultyCode);
            }
                ////以下luolang - update
                List<ResYogaVideoDetailVO> videoList = handleVideoList(videoListMap.get(workout.getId()));
                workoutRefreshVO.setVideos(videoList);
                workoutRefreshVO.setPlanTypeCode(planTypeCode);
                //workoutRefreshVO.setVideos(videoListMap.get(workout.getId()));
                //////以上luolang - update
                workoutList.add(workoutRefreshVO);
//            }
        }

        //替换m3u8地址
        if (CollectionUtil.isNotEmpty(workoutList) && ObjectUtil.notEqual(GlobalConstant.TWO, workoutListReq.getM3u8Type())) {
            workoutList.forEach(result -> {
                if (StrUtil.isNotBlank(result.getVideo2532Url())) {
                    result.setVideoM3u8Url(result.getVideo2532Url());
                }
            });
        }
        return workoutList;
    }

    private Set<Integer> listEnableYogaAutoWorkoutTemplatePub(List<ProjYogaAutoWorkout> yogaAutoWorkoutList) {

        if (CollectionUtils.isEmpty(yogaAutoWorkoutList)) {
            return Collections.emptySet();
        }
        Set<Integer> templateIdSet = yogaAutoWorkoutList.stream().map(ProjYogaAutoWorkout::getProjYogaAutoWorkoutTemplateId).collect(Collectors.toSet());

        // 查询启用的templateId
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjYogaAutoWorkoutTemplatePub> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProjYogaAutoWorkoutTemplatePub::getId)
                .eq(ProjYogaAutoWorkoutTemplatePub::getProjId, versionInfoBO.getProjId())
                .eq(ProjYogaAutoWorkoutTemplatePub::getStatus, GlobalConstant.STATUS_ENABLE)
                .eq(ProjYogaAutoWorkoutTemplatePub::getVersion, versionInfoBO.getCurrentVersion())
                .in(ProjYogaAutoWorkoutTemplatePub::getId, templateIdSet);

        List<ProjYogaAutoWorkoutTemplatePub> workoutTemplatePubList = projYogaAutoWorkoutTemplatePubService.list(wrapper);
        if (CollectionUtils.isEmpty(workoutTemplatePubList)) {
            return Collections.emptySet();
        }

        return workoutTemplatePubList.stream().map(BaseModel::getId).collect(Collectors.toSet());
    }

    @Override
    public List<ProjYogaAutoWorkoutListVO> replaceWorkout(ProjYogaAutoWorkoutPlanReq planReq, List<ProjYogaAutoWorkoutListVO> workoutListVOList) {

        //替换m3u8地址
        if (CollectionUtil.isNotEmpty(workoutListVOList) && ObjectUtil.notEqual(GlobalConstant.TWO, planReq.getM3u8Type())) {
            workoutListVOList.forEach(result -> {
                if (StrUtil.isNotBlank(result.getVideo2532Url())) {
                    result.setVideoM3u8Url(result.getVideo2532Url());
                }
            });
        }

        // 仅针对Newbie类型
        if (CollectionUtils.isEmpty(workoutListVOList) || !Objects.equals("Newbie", planReq.getDifficulty())) {
            return workoutListVOList;
        }

        // 没有匹配的时长，直接返回
        List<Integer> matchWorkoutIdList = listWorkoutByDuration(planReq.getDuration());
        if(CollectionUtils.isEmpty(matchWorkoutIdList)) {
            return workoutListVOList;
        }

        // 按条件查询
        LambdaQueryWrapper<ProjYogaAutoWorkout> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjYogaAutoWorkout::getId, matchWorkoutIdList);
        queryWrapper.eq(ProjYogaAutoWorkout::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjYogaAutoWorkout> matchWorkoutList = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(matchWorkoutList)) {
            log.warn("can't find workout by id, please check oog200 replace auto workout config.");
            return workoutListVOList;
        }

        // 为每一个workout 设置image、video
        List<ProjYogaAutoWorkoutListVO> replaceWorkoutList = convertWorkout2VOAndSupportI18n(planReq, matchWorkoutList);

        // 执行替换操作
        for (int i = 0; i < replaceWorkoutList.size(); i++) {
            if (i + 1 > workoutListVOList.size()) {
                break;
            }
            workoutListVOList.set(i, replaceWorkoutList.get(i));
        }
        i18nUtil.translate(replaceWorkoutList, ProjCodeEnums.OOG200, RequestContextUtils.getLanguage());
        return workoutListVOList;
    }

    private List<Integer> listWorkoutByDuration(String duration) {

        if (Objects.equals(duration, "-10")) {
            return oog200Properties.getFiveAndTenWorkoutIdList();
        }

        if (Objects.equals(duration, "10-20")) {
            return oog200Properties.getTenAndTwentyWorkoutIdList();
        }

        if (Objects.equals(duration, "20-")) {
            return oog200Properties.getTwentyPlusWorkoutIdList();
        }

        return Collections.emptyList();
    }

    private List<ProjYogaAutoWorkoutListVO> convertWorkout2VOAndSupportI18n(ProjYogaAutoWorkoutPlanReq planReq,
                                                                            List<ProjYogaAutoWorkout> matchWorkoutList) {

        // 获取i18n图像
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ResImage> imageList = resImageService.find4I18n(versionInfoBO.getAppCode(), "Yoga Auto Workout", this.getPoint(planReq.getGoal()), matchWorkoutList.size());

        // 获取i18n视频
        List<Integer> matchWorkoutIdList = matchWorkoutList.stream().map(workout -> workout.getId()).collect(Collectors.toList());
        List<ResYogaVideoDetailVO> yogaVideoDetailVOList = yogaVideoService.listYogaVideo4AutoWorkoutAndSupportI18n(matchWorkoutIdList);
        Map<Integer, List<ResYogaVideoDetailVO>> videoListMap = yogaVideoDetailVOList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));

        // 翻译workout
//        translationTableService.doTranslation(matchWorkoutList, ProjYogaAutoWorkout.class);

        // 组装对象
        final AtomicInteger imageIndex = new AtomicInteger(0);
        List<ProjYogaAutoWorkoutListVO> replaceWorkoutList = matchWorkoutList.stream().map(workout -> {

            // 后续可优化
            ProjYogaAutoWorkoutListVO listVO = new ProjYogaAutoWorkoutListVO();
            BeanUtils.copyProperties(workout, listVO);

            listVO.setDuration(TimeConvertUtil.millisToMinutes(workout.getDuration()));
            listVO.setEventName(EVENT_NAME_PREFIX + workout.getId());

            if (videoListMap.containsKey(workout.getId())) {
                List<ResYogaVideoDetailVO> videoList = handleVideoList(videoListMap.get(workout.getId()));
                listVO.setVideos(videoList);
            }

            if (!CollectionUtils.isEmpty(imageList)) {
                ResImage resImage = imageList.get(imageIndex.getAndAdd(1));
                listVO.setName(resImage.getName());
                listVO.setCoverImgUrl(resImage.getCoverImage());
            }

            // image 轮流赋值
            if (imageIndex.get() >= imageList.size() - 1) {
                imageIndex.set(0);
            }
            return listVO;
        }).collect(Collectors.toList());

        return replaceWorkoutList;
    }

    private List<ResYogaVideoDetailVO> handleVideoList(List<ResYogaVideoDetailVO> videoList) {

        if (CollectionUtils.isEmpty(videoList)) {
            return Collections.emptyList();
        }

        for (int i = videoList.size() - 1; i >= 0; i--) {
            if (i > 0) {
                videoList.get(i).setRealVideoDuration(videoList.get(i).getRealVideoDuration() + videoList.get(i - 1).getRealTransitionDuration());
                videoList.get(i).setRealTransitionDuration(videoList.get(i - 1).getRealTransitionDuration());
            } else {
                videoList.get(i).setRealTransitionDuration(0);
            }
        }
        return videoList;
    }

}
