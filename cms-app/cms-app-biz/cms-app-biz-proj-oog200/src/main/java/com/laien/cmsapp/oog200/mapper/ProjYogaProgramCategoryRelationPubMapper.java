package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjYogaProgramCategoryRelationPub;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 11:34
 */
public interface ProjYogaProgramCategoryRelationPubMapper extends BaseMapper<ProjYogaProgramCategoryRelationPub> {

    @Select("<script>" +
            "SELECT * FROM proj_yoga_program_category_relation_pub " +
            "WHERE del_flag = 0 and version = #{version} and proj_yoga_program_category_id IN " +
            "<foreach collection='categoryIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<ProjYogaProgramCategoryRelationPub> listByCategoryAndVersion(@Param("categoryIds") Collection<Integer> categoryIds, Integer version);
}
