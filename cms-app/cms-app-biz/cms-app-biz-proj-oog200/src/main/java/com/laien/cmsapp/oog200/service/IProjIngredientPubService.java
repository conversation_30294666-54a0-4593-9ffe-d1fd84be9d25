package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjIngredientPub;
import com.laien.cmsapp.oog200.response.ProjIngredientVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:41
 */
public interface IProjIngredientPubService extends IService<ProjIngredientPub> {

    List<ProjIngredientVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang);

}
