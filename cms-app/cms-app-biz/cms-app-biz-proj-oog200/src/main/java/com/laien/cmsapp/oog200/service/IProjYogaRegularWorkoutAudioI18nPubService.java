package com.laien.cmsapp.oog200.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjYogaRegularWorkoutAudioI18nPub;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/11/25 11:25
 */
public interface IProjYogaRegularWorkoutAudioI18nPubService extends IService<ProjYogaRegularWorkoutAudioI18nPub> {

    void setAudioI18n4Workout(List<ProjYogaRegularWorkoutDetailVO> workoutList, ProjPublishCurrentVersionInfoBO versionInfo);

}
