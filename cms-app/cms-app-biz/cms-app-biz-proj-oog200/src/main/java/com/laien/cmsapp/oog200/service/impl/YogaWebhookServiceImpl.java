package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.laien.cmsapp.adapty.AdaptyEventsDTO;
import com.laien.cmsapp.adapty.AdaptyWebhookEventTypeEnums;
import com.laien.cmsapp.entity.*;
import com.laien.cmsapp.oog200.entity.*;
import com.laien.cmsapp.oog200.properties.Oog200Properties;
import com.laien.cmsapp.oog200.response.ProjYogaUserAwardVO;
import com.laien.cmsapp.oog200.service.*;
import com.laien.cmsapp.response.AdaptyProfileVO;
import com.laien.cmsapp.response.AppleSubGroupVO;
import com.laien.cmsapp.service.*;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.exception.AdaptyCallbackException;
import com.laien.common.exception.BizException;
import com.laien.common.util.JacksonUtil;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: hhl
 * @date: 2025/6/11
 */
@Slf4j
@Service
public class YogaWebhookServiceImpl implements YogaWebhookService {

    @Resource
    private IProjYogaAdaptyEventService adaptyEventService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private AdaptyService adaptyService;

    @Resource
    private AppleService appleService;

    @Resource
    private Oog200Properties oog200Properties;

    @Resource
    private IProjYogaUserService yogaUserService;

    @Resource
    private IProjYogaUserActivityService yogaUserActivityService;

    @Resource
    private IProjYogaAwardService yogaAwardService;

    @Resource
    private IProjYogaUserAwardService yogaUserAwardService;

    @Resource
    private TransactionTemplate transactionTemplate;

    private static final int scheduleTime = 1000 * 60;
    private static final int waitLockTime = 1000 * 60 * 3;
    private static final String adaptyEventHandleKey = "LOCK:CMSAPP:200:ADAPTY:EVENT";
    private static final RateLimiter rateLimiter = RateLimiter.create(3);
    private static final List<AdaptyWebhookEventTypeEnums> eventTypes = Lists.newArrayList(AdaptyWebhookEventTypeEnums.SUBSCRIPTION_STARTED,
            AdaptyWebhookEventTypeEnums.SUBSCRIPTION_RENEWED, AdaptyWebhookEventTypeEnums.TRIAL_STARTED);

    @Override
    public void adaptyCallback(String body) {

        if (StringUtils.isBlank(body)) {
            return;
        }

        try {
            if (rateLimiter.tryAcquire()) {

                AdaptyEventsDTO adaptyEventsDTO = JacksonUtil.parseObject(body, AdaptyEventsDTO.class);
                if (Objects.isNull(adaptyEventsDTO) || Objects.isNull(adaptyEventsDTO.getEventProperties())) {
                    log.info("[200 adapty callback] event properties is null, ignore it.");
                    return;
                }

                // 转存到数据库
                saveAdaptyEvent(adaptyEventsDTO);
            }
        } catch (Exception ex) {
            log.error(String.format("[200 adapty callback] Handle 200 adapty callback error, body is : %s , please check.", body), ex);
            throw new AdaptyCallbackException(ex.getMessage());
        }
    }

    private void saveAdaptyEvent(AdaptyEventsDTO adaptyEventsDTO) {

        ProjYogaAdaptyEvent event = new ProjYogaAdaptyEvent();
        event.setCreateTime(LocalDateTime.now());
        event.setCreateUser("System");

        if (Objects.nonNull(adaptyEventsDTO.getUserAttributes())) {
            event.setUserId(parseUserId(adaptyEventsDTO.getUserAttributes().getAppUserId()));
            event.setInviteCode(adaptyEventsDTO.getUserAttributes().getInviteCode());
        }

        event.setEventType(adaptyEventsDTO.getEventType());
        event.setProductId(adaptyEventsDTO.getEventProperties().getVendorProductId());
        event.setTransactionId(adaptyEventsDTO.getEventProperties().getTransactionId());
        event.setPurchaseDate(adaptyEventsDTO.getEventProperties().getPurchaseDate());

        event.setProfileId(adaptyEventsDTO.getEventProperties().getProfileId());
        event.setOriginalTransactionId(adaptyEventsDTO.getEventProperties().getOriginalTransactionId());

        try{
            adaptyEventService.save(event);
        } catch (DuplicateKeyException ex) {
            log.warn("[200 adapty callback] Event already exists, ignore it.");
            log.warn(ex.getMessage());
        }
    }

    private Integer parseUserId(String userId) {

        try{
            return Integer.parseInt(userId);
        } catch (Exception e) {
            log.warn(String.format("[200 adapty callback] Parse userId error, userId is : %s", userId));
            return null;
        }
    }

    @Scheduled(fixedDelay = scheduleTime)
    @Override
    public void handleSubscribeEvent() {

        RLock rLock = null;
        try {
            rLock = redissonClient.getLock(adaptyEventHandleKey);
            if (rLock.tryLock(waitLockTime, TimeUnit.MILLISECONDS)) {
                transactionTemplate.executeWithoutResult(status -> syncHandleSubscribeEvent());
            } else {
                log.error("[200 adapty callback] Schedule can't get redis lock, have waited 180 seconds, please check.");
            }
        } catch (Exception ex) {
            log.error("[200 User Award Schedule Error]", ex);
        } finally {
            if (Objects.nonNull(rLock) && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    private void syncHandleSubscribeEvent() {

        try {
            // 1. 获取订阅事件
            List<ProjYogaAdaptyEvent> adaptyEventList = adaptyEventService.listUnHandleEvent();
            if (CollectionUtils.isEmpty(adaptyEventList)) {
                return;
            }

            // 2. 分发奖励
            handleAward4User(adaptyEventList);

            // 3. 更新event状态
            updateAdaptyEvent(adaptyEventList);
        } catch (Exception ex) {
            throw new BizException("[200 User Award Schedule Error]", ex);
        }
    }

    private void handleInvite4User(List<ProjYogaAdaptyEvent> adaptyEventList) {

        List<ProjYogaUserActivity> yogaUserActivityList = adaptyEventList.stream()
                .map(event -> {
                    ProjYogaUserActivity userActivity = new ProjYogaUserActivity();
                    userActivity.setUserId(event.getUserId());
                    userActivity.setInviteCode(event.getInviteCode());
                    userActivity.setProductId(event.getProductId());
                    userActivity.setCreateTime(LocalDateTime.now());
                    userActivity.setCreateUser("System");
                    return userActivity;
        }).collect(Collectors.toList());

        yogaUserActivityService.saveBatch(yogaUserActivityList);
    }

    private void handleAward4User(List<ProjYogaAdaptyEvent> adaptyEventList) throws Exception {

        // 排除非邀请订阅
        adaptyEventList = adaptyEventList.stream().filter(event ->
                Objects.nonNull(event.getUserId()) && StringUtils.isNotBlank(event.getInviteCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adaptyEventList)) {
            return;
        }

        // 排除非试用、非订阅类型
        adaptyEventList = adaptyEventList.stream().filter(event -> {
            AdaptyWebhookEventTypeEnums callbackEventType = EnumUtil.getBy(AdaptyWebhookEventTypeEnums::getName, event.getEventType());
            return Objects.isNull(callbackEventType) || !eventTypes.contains(callbackEventType) ? false : true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adaptyEventList)) {
            return;
        }

        // 排除非指定ProductId
        List<String> inviteSubProductIds = oog200Properties.getInviteSubProductIdList();
        adaptyEventList = adaptyEventList.stream().filter(event -> inviteSubProductIds.contains(event.getProductId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adaptyEventList)) {
            return;
        }

        // 排除已经被邀请过的用户
        adaptyEventList = filterInvitedUser(adaptyEventList);
        if (CollectionUtils.isEmpty(adaptyEventList)) {
            return;
        }

        // 获取符合奖励要求的用户
        Set<String> inviteCodeSet = adaptyEventList.stream().map(ProjYogaAdaptyEvent::getInviteCode).collect(Collectors.toSet());
        List<ProjYogaUser> yogaUserList = yogaUserService.listByInviteCode(inviteCodeSet);
        if (CollectionUtils.isEmpty(yogaUserList)) {
            log.error("[200 adapty callback] Schedule can't get yoga user list, invite code is {}, please check.", inviteCodeSet);
            throw new BizException("[200 adapty callback] Schedule can't get yoga user list.");
        }

        // 排除邀请自己的情况
        Map<Integer, String> userIdAndInviteCodeMap = yogaUserList.stream().collect(Collectors.toMap(ProjYogaUser::getId, ProjYogaUser::getInviteCode));
        adaptyEventList = adaptyEventList.stream().filter(event -> !Objects.equals(event.getInviteCode(), userIdAndInviteCodeMap.get(event.getUserId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adaptyEventList)) {
            return;
        }

        // 记录符合要求的邀请事件
        handleInvite4User(adaptyEventList);

        // 排除已经获取奖励的用户
        inviteCodeSet = adaptyEventList.stream().map(ProjYogaAdaptyEvent::getInviteCode).collect(Collectors.toSet());
        List<ProjYogaUserAwardVO> yogaAwardList = yogaUserAwardService.listByInviteCode(inviteCodeSet);
        if (CollectionUtils.isNotEmpty(yogaAwardList)) {
            Set<String> awardedInviteCodeSet = yogaAwardList.stream().map(ProjYogaUserAwardVO::getInviteCode).collect(Collectors.toSet());
            yogaUserList = yogaUserList.stream().filter(e -> !awardedInviteCodeSet.contains(e.getInviteCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(yogaUserList)) {
            return;
        }

        // 获取项目组中Product信息，用于订阅续订，如若用户没有订阅组中Product，则使用默认Product
        Integer groupId = oog200Properties.getAppleSubGroupId();
        String token = generateToken4Apple();
        AppleSubGroupVO appleSubGroupVO = appleService.listSubGroup(groupId, token);
        if (Objects.isNull(appleSubGroupVO) || CollectionUtils.isEmpty(appleSubGroupVO.getData())) {
            throw new BizException(String.format("[200 adapty callback] Schedule can't get sub group list from apple, group id is %s, token is %s, please check.", groupId, token));
        }

        // 匹配用户Product
        Set<String> validProductIds = appleSubGroupVO.getData().stream()
                .map(sub -> Objects.nonNull(sub.getAttributes()) ? sub.getAttributes().getProductId() : null)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<ProjYogaUserAwardVO> awardVOList = matchProduct4Users(yogaUserList, validProductIds);

        // 根据用户Product 匹配对应的奖品
        List<ProjYogaUserAward> yogaUserAwardList = Lists.newArrayList();
        LocalDateTime expireTime = LocalDateTime.now().plusDays(15);
        matchAward4Users(awardVOList, yogaUserAwardList, expireTime);

        // 保存用户数据、更新奖品状态
        yogaUserAwardService.saveBatch(yogaUserAwardList);
        List<Integer> usedAwardIds = yogaUserAwardList.stream().map(ProjYogaUserAward::getAwardId).collect(Collectors.toList());
        yogaAwardService.setUsed(usedAwardIds);
    }

    private List<ProjYogaAdaptyEvent> filterInvitedUser(List<ProjYogaAdaptyEvent> adaptyEventList) {

        Set<Integer> userIds = new HashSet<>();
        adaptyEventList = adaptyEventList.stream().filter(event -> {
            if (userIds.contains(event.getUserId())) {
                return false;
            } else {
                userIds.add(event.getUserId());
                return true;
            }
        }).collect(Collectors.toList());

        List<ProjYogaUserActivity> userActivityList = yogaUserActivityService.listByUserIds(userIds);
        if (CollectionUtils.isNotEmpty(userActivityList)) {
            Set<Integer> invitedUserIds = userActivityList.stream().map(ProjYogaUserActivity::getUserId).collect(Collectors.toSet());
            adaptyEventList = adaptyEventList.stream().filter(event -> !invitedUserIds.contains(event.getUserId())).collect(Collectors.toList());
        }

        return adaptyEventList;
    }


    private void matchAward4Users(List<ProjYogaUserAwardVO> awardVOList, List<ProjYogaUserAward> yogaUserAwardList,
                                  LocalDateTime expireTime) {

        Map<String, List<ProjYogaUserAwardVO>> productAndAwardMap = awardVOList.stream().collect(Collectors.groupingBy(ProjYogaUserAwardVO::getProductId));
        productAndAwardMap.entrySet().stream().forEach(entry -> {

            List<ProjYogaAward> yogaAwards = yogaAwardService.listUnusedAward(entry.getKey(), entry.getValue().size(), expireTime);
            if (CollectionUtils.isEmpty(yogaAwards) || yogaAwards.size() != entry.getValue().size()) {
                log.error("[200 adapty callback] Schedule can't get enough award, product id is {}, please check.", entry.getKey());
                throw new BizException("[200 adapty callback] Schedule can't get enough award.");
            }

            for (int i = 0; i < entry.getValue().size(); i++) {
                ProjYogaUserAwardVO vo = entry.getValue().get(i);
                ProjYogaAward award = yogaAwards.get(i);
                ProjYogaUserAward userAward = new ProjYogaUserAward(vo.getUserId(), award.getId());
                userAward.setCreateTime(LocalDateTime.now());
                userAward.setCreateUser("System");
                yogaUserAwardList.add(userAward);
            }
        });
    }

    private List<ProjYogaUserAwardVO> matchProduct4Users(List<ProjYogaUser> yogaUserList, Set<String> validProductIds) {

        return yogaUserList.stream().map(yogaUser -> {
            String productId = getProductId4User(yogaUser, validProductIds);
            ProjYogaUserAwardVO userAward = new ProjYogaUserAwardVO();
            userAward.setUserId(yogaUser.getId());
            userAward.setProductId(productId);
            return userAward;
        }).collect(Collectors.toList());
    }

    private String getProductId4User(ProjYogaUser yogaUser, Set<String> validProductIds) {

        String token = "Api-Key " + oog200Properties.getAdaptyKey();
        AdaptyProfileVO profileVO = adaptyService.getProfile(yogaUser.getAdaptyId(), token);
        if (Objects.isNull(profileVO) || Objects.isNull(profileVO.getData())) {
            log.error("[200 adapty callback] Schedule can't get profile info, profile id is {}, please check.", yogaUser.getAdaptyId());
            throw new BizException("[200 adapty callback] Schedule can't get profile info.");
        }

        // 给予默认的product
        if (CollectionUtils.isEmpty(profileVO.getData().getSubscriptions())) {
            return oog200Properties.getAppleDefaultProductId();
        }

        // 给予符合条件的product
        LocalDateTime now = LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC);;
        Optional<AdaptyProfileVO.AdaptyProfileSub> subOptional = profileVO.getData().getSubscriptions().stream()
                .filter(sub -> Objects.isNull(sub.getExpires_at()) ? true : sub.getExpires_at().toLocalDateTime().isAfter(now))
                .findFirst();

        if (subOptional.isPresent() && validProductIds.contains(subOptional.get().getStore_product_id())) {
            return subOptional.get().getStore_product_id();
        } else {
            return oog200Properties.getAppleDefaultProductId();
        }
    }

    private void updateAdaptyEvent(List<ProjYogaAdaptyEvent> adaptyEventList) {

        Set<Integer> eventIds = adaptyEventList.stream().map(ProjYogaAdaptyEvent::getId).collect(Collectors.toSet());
        LambdaUpdateWrapper<ProjYogaAdaptyEvent> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjYogaAdaptyEvent::getId, eventIds);

        updateWrapper.set(ProjYogaAdaptyEvent::getHandleFlag, GlobalConstant.ONE);
        updateWrapper.set(ProjYogaAdaptyEvent::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(ProjYogaAdaptyEvent::getUpdateUser, "System");
        adaptyEventService.update(updateWrapper);
    }

    private String generateToken4Apple() throws Exception {

        // Parse the PKCS8 private key
        if (StringUtils.isBlank(oog200Properties.getAppleKey())) {
            log.error("[200 generateJWT] apple key is null, please check.");
        }

        PrivateKey privateKey = parsePrivateKey(oog200Properties.getAppleKey());
        long currentTimeMillis = System.currentTimeMillis();
        long expirationTime = currentTimeMillis + (60 * 10) * 1000; // 20 minutes

        // Build and sign the JWT
        String jwt = Jwts.builder()
                .setHeaderParam("alg", "ES256")
                .setHeaderParam("kid", "B747X735D3")
                .setHeaderParam("typ", "JWT")
                .setIssuedAt(new Date(currentTimeMillis))
                .setIssuer("69a6de79-0e79-47e3-e053-5b8c7c11a4d1")
                .setAudience("appstoreconnect-v1")
                .setExpiration(new Date(expirationTime))
                .signWith(privateKey, SignatureAlgorithm.ES256)
                .compact();

        return "Bearer " + jwt;
    }

    private PrivateKey parsePrivateKey(String pkcs8Key) throws Exception {

        // Remove PEM headers and decode base64
        String privateKeyContent = pkcs8Key
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);

        // Create EC private key
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        return keyFactory.generatePrivate(keySpec);
    }

}
