package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjTemplatePub;

import java.util.List;

/**
 * <p>
 * template Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-26
 */
public interface ProjTemplateMapper extends BaseMapper<ProjTemplatePub> {

    /**
     * 查询启用的template 列表
     *
     * @param projId projId
     * @param version version
     * @param duration duration
     * @return list
     */
    List<Integer> selectTemplateIds(Integer projId, Integer version, Integer duration);

    /**
     * 根据时长区间查询启用的template 列表
     *
     * @param projId projId
     * @param version version
     * @param beginDuration beginDuration
     * @param endDuration endDuration
     * @return list
     */
    List<Integer> selectTemplateIdsByDuration(Integer projId, Integer version, Integer beginDuration, Integer endDuration);

}
