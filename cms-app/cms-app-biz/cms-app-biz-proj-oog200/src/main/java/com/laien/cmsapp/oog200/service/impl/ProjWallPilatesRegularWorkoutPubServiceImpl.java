package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.WorkoutCategoryRelationBO;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesRegularWorkoutPub;
import com.laien.cmsapp.oog200.mapper.ProjWallPilatesRegularWorkoutPubMapper;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import com.laien.cmsapp.oog200.service.IProjWallPilatesRegularWorkoutPubService;
import com.laien.cmsapp.oog200.service.IProjWallPilatesVideoService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularCategoryService;
import com.laien.cmsapp.oog200.util.YogaRegularWorkoutUtil;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.TimeConvertUtil;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.SpecialLimitEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.common.oog200.enums.YogaTypeEnum;
import com.laien.common.util.MyStringUtil;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * oog200 workout 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
public class ProjWallPilatesRegularWorkoutPubServiceImpl extends ServiceImpl<ProjWallPilatesRegularWorkoutPubMapper, ProjWallPilatesRegularWorkoutPub> implements IProjWallPilatesRegularWorkoutPubService {

    @Resource
    private IProjWallPilatesVideoService projWallPilatesVideoService;

    @Resource
    private I18nUtil i18nUtil;

    @Resource
    private IProjYogaRegularCategoryService projYogaRegularCategoryService;

    @Override
    public List<ProjYogaRegularWorkoutListVO> queryList(String lang, ProjPublishCurrentVersionInfoBO versionInfo, Set<Integer> categoryCodeSet) {
        List<WorkoutCategoryRelationBO> workoutCategoryRelationList = projYogaRegularCategoryService.findWorkoutCategoryRelationList(categoryCodeSet, YogaAutoWorkoutTemplateEnum.WALL_PILATES, versionInfo);
        if (CollUtil.isEmpty(workoutCategoryRelationList) && CollUtil.isNotEmpty(categoryCodeSet)) {
            log.error("list wall pilates regular workout not found");
            return new ArrayList<>();
        }
        Map<Integer, WorkoutCategoryRelationBO> workoutCategoryRelationMap = workoutCategoryRelationList.stream().collect(Collectors.toMap(WorkoutCategoryRelationBO::getWorkoutId, o-> o));
        LambdaQueryWrapper<ProjWallPilatesRegularWorkoutPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(categoryCodeSet), BaseModel::getId, workoutCategoryRelationMap.keySet())
                .eq(ProjWallPilatesRegularWorkoutPub::getVersion, versionInfo.getCurrentVersion())
                .eq(ProjWallPilatesRegularWorkoutPub::getProjId, versionInfo.getProjId())
                .eq(ProjWallPilatesRegularWorkoutPub::getStatus, GlobalConstant.STATUS_ENABLE);
        List<ProjWallPilatesRegularWorkoutPub> wallPilatesList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(wallPilatesList)) {
            return new ArrayList<>();
        }
        List<ProjYogaRegularWorkoutListVO> workoutVOList = new ArrayList<>(wallPilatesList.size());
        Map<String, SpecialLimitEnum> specialLimitMap = Arrays.stream(SpecialLimitEnum.values()).collect(Collectors.toMap(
                SpecialLimitEnum::getName,
                specialLimit -> specialLimit,
                (existing, replacement) -> existing
        ));
        Map<String, YogaTypeEnum> yogaTypeMap = Arrays.stream(YogaTypeEnum.values())
                .collect(Collectors.toMap(
                        YogaTypeEnum::getName,
                        yogaTypeEnum -> yogaTypeEnum,
                        (existing, replacement) -> existing
                ));

        for (ProjWallPilatesRegularWorkoutPub workout : wallPilatesList) {
            ProjYogaRegularWorkoutListVO workoutVO = new ProjYogaRegularWorkoutListVO();
            BeanUtils.copyProperties(workout, workoutVO);
            List<Integer> yogaTypeCodeList = new ArrayList<>();
            for (String yogaType : MyStringUtil.getSplitWithComa(workout.getYogaType())) {
                YogaTypeEnum yogaTypeEnum = yogaTypeMap.getOrDefault(yogaType, null);
                if (null != yogaTypeEnum) {
                    yogaTypeCodeList.add(yogaTypeEnum.getCode());
                }
            }

            workoutVO.setYogaTypeCodeList(yogaTypeCodeList);
            List<Integer> specialLimitCodeList = new ArrayList<>();
            for (String specialLimit : MyStringUtil.getSplitWithComa(workout.getSpecialLimit())) {
                SpecialLimitEnum specialLimitEnum = specialLimitMap.getOrDefault(specialLimit, null);
                if (null != specialLimitEnum) {
                    specialLimitCodeList.add(specialLimitEnum.getCode());
                }
            }

            String difficulty = workout.getDifficulty();
            DifficultyEnum difficultyEnum = DifficultyEnum.getByName(difficulty);
            if(null != difficultyEnum){
                workoutVO.setDifficultyCode(difficultyEnum.getCode());
            }

            WorkoutCategoryRelationBO workoutCategoryRelation = workoutCategoryRelationMap.get(workout.getId());
            if(null != workoutCategoryRelation) {
                Set<Integer> codeSet = workoutCategoryRelation.getCategoryCodeSet();
                if (CollUtil.isNotEmpty(codeSet)) {
                    workoutVO.setCategoryCodeList(new ArrayList<>(codeSet));
                }
            }

            workoutVO.setSpecialLimitCodeList(specialLimitCodeList);
            //当前类只处理wall pilates的，所以写死
            workoutVO.setWorkoutTypeCode(workout.getVideoType().getCode());
            workoutVO.setDuration(TimeConvertUtil.millisToMinutes(workout.getDuration()));
            workoutVOList.add(workoutVO);
        }

        i18nUtil.translate(workoutVOList, ProjCodeEnums.OOG200, lang);
        return workoutVOList;
    }

    @Override
    public List<ProjYogaRegularWorkoutDetailVO> list(Set<Integer> idSet, String lang, Integer m3u8Type, Integer status, ProjPublishCurrentVersionInfoBO versionInfo) {
        if (CollectionUtils.isEmpty(idSet)) {
            return Collections.emptyList();
        }
        // 获取指定的workout list
        LambdaQueryWrapper<ProjWallPilatesRegularWorkoutPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjWallPilatesRegularWorkoutPub::getVersion, versionInfo.getCurrentVersion())
                .eq(null != status, ProjWallPilatesRegularWorkoutPub::getStatus, status)
                .in(ProjWallPilatesRegularWorkoutPub::getId, idSet);
        List<ProjWallPilatesRegularWorkoutPub> wallPilatesList = baseMapper.selectList(wrapper);
        if(CollUtil.isEmpty(wallPilatesList)){
            return new ArrayList<>();
        }

        Map<Integer, List<ResYogaVideoDetailVO>> videoDetailI18nVO = getVideoDetailI18nVO(idSet, lang);
        List<ProjYogaRegularWorkoutDetailVO> detailList = new ArrayList<>();
        for (ProjWallPilatesRegularWorkoutPub workout : wallPilatesList) {
            detailList.add(toPlanWorkoutDetailVO(workout, videoDetailI18nVO, m3u8Type));
        }

        i18nUtil.translate(detailList, ProjCodeEnums.OOG200, lang);
        return detailList;
    }

    private static ProjYogaRegularWorkoutDetailVO toPlanWorkoutDetailVO(ProjWallPilatesRegularWorkoutPub source, Map<Integer, List<ResYogaVideoDetailVO>> videoMap, Integer m3u8Type) {
        ProjYogaRegularWorkoutDetailVO target = new ProjYogaRegularWorkoutDetailVO();
        BeanUtils.copyProperties(source, target);
        target.setWorkoutTypeCode(source.getVideoType().getCode());
        DifficultyEnum difficultyEnum = DifficultyEnum.getByName(target.getDifficulty());
        if (null != difficultyEnum) {
            target.setDifficultyCode(difficultyEnum.getCode());
        }
        target.setDuration(TimeConvertUtil.millisToMinutes(source.getDuration()));
        target.setVideos(videoMap.get(source.getId()));

        //替换m3u8地址
        YogaRegularWorkoutUtil.setVideoUrl(target, m3u8Type);
        return target;
    }

    private Map<Integer, List<ResYogaVideoDetailVO>> getVideoDetailI18nVO(Set<Integer> workoutIdSet, String language){
        List<ResYogaVideoDetailVO> videoList = projWallPilatesVideoService.queryByWallPilatesRegularWorkoutIdSet(workoutIdSet);
        return videoList.stream().collect(Collectors.groupingBy(ResYogaVideoDetailVO::getWorkoutId));
    }

}
