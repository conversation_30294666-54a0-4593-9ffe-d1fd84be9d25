package com.laien.cmsapp.oog200.requst;


import com.laien.common.oog200.enums.GenderEnums;
import com.laien.common.oog200.enums.SoundSubTypeEnums;
import com.laien.common.oog200.enums.SoundTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "声音请求参数", description = "声音请求参数")
public class ProjSoundReq {

    @ApiModelProperty(value = "声音类型（关联字典表）")
    private SoundTypeEnums soundType;

    @ApiModelProperty(value = "声音类型 子类型")
    private SoundSubTypeEnums soundSubType;

    @ApiModelProperty(value = "声音源(female|male 默认female")
    private GenderEnums gender = GenderEnums.FEMALE;
}
