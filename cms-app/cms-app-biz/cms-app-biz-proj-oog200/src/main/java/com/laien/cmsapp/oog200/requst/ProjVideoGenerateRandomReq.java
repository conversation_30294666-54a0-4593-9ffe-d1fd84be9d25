package com.laien.cmsapp.oog200.requst;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * note: video random
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "video random", description = "video random")
public class ProjVideoGenerateRandomReq {

    @ApiModelProperty(value = "difficulty")
    private String difficulty;

    @ApiModelProperty(value = "duration", required = true)
    private Integer duration;

    @ApiModelProperty(value = "Default | Least 默认Default")
    private String introType;

}
