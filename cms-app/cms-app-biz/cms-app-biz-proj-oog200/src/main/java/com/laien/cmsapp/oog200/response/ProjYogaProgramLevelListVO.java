package com.laien.cmsapp.oog200.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Author:  hhl
 * Date:  2024/12/24 14:18
 */
@Data
public class ProjYogaProgramLevelListVO {

    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "流程名称")
    private String eventName;

    @ApiModelProperty(value = "每个level下的workout列表")
    private List<ProjYogaRegularWorkoutListVO> programWorkoutList;
}
