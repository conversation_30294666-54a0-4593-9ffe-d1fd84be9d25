package com.laien.cmsapp.oog200.response;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * yoga名言警句
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@Accessors(chain = true)
@TableName("proj_yoga_quote_pub")
@ApiModel(value="ProjYogaQuotePub对象", description="yoga名言警句")
public class ProjYogaQuoteVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "名言警句内容")
    private String content;

}
