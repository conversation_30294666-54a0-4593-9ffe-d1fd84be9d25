package com.laien.cmsapp.oog200.response;

import com.laien.cmsapp.annotation.AbsoluteR2Url;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * Author:  hhl
 * Date:  2025/1/7 11:12
 */
@Data
@ApiModel(value="ProjDish对象", description="Dish")
public class ProjDishDetailVO {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "动作展示名称")
    private String name;

    @ApiModelProperty(value = "event名称")
    private String eventName;

    @ApiModelProperty(value = "封面图")
    @AbsoluteR2Url
    private String coverImgUrl;

    @ApiModelProperty(value = "详情图")
    @AbsoluteR2Url
    private String detailImgUrl;

    @ApiModelProperty(value = "准备时间，单位分钟")
    private Integer prepareTime;

    @ApiModelProperty(value = "卡路里")
    private BigDecimal calorie;

    @ApiModelProperty(value = "碳水含量")
    private BigDecimal carb;

    @ApiModelProperty(value = "蛋白质含量")
    private BigDecimal protein;

    @ApiModelProperty(value = "脂肪含量")
    private BigDecimal fat;

    @ApiModelProperty(value = "份数")
    private Integer serving;

    @ApiModelProperty(value = "dishStepList，操作步骤")
    private List<ProjDishStepVO> dishStepList = Collections.emptyList();

    @ApiModelProperty(value = "ingredient, 配料表")
    private List<ProjIngredientVO> ingredientList = Collections.emptyList();

    @ApiModelProperty(value = "过敏原")
    private List<String> allergenList = Collections.emptyList();

    @ApiModelProperty(value = "m3u8地址")
    @AbsoluteR2Url
    private String videoM3u8Url;

    @ApiModelProperty(value = "视频时长")
    private Integer duration;

}
