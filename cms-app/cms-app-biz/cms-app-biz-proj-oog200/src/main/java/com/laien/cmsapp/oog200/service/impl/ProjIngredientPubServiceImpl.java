package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.entity.ProjIngredientPub;
import com.laien.cmsapp.oog200.entity.ProjUnitPub;
import com.laien.cmsapp.oog200.mapper.ProjIngredientPubMapper;
import com.laien.cmsapp.oog200.mapper.ProjUnitPubMapper;
import com.laien.cmsapp.oog200.response.ProjIngredientVO;
import com.laien.cmsapp.oog200.service.IProjIngredientPubService;
import com.laien.cmsapp.oog200.service.IProjUnitPubService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.common.domain.enums.ProjCodeEnums;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2025/1/7 14:42
 */
@Service
public class ProjIngredientPubServiceImpl extends ServiceImpl<ProjIngredientPubMapper, ProjIngredientPub> implements IProjIngredientPubService {

    @Resource
    private ProjUnitPubMapper unitPubMapper;

    @Resource
    private IProjUnitPubService unitPubService;

    @Resource
    private I18nUtil i18nUtil;

    @Override
    public List<ProjIngredientVO> listByDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId, String lang) {

        List<ProjIngredientPub> ingredientPubList = listByVersionAndDishId(versionInfoBO, dishId);
        if (CollectionUtils.isEmpty(ingredientPubList)) {
            return Collections.emptyList();
        }

        i18nUtil.translate(ingredientPubList, ProjCodeEnums.OOG200, lang);
        List<ProjIngredientVO> ingredientVOList = ingredientPubList.stream().map(ingredient -> convert2VO(ingredient)).collect(Collectors.toList());
        List<Integer> unitIds = ingredientPubList.stream().map(ProjIngredientPub::getProjUnitId).collect(Collectors.toList());
        List<ProjUnitPub> unitPubList = unitPubService.listByVersionAndIds(versionInfoBO, unitIds);
        if (CollectionUtils.isEmpty(unitPubList)) {
            return ingredientVOList;
        }

        // set unit name
        Map<Integer, String> unitMap = unitPubList.stream().collect(Collectors.toMap(unit -> unit.getId(), unit -> unit.getName(), (k1, k2) -> k1));
        ingredientVOList.forEach(ingredient -> ingredient.setUnitName(unitMap.get(ingredient.getProjUnitId())));
        return ingredientVOList;
    }

    private ProjIngredientVO convert2VO(ProjIngredientPub projIngredientPub) {

        ProjIngredientVO projIngredientVO = new ProjIngredientVO();
        BeanUtils.copyProperties(projIngredientPub, projIngredientVO);
        return projIngredientVO;
    }

    private List<ProjIngredientPub> listByVersionAndDishId(ProjPublishCurrentVersionInfoBO versionInfoBO, Integer dishId) {

        LambdaQueryWrapper<ProjIngredientPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjIngredientPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjIngredientPub::getProjId, versionInfoBO.getProjId());
        queryWrapper.eq(ProjIngredientPub::getProjDishId, dishId);
        return list(queryWrapper);
    }
}
