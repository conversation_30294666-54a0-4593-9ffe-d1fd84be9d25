package com.laien.cmsapp.oog200.controller;


import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.requst.ProjRegularWorkoutListReq;
import com.laien.cmsapp.oog200.requst.ProjRegularWorkoutReq;
import com.laien.cmsapp.oog200.requst.ProjYogaRegularWorkoutListReq;
import com.laien.cmsapp.oog200.response.ProjWorkoutDetailVO116;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutDetailVO;
import com.laien.cmsapp.oog200.response.ProjYogaRegularWorkoutListVO;
import com.laien.cmsapp.oog200.service.IProjChairYogaRegularWorkoutPubService;
import com.laien.cmsapp.oog200.service.IProjWallPilatesRegularWorkoutPubService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularWorkoutAudioI18nPubService;
import com.laien.cmsapp.oog200.service.IProjYogaRegularWorkoutPubService;
import com.laien.cmsapp.oog200.util.YogaRegularWorkoutUtil;
import com.laien.cmsapp.requst.LangReq;
import com.laien.cmsapp.service.IProjPublishCurrentVersionService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.controller.ResponseController;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.common.oog200.enums.YogaDataSourceEnum;
import com.laien.common.response.setting.ResponseResult;
import com.laien.common.util.RequestContextUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * oog200 workout 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Api(tags = "app端：yogaRegularWorkout")
@RestController
@RequestMapping("/{appCode}/yogaRegularWorkout")
public class ProjYogaRegularWorkoutPubController extends ResponseController {

    @Resource
    private IProjYogaRegularWorkoutPubService projYogaRegularWorkoutPubService;
    @Resource
    private IProjWallPilatesRegularWorkoutPubService projWallPilatesRegularWorkoutPubService;
    @Resource
    private IProjChairYogaRegularWorkoutPubService projChairYogaRegularWorkoutPubService;
    @Resource
    private IProjYogaRegularWorkoutAudioI18nPubService workoutAudioI18nPubService;
    @Resource
    private IProjPublishCurrentVersionService projPublishCurrentVersionService;

    private static final String OOG200_CODE = "oog200";

    @ApiOperation(value = "regular workout列表v1", tags = {"oog200"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjYogaRegularWorkoutListVO>> listV1(LangReq req, ProjYogaRegularWorkoutListReq workoutListReq) {
        List<ProjYogaRegularWorkoutListVO> workoutList = projYogaRegularWorkoutPubService.list(workoutListReq);
        return succ(workoutList);
    }

    @ApiOperation(value = "regular workout detail v1", tags ={"oog200"})
    @GetMapping("/v1/detail/{id}")
    public ResponseResult<ProjYogaRegularWorkoutDetailVO> detailV1(@PathVariable Integer id,
                                                                   @RequestParam(required = false) String lang,
                                                                   @RequestParam(required = false, defaultValue = "1") Integer m3u8Type) {

        ProjYogaRegularWorkoutDetailVO detailVO = projYogaRegularWorkoutPubService.findDetailById(id, m3u8Type);
        return succ(detailVO);
    }

    @ApiOperation(value = "regular workout 详情列表 v1", notes = "通过Id List获取指定的regular workout，只返回状态为已启用的workout", tags ={"oog200"})
    @GetMapping(value = "/v1/listByIds")
    public ResponseResult<List<ProjYogaRegularWorkoutDetailVO>> listByIds(@RequestParam Set<Integer> workoutIds,
                                                                          @RequestParam(required = false) String lang,
                                                                          @RequestParam(required = false, defaultValue = "1") Integer m3u8Type) {

        List<ProjYogaRegularWorkoutDetailVO> resultList = projYogaRegularWorkoutPubService.listByIdsAndStatus(workoutIds, lang, GlobalConstant.STATUS_ENABLE, m3u8Type);
        return succ(resultList);
    }

    @ApiOperation(value = "regular workout列表v2", tags = {"oog200"})
    @GetMapping("/v2/list")
    public ResponseResult<List<ProjYogaRegularWorkoutListVO>> listV2(@RequestParam(required = false, defaultValue = "1") Integer m3u8Type,
                                                                     @RequestParam(required = false, defaultValue = "1") Integer difficultyCode) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjYogaRegularWorkoutListVO> workoutListVOList = Lists.newArrayList();
        Set<Integer> categoryCodeSet = new HashSet<>();
        categoryCodeSet.add(GlobalConstant.TOP_PICKS_CATEGORY_CODE);
        List<ProjYogaRegularWorkoutListVO> classicYogaWorkouts = projYogaRegularWorkoutPubService.listYogaRegularWorkoutV2(categoryCodeSet);
        if (!CollectionUtils.isEmpty(classicYogaWorkouts)) {
            workoutListVOList.addAll(classicYogaWorkouts);
        }

        String language = RequestContextUtils.getLanguage();
        List<ProjYogaRegularWorkoutListVO> wallPilatesWorkoutList = projWallPilatesRegularWorkoutPubService.queryList(language, versionInfoBO, categoryCodeSet);
        if (!CollectionUtils.isEmpty(wallPilatesWorkoutList)) {
            workoutListVOList.addAll(wallPilatesWorkoutList);
        }

        List<ProjYogaRegularWorkoutListVO> chairYogaWorkouts = projChairYogaRegularWorkoutPubService.listYogaRegularWorkout(versionInfoBO, language, categoryCodeSet);
        if (!CollectionUtils.isEmpty(chairYogaWorkouts)) {
            workoutListVOList.addAll(chairYogaWorkouts);
        }

        List<ProjYogaRegularWorkoutListVO> sortedWorkoutList = sortByDifficultAndNew(workoutListVOList, difficultyCode);
        YogaRegularWorkoutUtil.setMultiVideoUrl(sortedWorkoutList);
        return succ(YogaRegularWorkoutUtil.filterByDataSource(YogaDataSourceEnum.TOP_PICKS, sortedWorkoutList));

    }

    private List<ProjYogaRegularWorkoutListVO> sortByDifficultAndNew(List<ProjYogaRegularWorkoutListVO> workoutDetailVOList, Integer difficultyCode) {

        if (CollectionUtils.isEmpty(workoutDetailVOList)) {
            Collections.emptyList();
        }

        List<ProjYogaRegularWorkoutListVO> matchNewTimeAndDifficultyList = Lists.newArrayList();
        List<ProjYogaRegularWorkoutListVO> onlymatchNewTimeList = Lists.newArrayList();
        List<ProjYogaRegularWorkoutListVO> unmatchNewTimeList = Lists.newArrayList();
        List<ProjYogaRegularWorkoutListVO> nullNewTimeList = Lists.newArrayList();

        LocalDateTime currentTime = LocalDateTime.now();
        workoutDetailVOList.forEach(workout -> {
            if (Objects.isNull(workout.getNewStartTime()) || Objects.isNull(workout.getNewEndTime())) {
                nullNewTimeList.add(workout);
            } else {
                if (currentTime.isAfter(workout.getNewStartTime()) && currentTime.isBefore(workout.getNewEndTime())) {
                    if (Objects.equals(workout.getDifficultyCode(), difficultyCode)) {
                        matchNewTimeAndDifficultyList.add(workout);
                    } else {
                        onlymatchNewTimeList.add(workout);
                    }
                } else {
                    unmatchNewTimeList.add(workout);
                }
            }
        });

        Collections.sort(matchNewTimeAndDifficultyList, Comparator.comparing(ProjYogaRegularWorkoutListVO::getNewStartTime).reversed());
        Collections.sort(onlymatchNewTimeList, Comparator.comparing(ProjYogaRegularWorkoutListVO::getNewStartTime).reversed());
        Collections.sort(unmatchNewTimeList, Comparator.comparing(ProjYogaRegularWorkoutListVO::getNewStartTime).reversed());

        matchNewTimeAndDifficultyList.addAll(onlymatchNewTimeList);
        matchNewTimeAndDifficultyList.addAll(unmatchNewTimeList);
        matchNewTimeAndDifficultyList.addAll(nullNewTimeList);
        return matchNewTimeAndDifficultyList;
    }

    @ApiOperation(value = "regular workout detail v2", tags ={"oog200"})
    @GetMapping("/v2/detail/{id}")
    public ResponseResult<ProjYogaRegularWorkoutDetailVO> detailV2(@PathVariable Integer id,
                                                                   @RequestParam(required = false, defaultValue = "0") Integer workoutTypeCode,
                                                                   @RequestParam(required = false, defaultValue = "1") Integer m3u8Type) {
        String language = RequestContextUtils.getLanguage();
        ProjYogaRegularWorkoutDetailVO workoutDetailVO = null;
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        if (Objects.equals(workoutTypeCode, YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode())) {
            workoutDetailVO = projYogaRegularWorkoutPubService.findDetailByIdV2(id, m3u8Type);
        } else if (Objects.equals(workoutTypeCode, YogaAutoWorkoutTemplateEnum.WALL_PILATES.getCode())) {
            Set<Integer> idSet = new HashSet<>();
            idSet.add(id);
            List<ProjYogaRegularWorkoutDetailVO> detailList = projWallPilatesRegularWorkoutPubService.list(idSet, language, m3u8Type, null, versionInfoBO);
            if (!CollUtil.isEmpty(detailList)) {
                workoutDetailVO = detailList.get(GlobalConstant.ZERO);
            }
        } else if (Objects.equals(workoutTypeCode, YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getCode())) {
            workoutDetailVO = projChairYogaRegularWorkoutPubService.findDetailById(versionInfoBO, id, m3u8Type, language);
        }

        if (Objects.nonNull(workoutDetailVO)) {
            workoutDetailVO.setWorkoutTypeCode(workoutTypeCode);
            workoutAudioI18nPubService.setAudioI18n4Workout(Lists.newArrayList(workoutDetailVO), versionInfoBO);
        }
        return succ(workoutDetailVO);
    }


    @ApiOperation(value = "regular workout 详情列表 v2", notes = "通过Id List获取指定的regular workout，只返回状态为已启用的workout", tags ={"oog200"})
    @GetMapping(value = "/v2/listByIds")
    public ResponseResult<List<ProjYogaRegularWorkoutDetailVO>> listByIdAndWorkoutType(ProjRegularWorkoutListReq listReq) {

        if (CollectionUtils.isEmpty(listReq.getReqs())) {
            return succ(Collections.emptyList());
        }

        String lang = listReq.getLang();
        Integer m3u8Type = listReq.getM3u8Type();
        List<ProjYogaRegularWorkoutDetailVO> workoutDetailVOList = Lists.newArrayList();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();

        listReq.getReqs().forEach(req -> {
            if (Objects.isNull(req.getCode())) {
                req.setCode(YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode());
            }
        });

        Map<Integer, Set<Integer>> workoutTypeAndIdMap = listReq.getReqs().stream().collect(Collectors.groupingBy(ProjRegularWorkoutReq::getCode, Collectors.mapping(ProjRegularWorkoutReq::getId, Collectors.toSet())));
        workoutTypeAndIdMap.entrySet().forEach(entry -> {
            if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.CLASSIC_YOGA.getCode())) {
                workoutDetailVOList.addAll(projYogaRegularWorkoutPubService.listByIdsAndStatusV2(entry.getValue(), lang, GlobalConstant.STATUS_ENABLE, m3u8Type));
            } else if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.WALL_PILATES.getCode())) {
                workoutDetailVOList.addAll(projWallPilatesRegularWorkoutPubService.list(entry.getValue(), lang, m3u8Type, GlobalConstant.STATUS_ENABLE, versionInfoBO));
            } else if (Objects.equals(entry.getKey(), YogaAutoWorkoutTemplateEnum.CHAIR_YOGA.getCode())) {
                workoutDetailVOList.addAll(projChairYogaRegularWorkoutPubService.listByIdsAndStatus(entry.getValue(), lang, GlobalConstant.STATUS_ENABLE, m3u8Type, versionInfoBO));
            }
        });

        workoutAudioI18nPubService.setAudioI18n4Workout(workoutDetailVOList, versionInfoBO);
        return succ(workoutDetailVOList);
    }

    @ApiOperation(value = "获取指定配置的chair yoga workout，并随之返回配置的playlistId", notes = "提供给oog116项目临时使用", tags ={"oog200"})
    @GetMapping(value = "/v1/chairYoga/list")
    public ResponseResult<List<ProjWorkoutDetailVO116>> listChairWorkout(@RequestParam(required = false, defaultValue = "1") Integer m3u8Type,
                                                                         @RequestParam(required = false, defaultValue = GlobalConstant.DEFAULT_LANGUAGE) String lang) {

        ProjPublishCurrentVersionInfoBO versionInfoBO = projPublishCurrentVersionService.getPublishCurrentVersion(OOG200_CODE);
        List<ProjWorkoutDetailVO116> workoutFor116List = projChairYogaRegularWorkoutPubService.listWorkoutFor116(lang, m3u8Type, versionInfoBO);
        return succ(workoutFor116List);
    }


    @ApiOperation(value = "获取指定dataSource为regular workout，category为topPicks的workout", tags ={"oog200"})
    @GetMapping(value = "/v1/dataSource/list")
    public ResponseResult<List<ProjYogaRegularWorkoutListVO>> listByDataSourceCode() {

        List<ProjYogaRegularWorkoutListVO> workoutList = new ArrayList<>();
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();

        List<ProjYogaRegularWorkoutListVO> classicYogaWorkouts = projYogaRegularWorkoutPubService.listYogaRegularWorkoutV2(null);

        String language = RequestContextUtils.getLanguage();
        List<ProjYogaRegularWorkoutListVO> wallPilatesWorkoutList = projWallPilatesRegularWorkoutPubService.queryList(language, versionInfoBO, null);

        List<ProjYogaRegularWorkoutListVO> chairYogaWorkouts = projChairYogaRegularWorkoutPubService.listYogaRegularWorkout(versionInfoBO, language, null);

        CollUtil.addAll(workoutList, classicYogaWorkouts);
        CollUtil.addAll(workoutList, wallPilatesWorkoutList);
        CollUtil.addAll(workoutList, chairYogaWorkouts);
        List<ProjYogaRegularWorkoutListVO> filterWorkoutList = YogaRegularWorkoutUtil.filterByDataSource(YogaDataSourceEnum.TOP_PICKS, workoutList);
        List<ProjYogaRegularWorkoutListVO> sortedWorkoutList = filterWorkoutList.stream()
                .sorted(Comparator.comparing(
                                ProjYogaRegularWorkoutListVO::getNewStartTime,
                                Comparator.nullsLast(Comparator.reverseOrder())
                        )).collect(Collectors.toList());
        YogaRegularWorkoutUtil.setMultiVideoUrl(sortedWorkoutList);
        return succ(sortedWorkoutList);
    }



}
