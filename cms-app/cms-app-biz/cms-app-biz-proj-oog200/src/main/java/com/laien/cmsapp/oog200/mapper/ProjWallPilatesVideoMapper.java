package com.laien.cmsapp.oog200.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.laien.cmsapp.oog200.entity.ProjWallPilatesVideo;
import com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * wall pilates video资源 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public interface ProjWallPilatesVideoMapper extends BaseMapper<ProjWallPilatesVideo> {

    List<ResYogaVideoDetailVO> find(@Param("wallPilatesAutoWorkoutIdSet") Set<Integer> wallPilatesAutoWorkoutIdSet);

    List<ResYogaVideoDetailVO> queryByWallPilatesRegularWorkoutIdSet(@Param("wallPilatesRegularWorkoutIdSet") Set<Integer> wallPilatesRegularWorkoutIdSet);

}
