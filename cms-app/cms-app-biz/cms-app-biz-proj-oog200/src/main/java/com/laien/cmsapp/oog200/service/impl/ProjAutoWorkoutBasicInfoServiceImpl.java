package com.laien.cmsapp.oog200.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog200.bo.ProjAutoWorkoutBasicInfoBO;
import com.laien.cmsapp.oog200.bo.ProjYogaAutoWorkoutPlanBO;
import com.laien.cmsapp.oog200.entity.ProjAutoWorkoutBasicInfoPub;
import com.laien.cmsapp.oog200.entity.ProjAutoWorkoutBasicInfoWorkoutRelationPub;
import com.laien.cmsapp.oog200.mapper.ProjAutoWorkoutBasicInfoMapper;
import com.laien.cmsapp.oog200.requst.ProjAutoWorkoutListReq;
import com.laien.cmsapp.oog200.requst.ProjAutoWorkoutReq;
import com.laien.cmsapp.oog200.response.ProjPlanWorkoutListVO;
import com.laien.cmsapp.oog200.service.IProjAutoWorkoutBasicInfoService;
import com.laien.cmsapp.oog200.service.IProjAutoWorkoutBasicInfoWorkoutRelationService;
import com.laien.cmsapp.util.I18nUtil;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.domain.enums.ProjCodeEnums;
import com.laien.common.oog200.enums.AutoWorkoutBasicInfoPointEnum;
import com.laien.common.oog200.enums.DifficultyEnum;
import com.laien.common.oog200.enums.YogaAutoWorkoutTemplateEnum;
import com.laien.mybatisplus.config.BaseModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author:  hhl
 * Date:  2024/10/22 14:14
 */
@Service
public class ProjAutoWorkoutBasicInfoServiceImpl extends ServiceImpl<ProjAutoWorkoutBasicInfoMapper, ProjAutoWorkoutBasicInfoPub> implements IProjAutoWorkoutBasicInfoService {

    @Resource
    private IProjAutoWorkoutBasicInfoWorkoutRelationService relationService;

    @Resource
    I18nUtil i18nUtil;

    @Override
    public List<ProjPlanWorkoutListVO> generatePlan(ProjYogaAutoWorkoutPlanBO workoutPlanBO) {

        if (CollectionUtils.isEmpty(workoutPlanBO.getBasicInfoBOList())
                || (CollectionUtils.isEmpty(workoutPlanBO.getMatchAllWorkoutIds()) && CollectionUtils.isEmpty(workoutPlanBO.getMatchDurationWorkoutIds()) && CollectionUtils.isEmpty(workoutPlanBO.getMatchOtherWorkoutIds()))) {
            return Collections.emptyList();
        }

        List<ProjAutoWorkoutBasicInfoBO> basicInfoBOList = workoutPlanBO.getBasicInfoBOList();
        i18nUtil.translate(basicInfoBOList, ProjCodeEnums.OOG200, workoutPlanBO.getLang());
        List<ProjPlanWorkoutListVO> workoutPlan = Lists.newArrayList();
        List<Integer> usedWorkoutIds = Lists.newArrayList();
        for (int i = GlobalConstant.ZERO; i < GlobalConstant.TWENTY_ONE; i++) {

            int index = i % basicInfoBOList.size();
            ProjAutoWorkoutBasicInfoBO basicInfoBO = basicInfoBOList.get(index);
            ProjPlanWorkoutListVO planWorkoutListVO = new ProjPlanWorkoutListVO();

            planWorkoutListVO.setName(basicInfoBO.getName());
            planWorkoutListVO.setCoverImgUrl(basicInfoBO.getCoverImage());
            planWorkoutListVO.setInfoId(basicInfoBO.getId());
            workoutPlan.add(planWorkoutListVO);
            List<Integer> configWorkoutIds = CollectionUtils.isEmpty(basicInfoBO.getWorkoutIds()) ? Collections.emptyList() : basicInfoBO.getWorkoutIds();

            boolean selected = selectWorkoutFromConfig(planWorkoutListVO, configWorkoutIds, usedWorkoutIds, workoutPlanBO.getMatchAllWorkoutIds());
            if (selected) {
                continue;
            }
            selected = selectWorkoutFromDB(planWorkoutListVO, usedWorkoutIds, workoutPlanBO.getMatchAllWorkoutIds());
            if (selected) {
                continue;
            }

            selected = selectWorkoutFromConfig(planWorkoutListVO, configWorkoutIds, usedWorkoutIds, workoutPlanBO.getMatchDurationWorkoutIds());
            if (selected) {
                continue;
            }
            selected = selectWorkoutFromDB(planWorkoutListVO, usedWorkoutIds, workoutPlanBO.getMatchDurationWorkoutIds());
            if (selected) {
                continue;
            }

            selected = selectWorkoutFromConfig(planWorkoutListVO, configWorkoutIds, usedWorkoutIds, workoutPlanBO.getMatchOtherWorkoutIds());
            if (selected) {
                continue;
            }
            selected = selectWorkoutFromDB(planWorkoutListVO, usedWorkoutIds, workoutPlanBO.getMatchOtherWorkoutIds());
            if (selected) {
                continue;
            }

            index = i % usedWorkoutIds.size();
            planWorkoutListVO.setId(usedWorkoutIds.get(index));
        }

        return workoutPlan;
    }

    @Override
    public List<ProjPlanWorkoutListVO> setIntoWorkout(List<ProjPlanWorkoutListVO> workoutList, ProjAutoWorkoutListReq workoutListReq) {
        if (null == workoutListReq || CollUtil.isEmpty(workoutList)) {
            return workoutList;
        }
        List<ProjAutoWorkoutReq> workoutReqList = workoutListReq.getWorkoutReqList();
        if (CollUtil.isEmpty(workoutReqList)) {
            return workoutList;
        }
        // key：workoutId,value：basicInfoId
        Map<Integer, Integer> workoutIdMap = new HashMap<>();
        for (ProjAutoWorkoutReq workoutReq : workoutReqList) {
            Integer infoId = workoutReq.getInfoId();
            if (null != infoId) {
                workoutIdMap.put(workoutReq.getId(), infoId);
            }
        }
        if (CollUtil.isEmpty(workoutIdMap)) {
            return workoutList;
        }
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        LambdaQueryWrapper<ProjAutoWorkoutBasicInfoPub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjAutoWorkoutBasicInfoPub::getVersion, versionInfoBO.getCurrentVersion())
                .eq(ProjAutoWorkoutBasicInfoPub::getProjId, versionInfoBO.getProjId())
                .in(BaseModel::getId, workoutIdMap.values());
        List<ProjAutoWorkoutBasicInfoPub> infoList = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(infoList)) {
            return workoutList;
        }

        Map<Integer, ProjAutoWorkoutBasicInfoPub> infoMap = infoList.stream()
                .collect(Collectors.toMap(ProjAutoWorkoutBasicInfoPub::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );

        Map<Integer, ProjPlanWorkoutListVO> workoutListMap = workoutList.stream()
                .collect(Collectors.toMap(ProjPlanWorkoutListVO::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement)
                );

        workoutIdMap.forEach((workoutId, infoId) -> {
            ProjPlanWorkoutListVO workoutListVO = workoutListMap.get(workoutId);
            ProjAutoWorkoutBasicInfoPub infoPub = infoMap.get(infoId);
            if (null != workoutListVO && null != infoPub) {
                workoutListVO.setInfoId(infoPub.getId());
                workoutListVO.setCoverImgUrl(infoPub.getCoverImage());
                workoutListVO.setName(infoPub.getName());
            }
        });
        return workoutList;
    }

    @Override
    public List<ProjAutoWorkoutBasicInfoBO> listEnable(YogaAutoWorkoutTemplateEnum planType, AutoWorkoutBasicInfoPointEnum point, DifficultyEnum difficulty) {

        LambdaQueryWrapper<ProjAutoWorkoutBasicInfoPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjAutoWorkoutBasicInfoPub::getPlanType, planType);
        queryWrapper.eq(null != difficulty, ProjAutoWorkoutBasicInfoPub::getDifficulty, difficulty);
        queryWrapper.eq(null != point, ProjAutoWorkoutBasicInfoPub::getPoint, point);
        queryWrapper.eq(ProjAutoWorkoutBasicInfoPub::getStatus, GlobalConstant.STATUS_ENABLE);
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        queryWrapper.eq(ProjAutoWorkoutBasicInfoPub::getVersion, versionInfoBO.getCurrentVersion());
        queryWrapper.eq(ProjAutoWorkoutBasicInfoPub::getProjId, versionInfoBO.getProjId());

        queryWrapper.orderByAsc(ProjAutoWorkoutBasicInfoPub::getSorted);
        List<ProjAutoWorkoutBasicInfoPub> basicInfoList = list(queryWrapper);
        if (CollectionUtils.isEmpty(basicInfoList)) {
            return Collections.emptyList();
        }

        List<ProjAutoWorkoutBasicInfoBO> basicInfoBOList = basicInfoList.stream().map(basicInfo -> {
            ProjAutoWorkoutBasicInfoBO basicInfoBO = new ProjAutoWorkoutBasicInfoBO();
            BeanUtils.copyProperties(basicInfo, basicInfoBO);
            return basicInfoBO;
        }).collect(Collectors.toList());

        List<Integer> basicInfoIdList = basicInfoList.stream().map(ProjAutoWorkoutBasicInfoPub::getId).collect(Collectors.toList());
        List<ProjAutoWorkoutBasicInfoWorkoutRelationPub> workoutRelations = relationService.listByBasicInfoIds(basicInfoIdList);
        if (CollectionUtils.isEmpty(workoutRelations)) {
            return basicInfoBOList;
        }

        Map<Integer, List<Integer>> basicInfoAndWorkoutMap = workoutRelations.stream().collect(Collectors.groupingBy(ProjAutoWorkoutBasicInfoWorkoutRelationPub::getProjAutoWorkoutBasicInfoId,
                Collectors.mapping(ProjAutoWorkoutBasicInfoWorkoutRelationPub::getAutoWorkoutId, Collectors.toList())));
        basicInfoBOList.forEach(basicInfoBO -> basicInfoBO.setWorkoutIds(basicInfoAndWorkoutMap.getOrDefault(basicInfoBO.getId(), Collections.emptyList())));
        return basicInfoBOList;
    }

    private boolean selectWorkoutFromConfig(ProjPlanWorkoutListVO planWorkoutListVO, List<Integer> configWorkoutIds, List<Integer> usedWorkoutIds, Set<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return false;
        }

        List<Integer> matchWorkoutIds = workoutIds.stream().filter(id -> configWorkoutIds.contains(id) && !usedWorkoutIds.contains(id)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchWorkoutIds)) {
            return false;
        }

        Collections.shuffle(matchWorkoutIds);
        planWorkoutListVO.setId(matchWorkoutIds.get(GlobalConstant.ZERO));
        usedWorkoutIds.add(matchWorkoutIds.get(GlobalConstant.ZERO));
        return true;
    }

    private boolean selectWorkoutFromDB(ProjPlanWorkoutListVO planWorkoutListVO, List<Integer> usedWorkoutIds, Set<Integer> workoutIds) {

        if (CollectionUtils.isEmpty(workoutIds)) {
            return false;
        }

        List<Integer> matchWorkoutIds = workoutIds.stream().filter(id -> !usedWorkoutIds.contains(id)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchWorkoutIds)) {
            return false;
        }

        Collections.shuffle(matchWorkoutIds);
        planWorkoutListVO.setId(matchWorkoutIds.get(GlobalConstant.ZERO));
        usedWorkoutIds.add(matchWorkoutIds.get(GlobalConstant.ZERO));
        return true;
    }

}
