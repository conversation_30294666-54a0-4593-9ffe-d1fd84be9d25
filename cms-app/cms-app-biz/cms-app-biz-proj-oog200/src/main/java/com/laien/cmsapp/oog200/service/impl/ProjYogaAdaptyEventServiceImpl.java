package com.laien.cmsapp.oog200.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.cmsapp.oog200.entity.ProjYogaAdaptyEvent;
import com.laien.cmsapp.oog200.mapper.ProjYogaAdaptyEventMapper;
import com.laien.cmsapp.oog200.service.IProjYogaAdaptyEventService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: hhl
 * @date: 2025/6/13
 */
@Service
public class ProjYogaAdaptyEventServiceImpl extends ServiceImpl<ProjYogaAdaptyEventMapper, ProjYogaAdaptyEvent> implements IProjYogaAdaptyEventService {

    @Resource
    private ProjYogaAdaptyEventMapper adaptyEventMapper;

    @Override
    public List<ProjYogaAdaptyEvent> listUnHandleEvent() {

        return adaptyEventMapper.listUnHandleEvent();
    }

    @Override
    public ProjYogaAdaptyEvent getByUniqueKey(String profileId, String transactionId, String eventType) {

        LambdaQueryWrapper<ProjYogaAdaptyEvent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjYogaAdaptyEvent::getProfileId, profileId);
        queryWrapper.eq(ProjYogaAdaptyEvent::getTransactionId, transactionId);
        queryWrapper.eq(ProjYogaAdaptyEvent::getEventType, eventType);
        return getOne(queryWrapper);
    }
}
