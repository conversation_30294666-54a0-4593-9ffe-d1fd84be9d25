<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjVideoGenerateMapper">

    <select id="selectVideosById" resultType="com.laien.cmsapp.oog200.response.ProjVideoV2VO">
        SELECT
            vs.id,
            vs.video_name videoName,
            10000 previewDuration,
            vs.video1_duration videoDuration
        FROM
            proj_video_generate_relation vgr
            INNER JOIN res_video_slice vs ON vgr.video_id = vs.id
        WHERE
            vgr.del_flag = 0
          AND vgr.generate_id = #{id}
    </select>

    <select id="selectVideosV3ById" resultType="com.laien.cmsapp.oog200.response.ProjVideoV3VO">
        SELECT
            vs.id,
            vs.video_name videoName,
            10000 previewDuration,
            (vs.video1_duration * 3 + vs.video2_duration) videoDuration
        FROM
            proj_video_generate_relation vgr
                INNER JOIN res_video_slice vs ON vgr.video_id = vs.id
        WHERE
            vgr.del_flag = 0
          AND vgr.generate_id = #{id}
    </select>

    <select id="selectVideosV3ByIds" resultType="com.laien.cmsapp.oog200.response.ProjVideoV3VO">
        SELECT
            vgr.generate_id,
            vs.id,
            vs.video_name videoName,
            10000 previewDuration,
            (vs.video1_duration * 3 + vs.video2_duration) videoDuration
        FROM
            proj_video_generate_relation vgr
                INNER JOIN res_video_slice vs ON vgr.video_id = vs.id
        WHERE
        <foreach collection="idList" item="id" open="vgr.generate_id in (" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY vgr.generate_id,vgr.id;
    </select>

    <select id="selectVideosByTempAndIds" resultType="com.laien.cmsapp.oog200.entity.ProjVideoGenerate">
        SELECT
        *
        FROM
        proj_video_generate pvg
        <where>
            <if test="templateIds != null and templateIds.size() > 0">
                AND pvg.template_id in
                <foreach item="templateId" index="index" collection="templateIds" open="(" close=")" separator=",">
                    #{templateId}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                AND pvg.id in
                <foreach item="id" index="index" collection="ids" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dataVersion != null">
                AND data_version=#{dataVersion}
            </if>
        </where>
    </select>
</mapper>
