<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjTemplateMapper">

    <select id="selectTemplateIds" resultType="integer">
        SELECT
            id
        FROM
            proj_template_pub
        WHERE
            del_flag = 0
          AND `status` = 1
          AND proj_id = #{projId}
          AND version = #{version}
          AND duration = #{duration}
    </select>

    <select id="selectTemplateIdsByDuration" resultType="integer">
        SELECT
            id
        FROM
            proj_template_pub
        WHERE
            del_flag = 0
          AND `status` = 1
          AND proj_id = #{projId}
          AND version = #{version}
          AND duration &gt;= #{beginDuration}
          AND duration &lt;= #{endDuration}
    </select>

</mapper>
