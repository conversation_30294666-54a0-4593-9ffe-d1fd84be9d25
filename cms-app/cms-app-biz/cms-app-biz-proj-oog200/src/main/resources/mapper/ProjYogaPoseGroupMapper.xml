<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjYogaPoseGroupMapper">

    <select id="findByYogaPoseLevelId" resultType="com.laien.cmsapp.oog200.entity.ProjYogaPoseGroupPub">
        SELECT pg.id,
               pg.name,
               pg.event_name,
               pg.description,
               pg.type,
               pg.group_img_light_url,
               pg.group_img_dark_url
        FROM proj_yoga_pose_group_pub pg
                 JOIN proj_yoga_pose_level_group_relation_pub plg ON pg.id = plg.proj_yoga_pose_group_id
        WHERE pg.version = #{versionInfoBO.currentVersion}
          AND pg.`status` = 1
          AND pg.del_flag = 0
          AND pg.proj_id = #{versionInfoBO.projId}
          AND plg.proj_yoga_pose_level_id = #{yogaPoseLevelId}
          AND plg.version = #{versionInfoBO.currentVersion}
          AND plg.del_flag = 0
        ORDER BY plg.id
    </select>

</mapper>
