<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjChairYogaVideoMapper">
    <select id="listVideo4Res" resultType="com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO">
        SELECT wpv.id,
               wpv.name,
               wpv.event_name,
               wpv.image_url,
               wpv.core_voice_config_i18n_id,
               wvr.video_duration as real_video_duration,
               wpv.calorie,
               wvr.proj_chair_yoga_auto_workout_id AS workout_id,
               wvr.id as sort_id
        FROM
            proj_chair_yoga_video wpv
                INNER JOIN proj_chair_yoga_auto_workout_video_relation wvr ON wvr.proj_chair_yoga_video_id = wpv.id
        WHERE
        <foreach collection="workoutIds" item="id" open=" wvr.proj_chair_yoga_auto_workout_id in (" close=")" separator=",">
            #{id}
        </foreach>
            AND wvr.del_flag = 0
        ORDER BY wvr.id
    </select>

    <select id="listVideo4ResAndRegularWorkout" resultType="com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO">
        SELECT wpv.id,
        wpv.name,
        wpv.event_name,
        wpv.image_url,
        wvr.video_duration as real_video_duration,
        wpv.calorie,
        wpv.core_voice_config_i18n_id,
        wvr.proj_chair_yoga_regular_workout_id AS workout_id,
        wvr.id as sort_id
        FROM
        proj_chair_yoga_video wpv
        INNER JOIN proj_chair_yoga_regular_workout_video_relation wvr ON wvr.proj_chair_yoga_video_id = wpv.id
        WHERE
        <foreach collection="workoutIds" item="id" open=" wvr.proj_chair_yoga_regular_workout_id in (" close=")" separator=",">
            #{id}
        </foreach>
        AND wvr.del_flag = 0
        ORDER BY wvr.id
    </select>
</mapper>
