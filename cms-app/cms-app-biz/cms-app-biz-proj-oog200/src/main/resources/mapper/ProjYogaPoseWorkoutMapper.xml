<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjYogaPoseWorkoutMapper">


    <select id="findByYogaPoseGroupId" resultType="com.laien.cmsapp.oog200.bo.ProjYogaPoseWorkoutBO">
        SELECT
           yp.id,
           yp.name,
           yp.event_name,
           yp.sanskrit_name,
           yp.cover_img_url,
           yp.detail_img_url,
           yp.pose_light_img_url,
           yp.pose_dark_img_url,
           yp.difficulty,
           yp.instructions,
           yp.benefits,
           yp.chair_variation,
           yp.chair_variation_img_url,
           yp.chair_variation_tips,
           yp.flexibility,
           yp.balance,
           yp.strength,
           yp.relaxation,
           yp.calorie,
           yp.duration,
           yp.subscription,
           yp.video_m3u8_url,
           yp.video2532_url,
           yp.audio_long_json,
           yp.audio_short_json,
           yp.focus,
           yp.proj_yoga_pose_video_id,
           yp.position,
           yp.video_mask_json,
           pgw.proj_yoga_pose_group_id yogaPoseGroupId
        FROM
            proj_yoga_pose_workout_pub yp
                JOIN proj_yoga_pose_group_workout_relation_pub pgw ON yp.id = pgw.proj_yoga_pose_workout_id
        WHERE
            yp.version = #{versionInfoBO.currentVersion}
          AND yp.`status` = 1
          AND yp.del_flag = 0
          AND yp.proj_id = #{versionInfoBO.projId}
          AND pgw.version = #{versionInfoBO.currentVersion}
          AND pgw.del_flag = 0
          AND pgw.proj_yoga_pose_group_id IN
        <foreach collection="yogaPoseGroupIdSet" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        ORDER BY pgw.id
    </select>
</mapper>
