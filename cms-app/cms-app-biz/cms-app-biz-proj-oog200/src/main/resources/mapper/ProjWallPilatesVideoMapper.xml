<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjWallPilatesVideoMapper">
    <select id="find" resultType="com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO">
        SELECT wpv.id,
               wpv.name,
               wpv.event_name,
               wpv.image_url,
               wpv.target,
               wpv.position,
               wpv.core_voice_config_i18n_id,
               wpv.type,
               wpv.guidance_audio_url,
               wpv.guidance_audio_duration,
               wpv.name_audio_url,
               wpv.name_audio_duration,
               wpv.calorie,
               wvr.proj_wall_pilates_auto_workout_id AS workoutId
        FROM
            proj_wall_pilates_video wpv
                JOIN proj_wall_pilates_auto_workout_video_relation wvr ON wvr.proj_wall_pilates_video_id = wpv.id
        WHERE
        <foreach collection="wallPilatesAutoWorkoutIdSet" item="id" open=" wvr.proj_wall_pilates_auto_workout_id in (" close=")" separator=",">
            #{id}
        </foreach>
            AND wvr.del_flag = 0
        ORDER BY wvr.id
    </select>
    <select id="queryByWallPilatesRegularWorkoutIdSet"
            resultType="com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO">
        SELECT
            wpv.id,
            wpv.name,
            wpv.event_name,
            wpv.image_url,
            wpv.target,
            wpv.core_voice_config_i18n_id,
            wpv.position,
            wpv.type,
            wpv.guidance_audio_url,
            wpv.guidance_audio_duration,
            wpv.name_audio_url,
            wpv.name_audio_duration,
            wpv.calorie,
            wvr.proj_wall_pilates_regular_workout_id AS workoutId
        FROM
            proj_wall_pilates_video wpv
        JOIN proj_wall_pilates_regular_workout_video_relation wvr ON wvr.proj_wall_pilates_video_id = wpv.id
        WHERE
        <foreach collection="wallPilatesRegularWorkoutIdSet" item="id" open=" wvr.proj_wall_pilates_regular_workout_id in (" close=")" separator=",">
            #{id}
        </foreach>
        AND wvr.del_flag = 0
        ORDER BY wvr.id
    </select>
</mapper>
