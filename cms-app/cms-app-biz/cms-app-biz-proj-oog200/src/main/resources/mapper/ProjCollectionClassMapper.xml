<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjCollectionClassMapper">


    <select id="selectCollectionClassList" resultType="com.laien.cmsapp.oog200.entity.ProjCollectionClassPub">
        SELECT
            cc.id,
            cc.`name`,
            cc.event_name,
            cc.cover_img,
            cc.yoga_type,
            cc.new_end_time,
            cc.new_start_time,
            cc.subscription,
            cc.goal
        FROM
            proj_collection_class_pub cc
        WHERE
            cc.proj_id = #{projId}
          AND cc.version = #{version}
          AND cc.`status` = 1
          AND cc.del_flag = 0
        ORDER BY
            cc.id desc
    </select>

    <select id="selectCollectionClassCounts" resultType="com.laien.common.response.IdAndCountsAndMaxMinRes">
        SELECT
            ccr.collection_class_id AS id,
            count(*) counts,
            max(c.duration) `maxValue`,
            min(c.duration) `minValue`
        FROM
            proj_collection_class_pub cc
                INNER JOIN proj_collection_class_relation_pub ccr ON cc.id = ccr.collection_class_id
                INNER JOIN res_video_class c ON c.id = ccr.video_class_id
        WHERE
            cc.proj_id = #{projId}
          AND cc.version = #{version}
          AND cc.`status` = 1
          AND cc.del_flag = 0
          AND ccr.del_flag = 0
          AND ccr.version = #{version}
          AND c.del_flag = 0
          AND c.`status` = 1
        GROUP BY
            ccr.collection_class_id
    </select>

    <select id="selectCollectionClassDetail" resultType="com.laien.cmsapp.response.ProjCollectionClassDetailVO">
        SELECT
            cc.id,
            cc.`name`,
            cc.event_name,
            cc.detail_img,
            cc.description as `desc`,
            cc.teacher_id,
            cc.new_end_time,
            cc.new_start_time,
            cc.subscription,
            cc.yoga_type,
            cc.type as `typeCode`
        FROM
            proj_collection_class_pub cc
        WHERE
            cc.id = #{id}
          AND cc.version = #{info.currentVersion}
          AND cc.del_flag = 0
    </select>

    <select id="selectCollectionClassVideoClasses" resultType="com.laien.cmsapp.response.ResVideoClassVO">
        SELECT
            c.id,
            c.`name`,
            c.event_name,
            c.image_png,
            c.image_gif,
            c.video_url,
            c.video_m3u8_url,
            c.video2532_m3u8_url,
            c.duration,
            c.difficulty,
            c.play_type,
            c.calorie,
            c.subscription,
            c.new_start_time,
            c.new_end_time,
            c.type as `typeCode`,
            c.m3u8_url
        FROM
            proj_collection_class_relation_pub ccr
                INNER JOIN res_video_class c ON c.id = ccr.video_class_id
        WHERE
            ccr.collection_class_id = #{id}
          AND ccr.del_flag = 0
          AND ccr.version = #{info.currentVersion}
          AND c.del_flag = 0
          AND c.`status` = 1
    </select>


</mapper>
