<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjVideoGenerateI18nMapper">

    <select id="selectIdsIncludeDel" resultType="com.laien.cmsapp.oog200.entity.ProjVideoGenerateI18n">
        SELECT
            *
        FROM
            proj_video_generate_i18n pvgi
        <where>
            <if test="ids != null and ids.size() > 0">
                AND pvgi.generate_id in
                <foreach item="id" index="index" collection="ids" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
