<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ProjYogaAutoWorkoutMapper">

    <select id="selectWorkoutVideoListByIds" resultType="com.laien.cmsapp.oog200.response.ResYogaVideoDetailVO">
        SELECT
            wv.proj_yoga_auto_workout_id AS workoutId,
            v.id,
            v.`name`,
            v.event_name,
            v.image_url,
            v.core_voice_config_i18n_id,
            wv.real_video_duration,
            wv.real_transition_duration
        FROM
            proj_yoga_auto_workout_video_relation AS wv
                INNER JOIN res_yoga_video AS v ON wv.res_yoga_video_id = v.id
        WHERE
            wv.del_flag = 0
        <foreach collection="idList" item="id" open="AND wv.proj_yoga_auto_workout_id in (" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY
            wv.id ASC
    </select>

</mapper>
