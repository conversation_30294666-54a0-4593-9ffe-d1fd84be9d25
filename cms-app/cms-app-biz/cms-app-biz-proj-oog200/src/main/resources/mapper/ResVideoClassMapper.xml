<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.cmsapp.oog200.mapper.ResVideoClassMapper">

    <select id="selectIdsByProj" resultType="com.laien.cmsapp.entity.ResVideoClass">
        SELECT
            rvc.*
        FROM
            res_video_class rvc
        WHERE
            rvc.id IN
            <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND rvc.del_flag = 0
            AND rvc.`status` =1
            AND rvc.id IN (
            SELECT DISTINCT
                pccr.video_class_id
            FROM
                proj_collection_class_relation_pub pccr
                JOIN proj_collection_class_pub pcc ON pcc.id = pccr.collection_class_id
            WHERE
                pcc.proj_id = #{projId}
                AND pcc.`version` = #{version}
                AND pccr.`version` = #{version}
                AND pcc.`status` = 1
                AND pcc.del_flag = 0
            )

    </select>


</mapper>
