package com.laien.common.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * note: 日期工具
 *
 * <AUTHOR>
 */
public final class DateUtil {

    public static final String PATTERN_DATE_DEFAULT = "yyyy-MM-dd";
    public static final String PATTERN_DATETIME_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取当期时间
     *
     * @return localdate
     */
    public static LocalDate getNow() {
        return LocalDate.now();
    }

    /**
     * 获取当期周一日期
     *
     * @return LocalDate
     */
    public static LocalDate getMondayDate() {
        return getMondayDate(LocalDate.now());
    }

    /**
     * 获取下周一
     *
     * @return LocalDate
     */
    public static LocalDate getNextMondayDate() {
        LocalDate date = LocalDate.now();
        int value = date.getDayOfWeek().getValue();
        return date.plusDays(7 - value + 1);
    }

    /**
     * 获取指定日期所在周的周一日期
     *
     * @return LocalDate
     */
    public static LocalDate getMondayDate(LocalDate date) {
        int value = date.getDayOfWeek().getValue();
        return date.minusDays(value - 1);
    }

    /**
     * 获取当期周日日期
     *
     * @return LocalDate
     */
    public static LocalDate getSundayDate() {
        return getSundayDate(LocalDate.now());
    }

    /**
     * 获取指定日期所在周的周日日期
     *
     * @return LocalDate
     */
    public static LocalDate getSundayDate(LocalDate date) {
        int value = date.getDayOfWeek().getValue();
        return date.plusDays(7 - value);
    }

    /**
     * LocalDate转为字符串
     *
     * @param date 日期
     * @param pattern 格式
     * @return String
     */
    public static String formatDate(LocalDate date, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return date.format(formatter);
    }

    /**
     * 字符串转为LocalDate
     *
     * @param date 日期
     * @param pattern 格式
     * @return String
     */
    public static LocalDate parseDate(String date, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(date, formatter);
    }

    /**
     * 字符串转为LocalDateTime
     *
     * @param dateTime 日期时间
     * @return String
     */
    public static LocalDateTime parseDateTime(String dateTime) {
        return parseDateTime(dateTime, PATTERN_DATETIME_DEFAULT);
    }

    /**
     * 字符串转为LocalDateTime
     *
     * @param dateTime 日期时间
     * @param pattern 格式
     * @return String
     */
    public static LocalDateTime parseDateTime(String dateTime, String pattern) {
        if (Objects.isNull(dateTime)) {
            return null;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(dateTime, formatter);
    }

    /**
     * 获取当期时间
     *
     * @return LocalDateTime
     */
    public static LocalDateTime getNowDateTime() {
        return LocalDateTime.now();
    }

    /**
     * 获取指定时间的开始时间
     *
     * @param date date
     * @return LocalDateTime
     */
    public static LocalDateTime getDayStart(LocalDate date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.of(date, LocalTime.MIN);
    }

    /**
     * 获取指定时间的结束时间
     *
     * @param date date
     * @return LocalDateTime
     */
    public static LocalDateTime getDayEnd(LocalDate date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.of(date, LocalTime.MAX);
    }

}
