package com.laien.common.util;

import com.laien.common.constant.DeviceType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.laien.common.constant.GlobalConstant.*;

/**
 * 获取接口请求作用域工具类
 */
public class RequestContextUtils {

    /**
     * 获取当前登陆用户
     *
     * @return
     */
    public static LoginUserInfo getLoginUser() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        Object attribute = requestAttributes.getAttribute(PASS_USER_HEADER_NAME, RequestAttributes.SCOPE_SESSION);
        return attribute != null ? (LoginUserInfo) attribute : null;
    }

    /**
     * 获取语言
     *
     * @return
     */
    public static String getLanguage() {
        HttpServletRequest request = getRequest();
        String lang = request.getHeader(HEADER_LANGUAGE_NAME);
        if (StringUtils.isBlank(lang)) {
            return request.getParameter(HEADER_LANGUAGE_NAME);
        }

        return lang;
    }

    /**
     * 获取设备，默认phone
     * @return String
     */
    public static String getDeviceDefaultPhone() {
        HttpServletRequest request = getRequest();
        String device = request.getHeader(HEADER_DEVICE_NAME);
        if (device == null) {
            return DeviceType.PHONE;
        }

        return device;
    }

    /**
     * 获取项目id
     *
     * @return
     */
    public static Integer getProjectId() {
        HttpServletRequest request = getRequest();
        String projIdStr = request.getHeader(HEADER_PROJECTID_NAME);
        if (StringUtils.isNotBlank(projIdStr) && StringUtils.isNumeric(projIdStr)) {
            return Integer.parseInt(projIdStr);
        }
        return null;
    }

    /**
     * 获取appcode
     *
     * @return String
     */
    public static String getAppCode() {
        HttpServletRequest request = getRequest();
        return request.getHeader(HEADER_APPCODE);
    }

    private static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // get the request
        HttpServletRequest request = requestAttributes.getRequest();
        return request;
    }

    /**
     * 获取当期请求路径
     *
     * @return String
     */
    public static String getRequestUrl() {
        HttpServletRequest request = getRequest();
        String url = request.getRequestURI();
        String queryString = request.getQueryString();
        if (StringUtils.isNotBlank(queryString)) {
            url = url + "?" + queryString;
        }
        return url;
    }

}
