package com.laien.common.util;

import com.laien.common.constant.GlobalConstant;
import org.apache.commons.lang3.StringUtils;

/**
 * note: File url substring util
 *
 * <AUTHOR>
 */
public final class FileUrlSubUtils {

    /**
     * 截取url中的文件名
     *
     * @param url url
     * @return String
     */
    public static String getFileName(String url) {
        if (StringUtils.isBlank(url)) {
            return GlobalConstant.EMPTY_STRING;
        }
        return StringUtils.substringAfterLast(StringUtils.substringBeforeLast(url, "?"), "/");
    }

}
