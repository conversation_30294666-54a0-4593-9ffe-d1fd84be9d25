package com.laien.common.util;

import com.laien.common.exception.BizException;
import org.apache.poi.ss.formula.functions.T;

import java.util.function.BooleanSupplier;


/**
 * <p>
 * 业务异常工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public class BizExceptionUtil {

    /**
     * 根据条件抛出指定异常
     *
     * @param condition 判断条件的布尔函数
     * @throws BizException 如果条件为 true，则抛出指定异常
     */
    public static void throwIf(BooleanSupplier condition,String message) throws BizException {
        throwIf(condition.getAsBoolean(),message);
    }

    /**
     * 根据条件抛出指定异常
     *
     * @param condition 判断条件
     * @throws BizException 如果条件为 true，则抛出指定异常
     */
    public static void throwIf(boolean condition,String message) throws BizException {
        if (condition) {
            throw new BizException(message);
        }
    }
}