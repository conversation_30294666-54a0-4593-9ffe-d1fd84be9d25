package com.laien.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * note:id分页参数
 *
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@ApiModel(value="id分页参数", description="id分页参数")
public class IdPageReq extends PageReq {

    @ApiModelProperty(value = "数据id")
    private Integer id;

}
