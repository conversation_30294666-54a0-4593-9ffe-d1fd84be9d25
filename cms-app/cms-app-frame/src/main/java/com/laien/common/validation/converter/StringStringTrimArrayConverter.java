package com.laien.common.validation.converter;

import com.alibaba.excel.converters.string.StringStringConverter;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

import java.util.List;
import java.util.Optional;

public class StringStringTrimArrayConverter extends StringStringConverter {
    @Override
    public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        String s = Optional.ofNullable(cellData.getStringValue()).map(n -> n.trim()).get();
        if (s.contains(",")) {
            List<String> strings = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(s);
            s = Joiner.on(",").skipNulls().join(strings);
        }
        return s;
    }

    @Override
    public CellData convertToExcelData(String value, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration) {
        return new CellData(Optional.ofNullable(value).map(n -> n.trim()).get());
    }
}