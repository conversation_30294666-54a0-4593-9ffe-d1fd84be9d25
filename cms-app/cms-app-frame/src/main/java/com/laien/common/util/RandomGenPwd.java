package com.laien.common.util;

import java.util.Random;

/**
 * 随机密码器
 */
public class RandomGenPwd {

    /**
     * 随机出用户输入的密码位数的密码,从大小写字母,数字中取值
     */
    public static String gen(int num) {
        //创建char数组接收每一位随机出来的密码
        char[] passwor = new char[num];
        Random rand = new Random();
        //在ASCII码表中,48-57 数字,65-90 大写字母,97-122 小写字母
        for (int i = 0; i < passwor.length; i++) {
            int choice = rand.nextInt(3);
            //小写字母ASCII码表范围
            int lowercase = rand.nextInt(26) + 65;
            //大写字母ASCII码表范围
            int uppercase = rand.nextInt(26) + 97;
            //数字ASCII码表范围
            int figure = rand.nextInt(10) + 48;
            //从大写字母.小写字母.数字中随机取值
            switch (choice) {
                case 0:
                    passwor[i] = (char) lowercase;
                    break;
                case 1:
                    passwor[i] = (char) uppercase;
                    break;
                case 2:
                    passwor[i] = (char) figure;
            }
        }
        String password = new String(passwor);
        return password;
    }
}