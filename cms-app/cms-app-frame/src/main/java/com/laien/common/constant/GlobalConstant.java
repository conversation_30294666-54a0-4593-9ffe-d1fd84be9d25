package com.laien.common.constant;

/**
 * 全局变量
 *
 * <AUTHOR>
 */
public interface GlobalConstant {

    /**
     * 正向，1，yes
     */
    int YES = 1;
    /**
     * 负向，0，no
     */
    int NO = 0;

    /**
     * value -1
     */
    int MINUS_ONE = -1;
    /**
     * value 0
     */
    int ZERO = 0;
    /**
     * value 1
     */
    int ONE = 1;

    /**
     * value 2
     */
    int TWO = 2;
    /**
     * value 3
     */
    int THREE = 3;
    /**
     * value 4
     */
    int FOUR = 4;
    /**
     * value 5
     */
    int FIVE = 5;
    /**
     * value 6
     */
    int SIX = 6;
    /**
     * value 7
     */
    int SEVEN = 7;
    /**
     * value 8
     */
    int EIGHT = 8;
    /**
     * value 9
     */
    int NINE = 9;
    /**
     * value 10
     */
    int TEN = 10;
    /**
     * value 11
     */
    int ELEVEN = 11;
    /**
     * value 12
     */
    int TWELVE = 12;

    /**
     * value 13
     */
    int THIRTEEN = 13;

    int FOURTEEN = 14;

    int FIFTEEN = 15;

    /**
     * value 21
     */
    int TWENTY_ONE = 21;

    /**
     * value 28
     */
    int TWENTY_EIGHT = 28;

    /**
     * 默认语言
     */
    String DEFAULT_LANGUAGE = "en";

    /**
     * value 100
     */
    int HUNDRED = 100;
    /**
     * value 1000
     */
    int THOUSAND = 1000;

    /**
     * 空字符""
     */
    String EMPTY_STRING = "";

    /**
     * 空格字符串
     */
    String SPACE_STRING = " ";

    /**
     * 英文逗号
     */
    String COMMA = ",";

    /**
     * 鉴权的请求头名称
     */
    String AUTH_HEADER_NAME = "token";

    /**
     * 语言头
     */
    String HEADER_LANGUAGE_NAME = "lang";

    /**
     * 设备
     */
    String HEADER_DEVICE_NAME = "device";

    /**
     * 项目id
     */
    String HEADER_PROJECTID_NAME = "projId";

    /**
     * 服务传递用户信息
     */
    String PASS_USER_HEADER_NAME = "currentLoginUser";

    /**
     * 服务传递用户信息
     */
    String HEADER_APPVERSION = "version";

    /**
     * appcode
     */
    String HEADER_APPCODE = "appCode";

    /**
     * 数据状态 0 草稿 1 启用 2禁用
     */

    int STATUS_DRAFT = 0;

    int STATUS_ENABLE = 1;

    int STATUS_DISABLE = 2;


    /**
     * category的dataSource取值
     */
    String CATEGORY_SELECT_PART = "Select Part";
    String CATEGORY_ALL = "All";
    String CATEGORY_HISTORY = "History";
    String CATEGORY_FAVORITE = "Favorite";
    String CATEGORY_ROUTINE = "Routine";


    /**
     * 预发布版本号
     */
    int VERSION_PRE_RELEASE = -1;

    Integer TOP_PICKS_CATEGORY_CODE = 2000;
}
