package com.laien.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * note: id和status，count
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value="id和status，count", description="")
public class IdAndStatusCountsRes {

    @ApiModelProperty(value = "数据id")
    private Integer id;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "counts")
    private Integer counts;

}
