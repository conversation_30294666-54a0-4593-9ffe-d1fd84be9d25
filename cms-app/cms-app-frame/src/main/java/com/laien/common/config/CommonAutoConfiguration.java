package com.laien.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/9/26
 */
@Configuration
@ComponentScan(value = "com.laien.common")
@ConditionalOnProperty(prefix = "laien.common", name = "enable",havingValue = "true", matchIfMissing = true)
public class CommonAutoConfiguration {
}
