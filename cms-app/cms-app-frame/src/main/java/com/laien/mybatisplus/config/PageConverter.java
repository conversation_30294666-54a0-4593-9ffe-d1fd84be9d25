package com.laien.mybatisplus.config;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页转换为PageResult对象
 *
 * <AUTHOR>
 */
public final class PageConverter {

    public static <T> PageRes<T> convert(Page<T> page) {
        return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), page.getRecords());
    }

    public static <M, T> PageRes<T> convert(IPage<M> page, Class<T> clazz) {
        List<M> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), new ArrayList<>());
        }
        List<T> list = new ArrayList<>(records.size());
        records.forEach(item -> {
            T t = null;
            try {
                t = clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                throw new UnsupportedOperationException("newInstance failed,class is " + clazz.getName());
            }
            BeanUtils.copyProperties(item, t);
            list.add(t);
        });
        return new PageRes<T>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list);
    }


}
