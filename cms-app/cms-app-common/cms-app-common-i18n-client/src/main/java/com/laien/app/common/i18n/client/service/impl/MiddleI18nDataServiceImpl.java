package com.laien.app.common.i18n.client.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.app.common.i18n.client.entity.MiddleI18nData;
import com.laien.app.common.i18n.client.entity.MiddleI18nDataPub;
import com.laien.app.common.i18n.client.entity.MiddleTextInCode;
import com.laien.app.common.i18n.client.mapper.MiddleI18nDataMapper;
import com.laien.app.common.i18n.client.service.IMiddleI18nDataPubService;
import com.laien.app.common.i18n.client.service.IMiddleI18nDataService;
import com.laien.app.common.i18n.client.service.IMiddleTextInCodeService;
import com.laien.common.constant.GlobalConstant;
import com.laien.common.util.JacksonUtil;
import com.laien.common.util.RequestContextUtils;
import com.laien.mybatisplus.config.BaseModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 中台-本地化数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Service
public class MiddleI18nDataServiceImpl extends ServiceImpl<MiddleI18nDataMapper, MiddleI18nData> implements IMiddleI18nDataService {

    @Resource
    private IMiddleI18nDataPubService middleI18nDataPubService;
    @Resource
    private IMiddleTextInCodeService middleTextInCodeService;

    // 发布表后缀
    private static final String PUB_TABLE_SUFFIX = "_pub";

    @Override
    public <T> Map<Integer, Map<String, T>> getI18nDataGroupList(Class<?> entityClass, Class<T> convertClass) {
        return this.getI18nDataGroupList(entityClass, convertClass, null, true);
    }

    @Override
    public <T> Map<Integer, Map<String, T>> getI18nDataGroupList(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection) {
        return this.getI18nDataGroupList(entityClass, convertClass, idCollection, false);
    }

    /**
     * 无需发布的数据，获取所有语种
     *
     * @param entityClass entityClass
     * @param convertClass convertClass
     * @param idCollection idCollection
     * @param searchAll searchAll
     * @return map
     * @param <T> T
     */
    private <T> Map<Integer, Map<String, T>> getI18nDataGroupList(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection, boolean searchAll) {
        if (CollectionUtils.isEmpty(idCollection) && !searchAll) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<MiddleI18nData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiddleI18nData::getTableName, this.getTableName(entityClass));
        if (!searchAll) {
            queryWrapper.in(MiddleI18nData::getDataId, idCollection);
        }

        return this.list(queryWrapper).stream()
                .collect(Collectors.groupingBy(
                        MiddleI18nData::getDataId,
                        Collectors.toMap(
                                MiddleI18nData::getLanguage, v -> JacksonUtil.parseObject(v.getData(), convertClass)
                        )
                ));
        }



    @Override
    public <T> Map<Integer, Map<String, T>> getI18nDataGroupList(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection, Integer version) {
        if (CollectionUtils.isEmpty(idCollection)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<MiddleI18nDataPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiddleI18nDataPub::getTableName, this.getTableName(entityClass))
                .eq(MiddleI18nDataPub::getVersion, version)
                .in(MiddleI18nDataPub::getDataId, idCollection);

        return middleI18nDataPubService.list(queryWrapper).stream()
                .collect(Collectors.groupingBy(
                        MiddleI18nDataPub::getDataId,
                        Collectors.toMap(
                                MiddleI18nDataPub::getLanguage, v -> JacksonUtil.parseObject(v.getData(), convertClass)
                        )
                ));
    }

    @Override
    public <T> Map<Integer, T> getI18nDataMap(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection, String language) {
        if (CollectionUtils.isEmpty(idCollection)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<MiddleI18nData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiddleI18nData::getTableName, this.getTableName(entityClass))
                .eq(MiddleI18nData::getLanguage, language)
                .in(MiddleI18nData::getDataId, idCollection);

        return this.list(queryWrapper).stream()
                .collect(Collectors.toMap(
                        MiddleI18nData::getDataId, o -> JacksonUtil.parseObject(o.getData(), convertClass)
                ));
    }

    @Override
    public <T> Map<Integer, T> getI18nDataMap(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection, String language, Integer version) {
        if (CollectionUtils.isEmpty(idCollection)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<MiddleI18nDataPub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiddleI18nDataPub::getTableName, this.getTableName(entityClass))
                .eq(MiddleI18nDataPub::getVersion, version)
                .eq(MiddleI18nDataPub::getLanguage, language)
                .in(MiddleI18nDataPub::getDataId, idCollection);

        return middleI18nDataPubService.list(queryWrapper).stream()
                .collect(Collectors.toMap(
                        MiddleI18nDataPub::getDataId, o -> JacksonUtil.parseObject(o.getData(), convertClass)
                ));
    }


    @Override
    public <T, R extends BaseModel> Map<Integer, T> setI18nList(Class<?> entityClass, Class<T> convertClass,
                                                                      Collection<R> setList, String language, Integer version,
                                                                      Pair<BiConsumer<R, String>, Function<T, String>>... mappingPairs) {
        if (CollectionUtils.isEmpty(setList)) return new HashMap<>();
        Map<Integer, T> i18nDataMap = this.getI18nDataMap(entityClass, convertClass,
                setList.stream().map(BaseModel::getId).collect(Collectors.toSet()),
                language, version);
        if (i18nDataMap.isEmpty()) return i18nDataMap;
        for (R base : setList) {
            T i18n = i18nDataMap.get(base.getId());
            if (i18n == null || mappingPairs == null ) continue;
            for (Pair<BiConsumer<R, String>, Function<T, String>> pair : mappingPairs) {
                String value = pair.getValue().apply(i18n);
                if (StringUtils.isEmpty(value)) continue;
                pair.getKey().accept(base, value);
            }
        }
        return i18nDataMap;
    }

    @Override
    public <T, R extends BaseModel> Map<Integer, T> setI18nList(Class<?> entityClass, Class<T> convertClass,
                                                                Collection<R> setList, Integer version,
                                                                Pair<BiConsumer<R, String>, Function<T, String>>... mappingPairs) {
        String lang = RequestContextUtils.getLanguage();
        // 数据为空或默认语种不需要处理
        if (org.apache.commons.lang3.StringUtils.isBlank(lang) || Objects.equals(lang, GlobalConstant.DEFAULT_LANGUAGE)
                || setList.isEmpty()) {
            return null;
        }
        return this.setI18nList(entityClass, convertClass, setList, lang, version, mappingPairs);
    }

    /**
     * 获取表名
     *
     * @param entityClass entityClass
     * @return String
     */
    private String getTableName(Class<?> entityClass) {
        String tableName = StringUtils.camelToUnderline(entityClass.getSimpleName());
        if (tableName.endsWith(PUB_TABLE_SUFFIX)) {
            tableName = tableName.substring(0, tableName.length() - PUB_TABLE_SUFFIX.length());
        }
        return tableName;
    }

    @Override
    public Map<String, String> getTextInCodeI18nMap(Set<String> textSet, String language) {
        if (CollectionUtils.isEmpty(textSet)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<MiddleTextInCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MiddleTextInCode::getText, textSet);
        List<MiddleTextInCode> middleTextInCodes = middleTextInCodeService.list(queryWrapper);

        if (!middleTextInCodes.isEmpty()) {
            List<Integer> idList = middleTextInCodes.stream().map(MiddleTextInCode::getId).collect(Collectors.toList());
            LambdaQueryWrapper<MiddleI18nData> queryI8nWrapper = new LambdaQueryWrapper<>();
            queryI8nWrapper.eq(MiddleI18nData::getTableName, this.getTableName(MiddleTextInCode.class))
                    .eq(MiddleI18nData::getLanguage, language)
                    .in(MiddleI18nData::getDataId, idList);
            List<MiddleI18nData> i18nDataList = this.list(queryI8nWrapper);

            Map<Integer, MiddleI18nData> middleI18nDataMap = new HashMap<>();
            for (MiddleI18nData middleI18nData : i18nDataList) {
                middleI18nDataMap.put(middleI18nData.getDataId(), middleI18nData);
            }

            Map<String, String> textInCodeMap = new HashMap<>();
            for (MiddleTextInCode textInCode : middleTextInCodes) {
                MiddleI18nData i18nData = middleI18nDataMap.get(textInCode.getId());
                if (i18nData != null && i18nData.getData() != null) {
                    MiddleTextInCode textInCodeI18n = JacksonUtil.parseObject(i18nData.getData(), MiddleTextInCode.class);
                    textInCodeMap.put(textInCode.getText(), textInCodeI18n.getText());
                } else {
                    textInCodeMap.put(textInCode.getText(), textInCode.getText());
                }
            }

            return textInCodeMap;
        }

        return Collections.emptyMap();
    }

}
