package com.laien.app.common.i18n.client.entity;

import com.laien.mybatisplus.config.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 中台-固定文本
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("middle_text_in_code")
@ApiModel(value="MiddleTextInCode对象", description="中台-固定文本")
public class MiddleTextInCode extends BaseModel {

    private static final long serialVersionUID = 1L;

    private String text;

    @ApiModelProperty(value = "描述")
    private String note;


}
