package com.laien.app.common.i18n.client.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.laien.app.common.i18n.client.entity.MiddleI18nData;
import com.laien.mybatisplus.config.BaseModel;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <p>
 * 中台-本地化数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
public interface IMiddleI18nDataService extends IService<MiddleI18nData> {


    /**
     *
     * 无需发布的数据，获取所有语种
     *
     * @param entityClass 实体对象（原表）（原表）
     * @param convertClass 翻译转换对象
     * @return Map<id, T> map
     * @param <T> T
     */
    <T> Map<Integer, Map<String, T>> getI18nDataGroupList(Class<?> entityClass, Class<T> convertClass);

    /**
     *
     * 无需发布的数据，获取所有语种
     *
     * @param entityClass 实体对象（原表）
     * @param convertClass 翻译转换对象
     * @param idCollection id 列表 为空查询所有
     * @return Map<id, T> map
     * @param <T> T
     */
    <T> Map<Integer, Map<String, T>> getI18nDataGroupList(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection);

    /**
     * 需要发布的业务数据，获取所有语种
     *
     * @param entityClass 实体对象（原表）
     * @param convertClass 翻译转换对象
     * @param idCollection id 列表
     * @param version 版本
     * @return Map<id, T> map
     * @param <T> T
     */
    <T> Map<Integer, Map<String, T>> getI18nDataGroupList(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection, Integer version);

    /**
     *
     * 无需发布的数据
     *
     * @param entityClass 实体对象（原表）
     * @param convertClass 翻译转换对象
     * @param idCollection id 列表
     * @param language 语种
     * @return Map<id, T> map
     * @param <T> T
     */
    <T> Map<Integer, T> getI18nDataMap(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection, String language);

    /**
     * 需要发布的业务数据
     *
     * @param entityClass 实体对象（原表）
     * @param convertClass 翻译转换对象
     * @param idCollection id 列表
     * @param language 语种
     * @param version 版本
     * @return Map<id, T> map
     * @param <T> T
     */
    <T> Map<Integer, T> getI18nDataMap(Class<?> entityClass, Class<T> convertClass, Collection<Integer> idCollection, String language, Integer version);

    /**
     * 设置国际化
     * @param entityClass 实体对象（原表）
     * @param convertClass 翻译转换对象
     * @param setList 要设置的对象
     * @param language 语种
     * @param version 版本
     * @param mapping 映射关系
     * @param <T> T
     * @return
     */
    <T, R extends BaseModel> Map<Integer, T> setI18nList(Class<?> entityClass, Class<T> convertClass,
                                                         Collection<R> setList, String language, Integer version,
                                                         Pair<BiConsumer<R, String>, Function<T, String>>... mapping);

    <T, R extends BaseModel> Map<Integer, T> setI18nList(Class<?> entityClass, Class<T> convertClass,
                                                         Collection<R> setList, Integer version,
                                                         Pair<BiConsumer<R, String>, Function<T, String>>... mappingPairs);

    /**
     *  代码写的字符串，或其他数据库不存在的值需要翻译
     *  根据英文查询对应语言翻译
     *
     * @param textSet textSet
     * @return map
     */
    Map<String, String> getTextInCodeI18nMap(Set<String> textSet, String language);

}
