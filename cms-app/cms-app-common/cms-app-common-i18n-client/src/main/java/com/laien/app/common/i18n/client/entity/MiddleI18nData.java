package com.laien.app.common.i18n.client.entity;

import com.laien.mybatisplus.config.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 中台-本地化数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("middle_i18n_data")
@ApiModel(value="MiddleI18nData对象", description="中台-本地化数据")
public class MiddleI18nData extends BaseModel {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "数据id")
    private Integer dataId;

    @ApiModelProperty(value = "项目id")
    private Integer projId;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "翻译结果")
    private String data;


}
