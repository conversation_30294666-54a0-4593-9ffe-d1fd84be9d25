package com.laien.app.common.i18n.client.service.impl;

import com.laien.app.common.i18n.client.entity.MiddleTextInCode;
import com.laien.app.common.i18n.client.mapper.MiddleTextInCodeMapper;
import com.laien.app.common.i18n.client.service.IMiddleTextInCodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 中台-固定文本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Service
public class MiddleTextInCodeServiceImpl extends ServiceImpl<MiddleTextInCodeMapper, MiddleTextInCode> implements IMiddleTextInCodeService {

}
