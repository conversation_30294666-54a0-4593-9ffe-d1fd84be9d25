package com.laien.app.common.i18n.client.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@Configuration
@MapperScan({"com.laien.app.common.i18n.client.mapper"})
@ComponentScan(value = "com.laien.app.common.i18n.client")
public class I18nClientAutoConfiguration {
}
