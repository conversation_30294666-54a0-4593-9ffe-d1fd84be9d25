
-- proj info
update proj_info set del_flag = 1 where app_code not in ('OOG116','OOG101','OOG104','OOG200');

-- 105
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workout","workout:read","workout:add","workout:update","workout:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workoutVideo105","workoutVideo105:read","workoutVideo105:add","workoutVideo105:update","workoutVideo105:del");

-- 106
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("template106","template106:read","template106:add","template106:update","template106:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("category106","category106:read","category106:add","category106:update","category106:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("buttRegularWorkout","buttRegularWorkout:read","buttRegularWorkout:add","buttRegularWorkout:update","buttRegularWorkout:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("buttProgram","buttProgram:read","buttProgram:add","buttProgram:update","buttProgram:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("category106","category106:read","category106:add","category106:update","category106:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("itemImage","itemImage:read","itemImage:add","itemImage:update","itemImage:del");

-- 111
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("video111Library","video111Library:read","video111Library:add","video111Library:update","video111Library:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("template111","template111:read","template111:add","template111:update","template111:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workoutVideo111","workoutVideo111:read","workoutVideo111:add","workoutVideo111:update","workoutVideo111:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("challenge111","challenge111:read","challenge111:add","challenge111:update","challenge111:del");

-- 117
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("collection","collection:read","collection:add","collection:update","collection:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("danceCollection","danceCollection:read","danceCollection:add","danceCollection:update","danceCollection:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("danceWorkout","danceWorkout:read","danceWorkout:add","danceWorkout:update","danceWorkout:del");

update proj_menu set del_flag = 1 where proj_menu.menu_key in ("category","category:read","category:add","category:update","category:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("dailyimage","dailyimage:read","dailyimage:add","dailyimage:update","dailyimage:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("dailyWorkout","dailyWorkout:read","dailyWorkout:add","dailyWorkout:update","dailyWorkout:del");

update proj_menu set del_flag = 1 where proj_menu.menu_key in ("reminder","reminder:read","reminder:add","reminder:update","reminder:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("quote","quote:read","quote:add","quote:update","quote:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("programCategory","programCategory:read","programCategory:add","programCategory:update","programCategory:del");

update proj_menu set del_flag = 1 where proj_menu.menu_key in ("program","program:read","program:add","program:update","program:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("programWorkout","programWorkout:read","programWorkout:add","programWorkout:update","programWorkout:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("searchTerms","searchTerms:read","searchTerms:add","searchTerms:update","searchTerms:del");

update proj_menu set del_flag = 1 where proj_menu.menu_key in ("todayImage","todayImage:read","todayImage:add","todayImage:update","todayImage:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("custom","custom:read","custom:add","custom:update","custom:del");

-- 118
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workoutVideo","workoutVideo:read","workoutVideo:add","workoutVideo:update","workoutVideo:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workoutScene","workoutScene:read","workoutScene:add","workoutScene:update","workoutScene:del");

-- 120
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workoutVideo120","workoutVideo120:read","workoutVideo120:add","workoutVideo120:update","workoutVideo120:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workout120s","workout120s:read","workout120s:add","workout120s:update","workout120s:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("category120s","category120s:read","category120s:add","category120s:update","category120s:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("template120s","template120s:read","template120s:add","template120s:update","template120s:del");


-- 206
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("workout206","workout206:read","workout206:add","workout206:update","workout206:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("category206","category206:read","category206:add","category206:update","category206:del");
update proj_menu set del_flag = 1 where proj_menu.menu_key in ("template206","template206:read","template206:add","template206:update","template206:del");

-- res
update sys_perms set del_flag = 1 where perms_key like '%res_quote%';
update sys_perms set del_flag = 1 where perms_key like '%res_animation%';
update sys_perms set del_flag = 1 where perms_key like '%res_regular_exercise%';
