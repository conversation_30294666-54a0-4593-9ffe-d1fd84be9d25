-- 插入新增的菜单权限
BEGIN;
set @createTime = now();
set @createUser = '<EMAIL>';
-- 一级菜单：LMS（key：LMS）
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`, `perms_type`,required,`visible`,`status`,create_time,create_user)
VALUES (0, 'LMS', 'LMS', 1,1,1,1,
        @createTime,@createUser);
SET @firstId = LAST_INSERT_ID();

-- 二级菜单：Publish（parent_id = @firstId)
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`,  `perms_type`,required,`visible`,`status`,create_time,create_user)
VALUES (@firstId, 'Publish', 'lms_publish', 1,1,1,1,
        @createTime,@createUser);
SET @secondId = LAST_INSERT_ID();
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`, `path`,`perms_type`,required,`visible`,`status`,create_time,create_user) VALUES
  (@secondId, 'View', 'lms_publish:read', '', 2,1,1,1,
   @createTime,@createUser),
  (@secondId, 'New', 'lms_publish:add', '', 2,1,1,1,
   @createTime,@createUser),
  (@secondId, 'Edit', 'lms_publish:update', '', 2,1,1,1,
   @createTime,@createUser);

-- 二级菜单：`Text/Html`（parent_id = @firstId)
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`,  `perms_type`,required,`visible`,`status`,create_time,create_user)
VALUES (@firstId, 'Text/Html', 'lms_text', 1,1,1,1,
        @createTime,@createUser);
SET @secondId = LAST_INSERT_ID();
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`, `path`,`perms_type`,required,`visible`,`status`,create_time,create_user) VALUES
     (@secondId, 'View', 'lms_text:read', '', 2,1,1,1,
      @createTime,@createUser),
     (@secondId, 'New', 'lms_text:add', '', 2,1,1,1,
      @createTime,@createUser),
     (@secondId, 'Edit', 'lms_text:update', '', 2,1,1,1,
      @createTime,@createUser);

-- 二级菜单：`TTS`（parent_id = @firstId)
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`,  `perms_type`,required,`visible`,`status`,create_time,create_user)
VALUES (@firstId, 'TTS', 'lms_tts', 1,1,1,1,
        @createTime,@createUser);
SET @secondId = LAST_INSERT_ID();
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`, `path`,`perms_type`,required,`visible`,`status`,create_time,create_user) VALUES
       (@secondId, 'View', 'lms_tts:read', '', 2,1,1,1,
        @createTime,@createUser),
       (@secondId, 'New', 'lms_tts:add', '', 2,1,1,1,
        @createTime,@createUser),
       (@secondId, 'Edit', 'lms_tts:update', '', 2,1,1,1,
        @createTime,@createUser);

-- 二级菜单：`TTS Configuration`（parent_id = @firstId)
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`,  `perms_type`,required,`visible`,`status`,create_time,create_user)
VALUES (@firstId, 'TTS Configuration', 'lms_ttsConfig', 1,1,1,1,
        @createTime,@createUser);
SET @secondId = LAST_INSERT_ID();
INSERT INTO `sys_perms` (`parent_id`, `perms_name`, `perms_key`, `path`,`perms_type`,required,`visible`,`status`,create_time,create_user) VALUES
    (@secondId, 'View', 'lms_ttsConfig:read', '', 2,1,1,1,
     @createTime,@createUser),
    (@secondId, 'New', 'lms_ttsConfig:add', '', 2,1,1,1,
     @createTime,@createUser),
    (@secondId, 'Edit', 'lms_ttsConfig:update', '', 2,1,1,1,
     @createTime,@createUser);

COMMIT;
