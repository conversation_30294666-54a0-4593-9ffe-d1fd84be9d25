ALTER TABLE proj_info ADD COLUMN `text_languages` varchar(255) DEFAULT NULL COMMENT '文本语言，多个用英文逗号分隔' AFTER `languages`;


ALTER TABLE proj_sevenm_exercise_video
    ADD COLUMN `core_voice_config_i18n_id` int DEFAULT NULL COMMENT 'i18n配置的语音' AFTER `event_name`;
update proj_sevenm_exercise_video set core_voice_config_i18n_id=-1 where gender=1;
update proj_sevenm_exercise_video set core_voice_config_i18n_id=-2 where gender=2;

#leon
#101sound 表建表 SQL
CREATE TABLE IF NOT EXISTS `proj_sevenm_sound` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `table_code` tinyint DEFAULT '0' COMMENT '表表示',
                                     `proj_id` int DEFAULT NULL COMMENT '项目 ID',
                                     `core_voice_config_i18n_id` int DEFAULT NULL COMMENT 'i18n配置的语音',
                                     `sound_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音名称',
                                     `sound_type` tinyint DEFAULT NULL COMMENT '声音类型（关联字典表）',
                                     `sound_sub_type` tinyint DEFAULT NULL COMMENT '声音类型 子类型',
                                     `sound_script` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音脚本',
                                     `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音',
                                     `duration` int DEFAULT NULL COMMENT '时间',
                                     `gender` tinyint DEFAULT '1' COMMENT '性别',
                                     `status` tinyint DEFAULT '0' COMMENT '状态',
                                     `need_translation` tinyint NOT NULL DEFAULT '0' COMMENT '是否需要翻译',
                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                     `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                     `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='101 声音表';

#菜单配置------
BEGIN;
SET @menuId = 0;
SET @operator = '<EMAIL>';
SET @menuName:='7M Sound';
SET @urlStart:='sevenmSound';
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
COMMIT;


SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for core_business_task_relation_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `core_business_task_relation_i18n` (
                                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                    `core_text_task_i18n_id` int NOT NULL COMMENT '配置的英语音色id',
                                                    `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语种',
                                                    `business_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务名',
                                                    `business_field` varchar(127) NOT NULL COMMENT '业务字段',
                                                    `business_data_id` int NOT NULL COMMENT '业务数据id',
                                                    `app_code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'app code',
                                                    `update_flag` tinyint NOT NULL DEFAULT '1' COMMENT '修改标识,true：修改过，false：未修改过',
                                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                                    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    PRIMARY KEY (`id`) USING BTREE,
                                                    UNIQUE KEY `uk_code_task_language_business` (`app_code`,`core_text_task_i18n_id`,`language`,`business_name`,`business_field`,`business_data_id`) USING BTREE,
                                                    KEY `idx_core_text_task_i18n_id` (`core_text_task_i18n_id`),
                                                    KEY `idx_update_flag` (`update_flag`),
                                                    KEY `idx_business_data_id` (`business_data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_business_task_relation_i18n';

-- ----------------------------
-- Table structure for core_lms_publish_log
-- ----------------------------
CREATE TABLE IF NOT EXISTS `core_lms_publish_log` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `version` int NOT NULL COMMENT '版本',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `result` varchar(30) DEFAULT NULL COMMENT '发布结果',
                                        `core_business_task_relation_i18ns` json DEFAULT NULL COMMENT 'core_business_task_relation_i18ns',
                                        `text_core_business_task_relation_i18ns` json DEFAULT NULL COMMENT 'text_core_business_task_relation_i18ns',
                                        `fail_reason` varchar(3000) DEFAULT NULL COMMENT '失败原因',
                                        `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LMS发布日志';

-- ----------------------------
-- Table structure for core_speech_task_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS  `core_speech_task_i18n` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `core_voice_config_i18n_id` int NOT NULL COMMENT '配置的英语音色id',
                                         `core_voice_template_i18n_id` int NOT NULL COMMENT '多语言音色配置id',
                                         `core_text_task_i18n_id` int NOT NULL COMMENT 'core_text_task_i18n id',
                                         `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '翻译语种',
                                         `core_voice_template_i18n_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'core_voice_template_i18n_name',
                                         `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文',
                                         `text_md5` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文md5',
                                         `audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文音频',
                                         `duration` int DEFAULT NULL COMMENT '原文音频时长',
                                         `retry_count` int NOT NULL DEFAULT '0' COMMENT '原文音频时长',
                                         `translation_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '译文',
                                         `translation_text_md5` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '译文md5',
                                         `translation_audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '译文音频',
                                         `translation_duration` int DEFAULT NULL COMMENT '译文音频时长',
                                         `reduced` tinyint NOT NULL COMMENT '是否ai缩减',
                                         `audit_type` tinyint NOT NULL DEFAULT '1' COMMENT '审核类型',
                                         `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别',
                                         `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                         `check_status` tinyint NOT NULL DEFAULT '0' COMMENT '校验状态',
                                         `proj_code` int NOT NULL COMMENT 'proj code的和',
                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         UNIQUE KEY `uk_language_md5_config_id_template_id_code` (`language`,`text_md5`,`core_voice_config_i18n_id`,`core_voice_template_i18n_id`,`proj_code`),
                                         KEY `idx_language_text_md5` (`language`,`text_md5`),
                                         KEY `idx_status_update_time` (`status`,`update_time`) USING BTREE,
                                         KEY `idx_config_id` (`core_voice_config_i18n_id`) USING BTREE,
                                         KEY `idx_proj_code_language` (`proj_code`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_speech_task_i18n';

-- ----------------------------
-- Table structure for core_speech_task_i18n_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS `core_speech_task_i18n_pub` (
                                             `version` int NOT NULL COMMENT '版本',
                                             `id` int unsigned NOT NULL COMMENT 'id',
                                             `core_voice_config_i18n_id` int NOT NULL COMMENT '配置的英语音色id',
                                             `core_voice_template_i18n_id` int NOT NULL COMMENT '多语言音色配置id',
                                             `core_text_task_i18n_id` int NOT NULL COMMENT 'core_text_task_i18n id',
                                             `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '翻译语种',
                                             `core_voice_template_i18n_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'core_voice_template_i18n_name',
                                             `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文',
                                             `text_md5` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文md5',
                                             `audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文音频',
                                             `duration` int DEFAULT NULL COMMENT '原文音频时长',
                                             `retry_count` int NOT NULL DEFAULT '0' COMMENT '原文音频时长',
                                             `translation_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '译文',
                                             `translation_text_md5` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '译文md5',
                                             `translation_audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '译文音频',
                                             `translation_duration` int NOT NULL COMMENT '译文音频时长',
                                             `reduced` tinyint NOT NULL COMMENT '是否ai缩减',
                                             `audit_type` tinyint NOT NULL COMMENT '审核类型',
                                             `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别',
                                             `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                             `check_status` tinyint NOT NULL DEFAULT '0' COMMENT '校验状态',
                                             `proj_code` int NOT NULL COMMENT 'proj code的和',
                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                             `create_time` datetime NOT NULL COMMENT '创建时间',
                                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             PRIMARY KEY (`version`,`id`),
                                             KEY `idx_language_template_i18n_id_config_id_md5` (`version`,`language`,`core_voice_template_i18n_id`,`core_voice_config_i18n_id`,`text_md5`),
                                             KEY `idx_language_code_type_md5` (`version`,`text_md5`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_speech_task_i18n_pub';

-- ----------------------------
-- Table structure for core_text_task_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS  `core_text_task_i18n` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                       `type` tinyint NOT NULL COMMENT '文本翻译类型',
                                       `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '翻译语种',
                                       `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文',
                                       `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                       `check_status` tinyint NOT NULL DEFAULT '0' COMMENT '校验状态',
                                       `audit_type` tinyint NOT NULL DEFAULT '1' COMMENT '审核类型',
                                       `text_md5` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文md5',
                                       `translation_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '译文',
                                       `translation_text_md5` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '译文',
                                       `proj_code` int NOT NULL DEFAULT '1' COMMENT 'proj code的和',
                                       `retry_count` int NOT NULL DEFAULT '0' COMMENT '原文音频时长',
                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       UNIQUE KEY `uk_language_md5_type_code` (`language`,`text_md5`,`type`,`proj_code`),
                                       KEY `idx_status_update_time` (`status`,`update_time`) USING BTREE,
                                       KEY `idx_language_code_md5_type` (`language`,`text_md5`,`type`) USING BTREE,
                                       KEY `idx_proj_code_language` (`proj_code`,`language`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_text_task_i18n';

-- ----------------------------
-- Table structure for core_text_task_i18n_pub
-- ----------------------------
CREATE TABLE IF NOT EXISTS  `core_text_task_i18n_pub` (
                                           `version` int NOT NULL COMMENT '版本',
                                           `id` int unsigned NOT NULL COMMENT 'id',
                                           `type` tinyint NOT NULL COMMENT '文本翻译类型',
                                           `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '翻译语种',
                                           `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文',
                                           `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                           `check_status` tinyint NOT NULL DEFAULT '0' COMMENT '校验状态',
                                           `audit_type` tinyint NOT NULL COMMENT '审核类型',
                                           `text_md5` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原文md5',
                                           `translation_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '译文',
                                           `translation_text_md5` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '译文md5',
                                           `proj_code` int NOT NULL COMMENT 'proj code的和',
                                           `retry_count` int NOT NULL DEFAULT '0' COMMENT '原文音频时长',
                                           `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                           `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                           `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           PRIMARY KEY (`version`,`id`),
                                           KEY `idx_version_language_code_md5_type` (`version`,`language`,`text_md5`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_text_task_i18n_pub';

-- ----------------------------
-- Table structure for core_voice_config_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `core_voice_config_i18n` (
                                          `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置名称',
                                          `gender` tinyint NOT NULL COMMENT '性别',
                                          `audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audio url',
                                          `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                          `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                          `create_time` datetime NOT NULL COMMENT '创建时间',
                                          `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_text_task_i18n';

-- ----------------------------
-- Table structure for core_voice_config_template_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `core_voice_config_template_i18n` (
                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `core_voice_config_i18n_id` int NOT NULL COMMENT 'core_voice_config_i18n_id',
                                                   `core_voice_template_i18n_id` int NOT NULL COMMENT 'core_voice_template_i18n_id',
                                                   `gender` tinyint NOT NULL COMMENT '性别',
                                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   PRIMARY KEY (`id`) USING BTREE,
                                                   KEY `uk_config_id_template_id` (`core_voice_config_i18n_id`,`core_voice_template_i18n_id`) USING BTREE,
                                                   KEY `idx_core_voice_template_i18n_id` (`core_voice_template_i18n_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_voice_config_template_i18n';

-- ----------------------------
-- Table structure for core_voice_template_i18n
-- ----------------------------
CREATE TABLE IF NOT EXISTS `core_voice_template_i18n` (
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `language` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '语种',
                                            `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置名称',
                                            `gender` tinyint NOT NULL COMMENT '性别',
                                            `audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'audio url',
                                            `default_voice` tinyint NOT NULL DEFAULT '0' COMMENT '是否对应语言+性别的默认音色',
                                            `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                            `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            KEY `idx_language_gender` (`language`,`gender`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='core_voice_template_i18n';

SET FOREIGN_KEY_CHECKS = 1;
