
ALTER TABLE res_yoga_video
    ADD COLUMN `core_voice_config_i18n_id` int DEFAULT -1 COMMENT 'i18n配置的语音' AFTER `event_name`;

ALTER TABLE proj_chair_yoga_video
    ADD COLUMN `core_voice_config_i18n_id` int DEFAULT -1 COMMENT 'i18n配置的语音' AFTER `event_name`;

ALTER TABLE proj_wall_pilates_video
    ADD COLUMN `core_voice_config_i18n_id` int DEFAULT -1 COMMENT 'i18n配置的语音' AFTER `event_name`;

ALTER TABLE proj_yoga_pose_video
    ADD COLUMN `core_voice_config_i18n_id` int DEFAULT -1 COMMENT 'i18n配置的语音' AFTER `event_name`;

ALTER TABLE res_transition
    ADD COLUMN `core_voice_config_i18n_id` int DEFAULT -1 COMMENT 'i18n配置的语音' AFTER `name`;

update middle_i18n_config set del_flag = 1 where proj_id = 2;

update i18n_translation_table set del_flag = 1
where table_name in ('res_pose_library', 'proj_yoga_regular_workout', 'proj_collection_class', 'proj_collection_teacher', 'res_video_class', 'res_yoga_video');
