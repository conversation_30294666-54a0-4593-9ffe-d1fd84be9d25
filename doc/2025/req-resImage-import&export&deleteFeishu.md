移除 cms 关于飞书的配置文件
```
feishu:
  api:
    domain: https://open.feishu.cn
    app-id: cli_a647fba275aad00c
    app-secret: rN6VGejQjMzzVU5K5YSbAbXhAHjyf22M
    auth:
      tenant-access-token-url: ${feishu.api.domain}/open-apis/auth/v3/tenant_access_token/internal
    bitable:
      tables-url: ${feishu.api.domain}/open-apis/bitable/v1/apps/:app_token/tables
      fields-url: ${feishu.api.domain}/open-apis/bitable/v1/apps/:app_token/tables/:table_id/fields
      records-url: ${feishu.api.domain}/open-apis/bitable/v1/apps/:app_token/tables/:table_id/records
      records-search-url: ${feishu.api.bitable.records-url}/search
      records-batch-create-url: ${feishu.api.bitable.records-url}/batch_create
    apps:
      test:
        app-id: cli_a647fba275aad00c
        app-secret: rN6VGejQjMzzVU5K5YSbAbXhAHjyf22M
        auth:
          tenant-access-token-url: ${feishu.api.domain}/open-apis/auth/v3/tenant_access_token/internal
        bitable:
          tables-url: ${feishu.api.domain}/open-apis/bitable/v1/apps/:app_token/tables
          fields-url: ${feishu.api.domain}/open-apis/bitable/v1/apps/:app_token/tables/:table_id/fields
          records-url: ${feishu.api.domain}/open-apis/bitable/v1/apps/:app_token/tables/:table_id/records
          records-search-url: ${feishu.api.bitable.records-url}/search
          records-batch-create-url: ${feishu.api.bitable.records-url}/batch_create
```