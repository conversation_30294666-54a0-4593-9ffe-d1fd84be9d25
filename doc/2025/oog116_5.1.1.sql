
ALTER TABLE proj_template116 ADD COLUMN `template_type` int DEFAULT '100' COMMENT 'template_type, 100:normal;200:tai_chi,300:gentle_chair_yoga' AFTER `day`;

ALTER TABLE proj_template116_pub ADD COLUMN `template_type` int DEFAULT '100' COMMENT 'template_type, 100:normal;200:tai_chi,300:gentle_chair_yoga' AFTER `day`;

ALTER TABLE proj_workout116_res_video116 ADD COLUMN `circuit_video_duration` varchar(127) DEFAULT NULL COMMENT '一个video每一轮的时长，英文逗号分隔' AFTER `res_video116_id`;

ALTER TABLE proj_workout116_res_video116_pub ADD COLUMN `circuit_video_duration` varchar(127) DEFAULT NULL COMMENT '一个video每一轮的时长，英文逗号分隔' AFTER `res_video116_id`;

ALTER TABLE proj_workout116_generate_res_video116 ADD COLUMN `circuit_video_duration` varchar(127) DEFAULT NULL COMMENT '一个video每一轮的时长，英文逗号分隔' AFTER `res_video116_id`;

ALTER TABLE proj_workout116_generate_res_video116 ADD COLUMN `preview_duration` int DEFAULT NULL COMMENT 'workout生成时的 preview video时长' AFTER `res_video116_id`;

CREATE TABLE `proj_coach116` (
                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                 `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'coach name',
                                 `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'cover_img_url',
                                 `introduction` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'introduction',
                                 `status` tinyint NOT NULL DEFAULT '0' COMMENT '启用状态 1启用 2停用',
                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                 `proj_id` int NOT NULL COMMENT 'project id',
                                 `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='proj_coach116';

CREATE TABLE `proj_coach116_pub` (
                                     `version` int NOT NULL COMMENT '版本',
                                     `id` int unsigned NOT NULL COMMENT 'id',
                                     `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'coach name',
                                     `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'cover_img_url',
                                     `introduction` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'introduction',
                                     `status` tinyint NOT NULL DEFAULT '0' COMMENT '启用状态 1启用 2停用',
                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                     `proj_id` int NOT NULL COMMENT 'project id',
                                     `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                     `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                     PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='proj_coach116';


CREATE TABLE `proj_program116` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                   `event_name` varchar(127) DEFAULT NULL COMMENT '流程名称',
                                   `equipment` varchar(127) DEFAULT NULL COMMENT '器材',
                                   `goals` varchar(127) DEFAULT NULL COMMENT 'goals',
                                   `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
                                   `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
                                   `description` varchar(1023) DEFAULT NULL COMMENT '描述',
                                   `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                   `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                   `coach_id` int  NOT NULL COMMENT 'proj_coach116 id',
                                   `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                   `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                   `proj_id` int NOT NULL COMMENT '项目id',
                                   `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                   `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj program116 ';

CREATE TABLE `proj_program116_pub` (
                                       `version` int NOT NULL COMMENT '版本',
                                       `id` int unsigned NOT NULL COMMENT 'id',
                                       `name` varchar(127) NOT NULL COMMENT '动作展示名称',
                                       `event_name` varchar(127) DEFAULT NULL COMMENT '流程名称',
                                       `equipment` varchar(127) DEFAULT NULL COMMENT '器材',
                                       `goals` varchar(127) DEFAULT NULL COMMENT 'goals',
                                       `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
                                       `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
                                       `description` varchar(1023) DEFAULT NULL COMMENT '描述',
                                       `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
                                       `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
                                       `coach_id` int  NOT NULL COMMENT 'proj_coach116 id',
                                       `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                       `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                       `proj_id` int NOT NULL COMMENT '项目id',
                                       `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' proj program116';

CREATE TABLE `proj_program116_relation` (
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `proj_program116_id` int NOT NULL COMMENT 'proj_program116 id',
                                            `proj_workout116_id` int NOT NULL COMMENT 'proj_workout116 id',
                                            `proj_id` int NOT NULL COMMENT '项目id',
                                            `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                            `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' program116 and workout116 relation';


CREATE TABLE `proj_program116_relation_pub` (
                                                `version` int NOT NULL COMMENT '版本',
                                                `id` int unsigned NOT NULL COMMENT 'id',
                                                `proj_program116_id` int NOT NULL COMMENT 'proj_program116 id',
                                                `proj_workout116_id` int NOT NULL COMMENT 'proj_workout116 id',
                                                `proj_id` int NOT NULL COMMENT '项目id',
                                                `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1 已删除',
                                                `create_user` varchar(63) NOT NULL COMMENT '创建人',
                                                `create_time` datetime NOT NULL COMMENT '创建时间',
                                                `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT=' program116 and workout116 relation';



