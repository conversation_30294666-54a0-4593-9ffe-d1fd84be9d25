# 增加 Nacos 配置
# (已有的配置key不用重复增加，加最后不存在的级别)

在 cms-firebase.yaml 配置文件中，新增 Firebase 配置项；

```
firebase:
  bucket:
    file-dirs:          
      #104 sound 功能
      core-oog104-fitnessSound-mp3: /core/oog104/fitnessSound/mp3/
      #116 sound 功能
      core-oog116-sound116-mp3: /core/oog116/sound116/mp3/
      core-oog200-sound200-mp3: /core/oog200/sound200/mp3/                      
```
cms.yaml 配置文件中,新增 log 配置
```
operation-log:
 biz-table:
  proj_fitness_sound:
   biz_type: cms:proj:fitnessSound
   data-name-field: name
  proj_sound116:
   biz_type: cms:proj:sound116
   data-name-field: name
  proj_sound:
   biz_type: cms:proj:sound
   data-name-field: name
```
修改 116 sound 配置文件
```
cms:
 biz:
  oog116:
     femaleSoundConfig:
      first: sys116_firstup_female
      threeTwoOne: sys116_321_female
      go: sys116_Go_female
      rest: sys116_Rest_female
      next: sys116_Nextup_female
      finish: sys116_LastOne_female
      fiveFourThreeTwoOne: sys116_beepbeepbeep_female
      halfwayList:
        - sys116_15sPrompt1_female
        - sys116_15sPrompt2_female
        - sys116_15sPrompt3_female
        - sys116_15sPrompt4_female
        - sys116_15sPrompt5_female
        - sys116_15sPrompt6_female
        - sys116_15sPrompt7_female
        - sys116_15sPrompt8_female
        - sys116_15sPrompt9_female
        - sys116_15sPrompt10_female
        - sys116_15sPrompt11_female
     maleSoundConfig:
      first: sys116_firstup_male
      threeTwoOne: sys116_321_male
      go: sys116_Go_male
      rest: sys116_Rest_male
      next: sys116_Nextup_male
      finish: sys116_LastOne_male
      fiveFourThreeTwoOne: sys116_beepbeepbeep_male
      halfwayList:
          - sys116_15sPrompt1_male
          - sys116_15sPrompt2_male
          - sys116_15sPrompt3_male
          - sys116_15sPrompt4_male
          - sys116_15sPrompt5_male
          - sys116_15sPrompt6_male
          - sys116_15sPrompt7_male
          - sys116_15sPrompt8_male
          - sys116_15sPrompt9_male
          - sys116_15sPrompt10_male
          - sys116_15sPrompt11_male
     cardio105:
      femaleSoundConfig:
        first: sys116_105Cardio_Firstup_female
        threeTwoOne: sys116_105Cardio_321_female
        go: sys116_105Cardio_Go_female
        rest: sys116_105Cardio_Rest_female
        next: sys116_105Cardio_Nextup_female
        finish: sys116_105Cardio_LastOne_female
        fiveFourThreeTwoOne: sys116_105Cardio_beep54321_female
        halfwayList:
            - sys116_105Cardio_15sPrompt1_female
            - sys116_105Cardio_15sPrompt2_female
            - sys116_105Cardio_15sPrompt3_female
      maleSoundConfig:
        first: sys116_105Cardio_Firstup_male
        threeTwoOne: sys116_105Cardio_321_male
        go: sys116_105Cardio_Go_male
        rest: sys116_105Cardio_Rest_male
        next: sys116_105Cardio_Nextup_male
        finish: sys116_105Cardio_LastOne_male
        fiveFourThreeTwoOne: sys116_105Cardio_beep54321_male
        halfwayList:
            - sys116_105Cardio_15sPrompt1_male
            - sys116_105Cardio_15sPrompt2_male
            - sys116_105Cardio_15sPrompt3_male
     taichi:
      femaleSoundConfig:
        first: Tai Chi_First_female
        next: Tai Chi_Next_female
        last: Tai Chi_Last_female
        promptList4Second:
            - Tai Chi_Circuit 2 Prompt_7_female
            - Tai Chi_Circuit 2 Prompt_6_female
            - Tai Chi_Circuit 2 Prompt_5_female
            - Tai Chi_Circuit 2 Prompt_4_female
            - Tai Chi_Circuit 2 Prompt_3_female
            - Tai Chi_Circuit 2 Prompt_2_female
            - Tai Chi_Circuit 2 Prompt_1_female
        startList4First:
            - Tai Chi_Circuit1_Start_1_female
            - Tai Chi_Circuit1_Start_2_female
            - Tai Chi_Circuit1_Start_3_female

        startList4Second:
            - Tai Chi_Circuit 3 Start_4_female
            - Tai Chi_Circuit 3 Start_3_female
           # - Tai Chi_Circuit 3 Start_2_female
            - Tai Chi_Circuit 3 Start_1_female
      maleSoundConfig:
        first: Tai Chi_First_male
        next: Tai Chi_Next_male
        last: Tai Chi_Last_male
        promptList4Second:
            - Tai Chi_Circuit 2 Prompt_7_male
            - Tai Chi_Circuit 2 Prompt_6_male
            - Tai Chi_Circuit 2 Prompt_5_male
            - Tai Chi_Circuit 2 Prompt_4_male
            - Tai Chi_Circuit 2 Prompt_3_male
            - Tai Chi_Circuit 2 Prompt_2_male
            - Tai Chi_Circuit 2 Prompt_1_male
        startList4First:
            - Tai Chi_Circuit1_Start_1_male
            - Tai Chi_Circuit1_Start_2_male
            - Tai Chi_Circuit1_Start_3_male

        startList4Second:
            - Tai Chi_Circuit 3 Start_4_male
            - Tai Chi_Circuit 3 Start_3_male
            # - Tai Chi_Circuit 3 Start_2_male
            - Tai Chi_Circuit 3 Start_1_male
     chair-yoga:
      femaleSoundConfig:
        first: sys_Chair First Up_200_v7.2.0_female
        next: sys_Chair The Next Is_200_v7.2.0_female
        last: sys_Chair Last One_200_v7.2.0_female
        chairYogaVideo4Seated: 1132
        chairYogaVideo4Standing: 1132
        startList4First:
            - sys_Chairyoga_Circuit_1_Start_3_female
            - sys_Chairyoga_Circuit_1_Start_2_female
            - sys_Chairyoga_Circuit_1_Start_1_female
      maleSoundConfig:
        first: sys_Chair First Up_200_v7.2.0_male
        next: sys_Chair The Next Is_200_v7.2.0_male
        last: sys_Chair Last One_200_v7.2.0_male
        chairYogaVideo4Seated: 1132
        chairYogaVideo4Standing: 1132
        startList4First:
            - sys_Chairyoga_Circuit_1_Start_3_male
            - sys_Chairyoga_Circuit_1_Start_2_male
            - sys_Chairyoga_Circuit_1_Start_1_male
```
添加常量翻译后,需要调用后端接口
```
/core/i18n/constantTranslate
```