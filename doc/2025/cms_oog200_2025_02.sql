alter table res_video_class add column video2532_m3u8_url varchar(255) DEFAULT NULL COMMENT '2532视频m3u8地址';

alter table res_video_class add column video_m3u8_url varchar(255) DEFAULT NULL COMMENT '多分辨率视频m3u8地址';

BEGIN;
SET @menuName:='Yoga Playlist';
SET @urlStart:='yogaPlaylist';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Yoga Music';
SET @urlStart:='yogaMusic';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

CREATE TABLE `proj_yoga_music` (
       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
       `music_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '音乐名称',
       `audio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音频',
       `audio_duration` int DEFAULT '0' COMMENT '音频总时长',
       `music_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '音乐类型',
       `instructor` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '讲述者，用于Meditation类型',
       `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
       `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
       `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Music状态 0草稿 1启用 2停用',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `proj_id` int NOT NULL COMMENT '项目id',
       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
       `create_time` datetime NOT NULL COMMENT '创建时间',
       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='音乐表';


CREATE TABLE `proj_yoga_music_pub` (
       `version` int NOT NULL COMMENT '版本',
       `id` int unsigned NOT NULL COMMENT 'id',
       `music_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '音乐名称',
       `audio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音频',
       `audio_duration` int DEFAULT '0' COMMENT '音频总时长',
       `music_type` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '音乐类型',
       `instructor` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '讲述者，用于Meditation类型',
       `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
       `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
       `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Music状态 0草稿 1启用 2停用',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `proj_id` int NOT NULL COMMENT '项目id',
       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
       `create_time` datetime NOT NULL COMMENT '创建时间',
       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
       PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='音乐表';



CREATE TABLE `proj_yoga_playlist` (
      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
      `playlist_type` int unsigned NOT NULL COMMENT 'playlist type, 101: Plan_Classic and Chair，102: Animation, 103: Meditation, 104: Soundscape, 105: Pose Library',
      `playlist_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '列表名称',
      `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机封面图',
      `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板封面图',
      `phone_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机详情图',
      `tablet_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板详情图',
      `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
      `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
      `proj_id` int NOT NULL COMMENT '项目id',
      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
      `create_time` datetime NOT NULL COMMENT '创建时间',
      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
      `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 启用 2 禁用',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目播放列表表';


CREATE TABLE `proj_yoga_playlist_pub` (
      `version` int NOT NULL COMMENT '版本',
      `id` int unsigned NOT NULL COMMENT 'id',
      `playlist_type` int unsigned NOT NULL COMMENT 'playlist type, 101: Plan_Classic and Chair，102: Animation, 103: Meditation, 104: Soundscape, 105: Pose Library',
      `playlist_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '列表名称',
      `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机封面图',
      `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板封面图',
      `phone_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机详情图',
      `tablet_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板详情图',
      `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
      `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
      `proj_id` int NOT NULL COMMENT '项目id',
      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
      `create_time` datetime NOT NULL COMMENT '创建时间',
      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
      `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 启用 2 禁用',
      PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目播放列表表';



CREATE TABLE `proj_yoga_playlist_relation` (
       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
       `proj_yoga_playlist_id` int NOT NULL COMMENT '播放列表id',
       `proj_yoga_music_id` int NOT NULL COMMENT '音乐id',
       `display_name` varchar(127) DEFAULT NULL COMMENT 'display_name',
       `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
       `short_link` varchar(127) DEFAULT NULL COMMENT 'app短连接',
       `proj_id` int DEFAULT NULL COMMENT 'proj_id',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
       `create_time` datetime NOT NULL COMMENT '创建时间',
       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表';



CREATE TABLE `proj_yoga_playlist_relation_pub` (
       `version` int NOT NULL COMMENT '版本',
       `id` int unsigned NOT NULL COMMENT 'id',
       `proj_yoga_playlist_id` int NOT NULL COMMENT '播放列表id',
       `proj_yoga_music_id` int NOT NULL COMMENT '音乐id',
       `display_name` varchar(127) DEFAULT NULL COMMENT 'display_name',
       `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
       `short_link` varchar(127) DEFAULT NULL COMMENT 'app短连接',
       `proj_id` int DEFAULT NULL COMMENT 'proj_id',
       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
       `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
       `create_time` datetime NOT NULL COMMENT '创建时间',
       `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
       PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表';

#  执行前需要检查authInfoId,projId是否正确
# SET @authInfoId:=3;
# SET @projId:=2;

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_music', 'instructor', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_yoga_playlist', 'playlist_name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

# SET @projId:=2;
INSERT INTO cms.proj_yoga_music
(id, music_name, audio, audio_duration, music_type, cover_img_url, detail_img_url, status, proj_id, del_flag, create_user, create_time, update_user, update_time)
select id, music_name, audio, audio_duration, music_type, cover_img_url, detail_img_url, status, @projId, del_flag, create_user, create_time, update_user, update_time
from cms.res_music where del_flag = 0;

INSERT INTO cms.proj_yoga_playlist_relation
(id, proj_yoga_playlist_id, proj_yoga_music_id, display_name, subscription, short_link, proj_id, del_flag, create_user, create_time, update_user, update_time)
SELECT id, proj_playlist_id, res_music_id, display_name, subscription, short_link, proj_id, del_flag, create_user, create_time, update_user, update_time
FROM cms.proj_playlist_music
WHERE del_flag = 0 and proj_id = @projId ;

INSERT INTO cms.proj_yoga_playlist
(id, playlist_type, playlist_name, phone_cover_img_url, tablet_cover_img_url, phone_detail_img_url, tablet_detail_img_url, subscription, sort_no, proj_id, del_flag, create_user, create_time, update_user, update_time, status)
SELECT
    id,
    CASE
        WHEN playlist_type = 'Plan_Classic and Chair' THEN 101
        WHEN playlist_type = 'Animation' THEN 102
        WHEN playlist_type = 'Meditation' THEN 103
        WHEN playlist_type = 'Soundscape' THEN 104
        WHEN playlist_type = 'Pose Library' THEN 105
        END as playlist_type,
    playlist_name, phone_cover_img_url, tablet_cover_img_url, phone_detail_img_url, tablet_detail_img_url, subscription, sort_no, proj_id, del_flag, create_user, create_time, update_user, update_time, status
FROM cms.proj_playlist
WHERE del_flag = 0 and proj_id = @projId;
