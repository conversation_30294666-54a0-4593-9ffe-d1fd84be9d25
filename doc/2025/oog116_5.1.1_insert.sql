BEGIN;
SET @menuName:='Coach 116';
SET @urlStart:='coach116';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

BEGIN;
SET @menuName:='Program 116';
SET @urlStart:='program116';
SET @menuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, 'admin', now(), NULL, NULL, 0);
COMMIT;
ROLLBACK;

#  执行前需要检查authInfoId,projId是否正确
# SET @authInfoId:=1;
# SET @projId:=11;
# SET @lang:='de,es,fr,it,ja,pt';
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_coach116', 'name', @authInfoId, @projId, 1, 1, @lang, NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_coach116', 'introduction', @authInfoId, @projId, 1, 1, @lang, NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_program116', 'name', @authInfoId, @projId, 1, 1, @lang, NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_program116', 'equipment', @authInfoId, @projId, 1, 1, @lang, NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_program116', 'goals', @authInfoId, @projId, 1, 1, @lang, NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_program116', 'description', @authInfoId, @projId, 1, 1, @lang, NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

