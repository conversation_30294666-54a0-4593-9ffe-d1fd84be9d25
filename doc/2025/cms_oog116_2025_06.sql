ALTER TABLE proj_workout116_res_video116 ADD COLUMN `circuit` tinyint DEFAULT NULL COMMENT 'Video播放轮数,目前仅针对TaiChi类型Video,可选值 1、2、3、4、5' AFTER `res_video116_id`;

ALTER TABLE proj_workout116_res_video116 ADD COLUMN `res_video_duration` int DEFAULT NULL COMMENT 'workout生成时的video时长' AFTER `res_video116_id`;

ALTER TABLE proj_workout116_res_video116_pub ADD COLUMN `circuit` tinyint DEFAULT NULL COMMENT 'Video播放轮数,目前仅针对TaiChi类型Video,可选值 1、2、3、4、5' AFTER `res_video116_id`;

ALTER TABLE proj_workout116_res_video116_pub ADD COLUMN `res_video_duration` int DEFAULT NULL COMMENT 'workout生成时的video时长' AFTER `res_video116_id`;

#  执行前需要检查authInfoId 是否正确
# SET @authInfoId:=1;
# SET @res_image:='res_image';
update middle_i18n_config set languages = CONCAT(languages, ',ja,pt') where auth_info_id = @authInfoId;
update middle_i18n_config set languages = CONCAT(languages, ',ja,pt') where table_name = @res_image;

