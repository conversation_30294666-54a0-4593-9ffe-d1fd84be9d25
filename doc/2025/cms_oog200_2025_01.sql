#  执行前需要检查authInfoId,projId是否正确
# SET @authInfoId:=3;
# SET @projId:=2;

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_meal_plan', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_meal_plan', 'description', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_meal_plan', 'keywords', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_dish_collection', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_dish_collection', 'description', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);
INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_dish_collection', 'keywords', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_dish', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_dish_step', 'description', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_dish_step_tip', 'intro', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_allergen', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_ingredient', 'name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

INSERT INTO middle_i18n_config (`table_name`, `column_name`, `auth_info_id`, `proj_id`, `translation_type`, `type`,
                                `languages`, `speeches`, `old_data_flag`, `del_flag`, `create_user`,
                                `create_time`, `update_user`, `update_time`)
VALUES ('proj_fasting_article', 'title_name', @authInfoId, @projId, 1, 1, 'ar,da,de,es,fr,id,it,ja,ko,nl,pt,ru,sv,th,tr,vi,zh', NULL, 1, 0, 'hhl', CURRENT_TIMESTAMP, null,NULL);

