#leon
#104sound 表建表 SQL
CREATE TABLE IF NOT EXISTS `proj_fitness_sound` (
                                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `table_code` tinyint DEFAULT '0' COMMENT '表表示',
                                                   `proj_id` int DEFAULT NULL COMMENT '项目 ID',
                                                   `core_voice_config_i18n_id` int DEFAULT NULL COMMENT 'i18n配置的语音',
                                                   `sound_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音名称',
    `sound_type` tinyint DEFAULT NULL COMMENT '声音类型（关联字典表）',
    `sound_sub_type` tinyint DEFAULT NULL COMMENT '声音类型 子类型',
    `sound_script` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音脚本',
    `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音',
    `duration` int DEFAULT NULL COMMENT '时间',
    `gender` tinyint DEFAULT '1' COMMENT '性别',
    `status` tinyint DEFAULT '0' COMMENT '状态',
    `need_translation` tinyint NOT NULL DEFAULT '0' COMMENT '是否需要翻译',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='104 声音表';

#116sound 表建表 SQL
CREATE TABLE IF NOT EXISTS `proj_sound116` (
                                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                    `table_code` tinyint DEFAULT '0' COMMENT '表表示',
                                                    `proj_id` int DEFAULT NULL COMMENT '项目 ID',
                                                    `core_voice_config_i18n_id` int DEFAULT NULL COMMENT 'i18n配置的语音',
                                                    `sound_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音名称',
    `sound_type` tinyint DEFAULT NULL COMMENT '声音类型（关联字典表）',
    `sound_sub_type` tinyint DEFAULT NULL COMMENT '声音类型 子类型',
    `sound_script` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音脚本',
    `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音',
    `duration` int DEFAULT NULL COMMENT '时间',
    `gender` tinyint DEFAULT '1' COMMENT '性别',
    `status` tinyint DEFAULT '0' COMMENT '状态',
    `need_translation` tinyint NOT NULL DEFAULT '0' COMMENT '是否需要翻译',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='116 声音表';

#200sound 表建表 SQL
CREATE TABLE IF NOT EXISTS `proj_sound` (
                                               `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                               `table_code` tinyint DEFAULT '0' COMMENT '表表示',
                                               `proj_id` int DEFAULT NULL COMMENT '项目 ID',
                                               `core_voice_config_i18n_id` int DEFAULT NULL COMMENT 'i18n配置的语音',
                                               `sound_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音名称',
    `sound_type` tinyint DEFAULT NULL COMMENT '声音类型（关联字典表）',
    `sound_sub_type` tinyint DEFAULT NULL COMMENT '声音类型 子类型',
    `sound_script` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音脚本',
    `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '声音',
    `duration` int DEFAULT NULL COMMENT '时间',
    `gender` tinyint DEFAULT '1' COMMENT '性别',
    `status` tinyint DEFAULT '0' COMMENT '状态',
    `need_translation` tinyint NOT NULL DEFAULT '0' COMMENT '是否需要翻译',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='200 声音表';

#翻译
ALTER TABLE `res_video116`
ADD COLUMN `core_voice_config_i18n_id` int NULL DEFAULT NULL COMMENT 'i18n配置的语音' AFTER `id`;
update res_video116 set core_voice_config_i18n_id = -1 where gender = 'Female';
update res_video116 set core_voice_config_i18n_id = -2 where gender = 'Male';

#菜单配置------
BEGIN;
SET @menuId = 0;
SET @operator = '<EMAIL>';
SET @menuName:='Fitness Sound';
SET @urlStart:='fitnessSound';
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

COMMIT;

BEGIN;
SET @menuId = 0;
SET @operator = '<EMAIL>';
SET @menuName:='116 Sound';
SET @urlStart:='sound116';
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

COMMIT;

BEGIN;
SET @menuId = 0;
SET @operator = '<EMAIL>';
SET @menuName:='200 Sound';
SET @urlStart:='sound200';
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO proj_menu (id, parent_id, menu_name, menu_key, menu_type, path, component, required, visible, status, icon, remark, del_flag, create_user, create_time, update_user, update_time, sort_no) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

COMMIT;
#添加翻译常量
BEGIN;
INSERT INTO `core_lms_constant_text` ( `text`, `proj_code`, `del_flag`) VALUES ('No equipment', 8, 0);
INSERT INTO `core_lms_constant_text` ( `text`, `proj_code`, `del_flag`) VALUES ('Resistance band', 8, 0);
INSERT INTO `core_lms_constant_text` ( `text`, `proj_code`, `del_flag`) VALUES ('Dumbbell (lightweight)', 8, 0);
INSERT INTO `core_lms_constant_text` ( `text`, `proj_code`, `del_flag`) VALUES ('Chair', 8, 0);
INSERT INTO `core_lms_constant_text` ( `text`, `proj_code`, `del_flag`) VALUES ('All body parts', 8, 0);
COMMIT;

#逻辑删除旧的翻译逻辑定时任务
BEGIN;
update middle_i18n_config set del_flag = 1
where table_name in ('res_image','res_video116','proj_workout116_res_video116','proj_workout116','proj_workout116_generate','proj_template116_rule','proj_category116','proj_coach116','proj_program116');
COMMIT;