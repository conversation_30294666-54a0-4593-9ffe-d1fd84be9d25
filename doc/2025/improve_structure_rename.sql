
-- SELECT CONCAT('R<PERSON>AM<PERSON> TABLE ', table_name, ' TO ', table_name, '_bak_105;')
-- FROM information_schema.tables
-- WHERE table_schema = 'cms_test_large'AND table_name like '%105%';

-- 105
RENAME TABLE proj_workout_video105 TO proj_workout_video105_bak_105;
RENAME TABLE proj_workout_video105_pub TO proj_workout_video105_pub_bak_105;
RENAME TABLE proj_workout_video105_relation TO proj_workout_video105_relation_bak_105;
RENAME TABLE proj_workout_video105_relation_pub TO proj_workout_video105_relation_pub_bak_105;
RENAME TABLE res_video105 TO res_video105_bak_105;

-- 118
RENAME TABLE proj_workout_scene TO proj_workout_scene_bak_118;
RENAME TABLE proj_workout_scene_pub TO proj_workout_scene_pub_bak_118;
R<PERSON>AME TABLE proj_workout_video TO proj_workout_video_bak_118;
RENAME TABLE proj_workout_video_pub TO proj_workout_video_pub_bak_118;

-- 106
RENAME TABLE proj_item_image TO proj_item_image_bak_106;
RENAME TABLE proj_item_image_pub TO proj_item_image_pub_bak_106;
RENAME TABLE proj_butt_regular_workout_res_video106 TO proj_butt_regular_workout_res_video106_bak_106;
RENAME TABLE proj_butt_regular_workout_res_video106_pub TO proj_butt_regular_workout_res_video106_pub_bak_106;
RENAME TABLE proj_category106 TO proj_category106_bak_106;
RENAME TABLE proj_category106_proj_workout106 TO proj_category106_proj_workout106_bak_106;
RENAME TABLE proj_category106_proj_workout106_pub TO proj_category106_proj_workout106_pub_bak_106;
RENAME TABLE proj_category106_pub TO proj_category106_pub_bak_106;
RENAME TABLE proj_plan106_proj_playlist TO proj_plan106_proj_playlist_bak_106;
RENAME TABLE proj_template106 TO proj_template106_bak_106;
RENAME TABLE proj_template106_pub TO proj_template106_pub_bak_106;
RENAME TABLE proj_template106_rule TO proj_template106_rule_bak_106;
RENAME TABLE proj_template106_task TO proj_template106_task_bak_106;
RENAME TABLE proj_workout106 TO proj_workout106_bak_106;
RENAME TABLE proj_workout106_audio_i18n TO proj_workout106_audio_i18n_bak_106;
RENAME TABLE proj_workout106_generate_file_task TO proj_workout106_generate_file_task_bak_106;
RENAME TABLE proj_workout106_res_video106 TO proj_workout106_res_video106_bak_106;
RENAME TABLE res_video106 TO res_video106_bak_106;
RENAME TABLE res_video106_audio_i18n TO res_video106_audio_i18n_bak_106;
RENAME TABLE proj_butt_program TO proj_butt_program_bak_106;
RENAME TABLE proj_butt_program_proj_butt_regular_workout TO proj_butt_program_proj_butt_regular_workout_bak_106;
RENAME TABLE proj_butt_program_proj_butt_regular_workout_pub TO proj_butt_program_proj_butt_regular_workout_pub_bak_106;
RENAME TABLE proj_butt_program_pub TO proj_butt_program_pub_bak_106;
RENAME TABLE proj_butt_regular_workout TO proj_butt_regular_workout_bak_106;
RENAME TABLE proj_butt_regular_workout_generate_file_task TO proj_butt_regular_workout_generate_file_task_bak_106;
RENAME TABLE proj_butt_regular_workout_i18n TO proj_butt_regular_workout_i18n_bak_106;
RENAME TABLE proj_butt_regular_workout_i18n_pub TO proj_butt_regular_workout_i18n_pub_bak_106;
RENAME TABLE proj_butt_regular_workout_pub TO proj_butt_regular_workout_pub_bak_106;

-- 111
RENAME TABLE proj_challenge111 TO proj_challenge111_bak_111;
RENAME TABLE proj_challenge111_pub TO proj_challenge111_pub_bak_111;
RENAME TABLE proj_challenge111_workout_relation TO proj_challenge111_workout_relation_bak_111;
RENAME TABLE proj_challenge111_workout_relation_pub TO proj_challenge111_workout_relation_pub_bak_111;
RENAME TABLE proj_template111 TO proj_template111_bak_111;
RENAME TABLE proj_template111_generate TO proj_template111_generate_bak_111;
RENAME TABLE proj_template111_generate_i18n TO proj_template111_generate_i18n_bak_111;
RENAME TABLE proj_template111_generate_video TO proj_template111_generate_video_bak_111;
RENAME TABLE proj_template111_pub TO proj_template111_pub_bak_111;
RENAME TABLE proj_template111_rule TO proj_template111_rule_bak_111;
RENAME TABLE proj_template111_task TO proj_template111_task_bak_111;
RENAME TABLE proj_video111_library TO proj_video111_library_bak_111;
RENAME TABLE proj_video111_library_pub TO proj_video111_library_pub_bak_111;
RENAME TABLE proj_video111_library_relation TO proj_video111_library_relation_bak_111;
RENAME TABLE proj_video111_library_relation_pub TO proj_video111_library_relation_pub_bak_111;
RENAME TABLE proj_workout_video111 TO proj_workout_video111_bak_111;
RENAME TABLE proj_workout_video111_audio_i18n TO proj_workout_video111_audio_i18n_bak_111;
RENAME TABLE proj_workout_video111_audio_i18n_pub TO proj_workout_video111_audio_i18n_pub_bak_111;
RENAME TABLE proj_workout_video111_pub TO proj_workout_video111_pub_bak_111;
RENAME TABLE proj_workout_video111_relation TO proj_workout_video111_relation_bak_111;
RENAME TABLE proj_workout_video111_relation_pub TO proj_workout_video111_relation_pub_bak_111;
RENAME TABLE res_video111 TO res_video111_bak_111;
RENAME TABLE res_video111_audio_i18n TO res_video111_audio_i18n_bak_111;

-- 117
RENAME TABLE proj_reminder TO proj_reminder_bak_117;
RENAME TABLE proj_reminder_pub TO proj_reminder_pub_bak_117;

RENAME TABLE proj_category TO proj_category_bak_117;
RENAME TABLE proj_category_pub TO proj_category_pub_bak_117;
RENAME TABLE proj_category_relationship TO proj_category_relationship_bak_117;
RENAME TABLE proj_category_relationship_pub TO proj_category_relationship_pub_bak_117;
RENAME TABLE proj_coach_tips TO proj_coach_tips_bak_117;

RENAME TABLE proj_collection TO proj_collection_bak_117;
RENAME TABLE proj_collection_pub TO proj_collection_pub_bak_117;
RENAME TABLE proj_collection_keyword TO proj_collection_keyword_bak_117;
RENAME TABLE proj_collection_keyword_pub TO proj_collection_keyword_pub_bak_117;
RENAME TABLE proj_collection_workout TO proj_collection_workout_bak_117;
RENAME TABLE proj_collection_workout_pub TO proj_collection_workout_pub_bak_117;

RENAME TABLE proj_custom TO proj_custom_bak_117;
RENAME TABLE proj_custom_pub TO proj_custom_pub_bak_117;
RENAME TABLE proj_daily_image TO proj_daily_image_bak_117;
RENAME TABLE proj_daily_image_pub TO proj_daily_image_pub_bak_117;

RENAME TABLE proj_dance_collection TO proj_dance_collection_bak_117;
RENAME TABLE proj_dance_collection_proj_dance_workout TO proj_dance_collection_proj_dance_workout_bak_117;
RENAME TABLE proj_dance_collection_proj_dance_workout_pub TO proj_dance_collection_proj_dance_workout_pub_bak_117;
RENAME TABLE proj_dance_collection_pub TO proj_dance_collection_pub_bak_117;
RENAME TABLE proj_dance_workout TO proj_dance_workout_bak_117;
RENAME TABLE proj_dance_workout_i18n TO proj_dance_workout_i18n_bak_117;
RENAME TABLE proj_dance_workout_i18n_pub TO proj_dance_workout_i18n_pub_bak_117;
RENAME TABLE proj_dance_workout_pub TO proj_dance_workout_pub_bak_117;
RENAME TABLE proj_dance_workout_res_dance_move TO proj_dance_workout_res_dance_move_bak_117;
RENAME TABLE proj_dance_workout_res_dance_move_pub TO proj_dance_workout_res_dance_move_pub_bak_117;
RENAME TABLE proj_dance_workout_res_keyword TO proj_dance_workout_res_keyword_bak_117;
RENAME TABLE proj_dance_workout_res_keyword_pub TO proj_dance_workout_res_keyword_pub_bak_117;
RENAME TABLE proj_dance_workout_res_music TO proj_dance_workout_res_music_bak_117;
RENAME TABLE proj_dance_workout_res_music_pub TO proj_dance_workout_res_music_pub_bak_117;

RENAME TABLE proj_plan TO proj_plan_bak_117;
RENAME TABLE proj_plan_pub TO proj_plan_pub_bak_117;
RENAME TABLE proj_plan_workout TO proj_plan_workout_bak_117;
RENAME TABLE proj_plan_workout_pub TO proj_plan_workout_pub_bak_117;

RENAME TABLE proj_program TO proj_program_bak_117;
RENAME TABLE proj_program_pub TO proj_program_pub_bak_117;
RENAME TABLE proj_program_category TO proj_program_category_bak_117;
RENAME TABLE proj_program_category_pub TO proj_program_category_pub_bak_117;
RENAME TABLE proj_program_category_relation TO proj_program_category_relation_bak_117;
RENAME TABLE proj_program_category_relation_pub TO proj_program_category_relation_pub_bak_117;
RENAME TABLE proj_program_keyword TO proj_program_keyword_bak_117;
RENAME TABLE proj_program_keyword_pub TO proj_program_keyword_pub_bak_117;

RENAME TABLE proj_program_workout TO proj_program_workout_bak_117;
RENAME TABLE proj_program_workout_exercise TO proj_program_workout_exercise_bak_117;
RENAME TABLE proj_program_workout_exercise_pub TO proj_program_workout_exercise_pub_bak_117;
RENAME TABLE proj_program_workout_pub TO proj_program_workout_pub_bak_117;
RENAME TABLE proj_program_workout_relation TO proj_program_workout_relation_bak_117;
RENAME TABLE proj_program_workout_relation_pub TO proj_program_workout_relation_pub_bak_117;

RENAME TABLE proj_quote TO proj_quote_bak_117;
RENAME TABLE proj_quote_pub TO proj_quote_pub_bak_117;
RENAME TABLE proj_search_terms TO proj_search_terms_bak_117;
RENAME TABLE proj_search_terms_pub TO proj_search_terms_pub_bak_117;

RENAME TABLE proj_workout TO proj_workout_bak_117;
RENAME TABLE proj_workout_pub TO proj_workout_pub_bak_117;
RENAME TABLE proj_workout_exercise TO proj_workout_exercise_bak_117;
RENAME TABLE proj_workout_exercise_pub TO proj_workout_exercise_pub_bak_117;

RENAME TABLE proj_workout_keyword TO proj_workout_keyword_bak_117;
RENAME TABLE proj_workout_keyword_pub TO proj_workout_keyword_pub_bak_117;

RENAME TABLE res_animation TO res_animation_bak_117;
RENAME TABLE res_dance_move TO res_dance_move_bak_117;
RENAME TABLE res_dance_move_i18n TO res_dance_move_i18n_bak_117;
RENAME TABLE res_keyword TO res_keyword_bak_117;

RENAME TABLE res_quote TO res_quote_bak_117;
RENAME TABLE res_regular_exercise TO res_regular_exercise_bak_117;
RENAME TABLE res_regular_exercise_keyword TO res_regular_exercise_keyword_bak_117;
RENAME TABLE res_video TO res_video_bak_117;

-- 120
RENAME TABLE proj_category120s TO proj_category120s_bak_120;
RENAME TABLE proj_category120s_proj_workout120s TO proj_category120s_proj_workout120s_bak_120;
RENAME TABLE proj_category120s_proj_workout120s_pub TO proj_category120s_proj_workout120s_pub_bak_120;
RENAME TABLE proj_category120s_pub TO proj_category120s_pub_bak_120;
RENAME TABLE proj_template120s TO proj_template120s_bak_120;
RENAME TABLE proj_template120s_pub TO proj_template120s_pub_bak_120;
RENAME TABLE proj_template120s_rule TO proj_template120s_rule_bak_120;
RENAME TABLE proj_template120s_task TO proj_template120s_task_bak_120;
RENAME TABLE proj_workout120s TO proj_workout120s_bak_120;
RENAME TABLE proj_workout120s_copy1 TO proj_workout120s_copy1_bak_120;
RENAME TABLE proj_workout120s_generate TO proj_workout120s_generate_bak_120;
RENAME TABLE proj_workout120s_generate_copy1 TO proj_workout120s_generate_copy1_bak_120;
RENAME TABLE proj_workout120s_generate_res_video120s TO proj_workout120s_generate_res_video120s_bak_120;
RENAME TABLE proj_workout120s_pub TO proj_workout120s_pub_bak_120;
RENAME TABLE proj_workout120s_pub_copy1 TO proj_workout120s_pub_copy1_bak_120;
RENAME TABLE proj_workout120s_res_video120s TO proj_workout120s_res_video120s_bak_120;
RENAME TABLE proj_workout120s_res_video120s_pub TO proj_workout120s_res_video120s_pub_bak_120;
RENAME TABLE proj_workout_video120 TO proj_workout_video120_bak_120;
RENAME TABLE proj_workout_video120_pub TO proj_workout_video120_pub_bak_120;
RENAME TABLE proj_workout_video120_relation TO proj_workout_video120_relation_bak_120;
RENAME TABLE proj_workout_video120_relation_pub TO proj_workout_video120_relation_pub_bak_120;
RENAME TABLE res_video120 TO res_video120_bak_120;
RENAME TABLE res_video120s TO res_video120s_bak_120;
RENAME TABLE res_video120s_copy1 TO res_video120s_copy1_bak_120;

-- 206
RENAME TABLE proj_category206 TO proj_category206_bak_206;
RENAME TABLE proj_category206_proj_workout206 TO proj_category206_proj_workout206_bak_206;
RENAME TABLE proj_category206_proj_workout206_pub TO proj_category206_proj_workout206_pub_bak_206;
RENAME TABLE proj_category206_pub TO proj_category206_pub_bak_206;
RENAME TABLE proj_template206 TO proj_template206_bak_206;
RENAME TABLE proj_template206_pub TO proj_template206_pub_bak_206;
RENAME TABLE proj_template206_rule TO proj_template206_rule_bak_206;
RENAME TABLE proj_template206_task TO proj_template206_task_bak_206;
RENAME TABLE proj_workout206 TO proj_workout206_bak_206;
RENAME TABLE proj_workout206_audio_i18n TO proj_workout206_audio_i18n_bak_206;
RENAME TABLE proj_workout206_audio_i18n_pub TO proj_workout206_audio_i18n_pub_bak_206;
RENAME TABLE proj_workout206_file_task TO proj_workout206_file_task_bak_206;
RENAME TABLE proj_workout206_generate TO proj_workout206_generate_bak_206;
RENAME TABLE proj_workout206_generate_audio_i18n TO proj_workout206_generate_audio_i18n_bak_206;
RENAME TABLE proj_workout206_generate_file_task TO proj_workout206_generate_file_task_bak_206;
RENAME TABLE proj_workout206_generate_res_video206 TO proj_workout206_generate_res_video206_bak_206;
RENAME TABLE proj_workout206_pub TO proj_workout206_pub_bak_206;
RENAME TABLE proj_workout206_res_video206 TO proj_workout206_res_video206_bak_206;
RENAME TABLE proj_workout206_res_video206_pub TO proj_workout206_res_video206_pub_bak_206;
RENAME TABLE res_video206 TO res_video206_bak_206;
