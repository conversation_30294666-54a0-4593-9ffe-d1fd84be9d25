/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80025
 Source Host           : *************:3306
 Source Schema         : cms

 Target Server Type    : MySQL
 Target Server Version : 80025
 File Encoding         : 65001

 Date: 22/11/2022 09:53:40
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for common_logs
-- ----------------------------
DROP TABLE IF EXISTS `common_logs`;
CREATE TABLE `common_logs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `entity_name` varchar(50) NOT NULL COMMENT '实体名称',
  `op_type` int NOT NULL COMMENT '操作类型（1 新增 2 修改 3 删除 4 启用 5 禁用）',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志简述';

-- ----------------------------
-- Table structure for proj_category
-- ----------------------------
DROP TABLE IF EXISTS `proj_category`;
CREATE TABLE `proj_category` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `sort_no` int NOT NULL COMMENT '排序编号',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分类表';

-- ----------------------------
-- Table structure for proj_category_workout
-- ----------------------------
DROP TABLE IF EXISTS `proj_category_workout`;
CREATE TABLE `proj_category_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_category_id` int unsigned DEFAULT NULL COMMENT '分类id',
  `proj_workout_id` int unsigned DEFAULT NULL COMMENT '锻炼id',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分类和锻炼关联表';

-- ----------------------------
-- Table structure for proj_custom
-- ----------------------------
DROP TABLE IF EXISTS `proj_custom`;
CREATE TABLE `proj_custom` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `regular_exercise_id` int unsigned NOT NULL COMMENT '动作id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='custom';

-- ----------------------------
-- Table structure for proj_daily_image
-- ----------------------------
DROP TABLE IF EXISTS `proj_daily_image`;
CREATE TABLE `proj_daily_image` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `image_url` varchar(255) NOT NULL COMMENT '封面图地址',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0草稿 1启用 2停用',
  `sort_no` int unsigned NOT NULL COMMENT '排序编号',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='daily image';

-- ----------------------------
-- Table structure for proj_info
-- ----------------------------
DROP TABLE IF EXISTS `proj_info`;
CREATE TABLE `proj_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `icon_url` varchar(255) NOT NULL COMMENT '项目图标',
  `app_code` varchar(100) NOT NULL COMMENT 'app code',
  `apple_id` varchar(100) NOT NULL COMMENT 'apple id',
  `bundle_id` varchar(100) NOT NULL COMMENT 'bundle id',
  `app_store_name` varchar(100) NOT NULL COMMENT 'app store name',
  `app_subtitle` varchar(100) NOT NULL COMMENT 'app subtitle',
  `scheme` varchar(100) DEFAULT NULL COMMENT 'scheme',
  `web_api_key` varchar(100) DEFAULT '' COMMENT 'web api key',
  `dynamic_link` varchar(255) DEFAULT NULL COMMENT 'dynamic link',
  `campaign_link` varchar(255) DEFAULT NULL COMMENT 'campaign link',
  `workout_short_link` tinyint DEFAULT '0' COMMENT 'workout short link 开关，1开启 0关闭',
  `perms_id` int NOT NULL COMMENT '权限',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目信息表';

-- ----------------------------
-- Table structure for proj_menu
-- ----------------------------
DROP TABLE IF EXISTS `proj_menu`;
CREATE TABLE `proj_menu` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `parent_id` int NOT NULL DEFAULT '0' COMMENT '父级id',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `menu_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单标识，一旦使用 不得修改，否则容易造成权限错误',
  `menu_type` tinyint NOT NULL DEFAULT '1' COMMENT '权限类型： 1 菜单 2操作类型 3 项目 4外链',
  `path` varchar(255) DEFAULT NULL COMMENT '路由地址，链接地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `required` tinyint NOT NULL DEFAULT '1' COMMENT '随着父级联动，必须的（0 否 1是）',
  `visible` tinyint NOT NULL DEFAULT '1' COMMENT '菜单状态（0隐藏 1显示）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '菜单状态（1正常 2停用）',
  `icon` varchar(255) DEFAULT NULL COMMENT '菜单图标',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `menu_key` (`menu_key`) USING BTREE COMMENT 'menu_key唯一约束',
  KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目菜单表';

-- ----------------------------
-- Table structure for proj_plan
-- ----------------------------
DROP TABLE IF EXISTS `proj_plan`;
CREATE TABLE `proj_plan` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `plan_name` varchar(50) DEFAULT NULL COMMENT 'plan名称',
  `plan_type` varchar(50) DEFAULT NULL COMMENT 'plan类型',
  `phone_cover_img_url` varchar(255) DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) DEFAULT NULL COMMENT '平板封面图',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `free_days` int DEFAULT NULL COMMENT 'plan的免费天数,0代表全免费 999 代表收费',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='plan表';

-- ----------------------------
-- Table structure for proj_plan_workout
-- ----------------------------
DROP TABLE IF EXISTS `proj_plan_workout`;
CREATE TABLE `proj_plan_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_plan_id` int unsigned DEFAULT NULL COMMENT 'plan id',
  `proj_workout_id` int unsigned DEFAULT NULL COMMENT '锻炼id，rest day时 workout_id为-1',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='plan和锻炼关联表';

-- ----------------------------
-- Table structure for proj_playlist
-- ----------------------------
DROP TABLE IF EXISTS `proj_playlist`;
CREATE TABLE `proj_playlist` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `playlist_name` varchar(100) NOT NULL COMMENT '列表名称',
  `phone_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机封面图',
  `tablet_cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板封面图',
  `phone_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手机详情图',
  `tablet_detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '平板详情图',
  `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `default_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认 0非默认 1默认',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
  `proj_id` int NOT NULL COMMENT '项目id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 启用 2 禁用',
  PRIMARY KEY (`id`),
  KEY `del_flag` (`del_flag`),
  KEY `playlist_name` (`playlist_name`),
  KEY `subscription` (`subscription`),
  KEY `default_flag` (`default_flag`),
  KEY `proj_id` (`proj_id`),
  KEY `sort_no` (`sort_no`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目播放列表表';

-- ----------------------------
-- Table structure for proj_playlist_music
-- ----------------------------
DROP TABLE IF EXISTS `proj_playlist_music`;
CREATE TABLE `proj_playlist_music` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_playlist_id` int NOT NULL COMMENT '播放列表id',
  `res_music_id` int NOT NULL COMMENT '音乐id',
  `subscription` tinyint NOT NULL DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `proj_playlist_id` (`proj_playlist_id`),
  KEY `res_music_id` (`res_music_id`),
  KEY `subscription` (`subscription`),
  KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='播放列表音乐关联表';

-- ----------------------------
-- Table structure for proj_reminder
-- ----------------------------
DROP TABLE IF EXISTS `proj_reminder`;
CREATE TABLE `proj_reminder` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_id` int NOT NULL COMMENT '项目id',
  `res_reminder_id` int NOT NULL COMMENT '通知id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `proj_id` (`proj_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目通知表';

-- ----------------------------
-- Table structure for proj_workout
-- ----------------------------
DROP TABLE IF EXISTS `proj_workout`;
CREATE TABLE `proj_workout` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workout_type` varchar(50) DEFAULT NULL COMMENT '锻炼类型',
  `workout_name` varchar(100) DEFAULT NULL COMMENT '锻炼名称',
  `img_cover_phone` varchar(255) DEFAULT NULL COMMENT '手机端封面图',
  `img_cover_tablet` varchar(255) DEFAULT NULL COMMENT '平板端封面图',
  `img_detail_phone` varchar(255) DEFAULT NULL COMMENT '手机端详情图',
  `img_detail_tablet` varchar(255) DEFAULT NULL COMMENT '平板端详情图',
  `short_link` varchar(255) DEFAULT NULL COMMENT 'app短连接',
  `calorie` int unsigned DEFAULT NULL COMMENT '卡路里',
  `duration` int unsigned DEFAULT NULL COMMENT '时长',
  `difficulty` varchar(50) DEFAULT NULL COMMENT '难度',
  `intensity` varchar(50) DEFAULT NULL COMMENT '强度',
  `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `proj_id` int unsigned NOT NULL COMMENT '项目id',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `plan` tinyint NOT NULL DEFAULT '0' COMMENT '是否是plan',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='锻炼';

-- ----------------------------
-- Table structure for proj_workout_exercise
-- ----------------------------
DROP TABLE IF EXISTS `proj_workout_exercise`;
CREATE TABLE `proj_workout_exercise` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_workout_id` int unsigned DEFAULT NULL COMMENT '锻炼id',
  `regular_exercise_id` int unsigned DEFAULT NULL COMMENT '动作id',
  `duration` int DEFAULT NULL COMMENT '动作时长',
  `rest_duration` int DEFAULT NULL COMMENT '休息时长',
  `calorie` decimal(13,3) DEFAULT '0.000' COMMENT '卡路里',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='锻炼和动作关联表';

-- ----------------------------
-- Table structure for proj_workout_keyword
-- ----------------------------
DROP TABLE IF EXISTS `proj_workout_keyword`;
CREATE TABLE `proj_workout_keyword` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proj_workout_id` int unsigned NOT NULL COMMENT 'workout id',
  `keyword_id` int unsigned NOT NULL COMMENT '关键字id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workout 关键字';

-- ----------------------------
-- Table structure for res_animation
-- ----------------------------
DROP TABLE IF EXISTS `res_animation`;
CREATE TABLE `res_animation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `animation_name` varchar(100) DEFAULT NULL COMMENT '动画名称',
  `animation_phone_url` varchar(255) DEFAULT NULL COMMENT '手机端动画 gif',
  `animation_tablet_url` varchar(255) DEFAULT NULL COMMENT '平板端动画 gif',
  `animation_cover_url` varchar(255) DEFAULT NULL COMMENT '动画封面图片 png',
  `status` tinyint DEFAULT '0' COMMENT '状态 0 草稿 1启用 2 禁用',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='动画表';

-- ----------------------------
-- Table structure for res_keyword
-- ----------------------------
DROP TABLE IF EXISTS `res_keyword`;
CREATE TABLE `res_keyword` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `keyword` varchar(50) NOT NULL COMMENT '关键值',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='关键字表';

-- ----------------------------
-- Table structure for res_music
-- ----------------------------
DROP TABLE IF EXISTS `res_music`;
CREATE TABLE `res_music` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `music_name` varchar(100) NOT NULL COMMENT '音乐名称',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `audio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '音频',
  `audio_duration` int DEFAULT '0' COMMENT '音频总时长',
  `music_type` varchar(50) NOT NULL COMMENT '音乐类型（关联字典表）',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Music状态 0草稿 1启用 2停用',
  `compression_status` tinyint NOT NULL DEFAULT '1' COMMENT '压缩状态 1压缩中 2成功 3失败',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `music_name` (`music_name`),
  KEY `display_name` (`display_name`),
  KEY `music_type` (`music_type`),
  KEY `del_flag` (`del_flag`),
  KEY `create_time` (`create_time` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='音乐表';

-- ----------------------------
-- Table structure for res_quote
-- ----------------------------
DROP TABLE IF EXISTS `res_quote`;
CREATE TABLE `res_quote` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `quote_content` varchar(255) NOT NULL COMMENT '名言警句内容',
  `quote_author` varchar(50) NOT NULL COMMENT '名言警句作者',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `quote_content` (`quote_content`),
  KEY `quote_author` (`quote_author`),
  KEY `del_flag` (`del_flag`),
  KEY `create_time` (`create_time` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='名言警句表';

-- ----------------------------
-- Table structure for res_regular_exercise
-- ----------------------------
DROP TABLE IF EXISTS `res_regular_exercise`;
CREATE TABLE `res_regular_exercise` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `combination` varchar(50) DEFAULT NULL COMMENT '动作方向',
  `exercise_name` varchar(100) DEFAULT NULL COMMENT '动作名称',
  `display_name` varchar(100) DEFAULT NULL COMMENT '显示名',
  `group_id` varchar(50) DEFAULT NULL COMMENT '同组数据标识',
  `concat_name` varchar(32) DEFAULT '' COMMENT '拼接名',
  `group_show` tinyint DEFAULT NULL COMMENT '是否显示 0 隐藏 1显示',
  `exercise_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型 Fit、Yoga、Face Yoga',
  `exercise_sub_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '子类型',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `video_link_url` varchar(255) DEFAULT NULL COMMENT '第三方链接',
  `animation_id` int unsigned DEFAULT NULL COMMENT '动画id',
  `sound_id` int unsigned DEFAULT NULL COMMENT '声音id',
  `body_part` varchar(255) DEFAULT NULL COMMENT '部位',
  `target` varchar(255) DEFAULT NULL COMMENT '目的',
  `position` varchar(255) DEFAULT NULL COMMENT '位置',
  `focus` varchar(255) DEFAULT NULL COMMENT '焦点',
  `equipment` varchar(255) DEFAULT NULL COMMENT '必备',
  `met` int unsigned DEFAULT NULL COMMENT 'met',
  `star` int unsigned DEFAULT NULL COMMENT 'star',
  `special_need` varchar(255) DEFAULT NULL COMMENT '特殊需要',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `original_id` int DEFAULT NULL COMMENT '老系统中的id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='regular 动作';

-- ----------------------------
-- Table structure for res_regular_exercise_keyword
-- ----------------------------
DROP TABLE IF EXISTS `res_regular_exercise_keyword`;
CREATE TABLE `res_regular_exercise_keyword` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `regular_exercise_id` int unsigned NOT NULL COMMENT 'regular exercise id',
  `keyword_id` int unsigned NOT NULL COMMENT '关键字id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='regular exercise 关键字';

-- ----------------------------
-- Table structure for res_reminder
-- ----------------------------
DROP TABLE IF EXISTS `res_reminder`;
CREATE TABLE `res_reminder` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(50) NOT NULL COMMENT '通知标题',
  `content` varchar(255) DEFAULT NULL COMMENT '通知内容',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知表';

-- ----------------------------
-- Table structure for res_sound
-- ----------------------------
DROP TABLE IF EXISTS `res_sound`;
CREATE TABLE `res_sound` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sound_name` varchar(100) DEFAULT NULL COMMENT '声音名称',
  `sound_type` varchar(50) DEFAULT NULL COMMENT '声音类型（关联字典表）',
  `sound_sub_type` varchar(50) DEFAULT NULL COMMENT '声音类型 子类型',
  `sound_script` varchar(500) DEFAULT NULL COMMENT '声音脚本',
  `female_url` varchar(255) DEFAULT NULL COMMENT '女声',
  `female_robot_url` varchar(255) DEFAULT NULL COMMENT '机器女声',
  `male_url` varchar(255) DEFAULT NULL COMMENT '男声',
  `male_robot_url` varchar(255) DEFAULT NULL COMMENT '机器男声',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `compression_status` tinyint NOT NULL DEFAULT '1' COMMENT '压缩状态 1压缩中 2成功 3失败',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='声音表';

-- ----------------------------
-- Table structure for res_task
-- ----------------------------
DROP TABLE IF EXISTS `res_task`;
CREATE TABLE `res_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_name` varchar(50) NOT NULL COMMENT '视频名称',
  `task_type` tinyint NOT NULL COMMENT '任务类型',
  `table_name` varchar(50) NOT NULL COMMENT '表名',
  `table_column_name` varchar(50) NOT NULL COMMENT '表列名',
  `table_id` varchar(50) NOT NULL COMMENT '表数据id',
  `task_status` tinyint NOT NULL DEFAULT '0' COMMENT '任务状态 0待处理 1处理中 2成功 3失败',
  `fail_reason` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '失败原因',
  `task_result` varchar(255) DEFAULT NULL COMMENT '任务执行结果',
  `task_data` varchar(500) DEFAULT NULL COMMENT '任务内容',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `table_name` (`table_name`,`table_id`,`task_status`,`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资源任务表';

-- ----------------------------
-- Table structure for res_video
-- ----------------------------
DROP TABLE IF EXISTS `res_video`;
CREATE TABLE `res_video` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_name` varchar(50) NOT NULL COMMENT '视频名称',
  `video_script` varchar(500) NOT NULL COMMENT '视频描述',
  `video_type` varchar(50) NOT NULL COMMENT '视频类型（关联字典表）',
  `start_video_url` varchar(255) DEFAULT NULL COMMENT '开头视频',
  `main_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主体视频',
  `end_video_url` varchar(255) DEFAULT NULL COMMENT '结尾视频',
  `start_video_url_duration` int DEFAULT '0' COMMENT '开头视频总时长 单位秒',
  `main_video_url_duration` int DEFAULT '0' COMMENT '主体视频总时长 单位秒',
  `end_video_url_duration` int DEFAULT '0' COMMENT '结尾视频总时长 单位秒',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '视频状态 0草稿 1启用 2停用',
  `compression_status` tinyint NOT NULL DEFAULT '1' COMMENT '压缩状态 1压缩中 2成功 3失败',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `video_type` (`video_type`),
  KEY `video_name` (`video_name`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time` DESC),
  KEY `update_time` (`update_time`),
  KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='视频表';


-- ----------------------------
-- Table structure for storage_exercise104
-- ----------------------------
DROP TABLE IF EXISTS `storage_exercise104`;
CREATE TABLE `storage_exercise104` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `alternate_name` varchar(2000) NOT NULL,
  `daily_7` tinyint NOT NULL,
  `my_plan` tinyint NOT NULL,
  `intensity` int NOT NULL,
  `difficulty` int NOT NULL,
  `met` int NOT NULL,
  `body_part` int NOT NULL,
  `video_link` varchar(200) NOT NULL,
  `comment` varchar(2000) NOT NULL,
  `mark` varchar(2000) NOT NULL,
  `description` varchar(2000) NOT NULL,
  `position` int NOT NULL,
  `focus` int NOT NULL,
  `psd` varchar(250) NOT NULL,
  `psd_name` varchar(100) NOT NULL,
  `phone_gif` varchar(250) NOT NULL,
  `phone_gif_name` varchar(100) NOT NULL,
  `pad_gif` varchar(250) NOT NULL,
  `pad_gif_name` varchar(100) NOT NULL,
  `png` varchar(250) NOT NULL,
  `png_name` varchar(100) NOT NULL,
  `webp` varchar(250) NOT NULL,
  `webp_name` varchar(100) NOT NULL,
  `sound1` varchar(250) NOT NULL,
  `sound1_name` varchar(100) NOT NULL,
  `sound2` varchar(250) NOT NULL,
  `sound2_name` varchar(100) NOT NULL,
  `sound2_duration` double NOT NULL,
  `sound3` varchar(250) NOT NULL,
  `sound4` varchar(250) NOT NULL,
  `soundp_name` varchar(100) NOT NULL,
  `phone_gif_md5` varchar(35) NOT NULL,
  `pad_gif_md5` varchar(35) NOT NULL,
  `webp_md5` varchar(35) NOT NULL,
  `sound1_md5` varchar(35) NOT NULL,
  `sound2_md5` varchar(35) NOT NULL,
  `dumbbells` tinyint NOT NULL,
  `resistance_band` tinyint NOT NULL,
  `custom` tinyint NOT NULL,
  `random` tinyint NOT NULL,
  `custom_101` tinyint NOT NULL,
  `face_yoga` tinyint NOT NULL,
  `sanskrit` varchar(100) NOT NULL,
  `female_instruction` varchar(250) NOT NULL,
  `female` varchar(250) NOT NULL,
  `female_p` varchar(250) NOT NULL,
  `female_robot` varchar(250) NOT NULL,
  `female_wellsaid` varchar(250) NOT NULL,
  `male_instruction` varchar(250) NOT NULL,
  `male_robot` varchar(250) NOT NULL,
  `male_wellsaid` varchar(250) NOT NULL,
  `transition_id` int DEFAULT NULL,
  `del_flag` tinyint DEFAULT '0',
  `create_user` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_user` varchar(255) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for storage_exercise200
-- ----------------------------
DROP TABLE IF EXISTS `storage_exercise200`;
CREATE TABLE `storage_exercise200` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `alternate_name` text NOT NULL,
  `daily_7` tinyint NOT NULL,
  `my_plan` tinyint NOT NULL,
  `intensity` int NOT NULL,
  `difficulty` int NOT NULL,
  `met` int NOT NULL,
  `body_part` int NOT NULL,
  `video_link` varchar(200) NOT NULL,
  `comment` text NOT NULL,
  `mark` text NOT NULL,
  `description` text NOT NULL,
  `position` int NOT NULL,
  `focus` int NOT NULL,
  `psd` varchar(250) NOT NULL,
  `psd_name` varchar(100) NOT NULL,
  `phone_gif` varchar(250) NOT NULL,
  `phone_gif_name` varchar(100) NOT NULL,
  `pad_gif` varchar(250) NOT NULL,
  `pad_gif_name` varchar(100) NOT NULL,
  `png` varchar(250) NOT NULL,
  `png_name` varchar(100) NOT NULL,
  `webp` varchar(250) NOT NULL,
  `webp_name` varchar(100) NOT NULL,
  `sound1` varchar(250) NOT NULL,
  `sound1_name` varchar(100) NOT NULL,
  `sound2` varchar(250) NOT NULL,
  `sound2_name` varchar(100) NOT NULL,
  `sound2_duration` double NOT NULL,
  `sound3` varchar(250) NOT NULL,
  `sound4` varchar(250) NOT NULL,
  `soundp_name` varchar(100) NOT NULL,
  `phone_gif_md5` varchar(35) NOT NULL,
  `pad_gif_md5` varchar(35) NOT NULL,
  `webp_md5` varchar(35) NOT NULL,
  `sound1_md5` varchar(35) NOT NULL,
  `sound2_md5` varchar(35) NOT NULL,
  `dumbbells` tinyint NOT NULL,
  `resistance_band` tinyint NOT NULL,
  `custom` tinyint NOT NULL,
  `random` tinyint NOT NULL,
  `custom_101` tinyint NOT NULL,
  `face_yoga` tinyint NOT NULL,
  `sanskrit` varchar(100) NOT NULL,
  `female_instruction` varchar(250) NOT NULL,
  `female` varchar(250) NOT NULL,
  `female_p` varchar(250) NOT NULL,
  `female_robot` varchar(250) NOT NULL,
  `female_wellsaid` varchar(250) NOT NULL,
  `male_instruction` varchar(250) NOT NULL,
  `male_robot` varchar(250) NOT NULL,
  `male_wellsaid` varchar(250) NOT NULL,
  `transition_id` int DEFAULT NULL,
  `del_flag` tinyint DEFAULT '0',
  `create_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- ----------------------------
-- Table structure for storage_exercise_file_copy104
-- ----------------------------
DROP TABLE IF EXISTS `storage_exercise_file_copy104`;
CREATE TABLE `storage_exercise_file_copy104` (
  `id` int NOT NULL,
  `animation_phone_gif_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `animation_pad_gif_url` varchar(500) DEFAULT NULL,
  `animation_thumbnail_url` varchar(500) DEFAULT NULL,
  `sound_female_url` varchar(500) DEFAULT NULL,
  `sound_female_rebot_url` varchar(500) DEFAULT NULL,
  `sound_male_url` varchar(500) DEFAULT NULL,
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `sound_male_robot_url` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用于104项目 exercise相关文件迁移的临时保存目录';

-- ----------------------------
-- Table structure for storage_exercise_file_copy200
-- ----------------------------
DROP TABLE IF EXISTS `storage_exercise_file_copy200`;
CREATE TABLE `storage_exercise_file_copy200` (
  `id` int NOT NULL,
  `animation_phone_gif_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `animation_pad_gif_url` varchar(500) DEFAULT NULL,
  `animation_thumbnail_url` varchar(500) DEFAULT NULL,
  `sound_female_url` varchar(500) DEFAULT NULL,
  `sound_female_rebot_url` varchar(500) DEFAULT NULL,
  `sound_male_url` varchar(500) DEFAULT NULL,
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `sound_male_robot_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用于200项目 exercise相关文件迁移的临时保存目录';


-- ----------------------------
-- Table structure for storage_exercise_options104
-- ----------------------------
DROP TABLE IF EXISTS `storage_exercise_options104`;
CREATE TABLE `storage_exercise_options104` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_type` varchar(64) DEFAULT NULL,
  `exercise_id` int DEFAULT NULL,
  `data_id` int DEFAULT NULL,
  `data_value` varchar(255) DEFAULT NULL,
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for storage_exercise_options200
-- ----------------------------
DROP TABLE IF EXISTS `storage_exercise_options200`;
CREATE TABLE `storage_exercise_options200` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_type` varchar(64) DEFAULT NULL,
  `exercise_id` int DEFAULT NULL,
  `data_id` int DEFAULT NULL,
  `data_value` varchar(255) DEFAULT NULL,
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for storage_sound
-- ----------------------------
DROP TABLE IF EXISTS `storage_sound`;
CREATE TABLE `storage_sound` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `tag` int NOT NULL,
  `url` varchar(500) NOT NULL,
  `md5` varchar(100) NOT NULL,
  `duration` double NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人 账号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人账号',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `del_flag` (`del_flag`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门表';

-- ----------------------------
-- Table structure for sys_dept_perms
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept_perms`;
CREATE TABLE `sys_dept_perms` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `dept_id` int NOT NULL COMMENT '部门id',
  `perms_id` int NOT NULL COMMENT '权限id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除 1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人账号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `dept_id` (`dept_id`),
  KEY `perms_id` (`perms_id`),
  KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门权限关联表';

-- ----------------------------
-- Table structure for sys_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `sys_dictionary`;
CREATE TABLE `sys_dictionary` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `dict_name` varchar(50) DEFAULT NULL COMMENT '字典名称',
  `dict_key` varchar(50) NOT NULL COMMENT '字典key',
  `dict_value` varchar(255) NOT NULL COMMENT '字典value',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序编号',
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT '父id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `dict_key` (`dict_key`),
  KEY `parent_id` (`parent_id`),
  KEY `del_flag` (`del_flag`),
  KEY `sort_no` (`sort_no`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字典表';

-- ----------------------------
-- Table structure for sys_event
-- ----------------------------
DROP TABLE IF EXISTS `sys_event`;
CREATE TABLE `sys_event` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型',
  `event_data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '事件数据',
  `process_status` int NOT NULL DEFAULT '0' COMMENT '处理状态 0未处理 1处理中 2处理完毕',
  `process_start_time` datetime DEFAULT NULL COMMENT '开始处理时间',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  PRIMARY KEY (`id`),
  KEY `event_type` (`event_type`),
  KEY `process_status` (`process_status`),
  KEY `process_start_time` (`process_start_time`),
  KEY `del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='事件表';

-- ----------------------------
-- Table structure for sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `biz_type` varchar(64) DEFAULT NULL COMMENT '业务类型',
  `proj_id` int unsigned DEFAULT '0' COMMENT '项目id',
  `data_id` int DEFAULT NULL COMMENT '数据id',
  `data_info` varchar(255) DEFAULT NULL COMMENT '数据信息(最新值)',
  `operation_user` varchar(50) DEFAULT NULL COMMENT '操作人',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志表';

-- ----------------------------
-- Table structure for sys_operation_log_history
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log_history`;
CREATE TABLE `sys_operation_log_history` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `biz_type` varchar(64) DEFAULT NULL COMMENT '业务类型',
  `proj_id` int unsigned DEFAULT '0' COMMENT '项目id',
  `data_id` int DEFAULT '0' COMMENT '数据id',
  `data_info` varchar(255) DEFAULT NULL COMMENT '数据信息',
  `operation_type` int DEFAULT NULL COMMENT '操作类型',
  `data_before` text COMMENT '操作前数据',
  `data_after` text COMMENT '操作后数据',
  `data_before_status` int DEFAULT NULL COMMENT '操作前状态',
  `data_after_status` int DEFAULT NULL COMMENT '操作后状态',
  `compression_before_status` int DEFAULT NULL COMMENT '操作前压缩状态',
  `compression_after_status` int DEFAULT NULL COMMENT '操作后压缩状态',
  `operation_user` varchar(50) DEFAULT NULL COMMENT '操作人',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `biz_type` (`biz_type`),
  KEY `proj_id` (`proj_id`),
  KEY `data_id` (`data_id`),
  KEY `operation_time` (`operation_time`),
  KEY `operation_user` (`operation_user`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志历史表';

-- ----------------------------
-- Table structure for sys_perms
-- ----------------------------
DROP TABLE IF EXISTS `sys_perms`;
CREATE TABLE `sys_perms` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `parent_id` int NOT NULL DEFAULT '0' COMMENT '父级id',
  `perms_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `perms_key` varchar(100) NOT NULL COMMENT '权限标识',
  `perms_type` tinyint NOT NULL COMMENT '权限类型： 1 菜单 2操作类型 3 项目 4外链',
  `path` varchar(255) DEFAULT NULL COMMENT '路由地址，链接地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件地址',
  `required` tinyint NOT NULL DEFAULT '1' COMMENT '随着父级联动，必须的（0否 1是）',
  `visible` tinyint NOT NULL DEFAULT '1' COMMENT '菜单显示 （0隐藏 1显示）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '菜单状态 （ 1正常 2停用）',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '菜单图标',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0未删除，1已删除',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人 账号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人账号',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `perms_key` (`perms_key`),
  KEY `parent_id` (`parent_id`),
  KEY `visible` (`visible`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权限表';

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '显示名称',
  `real_name` varchar(50) NOT NULL COMMENT '真实名称',
  `password` varchar(50) NOT NULL COMMENT '密码',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账号状态（1正常 2停用）',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(11) DEFAULT NULL COMMENT '手机号',
  `sex` tinyint DEFAULT NULL COMMENT '用户性别（0男，1女，2未知）',
  `dept_id` int NOT NULL COMMENT '部门id',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型（0管理员，1普通用户）',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `user_name` (`user_name`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `dept_id` (`dept_id`) USING BTREE,
  KEY `create_time` (`create_time` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';

-- ----------------------------
-- Table structure for sys_user_perms
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_perms`;
CREATE TABLE `sys_user_perms` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int NOT NULL COMMENT '用户id',
  `perms_id` int NOT NULL COMMENT '权限id',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `perms_id` (`perms_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户权限关联表';

-- ----------------------------
-- Table structure for test_mp3gain_sound
-- ----------------------------
DROP TABLE IF EXISTS `test_mp3gain_sound`;
CREATE TABLE `test_mp3gain_sound` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `exercise_name` varchar(100) NOT NULL COMMENT 'sound名称',
  `audio_type` varchar(50) NOT NULL COMMENT '音频类型',
  `app` varchar(50) NOT NULL COMMENT '数据来源',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `result` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处理结果',
  `female` varchar(255) DEFAULT NULL COMMENT 'female',
  `female_robot` varchar(255) DEFAULT NULL COMMENT 'female_robot',
  `male` varchar(255) DEFAULT NULL COMMENT 'male',
  `male_robot` varchar(255) DEFAULT NULL COMMENT 'male_robot',
  `female_gain` varchar(255) DEFAULT NULL COMMENT 'mp3gain 后的female',
  `female_robot_gain` varchar(255) DEFAULT NULL COMMENT 'mp3gain 后的female_robot',
  `male_gain` varchar(255) DEFAULT NULL COMMENT 'mp3gain 后的 male',
  `male_robot_gain` varchar(255) DEFAULT NULL COMMENT 'mp3gain 后的male_robot',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='test_mp3gain_sound';

-- ----------------------------
-- Table structure for test_sound_url
-- ----------------------------
DROP TABLE IF EXISTS `test_sound_url`;
CREATE TABLE `test_sound_url` (
  `id` int NOT NULL AUTO_INCREMENT,
  `original_exercise_id` int DEFAULT NULL COMMENT '源文件id',
  `female` varchar(255) DEFAULT NULL COMMENT 'female url',
  `female_robot` varchar(255) DEFAULT NULL COMMENT 'female robot url',
  `male` varchar(255) DEFAULT NULL COMMENT 'male url',
  `male_robot` varchar(255) DEFAULT NULL COMMENT 'male robot url',
  `female_gain` varchar(255) DEFAULT NULL COMMENT 'female 经过mp3gain处理后的 url',
  `female_robot_gain` varchar(255) DEFAULT NULL COMMENT 'female robot 经过mp3gain处理后的 url',
  `male_gain` varchar(255) DEFAULT NULL COMMENT 'male  经过mp3gain处理后的 url',
  `male_robot_gain` varchar(255) DEFAULT NULL COMMENT 'male robot  经过mp3gain处理后的 url',
  `phone` varchar(255) DEFAULT NULL COMMENT 'animation phone gif url',
  `pad` varchar(255) DEFAULT NULL COMMENT 'animation pad gif url',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT 'animation thumbnail url',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

SET FOREIGN_KEY_CHECKS = 1;
