#所有使用的exercise id
SELECT DISTINCT
	rre.id
FROM
	proj_workout_exercise pwe,
	res_regular_exercise rre
WHERE
	pwe.del_flag = 0
	AND pwe.regular_exercise_id = rre.id

#删除未使用的 animation

DELETE
FROM
	res_animation
WHERE
	id NOT IN (
	SELECT
		rre.animation_id
	FROM
		res_regular_exercise rre
	WHERE
	rre.id IN ( SELECT DISTINCT rre.id FROM proj_workout_exercise pwe, res_regular_exercise rre WHERE pwe.del_flag = 0 AND pwe.regular_exercise_id = rre.id )
	)
#删除未使用的sound
DELETE
FROM
	res_sound
WHERE
	id NOT IN (
	SELECT
		rre.sound_id
	FROM
		res_regular_exercise rre
	WHERE
	rre.id IN ( SELECT DISTINCT rre.id FROM proj_workout_exercise pwe, res_regular_exercise rre WHERE pwe.del_flag = 0 AND pwe.regular_exercise_id = rre.id )
	) and id<1873
#删除exercise
DELETE
FROM
	res_regular_exercise
WHERE
	id NOT IN (
	SELECT
		a.id
	FROM
	( SELECT DISTINCT rre.id FROM proj_workout_exercise pwe, res_regular_exercise rre WHERE pwe.del_flag = 0 AND pwe.regular_exercise_id = rre.id ) a
	)