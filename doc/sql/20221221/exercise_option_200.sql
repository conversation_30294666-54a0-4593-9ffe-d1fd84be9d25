-- exercise bodypart 没有找到相关表
-- exercise position 没有找到相关表
-- storage_exercise_difficulty -> storage_difficulty
-- storage_exercise_equipment -> storage_equipment
-- storage_exercise_fit_type ->storage_fittype
-- storage_exercise_focus_old -> storage_focus 看数据好像不对
-- storage_exercise_special -> storage_special 页面上看着好像没有什么用
-- storage_exercise_special_needs -> storage_specialneed
-- storage_exercise_target -> storage_target
-- storage_exercise_yoga_types -> storage_yogatype


-- SELECT * from storage_exercise_yoga_types;
-- SELECT * from storage_yogatype;
-- 
-- SELECT * from storage_exercise_fit_type;
-- SELECT * from storage_fittype;
-- 
-- SELECT * from storage_exercise_target;
-- SELECT * from storage_target;
-- 
-- SELECT * from storage_exercise_equipment;
-- SELECT * from storage_equipment;
-- 
-- SELECT * from storage_exercise_special_needs;
-- SELECT * from storage_specialneed;



SELECT
	'yoga_types' data_type,
	exercise_id,
	yogatype_id data_id,
	name data_value 
FROM
	storage_exercise_yoga_types a
	LEFT JOIN storage_yogatype b ON a.yogatype_id = b.id UNION ALL
-- SELECT
-- 	'fit_type' data_type,
-- 	exercise_id,
-- 	fittype_id data_id,
-- 	name data_value
-- FROM
-- 	storage_exercise_fit_type a
-- 	LEFT JOIN storage_fittype b ON a.fittype_id = b.id UNION ALL
SELECT
	'target' data_type,
	exercise_id,
	target_id data_id,
	name data_value 
FROM
	storage_exercise_target a
	LEFT JOIN storage_target b ON a.target_id = b.id UNION ALL
SELECT
	'equipment' data_type,
	exercise_id,
	equipment_id data_id,
	name data_value 
FROM
	storage_exercise_equipment a
	LEFT JOIN storage_equipment b ON a.equipment_id = b.id UNION ALL
SELECT
	'special_needs' data_type,
	exercise_id,
	specialneed_id data_id,
	name data_value 
FROM
	storage_exercise_special_needs a
	LEFT JOIN storage_specialneed b ON a.specialneed_id = b.id

UNION ALL
SELECT 'body_part' data_type, 0 exercise_id, 1 data_id, 'Upper body' data_value
UNION all
SELECT 'body_part', 0 , 2, 'Lower body'
UNION all
SELECT 'body_part', 0 , 3 , 'Total body'
UNION all
SELECT 'body_part', 0 , 4 , ''
UNION all
SELECT 'body_part', 0 , 0 , '' 
UNION all
SELECT 'position', 0 , 0 , 'Lying' 
UNION all
SELECT 'position', 0 , 10 , 'Prone' 
UNION all
SELECT 'position', 0 , 1 , 'Seated' 
UNION all
SELECT 'position', 0 , 11 , 'Kneeling' 
UNION all
SELECT 'position', 0 , 2 , 'Standing' 
UNION all
SELECT 'position', 0 , 3 , 'Start' 
UNION all
SELECT 'position', 0 , 4 , 'End' 
UNION all
SELECT 'position', 0 , -1 , '' 
UNION all
SELECT 'focus', 0 , 0 , 'Balancing' 
UNION all
SELECT 'focus', 0 , 1 , 'Flexibility' 
UNION all
SELECT 'focus', 0 , 2 , 'Relaxation' 
UNION all
SELECT 'focus', 0 , 3 , 'Strength' 
UNION all
SELECT 'focus', 0 , -1 , '' 
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	