CREATE TABLE `storage_exercise200` (
	`id` int AUTO_INCREMENT,
	`name` varchar ( 100 ) NOT NULL,
	`display_name` varchar ( 100 ) NOT NULL,
	`alternate_name` text NOT NULL,
	`daily_7` tinyint NOT NULL,
	`my_plan` tinyint NOT NULL,
	`intensity` int NOT NULL,
	`difficulty` int NOT NULL,
	`met` int NOT NULL,
	`body_part` int NOT NULL,
	`video_link` varchar ( 200 ) NOT NULL,
	`comment` text NOT NULL,
	`mark` text NOT NULL,
	`description` text NOT NULL,
	`position` int NOT NULL,
	`focus` int NOT NULL,
	`psd` varchar ( 250 ) NOT NULL,
	`psd_name` varchar ( 100 ) NOT NULL,
	`phone_gif` varchar ( 250 ) NOT NULL,
	`phone_gif_name` varchar ( 100 ) NOT NULL,
	`pad_gif` varchar ( 250 ) NOT NULL,
	`pad_gif_name` varchar ( 100 ) NOT NULL,
	`png` varchar ( 250 ) NOT NULL,
	`png_name` varchar ( 100 ) NOT NULL,
	`webp` varchar ( 250 ) NOT NULL,
	`webp_name` varchar ( 100 ) NOT NULL,
	`sound1` varchar ( 250 ) NOT NULL,
	`sound1_name` varchar ( 100 ) NOT NULL,
	`sound2` varchar ( 250 ) NOT NULL,
	`sound2_name` varchar ( 100 ) NOT NULL,
	`sound2_duration` real NOT NULL,
	`sound3` varchar ( 250 ) NOT NULL,
	`sound4` varchar ( 250 ) NOT NULL,
	`soundp_name` varchar ( 100 ) NOT NULL,
	`phone_gif_md5` varchar ( 35 ) NOT NULL,
	`pad_gif_md5` varchar ( 35 ) NOT NULL,
	`webp_md5` varchar ( 35 ) NOT NULL,
	`sound1_md5` varchar ( 35 ) NOT NULL,
	`sound2_md5` varchar ( 35 ) NOT NULL,
	`dumbbells` tinyint NOT NULL,
	`resistance_band` tinyint NOT NULL,
	`custom` tinyint NOT NULL,
	`random` tinyint NOT NULL,
	`custom_101` tinyint NOT NULL,
	`face_yoga` tinyint NOT NULL,
	`sanskrit` varchar ( 100 ) NOT NULL,
	`female_instruction` varchar ( 250 ) NOT NULL,
	`female` varchar ( 250 ) NOT NULL,
	`female_p` varchar ( 250 ) NOT NULL,
	`female_robot` varchar ( 250 ) NOT NULL,
	`female_wellsaid` varchar ( 250 ) NOT NULL,
	`male_instruction` varchar ( 250 ) NOT NULL,
	`male_robot` varchar ( 250 ) NOT NULL,
	`male_wellsaid` varchar ( 250 ) NOT NULL,
    `transition_id` int NULL ,
    PRIMARY KEY (`id`)
);