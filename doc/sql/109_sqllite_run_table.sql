DROP TABLE IF EXISTS `storage_exercise`;
CREATE TABLE `storage_exercise` (
  `id` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `alternate_name` varchar(3000) NOT NULL,
  `daily_7` tinyint NOT NULL,
  `my_plan` tinyint NOT NULL,
  `intensity` int NOT NULL,
  `star_ratings` int NOT NULL,
  `met` int NOT NULL,
  `body_part` int NOT NULL,
  `video_link` varchar(200) NOT NULL,
  `comment` varchar(3000) NOT NULL,
  `mark` varchar(3000) NOT NULL,
  `description` varchar(3000) NOT NULL,
  `position` int NOT NULL,
  `focus` int NOT NULL,
  `psd` varchar(250) NOT NULL,
  `psd_name` varchar(100) NOT NULL,
  `phone_gif` varchar(250) NOT NULL,
  `phone_gif_name` varchar(100) NOT NULL,
  `pad_gif` varchar(250) NOT NULL,
  `pad_gif_name` varchar(100) NOT NULL,
  `png` varchar(250) NOT NULL,
  `png_name` varchar(100) NOT NULL,
  `webp` varchar(250) NOT NULL,
  `webp_name` varchar(100) NOT NULL,
  `sound1` varchar(250) NOT NULL,
  `sound1_name` varchar(100) NOT NULL,
  `sound2` varchar(250) NOT NULL,
  `sound2_name` varchar(100) NOT NULL,
  `sound2_duration` real NOT NULL,
  `sound3` varchar(250) NOT NULL,
  `sound4` varchar(250) NOT NULL,
  `soundp_name` varchar(100) NOT NULL,
  `phone_gif_md5` varchar(35) NOT NULL,
  `pad_gif_md5` varchar(35) NOT NULL,
  `webp_md5` varchar(35) NOT NULL,
  `sound1_md5` varchar(35) NOT NULL,
  `sound2_md5` varchar(35) NOT NULL,
  `dumbbells` tinyint NOT NULL,
  `resistance_band` tinyint NOT NULL,
  `yoga_mat` tinyint NOT NULL,
  `custom` tinyint NOT NULL,
  `female_id` int,
  `female_p_id` int,
  `female_robot_id` int,
  `male_robot_id` int,
  `random` tinyint NOT NULL,
  `custom_101` tinyint NOT NULL,
  `face_yoga` tinyint NOT NULL,
  `sanskrit` varchar(100) NOT NULL,
  `female_instruction_id` int,
  `male_instruction_id` int,
  `male_robot_wellsaid_id` int,
  `female_wellsaid_id` int
);

DROP TABLE IF EXISTS `storage_sound`;
CREATE TABLE `storage_sound` (`id` int NOT NULL PRIMARY KEY AUTO_INCREMENT, `name` varchar(40) NOT NULL, `tag` int NOT NULL, `url` varchar(500) NOT NULL, `md5` varchar(100) NOT NULL, `duration` double NOT NULL);


CREATE TABLE `storage_exercise_file_copy` (
  `exercise_id` int NOT NULL,
  `animation_phone_gif_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `animation_pad_gif_url` varchar(500) DEFAULT NULL,
  `animation_thumbnail_url` varchar(500) DEFAULT NULL,
  `sound_female_url` varchar(500) DEFAULT NULL,
  `sound_female_rebot_url` varchar(500) DEFAULT NULL,
  `sound_male_url` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`exercise_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用于109项目 exercise相关文件迁移的临时保存目录';

CREATE TABLE `storage_exercise_options` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_type` varchar(64) DEFAULT NULL,
  `exercise_id` int DEFAULT NULL,
  `data_id` int DEFAULT NULL,
  `data_value` varchar(255) DEFAULT NULL,
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 替换 " > `
-- 替换 AUTO_INCREMENT > AUTO_INCREMENT
-- 替换 int > int
-- 替换 real > double